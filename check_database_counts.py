#!/usr/bin/env python3
"""
Check Database Counts

This script checks the number of foods and nutrients in the USDA and FoodB databases.
"""

import os
import sys
import sqlite3
from pathlib import Path

# Configuration
OUTPUT_DIR = Path("../app/src/assets/databases")
USDA_DB_PATH = OUTPUT_DIR / "usda_foods.sqlite"
FOODB_DB_PATH = OUTPUT_DIR / "foodb_foods.sqlite"

def check_database_exists(db_path):
    """Check if the database file exists."""
    if not db_path.exists():
        print(f"Database file {db_path} does not exist.")
        return False
    return True

def count_records(db_path, table_name):
    """Count the number of records in a table."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        conn.close()
        return count
    except sqlite3.Error as e:
        print(f"Error counting records in {table_name}: {e}")
        return 0

def main():
    """Main function."""
    # Check if the database files exist
    usda_exists = check_database_exists(USDA_DB_PATH)
    foodb_exists = check_database_exists(FOODB_DB_PATH)
    
    if not usda_exists and not foodb_exists:
        print("No database files found. Please run the import scripts first.")
        sys.exit(1)
    
    # Check USDA database
    if usda_exists:
        print("\nUSDA Database:")
        print(f"File size: {os.path.getsize(USDA_DB_PATH) / (1024 * 1024):.2f} MB")
        
        food_count = count_records(USDA_DB_PATH, "foods")
        nutrient_count = count_records(USDA_DB_PATH, "nutrients")
        
        print(f"Number of foods: {food_count}")
        print(f"Number of nutrients: {nutrient_count}")
    
    # Check FoodB database
    if foodb_exists:
        print("\nFoodB Database:")
        print(f"File size: {os.path.getsize(FOODB_DB_PATH) / (1024 * 1024):.2f} MB")
        
        food_count = count_records(FOODB_DB_PATH, "foods")
        nutrient_count = count_records(FOODB_DB_PATH, "nutrients")
        compound_count = count_records(FOODB_DB_PATH, "compounds")
        
        print(f"Number of foods: {food_count}")
        print(f"Number of nutrients: {nutrient_count}")
        print(f"Number of compounds: {compound_count}")

if __name__ == "__main__":
    main()
