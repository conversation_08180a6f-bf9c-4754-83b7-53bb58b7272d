{"ast": null, "code": "import getBoundingClientRect from \"../../modules/getBoundingClientRect\";\nimport setValueForStyles from \"../../modules/setValueForStyles\";\nvar getRect = function getRect(node) {\n  var height = node.offsetHeight;\n  var width = node.offsetWidth;\n  var left = node.offsetLeft;\n  var top = node.offsetTop;\n  node = node.offsetParent;\n  while (node && node.nodeType === 1) {\n    left += node.offsetLeft + node.clientLeft - node.scrollLeft;\n    top += node.offsetTop + node.clientTop - node.scrollTop;\n    node = node.offsetParent;\n  }\n  top -= window.scrollY;\n  left -= window.scrollX;\n  return {\n    width: width,\n    height: height,\n    top: top,\n    left: left\n  };\n};\nvar _measureLayout = function measureLayout(node, relativeToNativeNode, callback) {\n  var relativeNode = relativeToNativeNode || node && node.parentNode;\n  if (node && relativeNode) {\n    setTimeout(function () {\n      if (node.isConnected && relativeNode.isConnected) {\n        var relativeRect = getRect(relativeNode);\n        var _getRect = getRect(node),\n          height = _getRect.height,\n          left = _getRect.left,\n          top = _getRect.top,\n          width = _getRect.width;\n        var x = left - relativeRect.left;\n        var y = top - relativeRect.top;\n        callback(x, y, width, height, left, top);\n      }\n    }, 0);\n  }\n};\nvar elementsToIgnore = {\n  A: true,\n  BODY: true,\n  INPUT: true,\n  SELECT: true,\n  TEXTAREA: true\n};\nvar UIManager = {\n  blur: function blur(node) {\n    try {\n      node.blur();\n    } catch (err) {}\n  },\n  focus: function focus(node) {\n    try {\n      var name = node.nodeName;\n      if (node.getAttribute('tabIndex') == null && node.isContentEditable !== true && elementsToIgnore[name] == null) {\n        node.setAttribute('tabIndex', '-1');\n      }\n      node.focus();\n    } catch (err) {}\n  },\n  measure: function measure(node, callback) {\n    _measureLayout(node, null, callback);\n  },\n  measureInWindow: function measureInWindow(node, callback) {\n    if (node) {\n      setTimeout(function () {\n        var _getBoundingClientRec = getBoundingClientRect(node),\n          height = _getBoundingClientRec.height,\n          left = _getBoundingClientRec.left,\n          top = _getBoundingClientRec.top,\n          width = _getBoundingClientRec.width;\n        callback(left, top, width, height);\n      }, 0);\n    }\n  },\n  measureLayout: function measureLayout(node, relativeToNativeNode, onFail, onSuccess) {\n    _measureLayout(node, relativeToNativeNode, onSuccess);\n  },\n  updateView: function updateView(node, props) {\n    for (var prop in props) {\n      if (!Object.prototype.hasOwnProperty.call(props, prop)) {\n        continue;\n      }\n      var value = props[prop];\n      switch (prop) {\n        case 'style':\n          {\n            setValueForStyles(node, value);\n            break;\n          }\n        case 'class':\n        case 'className':\n          {\n            node.setAttribute('class', value);\n            break;\n          }\n        case 'text':\n        case 'value':\n          node.value = value;\n          break;\n        default:\n          node.setAttribute(prop, value);\n      }\n    }\n  },\n  configureNextLayoutAnimation: function configureNextLayoutAnimation(config, onAnimationDidEnd) {\n    onAnimationDidEnd();\n  },\n  setLayoutAnimationEnabledExperimental: function setLayoutAnimationEnabledExperimental() {}\n};\nexport default UIManager;", "map": {"version": 3, "names": ["getBoundingClientRect", "setValueForStyles", "getRect", "node", "height", "offsetHeight", "width", "offsetWidth", "left", "offsetLeft", "top", "offsetTop", "offsetParent", "nodeType", "clientLeft", "scrollLeft", "clientTop", "scrollTop", "window", "scrollY", "scrollX", "measureLayout", "relativeToNativeNode", "callback", "relativeNode", "parentNode", "setTimeout", "isConnected", "relativeRect", "_getRect", "x", "y", "elementsToIgnore", "A", "BODY", "INPUT", "SELECT", "TEXTAREA", "UIManager", "blur", "err", "focus", "name", "nodeName", "getAttribute", "isContentEditable", "setAttribute", "measure", "measureInWindow", "_getBoundingClientRec", "onFail", "onSuccess", "updateView", "props", "prop", "Object", "prototype", "hasOwnProperty", "call", "value", "configureNextLayoutAnimation", "config", "onAnimationDidEnd", "setLayoutAnimationEnabledExperimental"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/UIManager/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport getBoundingClientRect from '../../modules/getBoundingClientRect';\nimport setValueForStyles from '../../modules/setValueForStyles';\nvar getRect = node => {\n  var height = node.offsetHeight;\n  var width = node.offsetWidth;\n  var left = node.offsetLeft;\n  var top = node.offsetTop;\n  node = node.offsetParent;\n  while (node && node.nodeType === 1 /* Node.ELEMENT_NODE */) {\n    left += node.offsetLeft + node.clientLeft - node.scrollLeft;\n    top += node.offsetTop + node.clientTop - node.scrollTop;\n    node = node.offsetParent;\n  }\n  top -= window.scrollY;\n  left -= window.scrollX;\n  return {\n    width,\n    height,\n    top,\n    left\n  };\n};\nvar measureLayout = (node, relativeToNativeNode, callback) => {\n  var relativeNode = relativeToNativeNode || node && node.parentNode;\n  if (node && relativeNode) {\n    setTimeout(() => {\n      if (node.isConnected && relativeNode.isConnected) {\n        var relativeRect = getRect(relativeNode);\n        var _getRect = getRect(node),\n          height = _getRect.height,\n          left = _getRect.left,\n          top = _getRect.top,\n          width = _getRect.width;\n        var x = left - relativeRect.left;\n        var y = top - relativeRect.top;\n        callback(x, y, width, height, left, top);\n      }\n    }, 0);\n  }\n};\nvar elementsToIgnore = {\n  A: true,\n  BODY: true,\n  INPUT: true,\n  SELECT: true,\n  TEXTAREA: true\n};\nvar UIManager = {\n  blur(node) {\n    try {\n      node.blur();\n    } catch (err) {}\n  },\n  focus(node) {\n    try {\n      var name = node.nodeName;\n      // A tabIndex of -1 allows element to be programmatically focused but\n      // prevents keyboard focus. We don't want to set the tabindex value on\n      // elements that should not prevent keyboard focus.\n      if (node.getAttribute('tabIndex') == null && node.isContentEditable !== true && elementsToIgnore[name] == null) {\n        node.setAttribute('tabIndex', '-1');\n      }\n      node.focus();\n    } catch (err) {}\n  },\n  measure(node, callback) {\n    measureLayout(node, null, callback);\n  },\n  measureInWindow(node, callback) {\n    if (node) {\n      setTimeout(() => {\n        var _getBoundingClientRec = getBoundingClientRect(node),\n          height = _getBoundingClientRec.height,\n          left = _getBoundingClientRec.left,\n          top = _getBoundingClientRec.top,\n          width = _getBoundingClientRec.width;\n        callback(left, top, width, height);\n      }, 0);\n    }\n  },\n  measureLayout(node, relativeToNativeNode, onFail, onSuccess) {\n    measureLayout(node, relativeToNativeNode, onSuccess);\n  },\n  updateView(node, props) {\n    for (var prop in props) {\n      if (!Object.prototype.hasOwnProperty.call(props, prop)) {\n        continue;\n      }\n      var value = props[prop];\n      switch (prop) {\n        case 'style':\n          {\n            setValueForStyles(node, value);\n            break;\n          }\n        case 'class':\n        case 'className':\n          {\n            node.setAttribute('class', value);\n            break;\n          }\n        case 'text':\n        case 'value':\n          // native platforms use `text` prop to replace text input value\n          node.value = value;\n          break;\n        default:\n          node.setAttribute(prop, value);\n      }\n    }\n  },\n  configureNextLayoutAnimation(config, onAnimationDidEnd) {\n    onAnimationDidEnd();\n  },\n  // mocks\n  setLayoutAnimationEnabledExperimental() {}\n};\nexport default UIManager;"], "mappings": "AASA,OAAOA,qBAAqB;AAC5B,OAAOC,iBAAiB;AACxB,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAGC,IAAI,EAAI;EACpB,IAAIC,MAAM,GAAGD,IAAI,CAACE,YAAY;EAC9B,IAAIC,KAAK,GAAGH,IAAI,CAACI,WAAW;EAC5B,IAAIC,IAAI,GAAGL,IAAI,CAACM,UAAU;EAC1B,IAAIC,GAAG,GAAGP,IAAI,CAACQ,SAAS;EACxBR,IAAI,GAAGA,IAAI,CAACS,YAAY;EACxB,OAAOT,IAAI,IAAIA,IAAI,CAACU,QAAQ,KAAK,CAAC,EAA0B;IAC1DL,IAAI,IAAIL,IAAI,CAACM,UAAU,GAAGN,IAAI,CAACW,UAAU,GAAGX,IAAI,CAACY,UAAU;IAC3DL,GAAG,IAAIP,IAAI,CAACQ,SAAS,GAAGR,IAAI,CAACa,SAAS,GAAGb,IAAI,CAACc,SAAS;IACvDd,IAAI,GAAGA,IAAI,CAACS,YAAY;EAC1B;EACAF,GAAG,IAAIQ,MAAM,CAACC,OAAO;EACrBX,IAAI,IAAIU,MAAM,CAACE,OAAO;EACtB,OAAO;IACLd,KAAK,EAALA,KAAK;IACLF,MAAM,EAANA,MAAM;IACNM,GAAG,EAAHA,GAAG;IACHF,IAAI,EAAJA;EACF,CAAC;AACH,CAAC;AACD,IAAIa,cAAa,GAAG,SAAhBA,aAAaA,CAAIlB,IAAI,EAAEmB,oBAAoB,EAAEC,QAAQ,EAAK;EAC5D,IAAIC,YAAY,GAAGF,oBAAoB,IAAInB,IAAI,IAAIA,IAAI,CAACsB,UAAU;EAClE,IAAItB,IAAI,IAAIqB,YAAY,EAAE;IACxBE,UAAU,CAAC,YAAM;MACf,IAAIvB,IAAI,CAACwB,WAAW,IAAIH,YAAY,CAACG,WAAW,EAAE;QAChD,IAAIC,YAAY,GAAG1B,OAAO,CAACsB,YAAY,CAAC;QACxC,IAAIK,QAAQ,GAAG3B,OAAO,CAACC,IAAI,CAAC;UAC1BC,MAAM,GAAGyB,QAAQ,CAACzB,MAAM;UACxBI,IAAI,GAAGqB,QAAQ,CAACrB,IAAI;UACpBE,GAAG,GAAGmB,QAAQ,CAACnB,GAAG;UAClBJ,KAAK,GAAGuB,QAAQ,CAACvB,KAAK;QACxB,IAAIwB,CAAC,GAAGtB,IAAI,GAAGoB,YAAY,CAACpB,IAAI;QAChC,IAAIuB,CAAC,GAAGrB,GAAG,GAAGkB,YAAY,CAAClB,GAAG;QAC9Ba,QAAQ,CAACO,CAAC,EAAEC,CAAC,EAAEzB,KAAK,EAAEF,MAAM,EAAEI,IAAI,EAAEE,GAAG,CAAC;MAC1C;IACF,CAAC,EAAE,CAAC,CAAC;EACP;AACF,CAAC;AACD,IAAIsB,gBAAgB,GAAG;EACrBC,CAAC,EAAE,IAAI;EACPC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,SAAS,GAAG;EACdC,IAAI,WAAJA,IAAIA,CAACpC,IAAI,EAAE;IACT,IAAI;MACFA,IAAI,CAACoC,IAAI,CAAC,CAAC;IACb,CAAC,CAAC,OAAOC,GAAG,EAAE,CAAC;EACjB,CAAC;EACDC,KAAK,WAALA,KAAKA,CAACtC,IAAI,EAAE;IACV,IAAI;MACF,IAAIuC,IAAI,GAAGvC,IAAI,CAACwC,QAAQ;MAIxB,IAAIxC,IAAI,CAACyC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,IAAIzC,IAAI,CAAC0C,iBAAiB,KAAK,IAAI,IAAIb,gBAAgB,CAACU,IAAI,CAAC,IAAI,IAAI,EAAE;QAC9GvC,IAAI,CAAC2C,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;MACrC;MACA3C,IAAI,CAACsC,KAAK,CAAC,CAAC;IACd,CAAC,CAAC,OAAOD,GAAG,EAAE,CAAC;EACjB,CAAC;EACDO,OAAO,WAAPA,OAAOA,CAAC5C,IAAI,EAAEoB,QAAQ,EAAE;IACtBF,cAAa,CAAClB,IAAI,EAAE,IAAI,EAAEoB,QAAQ,CAAC;EACrC,CAAC;EACDyB,eAAe,WAAfA,eAAeA,CAAC7C,IAAI,EAAEoB,QAAQ,EAAE;IAC9B,IAAIpB,IAAI,EAAE;MACRuB,UAAU,CAAC,YAAM;QACf,IAAIuB,qBAAqB,GAAGjD,qBAAqB,CAACG,IAAI,CAAC;UACrDC,MAAM,GAAG6C,qBAAqB,CAAC7C,MAAM;UACrCI,IAAI,GAAGyC,qBAAqB,CAACzC,IAAI;UACjCE,GAAG,GAAGuC,qBAAqB,CAACvC,GAAG;UAC/BJ,KAAK,GAAG2C,qBAAqB,CAAC3C,KAAK;QACrCiB,QAAQ,CAACf,IAAI,EAAEE,GAAG,EAAEJ,KAAK,EAAEF,MAAM,CAAC;MACpC,CAAC,EAAE,CAAC,CAAC;IACP;EACF,CAAC;EACDiB,aAAa,WAAbA,aAAaA,CAAClB,IAAI,EAAEmB,oBAAoB,EAAE4B,MAAM,EAAEC,SAAS,EAAE;IAC3D9B,cAAa,CAAClB,IAAI,EAAEmB,oBAAoB,EAAE6B,SAAS,CAAC;EACtD,CAAC;EACDC,UAAU,WAAVA,UAAUA,CAACjD,IAAI,EAAEkD,KAAK,EAAE;IACtB,KAAK,IAAIC,IAAI,IAAID,KAAK,EAAE;MACtB,IAAI,CAACE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,KAAK,EAAEC,IAAI,CAAC,EAAE;QACtD;MACF;MACA,IAAIK,KAAK,GAAGN,KAAK,CAACC,IAAI,CAAC;MACvB,QAAQA,IAAI;QACV,KAAK,OAAO;UACV;YACErD,iBAAiB,CAACE,IAAI,EAAEwD,KAAK,CAAC;YAC9B;UACF;QACF,KAAK,OAAO;QACZ,KAAK,WAAW;UACd;YACExD,IAAI,CAAC2C,YAAY,CAAC,OAAO,EAAEa,KAAK,CAAC;YACjC;UACF;QACF,KAAK,MAAM;QACX,KAAK,OAAO;UAEVxD,IAAI,CAACwD,KAAK,GAAGA,KAAK;UAClB;QACF;UACExD,IAAI,CAAC2C,YAAY,CAACQ,IAAI,EAAEK,KAAK,CAAC;MAClC;IACF;EACF,CAAC;EACDC,4BAA4B,WAA5BA,4BAA4BA,CAACC,MAAM,EAAEC,iBAAiB,EAAE;IACtDA,iBAAiB,CAAC,CAAC;EACrB,CAAC;EAEDC,qCAAqC,WAArCA,qCAAqCA,CAAA,EAAG,CAAC;AAC3C,CAAC;AACD,eAAezB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}