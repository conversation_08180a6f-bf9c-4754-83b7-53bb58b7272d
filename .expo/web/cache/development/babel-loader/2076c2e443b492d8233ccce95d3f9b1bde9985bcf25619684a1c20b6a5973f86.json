{"ast": null, "code": "'use client';\n\nimport VirtualizedList from \"../../vendor/react-native/VirtualizedList\";\nexport default VirtualizedList;", "map": {"version": 3, "names": ["VirtualizedList"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/VirtualizedList/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport VirtualizedList from '../../vendor/react-native/VirtualizedList';\nexport default VirtualizedList;"], "mappings": "AASA,YAAY;;AAEZ,OAAOA,eAAe;AACtB,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}