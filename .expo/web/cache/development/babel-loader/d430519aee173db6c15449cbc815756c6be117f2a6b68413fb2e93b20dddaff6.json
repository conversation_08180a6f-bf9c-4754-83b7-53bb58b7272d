{"ast": null, "code": "var TouchHistoryMath = {\n  centroidDimension: function centroidDimension(touchHistory, touchesChangedAfter, isXAxis, ofCurrent) {\n    var touchBank = touchHistory.touchBank;\n    var total = 0;\n    var count = 0;\n    var oneTouchData = touchHistory.numberActiveTouches === 1 ? touchHistory.touchBank[touchHistory.indexOfSingleActiveTouch] : null;\n    if (oneTouchData !== null) {\n      if (oneTouchData.touchActive && oneTouchData.currentTimeStamp > touchesChangedAfter) {\n        total += ofCurrent && isXAxis ? oneTouchData.currentPageX : ofCurrent && !isXAxis ? oneTouchData.currentPageY : !ofCurrent && isXAxis ? oneTouchData.previousPageX : oneTouchData.previousPageY;\n        count = 1;\n      }\n    } else {\n      for (var i = 0; i < touchBank.length; i++) {\n        var touchTrack = touchBank[i];\n        if (touchTrack !== null && touchTrack !== undefined && touchTrack.touchActive && touchTrack.currentTimeStamp >= touchesChangedAfter) {\n          var toAdd = void 0;\n          if (ofCurrent && isXAxis) {\n            toAdd = touchTrack.currentPageX;\n          } else if (ofCurrent && !isXAxis) {\n            toAdd = touchTrack.currentPageY;\n          } else if (!ofCurrent && isXAxis) {\n            toAdd = touchTrack.previousPageX;\n          } else {\n            toAdd = touchTrack.previousPageY;\n          }\n          total += toAdd;\n          count++;\n        }\n      }\n    }\n    return count > 0 ? total / count : TouchHistoryMath.noCentroid;\n  },\n  currentCentroidXOfTouchesChangedAfter: function currentCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true, true);\n  },\n  currentCentroidYOfTouchesChangedAfter: function currentCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false, true);\n  },\n  previousCentroidXOfTouchesChangedAfter: function previousCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true, false);\n  },\n  previousCentroidYOfTouchesChangedAfter: function previousCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false, false);\n  },\n  currentCentroidX: function currentCentroidX(touchHistory) {\n    return TouchHistoryMath.centroidDimension(touchHistory, 0, true, true);\n  },\n  currentCentroidY: function currentCentroidY(touchHistory) {\n    return TouchHistoryMath.centroidDimension(touchHistory, 0, false, true);\n  },\n  noCentroid: -1\n};\nexport default TouchHistoryMath;", "map": {"version": 3, "names": ["TouchHistoryMath", "centroidDimension", "touchHistory", "touchesChangedAfter", "isXAxis", "ofCurrent", "touchBank", "total", "count", "oneTouchData", "numberActiveTouches", "indexOfSingleActiveTouch", "touchActive", "currentTimeStamp", "currentPageX", "currentPageY", "previousPageX", "previousPageY", "i", "length", "touchTrack", "undefined", "toAdd", "noCentroid", "currentCentroidXOfTouchesChangedAfter", "currentCentroidYOfTouchesChangedAfter", "previousCentroidXOfTouchesChangedAfter", "previousCentroidYOfTouchesChangedAfter", "currentCentroidX", "currentCentroidY"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/TouchHistoryMath/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nvar TouchHistoryMath = {\n  /**\n   * This code is optimized and not intended to look beautiful. This allows\n   * computing of touch centroids that have moved after `touchesChangedAfter`\n   * timeStamp. You can compute the current centroid involving all touches\n   * moves after `touchesChangedAfter`, or you can compute the previous\n   * centroid of all touches that were moved after `touchesChangedAfter`.\n   *\n   * @param {TouchHistoryMath} touchHistory Standard Responder touch track\n   * data.\n   * @param {number} touchesChangedAfter timeStamp after which moved touches\n   * are considered \"actively moving\" - not just \"active\".\n   * @param {boolean} isXAxis Consider `x` dimension vs. `y` dimension.\n   * @param {boolean} ofCurrent Compute current centroid for actively moving\n   * touches vs. previous centroid of now actively moving touches.\n   * @return {number} value of centroid in specified dimension.\n   */\n  centroidDimension: function centroidDimension(touchHistory, touchesChangedAfter, isXAxis, ofCurrent) {\n    var touchBank = touchHistory.touchBank;\n    var total = 0;\n    var count = 0;\n    var oneTouchData = touchHistory.numberActiveTouches === 1 ? touchHistory.touchBank[touchHistory.indexOfSingleActiveTouch] : null;\n    if (oneTouchData !== null) {\n      if (oneTouchData.touchActive && oneTouchData.currentTimeStamp > touchesChangedAfter) {\n        total += ofCurrent && isXAxis ? oneTouchData.currentPageX : ofCurrent && !isXAxis ? oneTouchData.currentPageY : !ofCurrent && isXAxis ? oneTouchData.previousPageX : oneTouchData.previousPageY;\n        count = 1;\n      }\n    } else {\n      for (var i = 0; i < touchBank.length; i++) {\n        var touchTrack = touchBank[i];\n        if (touchTrack !== null && touchTrack !== undefined && touchTrack.touchActive && touchTrack.currentTimeStamp >= touchesChangedAfter) {\n          var toAdd = void 0; // Yuck, program temporarily in invalid state.\n          if (ofCurrent && isXAxis) {\n            toAdd = touchTrack.currentPageX;\n          } else if (ofCurrent && !isXAxis) {\n            toAdd = touchTrack.currentPageY;\n          } else if (!ofCurrent && isXAxis) {\n            toAdd = touchTrack.previousPageX;\n          } else {\n            toAdd = touchTrack.previousPageY;\n          }\n          total += toAdd;\n          count++;\n        }\n      }\n    }\n    return count > 0 ? total / count : TouchHistoryMath.noCentroid;\n  },\n  currentCentroidXOfTouchesChangedAfter: function currentCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true,\n    // isXAxis\n    true // ofCurrent\n    );\n  },\n  currentCentroidYOfTouchesChangedAfter: function currentCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false,\n    // isXAxis\n    true // ofCurrent\n    );\n  },\n  previousCentroidXOfTouchesChangedAfter: function previousCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true,\n    // isXAxis\n    false // ofCurrent\n    );\n  },\n  previousCentroidYOfTouchesChangedAfter: function previousCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {\n    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false,\n    // isXAxis\n    false // ofCurrent\n    );\n  },\n  currentCentroidX: function currentCentroidX(touchHistory) {\n    return TouchHistoryMath.centroidDimension(touchHistory, 0,\n    // touchesChangedAfter\n    true,\n    // isXAxis\n    true // ofCurrent\n    );\n  },\n  currentCentroidY: function currentCentroidY(touchHistory) {\n    return TouchHistoryMath.centroidDimension(touchHistory, 0,\n    // touchesChangedAfter\n    false,\n    // isXAxis\n    true // ofCurrent\n    );\n  },\n  noCentroid: -1\n};\nexport default TouchHistoryMath;"], "mappings": "AASA,IAAIA,gBAAgB,GAAG;EAiBrBC,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,YAAY,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,SAAS,EAAE;IACnG,IAAIC,SAAS,GAAGJ,YAAY,CAACI,SAAS;IACtC,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,YAAY,GAAGP,YAAY,CAACQ,mBAAmB,KAAK,CAAC,GAAGR,YAAY,CAACI,SAAS,CAACJ,YAAY,CAACS,wBAAwB,CAAC,GAAG,IAAI;IAChI,IAAIF,YAAY,KAAK,IAAI,EAAE;MACzB,IAAIA,YAAY,CAACG,WAAW,IAAIH,YAAY,CAACI,gBAAgB,GAAGV,mBAAmB,EAAE;QACnFI,KAAK,IAAIF,SAAS,IAAID,OAAO,GAAGK,YAAY,CAACK,YAAY,GAAGT,SAAS,IAAI,CAACD,OAAO,GAAGK,YAAY,CAACM,YAAY,GAAG,CAACV,SAAS,IAAID,OAAO,GAAGK,YAAY,CAACO,aAAa,GAAGP,YAAY,CAACQ,aAAa;QAC/LT,KAAK,GAAG,CAAC;MACX;IACF,CAAC,MAAM;MACL,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,SAAS,CAACa,MAAM,EAAED,CAAC,EAAE,EAAE;QACzC,IAAIE,UAAU,GAAGd,SAAS,CAACY,CAAC,CAAC;QAC7B,IAAIE,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAID,UAAU,CAACR,WAAW,IAAIQ,UAAU,CAACP,gBAAgB,IAAIV,mBAAmB,EAAE;UACnI,IAAImB,KAAK,GAAG,KAAK,CAAC;UAClB,IAAIjB,SAAS,IAAID,OAAO,EAAE;YACxBkB,KAAK,GAAGF,UAAU,CAACN,YAAY;UACjC,CAAC,MAAM,IAAIT,SAAS,IAAI,CAACD,OAAO,EAAE;YAChCkB,KAAK,GAAGF,UAAU,CAACL,YAAY;UACjC,CAAC,MAAM,IAAI,CAACV,SAAS,IAAID,OAAO,EAAE;YAChCkB,KAAK,GAAGF,UAAU,CAACJ,aAAa;UAClC,CAAC,MAAM;YACLM,KAAK,GAAGF,UAAU,CAACH,aAAa;UAClC;UACAV,KAAK,IAAIe,KAAK;UACdd,KAAK,EAAE;QACT;MACF;IACF;IACA,OAAOA,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAGR,gBAAgB,CAACuB,UAAU;EAChE,CAAC;EACDC,qCAAqC,EAAE,SAASA,qCAAqCA,CAACtB,YAAY,EAAEC,mBAAmB,EAAE;IACvH,OAAOH,gBAAgB,CAACC,iBAAiB,CAACC,YAAY,EAAEC,mBAAmB,EAAE,IAAI,EAEjF,IACA,CAAC;EACH,CAAC;EACDsB,qCAAqC,EAAE,SAASA,qCAAqCA,CAACvB,YAAY,EAAEC,mBAAmB,EAAE;IACvH,OAAOH,gBAAgB,CAACC,iBAAiB,CAACC,YAAY,EAAEC,mBAAmB,EAAE,KAAK,EAElF,IACA,CAAC;EACH,CAAC;EACDuB,sCAAsC,EAAE,SAASA,sCAAsCA,CAACxB,YAAY,EAAEC,mBAAmB,EAAE;IACzH,OAAOH,gBAAgB,CAACC,iBAAiB,CAACC,YAAY,EAAEC,mBAAmB,EAAE,IAAI,EAEjF,KACA,CAAC;EACH,CAAC;EACDwB,sCAAsC,EAAE,SAASA,sCAAsCA,CAACzB,YAAY,EAAEC,mBAAmB,EAAE;IACzH,OAAOH,gBAAgB,CAACC,iBAAiB,CAACC,YAAY,EAAEC,mBAAmB,EAAE,KAAK,EAElF,KACA,CAAC;EACH,CAAC;EACDyB,gBAAgB,EAAE,SAASA,gBAAgBA,CAAC1B,YAAY,EAAE;IACxD,OAAOF,gBAAgB,CAACC,iBAAiB,CAACC,YAAY,EAAE,CAAC,EAEzD,IAAI,EAEJ,IACA,CAAC;EACH,CAAC;EACD2B,gBAAgB,EAAE,SAASA,gBAAgBA,CAAC3B,YAAY,EAAE;IACxD,OAAOF,gBAAgB,CAACC,iBAAiB,CAACC,YAAY,EAAE,CAAC,EAEzD,KAAK,EAEL,IACA,CAAC;EACH,CAAC;EACDqB,UAAU,EAAE,CAAC;AACf,CAAC;AACD,eAAevB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}