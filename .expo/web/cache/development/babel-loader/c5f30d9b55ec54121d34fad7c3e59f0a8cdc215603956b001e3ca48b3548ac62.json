{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"aria-readonly\", \"color\", \"disabled\", \"onChange\", \"onValueChange\", \"readOnly\", \"style\", \"value\"];\nimport * as React from 'react';\nimport createElement from \"../createElement\";\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nvar CheckBox = React.forwardRef(function (props, forwardedRef) {\n  var ariaReadOnly = props['aria-readonly'],\n    color = props.color,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    onValueChange = props.onValueChange,\n    readOnly = props.readOnly,\n    style = props.style,\n    value = props.value,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  function handleChange(event) {\n    var value = event.nativeEvent.target.checked;\n    event.nativeEvent.value = value;\n    onChange && onChange(event);\n    onValueChange && onValueChange(value);\n  }\n  var fakeControl = React.createElement(View, {\n    style: [styles.fakeControl, value && styles.fakeControlChecked, value && color && {\n      backgroundColor: color,\n      borderColor: color\n    }, disabled && styles.fakeControlDisabled, value && disabled && styles.fakeControlCheckedAndDisabled]\n  });\n  var nativeControl = createElement('input', {\n    checked: value,\n    disabled: disabled,\n    onChange: handleChange,\n    readOnly: readOnly === true || ariaReadOnly === true || other.accessibilityReadOnly === true,\n    ref: forwardedRef,\n    style: [styles.nativeControl, styles.cursorInherit],\n    type: 'checkbox'\n  });\n  return React.createElement(View, _extends({}, other, {\n    \"aria-disabled\": disabled,\n    \"aria-readonly\": ariaReadOnly,\n    style: [styles.root, style, disabled && styles.cursorDefault]\n  }), fakeControl, nativeControl);\n});\nCheckBox.displayName = 'CheckBox';\nvar styles = StyleSheet.create({\n  root: {\n    cursor: 'pointer',\n    height: 16,\n    userSelect: 'none',\n    width: 16\n  },\n  cursorDefault: {\n    cursor: 'default'\n  },\n  cursorInherit: {\n    cursor: 'inherit'\n  },\n  fakeControl: {\n    alignItems: 'center',\n    backgroundColor: '#fff',\n    borderColor: '#657786',\n    borderRadius: 2,\n    borderStyle: 'solid',\n    borderWidth: 2,\n    height: '100%',\n    justifyContent: 'center',\n    width: '100%'\n  },\n  fakeControlChecked: {\n    backgroundColor: '#009688',\n    backgroundImage: 'url(\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8cGF0aAogICAgIGQ9Ik0gMC4wNDAzODA1OSwwLjYyNjc3NjcgMC4xNDY0NDY2MSwwLjUyMDcxMDY4IDAuNDI5Mjg5MzIsMC44MDM1NTMzOSAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IE0gMC4yMTcxNTcyOSwwLjgwMzU1MzM5IDAuODUzNTUzMzksMC4xNjcxNTcyOSAwLjk1OTYxOTQxLDAuMjczMjIzMyAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IgogICAgIGlkPSJyZWN0Mzc4MCIKICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lIiAvPgo8L3N2Zz4K\")',\n    backgroundRepeat: 'no-repeat',\n    borderColor: '#009688'\n  },\n  fakeControlDisabled: {\n    borderColor: '#CCD6DD'\n  },\n  fakeControlCheckedAndDisabled: {\n    backgroundColor: '#AAB8C2',\n    borderColor: '#AAB8C2'\n  },\n  nativeControl: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    height: '100%',\n    margin: 0,\n    appearance: 'none',\n    padding: 0,\n    width: '100%'\n  })\n});\nexport default CheckBox;", "map": {"version": 3, "names": ["_objectSpread", "_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "createElement", "StyleSheet", "View", "CheckBox", "forwardRef", "props", "forwardedRef", "ariaReadOnly", "color", "disabled", "onChange", "onValueChange", "readOnly", "style", "value", "other", "handleChange", "event", "nativeEvent", "target", "checked", "fakeControl", "styles", "fakeControlChecked", "backgroundColor", "borderColor", "fakeControlDisabled", "fakeControlCheckedAndDisabled", "nativeControl", "accessibilityReadOnly", "ref", "cursor<PERSON>nh<PERSON><PERSON>", "type", "root", "cursor<PERSON><PERSON><PERSON>", "displayName", "create", "cursor", "height", "userSelect", "width", "alignItems", "borderRadius", "borderStyle", "borderWidth", "justifyContent", "backgroundImage", "backgroundRepeat", "absoluteFillObject", "margin", "appearance", "padding"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/CheckBox/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"aria-readonly\", \"color\", \"disabled\", \"onChange\", \"onValueChange\", \"readOnly\", \"style\", \"value\"];\nimport * as React from 'react';\nimport createElement from '../createElement';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\nvar CheckBox = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var ariaReadOnly = props['aria-readonly'],\n    color = props.color,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    onValueChange = props.onValueChange,\n    readOnly = props.readOnly,\n    style = props.style,\n    value = props.value,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  function handleChange(event) {\n    var value = event.nativeEvent.target.checked;\n    event.nativeEvent.value = value;\n    onChange && onChange(event);\n    onValueChange && onValueChange(value);\n  }\n  var fakeControl = /*#__PURE__*/React.createElement(View, {\n    style: [styles.fakeControl, value && styles.fakeControlChecked,\n    // custom color\n    value && color && {\n      backgroundColor: color,\n      borderColor: color\n    }, disabled && styles.fakeControlDisabled, value && disabled && styles.fakeControlCheckedAndDisabled]\n  });\n  var nativeControl = createElement('input', {\n    checked: value,\n    disabled: disabled,\n    onChange: handleChange,\n    readOnly: readOnly === true || ariaReadOnly === true || other.accessibilityReadOnly === true,\n    ref: forwardedRef,\n    style: [styles.nativeControl, styles.cursorInherit],\n    type: 'checkbox'\n  });\n  return /*#__PURE__*/React.createElement(View, _extends({}, other, {\n    \"aria-disabled\": disabled,\n    \"aria-readonly\": ariaReadOnly,\n    style: [styles.root, style, disabled && styles.cursorDefault]\n  }), fakeControl, nativeControl);\n});\nCheckBox.displayName = 'CheckBox';\nvar styles = StyleSheet.create({\n  root: {\n    cursor: 'pointer',\n    height: 16,\n    userSelect: 'none',\n    width: 16\n  },\n  cursorDefault: {\n    cursor: 'default'\n  },\n  cursorInherit: {\n    cursor: 'inherit'\n  },\n  fakeControl: {\n    alignItems: 'center',\n    backgroundColor: '#fff',\n    borderColor: '#657786',\n    borderRadius: 2,\n    borderStyle: 'solid',\n    borderWidth: 2,\n    height: '100%',\n    justifyContent: 'center',\n    width: '100%'\n  },\n  fakeControlChecked: {\n    backgroundColor: '#009688',\n    backgroundImage: 'url(\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8cGF0aAogICAgIGQ9Ik0gMC4wNDAzODA1OSwwLjYyNjc3NjcgMC4xNDY0NDY2MSwwLjUyMDcxMDY4IDAuNDI5Mjg5MzIsMC44MDM1NTMzOSAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IE0gMC4yMTcxNTcyOSwwLjgwMzU1MzM5IDAuODUzNTUzMzksMC4xNjcxNTcyOSAwLjk1OTYxOTQxLDAuMjczMjIzMyAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IgogICAgIGlkPSJyZWN0Mzc4MCIKICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lIiAvPgo8L3N2Zz4K\")',\n    backgroundRepeat: 'no-repeat',\n    borderColor: '#009688'\n  },\n  fakeControlDisabled: {\n    borderColor: '#CCD6DD'\n  },\n  fakeControlCheckedAndDisabled: {\n    backgroundColor: '#AAB8C2',\n    borderColor: '#AAB8C2'\n  },\n  nativeControl: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    height: '100%',\n    margin: 0,\n    appearance: 'none',\n    padding: 0,\n    width: '100%'\n  })\n});\nexport default CheckBox;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,sCAAsC;AAChE,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;AACjH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa;AACpB,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,IAAIC,QAAQ,GAAgBJ,KAAK,CAACK,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACpE,IAAIC,YAAY,GAAGF,KAAK,CAAC,eAAe,CAAC;IACvCG,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,KAAK,GAAGlB,6BAA6B,CAACQ,KAAK,EAAEP,SAAS,CAAC;EACzD,SAASkB,YAAYA,CAACC,KAAK,EAAE;IAC3B,IAAIH,KAAK,GAAGG,KAAK,CAACC,WAAW,CAACC,MAAM,CAACC,OAAO;IAC5CH,KAAK,CAACC,WAAW,CAACJ,KAAK,GAAGA,KAAK;IAC/BJ,QAAQ,IAAIA,QAAQ,CAACO,KAAK,CAAC;IAC3BN,aAAa,IAAIA,aAAa,CAACG,KAAK,CAAC;EACvC;EACA,IAAIO,WAAW,GAAgBtB,KAAK,CAACC,aAAa,CAACE,IAAI,EAAE;IACvDW,KAAK,EAAE,CAACS,MAAM,CAACD,WAAW,EAAEP,KAAK,IAAIQ,MAAM,CAACC,kBAAkB,EAE9DT,KAAK,IAAIN,KAAK,IAAI;MAChBgB,eAAe,EAAEhB,KAAK;MACtBiB,WAAW,EAAEjB;IACf,CAAC,EAAEC,QAAQ,IAAIa,MAAM,CAACI,mBAAmB,EAAEZ,KAAK,IAAIL,QAAQ,IAAIa,MAAM,CAACK,6BAA6B;EACtG,CAAC,CAAC;EACF,IAAIC,aAAa,GAAG5B,aAAa,CAAC,OAAO,EAAE;IACzCoB,OAAO,EAAEN,KAAK;IACdL,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEM,YAAY;IACtBJ,QAAQ,EAAEA,QAAQ,KAAK,IAAI,IAAIL,YAAY,KAAK,IAAI,IAAIQ,KAAK,CAACc,qBAAqB,KAAK,IAAI;IAC5FC,GAAG,EAAExB,YAAY;IACjBO,KAAK,EAAE,CAACS,MAAM,CAACM,aAAa,EAAEN,MAAM,CAACS,aAAa,CAAC;IACnDC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,OAAoBjC,KAAK,CAACC,aAAa,CAACE,IAAI,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEmB,KAAK,EAAE;IAChE,eAAe,EAAEN,QAAQ;IACzB,eAAe,EAAEF,YAAY;IAC7BM,KAAK,EAAE,CAACS,MAAM,CAACW,IAAI,EAAEpB,KAAK,EAAEJ,QAAQ,IAAIa,MAAM,CAACY,aAAa;EAC9D,CAAC,CAAC,EAAEb,WAAW,EAAEO,aAAa,CAAC;AACjC,CAAC,CAAC;AACFzB,QAAQ,CAACgC,WAAW,GAAG,UAAU;AACjC,IAAIb,MAAM,GAAGrB,UAAU,CAACmC,MAAM,CAAC;EAC7BH,IAAI,EAAE;IACJI,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE;EACT,CAAC;EACDN,aAAa,EAAE;IACbG,MAAM,EAAE;EACV,CAAC;EACDN,aAAa,EAAE;IACbM,MAAM,EAAE;EACV,CAAC;EACDhB,WAAW,EAAE;IACXoB,UAAU,EAAE,QAAQ;IACpBjB,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE,SAAS;IACtBiB,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,CAAC;IACdN,MAAM,EAAE,MAAM;IACdO,cAAc,EAAE,QAAQ;IACxBL,KAAK,EAAE;EACT,CAAC;EACDjB,kBAAkB,EAAE;IAClBC,eAAe,EAAE,SAAS;IAC1BsB,eAAe,EAAE,m4BAAm4B;IACp5BC,gBAAgB,EAAE,WAAW;IAC7BtB,WAAW,EAAE;EACf,CAAC;EACDC,mBAAmB,EAAE;IACnBD,WAAW,EAAE;EACf,CAAC;EACDE,6BAA6B,EAAE;IAC7BH,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE;EACf,CAAC;EACDG,aAAa,EAAEjC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEM,UAAU,CAAC+C,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;IACjFV,MAAM,EAAE,MAAM;IACdW,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,CAAC;IACVX,KAAK,EAAE;EACT,CAAC;AACH,CAAC,CAAC;AACF,eAAerC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}