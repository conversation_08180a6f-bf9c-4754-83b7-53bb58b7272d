{"ast": null, "code": "var emptyFunction = function emptyFunction() {};\nfunction StatusBar() {\n  return null;\n}\nStatusBar.setBackgroundColor = emptyFunction;\nStatusBar.setBarStyle = emptyFunction;\nStatusBar.setHidden = emptyFunction;\nStatusBar.setNetworkActivityIndicatorVisible = emptyFunction;\nStatusBar.setTranslucent = emptyFunction;\nexport default StatusBar;", "map": {"version": 3, "names": ["emptyFunction", "StatusBar", "setBackgroundColor", "setBarStyle", "setHidden", "setNetworkActivityIndicatorVisible", "setTranslucent"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/StatusBar/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar emptyFunction = () => {};\nfunction StatusBar() {\n  return null;\n}\nStatusBar.setBackgroundColor = emptyFunction;\nStatusBar.setBarStyle = emptyFunction;\nStatusBar.setHidden = emptyFunction;\nStatusBar.setNetworkActivityIndicatorVisible = emptyFunction;\nStatusBar.setTranslucent = emptyFunction;\nexport default StatusBar;"], "mappings": "AASA,IAAIA,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS,CAAC,CAAC;AAC5B,SAASC,SAASA,CAAA,EAAG;EACnB,OAAO,IAAI;AACb;AACAA,SAAS,CAACC,kBAAkB,GAAGF,aAAa;AAC5CC,SAAS,CAACE,WAAW,GAAGH,aAAa;AACrCC,SAAS,CAACG,SAAS,GAAGJ,aAAa;AACnCC,SAAS,CAACI,kCAAkC,GAAGL,aAAa;AAC5DC,SAAS,CAACK,cAAc,GAAGN,aAAa;AACxC,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}