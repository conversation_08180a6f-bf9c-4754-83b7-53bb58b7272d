{"ast": null, "code": "import CheckboxComponent from \"./Checkbox\";\nimport CheckboxAndroid from \"./CheckboxAndroid\";\nimport CheckboxIOS from \"./CheckboxIOS\";\nimport CheckboxItem from \"./CheckboxItem\";\nvar Checkbox = Object.assign(CheckboxComponent, {\n  Item: CheckboxItem,\n  Android: CheckboxAndroid,\n  IOS: CheckboxIOS\n});\nexport default Checkbox;", "map": {"version": 3, "names": ["CheckboxComponent", "CheckboxAndroid", "CheckboxIOS", "CheckboxItem", "Checkbox", "Object", "assign", "<PERSON><PERSON>", "Android", "IOS"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Checkbox/index.ts"], "sourcesContent": ["import CheckboxComponent from './Checkbox';\nimport CheckboxAndroid from './CheckboxAndroid';\nimport CheckboxIOS from './CheckboxIOS';\nimport CheckboxItem from './CheckboxItem';\n\nconst Checkbox = Object.assign(\n  // @component ./Checkbox.tsx\n  CheckboxComponent,\n  {\n    // @component ./CheckboxItem.tsx\n    Item: CheckboxItem,\n    // @component ./CheckboxAndroid.tsx\n    Android: CheckboxAndroid,\n    // @component ./CheckboxIOS.tsx\n    IOS: CheckboxIOS,\n  }\n);\n\nexport default Checkbox;\n"], "mappings": "AAAA,OAAOA,iBAAiB;AACxB,OAAOC,eAAe;AACtB,OAAOC,WAAW;AAClB,OAAOC,YAAY;AAEnB,IAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAE5BN,iBAAiB,EACjB;EAEEO,IAAI,EAAEJ,YAAY;EAElBK,OAAO,EAAEP,eAAe;EAExBQ,GAAG,EAAEP;AACP,CACF,CAAC;AAED,eAAeE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}