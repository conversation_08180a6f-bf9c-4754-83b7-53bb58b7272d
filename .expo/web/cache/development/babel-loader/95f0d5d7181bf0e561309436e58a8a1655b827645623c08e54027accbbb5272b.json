{"ast": null, "code": "export var PermissionStatus;\n(function (PermissionStatus) {\n  PermissionStatus[\"GRANTED\"] = \"granted\";\n  PermissionStatus[\"UNDETERMINED\"] = \"undetermined\";\n  PermissionStatus[\"DENIED\"] = \"denied\";\n})(PermissionStatus || (PermissionStatus = {}));", "map": {"version": 3, "names": ["PermissionStatus"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-modules-core/src/PermissionsInterface.ts"], "sourcesContent": ["export enum PermissionStatus {\n  /**\n   * User has granted the permission.\n   */\n  GRANTED = 'granted',\n  /**\n   * User hasn't granted or denied the permission yet.\n   */\n  UNDETERMINED = 'undetermined',\n  /**\n   * User has denied the permission.\n   */\n  DENIED = 'denied',\n}\n\n/**\n * Permission expiration time. Currently, all permissions are granted permanently.\n */\nexport type PermissionExpiration = 'never' | number;\n\n/**\n * An object obtained by permissions get and request functions.\n */\nexport interface PermissionResponse {\n  /**\n   * Determines the status of the permission.\n   */\n  status: PermissionStatus;\n  /**\n   * Determines time when the permission expires.\n   */\n  expires: PermissionExpiration;\n  /**\n   * A convenience boolean that indicates if the permission is granted.\n   */\n  granted: boolean;\n  /**\n   * Indicates if user can be asked again for specific permission.\n   * If not, one should be directed to the Settings app\n   * in order to enable/disable the permission.\n   */\n  canAskAgain: boolean;\n}\n"], "mappings": "AAAA,WAAYA,gBAaX;AAbD,WAAYA,gBAAgB;EAI1BA,gBAAA,uBAAmB;EAInBA,gBAAA,iCAA6B;EAI7BA,gBAAA,qBAAiB;AACnB,CAAC,EAbWA,gBAAgB,KAAhBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}