{"ast": null, "code": "import React from 'react';\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\nimport { IconButton } from 'react-native-paper';\nimport { ThemeProvider, useTheme } from \"./app/src/theme/ThemeProvider\";\nimport { DatabaseProvider, useDatabase } from \"./app/src/database/DatabaseProvider\";\nimport HomeScreen from \"./app/src/screens/HomeScreen\";\nimport FoodScreen from \"./app/src/screens/FoodScreen\";\nimport ProfileScreen from \"./app/src/screens/ProfileScreen\";\nimport FoodDetailScreen from \"./app/src/screens/FoodDetailScreen\";\nimport AddConsumptionScreen from \"./app/src/screens/AddConsumptionScreen\";\nimport SplashScreen from \"./app/src/screens/SplashScreen\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Tab = createBottomTabNavigator();\nvar Stack = createNativeStackNavigator();\nvar FoodStack = function FoodStack() {\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  return _jsxs(Stack.Navigator, {\n    screenOptions: {\n      headerStyle: {\n        backgroundColor: theme.colors.primary\n      },\n      headerTintColor: theme.colors.onPrimary\n    },\n    children: [_jsx(Stack.Screen, {\n      name: \"FoodList\",\n      component: FoodScreen,\n      options: {\n        title: 'Foods'\n      }\n    }), _jsx(Stack.Screen, {\n      name: \"FoodDetail\",\n      component: FoodDetailScreen,\n      options: function options(_ref) {\n        var _route$params, _route$params$food;\n        var route = _ref.route;\n        return {\n          title: ((_route$params = route.params) == null ? void 0 : (_route$params$food = _route$params.food) == null ? void 0 : _route$params$food.name) || 'Food Details'\n        };\n      }\n    })]\n  });\n};\nvar HomeStack = function HomeStack() {\n  var _useTheme2 = useTheme(),\n    theme = _useTheme2.theme;\n  return _jsxs(Stack.Navigator, {\n    screenOptions: {\n      headerStyle: {\n        backgroundColor: theme.colors.primary\n      },\n      headerTintColor: theme.colors.onPrimary\n    },\n    children: [_jsx(Stack.Screen, {\n      name: \"Home\",\n      component: HomeScreen,\n      options: {\n        title: 'ZnüniZähler'\n      }\n    }), _jsx(Stack.Screen, {\n      name: \"AddConsumption\",\n      component: AddConsumptionScreen,\n      options: {\n        title: 'Add Consumption'\n      }\n    })]\n  });\n};\nvar MainTabs = function MainTabs() {\n  var _useTheme3 = useTheme(),\n    theme = _useTheme3.theme,\n    isDarkMode = _useTheme3.isDarkMode,\n    toggleTheme = _useTheme3.toggleTheme;\n  return _jsxs(Tab.Navigator, {\n    screenOptions: function screenOptions(_ref2) {\n      var route = _ref2.route;\n      return {\n        headerStyle: {\n          backgroundColor: theme.colors.primary\n        },\n        headerTintColor: theme.colors.onPrimary,\n        tabBarActiveTintColor: theme.colors.primary,\n        tabBarInactiveTintColor: theme.colors.onSurfaceDisabled,\n        tabBarStyle: {\n          backgroundColor: theme.colors.surface\n        },\n        tabBarIcon: function tabBarIcon(_ref3) {\n          var focused = _ref3.focused,\n            color = _ref3.color,\n            size = _ref3.size;\n          var iconName;\n          if (route.name === 'HomeTab') {\n            iconName = 'home';\n          } else if (route.name === 'FoodTab') {\n            iconName = 'food-apple';\n          } else if (route.name === 'ProfileTab') {\n            iconName = 'account';\n          }\n          return _jsx(IconButton, {\n            icon: iconName,\n            size: size,\n            iconColor: color\n          });\n        }\n      };\n    },\n    children: [_jsx(Tab.Screen, {\n      name: \"HomeTab\",\n      component: HomeStack,\n      options: {\n        headerShown: false,\n        title: 'Home'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"FoodTab\",\n      component: FoodStack,\n      options: {\n        headerShown: false,\n        title: 'Foods'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"ProfileTab\",\n      component: ProfileScreen,\n      options: {\n        title: 'Profile',\n        headerRight: function headerRight() {\n          return _jsx(IconButton, {\n            icon: isDarkMode ? 'weather-night' : 'weather-sunny',\n            iconColor: theme.colors.onPrimary,\n            onPress: toggleTheme\n          });\n        }\n      }\n    })]\n  });\n};\nvar RootNavigator = function RootNavigator() {\n  var _useDatabase = useDatabase(),\n    isLoading = _useDatabase.isLoading,\n    isInitialized = _useDatabase.isInitialized,\n    error = _useDatabase.error;\n  if (isLoading) {\n    return _jsx(SplashScreen, {\n      message: \"Loading database...\"\n    });\n  }\n  if (error) {\n    return _jsx(SplashScreen, {\n      message: `Error: ${error}`,\n      isError: true\n    });\n  }\n  if (!isInitialized) {\n    return _jsx(SplashScreen, {\n      message: \"Initializing database...\"\n    });\n  }\n  return _jsx(NavigationContainer, {\n    children: _jsx(MainTabs, {})\n  });\n};\nexport default function App() {\n  return _jsx(ThemeProvider, {\n    children: _jsxs(DatabaseProvider, {\n      children: [_jsx(RootNavigator, {}), _jsx(StatusBar, {\n        style: \"auto\"\n      })]\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "StatusBar", "NavigationContainer", "createBottomTabNavigator", "createNativeStackNavigator", "IconButton", "ThemeProvider", "useTheme", "DatabaseProvider", "useDatabase", "HomeScreen", "FoodScreen", "ProfileScreen", "FoodDetailScreen", "AddConsumptionScreen", "SplashScreen", "jsx", "_jsx", "jsxs", "_jsxs", "Tab", "<PERSON><PERSON>", "FoodStack", "_useTheme", "theme", "Navigator", "screenOptions", "headerStyle", "backgroundColor", "colors", "primary", "headerTintColor", "onPrimary", "children", "Screen", "name", "component", "options", "title", "_ref", "_route$params", "_route$params$food", "route", "params", "food", "HomeStack", "_useTheme2", "MainTabs", "_useTheme3", "isDarkMode", "toggleTheme", "_ref2", "tabBarActiveTintColor", "tabBarInactiveTintColor", "onSurfaceDisabled", "tabBarStyle", "surface", "tabBarIcon", "_ref3", "focused", "color", "size", "iconName", "icon", "iconColor", "headerShown", "headerRight", "onPress", "RootNavigator", "_useDatabase", "isLoading", "isInitialized", "error", "message", "isError", "App", "style"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/App.js"], "sourcesContent": ["import React from 'react';\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\nimport { IconButton } from 'react-native-paper';\n\n// Providers\nimport { ThemeProvider, useTheme } from './app/src/theme/ThemeProvider';\nimport { DatabaseProvider, useDatabase } from './app/src/database/DatabaseProvider';\n\n// Screens\nimport HomeScreen from './app/src/screens/HomeScreen';\nimport FoodScreen from './app/src/screens/FoodScreen';\nimport ProfileScreen from './app/src/screens/ProfileScreen';\nimport FoodDetailScreen from './app/src/screens/FoodDetailScreen';\nimport AddConsumptionScreen from './app/src/screens/AddConsumptionScreen';\nimport SplashScreen from './app/src/screens/SplashScreen';\n\n// Create navigators\nconst Tab = createBottomTabNavigator();\nconst Stack = createNativeStackNavigator();\n\n// Food stack navigator\nconst FoodStack = () => {\n  const { theme } = useTheme();\n  \n  return (\n    <Stack.Navigator\n      screenOptions={{\n        headerStyle: {\n          backgroundColor: theme.colors.primary,\n        },\n        headerTintColor: theme.colors.onPrimary,\n      }}\n    >\n      <Stack.Screen \n        name=\"FoodList\" \n        component={FoodScreen}\n        options={{ title: 'Foods' }}\n      />\n      <Stack.Screen \n        name=\"FoodDetail\" \n        component={FoodDetailScreen}\n        options={({ route }) => ({ \n          title: route.params?.food?.name || 'Food Details' \n        })}\n      />\n    </Stack.Navigator>\n  );\n};\n\n// Home stack navigator\nconst HomeStack = () => {\n  const { theme } = useTheme();\n  \n  return (\n    <Stack.Navigator\n      screenOptions={{\n        headerStyle: {\n          backgroundColor: theme.colors.primary,\n        },\n        headerTintColor: theme.colors.onPrimary,\n      }}\n    >\n      <Stack.Screen \n        name=\"Home\" \n        component={HomeScreen}\n        options={{ title: 'ZnüniZähler' }}\n      />\n      <Stack.Screen \n        name=\"AddConsumption\" \n        component={AddConsumptionScreen}\n        options={{ title: 'Add Consumption' }}\n      />\n    </Stack.Navigator>\n  );\n};\n\n// Main tab navigator\nconst MainTabs = () => {\n  const { theme, isDarkMode, toggleTheme } = useTheme();\n  \n  return (\n    <Tab.Navigator\n      screenOptions={({ route }) => ({\n        headerStyle: {\n          backgroundColor: theme.colors.primary,\n        },\n        headerTintColor: theme.colors.onPrimary,\n        tabBarActiveTintColor: theme.colors.primary,\n        tabBarInactiveTintColor: theme.colors.onSurfaceDisabled,\n        tabBarStyle: {\n          backgroundColor: theme.colors.surface,\n        },\n        tabBarIcon: ({ focused, color, size }) => {\n          let iconName;\n          \n          if (route.name === 'HomeTab') {\n            iconName = 'home';\n          } else if (route.name === 'FoodTab') {\n            iconName = 'food-apple';\n          } else if (route.name === 'ProfileTab') {\n            iconName = 'account';\n          }\n          \n          return <IconButton icon={iconName} size={size} iconColor={color} />;\n        },\n      })}\n    >\n      <Tab.Screen \n        name=\"HomeTab\" \n        component={HomeStack} \n        options={{ \n          headerShown: false,\n          title: 'Home',\n        }}\n      />\n      <Tab.Screen \n        name=\"FoodTab\" \n        component={FoodStack} \n        options={{ \n          headerShown: false,\n          title: 'Foods',\n        }}\n      />\n      <Tab.Screen \n        name=\"ProfileTab\" \n        component={ProfileScreen} \n        options={{ \n          title: 'Profile',\n          headerRight: () => (\n            <IconButton\n              icon={isDarkMode ? 'weather-night' : 'weather-sunny'}\n              iconColor={theme.colors.onPrimary}\n              onPress={toggleTheme}\n            />\n          ),\n        }}\n      />\n    </Tab.Navigator>\n  );\n};\n\n// Root navigator\nconst RootNavigator = () => {\n  const { isLoading, isInitialized, error } = useDatabase();\n  \n  if (isLoading) {\n    return <SplashScreen message=\"Loading database...\" />;\n  }\n  \n  if (error) {\n    return <SplashScreen message={`Error: ${error}`} isError />;\n  }\n  \n  if (!isInitialized) {\n    return <SplashScreen message=\"Initializing database...\" />;\n  }\n  \n  return (\n    <NavigationContainer>\n      <MainTabs />\n    </NavigationContainer>\n  );\n};\n\n// Main app component\nexport default function App() {\n  return (\n    <ThemeProvider>\n      <DatabaseProvider>\n        <RootNavigator />\n        <StatusBar style=\"auto\" />\n      </DatabaseProvider>\n    </ThemeProvider>\n  );\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,UAAU,QAAQ,oBAAoB;AAG/C,SAASC,aAAa,EAAEC,QAAQ;AAChC,SAASC,gBAAgB,EAAEC,WAAW;AAGtC,OAAOC,UAAU;AACjB,OAAOC,UAAU;AACjB,OAAOC,aAAa;AACpB,OAAOC,gBAAgB;AACvB,OAAOC,oBAAoB;AAC3B,OAAOC,YAAY;AAAuC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAG1D,IAAMC,GAAG,GAAGjB,wBAAwB,CAAC,CAAC;AACtC,IAAMkB,KAAK,GAAGjB,0BAA0B,CAAC,CAAC;AAG1C,IAAMkB,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;EACtB,IAAAC,SAAA,GAAkBhB,QAAQ,CAAC,CAAC;IAApBiB,KAAK,GAAAD,SAAA,CAALC,KAAK;EAEb,OACEL,KAAA,CAACE,KAAK,CAACI,SAAS;IACdC,aAAa,EAAE;MACbC,WAAW,EAAE;QACXC,eAAe,EAAEJ,KAAK,CAACK,MAAM,CAACC;MAChC,CAAC;MACDC,eAAe,EAAEP,KAAK,CAACK,MAAM,CAACG;IAChC,CAAE;IAAAC,QAAA,GAEFhB,IAAA,CAACI,KAAK,CAACa,MAAM;MACXC,IAAI,EAAC,UAAU;MACfC,SAAS,EAAEzB,UAAW;MACtB0B,OAAO,EAAE;QAAEC,KAAK,EAAE;MAAQ;IAAE,CAC7B,CAAC,EACFrB,IAAA,CAACI,KAAK,CAACa,MAAM;MACXC,IAAI,EAAC,YAAY;MACjBC,SAAS,EAAEvB,gBAAiB;MAC5BwB,OAAO,EAAE,SAATA,OAAOA,CAAAE,IAAA;QAAA,IAAAC,aAAA,EAAAC,kBAAA;QAAA,IAAKC,KAAK,GAAAH,IAAA,CAALG,KAAK;QAAA,OAAQ;UACvBJ,KAAK,EAAE,EAAAE,aAAA,GAAAE,KAAK,CAACC,MAAM,sBAAAF,kBAAA,GAAZD,aAAA,CAAcI,IAAI,qBAAlBH,kBAAA,CAAoBN,IAAI,KAAI;QACrC,CAAC;MAAA;IAAE,CACJ,CAAC;EAAA,CACa,CAAC;AAEtB,CAAC;AAGD,IAAMU,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;EACtB,IAAAC,UAAA,GAAkBvC,QAAQ,CAAC,CAAC;IAApBiB,KAAK,GAAAsB,UAAA,CAALtB,KAAK;EAEb,OACEL,KAAA,CAACE,KAAK,CAACI,SAAS;IACdC,aAAa,EAAE;MACbC,WAAW,EAAE;QACXC,eAAe,EAAEJ,KAAK,CAACK,MAAM,CAACC;MAChC,CAAC;MACDC,eAAe,EAAEP,KAAK,CAACK,MAAM,CAACG;IAChC,CAAE;IAAAC,QAAA,GAEFhB,IAAA,CAACI,KAAK,CAACa,MAAM;MACXC,IAAI,EAAC,MAAM;MACXC,SAAS,EAAE1B,UAAW;MACtB2B,OAAO,EAAE;QAAEC,KAAK,EAAE;MAAc;IAAE,CACnC,CAAC,EACFrB,IAAA,CAACI,KAAK,CAACa,MAAM;MACXC,IAAI,EAAC,gBAAgB;MACrBC,SAAS,EAAEtB,oBAAqB;MAChCuB,OAAO,EAAE;QAAEC,KAAK,EAAE;MAAkB;IAAE,CACvC,CAAC;EAAA,CACa,CAAC;AAEtB,CAAC;AAGD,IAAMS,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;EACrB,IAAAC,UAAA,GAA2CzC,QAAQ,CAAC,CAAC;IAA7CiB,KAAK,GAAAwB,UAAA,CAALxB,KAAK;IAAEyB,UAAU,GAAAD,UAAA,CAAVC,UAAU;IAAEC,WAAW,GAAAF,UAAA,CAAXE,WAAW;EAEtC,OACE/B,KAAA,CAACC,GAAG,CAACK,SAAS;IACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAyB,KAAA;MAAA,IAAKT,KAAK,GAAAS,KAAA,CAALT,KAAK;MAAA,OAAQ;QAC7Bf,WAAW,EAAE;UACXC,eAAe,EAAEJ,KAAK,CAACK,MAAM,CAACC;QAChC,CAAC;QACDC,eAAe,EAAEP,KAAK,CAACK,MAAM,CAACG,SAAS;QACvCoB,qBAAqB,EAAE5B,KAAK,CAACK,MAAM,CAACC,OAAO;QAC3CuB,uBAAuB,EAAE7B,KAAK,CAACK,MAAM,CAACyB,iBAAiB;QACvDC,WAAW,EAAE;UACX3B,eAAe,EAAEJ,KAAK,CAACK,MAAM,CAAC2B;QAChC,CAAC;QACDC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA,EAAgC;UAAA,IAA3BC,OAAO,GAAAD,KAAA,CAAPC,OAAO;YAAEC,KAAK,GAAAF,KAAA,CAALE,KAAK;YAAEC,IAAI,GAAAH,KAAA,CAAJG,IAAI;UACjC,IAAIC,QAAQ;UAEZ,IAAIpB,KAAK,CAACP,IAAI,KAAK,SAAS,EAAE;YAC5B2B,QAAQ,GAAG,MAAM;UACnB,CAAC,MAAM,IAAIpB,KAAK,CAACP,IAAI,KAAK,SAAS,EAAE;YACnC2B,QAAQ,GAAG,YAAY;UACzB,CAAC,MAAM,IAAIpB,KAAK,CAACP,IAAI,KAAK,YAAY,EAAE;YACtC2B,QAAQ,GAAG,SAAS;UACtB;UAEA,OAAO7C,IAAA,CAACZ,UAAU;YAAC0D,IAAI,EAAED,QAAS;YAACD,IAAI,EAAEA,IAAK;YAACG,SAAS,EAAEJ;UAAM,CAAE,CAAC;QACrE;MACF,CAAC;IAAA,CAAE;IAAA3B,QAAA,GAEHhB,IAAA,CAACG,GAAG,CAACc,MAAM;MACTC,IAAI,EAAC,SAAS;MACdC,SAAS,EAAES,SAAU;MACrBR,OAAO,EAAE;QACP4B,WAAW,EAAE,KAAK;QAClB3B,KAAK,EAAE;MACT;IAAE,CACH,CAAC,EACFrB,IAAA,CAACG,GAAG,CAACc,MAAM;MACTC,IAAI,EAAC,SAAS;MACdC,SAAS,EAAEd,SAAU;MACrBe,OAAO,EAAE;QACP4B,WAAW,EAAE,KAAK;QAClB3B,KAAK,EAAE;MACT;IAAE,CACH,CAAC,EACFrB,IAAA,CAACG,GAAG,CAACc,MAAM;MACTC,IAAI,EAAC,YAAY;MACjBC,SAAS,EAAExB,aAAc;MACzByB,OAAO,EAAE;QACPC,KAAK,EAAE,SAAS;QAChB4B,WAAW,EAAE,SAAbA,WAAWA,CAAA;UAAA,OACTjD,IAAA,CAACZ,UAAU;YACT0D,IAAI,EAAEd,UAAU,GAAG,eAAe,GAAG,eAAgB;YACrDe,SAAS,EAAExC,KAAK,CAACK,MAAM,CAACG,SAAU;YAClCmC,OAAO,EAAEjB;UAAY,CACtB,CAAC;QAAA;MAEN;IAAE,CACH,CAAC;EAAA,CACW,CAAC;AAEpB,CAAC;AAGD,IAAMkB,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,IAAAC,YAAA,GAA4C5D,WAAW,CAAC,CAAC;IAAjD6D,SAAS,GAAAD,YAAA,CAATC,SAAS;IAAEC,aAAa,GAAAF,YAAA,CAAbE,aAAa;IAAEC,KAAK,GAAAH,YAAA,CAALG,KAAK;EAEvC,IAAIF,SAAS,EAAE;IACb,OAAOrD,IAAA,CAACF,YAAY;MAAC0D,OAAO,EAAC;IAAqB,CAAE,CAAC;EACvD;EAEA,IAAID,KAAK,EAAE;IACT,OAAOvD,IAAA,CAACF,YAAY;MAAC0D,OAAO,EAAE,UAAUD,KAAK,EAAG;MAACE,OAAO;IAAA,CAAE,CAAC;EAC7D;EAEA,IAAI,CAACH,aAAa,EAAE;IAClB,OAAOtD,IAAA,CAACF,YAAY;MAAC0D,OAAO,EAAC;IAA0B,CAAE,CAAC;EAC5D;EAEA,OACExD,IAAA,CAACf,mBAAmB;IAAA+B,QAAA,EAClBhB,IAAA,CAAC8B,QAAQ,IAAE;EAAC,CACO,CAAC;AAE1B,CAAC;AAGD,eAAe,SAAS4B,GAAGA,CAAA,EAAG;EAC5B,OACE1D,IAAA,CAACX,aAAa;IAAA2B,QAAA,EACZd,KAAA,CAACX,gBAAgB;MAAAyB,QAAA,GACfhB,IAAA,CAACmD,aAAa,IAAE,CAAC,EACjBnD,IAAA,CAAChB,SAAS;QAAC2E,KAAK,EAAC;MAAM,CAAE,CAAC;IAAA,CACV;EAAC,CACN,CAAC;AAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}