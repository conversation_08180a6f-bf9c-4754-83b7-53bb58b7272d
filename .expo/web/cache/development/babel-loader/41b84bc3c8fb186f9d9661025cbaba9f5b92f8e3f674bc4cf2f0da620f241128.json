{"ast": null, "code": "import * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nexport default function useAnimatedValueArray(initialValues) {\n  var refs = React.useRef([]);\n  refs.current.length = initialValues.length;\n  initialValues.forEach(function (initialValue, i) {\n    var _refs$current$i;\n    refs.current[i] = (_refs$current$i = refs.current[i]) != null ? _refs$current$i : new Animated.Value(initialValue);\n  });\n  return refs.current;\n}", "map": {"version": 3, "names": ["React", "Animated", "useAnimatedValueArray", "initialValues", "refs", "useRef", "current", "length", "for<PERSON>ach", "initialValue", "i", "_refs$current$i", "Value"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/utils/useAnimatedValueArray.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated } from 'react-native';\n\nexport default function useAnimatedValueArray(initialValues: number[]) {\n  const refs = React.useRef<Animated.Value[]>([]);\n\n  refs.current.length = initialValues.length;\n  initialValues.forEach((initialValue, i) => {\n    refs.current[i] = refs.current[i] ?? new Animated.Value(initialValue);\n  });\n\n  return refs.current;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAG9B,eAAe,SAASC,qBAAqBA,CAACC,aAAuB,EAAE;EACrE,IAAMC,IAAI,GAAGJ,KAAK,CAACK,MAAM,CAAmB,EAAE,CAAC;EAE/CD,IAAI,CAACE,OAAO,CAACC,MAAM,GAAGJ,aAAa,CAACI,MAAM;EAC1CJ,aAAa,CAACK,OAAO,CAAC,UAACC,YAAY,EAAEC,CAAC,EAAK;IAAA,IAAAC,eAAA;IACzCP,IAAI,CAACE,OAAO,CAACI,CAAC,CAAC,IAAAC,eAAA,GAAGP,IAAI,CAACE,OAAO,CAACI,CAAC,CAAC,YAAAC,eAAA,GAAI,IAAIV,QAAQ,CAACW,KAAK,CAACH,YAAY,CAAC;EACvE,CAAC,CAAC;EAEF,OAAOL,IAAI,CAACE,OAAO;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}