{"ast": null, "code": "import StatusBar from \"react-native-web/dist/exports/StatusBar\";\nexport default function setStatusBarTranslucent(translucent) {\n  StatusBar.setTranslucent(translucent);\n}", "map": {"version": 3, "names": ["setStatusBarTranslucent", "translucent", "StatusBar", "setTranslucent"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-status-bar/src/setStatusBarTranslucent.ts"], "sourcesContent": ["import { StatusBar } from 'react-native';\n\n// @needsAudit\n/**\n * Set the translucency of the status bar.\n * @param translucent Whether the app can draw under the status bar. When `true`, content will be\n * rendered under the status bar. This is always `true` on iOS and cannot be changed.\n * @platform android\n */\nexport default function setStatusBarTranslucent(translucent: boolean) {\n  StatusBar.setTranslucent(translucent);\n}\n"], "mappings": ";AASA,eAAc,SAAUA,uBAAuBA,CAACC,WAAoB;EAClEC,SAAS,CAACC,cAAc,CAACF,WAAW,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}