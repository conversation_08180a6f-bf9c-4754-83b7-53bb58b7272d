{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"iconColor\", \"rippleColor\", \"onIconPress\", \"searchAccessibilityLabel\", \"clearIcon\", \"clearAccessibilityLabel\", \"onClearIconPress\", \"traileringIcon\", \"traileringIconColor\", \"traileringIconAccessibilityLabel\", \"traileringRippleColor\", \"onTraileringIconPress\", \"right\", \"mode\", \"showDivider\", \"inputStyle\", \"placeholder\", \"elevation\", \"style\", \"theme\", \"value\", \"loading\", \"testID\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport ActivityIndicator from \"./ActivityIndicator\";\nimport Divider from \"./Divider\";\nimport IconButton from \"./IconButton/IconButton\";\nimport MaterialCommunityIcon from \"./MaterialCommunityIcon\";\nimport Surface from \"./Surface\";\nimport { useInternalTheme } from \"../core/theming\";\nimport { forwardRef } from \"../utils/forwardRef\";\nvar Searchbar = forwardRef(function (_ref, ref) {\n  var _theme$colors, _theme$colors2;\n  var icon = _ref.icon,\n    customIconColor = _ref.iconColor,\n    customRippleColor = _ref.rippleColor,\n    onIconPress = _ref.onIconPress,\n    _ref$searchAccessibil = _ref.searchAccessibilityLabel,\n    searchAccessibilityLabel = _ref$searchAccessibil === void 0 ? 'search' : _ref$searchAccessibil,\n    clearIcon = _ref.clearIcon,\n    _ref$clearAccessibili = _ref.clearAccessibilityLabel,\n    clearAccessibilityLabel = _ref$clearAccessibili === void 0 ? 'clear' : _ref$clearAccessibili,\n    onClearIconPress = _ref.onClearIconPress,\n    traileringIcon = _ref.traileringIcon,\n    traileringIconColor = _ref.traileringIconColor,\n    traileringIconAccessibilityLabel = _ref.traileringIconAccessibilityLabel,\n    customTraileringRippleColor = _ref.traileringRippleColor,\n    onTraileringIconPress = _ref.onTraileringIconPress,\n    right = _ref.right,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'bar' : _ref$mode,\n    _ref$showDivider = _ref.showDivider,\n    showDivider = _ref$showDivider === void 0 ? true : _ref$showDivider,\n    inputStyle = _ref.inputStyle,\n    placeholder = _ref.placeholder,\n    _ref$elevation = _ref.elevation,\n    elevation = _ref$elevation === void 0 ? 0 : _ref$elevation,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    value = _ref.value,\n    _ref$loading = _ref.loading,\n    loading = _ref$loading === void 0 ? false : _ref$loading,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'search-bar' : _ref$testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var root = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        var _root$current;\n        return (_root$current = root.current) === null || _root$current === void 0 ? void 0 : _root$current.focus();\n      },\n      clear: function clear() {\n        var _root$current2;\n        return (_root$current2 = root.current) === null || _root$current2 === void 0 ? void 0 : _root$current2.clear();\n      },\n      setNativeProps: function setNativeProps(args) {\n        var _root$current3;\n        return (_root$current3 = root.current) === null || _root$current3 === void 0 ? void 0 : _root$current3.setNativeProps(args);\n      },\n      isFocused: function isFocused() {\n        var _root$current4;\n        return ((_root$current4 = root.current) === null || _root$current4 === void 0 ? void 0 : _root$current4.isFocused()) || false;\n      },\n      blur: function blur() {\n        var _root$current5;\n        return (_root$current5 = root.current) === null || _root$current5 === void 0 ? void 0 : _root$current5.blur();\n      },\n      setSelection: function setSelection(start, end) {\n        var _root$current6;\n        return (_root$current6 = root.current) === null || _root$current6 === void 0 ? void 0 : _root$current6.setSelection(start, end);\n      }\n    };\n  });\n  var handleClearPress = function handleClearPress(e) {\n    var _root$current7, _rest$onChangeText;\n    (_root$current7 = root.current) === null || _root$current7 === void 0 ? void 0 : _root$current7.clear();\n    (_rest$onChangeText = rest.onChangeText) === null || _rest$onChangeText === void 0 ? void 0 : _rest$onChangeText.call(rest, '');\n    onClearIconPress === null || onClearIconPress === void 0 ? void 0 : onClearIconPress(e);\n  };\n  var roundness = theme.roundness,\n    dark = theme.dark,\n    isV3 = theme.isV3,\n    fonts = theme.fonts;\n  var placeholderTextColor = isV3 ? theme.colors.onSurface : (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.placeholder;\n  var textColor = isV3 ? theme.colors.onSurfaceVariant : theme.colors.text;\n  var md2IconColor = dark ? textColor : color(textColor).alpha(0.54).rgb().string();\n  var iconColor = customIconColor || (isV3 ? theme.colors.onSurfaceVariant : md2IconColor);\n  var rippleColor = customRippleColor || color(textColor).alpha(0.32).rgb().string();\n  var traileringRippleColor = customTraileringRippleColor || color(textColor).alpha(0.32).rgb().string();\n  var font = isV3 ? _objectSpread(_objectSpread({}, fonts.bodyLarge), {}, {\n    lineHeight: Platform.select({\n      ios: 0,\n      default: fonts.bodyLarge.lineHeight\n    })\n  }) : theme.fonts.regular;\n  var isBarMode = isV3 && mode === 'bar';\n  var shouldRenderTraileringIcon = isBarMode && traileringIcon && !loading && (!value || right !== undefined);\n  return React.createElement(Surface, _extends({\n    style: [{\n      borderRadius: roundness\n    }, !isV3 && styles.elevation, isV3 && {\n      backgroundColor: theme.colors.elevation.level3,\n      borderRadius: roundness * (isBarMode ? 7 : 0)\n    }, styles.container, style],\n    testID: `${testID}-container`\n  }, theme.isV3 && {\n    elevation: elevation\n  }, {\n    theme: theme\n  }), React.createElement(IconButton, {\n    accessibilityRole: \"button\",\n    borderless: true,\n    rippleColor: rippleColor,\n    onPress: onIconPress,\n    iconColor: iconColor,\n    icon: icon || function (_ref2) {\n      var size = _ref2.size,\n        color = _ref2.color;\n      return React.createElement(MaterialCommunityIcon, {\n        name: \"magnify\",\n        color: color,\n        size: size,\n        direction: I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'\n      });\n    },\n    theme: theme,\n    accessibilityLabel: searchAccessibilityLabel,\n    testID: `${testID}-icon`\n  }), React.createElement(TextInput, _extends({\n    style: [styles.input, _objectSpread(_objectSpread({\n      color: textColor\n    }, font), Platform.select({\n      web: {\n        outline: 'none'\n      }\n    })), isV3 && (isBarMode ? styles.barModeInput : styles.viewModeInput), inputStyle],\n    placeholder: placeholder || '',\n    placeholderTextColor: placeholderTextColor,\n    selectionColor: (_theme$colors2 = theme.colors) === null || _theme$colors2 === void 0 ? void 0 : _theme$colors2.primary,\n    underlineColorAndroid: \"transparent\",\n    returnKeyType: \"search\",\n    keyboardAppearance: dark ? 'dark' : 'light',\n    accessibilityRole: \"search\",\n    ref: root,\n    value: value,\n    testID: testID\n  }, rest)), loading ? React.createElement(ActivityIndicator, {\n    testID: \"activity-indicator\",\n    style: isV3 ? styles.v3Loader : styles.loader\n  }) : React.createElement(View, {\n    pointerEvents: value ? 'auto' : 'none',\n    testID: `${testID}-icon-wrapper`,\n    style: [isV3 && !value && styles.v3ClearIcon, isV3 && right !== undefined && styles.v3ClearIconHidden]\n  }, React.createElement(IconButton, {\n    borderless: true,\n    accessibilityLabel: clearAccessibilityLabel,\n    iconColor: value ? iconColor : 'rgba(255, 255, 255, 0)',\n    rippleColor: rippleColor,\n    onPress: handleClearPress,\n    icon: clearIcon || function (_ref3) {\n      var size = _ref3.size,\n        color = _ref3.color;\n      return React.createElement(MaterialCommunityIcon, {\n        name: isV3 ? 'close' : 'close-circle-outline',\n        color: color,\n        size: size,\n        direction: I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'\n      });\n    },\n    testID: `${testID}-clear-icon`,\n    accessibilityRole: \"button\",\n    theme: theme\n  })), shouldRenderTraileringIcon ? React.createElement(IconButton, {\n    accessibilityRole: \"button\",\n    borderless: true,\n    onPress: onTraileringIconPress,\n    iconColor: traileringIconColor || theme.colors.onSurfaceVariant,\n    rippleColor: traileringRippleColor,\n    icon: traileringIcon,\n    accessibilityLabel: traileringIconAccessibilityLabel,\n    testID: `${testID}-trailering-icon`\n  }) : null, isBarMode && (right === null || right === void 0 ? void 0 : right({\n    color: textColor,\n    style: styles.rightStyle,\n    testID: testID\n  })), isV3 && !isBarMode && showDivider && React.createElement(Divider, {\n    bold: true,\n    style: [styles.divider, {\n      backgroundColor: theme.colors.outline\n    }],\n    testID: `${testID}-divider`\n  }));\n});\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center'\n  },\n  input: {\n    flex: 1,\n    fontSize: 18,\n    paddingLeft: 8,\n    alignSelf: 'stretch',\n    textAlign: I18nManager.getConstants().isRTL ? 'right' : 'left',\n    minWidth: 0\n  },\n  barModeInput: {\n    paddingLeft: 0,\n    minHeight: 56\n  },\n  viewModeInput: {\n    paddingLeft: 0,\n    minHeight: 72\n  },\n  elevation: {\n    elevation: 4\n  },\n  loader: {\n    margin: 10\n  },\n  v3Loader: {\n    marginHorizontal: 16\n  },\n  rightStyle: {\n    marginRight: 16\n  },\n  v3ClearIcon: {\n    position: 'absolute',\n    right: 0,\n    marginLeft: 16\n  },\n  v3ClearIconHidden: {\n    display: 'none'\n  },\n  divider: {\n    position: 'absolute',\n    bottom: 0,\n    width: '100%'\n  }\n});\nexport default Searchbar;", "map": {"version": 3, "names": ["React", "I18nManager", "Platform", "StyleSheet", "TextInput", "View", "color", "ActivityIndicator", "Divider", "IconButton", "MaterialCommunityIcon", "Surface", "useInternalTheme", "forwardRef", "Searchbar", "_ref", "ref", "_theme$colors", "_theme$colors2", "icon", "customIconColor", "iconColor", "customRippleColor", "rippleColor", "onIconPress", "_ref$searchAccessibil", "searchAccessibilityLabel", "clearIcon", "_ref$clearAccessibili", "clearAccessibilityLabel", "onClearIconPress", "traileringIcon", "traileringIconColor", "traileringIconAccessibilityLabel", "customTraileringRippleColor", "traileringRippleColor", "onTraileringIconPress", "right", "_ref$mode", "mode", "_ref$showDivider", "showDivider", "inputStyle", "placeholder", "_ref$elevation", "elevation", "style", "themeOverrides", "theme", "value", "_ref$loading", "loading", "_ref$testID", "testID", "rest", "_objectWithoutProperties", "_excluded", "root", "useRef", "useImperativeHandle", "focus", "_root$current", "current", "clear", "_root$current2", "setNativeProps", "args", "_root$current3", "isFocused", "_root$current4", "blur", "_root$current5", "setSelection", "start", "end", "_root$current6", "handleClearPress", "e", "_root$current7", "_rest$onChangeText", "onChangeText", "call", "roundness", "dark", "isV3", "fonts", "placeholderTextColor", "colors", "onSurface", "textColor", "onSurfaceVariant", "text", "md2IconColor", "alpha", "rgb", "string", "font", "_objectSpread", "bodyLarge", "lineHeight", "select", "ios", "default", "regular", "isBarMode", "shouldRenderTraileringIcon", "undefined", "createElement", "_extends", "borderRadius", "styles", "backgroundColor", "level3", "container", "accessibilityRole", "borderless", "onPress", "_ref2", "size", "name", "direction", "getConstants", "isRTL", "accessibilityLabel", "input", "web", "outline", "barModeInput", "viewModeInput", "selectionColor", "primary", "underlineColorAndroid", "returnKeyType", "keyboardAppearance", "v3Loader", "loader", "pointerEvents", "v3ClearIcon", "v3ClearIconHidden", "_ref3", "rightStyle", "bold", "divider", "create", "flexDirection", "alignItems", "flex", "fontSize", "paddingLeft", "alignSelf", "textAlign", "min<PERSON><PERSON><PERSON>", "minHeight", "margin", "marginHorizontal", "marginRight", "position", "marginLeft", "display", "bottom", "width"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Searchbar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  ColorValue,\n  GestureResponderEvent,\n  I18nManager,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  TextInput,\n  TextInputProps,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\n\nimport ActivityIndicator from './ActivityIndicator';\nimport Divider from './Divider';\nimport type { IconSource } from './Icon';\nimport IconButton from './IconButton/IconButton';\nimport MaterialCommunityIcon from './MaterialCommunityIcon';\nimport Surface from './Surface';\nimport { useInternalTheme } from '../core/theming';\nimport type { ThemeProp } from '../types';\nimport { forwardRef } from '../utils/forwardRef';\n\ninterface Style {\n  marginRight: number;\n}\n\nexport type Props = React.ComponentPropsWithRef<typeof TextInput> & {\n  /**\n   * Hint text shown when the input is empty.\n   */\n  placeholder?: string;\n  /**\n   * The value of the text input.\n   */\n  value: string;\n  /**\n   * Callback that is called when the text input's text changes.\n   */\n  onChangeText?: (query: string) => void;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Search layout mode, the default value is \"bar\".\n   */\n  mode?: 'bar' | 'view';\n  /**\n   * Icon name for the left icon button (see `onIconPress`).\n   */\n  icon?: IconSource;\n  /**\n   * Custom color for icon, default will be derived from theme\n   */\n  iconColor?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Callback to execute if we want the left icon to act as button.\n   */\n  onIconPress?: (e: GestureResponderEvent) => void;\n\n  /**\n   * Callback to execute if we want to add custom behaviour to close icon button.\n   */\n  onClearIconPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Accessibility label for the button. This is read by the screen reader when the user taps the button.\n   */\n  searchAccessibilityLabel?: string;\n  /**\n   * Custom icon for clear button, default will be icon close. It's visible when `loading` is set to `false`.\n   * In v5.x with theme version 3, `clearIcon` is visible only `right` prop is not defined.\n   */\n  clearIcon?: IconSource;\n  /**\n   * Accessibility label for the button. This is read by the screen reader when the user taps the button.\n   */\n  clearAccessibilityLabel?: string;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Icon name for the right trailering icon button.\n   * Works only when `mode` is set to \"bar\". It won't be displayed if `loading` is set to `true`.\n   */\n  traileringIcon?: IconSource;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Custom color for the right trailering icon, default will be derived from theme\n   */\n  traileringIconColor?: string;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Color of the trailering icon ripple effect.\n   */\n  traileringRippleColor?: ColorValue;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Callback to execute on the right trailering icon button press.\n   */\n  onTraileringIconPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Accessibility label for the right trailering icon button. This is read by the screen reader when the user taps the button.\n   */\n  traileringIconAccessibilityLabel?: string;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Callback which returns a React element to display on the right side.\n   * Works only when `mode` is set to \"bar\".\n   */\n  right?: (props: {\n    color: string;\n    style: Style;\n    testID: string;\n  }) => React.ReactNode;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Whether to show `Divider` at the bottom of the search.\n   * Works only when `mode` is set to \"view\". True by default.\n   */\n  showDivider?: boolean;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Changes Searchbar shadow and background on iOS and Android.\n   */\n  elevation?: 0 | 1 | 2 | 3 | 4 | 5 | Animated.Value;\n  /**\n   * Set style of the TextInput component inside the searchbar\n   */\n  inputStyle?: StyleProp<TextStyle>;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Custom flag for replacing clear button with activity indicator.\n   */\n  loading?: Boolean;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\ntype TextInputHandles = Pick<\n  TextInput,\n  'setNativeProps' | 'isFocused' | 'clear' | 'blur' | 'focus' | 'setSelection'\n>;\n\n/**\n * Searchbar is a simple input box where users can type search queries.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Searchbar } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [searchQuery, setSearchQuery] = React.useState('');\n *\n *   return (\n *     <Searchbar\n *       placeholder=\"Search\"\n *       onChangeText={setSearchQuery}\n *       value={searchQuery}\n *     />\n *   );\n * };\n *\n * export default MyComponent;\n\n * ```\n */\nconst Searchbar = forwardRef<TextInputHandles, Props>(\n  (\n    {\n      icon,\n      iconColor: customIconColor,\n      rippleColor: customRippleColor,\n      onIconPress,\n      searchAccessibilityLabel = 'search',\n      clearIcon,\n      clearAccessibilityLabel = 'clear',\n      onClearIconPress,\n      traileringIcon,\n      traileringIconColor,\n      traileringIconAccessibilityLabel,\n      traileringRippleColor: customTraileringRippleColor,\n      onTraileringIconPress,\n      right,\n      mode = 'bar',\n      showDivider = true,\n      inputStyle,\n      placeholder,\n      elevation = 0,\n      style,\n      theme: themeOverrides,\n      value,\n      loading = false,\n      testID = 'search-bar',\n      ...rest\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(themeOverrides);\n    const root = React.useRef<TextInput>(null);\n\n    React.useImperativeHandle(ref, () => ({\n      focus: () => root.current?.focus(),\n      clear: () => root.current?.clear(),\n      setNativeProps: (args: TextInputProps) =>\n        root.current?.setNativeProps(args),\n      isFocused: () => root.current?.isFocused() || false,\n      blur: () => root.current?.blur(),\n      setSelection: (start: number, end: number) =>\n        root.current?.setSelection(start, end),\n    }));\n\n    const handleClearPress = (e: any) => {\n      root.current?.clear();\n      rest.onChangeText?.('');\n      onClearIconPress?.(e);\n    };\n\n    const { roundness, dark, isV3, fonts } = theme;\n\n    const placeholderTextColor = isV3\n      ? theme.colors.onSurface\n      : theme.colors?.placeholder;\n    const textColor = isV3 ? theme.colors.onSurfaceVariant : theme.colors.text;\n    const md2IconColor = dark\n      ? textColor\n      : color(textColor).alpha(0.54).rgb().string();\n    const iconColor =\n      customIconColor || (isV3 ? theme.colors.onSurfaceVariant : md2IconColor);\n    const rippleColor =\n      customRippleColor || color(textColor).alpha(0.32).rgb().string();\n    const traileringRippleColor =\n      customTraileringRippleColor ||\n      color(textColor).alpha(0.32).rgb().string();\n\n    const font = isV3\n      ? {\n          ...fonts.bodyLarge,\n          lineHeight: Platform.select({\n            ios: 0,\n            default: fonts.bodyLarge.lineHeight,\n          }),\n        }\n      : theme.fonts.regular;\n\n    const isBarMode = isV3 && mode === 'bar';\n    const shouldRenderTraileringIcon =\n      isBarMode &&\n      traileringIcon &&\n      !loading &&\n      (!value || right !== undefined);\n\n    return (\n      <Surface\n        style={[\n          { borderRadius: roundness },\n          !isV3 && styles.elevation,\n          isV3 && {\n            backgroundColor: theme.colors.elevation.level3,\n            borderRadius: roundness * (isBarMode ? 7 : 0),\n          },\n          styles.container,\n          style,\n        ]}\n        testID={`${testID}-container`}\n        {...(theme.isV3 && { elevation })}\n        theme={theme}\n      >\n        <IconButton\n          accessibilityRole=\"button\"\n          borderless\n          rippleColor={rippleColor}\n          onPress={onIconPress}\n          iconColor={iconColor}\n          icon={\n            icon ||\n            (({ size, color }) => (\n              <MaterialCommunityIcon\n                name=\"magnify\"\n                color={color}\n                size={size}\n                direction={I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'}\n              />\n            ))\n          }\n          theme={theme}\n          accessibilityLabel={searchAccessibilityLabel}\n          testID={`${testID}-icon`}\n        />\n        <TextInput\n          style={[\n            styles.input,\n            {\n              color: textColor,\n              ...font,\n              ...Platform.select({ web: { outline: 'none' } }),\n            },\n            isV3 && (isBarMode ? styles.barModeInput : styles.viewModeInput),\n            inputStyle,\n          ]}\n          placeholder={placeholder || ''}\n          placeholderTextColor={placeholderTextColor}\n          selectionColor={theme.colors?.primary}\n          underlineColorAndroid=\"transparent\"\n          returnKeyType=\"search\"\n          keyboardAppearance={dark ? 'dark' : 'light'}\n          accessibilityRole=\"search\"\n          ref={root}\n          value={value}\n          testID={testID}\n          {...rest}\n        />\n        {loading ? (\n          <ActivityIndicator\n            testID=\"activity-indicator\"\n            style={isV3 ? styles.v3Loader : styles.loader}\n          />\n        ) : (\n          // Clear icon should be always rendered within Searchbar – it's transparent,\n          // without touch events, when there is no value. It's done to avoid issues\n          // with the abruptly stopping ripple effect and changing bar width on web,\n          // when clearing the value.\n          <View\n            pointerEvents={value ? 'auto' : 'none'}\n            testID={`${testID}-icon-wrapper`}\n            style={[\n              isV3 && !value && styles.v3ClearIcon,\n              isV3 && right !== undefined && styles.v3ClearIconHidden,\n            ]}\n          >\n            <IconButton\n              borderless\n              accessibilityLabel={clearAccessibilityLabel}\n              iconColor={value ? iconColor : 'rgba(255, 255, 255, 0)'}\n              rippleColor={rippleColor}\n              onPress={handleClearPress}\n              icon={\n                clearIcon ||\n                (({ size, color }) => (\n                  <MaterialCommunityIcon\n                    name={isV3 ? 'close' : 'close-circle-outline'}\n                    color={color}\n                    size={size}\n                    direction={I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'}\n                  />\n                ))\n              }\n              testID={`${testID}-clear-icon`}\n              accessibilityRole=\"button\"\n              theme={theme}\n            />\n          </View>\n        )}\n        {shouldRenderTraileringIcon ? (\n          <IconButton\n            accessibilityRole=\"button\"\n            borderless\n            onPress={onTraileringIconPress}\n            iconColor={traileringIconColor || theme.colors.onSurfaceVariant}\n            rippleColor={traileringRippleColor}\n            icon={traileringIcon}\n            accessibilityLabel={traileringIconAccessibilityLabel}\n            testID={`${testID}-trailering-icon`}\n          />\n        ) : null}\n        {isBarMode &&\n          right?.({ color: textColor, style: styles.rightStyle, testID })}\n        {isV3 && !isBarMode && showDivider && (\n          <Divider\n            bold\n            style={[\n              styles.divider,\n              {\n                backgroundColor: theme.colors.outline,\n              },\n            ]}\n            testID={`${testID}-divider`}\n          />\n        )}\n      </Surface>\n    );\n  }\n);\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n  input: {\n    flex: 1,\n    fontSize: 18,\n    paddingLeft: 8,\n    alignSelf: 'stretch',\n    textAlign: I18nManager.getConstants().isRTL ? 'right' : 'left',\n    minWidth: 0,\n  },\n  barModeInput: {\n    paddingLeft: 0,\n    minHeight: 56,\n  },\n  viewModeInput: {\n    paddingLeft: 0,\n    minHeight: 72,\n  },\n  elevation: {\n    elevation: 4,\n  },\n  loader: {\n    margin: 10,\n  },\n  v3Loader: {\n    marginHorizontal: 16,\n  },\n  rightStyle: {\n    marginRight: 16,\n  },\n  v3ClearIcon: {\n    position: 'absolute',\n    right: 0,\n    marginLeft: 16,\n  },\n  v3ClearIconHidden: {\n    display: 'none',\n  },\n  divider: {\n    position: 'absolute',\n    bottom: 0,\n    width: '100%',\n  },\n});\n\nexport default Searchbar;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,IAAA;AAgB9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,OAAOC,iBAAiB;AACxB,OAAOC,OAAO;AAEd,OAAOC,UAAU;AACjB,OAAOC,qBAAqB;AAC5B,OAAOC,OAAO;AACd,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AAwJnB,IAAMC,SAAS,GAAGD,UAAU,CAC1B,UAAAE,IAAA,EA4BEC,GAAG,EACA;EAAA,IAAAC,aAAA,EAAAC,cAAA;EAAA,IA3BDC,IAAI,GAyBEJ,IAAA,CAzBNI,IAAI;IACOC,eAAe,GAwBpBL,IAAA,CAxBNM,SAAS;IACIC,iBAAiB,GAuBxBP,IAAA,CAvBNQ,WAAW;IACXC,WAAW,GAsBLT,IAAA,CAtBNS,WAAW;IAAAC,qBAAA,GAsBLV,IAAA,CArBNW,wBAAwB;IAAxBA,wBAAwB,GAAAD,qBAAA,cAAG,QAAQ,GAAAA,qBAAA;IACnCE,SAAS,GAoBHZ,IAAA,CApBNY,SAAS;IAAAC,qBAAA,GAoBHb,IAAA,CAnBNc,uBAAuB;IAAvBA,uBAAuB,GAAAD,qBAAA,cAAG,OAAO,GAAAA,qBAAA;IACjCE,gBAAgB,GAkBVf,IAAA,CAlBNe,gBAAgB;IAChBC,cAAc,GAiBRhB,IAAA,CAjBNgB,cAAc;IACdC,mBAAmB,GAgBbjB,IAAA,CAhBNiB,mBAAmB;IACnBC,gCAAgC,GAe1BlB,IAAA,CAfNkB,gCAAgC;IACTC,2BAA2B,GAc5CnB,IAAA,CAdNoB,qBAAqB;IACrBC,qBAAqB,GAafrB,IAAA,CAbNqB,qBAAqB;IACrBC,KAAK,GAYCtB,IAAA,CAZNsB,KAAK;IAAAC,SAAA,GAYCvB,IAAA,CAXNwB,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,KAAK,GAAAA,SAAA;IAAAE,gBAAA,GAWNzB,IAAA,CAVN0B,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,IAAI,GAAAA,gBAAA;IAClBE,UAAU,GASJ3B,IAAA,CATN2B,UAAU;IACVC,WAAW,GAQL5B,IAAA,CARN4B,WAAW;IAAAC,cAAA,GAQL7B,IAAA,CAPN8B,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,CAAC,GAAAA,cAAA;IACbE,KAAK,GAMC/B,IAAA,CANN+B,KAAK;IACEC,cAAc,GAKfhC,IAAA,CALNiC,KAAK;IACLC,KAAK,GAIClC,IAAA,CAJNkC,KAAK;IAAAC,YAAA,GAICnC,IAAA,CAHNoC,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IAAAE,WAAA,GAGTrC,IAAA,CAFNsC,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,YAAY,GAAAA,WAAA;IAClBE,IAAA,GAAAC,wBAAA,CACGxC,IAAA,EAAAyC,SAAA;EAGR,IAAMR,KAAK,GAAGpC,gBAAgB,CAACmC,cAAc,CAAC;EAC9C,IAAMU,IAAI,GAAGzD,KAAK,CAAC0D,MAAM,CAAY,IAAI,CAAC;EAE1C1D,KAAK,CAAC2D,mBAAmB,CAAC3C,GAAG,EAAE;IAAA,OAAO;MACpC4C,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAE;QAAA,IAAAC,aAAA;QAAA,QAAAA,aAAA,GAAMJ,IAAI,CAACK,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcD,KAAK,CAAC,CAAC;MAAA;MAClCG,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAE;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GAAMP,IAAI,CAACK,OAAO,cAAAE,cAAA,uBAAZA,cAAA,CAAcD,KAAK,CAAC,CAAC;MAAA;MAClCE,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,IAAoB;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GACnCV,IAAI,CAACK,OAAO,cAAAK,cAAA,uBAAZA,cAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;MAAA;MACpCE,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAE;QAAA,IAAAC,cAAA;QAAA,OAAM,EAAAA,cAAA,GAAAZ,IAAI,CAACK,OAAO,cAAAO,cAAA,uBAAZA,cAAA,CAAcD,SAAS,CAAC,CAAC,KAAI,KAAK;MAAA;MACnDE,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAE;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GAAMd,IAAI,CAACK,OAAO,cAAAS,cAAA,uBAAZA,cAAA,CAAcD,IAAI,CAAC,CAAC;MAAA;MAChCE,YAAY,EAAE,SAAdA,YAAYA,CAAGC,KAAa,EAAEC,GAAW;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GACvClB,IAAI,CAACK,OAAO,cAAAa,cAAA,uBAAZA,cAAA,CAAcH,YAAY,CAACC,KAAK,EAAEC,GAAG,CAAC;MAAA;IAC1C,CAAC;EAAA,CAAC,CAAC;EAEH,IAAME,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,CAAM,EAAK;IAAA,IAAAC,cAAA,EAAAC,kBAAA;IACnC,CAAAD,cAAA,GAAArB,IAAI,CAACK,OAAO,cAAAgB,cAAA,uBAAZA,cAAA,CAAcf,KAAK,CAAC,CAAC;IACrB,CAAAgB,kBAAA,GAAAzB,IAAI,CAAC0B,YAAY,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAAE,IAAA,CAAA3B,IAAI,EAAgB,EAAE,CAAC;IACvBxB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG+C,CAAC,CAAC;EACvB,CAAC;EAED,IAAQK,SAAS,GAAwBlC,KAAK,CAAtCkC,SAAS;IAAEC,IAAI,GAAkBnC,KAAK,CAA3BmC,IAAI;IAAEC,IAAI,GAAYpC,KAAK,CAArBoC,IAAI;IAAEC,KAAA,GAAUrC,KAAK,CAAfqC,KAAA;EAE/B,IAAMC,oBAAoB,GAAGF,IAAI,GAC7BpC,KAAK,CAACuC,MAAM,CAACC,SAAS,IAAAvE,aAAA,GACtB+B,KAAK,CAACuC,MAAM,cAAAtE,aAAA,uBAAZA,aAAA,CAAc0B,WAAW;EAC7B,IAAM8C,SAAS,GAAGL,IAAI,GAAGpC,KAAK,CAACuC,MAAM,CAACG,gBAAgB,GAAG1C,KAAK,CAACuC,MAAM,CAACI,IAAI;EAC1E,IAAMC,YAAY,GAAGT,IAAI,GACrBM,SAAS,GACTnF,KAAK,CAACmF,SAAS,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC/C,IAAM1E,SAAS,GACbD,eAAe,KAAKgE,IAAI,GAAGpC,KAAK,CAACuC,MAAM,CAACG,gBAAgB,GAAGE,YAAY,CAAC;EAC1E,IAAMrE,WAAW,GACfD,iBAAiB,IAAIhB,KAAK,CAACmF,SAAS,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAClE,IAAM5D,qBAAqB,GACzBD,2BAA2B,IAC3B5B,KAAK,CAACmF,SAAS,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAE7C,IAAMC,IAAI,GAAGZ,IAAI,GAAAa,aAAA,CAAAA,aAAA,KAERZ,KAAK,CAACa,SAAS;IAClBC,UAAU,EAAEjG,QAAQ,CAACkG,MAAM,CAAC;MAC1BC,GAAG,EAAE,CAAC;MACNC,OAAO,EAAEjB,KAAK,CAACa,SAAS,CAACC;IAC3B,CAAC;EAAA,KAEHnD,KAAK,CAACqC,KAAK,CAACkB,OAAO;EAEvB,IAAMC,SAAS,GAAGpB,IAAI,IAAI7C,IAAI,KAAK,KAAK;EACxC,IAAMkE,0BAA0B,GAC9BD,SAAS,IACTzE,cAAc,IACd,CAACoB,OAAO,KACP,CAACF,KAAK,IAAIZ,KAAK,KAAKqE,SAAS,CAAC;EAEjC,OACE1G,KAAA,CAAA2G,aAAA,CAAChG,OAAO,EAAAiG,QAAA;IACN9D,KAAK,EAAE,CACL;MAAE+D,YAAY,EAAE3B;IAAU,CAAC,EAC3B,CAACE,IAAI,IAAI0B,MAAM,CAACjE,SAAS,EACzBuC,IAAI,IAAI;MACN2B,eAAe,EAAE/D,KAAK,CAACuC,MAAM,CAAC1C,SAAS,CAACmE,MAAM;MAC9CH,YAAY,EAAE3B,SAAS,IAAIsB,SAAS,GAAG,CAAC,GAAG,CAAC;IAC9C,CAAC,EACDM,MAAM,CAACG,SAAS,EAChBnE,KAAK,CACL;IACFO,MAAM,EAAG,GAAEA,MAAO;EAAY,GACzBL,KAAK,CAACoC,IAAI,IAAI;IAAEvC,SAAA,EAAAA;EAAU,CAAC;IAChCG,KAAK,EAAEA;EAAM,IAEbhD,KAAA,CAAA2G,aAAA,CAAClG,UAAU;IACTyG,iBAAiB,EAAC,QAAQ;IAC1BC,UAAU;IACV5F,WAAW,EAAEA,WAAY;IACzB6F,OAAO,EAAE5F,WAAY;IACrBH,SAAS,EAAEA,SAAU;IACrBF,IAAI,EACFA,IAAI,IACH,UAAAkG,KAAA;MAAA,IAAGC,IAAI,GAASD,KAAA,CAAbC,IAAI;QAAEhH,KAAA,GAAO+G,KAAA,CAAP/G,KAAA;MAAO,OACfN,KAAA,CAAA2G,aAAA,CAACjG,qBAAqB;QACpB6G,IAAI,EAAC,SAAS;QACdjH,KAAK,EAAEA,KAAM;QACbgH,IAAI,EAAEA,IAAK;QACXE,SAAS,EAAEvH,WAAW,CAACwH,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG;MAAM,CAC7D,CAAC;IAAA,CAEL;IACD1E,KAAK,EAAEA,KAAM;IACb2E,kBAAkB,EAAEjG,wBAAyB;IAC7C2B,MAAM,EAAG,GAAEA,MAAO;EAAO,CAC1B,CAAC,EACFrD,KAAA,CAAA2G,aAAA,CAACvG,SAAS,EAAAwG,QAAA;IACR9D,KAAK,EAAE,CACLgE,MAAM,CAACc,KAAK,EAAA3B,aAAA,CAAAA,aAAA;MAEV3F,KAAK,EAAEmF;IAAS,GACbO,IAAI,GACJ9F,QAAQ,CAACkG,MAAM,CAAC;MAAEyB,GAAG,EAAE;QAAEC,OAAO,EAAE;MAAO;IAAE,CAAC,IAEjD1C,IAAI,KAAKoB,SAAS,GAAGM,MAAM,CAACiB,YAAY,GAAGjB,MAAM,CAACkB,aAAa,CAAC,EAChEtF,UAAU,CACV;IACFC,WAAW,EAAEA,WAAW,IAAI,EAAG;IAC/B2C,oBAAoB,EAAEA,oBAAqB;IAC3C2C,cAAc,GAAA/G,cAAA,GAAE8B,KAAK,CAACuC,MAAM,cAAArE,cAAA,uBAAZA,cAAA,CAAcgH,OAAQ;IACtCC,qBAAqB,EAAC,aAAa;IACnCC,aAAa,EAAC,QAAQ;IACtBC,kBAAkB,EAAElD,IAAI,GAAG,MAAM,GAAG,OAAQ;IAC5C+B,iBAAiB,EAAC,QAAQ;IAC1BlG,GAAG,EAAEyC,IAAK;IACVR,KAAK,EAAEA,KAAM;IACbI,MAAM,EAAEA;EAAO,GACXC,IAAI,CACT,CAAC,EACDH,OAAO,GACNnD,KAAA,CAAA2G,aAAA,CAACpG,iBAAiB;IAChB8C,MAAM,EAAC,oBAAoB;IAC3BP,KAAK,EAAEsC,IAAI,GAAG0B,MAAM,CAACwB,QAAQ,GAAGxB,MAAM,CAACyB;EAAO,CAC/C,CAAC,GAMFvI,KAAA,CAAA2G,aAAA,CAACtG,IAAI;IACHmI,aAAa,EAAEvF,KAAK,GAAG,MAAM,GAAG,MAAO;IACvCI,MAAM,EAAG,GAAEA,MAAO,eAAe;IACjCP,KAAK,EAAE,CACLsC,IAAI,IAAI,CAACnC,KAAK,IAAI6D,MAAM,CAAC2B,WAAW,EACpCrD,IAAI,IAAI/C,KAAK,KAAKqE,SAAS,IAAII,MAAM,CAAC4B,iBAAiB;EACvD,GAEF1I,KAAA,CAAA2G,aAAA,CAAClG,UAAU;IACT0G,UAAU;IACVQ,kBAAkB,EAAE9F,uBAAwB;IAC5CR,SAAS,EAAE4B,KAAK,GAAG5B,SAAS,GAAG,wBAAyB;IACxDE,WAAW,EAAEA,WAAY;IACzB6F,OAAO,EAAExC,gBAAiB;IAC1BzD,IAAI,EACFQ,SAAS,IACR,UAAAgH,KAAA;MAAA,IAAGrB,IAAI,GAASqB,KAAA,CAAbrB,IAAI;QAAEhH,KAAA,GAAOqI,KAAA,CAAPrI,KAAA;MAAO,OACfN,KAAA,CAAA2G,aAAA,CAACjG,qBAAqB;QACpB6G,IAAI,EAAEnC,IAAI,GAAG,OAAO,GAAG,sBAAuB;QAC9C9E,KAAK,EAAEA,KAAM;QACbgH,IAAI,EAAEA,IAAK;QACXE,SAAS,EAAEvH,WAAW,CAACwH,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG;MAAM,CAC7D,CAAC;IAAA,CAEL;IACDrE,MAAM,EAAG,GAAEA,MAAO,aAAa;IAC/B6D,iBAAiB,EAAC,QAAQ;IAC1BlE,KAAK,EAAEA;EAAM,CACd,CACG,CACP,EACAyD,0BAA0B,GACzBzG,KAAA,CAAA2G,aAAA,CAAClG,UAAU;IACTyG,iBAAiB,EAAC,QAAQ;IAC1BC,UAAU;IACVC,OAAO,EAAEhF,qBAAsB;IAC/Bf,SAAS,EAAEW,mBAAmB,IAAIgB,KAAK,CAACuC,MAAM,CAACG,gBAAiB;IAChEnE,WAAW,EAAEY,qBAAsB;IACnChB,IAAI,EAAEY,cAAe;IACrB4F,kBAAkB,EAAE1F,gCAAiC;IACrDoB,MAAM,EAAG,GAAEA,MAAO;EAAkB,CACrC,CAAC,GACA,IAAI,EACPmD,SAAS,KACRnE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;IAAE/B,KAAK,EAAEmF,SAAS;IAAE3C,KAAK,EAAEgE,MAAM,CAAC8B,UAAU;IAAEvF,MAAA,EAAAA;EAAO,CAAC,CAAC,GAChE+B,IAAI,IAAI,CAACoB,SAAS,IAAI/D,WAAW,IAChCzC,KAAA,CAAA2G,aAAA,CAACnG,OAAO;IACNqI,IAAI;IACJ/F,KAAK,EAAE,CACLgE,MAAM,CAACgC,OAAO,EACd;MACE/B,eAAe,EAAE/D,KAAK,CAACuC,MAAM,CAACuC;IAChC,CAAC,CACD;IACFzE,MAAM,EAAG,GAAEA,MAAO;EAAU,CAC7B,CAEI,CAAC;AAEd,CACF,CAAC;AAED,IAAMyD,MAAM,GAAG3G,UAAU,CAAC4I,MAAM,CAAC;EAC/B9B,SAAS,EAAE;IACT+B,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EACDrB,KAAK,EAAE;IACLsB,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAErJ,WAAW,CAACwH,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,OAAO,GAAG,MAAM;IAC9D6B,QAAQ,EAAE;EACZ,CAAC;EACDxB,YAAY,EAAE;IACZqB,WAAW,EAAE,CAAC;IACdI,SAAS,EAAE;EACb,CAAC;EACDxB,aAAa,EAAE;IACboB,WAAW,EAAE,CAAC;IACdI,SAAS,EAAE;EACb,CAAC;EACD3G,SAAS,EAAE;IACTA,SAAS,EAAE;EACb,CAAC;EACD0F,MAAM,EAAE;IACNkB,MAAM,EAAE;EACV,CAAC;EACDnB,QAAQ,EAAE;IACRoB,gBAAgB,EAAE;EACpB,CAAC;EACDd,UAAU,EAAE;IACVe,WAAW,EAAE;EACf,CAAC;EACDlB,WAAW,EAAE;IACXmB,QAAQ,EAAE,UAAU;IACpBvH,KAAK,EAAE,CAAC;IACRwH,UAAU,EAAE;EACd,CAAC;EACDnB,iBAAiB,EAAE;IACjBoB,OAAO,EAAE;EACX,CAAC;EACDhB,OAAO,EAAE;IACPc,QAAQ,EAAE,UAAU;IACpBG,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAelJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}