{"ast": null, "code": "'use strict';\n\nimport InteractionManager from \"../../../exports/InteractionManager\";\nimport TouchHistoryMath from \"../TouchHistoryMath\";\nvar currentCentroidXOfTouchesChangedAfter = TouchHistoryMath.currentCentroidXOfTouchesChangedAfter;\nvar currentCentroidYOfTouchesChangedAfter = TouchHistoryMath.currentCentroidYOfTouchesChangedAfter;\nvar previousCentroidXOfTouchesChangedAfter = TouchHistoryMath.previousCentroidXOfTouchesChangedAfter;\nvar previousCentroidYOfTouchesChangedAfter = TouchHistoryMath.previousCentroidYOfTouchesChangedAfter;\nvar currentCentroidX = TouchHistoryMath.currentCentroidX;\nvar currentCentroidY = TouchHistoryMath.currentCentroidY;\nvar PanResponder = {\n  _initializeGestureState: function _initializeGestureState(gestureState) {\n    gestureState.moveX = 0;\n    gestureState.moveY = 0;\n    gestureState.x0 = 0;\n    gestureState.y0 = 0;\n    gestureState.dx = 0;\n    gestureState.dy = 0;\n    gestureState.vx = 0;\n    gestureState.vy = 0;\n    gestureState.numberActiveTouches = 0;\n    gestureState._accountsForMovesUpTo = 0;\n  },\n  _updateGestureStateOnMove: function _updateGestureStateOnMove(gestureState, touchHistory) {\n    gestureState.numberActiveTouches = touchHistory.numberActiveTouches;\n    gestureState.moveX = currentCentroidXOfTouchesChangedAfter(touchHistory, gestureState._accountsForMovesUpTo);\n    gestureState.moveY = currentCentroidYOfTouchesChangedAfter(touchHistory, gestureState._accountsForMovesUpTo);\n    var movedAfter = gestureState._accountsForMovesUpTo;\n    var prevX = previousCentroidXOfTouchesChangedAfter(touchHistory, movedAfter);\n    var x = currentCentroidXOfTouchesChangedAfter(touchHistory, movedAfter);\n    var prevY = previousCentroidYOfTouchesChangedAfter(touchHistory, movedAfter);\n    var y = currentCentroidYOfTouchesChangedAfter(touchHistory, movedAfter);\n    var nextDX = gestureState.dx + (x - prevX);\n    var nextDY = gestureState.dy + (y - prevY);\n    var dt = touchHistory.mostRecentTimeStamp - gestureState._accountsForMovesUpTo;\n    gestureState.vx = (nextDX - gestureState.dx) / dt;\n    gestureState.vy = (nextDY - gestureState.dy) / dt;\n    gestureState.dx = nextDX;\n    gestureState.dy = nextDY;\n    gestureState._accountsForMovesUpTo = touchHistory.mostRecentTimeStamp;\n  },\n  create: function create(config) {\n    var interactionState = {\n      handle: null,\n      shouldCancelClick: false,\n      timeout: null\n    };\n    var gestureState = {\n      stateID: Math.random(),\n      moveX: 0,\n      moveY: 0,\n      x0: 0,\n      y0: 0,\n      dx: 0,\n      dy: 0,\n      vx: 0,\n      vy: 0,\n      numberActiveTouches: 0,\n      _accountsForMovesUpTo: 0\n    };\n    var panHandlers = {\n      onStartShouldSetResponder: function onStartShouldSetResponder(event) {\n        return config.onStartShouldSetPanResponder == null ? false : config.onStartShouldSetPanResponder(event, gestureState);\n      },\n      onMoveShouldSetResponder: function onMoveShouldSetResponder(event) {\n        return config.onMoveShouldSetPanResponder == null ? false : config.onMoveShouldSetPanResponder(event, gestureState);\n      },\n      onStartShouldSetResponderCapture: function onStartShouldSetResponderCapture(event) {\n        if (event.nativeEvent.touches.length === 1) {\n          PanResponder._initializeGestureState(gestureState);\n        }\n        gestureState.numberActiveTouches = event.touchHistory.numberActiveTouches;\n        return config.onStartShouldSetPanResponderCapture != null ? config.onStartShouldSetPanResponderCapture(event, gestureState) : false;\n      },\n      onMoveShouldSetResponderCapture: function onMoveShouldSetResponderCapture(event) {\n        var touchHistory = event.touchHistory;\n        if (gestureState._accountsForMovesUpTo === touchHistory.mostRecentTimeStamp) {\n          return false;\n        }\n        PanResponder._updateGestureStateOnMove(gestureState, touchHistory);\n        return config.onMoveShouldSetPanResponderCapture ? config.onMoveShouldSetPanResponderCapture(event, gestureState) : false;\n      },\n      onResponderGrant: function onResponderGrant(event) {\n        if (!interactionState.handle) {\n          interactionState.handle = InteractionManager.createInteractionHandle();\n        }\n        if (interactionState.timeout) {\n          clearInteractionTimeout(interactionState);\n        }\n        interactionState.shouldCancelClick = true;\n        gestureState.x0 = currentCentroidX(event.touchHistory);\n        gestureState.y0 = currentCentroidY(event.touchHistory);\n        gestureState.dx = 0;\n        gestureState.dy = 0;\n        if (config.onPanResponderGrant) {\n          config.onPanResponderGrant(event, gestureState);\n        }\n        return config.onShouldBlockNativeResponder == null ? true : config.onShouldBlockNativeResponder(event, gestureState);\n      },\n      onResponderReject: function onResponderReject(event) {\n        clearInteractionHandle(interactionState, config.onPanResponderReject, event, gestureState);\n      },\n      onResponderRelease: function onResponderRelease(event) {\n        clearInteractionHandle(interactionState, config.onPanResponderRelease, event, gestureState);\n        setInteractionTimeout(interactionState);\n        PanResponder._initializeGestureState(gestureState);\n      },\n      onResponderStart: function onResponderStart(event) {\n        var touchHistory = event.touchHistory;\n        gestureState.numberActiveTouches = touchHistory.numberActiveTouches;\n        if (config.onPanResponderStart) {\n          config.onPanResponderStart(event, gestureState);\n        }\n      },\n      onResponderMove: function onResponderMove(event) {\n        var touchHistory = event.touchHistory;\n        if (gestureState._accountsForMovesUpTo === touchHistory.mostRecentTimeStamp) {\n          return;\n        }\n        PanResponder._updateGestureStateOnMove(gestureState, touchHistory);\n        if (config.onPanResponderMove) {\n          config.onPanResponderMove(event, gestureState);\n        }\n      },\n      onResponderEnd: function onResponderEnd(event) {\n        var touchHistory = event.touchHistory;\n        gestureState.numberActiveTouches = touchHistory.numberActiveTouches;\n        clearInteractionHandle(interactionState, config.onPanResponderEnd, event, gestureState);\n      },\n      onResponderTerminate: function onResponderTerminate(event) {\n        clearInteractionHandle(interactionState, config.onPanResponderTerminate, event, gestureState);\n        setInteractionTimeout(interactionState);\n        PanResponder._initializeGestureState(gestureState);\n      },\n      onResponderTerminationRequest: function onResponderTerminationRequest(event) {\n        return config.onPanResponderTerminationRequest == null ? true : config.onPanResponderTerminationRequest(event, gestureState);\n      },\n      onClickCapture: function onClickCapture(event) {\n        if (interactionState.shouldCancelClick === true) {\n          event.stopPropagation();\n          event.preventDefault();\n        }\n      }\n    };\n    return {\n      panHandlers: panHandlers,\n      getInteractionHandle: function getInteractionHandle() {\n        return interactionState.handle;\n      }\n    };\n  }\n};\nfunction clearInteractionHandle(interactionState, callback, event, gestureState) {\n  if (interactionState.handle) {\n    InteractionManager.clearInteractionHandle(interactionState.handle);\n    interactionState.handle = null;\n  }\n  if (callback) {\n    callback(event, gestureState);\n  }\n}\nfunction clearInteractionTimeout(interactionState) {\n  clearTimeout(interactionState.timeout);\n}\nfunction setInteractionTimeout(interactionState) {\n  interactionState.timeout = setTimeout(function () {\n    interactionState.shouldCancelClick = false;\n  }, 250);\n}\nexport default PanResponder;", "map": {"version": 3, "names": ["InteractionManager", "TouchHistoryMath", "currentCentroidXOfTouchesChangedAfter", "currentCentroidYOfTouchesChangedAfter", "previousCentroidXOfTouchesChangedAfter", "previousCentroidYOfTouchesChangedAfter", "currentCentroidX", "currentCentroidY", "PanResponder", "_initializeGestureState", "gestureState", "moveX", "moveY", "x0", "y0", "dx", "dy", "vx", "vy", "numberActiveTouches", "_accountsForMovesUpTo", "_updateGestureStateOnMove", "touchHistory", "movedAfter", "prevX", "x", "prevY", "y", "nextDX", "nextDY", "dt", "mostRecentTimeStamp", "create", "config", "interactionState", "handle", "shouldCancelClick", "timeout", "stateID", "Math", "random", "panHandlers", "onStartShouldSetResponder", "event", "onStartShouldSetPanResponder", "onMoveShouldSetResponder", "onMoveShouldSetPanResponder", "onStartShouldSetResponderCapture", "nativeEvent", "touches", "length", "onStartShouldSetPanResponderCapture", "onMoveShouldSetResponderCapture", "onMoveShouldSetPanResponderCapture", "onResponderGrant", "createInteractionHandle", "clearInteractionTimeout", "onPanResponderGrant", "onShouldBlockNativeResponder", "onResponderReject", "clearInteractionHandle", "onPanResponderReject", "onResponderRelease", "onPanResponderRelease", "setInteractionTimeout", "onResponderStart", "onPanResponderStart", "onResponderMove", "onPanResponderMove", "onResponderEnd", "onPanResponderEnd", "onResponderTerminate", "onPanResponderTerminate", "onResponderTerminationRequest", "onPanResponderTerminationRequest", "onClickCapture", "stopPropagation", "preventDefault", "getInteractionHandle", "callback", "clearTimeout", "setTimeout"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/PanResponder/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport InteractionManager from '../../../exports/InteractionManager';\nimport TouchHistoryMath from '../TouchHistoryMath';\nvar currentCentroidXOfTouchesChangedAfter = TouchHistoryMath.currentCentroidXOfTouchesChangedAfter;\nvar currentCentroidYOfTouchesChangedAfter = TouchHistoryMath.currentCentroidYOfTouchesChangedAfter;\nvar previousCentroidXOfTouchesChangedAfter = TouchHistoryMath.previousCentroidXOfTouchesChangedAfter;\nvar previousCentroidYOfTouchesChangedAfter = TouchHistoryMath.previousCentroidYOfTouchesChangedAfter;\nvar currentCentroidX = TouchHistoryMath.currentCentroidX;\nvar currentCentroidY = TouchHistoryMath.currentCentroidY;\n\n/**\n * `PanResponder` reconciles several touches into a single gesture. It makes\n * single-touch gestures resilient to extra touches, and can be used to\n * recognize simple multi-touch gestures.\n *\n * By default, `PanResponder` holds an `InteractionManager` handle to block\n * long-running JS events from interrupting active gestures.\n *\n * It provides a predictable wrapper of the responder handlers provided by the\n * [gesture responder system](docs/gesture-responder-system.html).\n * For each handler, it provides a new `gestureState` object alongside the\n * native event object:\n *\n * ```\n * onPanResponderMove: (event, gestureState) => {}\n * ```\n *\n * A native event is a synthetic touch event with the following form:\n *\n *  - `nativeEvent`\n *      + `changedTouches` - Array of all touch events that have changed since the last event\n *      + `identifier` - The ID of the touch\n *      + `locationX` - The X position of the touch, relative to the element\n *      + `locationY` - The Y position of the touch, relative to the element\n *      + `pageX` - The X position of the touch, relative to the root element\n *      + `pageY` - The Y position of the touch, relative to the root element\n *      + `target` - The node id of the element receiving the touch event\n *      + `timestamp` - A time identifier for the touch, useful for velocity calculation\n *      + `touches` - Array of all current touches on the screen\n *\n * A `gestureState` object has the following:\n *\n *  - `stateID` - ID of the gestureState- persisted as long as there at least\n *     one touch on screen\n *  - `moveX` - the latest screen coordinates of the recently-moved touch\n *  - `moveY` - the latest screen coordinates of the recently-moved touch\n *  - `x0` - the screen coordinates of the responder grant\n *  - `y0` - the screen coordinates of the responder grant\n *  - `dx` - accumulated distance of the gesture since the touch started\n *  - `dy` - accumulated distance of the gesture since the touch started\n *  - `vx` - current velocity of the gesture\n *  - `vy` - current velocity of the gesture\n *  - `numberActiveTouches` - Number of touches currently on screen\n *\n * ### Basic Usage\n *\n * ```\n *   componentWillMount: function() {\n *     this._panResponder = PanResponder.create({\n *       // Ask to be the responder:\n *       onStartShouldSetPanResponder: (evt, gestureState) => true,\n *       onStartShouldSetPanResponderCapture: (evt, gestureState) => true,\n *       onMoveShouldSetPanResponder: (evt, gestureState) => true,\n *       onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,\n *\n *       onPanResponderGrant: (evt, gestureState) => {\n *         // The gesture has started. Show visual feedback so the user knows\n *         // what is happening!\n *\n *         // gestureState.d{x,y} will be set to zero now\n *       },\n *       onPanResponderMove: (evt, gestureState) => {\n *         // The most recent move distance is gestureState.move{X,Y}\n *\n *         // The accumulated gesture distance since becoming responder is\n *         // gestureState.d{x,y}\n *       },\n *       onPanResponderTerminationRequest: (evt, gestureState) => true,\n *       onPanResponderRelease: (evt, gestureState) => {\n *         // The user has released all touches while this view is the\n *         // responder. This typically means a gesture has succeeded\n *       },\n *       onPanResponderTerminate: (evt, gestureState) => {\n *         // Another component has become the responder, so this gesture\n *         // should be cancelled\n *       },\n *       onShouldBlockNativeResponder: (evt, gestureState) => {\n *         // Returns whether this component should block native components from becoming the JS\n *         // responder. Returns true by default. Is currently only supported on android.\n *         return true;\n *       },\n *     });\n *   },\n *\n *   render: function() {\n *     return (\n *       <View {...this._panResponder.panHandlers} />\n *     );\n *   },\n *\n * ```\n *\n * ### Working Example\n *\n * To see it in action, try the\n * [PanResponder example in RNTester](https://github.com/facebook/react-native/blob/master/RNTester/js/PanResponderExample.js)\n */\n\nvar PanResponder = {\n  /**\n   *\n   * A graphical explanation of the touch data flow:\n   *\n   * +----------------------------+             +--------------------------------+\n   * | ResponderTouchHistoryStore |             |TouchHistoryMath                |\n   * +----------------------------+             +----------+---------------------+\n   * |Global store of touchHistory|             |Allocation-less math util       |\n   * |including activeness, start |             |on touch history (centroids     |\n   * |position, prev/cur position.|             |and multitouch movement etc)    |\n   * |                            |             |                                |\n   * +----^-----------------------+             +----^---------------------------+\n   *      |                                          |\n   *      | (records relevant history                |\n   *      |  of touches relevant for                 |\n   *      |  implementing higher level               |\n   *      |  gestures)                               |\n   *      |                                          |\n   * +----+-----------------------+             +----|---------------------------+\n   * | ResponderEventPlugin       |             |    |   Your App/Component      |\n   * +----------------------------+             +----|---------------------------+\n   * |Negotiates which view gets  | Low level   |    |             High level    |\n   * |onResponderMove events.     | events w/   |  +-+-------+     events w/     |\n   * |Also records history into   | touchHistory|  |   Pan   |     multitouch +  |\n   * |ResponderTouchHistoryStore. +---------------->Responder+-----> accumulative|\n   * +----------------------------+ attached to |  |         |     distance and  |\n   *                                 each event |  +---------+     velocity.     |\n   *                                            |                                |\n   *                                            |                                |\n   *                                            +--------------------------------+\n   *\n   *\n   *\n   * Gesture that calculates cumulative movement over time in a way that just\n   * \"does the right thing\" for multiple touches. The \"right thing\" is very\n   * nuanced. When moving two touches in opposite directions, the cumulative\n   * distance is zero in each dimension. When two touches move in parallel five\n   * pixels in the same direction, the cumulative distance is five, not ten. If\n   * two touches start, one moves five in a direction, then stops and the other\n   * touch moves fives in the same direction, the cumulative distance is ten.\n   *\n   * This logic requires a kind of processing of time \"clusters\" of touch events\n   * so that two touch moves that essentially occur in parallel but move every\n   * other frame respectively, are considered part of the same movement.\n   *\n   * Explanation of some of the non-obvious fields:\n   *\n   * - moveX/moveY: If no move event has been observed, then `(moveX, moveY)` is\n   *   invalid. If a move event has been observed, `(moveX, moveY)` is the\n   *   centroid of the most recently moved \"cluster\" of active touches.\n   *   (Currently all move have the same timeStamp, but later we should add some\n   *   threshold for what is considered to be \"moving\"). If a palm is\n   *   accidentally counted as a touch, but a finger is moving greatly, the palm\n   *   will move slightly, but we only want to count the single moving touch.\n   * - x0/y0: Centroid location (non-cumulative) at the time of becoming\n   *   responder.\n   * - dx/dy: Cumulative touch distance - not the same thing as sum of each touch\n   *   distance. Accounts for touch moves that are clustered together in time,\n   *   moving the same direction. Only valid when currently responder (otherwise,\n   *   it only represents the drag distance below the threshold).\n   * - vx/vy: Velocity.\n   */\n\n  _initializeGestureState(gestureState) {\n    gestureState.moveX = 0;\n    gestureState.moveY = 0;\n    gestureState.x0 = 0;\n    gestureState.y0 = 0;\n    gestureState.dx = 0;\n    gestureState.dy = 0;\n    gestureState.vx = 0;\n    gestureState.vy = 0;\n    gestureState.numberActiveTouches = 0;\n    // All `gestureState` accounts for timeStamps up until:\n    gestureState._accountsForMovesUpTo = 0;\n  },\n  /**\n   * This is nuanced and is necessary. It is incorrect to continuously take all\n   * active *and* recently moved touches, find the centroid, and track how that\n   * result changes over time. Instead, we must take all recently moved\n   * touches, and calculate how the centroid has changed just for those\n   * recently moved touches, and append that change to an accumulator. This is\n   * to (at least) handle the case where the user is moving three fingers, and\n   * then one of the fingers stops but the other two continue.\n   *\n   * This is very different than taking all of the recently moved touches and\n   * storing their centroid as `dx/dy`. For correctness, we must *accumulate\n   * changes* in the centroid of recently moved touches.\n   *\n   * There is also some nuance with how we handle multiple moved touches in a\n   * single event. With the way `ReactNativeEventEmitter` dispatches touches as\n   * individual events, multiple touches generate two 'move' events, each of\n   * them triggering `onResponderMove`. But with the way `PanResponder` works,\n   * all of the gesture inference is performed on the first dispatch, since it\n   * looks at all of the touches (even the ones for which there hasn't been a\n   * native dispatch yet). Therefore, `PanResponder` does not call\n   * `onResponderMove` passed the first dispatch. This diverges from the\n   * typical responder callback pattern (without using `PanResponder`), but\n   * avoids more dispatches than necessary.\n   */\n  _updateGestureStateOnMove(gestureState, touchHistory) {\n    gestureState.numberActiveTouches = touchHistory.numberActiveTouches;\n    gestureState.moveX = currentCentroidXOfTouchesChangedAfter(touchHistory, gestureState._accountsForMovesUpTo);\n    gestureState.moveY = currentCentroidYOfTouchesChangedAfter(touchHistory, gestureState._accountsForMovesUpTo);\n    var movedAfter = gestureState._accountsForMovesUpTo;\n    var prevX = previousCentroidXOfTouchesChangedAfter(touchHistory, movedAfter);\n    var x = currentCentroidXOfTouchesChangedAfter(touchHistory, movedAfter);\n    var prevY = previousCentroidYOfTouchesChangedAfter(touchHistory, movedAfter);\n    var y = currentCentroidYOfTouchesChangedAfter(touchHistory, movedAfter);\n    var nextDX = gestureState.dx + (x - prevX);\n    var nextDY = gestureState.dy + (y - prevY);\n\n    // TODO: This must be filtered intelligently.\n    var dt = touchHistory.mostRecentTimeStamp - gestureState._accountsForMovesUpTo;\n    gestureState.vx = (nextDX - gestureState.dx) / dt;\n    gestureState.vy = (nextDY - gestureState.dy) / dt;\n    gestureState.dx = nextDX;\n    gestureState.dy = nextDY;\n    gestureState._accountsForMovesUpTo = touchHistory.mostRecentTimeStamp;\n  },\n  /**\n   * @param {object} config Enhanced versions of all of the responder callbacks\n   * that provide not only the typical `ResponderSyntheticEvent`, but also the\n   * `PanResponder` gesture state.  Simply replace the word `Responder` with\n   * `PanResponder` in each of the typical `onResponder*` callbacks. For\n   * example, the `config` object would look like:\n   *\n   *  - `onMoveShouldSetPanResponder: (e, gestureState) => {...}`\n   *  - `onMoveShouldSetPanResponderCapture: (e, gestureState) => {...}`\n   *  - `onStartShouldSetPanResponder: (e, gestureState) => {...}`\n   *  - `onStartShouldSetPanResponderCapture: (e, gestureState) => {...}`\n   *  - `onPanResponderReject: (e, gestureState) => {...}`\n   *  - `onPanResponderGrant: (e, gestureState) => {...}`\n   *  - `onPanResponderStart: (e, gestureState) => {...}`\n   *  - `onPanResponderEnd: (e, gestureState) => {...}`\n   *  - `onPanResponderRelease: (e, gestureState) => {...}`\n   *  - `onPanResponderMove: (e, gestureState) => {...}`\n   *  - `onPanResponderTerminate: (e, gestureState) => {...}`\n   *  - `onPanResponderTerminationRequest: (e, gestureState) => {...}`\n   *  - `onShouldBlockNativeResponder: (e, gestureState) => {...}`\n   *\n   *  In general, for events that have capture equivalents, we update the\n   *  gestureState once in the capture phase and can use it in the bubble phase\n   *  as well.\n   *\n   *  Be careful with onStartShould* callbacks. They only reflect updated\n   *  `gestureState` for start/end events that bubble/capture to the Node.\n   *  Once the node is the responder, you can rely on every start/end event\n   *  being processed by the gesture and `gestureState` being updated\n   *  accordingly. (numberActiveTouches) may not be totally accurate unless you\n   *  are the responder.\n   */\n  create(config) {\n    var interactionState = {\n      handle: null,\n      shouldCancelClick: false,\n      timeout: null\n    };\n    var gestureState = {\n      // Useful for debugging\n      stateID: Math.random(),\n      moveX: 0,\n      moveY: 0,\n      x0: 0,\n      y0: 0,\n      dx: 0,\n      dy: 0,\n      vx: 0,\n      vy: 0,\n      numberActiveTouches: 0,\n      _accountsForMovesUpTo: 0\n    };\n    var panHandlers = {\n      onStartShouldSetResponder(event) {\n        return config.onStartShouldSetPanResponder == null ? false : config.onStartShouldSetPanResponder(event, gestureState);\n      },\n      onMoveShouldSetResponder(event) {\n        return config.onMoveShouldSetPanResponder == null ? false : config.onMoveShouldSetPanResponder(event, gestureState);\n      },\n      onStartShouldSetResponderCapture(event) {\n        // TODO: Actually, we should reinitialize the state any time\n        // touches.length increases from 0 active to > 0 active.\n        if (event.nativeEvent.touches.length === 1) {\n          PanResponder._initializeGestureState(gestureState);\n        }\n        gestureState.numberActiveTouches = event.touchHistory.numberActiveTouches;\n        return config.onStartShouldSetPanResponderCapture != null ? config.onStartShouldSetPanResponderCapture(event, gestureState) : false;\n      },\n      onMoveShouldSetResponderCapture(event) {\n        var touchHistory = event.touchHistory;\n        // Responder system incorrectly dispatches should* to current responder\n        // Filter out any touch moves past the first one - we would have\n        // already processed multi-touch geometry during the first event.\n        if (gestureState._accountsForMovesUpTo === touchHistory.mostRecentTimeStamp) {\n          return false;\n        }\n        PanResponder._updateGestureStateOnMove(gestureState, touchHistory);\n        return config.onMoveShouldSetPanResponderCapture ? config.onMoveShouldSetPanResponderCapture(event, gestureState) : false;\n      },\n      onResponderGrant(event) {\n        if (!interactionState.handle) {\n          interactionState.handle = InteractionManager.createInteractionHandle();\n        }\n        if (interactionState.timeout) {\n          clearInteractionTimeout(interactionState);\n        }\n        interactionState.shouldCancelClick = true;\n        gestureState.x0 = currentCentroidX(event.touchHistory);\n        gestureState.y0 = currentCentroidY(event.touchHistory);\n        gestureState.dx = 0;\n        gestureState.dy = 0;\n        if (config.onPanResponderGrant) {\n          config.onPanResponderGrant(event, gestureState);\n        }\n        // TODO: t7467124 investigate if this can be removed\n        return config.onShouldBlockNativeResponder == null ? true : config.onShouldBlockNativeResponder(event, gestureState);\n      },\n      onResponderReject(event) {\n        clearInteractionHandle(interactionState, config.onPanResponderReject, event, gestureState);\n      },\n      onResponderRelease(event) {\n        clearInteractionHandle(interactionState, config.onPanResponderRelease, event, gestureState);\n        setInteractionTimeout(interactionState);\n        PanResponder._initializeGestureState(gestureState);\n      },\n      onResponderStart(event) {\n        var touchHistory = event.touchHistory;\n        gestureState.numberActiveTouches = touchHistory.numberActiveTouches;\n        if (config.onPanResponderStart) {\n          config.onPanResponderStart(event, gestureState);\n        }\n      },\n      onResponderMove(event) {\n        var touchHistory = event.touchHistory;\n        // Guard against the dispatch of two touch moves when there are two\n        // simultaneously changed touches.\n        if (gestureState._accountsForMovesUpTo === touchHistory.mostRecentTimeStamp) {\n          return;\n        }\n        // Filter out any touch moves past the first one - we would have\n        // already processed multi-touch geometry during the first event.\n        PanResponder._updateGestureStateOnMove(gestureState, touchHistory);\n        if (config.onPanResponderMove) {\n          config.onPanResponderMove(event, gestureState);\n        }\n      },\n      onResponderEnd(event) {\n        var touchHistory = event.touchHistory;\n        gestureState.numberActiveTouches = touchHistory.numberActiveTouches;\n        clearInteractionHandle(interactionState, config.onPanResponderEnd, event, gestureState);\n      },\n      onResponderTerminate(event) {\n        clearInteractionHandle(interactionState, config.onPanResponderTerminate, event, gestureState);\n        setInteractionTimeout(interactionState);\n        PanResponder._initializeGestureState(gestureState);\n      },\n      onResponderTerminationRequest(event) {\n        return config.onPanResponderTerminationRequest == null ? true : config.onPanResponderTerminationRequest(event, gestureState);\n      },\n      // We do not want to trigger 'click' activated gestures or native behaviors\n      // on any pan target that is under a mouse cursor when it is released.\n      // Browsers will natively cancel 'click' events on a target if a non-mouse\n      // active pointer moves.\n      onClickCapture: event => {\n        if (interactionState.shouldCancelClick === true) {\n          event.stopPropagation();\n          event.preventDefault();\n        }\n      }\n    };\n    return {\n      panHandlers,\n      getInteractionHandle() {\n        return interactionState.handle;\n      }\n    };\n  }\n};\nfunction clearInteractionHandle(interactionState, callback, event, gestureState) {\n  if (interactionState.handle) {\n    InteractionManager.clearInteractionHandle(interactionState.handle);\n    interactionState.handle = null;\n  }\n  if (callback) {\n    callback(event, gestureState);\n  }\n}\nfunction clearInteractionTimeout(interactionState) {\n  clearTimeout(interactionState.timeout);\n}\nfunction setInteractionTimeout(interactionState) {\n  interactionState.timeout = setTimeout(() => {\n    interactionState.shouldCancelClick = false;\n  }, 250);\n}\nexport default PanResponder;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,kBAAkB;AACzB,OAAOC,gBAAgB;AACvB,IAAIC,qCAAqC,GAAGD,gBAAgB,CAACC,qCAAqC;AAClG,IAAIC,qCAAqC,GAAGF,gBAAgB,CAACE,qCAAqC;AAClG,IAAIC,sCAAsC,GAAGH,gBAAgB,CAACG,sCAAsC;AACpG,IAAIC,sCAAsC,GAAGJ,gBAAgB,CAACI,sCAAsC;AACpG,IAAIC,gBAAgB,GAAGL,gBAAgB,CAACK,gBAAgB;AACxD,IAAIC,gBAAgB,GAAGN,gBAAgB,CAACM,gBAAgB;AAoGxD,IAAIC,YAAY,GAAG;EAgEjBC,uBAAuB,WAAvBA,uBAAuBA,CAACC,YAAY,EAAE;IACpCA,YAAY,CAACC,KAAK,GAAG,CAAC;IACtBD,YAAY,CAACE,KAAK,GAAG,CAAC;IACtBF,YAAY,CAACG,EAAE,GAAG,CAAC;IACnBH,YAAY,CAACI,EAAE,GAAG,CAAC;IACnBJ,YAAY,CAACK,EAAE,GAAG,CAAC;IACnBL,YAAY,CAACM,EAAE,GAAG,CAAC;IACnBN,YAAY,CAACO,EAAE,GAAG,CAAC;IACnBP,YAAY,CAACQ,EAAE,GAAG,CAAC;IACnBR,YAAY,CAACS,mBAAmB,GAAG,CAAC;IAEpCT,YAAY,CAACU,qBAAqB,GAAG,CAAC;EACxC,CAAC;EAyBDC,yBAAyB,WAAzBA,yBAAyBA,CAACX,YAAY,EAAEY,YAAY,EAAE;IACpDZ,YAAY,CAACS,mBAAmB,GAAGG,YAAY,CAACH,mBAAmB;IACnET,YAAY,CAACC,KAAK,GAAGT,qCAAqC,CAACoB,YAAY,EAAEZ,YAAY,CAACU,qBAAqB,CAAC;IAC5GV,YAAY,CAACE,KAAK,GAAGT,qCAAqC,CAACmB,YAAY,EAAEZ,YAAY,CAACU,qBAAqB,CAAC;IAC5G,IAAIG,UAAU,GAAGb,YAAY,CAACU,qBAAqB;IACnD,IAAII,KAAK,GAAGpB,sCAAsC,CAACkB,YAAY,EAAEC,UAAU,CAAC;IAC5E,IAAIE,CAAC,GAAGvB,qCAAqC,CAACoB,YAAY,EAAEC,UAAU,CAAC;IACvE,IAAIG,KAAK,GAAGrB,sCAAsC,CAACiB,YAAY,EAAEC,UAAU,CAAC;IAC5E,IAAII,CAAC,GAAGxB,qCAAqC,CAACmB,YAAY,EAAEC,UAAU,CAAC;IACvE,IAAIK,MAAM,GAAGlB,YAAY,CAACK,EAAE,IAAIU,CAAC,GAAGD,KAAK,CAAC;IAC1C,IAAIK,MAAM,GAAGnB,YAAY,CAACM,EAAE,IAAIW,CAAC,GAAGD,KAAK,CAAC;IAG1C,IAAII,EAAE,GAAGR,YAAY,CAACS,mBAAmB,GAAGrB,YAAY,CAACU,qBAAqB;IAC9EV,YAAY,CAACO,EAAE,GAAG,CAACW,MAAM,GAAGlB,YAAY,CAACK,EAAE,IAAIe,EAAE;IACjDpB,YAAY,CAACQ,EAAE,GAAG,CAACW,MAAM,GAAGnB,YAAY,CAACM,EAAE,IAAIc,EAAE;IACjDpB,YAAY,CAACK,EAAE,GAAGa,MAAM;IACxBlB,YAAY,CAACM,EAAE,GAAGa,MAAM;IACxBnB,YAAY,CAACU,qBAAqB,GAAGE,YAAY,CAACS,mBAAmB;EACvE,CAAC;EAiCDC,MAAM,WAANA,MAAMA,CAACC,MAAM,EAAE;IACb,IAAIC,gBAAgB,GAAG;MACrBC,MAAM,EAAE,IAAI;MACZC,iBAAiB,EAAE,KAAK;MACxBC,OAAO,EAAE;IACX,CAAC;IACD,IAAI3B,YAAY,GAAG;MAEjB4B,OAAO,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC;MACtB7B,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,CAAC;MACRC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,mBAAmB,EAAE,CAAC;MACtBC,qBAAqB,EAAE;IACzB,CAAC;IACD,IAAIqB,WAAW,GAAG;MAChBC,yBAAyB,WAAzBA,yBAAyBA,CAACC,KAAK,EAAE;QAC/B,OAAOV,MAAM,CAACW,4BAA4B,IAAI,IAAI,GAAG,KAAK,GAAGX,MAAM,CAACW,4BAA4B,CAACD,KAAK,EAAEjC,YAAY,CAAC;MACvH,CAAC;MACDmC,wBAAwB,WAAxBA,wBAAwBA,CAACF,KAAK,EAAE;QAC9B,OAAOV,MAAM,CAACa,2BAA2B,IAAI,IAAI,GAAG,KAAK,GAAGb,MAAM,CAACa,2BAA2B,CAACH,KAAK,EAAEjC,YAAY,CAAC;MACrH,CAAC;MACDqC,gCAAgC,WAAhCA,gCAAgCA,CAACJ,KAAK,EAAE;QAGtC,IAAIA,KAAK,CAACK,WAAW,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;UAC1C1C,YAAY,CAACC,uBAAuB,CAACC,YAAY,CAAC;QACpD;QACAA,YAAY,CAACS,mBAAmB,GAAGwB,KAAK,CAACrB,YAAY,CAACH,mBAAmB;QACzE,OAAOc,MAAM,CAACkB,mCAAmC,IAAI,IAAI,GAAGlB,MAAM,CAACkB,mCAAmC,CAACR,KAAK,EAAEjC,YAAY,CAAC,GAAG,KAAK;MACrI,CAAC;MACD0C,+BAA+B,WAA/BA,+BAA+BA,CAACT,KAAK,EAAE;QACrC,IAAIrB,YAAY,GAAGqB,KAAK,CAACrB,YAAY;QAIrC,IAAIZ,YAAY,CAACU,qBAAqB,KAAKE,YAAY,CAACS,mBAAmB,EAAE;UAC3E,OAAO,KAAK;QACd;QACAvB,YAAY,CAACa,yBAAyB,CAACX,YAAY,EAAEY,YAAY,CAAC;QAClE,OAAOW,MAAM,CAACoB,kCAAkC,GAAGpB,MAAM,CAACoB,kCAAkC,CAACV,KAAK,EAAEjC,YAAY,CAAC,GAAG,KAAK;MAC3H,CAAC;MACD4C,gBAAgB,WAAhBA,gBAAgBA,CAACX,KAAK,EAAE;QACtB,IAAI,CAACT,gBAAgB,CAACC,MAAM,EAAE;UAC5BD,gBAAgB,CAACC,MAAM,GAAGnC,kBAAkB,CAACuD,uBAAuB,CAAC,CAAC;QACxE;QACA,IAAIrB,gBAAgB,CAACG,OAAO,EAAE;UAC5BmB,uBAAuB,CAACtB,gBAAgB,CAAC;QAC3C;QACAA,gBAAgB,CAACE,iBAAiB,GAAG,IAAI;QACzC1B,YAAY,CAACG,EAAE,GAAGP,gBAAgB,CAACqC,KAAK,CAACrB,YAAY,CAAC;QACtDZ,YAAY,CAACI,EAAE,GAAGP,gBAAgB,CAACoC,KAAK,CAACrB,YAAY,CAAC;QACtDZ,YAAY,CAACK,EAAE,GAAG,CAAC;QACnBL,YAAY,CAACM,EAAE,GAAG,CAAC;QACnB,IAAIiB,MAAM,CAACwB,mBAAmB,EAAE;UAC9BxB,MAAM,CAACwB,mBAAmB,CAACd,KAAK,EAAEjC,YAAY,CAAC;QACjD;QAEA,OAAOuB,MAAM,CAACyB,4BAA4B,IAAI,IAAI,GAAG,IAAI,GAAGzB,MAAM,CAACyB,4BAA4B,CAACf,KAAK,EAAEjC,YAAY,CAAC;MACtH,CAAC;MACDiD,iBAAiB,WAAjBA,iBAAiBA,CAAChB,KAAK,EAAE;QACvBiB,sBAAsB,CAAC1B,gBAAgB,EAAED,MAAM,CAAC4B,oBAAoB,EAAElB,KAAK,EAAEjC,YAAY,CAAC;MAC5F,CAAC;MACDoD,kBAAkB,WAAlBA,kBAAkBA,CAACnB,KAAK,EAAE;QACxBiB,sBAAsB,CAAC1B,gBAAgB,EAAED,MAAM,CAAC8B,qBAAqB,EAAEpB,KAAK,EAAEjC,YAAY,CAAC;QAC3FsD,qBAAqB,CAAC9B,gBAAgB,CAAC;QACvC1B,YAAY,CAACC,uBAAuB,CAACC,YAAY,CAAC;MACpD,CAAC;MACDuD,gBAAgB,WAAhBA,gBAAgBA,CAACtB,KAAK,EAAE;QACtB,IAAIrB,YAAY,GAAGqB,KAAK,CAACrB,YAAY;QACrCZ,YAAY,CAACS,mBAAmB,GAAGG,YAAY,CAACH,mBAAmB;QACnE,IAAIc,MAAM,CAACiC,mBAAmB,EAAE;UAC9BjC,MAAM,CAACiC,mBAAmB,CAACvB,KAAK,EAAEjC,YAAY,CAAC;QACjD;MACF,CAAC;MACDyD,eAAe,WAAfA,eAAeA,CAACxB,KAAK,EAAE;QACrB,IAAIrB,YAAY,GAAGqB,KAAK,CAACrB,YAAY;QAGrC,IAAIZ,YAAY,CAACU,qBAAqB,KAAKE,YAAY,CAACS,mBAAmB,EAAE;UAC3E;QACF;QAGAvB,YAAY,CAACa,yBAAyB,CAACX,YAAY,EAAEY,YAAY,CAAC;QAClE,IAAIW,MAAM,CAACmC,kBAAkB,EAAE;UAC7BnC,MAAM,CAACmC,kBAAkB,CAACzB,KAAK,EAAEjC,YAAY,CAAC;QAChD;MACF,CAAC;MACD2D,cAAc,WAAdA,cAAcA,CAAC1B,KAAK,EAAE;QACpB,IAAIrB,YAAY,GAAGqB,KAAK,CAACrB,YAAY;QACrCZ,YAAY,CAACS,mBAAmB,GAAGG,YAAY,CAACH,mBAAmB;QACnEyC,sBAAsB,CAAC1B,gBAAgB,EAAED,MAAM,CAACqC,iBAAiB,EAAE3B,KAAK,EAAEjC,YAAY,CAAC;MACzF,CAAC;MACD6D,oBAAoB,WAApBA,oBAAoBA,CAAC5B,KAAK,EAAE;QAC1BiB,sBAAsB,CAAC1B,gBAAgB,EAAED,MAAM,CAACuC,uBAAuB,EAAE7B,KAAK,EAAEjC,YAAY,CAAC;QAC7FsD,qBAAqB,CAAC9B,gBAAgB,CAAC;QACvC1B,YAAY,CAACC,uBAAuB,CAACC,YAAY,CAAC;MACpD,CAAC;MACD+D,6BAA6B,WAA7BA,6BAA6BA,CAAC9B,KAAK,EAAE;QACnC,OAAOV,MAAM,CAACyC,gCAAgC,IAAI,IAAI,GAAG,IAAI,GAAGzC,MAAM,CAACyC,gCAAgC,CAAC/B,KAAK,EAAEjC,YAAY,CAAC;MAC9H,CAAC;MAKDiE,cAAc,EAAE,SAAhBA,cAAcA,CAAEhC,KAAK,EAAI;QACvB,IAAIT,gBAAgB,CAACE,iBAAiB,KAAK,IAAI,EAAE;UAC/CO,KAAK,CAACiC,eAAe,CAAC,CAAC;UACvBjC,KAAK,CAACkC,cAAc,CAAC,CAAC;QACxB;MACF;IACF,CAAC;IACD,OAAO;MACLpC,WAAW,EAAXA,WAAW;MACXqC,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;QACrB,OAAO5C,gBAAgB,CAACC,MAAM;MAChC;IACF,CAAC;EACH;AACF,CAAC;AACD,SAASyB,sBAAsBA,CAAC1B,gBAAgB,EAAE6C,QAAQ,EAAEpC,KAAK,EAAEjC,YAAY,EAAE;EAC/E,IAAIwB,gBAAgB,CAACC,MAAM,EAAE;IAC3BnC,kBAAkB,CAAC4D,sBAAsB,CAAC1B,gBAAgB,CAACC,MAAM,CAAC;IAClED,gBAAgB,CAACC,MAAM,GAAG,IAAI;EAChC;EACA,IAAI4C,QAAQ,EAAE;IACZA,QAAQ,CAACpC,KAAK,EAAEjC,YAAY,CAAC;EAC/B;AACF;AACA,SAAS8C,uBAAuBA,CAACtB,gBAAgB,EAAE;EACjD8C,YAAY,CAAC9C,gBAAgB,CAACG,OAAO,CAAC;AACxC;AACA,SAAS2B,qBAAqBA,CAAC9B,gBAAgB,EAAE;EAC/CA,gBAAgB,CAACG,OAAO,GAAG4C,UAAU,CAAC,YAAM;IAC1C/C,gBAAgB,CAACE,iBAAiB,GAAG,KAAK;EAC5C,CAAC,EAAE,GAAG,CAAC;AACT;AACA,eAAe5B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}