{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport Platform from \"../../../exports/Platform\";\nimport RCTDeviceEventEmitter from \"./RCTDeviceEventEmitter\";\nimport invariant from 'fbjs/lib/invariant';\nvar NativeEventEmitter = function () {\n  function NativeEventEmitter(nativeModule) {\n    _classCallCheck(this, NativeEventEmitter);\n    if (Platform.OS === 'ios') {\n      invariant(nativeModule != null, '`new NativeEventEmitter()` requires a non-null argument.');\n      this._nativeModule = nativeModule;\n    }\n  }\n  return _createClass(NativeEventEmitter, [{\n    key: \"addListener\",\n    value: function addListener(eventType, listener, context) {\n      var _this = this;\n      var _this$_nativeModule;\n      (_this$_nativeModule = this._nativeModule) == null ? void 0 : _this$_nativeModule.addListener(eventType);\n      var subscription = RCTDeviceEventEmitter.addListener(eventType, listener, context);\n      return {\n        remove: function remove() {\n          if (subscription != null) {\n            var _this$_nativeModule2;\n            (_this$_nativeModule2 = _this._nativeModule) == null ? void 0 : _this$_nativeModule2.removeListeners(1);\n            subscription.remove();\n            subscription = null;\n          }\n        }\n      };\n    }\n  }, {\n    key: \"removeListener\",\n    value: function removeListener(eventType, listener) {\n      var _this$_nativeModule3;\n      (_this$_nativeModule3 = this._nativeModule) == null ? void 0 : _this$_nativeModule3.removeListeners(1);\n      RCTDeviceEventEmitter.removeListener(eventType, listener);\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(eventType) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      RCTDeviceEventEmitter.emit.apply(RCTDeviceEventEmitter, [eventType].concat(args));\n    }\n  }, {\n    key: \"removeAllListeners\",\n    value: function removeAllListeners(eventType) {\n      var _this$_nativeModule4;\n      invariant(eventType != null, '`NativeEventEmitter.removeAllListener()` requires a non-null argument.');\n      (_this$_nativeModule4 = this._nativeModule) == null ? void 0 : _this$_nativeModule4.removeListeners(this.listenerCount(eventType));\n      RCTDeviceEventEmitter.removeAllListeners(eventType);\n    }\n  }, {\n    key: \"listenerCount\",\n    value: function listenerCount(eventType) {\n      return RCTDeviceEventEmitter.listenerCount(eventType);\n    }\n  }]);\n}();\nexport { NativeEventEmitter as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "Platform", "RCTDeviceEventEmitter", "invariant", "NativeEventEmitter", "nativeModule", "OS", "_nativeModule", "key", "value", "addListener", "eventType", "listener", "context", "_this", "_this$_nativeModule", "subscription", "remove", "_this$_nativeModule2", "removeListeners", "removeListener", "_this$_nativeModule3", "emit", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "removeAllListeners", "_this$_nativeModule4", "listenerCount", "default"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/react-native-web/dist/vendor/react-native/EventEmitter/NativeEventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport Platform from '../../../exports/Platform';\nimport RCTDeviceEventEmitter from './RCTDeviceEventEmitter';\nimport invariant from 'fbjs/lib/invariant';\n/**\n * `NativeEventEmitter` is intended for use by Native Modules to emit events to\n * JavaScript listeners. If a `NativeModule` is supplied to the constructor, it\n * will be notified (via `addListener` and `removeListeners`) when the listener\n * count changes to manage \"native memory\".\n *\n * Currently, all native events are fired via a global `RCTDeviceEventEmitter`.\n * This means event names must be globally unique, and it means that call sites\n * can theoretically listen to `RCTDeviceEventEmitter` (although discouraged).\n */\nexport default class NativeEventEmitter {\n  constructor(nativeModule) {\n    if (Platform.OS === 'ios') {\n      invariant(nativeModule != null, '`new NativeEventEmitter()` requires a non-null argument.');\n      this._nativeModule = nativeModule;\n    }\n  }\n  addListener(eventType, listener, context) {\n    var _this$_nativeModule;\n    (_this$_nativeModule = this._nativeModule) == null ? void 0 : _this$_nativeModule.addListener(eventType);\n    var subscription = RCTDeviceEventEmitter.addListener(eventType, listener, context);\n    return {\n      remove: () => {\n        if (subscription != null) {\n          var _this$_nativeModule2;\n          (_this$_nativeModule2 = this._nativeModule) == null ? void 0 : _this$_nativeModule2.removeListeners(1);\n          // $FlowFixMe[incompatible-use]\n          subscription.remove();\n          subscription = null;\n        }\n      }\n    };\n  }\n\n  /**\n   * @deprecated Use `remove` on the EventSubscription from `addListener`.\n   */\n  removeListener(eventType, listener) {\n    var _this$_nativeModule3;\n    (_this$_nativeModule3 = this._nativeModule) == null ? void 0 : _this$_nativeModule3.removeListeners(1);\n    // NOTE: This will report a deprecation notice via `console.error`.\n    // $FlowFixMe[prop-missing] - `removeListener` exists but is deprecated.\n    RCTDeviceEventEmitter.removeListener(eventType, listener);\n  }\n  emit(eventType) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    // Generally, `RCTDeviceEventEmitter` is directly invoked. But this is\n    // included for completeness.\n    RCTDeviceEventEmitter.emit(eventType, ...args);\n  }\n  removeAllListeners(eventType) {\n    var _this$_nativeModule4;\n    invariant(eventType != null, '`NativeEventEmitter.removeAllListener()` requires a non-null argument.');\n    (_this$_nativeModule4 = this._nativeModule) == null ? void 0 : _this$_nativeModule4.removeListeners(this.listenerCount(eventType));\n    RCTDeviceEventEmitter.removeAllListeners(eventType);\n  }\n  listenerCount(eventType) {\n    return RCTDeviceEventEmitter.listenerCount(eventType);\n  }\n}"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,OAAOC,QAAQ;AACf,OAAOC,qBAAqB;AAC5B,OAAOC,SAAS,MAAM,oBAAoB;AAAC,IAWtBC,kBAAkB;EACrC,SAAAA,mBAAYC,YAAY,EAAE;IAAAN,eAAA,OAAAK,kBAAA;IACxB,IAAIH,QAAQ,CAACK,EAAE,KAAK,KAAK,EAAE;MACzBH,SAAS,CAACE,YAAY,IAAI,IAAI,EAAE,0DAA0D,CAAC;MAC3F,IAAI,CAACE,aAAa,GAAGF,YAAY;IACnC;EACF;EAAC,OAAAL,YAAA,CAAAI,kBAAA;IAAAI,GAAA;IAAAC,KAAA,EACD,SAAAC,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MAAA,IAAAC,KAAA;MACxC,IAAIC,mBAAmB;MACvB,CAACA,mBAAmB,GAAG,IAAI,CAACR,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGQ,mBAAmB,CAACL,WAAW,CAACC,SAAS,CAAC;MACxG,IAAIK,YAAY,GAAGd,qBAAqB,CAACQ,WAAW,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;MAClF,OAAO;QACLI,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;UACZ,IAAID,YAAY,IAAI,IAAI,EAAE;YACxB,IAAIE,oBAAoB;YACxB,CAACA,oBAAoB,GAAGJ,KAAI,CAACP,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,oBAAoB,CAACC,eAAe,CAAC,CAAC,CAAC;YAEtGH,YAAY,CAACC,MAAM,CAAC,CAAC;YACrBD,YAAY,GAAG,IAAI;UACrB;QACF;MACF,CAAC;IACH;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAKD,SAAAW,cAAcA,CAACT,SAAS,EAAEC,QAAQ,EAAE;MAClC,IAAIS,oBAAoB;MACxB,CAACA,oBAAoB,GAAG,IAAI,CAACd,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGc,oBAAoB,CAACF,eAAe,CAAC,CAAC,CAAC;MAGtGjB,qBAAqB,CAACkB,cAAc,CAACT,SAAS,EAAEC,QAAQ,CAAC;IAC3D;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAa,IAAIA,CAACX,SAAS,EAAE;MACd,KAAK,IAAIY,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAClC;MAGA1B,qBAAqB,CAACoB,IAAI,CAAAO,KAAA,CAA1B3B,qBAAqB,GAAMS,SAAS,EAAAmB,MAAA,CAAKJ,IAAI,EAAC;IAChD;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EACD,SAAAsB,kBAAkBA,CAACpB,SAAS,EAAE;MAC5B,IAAIqB,oBAAoB;MACxB7B,SAAS,CAACQ,SAAS,IAAI,IAAI,EAAE,wEAAwE,CAAC;MACtG,CAACqB,oBAAoB,GAAG,IAAI,CAACzB,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyB,oBAAoB,CAACb,eAAe,CAAC,IAAI,CAACc,aAAa,CAACtB,SAAS,CAAC,CAAC;MAClIT,qBAAqB,CAAC6B,kBAAkB,CAACpB,SAAS,CAAC;IACrD;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAwB,aAAaA,CAACtB,SAAS,EAAE;MACvB,OAAOT,qBAAqB,CAAC+B,aAAa,CAACtB,SAAS,CAAC;IACvD;EAAC;AAAA;AAAA,SAlDkBP,kBAAkB,IAAA8B,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}