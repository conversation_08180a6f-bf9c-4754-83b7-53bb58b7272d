{"ast": null, "code": "import Platform from \"../../../exports/Platform\";\nexport default Platform;", "map": {"version": 3, "names": ["Platform"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/Utilities/Platform.js"], "sourcesContent": ["import Platform from '../../../exports/Platform';\nexport default Platform;"], "mappings": "AAAA,OAAOA,QAAQ;AACf,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}