{"ast": null, "code": "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport canUseDOM from \"../../modules/canUseDom\";\nfunction ModalPortal(props) {\n  var children = props.children;\n  var elementRef = React.useRef(null);\n  if (canUseDOM && !elementRef.current) {\n    var element = document.createElement('div');\n    if (element && document.body) {\n      document.body.appendChild(element);\n      elementRef.current = element;\n    }\n  }\n  React.useEffect(function () {\n    if (canUseDOM) {\n      return function () {\n        if (document.body && elementRef.current) {\n          document.body.removeChild(elementRef.current);\n          elementRef.current = null;\n        }\n      };\n    }\n  }, []);\n  return elementRef.current && canUseDOM ? ReactDOM.createPortal(children, elementRef.current) : null;\n}\nexport default ModalPortal;", "map": {"version": 3, "names": ["React", "ReactDOM", "canUseDOM", "ModalPortal", "props", "children", "elementRef", "useRef", "current", "element", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "createPortal"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Modal/ModalPortal.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport canUseDOM from '../../modules/canUseDom';\nfunction ModalPortal(props) {\n  var children = props.children;\n  var elementRef = React.useRef(null);\n  if (canUseDOM && !elementRef.current) {\n    var element = document.createElement('div');\n    if (element && document.body) {\n      document.body.appendChild(element);\n      elementRef.current = element;\n    }\n  }\n  React.useEffect(() => {\n    if (canUseDOM) {\n      return () => {\n        if (document.body && elementRef.current) {\n          document.body.removeChild(elementRef.current);\n          elementRef.current = null;\n        }\n      };\n    }\n  }, []);\n  return elementRef.current && canUseDOM ? /*#__PURE__*/ReactDOM.createPortal(children, elementRef.current) : null;\n}\nexport default ModalPortal;"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,SAAS;AAChB,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,IAAIC,UAAU,GAAGN,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIL,SAAS,IAAI,CAACI,UAAU,CAACE,OAAO,EAAE;IACpC,IAAIC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC3C,IAAIF,OAAO,IAAIC,QAAQ,CAACE,IAAI,EAAE;MAC5BF,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,OAAO,CAAC;MAClCH,UAAU,CAACE,OAAO,GAAGC,OAAO;IAC9B;EACF;EACAT,KAAK,CAACc,SAAS,CAAC,YAAM;IACpB,IAAIZ,SAAS,EAAE;MACb,OAAO,YAAM;QACX,IAAIQ,QAAQ,CAACE,IAAI,IAAIN,UAAU,CAACE,OAAO,EAAE;UACvCE,QAAQ,CAACE,IAAI,CAACG,WAAW,CAACT,UAAU,CAACE,OAAO,CAAC;UAC7CF,UAAU,CAACE,OAAO,GAAG,IAAI;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,UAAU,CAACE,OAAO,IAAIN,SAAS,GAAgBD,QAAQ,CAACe,YAAY,CAACX,QAAQ,EAAEC,UAAU,CAACE,OAAO,CAAC,GAAG,IAAI;AAClH;AACA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}