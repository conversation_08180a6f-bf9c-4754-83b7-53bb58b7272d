{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React from 'react';\nimport { AdornmentSide, AdornmentType, InputMode } from \"./enums\";\nimport TextInputAffix, { AffixAdornment } from \"./TextInputAffix\";\nimport TextInputIcon, { IconAdornment } from \"./TextInputIcon\";\nimport { getConstants } from \"../helpers\";\nexport function getAdornmentConfig(_ref) {\n  var left = _ref.left,\n    right = _ref.right;\n  var adornmentConfig = [];\n  if (left || right) {\n    [{\n      side: AdornmentSide.Left,\n      adornment: left\n    }, {\n      side: AdornmentSide.Right,\n      adornment: right\n    }].forEach(function (_ref2) {\n      var side = _ref2.side,\n        adornment = _ref2.adornment;\n      if (adornment && React.isValidElement(adornment)) {\n        var type;\n        if (adornment.type === TextInputAffix) {\n          type = AdornmentType.Affix;\n        } else if (adornment.type === TextInputIcon) {\n          type = AdornmentType.Icon;\n        }\n        adornmentConfig.push({\n          side: side,\n          type: type\n        });\n      }\n    });\n  }\n  return adornmentConfig;\n}\nexport function getAdornmentStyleAdjustmentForNativeInput(_ref3) {\n  var adornmentConfig = _ref3.adornmentConfig,\n    leftAffixWidth = _ref3.leftAffixWidth,\n    rightAffixWidth = _ref3.rightAffixWidth,\n    paddingHorizontal = _ref3.paddingHorizontal,\n    _ref3$inputOffset = _ref3.inputOffset,\n    inputOffset = _ref3$inputOffset === void 0 ? 0 : _ref3$inputOffset,\n    mode = _ref3.mode,\n    isV3 = _ref3.isV3;\n  var _getConstants = getConstants(isV3),\n    OUTLINED_INPUT_OFFSET = _getConstants.OUTLINED_INPUT_OFFSET,\n    ADORNMENT_OFFSET = _getConstants.ADORNMENT_OFFSET;\n  if (adornmentConfig.length) {\n    var adornmentStyleAdjustmentForNativeInput = adornmentConfig.map(function (_ref4) {\n      var type = _ref4.type,\n        side = _ref4.side;\n      var isLeftSide = side === AdornmentSide.Left;\n      var inputModeAdornemntOffset = mode === InputMode.Outlined ? ADORNMENT_OFFSET + OUTLINED_INPUT_OFFSET : ADORNMENT_OFFSET;\n      var paddingKey = `padding${captalize(side)}`;\n      var affixWidth = isLeftSide ? leftAffixWidth : rightAffixWidth;\n      var padding = typeof paddingHorizontal === 'number' ? paddingHorizontal : inputModeAdornemntOffset;\n      var offset = affixWidth + padding;\n      var isAffix = type === AdornmentType.Affix;\n      var marginKey = `margin${captalize(side)}`;\n      return _defineProperty(_defineProperty({}, marginKey, isAffix ? 0 : offset), paddingKey, isAffix ? offset : inputOffset);\n    });\n    var allStyleAdjustmentsMerged = adornmentStyleAdjustmentForNativeInput.reduce(function (mergedStyles, currentStyle) {\n      return _objectSpread(_objectSpread({}, mergedStyles), currentStyle);\n    }, {});\n    return allStyleAdjustmentsMerged;\n  } else {\n    return [{}];\n  }\n}\nvar captalize = function captalize(text) {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n};\nvar TextInputAdornment = function TextInputAdornment(_ref5) {\n  var adornmentConfig = _ref5.adornmentConfig,\n    left = _ref5.left,\n    right = _ref5.right,\n    onAffixChange = _ref5.onAffixChange,\n    textStyle = _ref5.textStyle,\n    visible = _ref5.visible,\n    topPosition = _ref5.topPosition,\n    isTextInputFocused = _ref5.isTextInputFocused,\n    forceFocus = _ref5.forceFocus,\n    paddingHorizontal = _ref5.paddingHorizontal,\n    maxFontSizeMultiplier = _ref5.maxFontSizeMultiplier,\n    theme = _ref5.theme,\n    disabled = _ref5.disabled;\n  if (adornmentConfig.length) {\n    return React.createElement(React.Fragment, null, adornmentConfig.map(function (_ref6) {\n      var type = _ref6.type,\n        side = _ref6.side;\n      var inputAdornmentComponent;\n      if (side === AdornmentSide.Left) {\n        inputAdornmentComponent = left;\n      } else if (side === AdornmentSide.Right) {\n        inputAdornmentComponent = right;\n      }\n      var commonProps = {\n        side: side,\n        testID: `${side}-${type}-adornment`,\n        isTextInputFocused: isTextInputFocused,\n        paddingHorizontal: paddingHorizontal,\n        disabled: disabled\n      };\n      if (type === AdornmentType.Icon) {\n        return React.createElement(IconAdornment, _extends({}, commonProps, {\n          theme: theme,\n          key: side,\n          icon: inputAdornmentComponent,\n          topPosition: topPosition[AdornmentType.Icon],\n          forceFocus: forceFocus\n        }));\n      } else if (type === AdornmentType.Affix) {\n        return React.createElement(AffixAdornment, _extends({}, commonProps, {\n          key: side,\n          topPosition: topPosition[AdornmentType.Affix][side],\n          affix: inputAdornmentComponent,\n          textStyle: textStyle,\n          onLayout: onAffixChange[side],\n          visible: visible,\n          maxFontSizeMultiplier: maxFontSizeMultiplier\n        }));\n      } else {\n        return null;\n      }\n    }));\n  } else {\n    return null;\n  }\n};\nexport default TextInputAdornment;", "map": {"version": 3, "names": ["React", "AdornmentSide", "AdornmentType", "InputMode", "TextInputAffix", "AffixAdornment", "TextInputIcon", "IconAdornment", "getConstants", "getAdornmentConfig", "_ref", "left", "right", "adornmentConfig", "side", "Left", "adornment", "Right", "for<PERSON>ach", "_ref2", "isValidElement", "type", "Affix", "Icon", "push", "getAdornmentStyleAdjustmentForNativeInput", "_ref3", "leftAffixWidth", "rightAffix<PERSON>idth", "paddingHorizontal", "_ref3$inputOffset", "inputOffset", "mode", "isV3", "_getConstants", "OUTLINED_INPUT_OFFSET", "ADORNMENT_OFFSET", "length", "adornmentStyleAdjustmentForNativeInput", "map", "_ref4", "isLeftSide", "inputModeAdornemntOffset", "Outlined", "paddingKey", "captalize", "affix<PERSON>id<PERSON>", "padding", "offset", "isAffix", "margin<PERSON>ey", "_defineProperty", "allStyleAdjustmentsMerged", "reduce", "mergedStyles", "currentStyle", "_objectSpread", "text", "char<PERSON>t", "toUpperCase", "slice", "TextInputAdornment", "_ref5", "onAffixChange", "textStyle", "visible", "topPosition", "isTextInputFocused", "forceFocus", "maxFontSizeMultiplier", "theme", "disabled", "createElement", "Fragment", "_ref6", "inputAdornmentComponent", "commonProps", "testID", "_extends", "key", "icon", "affix", "onLayout"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/TextInput/Adornment/TextInputAdornment.tsx"], "sourcesContent": ["import React from 'react';\nimport type {\n  LayoutChangeEvent,\n  TextStyle,\n  StyleProp,\n  Animated,\n  DimensionValue,\n} from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { AdornmentSide, AdornmentType, InputMode } from './enums';\nimport TextInputAffix, { AffixAdornment } from './TextInputAffix';\nimport TextInputIcon, { IconAdornment } from './TextInputIcon';\nimport type {\n  AdornmentConfig,\n  AdornmentStyleAdjustmentForNativeInput,\n} from './types';\nimport { getConstants } from '../helpers';\n\nexport function getAdornmentConfig({\n  left,\n  right,\n}: {\n  left?: React.ReactNode;\n  right?: React.ReactNode;\n}): Array<AdornmentConfig> {\n  let adornmentConfig: any[] = [];\n  if (left || right) {\n    [\n      { side: AdornmentSide.Left, adornment: left },\n      { side: AdornmentSide.Right, adornment: right },\n    ].forEach(({ side, adornment }) => {\n      if (adornment && React.isValidElement(adornment)) {\n        let type;\n        if (adornment.type === TextInputAffix) {\n          type = AdornmentType.Affix;\n        } else if (adornment.type === TextInputIcon) {\n          type = AdornmentType.Icon;\n        }\n        adornmentConfig.push({\n          side,\n          type,\n        });\n      }\n    });\n  }\n\n  return adornmentConfig;\n}\n\nexport function getAdornmentStyleAdjustmentForNativeInput({\n  adornmentConfig,\n  leftAffixWidth,\n  rightAffixWidth,\n  paddingHorizontal,\n  inputOffset = 0,\n  mode,\n  isV3,\n}: {\n  inputOffset?: number;\n  adornmentConfig: AdornmentConfig[];\n  leftAffixWidth: number;\n  rightAffixWidth: number;\n  mode?: 'outlined' | 'flat';\n  paddingHorizontal?: DimensionValue;\n  isV3?: boolean;\n}): AdornmentStyleAdjustmentForNativeInput | {} {\n  const { OUTLINED_INPUT_OFFSET, ADORNMENT_OFFSET } = getConstants(isV3);\n\n  if (adornmentConfig.length) {\n    const adornmentStyleAdjustmentForNativeInput = adornmentConfig.map(\n      ({ type, side }: AdornmentConfig) => {\n        const isLeftSide = side === AdornmentSide.Left;\n        const inputModeAdornemntOffset =\n          mode === InputMode.Outlined\n            ? ADORNMENT_OFFSET + OUTLINED_INPUT_OFFSET\n            : ADORNMENT_OFFSET;\n        const paddingKey = `padding${captalize(side)}`;\n        const affixWidth = isLeftSide ? leftAffixWidth : rightAffixWidth;\n        const padding =\n          typeof paddingHorizontal === 'number'\n            ? paddingHorizontal\n            : inputModeAdornemntOffset;\n        const offset = affixWidth + padding;\n\n        const isAffix = type === AdornmentType.Affix;\n        const marginKey = `margin${captalize(side)}`;\n\n        return {\n          [marginKey]: isAffix ? 0 : offset,\n          [paddingKey]: isAffix ? offset : inputOffset,\n        };\n      }\n    );\n    const allStyleAdjustmentsMerged =\n      adornmentStyleAdjustmentForNativeInput.reduce(\n        (mergedStyles, currentStyle) => {\n          return {\n            ...mergedStyles,\n            ...currentStyle,\n          };\n        },\n        {}\n      );\n    return allStyleAdjustmentsMerged;\n  } else {\n    return [{}];\n  }\n}\n\nconst captalize = (text: string) =>\n  text.charAt(0).toUpperCase() + text.slice(1);\n\nexport interface TextInputAdornmentProps {\n  forceFocus: () => void;\n  adornmentConfig: AdornmentConfig[];\n  topPosition: {\n    [AdornmentType.Affix]: {\n      [AdornmentSide.Left]: number | null;\n      [AdornmentSide.Right]: number | null;\n    };\n    [AdornmentType.Icon]: number;\n  };\n  onAffixChange: {\n    [AdornmentSide.Left]: (event: LayoutChangeEvent) => void;\n    [AdornmentSide.Right]: (event: LayoutChangeEvent) => void;\n  };\n  left?: React.ReactNode;\n  right?: React.ReactNode;\n  textStyle?: StyleProp<TextStyle>;\n  visible?: Animated.Value;\n  isTextInputFocused: boolean;\n  paddingHorizontal?: DimensionValue;\n  maxFontSizeMultiplier?: number | undefined | null;\n  theme?: ThemeProp;\n  disabled?: boolean;\n}\n\nconst TextInputAdornment: React.FunctionComponent<TextInputAdornmentProps> = ({\n  adornmentConfig,\n  left,\n  right,\n  onAffixChange,\n  textStyle,\n  visible,\n  topPosition,\n  isTextInputFocused,\n  forceFocus,\n  paddingHorizontal,\n  maxFontSizeMultiplier,\n  theme,\n  disabled,\n}) => {\n  if (adornmentConfig.length) {\n    return (\n      <>\n        {adornmentConfig.map(({ type, side }: AdornmentConfig) => {\n          let inputAdornmentComponent;\n          if (side === AdornmentSide.Left) {\n            inputAdornmentComponent = left;\n          } else if (side === AdornmentSide.Right) {\n            inputAdornmentComponent = right;\n          }\n\n          const commonProps = {\n            side: side,\n            testID: `${side}-${type}-adornment`,\n            isTextInputFocused,\n            paddingHorizontal,\n            disabled,\n          };\n          if (type === AdornmentType.Icon) {\n            return (\n              <IconAdornment\n                {...commonProps}\n                theme={theme}\n                key={side}\n                icon={inputAdornmentComponent}\n                topPosition={topPosition[AdornmentType.Icon]}\n                forceFocus={forceFocus}\n              />\n            );\n          } else if (type === AdornmentType.Affix) {\n            return (\n              <AffixAdornment\n                {...commonProps}\n                key={side}\n                topPosition={topPosition[AdornmentType.Affix][side]}\n                affix={inputAdornmentComponent}\n                textStyle={textStyle}\n                onLayout={onAffixChange[side]}\n                visible={visible}\n                maxFontSizeMultiplier={maxFontSizeMultiplier}\n              />\n            );\n          } else {\n            return null;\n          }\n        })}\n      </>\n    );\n  } else {\n    return null;\n  }\n};\n\nexport default TextInputAdornment;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAWzB,SAASC,aAAa,EAAEC,aAAa,EAAEC,SAAS;AAChD,OAAOC,cAAc,IAAIC,cAAc;AACvC,OAAOC,aAAa,IAAIC,aAAa;AAKrC,SAASC,YAAY;AAErB,OAAO,SAASC,kBAAkBA,CAAAC,IAAA,EAMP;EAAA,IALzBC,IAAI,GAKLD,IAAA,CALCC,IAAI;IACJC,KAAA,GAIDF,IAAA,CAJCE,KAAA;EAKA,IAAIC,eAAsB,GAAG,EAAE;EAC/B,IAAIF,IAAI,IAAIC,KAAK,EAAE;IACjB,CACE;MAAEE,IAAI,EAAEb,aAAa,CAACc,IAAI;MAAEC,SAAS,EAAEL;IAAK,CAAC,EAC7C;MAAEG,IAAI,EAAEb,aAAa,CAACgB,KAAK;MAAED,SAAS,EAAEJ;IAAM,CAAC,CAChD,CAACM,OAAO,CAAC,UAAAC,KAAA,EAAyB;MAAA,IAAtBL,IAAI,GAAaK,KAAA,CAAjBL,IAAI;QAAEE,SAAA,GAAWG,KAAA,CAAXH,SAAA;MACjB,IAAIA,SAAS,IAAIhB,KAAK,CAACoB,cAAc,CAACJ,SAAS,CAAC,EAAE;QAChD,IAAIK,IAAI;QACR,IAAIL,SAAS,CAACK,IAAI,KAAKjB,cAAc,EAAE;UACrCiB,IAAI,GAAGnB,aAAa,CAACoB,KAAK;QAC5B,CAAC,MAAM,IAAIN,SAAS,CAACK,IAAI,KAAKf,aAAa,EAAE;UAC3Ce,IAAI,GAAGnB,aAAa,CAACqB,IAAI;QAC3B;QACAV,eAAe,CAACW,IAAI,CAAC;UACnBV,IAAI,EAAJA,IAAI;UACJO,IAAA,EAAAA;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEA,OAAOR,eAAe;AACxB;AAEA,OAAO,SAASY,yCAAyCA,CAAAC,KAAA,EAgBT;EAAA,IAf9Cb,eAAe,GAehBa,KAAA,CAfCb,eAAe;IACfc,cAAc,GAcfD,KAAA,CAdCC,cAAc;IACdC,eAAe,GAahBF,KAAA,CAbCE,eAAe;IACfC,iBAAiB,GAYlBH,KAAA,CAZCG,iBAAiB;IAAAC,iBAAA,GAYlBJ,KAAA,CAXCK,WAAW;IAAXA,WAAW,GAAAD,iBAAA,cAAG,CAAC,GAAAA,iBAAA;IACfE,IAAI,GAULN,KAAA,CAVCM,IAAI;IACJC,IAAA,GASDP,KAAA,CATCO,IAAA;EAUA,IAAAC,aAAA,GAAoD1B,YAAY,CAACyB,IAAI,CAAC;IAA9DE,qBAAqB,GAAAD,aAAA,CAArBC,qBAAqB;IAAEC,gBAAA,GAAAF,aAAA,CAAAE,gBAAA;EAE/B,IAAIvB,eAAe,CAACwB,MAAM,EAAE;IAC1B,IAAMC,sCAAsC,GAAGzB,eAAe,CAAC0B,GAAG,CAChE,UAAAC,KAAA,EAAqC;MAAA,IAAlCnB,IAAI,GAAyBmB,KAAA,CAA7BnB,IAAI;QAAEP,IAAA,GAAuB0B,KAAA,CAAvB1B,IAAA;MACP,IAAM2B,UAAU,GAAG3B,IAAI,KAAKb,aAAa,CAACc,IAAI;MAC9C,IAAM2B,wBAAwB,GAC5BV,IAAI,KAAK7B,SAAS,CAACwC,QAAQ,GACvBP,gBAAgB,GAAGD,qBAAqB,GACxCC,gBAAgB;MACtB,IAAMQ,UAAU,GAAI,UAASC,SAAS,CAAC/B,IAAI,CAAE,EAAC;MAC9C,IAAMgC,UAAU,GAAGL,UAAU,GAAGd,cAAc,GAAGC,eAAe;MAChE,IAAMmB,OAAO,GACX,OAAOlB,iBAAiB,KAAK,QAAQ,GACjCA,iBAAiB,GACjBa,wBAAwB;MAC9B,IAAMM,MAAM,GAAGF,UAAU,GAAGC,OAAO;MAEnC,IAAME,OAAO,GAAG5B,IAAI,KAAKnB,aAAa,CAACoB,KAAK;MAC5C,IAAM4B,SAAS,GAAI,SAAQL,SAAS,CAAC/B,IAAI,CAAE,EAAC;MAE5C,OAAAqC,eAAA,CAAAA,eAAA,KACGD,SAAS,EAAGD,OAAO,GAAG,CAAC,GAAGD,MAAM,GAChCJ,UAAU,EAAGK,OAAO,GAAGD,MAAM,GAAGjB,WAAA;IAErC,CACF,CAAC;IACD,IAAMqB,yBAAyB,GAC7Bd,sCAAsC,CAACe,MAAM,CAC3C,UAACC,YAAY,EAAEC,YAAY,EAAK;MAC9B,OAAAC,aAAA,CAAAA,aAAA,KACKF,YAAY,GACZC,YAAA;IAEP,CAAC,EACD,CAAC,CACH,CAAC;IACH,OAAOH,yBAAyB;EAClC,CAAC,MAAM;IACL,OAAO,CAAC,CAAC,CAAC,CAAC;EACb;AACF;AAEA,IAAMP,SAAS,GAAI,SAAbA,SAASA,CAAIY,IAAY;EAAA,OAC7BA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;AAAA;AA2B9C,IAAMC,kBAAoE,GAAG,SAAvEA,kBAAoEA,CAAGC,KAAA,EAcvE;EAAA,IAbJjD,eAAe,GAahBiD,KAAA,CAbCjD,eAAe;IACfF,IAAI,GAYLmD,KAAA,CAZCnD,IAAI;IACJC,KAAK,GAWNkD,KAAA,CAXClD,KAAK;IACLmD,aAAa,GAUdD,KAAA,CAVCC,aAAa;IACbC,SAAS,GASVF,KAAA,CATCE,SAAS;IACTC,OAAO,GAQRH,KAAA,CARCG,OAAO;IACPC,WAAW,GAOZJ,KAAA,CAPCI,WAAW;IACXC,kBAAkB,GAMnBL,KAAA,CANCK,kBAAkB;IAClBC,UAAU,GAKXN,KAAA,CALCM,UAAU;IACVvC,iBAAiB,GAIlBiC,KAAA,CAJCjC,iBAAiB;IACjBwC,qBAAqB,GAGtBP,KAAA,CAHCO,qBAAqB;IACrBC,KAAK,GAENR,KAAA,CAFCQ,KAAK;IACLC,QAAA,GACDT,KAAA,CADCS,QAAA;EAEA,IAAI1D,eAAe,CAACwB,MAAM,EAAE;IAC1B,OACErC,KAAA,CAAAwE,aAAA,CAAAxE,KAAA,CAAAyE,QAAA,QACG5D,eAAe,CAAC0B,GAAG,CAAC,UAAAmC,KAAA,EAAqC;MAAA,IAAlCrD,IAAI,GAAyBqD,KAAA,CAA7BrD,IAAI;QAAEP,IAAA,GAAuB4D,KAAA,CAAvB5D,IAAA;MAC5B,IAAI6D,uBAAuB;MAC3B,IAAI7D,IAAI,KAAKb,aAAa,CAACc,IAAI,EAAE;QAC/B4D,uBAAuB,GAAGhE,IAAI;MAChC,CAAC,MAAM,IAAIG,IAAI,KAAKb,aAAa,CAACgB,KAAK,EAAE;QACvC0D,uBAAuB,GAAG/D,KAAK;MACjC;MAEA,IAAMgE,WAAW,GAAG;QAClB9D,IAAI,EAAEA,IAAI;QACV+D,MAAM,EAAG,GAAE/D,IAAK,IAAGO,IAAK,YAAW;QACnC8C,kBAAkB,EAAlBA,kBAAkB;QAClBtC,iBAAiB,EAAjBA,iBAAiB;QACjB0C,QAAA,EAAAA;MACF,CAAC;MACD,IAAIlD,IAAI,KAAKnB,aAAa,CAACqB,IAAI,EAAE;QAC/B,OACEvB,KAAA,CAAAwE,aAAA,CAACjE,aAAa,EAAAuE,QAAA,KACRF,WAAW;UACfN,KAAK,EAAEA,KAAM;UACbS,GAAG,EAAEjE,IAAK;UACVkE,IAAI,EAAEL,uBAAwB;UAC9BT,WAAW,EAAEA,WAAW,CAAChE,aAAa,CAACqB,IAAI,CAAE;UAC7C6C,UAAU,EAAEA;QAAW,EACxB,CAAC;MAEN,CAAC,MAAM,IAAI/C,IAAI,KAAKnB,aAAa,CAACoB,KAAK,EAAE;QACvC,OACEtB,KAAA,CAAAwE,aAAA,CAACnE,cAAc,EAAAyE,QAAA,KACTF,WAAW;UACfG,GAAG,EAAEjE,IAAK;UACVoD,WAAW,EAAEA,WAAW,CAAChE,aAAa,CAACoB,KAAK,CAAC,CAACR,IAAI,CAAE;UACpDmE,KAAK,EAAEN,uBAAwB;UAC/BX,SAAS,EAAEA,SAAU;UACrBkB,QAAQ,EAAEnB,aAAa,CAACjD,IAAI,CAAE;UAC9BmD,OAAO,EAAEA,OAAQ;UACjBI,qBAAqB,EAAEA;QAAsB,EAC9C,CAAC;MAEN,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC,CACD,CAAC;EAEP,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF,CAAC;AAED,eAAeR,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}