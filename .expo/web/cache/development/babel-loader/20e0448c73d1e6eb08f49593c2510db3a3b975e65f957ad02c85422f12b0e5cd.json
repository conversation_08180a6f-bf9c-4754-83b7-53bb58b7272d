{"ast": null, "code": "'use client';\n\nimport { createContext } from 'react';\nvar TextAncestorContext = createContext(false);\nexport default TextAncestorContext;", "map": {"version": 3, "names": ["createContext", "TextAncestorContext"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Text/TextAncestorContext.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport { createContext } from 'react';\nvar TextAncestorContext = /*#__PURE__*/createContext(false);\nexport default TextAncestorContext;"], "mappings": "AASA,YAAY;;AAEZ,SAASA,aAAa,QAAQ,OAAO;AACrC,IAAIC,mBAAmB,GAAgBD,aAAa,CAAC,KAAK,CAAC;AAC3D,eAAeC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}