{"ast": null, "code": "import mergeOptions from 'merge-options';\nvar merge = mergeOptions.bind({\n  concatArrays: true,\n  ignoreUndefined: true\n});\nfunction mergeLocalStorageItem(key, value) {\n  var oldValue = window.localStorage.getItem(key);\n  if (oldValue) {\n    var oldObject = JSON.parse(oldValue);\n    var newObject = JSON.parse(value);\n    var nextValue = JSON.stringify(merge(oldObject, newObject));\n    window.localStorage.setItem(key, nextValue);\n  } else {\n    window.localStorage.setItem(key, value);\n  }\n}\nfunction createPromise(getValue, callback) {\n  return new Promise(function (resolve, reject) {\n    try {\n      var value = getValue();\n      callback === null || callback === void 0 ? void 0 : callback(null, value);\n      resolve(value);\n    } catch (err) {\n      callback === null || callback === void 0 ? void 0 : callback(err);\n      reject(err);\n    }\n  });\n}\nfunction createPromiseAll(promises, callback, processResult) {\n  return Promise.all(promises).then(function (result) {\n    var _ref;\n    var value = (_ref = processResult === null || processResult === void 0 ? void 0 : processResult(result)) != null ? _ref : null;\n    callback === null || callback === void 0 ? void 0 : callback(null, value);\n    return Promise.resolve(value);\n  }, function (errors) {\n    callback === null || callback === void 0 ? void 0 : callback(errors);\n    return Promise.reject(errors);\n  });\n}\nvar AsyncStorage = {\n  getItem: function getItem(key, callback) {\n    return createPromise(function () {\n      return window.localStorage.getItem(key);\n    }, callback);\n  },\n  setItem: function setItem(key, value, callback) {\n    return createPromise(function () {\n      return window.localStorage.setItem(key, value);\n    }, callback);\n  },\n  removeItem: function removeItem(key, callback) {\n    return createPromise(function () {\n      return window.localStorage.removeItem(key);\n    }, callback);\n  },\n  mergeItem: function mergeItem(key, value, callback) {\n    return createPromise(function () {\n      return mergeLocalStorageItem(key, value);\n    }, callback);\n  },\n  clear: function clear(callback) {\n    return createPromise(function () {\n      return window.localStorage.clear();\n    }, callback);\n  },\n  getAllKeys: function getAllKeys(callback) {\n    return createPromise(function () {\n      var numberOfKeys = window.localStorage.length;\n      var keys = [];\n      for (var i = 0; i < numberOfKeys; i += 1) {\n        var key = window.localStorage.key(i) || '';\n        keys.push(key);\n      }\n      return keys;\n    }, callback);\n  },\n  flushGetRequests: function flushGetRequests() {\n    return undefined;\n  },\n  multiGet: function multiGet(keys, callback) {\n    var promises = keys.map(function (key) {\n      return AsyncStorage.getItem(key);\n    });\n    var processResult = function processResult(result) {\n      return result.map(function (value, i) {\n        return [keys[i], value];\n      });\n    };\n    return createPromiseAll(promises, callback, processResult);\n  },\n  multiSet: function multiSet(keyValuePairs, callback) {\n    var promises = keyValuePairs.map(function (item) {\n      return AsyncStorage.setItem(item[0], item[1]);\n    });\n    return createPromiseAll(promises, callback);\n  },\n  multiRemove: function multiRemove(keys, callback) {\n    var promises = keys.map(function (key) {\n      return AsyncStorage.removeItem(key);\n    });\n    return createPromiseAll(promises, callback);\n  },\n  multiMerge: function multiMerge(keyValuePairs, callback) {\n    var promises = keyValuePairs.map(function (item) {\n      return AsyncStorage.mergeItem(item[0], item[1]);\n    });\n    return createPromiseAll(promises, callback);\n  }\n};\nexport default AsyncStorage;", "map": {"version": 3, "names": ["mergeOptions", "merge", "bind", "concatArrays", "ignoreUndefined", "mergeLocalStorageItem", "key", "value", "oldValue", "window", "localStorage", "getItem", "oldObject", "JSON", "parse", "newObject", "nextValue", "stringify", "setItem", "createPromise", "getValue", "callback", "Promise", "resolve", "reject", "err", "createPromiseAll", "promises", "processResult", "all", "then", "result", "_ref", "errors", "AsyncStorage", "removeItem", "mergeItem", "clear", "getAllKeys", "numberOfKeys", "length", "keys", "i", "push", "flushGetRequests", "undefined", "multiGet", "map", "multiSet", "keyValuePairs", "item", "multiRemove", "multiMerge"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-async-storage/async-storage/lib/module/AsyncStorage.ts"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n// @ts-ignore Cannot find module 'merge-options' or its corresponding type declarations\nimport mergeOptions from 'merge-options';\nimport type {\n  AsyncStorageStatic,\n  MultiCallback,\n  MultiGetCallback,\n} from './types';\n\nconst merge = mergeOptions.bind({\n  concatArrays: true,\n  ignoreUndefined: true,\n});\n\nfunction mergeLocalStorageItem(key: string, value: string) {\n  const oldValue = window.localStorage.getItem(key);\n  if (oldValue) {\n    const oldObject = JSON.parse(oldValue);\n    const newObject = JSON.parse(value);\n    const nextValue = JSON.stringify(merge(oldObject, newObject));\n    window.localStorage.setItem(key, nextValue);\n  } else {\n    window.localStorage.setItem(key, value);\n  }\n}\n\nfunction createPromise<Result, Callback extends Function>(\n  getValue: () => Result,\n  callback?: Callback\n): Promise<Result> {\n  return new Promise((resolve, reject) => {\n    try {\n      const value = getValue();\n      callback?.(null, value);\n      resolve(value);\n    } catch (err) {\n      callback?.(err);\n      reject(err);\n    }\n  });\n}\n\nfunction createPromiseAll<ReturnType, Result, ResultProcessor extends Function>(\n  promises: Promise<Result>[],\n  callback?: MultiCallback | MultiGetCallback,\n  processResult?: ResultProcessor\n): Promise<ReturnType> {\n  return Promise.all(promises).then(\n    (result) => {\n      const value = processResult?.(result) ?? null;\n      callback?.(null, value);\n      return Promise.resolve(value);\n    },\n    (errors) => {\n      callback?.(errors);\n      return Promise.reject(errors);\n    }\n  );\n}\n\nconst AsyncStorage: AsyncStorageStatic = {\n  /**\n   * Fetches `key` value.\n   */\n  getItem: (key, callback) => {\n    return createPromise(() => window.localStorage.getItem(key), callback);\n  },\n\n  /**\n   * Sets `value` for `key`.\n   */\n  setItem: (key, value, callback) => {\n    return createPromise(\n      () => window.localStorage.setItem(key, value),\n      callback\n    );\n  },\n\n  /**\n   * Removes a `key`\n   */\n  removeItem: (key, callback) => {\n    return createPromise(() => window.localStorage.removeItem(key), callback);\n  },\n\n  /**\n   * Merges existing value with input value, assuming they are stringified JSON.\n   */\n  mergeItem: (key, value, callback) => {\n    return createPromise(() => mergeLocalStorageItem(key, value), callback);\n  },\n\n  /**\n   * Erases *all* AsyncStorage for the domain.\n   */\n  clear: (callback) => {\n    return createPromise(() => window.localStorage.clear(), callback);\n  },\n\n  /**\n   * Gets *all* keys known to the app, for all callers, libraries, etc.\n   */\n  getAllKeys: (callback) => {\n    return createPromise(() => {\n      const numberOfKeys = window.localStorage.length;\n      const keys: string[] = [];\n      for (let i = 0; i < numberOfKeys; i += 1) {\n        const key = window.localStorage.key(i) || '';\n        keys.push(key);\n      }\n      return keys;\n    }, callback);\n  },\n\n  /**\n   * (stub) Flushes any pending requests using a single batch call to get the data.\n   */\n  flushGetRequests: () => undefined,\n\n  /**\n   * multiGet resolves to an array of key-value pair arrays that matches the\n   * input format of multiSet.\n   *\n   *   multiGet(['k1', 'k2']) -> [['k1', 'val1'], ['k2', 'val2']]\n   */\n  multiGet: (keys, callback) => {\n    const promises = keys.map((key) => AsyncStorage.getItem(key));\n    const processResult = (result: string[]) =>\n      result.map((value, i) => [keys[i], value]);\n    return createPromiseAll(promises, callback, processResult);\n  },\n\n  /**\n   * Takes an array of key-value array pairs.\n   *   multiSet([['k1', 'val1'], ['k2', 'val2']])\n   */\n  multiSet: (keyValuePairs, callback) => {\n    const promises = keyValuePairs.map((item) =>\n      AsyncStorage.setItem(item[0], item[1])\n    );\n    return createPromiseAll(promises, callback);\n  },\n\n  /**\n   * Delete all the keys in the `keys` array.\n   */\n  multiRemove: (keys, callback) => {\n    const promises = keys.map((key) => AsyncStorage.removeItem(key));\n    return createPromiseAll(promises, callback);\n  },\n\n  /**\n   * Takes an array of key-value array pairs and merges them with existing\n   * values, assuming they are stringified JSON.\n   *\n   *   multiMerge([['k1', 'val1'], ['k2', 'val2']])\n   */\n  multiMerge: (keyValuePairs, callback) => {\n    const promises = keyValuePairs.map((item) =>\n      AsyncStorage.mergeItem(item[0], item[1])\n    );\n    return createPromiseAll(promises, callback);\n  },\n};\n\nexport default AsyncStorage;\n"], "mappings": "AASA,OAAOA,YAAP,MAAyB,eAAzB;AAOA,IAAMC,KAAK,GAAGD,YAAY,CAACE,IAAb,CAAkB;EAC9BC,YAAY,EAAE,IADgB;EAE9BC,eAAe,EAAE;AAFa,CAAlB,CAAd;AAKA,SAASC,qBAATA,CAA+BC,GAA/B,EAA4CC,KAA5C,EAA2D;EACzD,IAAMC,QAAQ,GAAGC,MAAM,CAACC,YAAP,CAAoBC,OAApB,CAA4BL,GAA5B,CAAjB;EACA,IAAIE,QAAJ,EAAc;IACZ,IAAMI,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWN,QAAX,CAAlB;IACA,IAAMO,SAAS,GAAGF,IAAI,CAACC,KAAL,CAAWP,KAAX,CAAlB;IACA,IAAMS,SAAS,GAAGH,IAAI,CAACI,SAAL,CAAehB,KAAK,CAACW,SAAD,EAAYG,SAAZ,CAApB,CAAlB;IACAN,MAAM,CAACC,YAAP,CAAoBQ,OAApB,CAA4BZ,GAA5B,EAAiCU,SAAjC;EACD,CALD,MAKO;IACLP,MAAM,CAACC,YAAP,CAAoBQ,OAApB,CAA4BZ,GAA5B,EAAiCC,KAAjC;EACD;AACF;AAED,SAASY,aAATA,CACEC,QADF,EAEEC,QAFF,EAGmB;EACjB,OAAO,IAAIC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;IACtC,IAAI;MACF,IAAMjB,KAAK,GAAGa,QAAQ,EAAtB;MACAC,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAG,IAAH,EAASd,KAAT,CAAR;MACAgB,OAAO,CAAChB,KAAD,CAAP;IACD,CAJD,CAIE,OAAOkB,GAAP,EAAY;MACZJ,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAGI,GAAH,CAAR;MACAD,MAAM,CAACC,GAAD,CAAN;IACD;EACF,CATM,CAAP;AAUD;AAED,SAASC,gBAATA,CACEC,QADF,EAEEN,QAFF,EAGEO,aAHF,EAIuB;EACrB,OAAON,OAAO,CAACO,GAAR,CAAYF,QAAZ,EAAsBG,IAAtB,CACJ,UAAAC,MAAD,EAAY;IAAA,IAAAC,IAAA;IACV,IAAMzB,KAAK,IAAAyB,IAAA,GAAGJ,aAAa,SAAb,IAAAA,aAAa,WAAb,YAAAA,aAAa,CAAGG,MAAH,CAAb,YAAAC,IAAA,GAA2B,IAAzC;IACAX,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAG,IAAH,EAASd,KAAT,CAAR;IACA,OAAOe,OAAO,CAACC,OAAR,CAAgBhB,KAAhB,CAAP;EACD,CALI,EAMJ,UAAA0B,MAAD,EAAY;IACVZ,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAGY,MAAH,CAAR;IACA,OAAOX,OAAO,CAACE,MAAR,CAAeS,MAAf,CAAP;EACD,CATI,CAAP;AAWD;AAED,IAAMC,YAAgC,GAAG;EAIvCvB,OAAO,EAAE,SAATA,OAAOA,CAAGL,GAAD,EAAMe,QAAN,EAAmB;IAC1B,OAAOF,aAAa,CAAC;MAAA,OAAMV,MAAM,CAACC,YAAP,CAAoBC,OAApB,CAA4BL,GAA5B,CAAP;IAAA,GAAyCe,QAAzC,CAApB;EACD,CANsC;EAWvCH,OAAO,EAAE,SAATA,OAAOA,CAAGZ,GAAD,EAAMC,KAAN,EAAac,QAAb,EAA0B;IACjC,OAAOF,aAAa,CAClB;MAAA,OAAMV,MAAM,CAACC,YAAP,CAAoBQ,OAApB,CAA4BZ,GAA5B,EAAiCC,KAAjC,CADY;IAAA,GAElBc,QAFkB,CAApB;EAID,CAhBsC;EAqBvCc,UAAU,EAAE,SAAZA,UAAUA,CAAG7B,GAAD,EAAMe,QAAN,EAAmB;IAC7B,OAAOF,aAAa,CAAC;MAAA,OAAMV,MAAM,CAACC,YAAP,CAAoByB,UAApB,CAA+B7B,GAA/B,CAAP;IAAA,GAA4Ce,QAA5C,CAApB;EACD,CAvBsC;EA4BvCe,SAAS,EAAE,SAAXA,SAASA,CAAG9B,GAAD,EAAMC,KAAN,EAAac,QAAb,EAA0B;IACnC,OAAOF,aAAa,CAAC;MAAA,OAAMd,qBAAqB,CAACC,GAAD,EAAMC,KAAN,CAA5B;IAAA,GAA0Cc,QAA1C,CAApB;EACD,CA9BsC;EAmCvCgB,KAAK,EAAG,SAARA,KAAKA,CAAGhB,QAAD,EAAc;IACnB,OAAOF,aAAa,CAAC;MAAA,OAAMV,MAAM,CAACC,YAAP,CAAoB2B,KAApB,EAAP;IAAA,GAAoChB,QAApC,CAApB;EACD,CArCsC;EA0CvCiB,UAAU,EAAG,SAAbA,UAAUA,CAAGjB,QAAD,EAAc;IACxB,OAAOF,aAAa,CAAC,YAAM;MACzB,IAAMoB,YAAY,GAAG9B,MAAM,CAACC,YAAP,CAAoB8B,MAAzC;MACA,IAAMC,IAAc,GAAG,EAAvB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,YAApB,EAAkCG,CAAC,IAAI,CAAvC,EAA0C;QACxC,IAAMpC,GAAG,GAAGG,MAAM,CAACC,YAAP,CAAoBJ,GAApB,CAAwBoC,CAAxB,KAA8B,EAA1C;QACAD,IAAI,CAACE,IAAL,CAAUrC,GAAV;MACD;MACD,OAAOmC,IAAP;IACD,CARmB,EAQjBpB,QARiB,CAApB;EASD,CApDsC;EAyDvCuB,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA;IAAA,OAAQC,SAzDe;EAAA;EAiEvCC,QAAQ,EAAE,SAAVA,QAAQA,CAAGL,IAAD,EAAOpB,QAAP,EAAoB;IAC5B,IAAMM,QAAQ,GAAGc,IAAI,CAACM,GAAL,CAAU,UAAAzC,GAAD;MAAA,OAAS4B,YAAY,CAACvB,OAAb,CAAqBL,GAArB,CAAlB;IAAA,EAAjB;IACA,IAAMsB,aAAa,GAAI,SAAjBA,aAAaA,CAAIG,MAAD;MAAA,OACpBA,MAAM,CAACgB,GAAP,CAAW,UAACxC,KAAD,EAAQmC,CAAR;QAAA,OAAc,CAACD,IAAI,CAACC,CAAD,CAAL,EAAUnC,KAAV,CAAzB;MAAA,EADF;IAAA;IAEA,OAAOmB,gBAAgB,CAACC,QAAD,EAAWN,QAAX,EAAqBO,aAArB,CAAvB;EACD,CAtEsC;EA4EvCoB,QAAQ,EAAE,SAAVA,QAAQA,CAAGC,aAAD,EAAgB5B,QAAhB,EAA6B;IACrC,IAAMM,QAAQ,GAAGsB,aAAa,CAACF,GAAd,CAAmB,UAAAG,IAAD;MAAA,OACjChB,YAAY,CAAChB,OAAb,CAAqBgC,IAAI,CAAC,CAAD,CAAzB,EAA8BA,IAAI,CAAC,CAAD,CAAlC,CADe;IAAA,EAAjB;IAGA,OAAOxB,gBAAgB,CAACC,QAAD,EAAWN,QAAX,CAAvB;EACD,CAjFsC;EAsFvC8B,WAAW,EAAE,SAAbA,WAAWA,CAAGV,IAAD,EAAOpB,QAAP,EAAoB;IAC/B,IAAMM,QAAQ,GAAGc,IAAI,CAACM,GAAL,CAAU,UAAAzC,GAAD;MAAA,OAAS4B,YAAY,CAACC,UAAb,CAAwB7B,GAAxB,CAAlB;IAAA,EAAjB;IACA,OAAOoB,gBAAgB,CAACC,QAAD,EAAWN,QAAX,CAAvB;EACD,CAzFsC;EAiGvC+B,UAAU,EAAE,SAAZA,UAAUA,CAAGH,aAAD,EAAgB5B,QAAhB,EAA6B;IACvC,IAAMM,QAAQ,GAAGsB,aAAa,CAACF,GAAd,CAAmB,UAAAG,IAAD;MAAA,OACjChB,YAAY,CAACE,SAAb,CAAuBc,IAAI,CAAC,CAAD,CAA3B,EAAgCA,IAAI,CAAC,CAAD,CAApC,CADe;IAAA,EAAjB;IAGA,OAAOxB,gBAAgB,CAACC,QAAD,EAAWN,QAAX,CAAvB;EACD;AAtGsC,CAAzC;AAyGA,eAAea,YAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}