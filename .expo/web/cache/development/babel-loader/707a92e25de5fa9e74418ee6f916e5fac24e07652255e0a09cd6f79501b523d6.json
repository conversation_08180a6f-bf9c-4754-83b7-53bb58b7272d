{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"alpha\", \"family\", \"style\", \"theme\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nimport Text from \"./Text\";\nimport { useInternalTheme } from \"../../../core/theming\";\nvar StyledText = function StyledText(_ref) {\n  var _theme$colors, _theme$fonts;\n  var _ref$alpha = _ref.alpha,\n    alpha = _ref$alpha === void 0 ? 1 : _ref$alpha,\n    family = _ref.family,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var textColor = color(theme.isV3 ? theme.colors.onSurface : (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.text).alpha(alpha).rgb().string();\n  var writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n  return React.createElement(Text, _extends({}, rest, {\n    style: [styles.text, _objectSpread(_objectSpread({\n      color: textColor\n    }, !theme.isV3 && ((_theme$fonts = theme.fonts) === null || _theme$fonts === void 0 ? void 0 : _theme$fonts[family])), {}, {\n      writingDirection: writingDirection\n    }), style]\n  }));\n};\nvar styles = StyleSheet.create({\n  text: {\n    textAlign: 'left'\n  }\n});\nexport default StyledText;", "map": {"version": 3, "names": ["React", "I18nManager", "StyleSheet", "color", "Text", "useInternalTheme", "StyledText", "_ref", "_theme$colors", "_theme$fonts", "_ref$alpha", "alpha", "family", "style", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "textColor", "isV3", "colors", "onSurface", "text", "rgb", "string", "writingDirection", "getConstants", "isRTL", "createElement", "_extends", "styles", "_objectSpread", "fonts", "create", "textAlign"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Typography/v2/StyledText.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { I18nManager, StyleProp, StyleSheet, TextStyle } from 'react-native';\n\nimport color from 'color';\nimport type { ThemeProp } from 'src/types';\n\nimport Text from './Text';\nimport { useInternalTheme } from '../../../core/theming';\n\ntype Props = React.ComponentProps<typeof Text> & {\n  alpha?: number;\n  family: 'regular' | 'medium' | 'light' | 'thin';\n  style?: StyleProp<TextStyle>;\n  theme?: ThemeProp;\n};\n\nconst StyledText = ({\n  alpha = 1,\n  family,\n  style,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  const textColor = color(\n    theme.isV3 ? theme.colors.onSurface : theme.colors?.text\n  )\n    .alpha(alpha)\n    .rgb()\n    .string();\n  const writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n\n  return (\n    <Text\n      {...rest}\n      style={[\n        styles.text,\n        {\n          color: textColor,\n          ...(!theme.isV3 && theme.fonts?.[family]),\n          writingDirection,\n        },\n        style,\n      ]}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  text: {\n    textAlign: 'left',\n  },\n});\n\nexport default StyledText;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,UAAA;AAG9B,OAAOC,KAAK,MAAM,OAAO;AAGzB,OAAOC,IAAI;AACX,SAASC,gBAAgB;AASzB,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAGC,IAAA,EAMN;EAAA,IAAAC,aAAA,EAAAC,YAAA;EAAA,IAAAC,UAAA,GAALH,IAAA,CALNI,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,GAAAA,UAAA;IACTE,MAAM,GAIAL,IAAA,CAJNK,MAAM;IACNC,KAAK,GAGCN,IAAA,CAHNM,KAAK;IACEC,cAAc,GAEfP,IAAA,CAFNQ,KAAK;IACFC,IAAA,GAAAC,wBAAA,CACGV,IAAA,EAAAW,SAAA;EACN,IAAMH,KAAK,GAAGV,gBAAgB,CAACS,cAAc,CAAC;EAE9C,IAAMK,SAAS,GAAGhB,KAAK,CACrBY,KAAK,CAACK,IAAI,GAAGL,KAAK,CAACM,MAAM,CAACC,SAAS,IAAAd,aAAA,GAAGO,KAAK,CAACM,MAAM,cAAAb,aAAA,uBAAZA,aAAA,CAAce,IACtD,CAAC,CACEZ,KAAK,CAACA,KAAK,CAAC,CACZa,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;EACX,IAAMC,gBAAgB,GAAGzB,WAAW,CAAC0B,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE,OACE5B,KAAA,CAAA6B,aAAA,CAACzB,IAAI,EAAA0B,QAAA,KACCd,IAAI;IACRH,KAAK,EAAE,CACLkB,MAAM,CAACR,IAAI,EAAAS,aAAA,CAAAA,aAAA;MAET7B,KAAK,EAAEgB;IAAS,GACZ,CAACJ,KAAK,CAACK,IAAI,MAAAX,YAAA,GAAIM,KAAK,CAACkB,KAAK,cAAAxB,YAAA,uBAAXA,YAAA,CAAcG,MAAM,CAAC;MACxCc,gBAAA,EAAAA;IAAA,IAEFb,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,IAAMkB,MAAM,GAAG7B,UAAU,CAACgC,MAAM,CAAC;EAC/BX,IAAI,EAAE;IACJY,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAe7B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}