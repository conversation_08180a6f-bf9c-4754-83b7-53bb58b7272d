{"ast": null, "code": "export var AppOwnership;\n(function (AppOwnership) {\n  AppOwnership[\"Standalone\"] = \"standalone\";\n  AppOwnership[\"Expo\"] = \"expo\";\n  AppOwnership[\"Guest\"] = \"guest\";\n})(AppOwnership || (AppOwnership = {}));\nexport var ExecutionEnvironment;\n(function (ExecutionEnvironment) {\n  ExecutionEnvironment[\"Bare\"] = \"bare\";\n  ExecutionEnvironment[\"Standalone\"] = \"standalone\";\n  ExecutionEnvironment[\"StoreClient\"] = \"storeClient\";\n})(ExecutionEnvironment || (ExecutionEnvironment = {}));\nexport var UserInterfaceIdiom;\n(function (UserInterfaceIdiom) {\n  UserInterfaceIdiom[\"Handset\"] = \"handset\";\n  UserInterfaceIdiom[\"Tablet\"] = \"tablet\";\n  UserInterfaceIdiom[\"Unsupported\"] = \"unsupported\";\n})(UserInterfaceIdiom || (UserInterfaceIdiom = {}));", "map": {"version": 3, "names": ["AppOwnership", "ExecutionEnvironment", "UserInterfaceIdiom"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-constants/src/Constants.types.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\n// @needsAudit\nexport enum AppOwnership {\n  /**\n   * It is a [standalone app](/classic/building-standalone-apps#building-standalone-apps).\n   */\n  Standalone = 'standalone',\n  /**\n   * The experience is running inside of the Expo Go app.\n   */\n  Expo = 'expo',\n  /**\n   * It has been opened through a link from a standalone app.\n   */\n  Guest = 'guest',\n}\n\n// @docsMissing\nexport enum ExecutionEnvironment {\n  Bare = 'bare',\n  Standalone = 'standalone',\n  StoreClient = 'storeClient',\n}\n\n// @needsAudit\n/**\n * Current supported values are `handset` and `tablet`. Apple TV and CarPlay will show up\n * as `unsupported`.\n */\nexport enum UserInterfaceIdiom {\n  Handset = 'handset',\n  Tablet = 'tablet',\n  Unsupported = 'unsupported',\n}\n\n// @needsAudit\nexport interface IOSManifest {\n  /**\n   * The build number specified in the embedded **Info.plist** value for `CFBundleVersion` in this app.\n   * In a standalone app, you can set this with the `ios.buildNumber` value in **app.json**. This\n   * may differ from the value in `Constants.expoConfig.ios.buildNumber` because the manifest\n   * can be updated, whereas this value will never change for a given native binary.\n   * The value is set to `null` in case you run your app in Expo Go.\n   */\n  buildNumber: string | null;\n  /**\n   * The Apple internal model identifier for this device, e.g. `iPhone1,1`.\n   * @deprecated Use `expo-device`'s [`Device.modelId`](./device/#devicemodelid).\n   */\n  platform: string;\n  /**\n   * The human-readable model name of this device, e.g. `\"iPhone 7 Plus\"` if it can be determined,\n   * otherwise will be `null`.\n   * @deprecated Moved to `expo-device` as [`Device.modelName`](./device/#devicemodelname).\n   */\n  model: string | null;\n  /**\n   * The user interface idiom of this device, i.e. whether the app is running on an iPhone or an iPad.\n   * @deprecated Use `expo-device`'s [`Device.getDeviceTypeAsync()`](./device/#devicegetdevicetypeasync).\n   */\n  userInterfaceIdiom: UserInterfaceIdiom;\n  /**\n   * The version of iOS running on this device, e.g. `10.3`.\n   * @deprecated Use `expo-device`'s [`Device.osVersion`](./device/#deviceosversion).\n   */\n  systemVersion: string;\n  [key: string]: any;\n}\n\n// @needsAudit\nexport interface AndroidManifest {\n  /**\n   * The version code set by `android.versionCode` in app.json.\n   * The value is set to `null` in case you run your app in Expo Go.\n   * @deprecated Use `expo-application`'s [`Application.nativeBuildVersion`](./application/#applicationnativebuildversion).\n   */\n  versionCode: number;\n  [key: string]: any;\n}\n\nexport interface WebManifest {\n  [key: string]: any;\n}\n\n// @docsMissing\nexport interface ManifestAsset {\n  url: string;\n}\n\n// @needsAudit @docsMissing\n/**\n * A modern manifest.\n */\nexport type Manifest = {\n  id: string;\n  createdAt: string;\n  runtimeVersion: string;\n  launchAsset: ManifestAsset;\n  assets: ManifestAsset[];\n  metadata: object;\n  extra?: ManifestExtra;\n};\n\n// @docsMissing\nexport type ManifestExtra = ClientScopingConfig & {\n  expoClient?: ExpoConfig & {\n    /**\n     * Only present during development using @expo/cli.\n     */\n    hostUri?: string;\n  };\n  expoGo?: ExpoGoConfig;\n  eas?: EASConfig;\n};\n\n// @needsAudit\nexport type EASConfig = {\n  /**\n   * The ID for this project if it's using EAS. UUID. This value will not change when a project is\n   * transferred between accounts or renamed.\n   */\n  projectId?: string;\n};\n\n// @needsAudit\nexport type ClientScopingConfig = {\n  /**\n   * An opaque unique string for scoping client-side data to this project. This value\n   * will not change when a project is transferred between accounts or renamed.\n   */\n  scopeKey?: string;\n};\n\n// @docsMissing\nexport type ExpoGoConfig = {\n  mainModuleName?: string;\n  debuggerHost?: string;\n  logUrl?: string;\n  developer?: {\n    tool?: string;\n    [key: string]: any;\n  };\n  packagerOpts?: ExpoGoPackagerOpts;\n};\n\n// @docsMissing\nexport type ExpoGoPackagerOpts = {\n  hostType?: string;\n  dev?: boolean;\n  strict?: boolean;\n  minify?: boolean;\n  urlType?: string;\n  urlRandomness?: string;\n  lanType?: string;\n  [key: string]: any;\n};\n\nexport type ExpoClientConfig = ExpoConfig & {\n  /**\n   * Published apps only.\n   */\n  releaseId?: string;\n  revisionId?: string;\n  releaseChannel?: string;\n  bundleUrl: string;\n  hostUri?: string;\n  publishedTime?: string;\n  /**\n   * The Expo account name and slug for this project.\n   * @deprecated Prefer `projectId` or `originalFullName` instead for identification and\n   * `scopeKey` for scoping due to immutability.\n   */\n  id?: string;\n  /**\n   * The original Expo account name and slug for this project. Formatted like `@username/slug`.\n   * When unauthenticated, the username is `@anonymous`. For published projects, this value\n   * will not change when a project is transferred between accounts or renamed.\n   */\n  originalFullName?: string;\n  /**\n   * The Expo account name and slug used for display purposes. Formatted like `@username/slug`.\n   * When unauthenticated, the username is `@anonymous`. For published projects, this value\n   * may change when a project is transferred between accounts or renamed.\n   */\n  currentFullName?: string;\n};\n\n/**\n * Represents an intersection of all possible Config types.\n */\nexport type AppManifest = ExpoClientConfig &\n  ExpoGoConfig &\n  EASConfig &\n  ClientScopingConfig &\n  Record<string, any>;\n\n// @needsAudit @docsMissing\nexport interface PlatformManifest {\n  ios?: IOSManifest;\n  android?: AndroidManifest;\n  web?: WebManifest;\n  detach?: {\n    scheme?: string;\n    [key: string]: any;\n  };\n  logUrl?: string;\n  scheme?: string;\n  hostUri?: string;\n  developer?: string;\n  [key: string]: any;\n}\n\n// @needsAudit @docsMissing\nexport interface NativeConstants {\n  /**\n   * @hidden\n   */\n  name: 'ExponentConstants';\n  /**\n   * Returns `expo`, `standalone`, or `guest`. This property only applies to the managed workflow\n   * and classic builds; for apps built with EAS Build and in bare workflow, the result is\n   * always `null`.\n   */\n  appOwnership: AppOwnership | null;\n  debugMode: boolean;\n  /**\n   * A human-readable name for the device type.\n   */\n  deviceName?: string;\n  /**\n   * The [device year class](https://github.com/facebook/device-year-class) of this device.\n   * @deprecated Moved to `expo-device` as [`Device.deviceYearClass`](./device/#deviceyearclass).\n   */\n  deviceYearClass: number | null;\n  executionEnvironment: ExecutionEnvironment;\n  experienceUrl: string;\n  // only nullable on web\n  expoRuntimeVersion: string | null;\n  /**\n   * The version string of the Expo Go app currently running.\n   * Returns `null` in bare workflow and web.\n   */\n  expoVersion: string | null;\n  isDetached?: boolean;\n  intentUri?: string;\n  /**\n   * An identifier that is unique to this particular device and whose lifetime is at least as long\n   * as the installation of the app.\n   * @deprecated `Constants.installationId` is deprecated in favor of generating your own ID and\n   * storing it.\n   */\n  installationId: string;\n  /**\n   * `true` if the app is running on a device, `false` if running in a simulator or emulator.\n   * @deprecated Use `expo-device`'s [`Device.isDevice`](./device/#deviceisdevice).\n   */\n  isDevice: boolean;\n  isHeadless: boolean;\n  linkingUri: string;\n  /**\n   * The **Info.plist** value for `CFBundleShortVersionString` on iOS and the version name set\n   * by `version` in app.json on Android at the time the native app was built.\n   * @deprecated Use `expo-application`'s [`Application.nativeApplicationVersion`](./application/#applicationnativeapplicationversion).\n   */\n  nativeAppVersion: string | null;\n  /**\n   * The **Info.plist** value for `CFBundleVersion` on iOS (set with `ios.buildNumber` value in\n   * **app.json** in a standalone app) and the version code set by `android.versionCode` in\n   * **app.json** on Android at the time the native app was built.\n   * @deprecated Use `expo-application`'s [`Application.nativeBuildVersion`](./application/#applicationnativebuildversion).\n   */\n  nativeBuildVersion: string | null;\n  /**\n   * Classic manifest for Expo apps using classic updates and the updates embedded in builds.\n   * Returns `null` in bare workflow and when `manifest2` is non-null.\n   * @deprecated Use `Constants.expoConfig` instead, which behaves more consistently across EAS Build\n   * and EAS Update.\n   */\n  manifest: AppManifest | null;\n  /**\n   * Manifest for Expo apps using modern Expo Updates from a remote source, such as apps that\n   * use EAS Update. Returns `null` in bare workflow and when `manifest` is non-null.\n   * `Constants.expoConfig` should be used for accessing the Expo config object.\n   */\n  manifest2: Manifest | null;\n  /**\n   * The standard Expo config object defined in `app.json` and `app.config.js` files. For both\n   * classic and modern manifests, whether they are embedded or remote.\n   */\n  expoConfig:\n    | (ExpoConfig & {\n        /**\n         * Only present during development using @expo/cli.\n         */\n        hostUri?: string;\n      })\n    | null;\n  /**\n   * The standard Expo Go config object populated when running in Expo Go.\n   */\n  expoGoConfig: ExpoGoConfig | null;\n  /**\n   * The standard EAS config object populated when using EAS.\n   */\n  easConfig: EASConfig | null;\n  /**\n   * A string that is unique to the current session of your app. It is different across apps and\n   * across multiple launches of the same app.\n   */\n  sessionId: string;\n  /**\n   * The default status bar height for the device. Does not factor in changes when location tracking\n   * is in use or a phone call is active.\n   */\n  statusBarHeight: number;\n  /**\n   * A list of the system font names available on the current device.\n   */\n  systemFonts: string[];\n  systemVersion?: number;\n  /**\n   * @hidden\n   */\n  supportedExpoSdks?: string[];\n  platform?: PlatformManifest;\n  /**\n   * Gets the user agent string which would be included in requests sent by a web view running on\n   * this device. This is probably not the same user agent you might be providing in your JS `fetch`\n   * requests.\n   */\n  getWebViewUserAgentAsync: () => Promise<string | null>;\n  [key: string]: any;\n}\n\nexport interface Constants extends NativeConstants {\n  /**\n   * @hidden\n   * @warning do not use this property. Use `manifest` by default.\n   *\n   * In certain cases accessing manifest via this property\n   * suppresses important warning about missing manifest.\n   */\n  __unsafeNoWarnManifest?: AppManifest;\n  /**\n   * @hidden\n   * @warning do not use this property. Use `manifest2` by default.\n   *\n   * In certain cases accessing manifest via this property\n   * suppresses important warning about missing manifest.\n   */\n  __unsafeNoWarnManifest2?: Manifest;\n}\n"], "mappings": "AAGA,WAAYA,YAaX;AAbD,WAAYA,YAAY;EAItBA,YAAA,6BAAyB;EAIzBA,YAAA,iBAAa;EAIbA,YAAA,mBAAe;AACjB,CAAC,EAbWA,YAAY,KAAZA,YAAY;AAgBxB,WAAYC,oBAIX;AAJD,WAAYA,oBAAoB;EAC9BA,oBAAA,iBAAa;EACbA,oBAAA,6BAAyB;EACzBA,oBAAA,+BAA2B;AAC7B,CAAC,EAJWA,oBAAoB,KAApBA,oBAAoB;AAWhC,WAAYC,kBAIX;AAJD,WAAYA,kBAAkB;EAC5BA,kBAAA,uBAAmB;EACnBA,kBAAA,qBAAiB;EACjBA,kBAAA,+BAA2B;AAC7B,CAAC,EAJWA,kBAAkB,KAAlBA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}