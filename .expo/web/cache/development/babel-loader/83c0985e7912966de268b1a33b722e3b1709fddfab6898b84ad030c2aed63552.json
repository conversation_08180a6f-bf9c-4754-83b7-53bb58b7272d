{"ast": null, "code": "export var THEMES = {\n  dark: {\n    background: '#121212',\n    surface: '#1E1E1E',\n    primary: '#4CAF50',\n    accent: '#03DAC6',\n    error: '#CF6679',\n    warning: '#FF9800',\n    info: '#2196F3',\n    success: '#4CAF50',\n    text: '#FFFFFF',\n    textSecondary: '#B0B0B0',\n    textDisabled: '#666666',\n    border: '#2C2C2C',\n    divider: '#2C2C2C',\n    cardBackground: '#2C2C2C',\n    headerBackground: '#121212',\n    buttonBackground: '#333333',\n    inputBackground: '#333333',\n    statusBar: 'light-content',\n    icon: '#FFFFFF',\n    summaryBackground: '#262626',\n    profileButton: '#388E3C',\n    manualEntryHeader: '#333333',\n    manualEntrySubheader: '#FF9800',\n    userProfileHeader: '#121212',\n    settingsItemBackground: '#2C2C2C',\n    activeOpacity: 0.9,\n    disabledOpacity: 0.3,\n    shadowColor: '#000000',\n    shadowOpacity: 0.3\n  },\n  light: {\n    background: '#F5F5F5',\n    surface: '#FFFFFF',\n    primary: '#4CAF50',\n    accent: '#03DAC6',\n    error: '#F44336',\n    warning: '#FF9800',\n    info: '#2196F3',\n    success: '#4CAF50',\n    text: '#212121',\n    textSecondary: '#757575',\n    textDisabled: '#9E9E9E',\n    border: '#E0E0E0',\n    divider: '#E0E0E0',\n    cardBackground: '#FFFFFF',\n    headerBackground: '#4CAF50',\n    buttonBackground: '#FFFFFF',\n    inputBackground: '#FFFFFF',\n    statusBar: 'dark-content',\n    icon: '#4CAF50',\n    summaryBackground: '#FFFFFF',\n    profileButton: '#4CAF50',\n    manualEntryHeader: '#4CAF50',\n    manualEntrySubheader: '#FF9800',\n    userProfileHeader: '#4CAF50',\n    settingsItemBackground: '#FFFFFF',\n    activeOpacity: 1,\n    disabledOpacity: 0.5,\n    shadowColor: '#000000',\n    shadowOpacity: 0.1\n  }\n};\nexport var TYPOGRAPHY = {\n  h1: {\n    fontSize: 32,\n    fontWeight: 'bold',\n    lineHeight: 40\n  },\n  h2: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    lineHeight: 32\n  },\n  h3: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    lineHeight: 28\n  },\n  h4: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    lineHeight: 24\n  },\n  subtitle1: {\n    fontSize: 16,\n    fontWeight: '500',\n    lineHeight: 24\n  },\n  subtitle2: {\n    fontSize: 14,\n    fontWeight: '500',\n    lineHeight: 20\n  },\n  body1: {\n    fontSize: 16,\n    fontWeight: 'normal',\n    lineHeight: 24\n  },\n  body2: {\n    fontSize: 14,\n    fontWeight: 'normal',\n    lineHeight: 20\n  },\n  button: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    lineHeight: 24,\n    textTransform: 'uppercase'\n  },\n  caption: {\n    fontSize: 12,\n    fontWeight: 'normal',\n    lineHeight: 16\n  },\n  overline: {\n    fontSize: 10,\n    fontWeight: '500',\n    lineHeight: 16,\n    textTransform: 'uppercase',\n    letterSpacing: 1.5\n  }\n};\nexport var SPACING = {\n  xs: 4,\n  sm: 8,\n  md: 16,\n  lg: 24,\n  xl: 32,\n  xxl: 48\n};\nexport var BORDER_RADIUS = {\n  xs: 4,\n  sm: 8,\n  md: 12,\n  lg: 16,\n  xl: 24,\n  round: 9999\n};\nexport var SHADOWS = {\n  small: {\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowRadius: 3,\n    elevation: 2\n  },\n  medium: {\n    shadowOffset: {\n      width: 0,\n      height: 3\n    },\n    shadowRadius: 6,\n    elevation: 4\n  },\n  large: {\n    shadowOffset: {\n      width: 0,\n      height: 6\n    },\n    shadowRadius: 10,\n    elevation: 8\n  }\n};\nexport var DEFAULT_THEME = 'dark';\nexport var getThemeColors = function getThemeColors(themeName) {\n  return THEMES[themeName] || THEMES[DEFAULT_THEME];\n};\nexport var createThemedStyle = function createThemedStyle(baseStyle, themeColors) {\n  var themedStyle = {};\n  Object.keys(baseStyle).forEach(function (key) {\n    var value = baseStyle[key];\n    if (typeof value === 'string' && value.startsWith('$')) {\n      var colorKey = value.substring(1);\n      themedStyle[key] = themeColors[colorKey] || value;\n    } else {\n      themedStyle[key] = value;\n    }\n  });\n  return themedStyle;\n};", "map": {"version": 3, "names": ["THEMES", "dark", "background", "surface", "primary", "accent", "error", "warning", "info", "success", "text", "textSecondary", "textDisabled", "border", "divider", "cardBackground", "headerBackground", "buttonBackground", "inputBackground", "statusBar", "icon", "summaryBackground", "profile<PERSON><PERSON>on", "manualEntryHeader", "manualEntrySubheader", "userProfileHeader", "settingsItemBackground", "activeOpacity", "disabledOpacity", "shadowColor", "shadowOpacity", "light", "TYPOGRAPHY", "h1", "fontSize", "fontWeight", "lineHeight", "h2", "h3", "h4", "subtitle1", "subtitle2", "body1", "body2", "button", "textTransform", "caption", "overline", "letterSpacing", "SPACING", "xs", "sm", "md", "lg", "xl", "xxl", "BORDER_RADIUS", "round", "SHADOWS", "small", "shadowOffset", "width", "height", "shadowRadius", "elevation", "medium", "large", "DEFAULT_THEME", "getThemeColors", "themeName", "createThemedStyle", "baseStyle", "themeColors", "themedStyle", "Object", "keys", "for<PERSON>ach", "key", "value", "startsWith", "colorKey", "substring"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/theme/theme.js"], "sourcesContent": ["/**\n * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Theme Configuration\n * Centralized theme management for consistent styling across the app\n */\n\n// Define theme colors\nexport const THEMES = {\n  dark: {\n    // Base colors\n    background: '#121212',\n    surface: '#1E1E1E',\n    primary: '#4CAF50',\n    accent: '#03DAC6',\n    error: '#CF6679',\n    warning: '#FF9800',\n    info: '#2196F3',\n    success: '#4CAF50',\n\n    // Text colors\n    text: '#FFFFFF',\n    textSecondary: '#B0B0B0',\n    textDisabled: '#666666',\n\n    // UI elements\n    border: '#2C2C2C',\n    divider: '#2C2C2C',\n    cardBackground: '#2C2C2C',\n    headerBackground: '#121212',\n    buttonBackground: '#333333',\n    inputBackground: '#333333',\n    statusBar: 'light-content',\n    icon: '#FFFFFF',\n    summaryBackground: '#262626',\n    profileButton: '#388E3C',\n\n    // Specific sections\n    manualEntryHeader: '#333333',\n    manualEntrySubheader: '#FF9800',\n    userProfileHeader: '#121212',\n    settingsItemBackground: '#2C2C2C',\n\n    // Opacity values for states\n    activeOpacity: 0.9,\n    disabledOpacity: 0.3,\n\n    // Shadows\n    shadowColor: '#000000',\n    shadowOpacity: 0.3\n  },\n  light: {\n    // Base colors\n    background: '#F5F5F5',\n    surface: '#FFFFFF',\n    primary: '#4CAF50',\n    accent: '#03DAC6',\n    error: '#F44336',\n    warning: '#FF9800',\n    info: '#2196F3',\n    success: '#4CAF50',\n\n    // Text colors\n    text: '#212121',\n    textSecondary: '#757575',\n    textDisabled: '#9E9E9E',\n\n    // UI elements\n    border: '#E0E0E0',\n    divider: '#E0E0E0',\n    cardBackground: '#FFFFFF',\n    headerBackground: '#4CAF50',\n    buttonBackground: '#FFFFFF',\n    inputBackground: '#FFFFFF',\n    statusBar: 'dark-content',\n    icon: '#4CAF50',\n    summaryBackground: '#FFFFFF',\n    profileButton: '#4CAF50',\n\n    // Specific sections\n    manualEntryHeader: '#4CAF50',\n    manualEntrySubheader: '#FF9800',\n    userProfileHeader: '#4CAF50',\n    settingsItemBackground: '#FFFFFF',\n\n    // Opacity values for states\n    activeOpacity: 1,\n    disabledOpacity: 0.5,\n\n    // Shadows\n    shadowColor: '#000000',\n    shadowOpacity: 0.1\n  }\n};\n\n// Typography styles\nexport const TYPOGRAPHY = {\n  h1: {\n    fontSize: 32,\n    fontWeight: 'bold',\n    lineHeight: 40,\n  },\n  h2: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    lineHeight: 32,\n  },\n  h3: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    lineHeight: 28,\n  },\n  h4: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    lineHeight: 24,\n  },\n  subtitle1: {\n    fontSize: 16,\n    fontWeight: '500',\n    lineHeight: 24,\n  },\n  subtitle2: {\n    fontSize: 14,\n    fontWeight: '500',\n    lineHeight: 20,\n  },\n  body1: {\n    fontSize: 16,\n    fontWeight: 'normal',\n    lineHeight: 24,\n  },\n  body2: {\n    fontSize: 14,\n    fontWeight: 'normal',\n    lineHeight: 20,\n  },\n  button: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    lineHeight: 24,\n    textTransform: 'uppercase',\n  },\n  caption: {\n    fontSize: 12,\n    fontWeight: 'normal',\n    lineHeight: 16,\n  },\n  overline: {\n    fontSize: 10,\n    fontWeight: '500',\n    lineHeight: 16,\n    textTransform: 'uppercase',\n    letterSpacing: 1.5,\n  },\n};\n\n// Spacing values\nexport const SPACING = {\n  xs: 4,\n  sm: 8,\n  md: 16,\n  lg: 24,\n  xl: 32,\n  xxl: 48,\n};\n\n// Border radius values\nexport const BORDER_RADIUS = {\n  xs: 4,\n  sm: 8,\n  md: 12,\n  lg: 16,\n  xl: 24,\n  round: 9999,\n};\n\n// Shadow styles\nexport const SHADOWS = {\n  small: {\n    shadowOffset: { width: 0, height: 2 },\n    shadowRadius: 3,\n    elevation: 2,\n  },\n  medium: {\n    shadowOffset: { width: 0, height: 3 },\n    shadowRadius: 6,\n    elevation: 4,\n  },\n  large: {\n    shadowOffset: { width: 0, height: 6 },\n    shadowRadius: 10,\n    elevation: 8,\n  },\n};\n\n// Default theme\nexport const DEFAULT_THEME = 'dark';\n\n// Helper function to get current theme colors\nexport const getThemeColors = (themeName) => {\n  // Return the requested theme or default if not found\n  return THEMES[themeName] || THEMES[DEFAULT_THEME];\n};\n\n// Helper function to create combined styles with theme colors\nexport const createThemedStyle = (baseStyle, themeColors) => {\n  const themedStyle = {};\n\n  // Process each style property\n  Object.keys(baseStyle).forEach(key => {\n    const value = baseStyle[key];\n\n    // If the value is a string that starts with '$', it's a theme color reference\n    if (typeof value === 'string' && value.startsWith('$')) {\n      const colorKey = value.substring(1);\n      themedStyle[key] = themeColors[colorKey] || value;\n    } else {\n      themedStyle[key] = value;\n    }\n  });\n\n  return themedStyle;\n};\n"], "mappings": "AAMA,OAAO,IAAMA,MAAM,GAAG;EACpBC,IAAI,EAAE;IAEJC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAGlBC,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBC,YAAY,EAAE,SAAS;IAGvBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,SAAS;IAClBC,cAAc,EAAE,SAAS;IACzBC,gBAAgB,EAAE,SAAS;IAC3BC,gBAAgB,EAAE,SAAS;IAC3BC,eAAe,EAAE,SAAS;IAC1BC,SAAS,EAAE,eAAe;IAC1BC,IAAI,EAAE,SAAS;IACfC,iBAAiB,EAAE,SAAS;IAC5BC,aAAa,EAAE,SAAS;IAGxBC,iBAAiB,EAAE,SAAS;IAC5BC,oBAAoB,EAAE,SAAS;IAC/BC,iBAAiB,EAAE,SAAS;IAC5BC,sBAAsB,EAAE,SAAS;IAGjCC,aAAa,EAAE,GAAG;IAClBC,eAAe,EAAE,GAAG;IAGpBC,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE;EACjB,CAAC;EACDC,KAAK,EAAE;IAEL7B,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAGlBC,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBC,YAAY,EAAE,SAAS;IAGvBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,SAAS;IAClBC,cAAc,EAAE,SAAS;IACzBC,gBAAgB,EAAE,SAAS;IAC3BC,gBAAgB,EAAE,SAAS;IAC3BC,eAAe,EAAE,SAAS;IAC1BC,SAAS,EAAE,cAAc;IACzBC,IAAI,EAAE,SAAS;IACfC,iBAAiB,EAAE,SAAS;IAC5BC,aAAa,EAAE,SAAS;IAGxBC,iBAAiB,EAAE,SAAS;IAC5BC,oBAAoB,EAAE,SAAS;IAC/BC,iBAAiB,EAAE,SAAS;IAC5BC,sBAAsB,EAAE,SAAS;IAGjCC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,GAAG;IAGpBC,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE;EACjB;AACF,CAAC;AAGD,OAAO,IAAME,UAAU,GAAG;EACxBC,EAAE,EAAE;IACFC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE;EACd,CAAC;EACDC,EAAE,EAAE;IACFH,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE;EACd,CAAC;EACDE,EAAE,EAAE;IACFJ,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE;EACd,CAAC;EACDG,EAAE,EAAE;IACFL,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE;EACd,CAAC;EACDI,SAAS,EAAE;IACTN,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE;EACd,CAAC;EACDK,SAAS,EAAE;IACTP,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE;EACd,CAAC;EACDM,KAAK,EAAE;IACLR,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC;EACDO,KAAK,EAAE;IACLT,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC;EACDQ,MAAM,EAAE;IACNV,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,EAAE;IACdS,aAAa,EAAE;EACjB,CAAC;EACDC,OAAO,EAAE;IACPZ,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC;EACDW,QAAQ,EAAE;IACRb,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdS,aAAa,EAAE,WAAW;IAC1BG,aAAa,EAAE;EACjB;AACF,CAAC;AAGD,OAAO,IAAMC,OAAO,GAAG;EACrBC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,GAAG,EAAE;AACP,CAAC;AAGD,OAAO,IAAMC,aAAa,GAAG;EAC3BN,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNG,KAAK,EAAE;AACT,CAAC;AAGD,OAAO,IAAMC,OAAO,GAAG;EACrBC,KAAK,EAAE;IACLC,YAAY,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDC,MAAM,EAAE;IACNL,YAAY,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDE,KAAK,EAAE;IACLN,YAAY,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb;AACF,CAAC;AAGD,OAAO,IAAMG,aAAa,GAAG,MAAM;AAGnC,OAAO,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,SAAS,EAAK;EAE3C,OAAOrE,MAAM,CAACqE,SAAS,CAAC,IAAIrE,MAAM,CAACmE,aAAa,CAAC;AACnD,CAAC;AAGD,OAAO,IAAMG,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,SAAS,EAAEC,WAAW,EAAK;EAC3D,IAAMC,WAAW,GAAG,CAAC,CAAC;EAGtBC,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,OAAO,CAAC,UAAAC,GAAG,EAAI;IACpC,IAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC;IAG5B,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;MACtD,IAAMC,QAAQ,GAAGF,KAAK,CAACG,SAAS,CAAC,CAAC,CAAC;MACnCR,WAAW,CAACI,GAAG,CAAC,GAAGL,WAAW,CAACQ,QAAQ,CAAC,IAAIF,KAAK;IACnD,CAAC,MAAM;MACLL,WAAW,CAACI,GAAG,CAAC,GAAGC,KAAK;IAC1B;EACF,CAAC,CAAC;EAEF,OAAOL,WAAW;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}