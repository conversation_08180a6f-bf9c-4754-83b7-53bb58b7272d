{"ast": null, "code": "import createIconSet from \"./create-icon-set\";\nexport default function createIconSetFromFontello(config, fontFamilyArg, fontFile) {\n  var glyphMap = {};\n  config.glyphs.forEach(function (glyph) {\n    glyphMap[glyph.css] = glyph.code;\n  });\n  var fontFamily = fontFamilyArg || config.name || 'fontello';\n  return createIconSet(glyphMap, fontFamily, fontFile || `${fontFamily}.ttf`);\n}", "map": {"version": 3, "names": ["createIconSet", "createIconSetFromFontello", "config", "fontFamilyArg", "fontFile", "glyphMap", "glyphs", "for<PERSON>ach", "glyph", "css", "code", "fontFamily", "name"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/create-icon-set-from-fontello.js"], "sourcesContent": ["import createIconSet from './create-icon-set';\n\nexport default function createIconSetFromFontello(\n  config,\n  fontFamilyArg,\n  fontFile\n) {\n  const glyphMap = {};\n  config.glyphs.forEach(glyph => {\n    glyphMap[glyph.css] = glyph.code;\n  });\n\n  const fontFamily = fontFamilyArg || config.name || 'fontello';\n\n  return createIconSet(glyphMap, fontFamily, fontFile || `${fontFamily}.ttf`);\n}\n"], "mappings": "AAAA,OAAOA,aAAa;AAEpB,eAAe,SAASC,yBAAyBA,CAC/CC,MAAM,EACNC,aAAa,EACbC,QAAQ,EACR;EACA,IAAMC,QAAQ,GAAG,CAAC,CAAC;EACnBH,MAAM,CAACI,MAAM,CAACC,OAAO,CAAC,UAAAC,KAAK,EAAI;IAC7BH,QAAQ,CAACG,KAAK,CAACC,GAAG,CAAC,GAAGD,KAAK,CAACE,IAAI;EAClC,CAAC,CAAC;EAEF,IAAMC,UAAU,GAAGR,aAAa,IAAID,MAAM,CAACU,IAAI,IAAI,UAAU;EAE7D,OAAOZ,aAAa,CAACK,QAAQ,EAAEM,UAAU,EAAEP,QAAQ,IAAI,GAAGO,UAAU,MAAM,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}