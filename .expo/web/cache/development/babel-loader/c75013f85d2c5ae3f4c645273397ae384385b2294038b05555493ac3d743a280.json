{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"disabled\", \"editable\", \"label\", \"error\", \"selectionColor\", \"cursorColor\", \"underlineColor\", \"outlineColor\", \"activeOutlineColor\", \"outlineStyle\", \"textColor\", \"dense\", \"style\", \"theme\", \"render\", \"multiline\", \"parentState\", \"innerRef\", \"onFocus\", \"forceFocus\", \"onBlur\", \"onChangeText\", \"onLayoutAnimatedText\", \"onLabelTextLayout\", \"onLeftAffixLayoutChange\", \"onRightAffixLayoutChange\", \"onInputLayout\", \"onLayout\", \"left\", \"right\", \"placeholderTextColor\", \"testID\", \"contentStyle\", \"scaledLabel\"],\n  _excluded2 = [\"fontSize\", \"fontWeight\", \"lineHeight\", \"height\", \"backgroundColor\", \"textAlign\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport View from \"react-native-web/dist/exports/View\";\nimport NativeTextInput from \"react-native-web/dist/exports/TextInput\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { Outline } from \"./Addons/Outline\";\nimport { AdornmentType, AdornmentSide } from \"./Adornment/enums\";\nimport TextInputAdornment, { getAdornmentConfig, getAdornmentStyleAdjustmentForNativeInput } from \"./Adornment/TextInputAdornment\";\nimport { MAXIMIZED_LABEL_FONT_SIZE, MINIMIZED_LABEL_FONT_SIZE, LABEL_WIGGLE_X_OFFSET, ADORNMENT_SIZE, OUTLINE_MINIMIZED_LABEL_Y_OFFSET, LABEL_PADDING_TOP, MIN_DENSE_HEIGHT_OUTLINED, LABEL_PADDING_TOP_DENSE } from \"./constants\";\nimport { calculateLabelTopPosition, calculateInputHeight, calculatePadding, adjustPaddingOut, calculateOutlinedIconAndAffixTopPosition, getOutlinedInputColors, getConstants } from \"./helpers\";\nimport InputLabel from \"./Label/InputLabel\";\nimport LabelBackground from \"./Label/LabelBackground\";\nvar TextInputOutlined = function TextInputOutlined(_ref) {\n  var _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    _ref$editable = _ref.editable,\n    editable = _ref$editable === void 0 ? true : _ref$editable,\n    label = _ref.label,\n    _ref$error = _ref.error,\n    error = _ref$error === void 0 ? false : _ref$error,\n    customSelectionColor = _ref.selectionColor,\n    cursorColor = _ref.cursorColor,\n    _underlineColor = _ref.underlineColor,\n    customOutlineColor = _ref.outlineColor,\n    activeOutlineColor = _ref.activeOutlineColor,\n    outlineStyle = _ref.outlineStyle,\n    textColor = _ref.textColor,\n    dense = _ref.dense,\n    style = _ref.style,\n    theme = _ref.theme,\n    _ref$render = _ref.render,\n    render = _ref$render === void 0 ? function (props) {\n      return React.createElement(NativeTextInput, props);\n    } : _ref$render,\n    _ref$multiline = _ref.multiline,\n    multiline = _ref$multiline === void 0 ? false : _ref$multiline,\n    parentState = _ref.parentState,\n    innerRef = _ref.innerRef,\n    onFocus = _ref.onFocus,\n    forceFocus = _ref.forceFocus,\n    onBlur = _ref.onBlur,\n    onChangeText = _ref.onChangeText,\n    onLayoutAnimatedText = _ref.onLayoutAnimatedText,\n    onLabelTextLayout = _ref.onLabelTextLayout,\n    onLeftAffixLayoutChange = _ref.onLeftAffixLayoutChange,\n    onRightAffixLayoutChange = _ref.onRightAffixLayoutChange,\n    onInputLayout = _ref.onInputLayout,\n    onLayout = _ref.onLayout,\n    left = _ref.left,\n    right = _ref.right,\n    placeholderTextColor = _ref.placeholderTextColor,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'text-input-outlined' : _ref$testID,\n    contentStyle = _ref.contentStyle,\n    scaledLabel = _ref.scaledLabel,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var adornmentConfig = getAdornmentConfig({\n    left: left,\n    right: right\n  });\n  var colors = theme.colors,\n    isV3 = theme.isV3,\n    roundness = theme.roundness;\n  var font = isV3 ? theme.fonts.bodyLarge : theme.fonts.regular;\n  var hasActiveOutline = parentState.focused || error;\n  var _getConstants = getConstants(isV3),\n    INPUT_PADDING_HORIZONTAL = _getConstants.INPUT_PADDING_HORIZONTAL,\n    MIN_HEIGHT = _getConstants.MIN_HEIGHT,\n    ADORNMENT_OFFSET = _getConstants.ADORNMENT_OFFSET,\n    MIN_WIDTH = _getConstants.MIN_WIDTH;\n  var _ref4 = StyleSheet.flatten(style) || {},\n    fontSizeStyle = _ref4.fontSize,\n    fontWeight = _ref4.fontWeight,\n    lineHeightStyle = _ref4.lineHeight,\n    height = _ref4.height,\n    _ref4$backgroundColor = _ref4.backgroundColor,\n    backgroundColor = _ref4$backgroundColor === void 0 ? colors === null || colors === void 0 ? void 0 : colors.background : _ref4$backgroundColor,\n    textAlign = _ref4.textAlign,\n    viewStyle = _objectWithoutProperties(_ref4, _excluded2);\n  var fontSize = fontSizeStyle || MAXIMIZED_LABEL_FONT_SIZE;\n  var lineHeight = lineHeightStyle || (Platform.OS === 'web' ? fontSize * 1.2 : undefined);\n  var _getOutlinedInputColo = getOutlinedInputColors({\n      activeOutlineColor: activeOutlineColor,\n      customOutlineColor: customOutlineColor,\n      customSelectionColor: customSelectionColor,\n      textColor: textColor,\n      disabled: disabled,\n      error: error,\n      theme: theme\n    }),\n    inputTextColor = _getOutlinedInputColo.inputTextColor,\n    activeColor = _getOutlinedInputColo.activeColor,\n    outlineColor = _getOutlinedInputColo.outlineColor,\n    placeholderColor = _getOutlinedInputColo.placeholderColor,\n    errorColor = _getOutlinedInputColo.errorColor,\n    selectionColor = _getOutlinedInputColo.selectionColor;\n  var densePaddingTop = label ? LABEL_PADDING_TOP_DENSE : 0;\n  var paddingTop = label ? LABEL_PADDING_TOP : 0;\n  var yOffset = label ? OUTLINE_MINIMIZED_LABEL_Y_OFFSET : 0;\n  var labelScale = MINIMIZED_LABEL_FONT_SIZE / fontSize;\n  var fontScale = MAXIMIZED_LABEL_FONT_SIZE / fontSize;\n  var labelWidth = parentState.labelLayout.width;\n  var labelHeight = parentState.labelLayout.height;\n  var labelHalfWidth = labelWidth / 2;\n  var labelHalfHeight = labelHeight / 2;\n  var baseLabelTranslateX = (I18nManager.getConstants().isRTL ? 1 : -1) * (labelHalfWidth - labelScale * labelWidth / 2 - (fontSize - MINIMIZED_LABEL_FONT_SIZE) * labelScale);\n  var labelTranslationXOffset = 0;\n  var isAdornmentLeftIcon = adornmentConfig.some(function (_ref2) {\n    var side = _ref2.side,\n      type = _ref2.type;\n    return side === AdornmentSide.Left && type === AdornmentType.Icon;\n  });\n  var isAdornmentRightIcon = adornmentConfig.some(function (_ref3) {\n    var side = _ref3.side,\n      type = _ref3.type;\n    return side === AdornmentSide.Right && type === AdornmentType.Icon;\n  });\n  if (isAdornmentLeftIcon) {\n    labelTranslationXOffset = (I18nManager.getConstants().isRTL ? -1 : 1) * (ADORNMENT_SIZE + ADORNMENT_OFFSET - (isV3 ? 0 : 8));\n  }\n  var minInputHeight = (dense ? MIN_DENSE_HEIGHT_OUTLINED : MIN_HEIGHT) - paddingTop;\n  var inputHeight = calculateInputHeight(labelHeight, height, minInputHeight);\n  var topPosition = calculateLabelTopPosition(labelHeight, inputHeight, paddingTop);\n  if (height && typeof height !== 'number') {\n    console.warn('Currently we support only numbers in height prop');\n  }\n  var paddingSettings = {\n    height: height ? +height : null,\n    labelHalfHeight: labelHalfHeight,\n    offset: paddingTop,\n    multiline: multiline ? multiline : null,\n    dense: dense ? dense : null,\n    topPosition: topPosition,\n    fontSize: fontSize,\n    lineHeight: lineHeight,\n    label: label,\n    scale: fontScale,\n    isAndroid: Platform.OS === 'android',\n    styles: StyleSheet.flatten(dense ? styles.inputOutlinedDense : styles.inputOutlined)\n  };\n  var pad = calculatePadding(paddingSettings);\n  var paddingOut = adjustPaddingOut(_objectSpread(_objectSpread({}, paddingSettings), {}, {\n    pad: pad\n  }));\n  var baseLabelTranslateY = -labelHalfHeight - (topPosition + yOffset);\n  var _React$useRef = React.useRef([new Animated.Value(0), new Animated.Value(1)]),\n    placeholderOpacityAnims = _React$useRef.current;\n  var placeholderOpacity = hasActiveOutline ? parentState.labeled : placeholderOpacityAnims[parentState.labelLayout.measured ? 1 : 0];\n  var placeholderStyle = {\n    position: 'absolute',\n    left: 0,\n    paddingHorizontal: INPUT_PADDING_HORIZONTAL\n  };\n  var placeholderTextColorBasedOnState = parentState.displayPlaceholder ? placeholderTextColor != null ? placeholderTextColor : placeholderColor : 'transparent';\n  var labelBackgroundColor = backgroundColor === 'transparent' ? theme.colors.background : backgroundColor;\n  var labelProps = {\n    label: label,\n    onLayoutAnimatedText: onLayoutAnimatedText,\n    onLabelTextLayout: onLabelTextLayout,\n    placeholderOpacity: placeholderOpacity,\n    labelError: error,\n    placeholderStyle: placeholderStyle,\n    baseLabelTranslateY: baseLabelTranslateY,\n    baseLabelTranslateX: baseLabelTranslateX,\n    font: font,\n    fontSize: fontSize,\n    lineHeight: lineHeight,\n    fontWeight: fontWeight,\n    labelScale: labelScale,\n    wiggleOffsetX: LABEL_WIGGLE_X_OFFSET,\n    topPosition: topPosition,\n    hasActiveOutline: hasActiveOutline,\n    activeColor: activeColor,\n    placeholderColor: placeholderColor,\n    backgroundColor: labelBackgroundColor,\n    errorColor: errorColor,\n    labelTranslationXOffset: labelTranslationXOffset,\n    roundness: roundness,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    testID: testID,\n    contentStyle: contentStyle,\n    inputContainerLayout: {\n      width: parentState.inputContainerLayout.width + (isAdornmentRightIcon || isAdornmentLeftIcon ? INPUT_PADDING_HORIZONTAL : 0)\n    },\n    opacity: parentState.value || parentState.focused ? parentState.labelLayout.measured ? 1 : 0 : 1,\n    isV3: isV3\n  };\n  var onLayoutChange = React.useCallback(function (e) {\n    onInputLayout(e);\n    onLayout === null || onLayout === void 0 ? void 0 : onLayout(e);\n  }, [onLayout, onInputLayout]);\n  var minHeight = height || (dense ? MIN_DENSE_HEIGHT_OUTLINED : MIN_HEIGHT);\n  var outlinedHeight = inputHeight + (dense ? densePaddingTop / 2 : paddingTop);\n  var leftLayout = parentState.leftLayout,\n    rightLayout = parentState.rightLayout;\n  var leftAffixTopPosition = calculateOutlinedIconAndAffixTopPosition({\n    height: outlinedHeight,\n    affixHeight: leftLayout.height || 0,\n    labelYOffset: -yOffset\n  });\n  var rightAffixTopPosition = calculateOutlinedIconAndAffixTopPosition({\n    height: outlinedHeight,\n    affixHeight: rightLayout.height || 0,\n    labelYOffset: -yOffset\n  });\n  var iconTopPosition = calculateOutlinedIconAndAffixTopPosition({\n    height: outlinedHeight,\n    affixHeight: ADORNMENT_SIZE,\n    labelYOffset: -yOffset\n  });\n  var rightAffixWidth = right ? rightLayout.width || ADORNMENT_SIZE : ADORNMENT_SIZE;\n  var leftAffixWidth = left ? leftLayout.width || ADORNMENT_SIZE : ADORNMENT_SIZE;\n  var adornmentStyleAdjustmentForNativeInput = getAdornmentStyleAdjustmentForNativeInput({\n    adornmentConfig: adornmentConfig,\n    rightAffixWidth: rightAffixWidth,\n    leftAffixWidth: leftAffixWidth,\n    mode: 'outlined',\n    isV3: isV3\n  });\n  var affixTopPosition = _defineProperty(_defineProperty({}, AdornmentSide.Left, leftAffixTopPosition), AdornmentSide.Right, rightAffixTopPosition);\n  var onAffixChange = _defineProperty(_defineProperty({}, AdornmentSide.Left, onLeftAffixLayoutChange), AdornmentSide.Right, onRightAffixLayoutChange);\n  var adornmentProps = {\n    adornmentConfig: adornmentConfig,\n    forceFocus: forceFocus,\n    topPosition: _defineProperty(_defineProperty({}, AdornmentType.Icon, iconTopPosition), AdornmentType.Affix, affixTopPosition),\n    onAffixChange: onAffixChange,\n    isTextInputFocused: parentState.focused,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    disabled: disabled\n  };\n  if (adornmentConfig.length) {\n    adornmentProps = _objectSpread(_objectSpread({}, adornmentProps), {}, {\n      left: left,\n      right: right,\n      textStyle: _objectSpread(_objectSpread({}, font), {}, {\n        fontSize: fontSize,\n        lineHeight: lineHeight,\n        fontWeight: fontWeight\n      }),\n      visible: parentState.labeled\n    });\n  }\n  return React.createElement(View, {\n    style: viewStyle\n  }, React.createElement(Outline, {\n    isV3: isV3,\n    style: outlineStyle,\n    label: label,\n    roundness: roundness,\n    hasActiveOutline: hasActiveOutline,\n    focused: parentState.focused,\n    activeColor: activeColor,\n    outlineColor: outlineColor,\n    backgroundColor: backgroundColor\n  }), React.createElement(View, {\n    style: [styles.labelContainer, {\n      paddingTop: paddingTop,\n      minHeight: minHeight\n    }]\n  }, label ? React.createElement(InputLabel, _extends({\n    labeled: parentState.labeled,\n    error: parentState.error,\n    focused: parentState.focused,\n    scaledLabel: scaledLabel,\n    wiggle: Boolean(parentState.value && labelProps.labelError),\n    labelLayoutMeasured: parentState.labelLayout.measured,\n    labelLayoutWidth: parentState.labelLayout.width,\n    labelLayoutHeight: parentState.labelLayout.height\n  }, labelProps, {\n    labelBackground: LabelBackground,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier\n  })) : null, render === null || render === void 0 ? void 0 : render(_objectSpread(_objectSpread({}, rest), {}, {\n    ref: innerRef,\n    onLayout: onLayoutChange,\n    onChangeText: onChangeText,\n    placeholder: rest.placeholder,\n    editable: !disabled && editable,\n    selectionColor: selectionColor,\n    cursorColor: typeof cursorColor === 'undefined' ? activeColor : cursorColor,\n    placeholderTextColor: placeholderTextColorBasedOnState,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    underlineColorAndroid: 'transparent',\n    multiline: multiline,\n    style: [styles.input, !multiline || multiline && height ? {\n      height: inputHeight\n    } : {}, paddingOut, _objectSpread(_objectSpread({}, font), {}, {\n      fontSize: fontSize,\n      lineHeight: lineHeight,\n      fontWeight: fontWeight,\n      color: inputTextColor,\n      textAlignVertical: multiline ? 'top' : 'center',\n      textAlign: textAlign ? textAlign : I18nManager.getConstants().isRTL ? 'right' : 'left',\n      paddingHorizontal: INPUT_PADDING_HORIZONTAL,\n      minWidth: Math.min(parentState.labelTextLayout.width + 2 * INPUT_PADDING_HORIZONTAL, MIN_WIDTH)\n    }), Platform.OS === 'web' ? {\n      outline: 'none'\n    } : undefined, adornmentStyleAdjustmentForNativeInput, contentStyle],\n    testID: testID\n  }))), React.createElement(TextInputAdornment, adornmentProps));\n};\nexport default TextInputOutlined;\nvar styles = StyleSheet.create({\n  labelContainer: {\n    paddingBottom: 0,\n    flexGrow: 1\n  },\n  input: {\n    margin: 0,\n    flexGrow: 1\n  },\n  inputOutlined: {\n    paddingTop: 8,\n    paddingBottom: 8\n  },\n  inputOutlinedDense: {\n    paddingTop: 4,\n    paddingBottom: 4\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "View", "NativeTextInput", "StyleSheet", "I18nManager", "Platform", "Outline", "AdornmentType", "AdornmentSide", "TextInputAdornment", "getAdornmentConfig", "getAdornmentStyleAdjustmentForNativeInput", "MAXIMIZED_LABEL_FONT_SIZE", "MINIMIZED_LABEL_FONT_SIZE", "LABEL_WIGGLE_X_OFFSET", "ADORNMENT_SIZE", "OUTLINE_MINIMIZED_LABEL_Y_OFFSET", "LABEL_PADDING_TOP", "MIN_DENSE_HEIGHT_OUTLINED", "LABEL_PADDING_TOP_DENSE", "calculateLabelTopPosition", "calculateInputHeight", "calculatePadding", "adjustPaddingOut", "calculateOutlinedIconAndAffixTopPosition", "getOutlinedInputColors", "getConstants", "InputLabel", "LabelBackground", "TextInputOutlined", "_ref", "_ref$disabled", "disabled", "_ref$editable", "editable", "label", "_ref$error", "error", "customSelectionColor", "selectionColor", "cursorColor", "_underlineColor", "underlineColor", "customOutlineColor", "outlineColor", "activeOutlineColor", "outlineStyle", "textColor", "dense", "style", "theme", "_ref$render", "render", "props", "createElement", "_ref$multiline", "multiline", "parentState", "innerRef", "onFocus", "forceFocus", "onBlur", "onChangeText", "onLayoutAnimatedText", "onLabelTextLayout", "onLeftAffixLayoutChange", "onRightAffixLayoutChange", "onInputLayout", "onLayout", "left", "right", "placeholderTextColor", "_ref$testID", "testID", "contentStyle", "scaledLabel", "rest", "_objectWithoutProperties", "_excluded", "adornmentConfig", "colors", "isV3", "roundness", "font", "fonts", "bodyLarge", "regular", "hasActiveOutline", "focused", "_getConstants", "INPUT_PADDING_HORIZONTAL", "MIN_HEIGHT", "ADORNMENT_OFFSET", "MIN_WIDTH", "_ref4", "flatten", "fontSizeStyle", "fontSize", "fontWeight", "lineHeightStyle", "lineHeight", "height", "_ref4$backgroundColor", "backgroundColor", "background", "textAlign", "viewStyle", "_excluded2", "OS", "undefined", "_getOutlinedInputColo", "inputTextColor", "activeColor", "placeholderColor", "errorColor", "densePaddingTop", "paddingTop", "yOffset", "labelScale", "fontScale", "labelWidth", "labelLayout", "width", "labelHeight", "labelHalfWidth", "labelHalfHeight", "baseLabelTranslateX", "isRTL", "labelTranslationXOffset", "isAdornmentLeftIcon", "some", "_ref2", "side", "type", "Left", "Icon", "isAdornmentRightIcon", "_ref3", "Right", "minInputHeight", "inputHeight", "topPosition", "console", "warn", "paddingSettings", "offset", "scale", "isAndroid", "styles", "inputOutlinedDense", "inputOutlined", "pad", "paddingOut", "_objectSpread", "baseLabelTranslateY", "_React$useRef", "useRef", "Value", "placeholderOpacityAnims", "current", "placeholderOpacity", "labeled", "measured", "placeholder<PERSON><PERSON><PERSON>", "position", "paddingHorizontal", "placeholderTextColorBasedOnState", "displayPlaceholder", "labelBackgroundColor", "labelProps", "labelError", "wiggleOffsetX", "maxFontSizeMultiplier", "inputContainerLayout", "opacity", "value", "onLayoutChange", "useCallback", "e", "minHeight", "outlinedHeight", "leftLayout", "rightLayout", "leftAffixTopPosition", "affixHeight", "labelYOffset", "rightAffixTopPosition", "iconTopPosition", "rightAffix<PERSON>idth", "leftAffixWidth", "adornmentStyleAdjustmentForNativeInput", "mode", "affixTopPosition", "_defineProperty", "onAffixChange", "adornmentProps", "Affix", "isTextInputFocused", "length", "textStyle", "visible", "labelContainer", "_extends", "wiggle", "Boolean", "labelLayoutMeasured", "labelLayoutWidth", "labelLayoutHeight", "labelBackground", "ref", "placeholder", "underlineColorAndroid", "input", "color", "textAlignVertical", "min<PERSON><PERSON><PERSON>", "Math", "min", "labelTextLayout", "outline", "create", "paddingBottom", "flexGrow", "margin"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/TextInput/TextInputOutlined.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  View,\n  TextInput as NativeTextInput,\n  StyleSheet,\n  I18nManager,\n  Platform,\n  TextStyle,\n  ColorValue,\n  LayoutChangeEvent,\n} from 'react-native';\n\nimport { Outline } from './Addons/Outline';\nimport { AdornmentType, AdornmentSide } from './Adornment/enums';\nimport TextInputAdornment, {\n  getAdornmentConfig,\n  getAdornmentStyleAdjustmentForNativeInput,\n  TextInputAdornmentProps,\n} from './Adornment/TextInputAdornment';\nimport {\n  MAXIMIZED_LABEL_FONT_SIZE,\n  MINIMIZED_LABEL_FONT_SIZE,\n  LABEL_WIGGLE_X_OFFSET,\n  ADORNMENT_SIZE,\n  OUTLINE_MINIMIZED_LABEL_Y_OFFSET,\n  LABEL_PADDING_TOP,\n  MIN_DENSE_HEIGHT_OUTLINED,\n  LABEL_PADDING_TOP_DENSE,\n} from './constants';\nimport {\n  calculateLabelTopPosition,\n  calculateInputHeight,\n  calculatePadding,\n  adjustPaddingOut,\n  Padding,\n  calculateOutlinedIconAndAffixTopPosition,\n  getOutlinedInputColors,\n  getConstants,\n} from './helpers';\nimport InputLabel from './Label/InputLabel';\nimport LabelBackground from './Label/LabelBackground';\nimport type { RenderProps, ChildTextInputProps } from './types';\n\nconst TextInputOutlined = ({\n  disabled = false,\n  editable = true,\n  label,\n  error = false,\n  selectionColor: customSelectionColor,\n  cursorColor,\n  underlineColor: _underlineColor,\n  outlineColor: customOutlineColor,\n  activeOutlineColor,\n  outlineStyle,\n  textColor,\n  dense,\n  style,\n  theme,\n  render = (props: RenderProps) => <NativeTextInput {...props} />,\n  multiline = false,\n  parentState,\n  innerRef,\n  onFocus,\n  forceFocus,\n  onBlur,\n  onChangeText,\n  onLayoutAnimatedText,\n  onLabelTextLayout,\n  onLeftAffixLayoutChange,\n  onRightAffixLayoutChange,\n  onInputLayout,\n  onLayout,\n  left,\n  right,\n  placeholderTextColor,\n  testID = 'text-input-outlined',\n  contentStyle,\n  scaledLabel,\n  ...rest\n}: ChildTextInputProps) => {\n  const adornmentConfig = getAdornmentConfig({ left, right });\n\n  const { colors, isV3, roundness } = theme;\n  const font = isV3 ? theme.fonts.bodyLarge : theme.fonts.regular;\n  const hasActiveOutline = parentState.focused || error;\n\n  const { INPUT_PADDING_HORIZONTAL, MIN_HEIGHT, ADORNMENT_OFFSET, MIN_WIDTH } =\n    getConstants(isV3);\n\n  const {\n    fontSize: fontSizeStyle,\n    fontWeight,\n    lineHeight: lineHeightStyle,\n    height,\n    backgroundColor = colors?.background,\n    textAlign,\n    ...viewStyle\n  } = (StyleSheet.flatten(style) || {}) as TextStyle;\n  const fontSize = fontSizeStyle || MAXIMIZED_LABEL_FONT_SIZE;\n  const lineHeight =\n    lineHeightStyle || (Platform.OS === 'web' ? fontSize * 1.2 : undefined);\n\n  const {\n    inputTextColor,\n    activeColor,\n    outlineColor,\n    placeholderColor,\n    errorColor,\n    selectionColor,\n  } = getOutlinedInputColors({\n    activeOutlineColor,\n    customOutlineColor,\n    customSelectionColor,\n    textColor,\n    disabled,\n    error,\n    theme,\n  });\n\n  const densePaddingTop = label ? LABEL_PADDING_TOP_DENSE : 0;\n  const paddingTop = label ? LABEL_PADDING_TOP : 0;\n  const yOffset = label ? OUTLINE_MINIMIZED_LABEL_Y_OFFSET : 0;\n\n  const labelScale = MINIMIZED_LABEL_FONT_SIZE / fontSize;\n  const fontScale = MAXIMIZED_LABEL_FONT_SIZE / fontSize;\n\n  const labelWidth = parentState.labelLayout.width;\n  const labelHeight = parentState.labelLayout.height;\n  const labelHalfWidth = labelWidth / 2;\n  const labelHalfHeight = labelHeight / 2;\n\n  const baseLabelTranslateX =\n    (I18nManager.getConstants().isRTL ? 1 : -1) *\n    (labelHalfWidth -\n      (labelScale * labelWidth) / 2 -\n      (fontSize - MINIMIZED_LABEL_FONT_SIZE) * labelScale);\n\n  let labelTranslationXOffset = 0;\n  const isAdornmentLeftIcon = adornmentConfig.some(\n    ({ side, type }) =>\n      side === AdornmentSide.Left && type === AdornmentType.Icon\n  );\n  const isAdornmentRightIcon = adornmentConfig.some(\n    ({ side, type }) =>\n      side === AdornmentSide.Right && type === AdornmentType.Icon\n  );\n\n  if (isAdornmentLeftIcon) {\n    labelTranslationXOffset =\n      (I18nManager.getConstants().isRTL ? -1 : 1) *\n      (ADORNMENT_SIZE + ADORNMENT_OFFSET - (isV3 ? 0 : 8));\n  }\n\n  const minInputHeight =\n    (dense ? MIN_DENSE_HEIGHT_OUTLINED : MIN_HEIGHT) - paddingTop;\n\n  const inputHeight = calculateInputHeight(labelHeight, height, minInputHeight);\n\n  const topPosition = calculateLabelTopPosition(\n    labelHeight,\n    inputHeight,\n    paddingTop\n  );\n\n  if (height && typeof height !== 'number') {\n    // eslint-disable-next-line\n    console.warn('Currently we support only numbers in height prop');\n  }\n\n  const paddingSettings = {\n    height: height ? +height : null,\n    labelHalfHeight,\n    offset: paddingTop,\n    multiline: multiline ? multiline : null,\n    dense: dense ? dense : null,\n    topPosition,\n    fontSize,\n    lineHeight,\n    label,\n    scale: fontScale,\n    isAndroid: Platform.OS === 'android',\n    styles: StyleSheet.flatten(\n      dense ? styles.inputOutlinedDense : styles.inputOutlined\n    ) as Padding,\n  };\n\n  const pad = calculatePadding(paddingSettings);\n\n  const paddingOut = adjustPaddingOut({ ...paddingSettings, pad });\n\n  const baseLabelTranslateY = -labelHalfHeight - (topPosition + yOffset);\n\n  const { current: placeholderOpacityAnims } = React.useRef([\n    new Animated.Value(0),\n    new Animated.Value(1),\n  ]);\n\n  const placeholderOpacity = hasActiveOutline\n    ? parentState.labeled\n    : placeholderOpacityAnims[parentState.labelLayout.measured ? 1 : 0];\n\n  const placeholderStyle = {\n    position: 'absolute',\n    left: 0,\n    paddingHorizontal: INPUT_PADDING_HORIZONTAL,\n  };\n\n  const placeholderTextColorBasedOnState = parentState.displayPlaceholder\n    ? placeholderTextColor ?? placeholderColor\n    : 'transparent';\n\n  const labelBackgroundColor: ColorValue =\n    backgroundColor === 'transparent'\n      ? theme.colors.background\n      : backgroundColor;\n\n  const labelProps = {\n    label,\n    onLayoutAnimatedText,\n    onLabelTextLayout,\n    placeholderOpacity,\n    labelError: error,\n    placeholderStyle,\n    baseLabelTranslateY,\n    baseLabelTranslateX,\n    font,\n    fontSize,\n    lineHeight,\n    fontWeight,\n    labelScale,\n    wiggleOffsetX: LABEL_WIGGLE_X_OFFSET,\n    topPosition,\n    hasActiveOutline,\n    activeColor,\n    placeholderColor,\n    backgroundColor: labelBackgroundColor,\n    errorColor,\n    labelTranslationXOffset,\n    roundness,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    testID,\n    contentStyle,\n    inputContainerLayout: {\n      width:\n        parentState.inputContainerLayout.width +\n        (isAdornmentRightIcon || isAdornmentLeftIcon\n          ? INPUT_PADDING_HORIZONTAL\n          : 0),\n    },\n    opacity:\n      parentState.value || parentState.focused\n        ? parentState.labelLayout.measured\n          ? 1\n          : 0\n        : 1,\n    isV3,\n  };\n\n  const onLayoutChange = React.useCallback(\n    (e: LayoutChangeEvent) => {\n      onInputLayout(e);\n      onLayout?.(e);\n    },\n    [onLayout, onInputLayout]\n  );\n\n  const minHeight = (height ||\n    (dense ? MIN_DENSE_HEIGHT_OUTLINED : MIN_HEIGHT)) as number;\n\n  const outlinedHeight =\n    inputHeight + (dense ? densePaddingTop / 2 : paddingTop);\n  const { leftLayout, rightLayout } = parentState;\n\n  const leftAffixTopPosition = calculateOutlinedIconAndAffixTopPosition({\n    height: outlinedHeight,\n    affixHeight: leftLayout.height || 0,\n    labelYOffset: -yOffset,\n  });\n\n  const rightAffixTopPosition = calculateOutlinedIconAndAffixTopPosition({\n    height: outlinedHeight,\n    affixHeight: rightLayout.height || 0,\n    labelYOffset: -yOffset,\n  });\n  const iconTopPosition = calculateOutlinedIconAndAffixTopPosition({\n    height: outlinedHeight,\n    affixHeight: ADORNMENT_SIZE,\n    labelYOffset: -yOffset,\n  });\n\n  const rightAffixWidth = right\n    ? rightLayout.width || ADORNMENT_SIZE\n    : ADORNMENT_SIZE;\n\n  const leftAffixWidth = left\n    ? leftLayout.width || ADORNMENT_SIZE\n    : ADORNMENT_SIZE;\n\n  const adornmentStyleAdjustmentForNativeInput =\n    getAdornmentStyleAdjustmentForNativeInput({\n      adornmentConfig,\n      rightAffixWidth,\n      leftAffixWidth,\n      mode: 'outlined',\n      isV3,\n    });\n  const affixTopPosition = {\n    [AdornmentSide.Left]: leftAffixTopPosition,\n    [AdornmentSide.Right]: rightAffixTopPosition,\n  };\n  const onAffixChange = {\n    [AdornmentSide.Left]: onLeftAffixLayoutChange,\n    [AdornmentSide.Right]: onRightAffixLayoutChange,\n  };\n\n  let adornmentProps: TextInputAdornmentProps = {\n    adornmentConfig,\n    forceFocus,\n    topPosition: {\n      [AdornmentType.Icon]: iconTopPosition,\n      [AdornmentType.Affix]: affixTopPosition,\n    },\n    onAffixChange,\n    isTextInputFocused: parentState.focused,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    disabled,\n  };\n  if (adornmentConfig.length) {\n    adornmentProps = {\n      ...adornmentProps,\n      left,\n      right,\n      textStyle: { ...font, fontSize, lineHeight, fontWeight },\n      visible: parentState.labeled,\n    };\n  }\n\n  return (\n    <View style={viewStyle}>\n      {/*\n          Render the outline separately from the container\n          This is so that the label can overlap the outline\n          Otherwise the border will cut off the label on Android\n          */}\n      <Outline\n        isV3={isV3}\n        style={outlineStyle}\n        label={label}\n        roundness={roundness}\n        hasActiveOutline={hasActiveOutline}\n        focused={parentState.focused}\n        activeColor={activeColor}\n        outlineColor={outlineColor}\n        backgroundColor={backgroundColor}\n      />\n      <View\n        style={[\n          styles.labelContainer,\n          {\n            paddingTop,\n            minHeight,\n          },\n        ]}\n      >\n        {label ? (\n          <InputLabel\n            labeled={parentState.labeled}\n            error={parentState.error}\n            focused={parentState.focused}\n            scaledLabel={scaledLabel}\n            wiggle={Boolean(parentState.value && labelProps.labelError)}\n            labelLayoutMeasured={parentState.labelLayout.measured}\n            labelLayoutWidth={parentState.labelLayout.width}\n            labelLayoutHeight={parentState.labelLayout.height}\n            {...labelProps}\n            labelBackground={LabelBackground}\n            maxFontSizeMultiplier={rest.maxFontSizeMultiplier}\n          />\n        ) : null}\n        {render?.({\n          ...rest,\n          ref: innerRef,\n          onLayout: onLayoutChange,\n          onChangeText,\n          placeholder: rest.placeholder,\n          editable: !disabled && editable,\n          selectionColor,\n          cursorColor:\n            typeof cursorColor === 'undefined' ? activeColor : cursorColor,\n          placeholderTextColor: placeholderTextColorBasedOnState,\n          onFocus,\n          onBlur,\n          underlineColorAndroid: 'transparent',\n          multiline,\n          style: [\n            styles.input,\n            !multiline || (multiline && height) ? { height: inputHeight } : {},\n            paddingOut,\n            {\n              ...font,\n              fontSize,\n              lineHeight,\n              fontWeight,\n              color: inputTextColor,\n              textAlignVertical: multiline ? 'top' : 'center',\n              textAlign: textAlign\n                ? textAlign\n                : I18nManager.getConstants().isRTL\n                ? 'right'\n                : 'left',\n              paddingHorizontal: INPUT_PADDING_HORIZONTAL,\n              minWidth: Math.min(\n                parentState.labelTextLayout.width +\n                  2 * INPUT_PADDING_HORIZONTAL,\n                MIN_WIDTH\n              ),\n            },\n            Platform.OS === 'web' ? { outline: 'none' } : undefined,\n            adornmentStyleAdjustmentForNativeInput,\n            contentStyle,\n          ],\n          testID,\n        } as RenderProps)}\n      </View>\n      <TextInputAdornment {...adornmentProps} />\n    </View>\n  );\n};\n\nexport default TextInputOutlined;\n\nconst styles = StyleSheet.create({\n  labelContainer: {\n    paddingBottom: 0,\n    flexGrow: 1,\n  },\n  input: {\n    margin: 0,\n    flexGrow: 1,\n  },\n  inputOutlined: {\n    paddingTop: 8,\n    paddingBottom: 8,\n  },\n  inputOutlinedDense: {\n    paddingTop: 4,\n    paddingBottom: 4,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAa9B,SAASC,OAAO;AAChB,SAASC,aAAa,EAAEC,aAAa;AACrC,OAAOC,kBAAkB,IACvBC,kBAAkB,EAClBC,yCAAyC;AAG3C,SACEC,yBAAyB,EACzBC,yBAAyB,EACzBC,qBAAqB,EACrBC,cAAc,EACdC,gCAAgC,EAChCC,iBAAiB,EACjBC,yBAAyB,EACzBC,uBAAuB;AAEzB,SACEC,yBAAyB,EACzBC,oBAAoB,EACpBC,gBAAgB,EAChBC,gBAAgB,EAEhBC,wCAAwC,EACxCC,sBAAsB,EACtBC,YAAY;AAEd,OAAOC,UAAU;AACjB,OAAOC,eAAe;AAGtB,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAGC,IAAA,EAoCC;EAAA,IAAAC,aAAA,GAALD,IAAA,CAnCpBE,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,aAAA,GAmCIH,IAAA,CAlCpBI,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACfE,KAAK,GAiCeL,IAAA,CAjCpBK,KAAK;IAAAC,UAAA,GAiCeN,IAAA,CAhCpBO,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,KAAK,GAAAA,UAAA;IACGE,oBAAoB,GA+BhBR,IAAA,CA/BpBS,cAAc;IACdC,WAAW,GA8BSV,IAAA,CA9BpBU,WAAW;IACKC,eAAe,GA6BXX,IAAA,CA7BpBY,cAAc;IACAC,kBAAkB,GA4BZb,IAAA,CA5BpBc,YAAY;IACZC,kBAAkB,GA2BEf,IAAA,CA3BpBe,kBAAkB;IAClBC,YAAY,GA0BQhB,IAAA,CA1BpBgB,YAAY;IACZC,SAAS,GAyBWjB,IAAA,CAzBpBiB,SAAS;IACTC,KAAK,GAwBelB,IAAA,CAxBpBkB,KAAK;IACLC,KAAK,GAuBenB,IAAA,CAvBpBmB,KAAK;IACLC,KAAK,GAsBepB,IAAA,CAtBpBoB,KAAK;IAAAC,WAAA,GAsBerB,IAAA,CArBpBsB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAI,UAAAE,KAAkB;MAAA,OAAKtD,KAAA,CAAAuD,aAAA,CAACpD,eAAe,EAAKmD,KAAQ,CAAC;IAAA,IAAAF,WAAA;IAAAI,cAAA,GAqB3CzB,IAAA,CApBpB0B,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IACjBE,WAAW,GAmBS3B,IAAA,CAnBpB2B,WAAW;IACXC,QAAQ,GAkBY5B,IAAA,CAlBpB4B,QAAQ;IACRC,OAAO,GAiBa7B,IAAA,CAjBpB6B,OAAO;IACPC,UAAU,GAgBU9B,IAAA,CAhBpB8B,UAAU;IACVC,MAAM,GAec/B,IAAA,CAfpB+B,MAAM;IACNC,YAAY,GAcQhC,IAAA,CAdpBgC,YAAY;IACZC,oBAAoB,GAaAjC,IAAA,CAbpBiC,oBAAoB;IACpBC,iBAAiB,GAYGlC,IAAA,CAZpBkC,iBAAiB;IACjBC,uBAAuB,GAWHnC,IAAA,CAXpBmC,uBAAuB;IACvBC,wBAAwB,GAUJpC,IAAA,CAVpBoC,wBAAwB;IACxBC,aAAa,GASOrC,IAAA,CATpBqC,aAAa;IACbC,QAAQ,GAQYtC,IAAA,CARpBsC,QAAQ;IACRC,IAAI,GAOgBvC,IAAA,CAPpBuC,IAAI;IACJC,KAAK,GAMexC,IAAA,CANpBwC,KAAK;IACLC,oBAAoB,GAKAzC,IAAA,CALpByC,oBAAoB;IAAAC,WAAA,GAKA1C,IAAA,CAJpB2C,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,qBAAqB,GAAAA,WAAA;IAC9BE,YAAY,GAGQ5C,IAAA,CAHpB4C,YAAY;IACZC,WAAW,GAES7C,IAAA,CAFpB6C,WAAW;IACRC,IAAA,GAAAC,wBAAA,CACiB/C,IAAA,EAAAgD,SAAA;EACpB,IAAMC,eAAe,GAAGrE,kBAAkB,CAAC;IAAE2D,IAAI,EAAJA,IAAI;IAAEC,KAAA,EAAAA;EAAM,CAAC,CAAC;EAE3D,IAAQU,MAAM,GAAsB9B,KAAK,CAAjC8B,MAAM;IAAEC,IAAI,GAAgB/B,KAAK,CAAzB+B,IAAI;IAAEC,SAAA,GAAchC,KAAK,CAAnBgC,SAAA;EACtB,IAAMC,IAAI,GAAGF,IAAI,GAAG/B,KAAK,CAACkC,KAAK,CAACC,SAAS,GAAGnC,KAAK,CAACkC,KAAK,CAACE,OAAO;EAC/D,IAAMC,gBAAgB,GAAG9B,WAAW,CAAC+B,OAAO,IAAInD,KAAK;EAErD,IAAAoD,aAAA,GACE/D,YAAY,CAACuD,IAAI,CAAC;IADZS,wBAAwB,GAAAD,aAAA,CAAxBC,wBAAwB;IAAEC,UAAU,GAAAF,aAAA,CAAVE,UAAU;IAAEC,gBAAgB,GAAAH,aAAA,CAAhBG,gBAAgB;IAAEC,SAAA,GAAAJ,aAAA,CAAAI,SAAA;EAGhE,IAAAC,KAAA,GAQK3F,UAAU,CAAC4F,OAAO,CAAC9C,KAAK,CAAC,IAAI,CAAC,CAAe;IAPtC+C,aAAa,GAAAF,KAAA,CAAvBG,QAAQ;IACRC,UAAU,GAAAJ,KAAA,CAAVI,UAAU;IACEC,eAAe,GAAAL,KAAA,CAA3BM,UAAU;IACVC,MAAM,GAAAP,KAAA,CAANO,MAAM;IAAAC,qBAAA,GAAAR,KAAA,CACNS,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAGtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEwB,UAAU,GAAAF,qBAAA;IACpCG,SAAS,GAAAX,KAAA,CAATW,SAAS;IACNC,SAAA,GAAA7B,wBAAA,CAAAiB,KAAA,EAAAa,UAAA;EAEL,IAAMV,QAAQ,GAAGD,aAAa,IAAIpF,yBAAyB;EAC3D,IAAMwF,UAAU,GACdD,eAAe,KAAK9F,QAAQ,CAACuG,EAAE,KAAK,KAAK,GAAGX,QAAQ,GAAG,GAAG,GAAGY,SAAS,CAAC;EAEzE,IAAAC,qBAAA,GAOIrF,sBAAsB,CAAC;MACzBoB,kBAAkB,EAAlBA,kBAAkB;MAClBF,kBAAkB,EAAlBA,kBAAkB;MAClBL,oBAAoB,EAApBA,oBAAoB;MACpBS,SAAS,EAATA,SAAS;MACTf,QAAQ,EAARA,QAAQ;MACRK,KAAK,EAALA,KAAK;MACLa,KAAA,EAAAA;IACF,CAAC,CAAC;IAdA6D,cAAc,GAAAD,qBAAA,CAAdC,cAAc;IACdC,WAAW,GAAAF,qBAAA,CAAXE,WAAW;IACXpE,YAAY,GAAAkE,qBAAA,CAAZlE,YAAY;IACZqE,gBAAgB,GAAAH,qBAAA,CAAhBG,gBAAgB;IAChBC,UAAU,GAAAJ,qBAAA,CAAVI,UAAU;IACV3E,cAAA,GAAAuE,qBAAA,CAAAvE,cAAA;EAWF,IAAM4E,eAAe,GAAGhF,KAAK,GAAGhB,uBAAuB,GAAG,CAAC;EAC3D,IAAMiG,UAAU,GAAGjF,KAAK,GAAGlB,iBAAiB,GAAG,CAAC;EAChD,IAAMoG,OAAO,GAAGlF,KAAK,GAAGnB,gCAAgC,GAAG,CAAC;EAE5D,IAAMsG,UAAU,GAAGzG,yBAAyB,GAAGoF,QAAQ;EACvD,IAAMsB,SAAS,GAAG3G,yBAAyB,GAAGqF,QAAQ;EAEtD,IAAMuB,UAAU,GAAG/D,WAAW,CAACgE,WAAW,CAACC,KAAK;EAChD,IAAMC,WAAW,GAAGlE,WAAW,CAACgE,WAAW,CAACpB,MAAM;EAClD,IAAMuB,cAAc,GAAGJ,UAAU,GAAG,CAAC;EACrC,IAAMK,eAAe,GAAGF,WAAW,GAAG,CAAC;EAEvC,IAAMG,mBAAmB,GACvB,CAAC1H,WAAW,CAACsB,YAAY,CAAC,CAAC,CAACqG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KACzCH,cAAc,GACZN,UAAU,GAAGE,UAAU,GAAI,CAAC,GAC7B,CAACvB,QAAQ,GAAGpF,yBAAyB,IAAIyG,UAAU,CAAC;EAExD,IAAIU,uBAAuB,GAAG,CAAC;EAC/B,IAAMC,mBAAmB,GAAGlD,eAAe,CAACmD,IAAI,CAC9C,UAAAC,KAAA;IAAA,IAAGC,IAAI,GAAQD,KAAA,CAAZC,IAAI;MAAEC,IAAA,GAAMF,KAAA,CAANE,IAAA;IAAM,OACbD,IAAI,KAAK5H,aAAa,CAAC8H,IAAI,IAAID,IAAI,KAAK9H,aAAa,CAACgI,IAAI;EAAA,CAC9D,CAAC;EACD,IAAMC,oBAAoB,GAAGzD,eAAe,CAACmD,IAAI,CAC/C,UAAAO,KAAA;IAAA,IAAGL,IAAI,GAAQK,KAAA,CAAZL,IAAI;MAAEC,IAAA,GAAMI,KAAA,CAANJ,IAAA;IAAM,OACbD,IAAI,KAAK5H,aAAa,CAACkI,KAAK,IAAIL,IAAI,KAAK9H,aAAa,CAACgI,IAAI;EAAA,CAC/D,CAAC;EAED,IAAIN,mBAAmB,EAAE;IACvBD,uBAAuB,GACrB,CAAC5H,WAAW,CAACsB,YAAY,CAAC,CAAC,CAACqG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KACzChH,cAAc,GAAG6E,gBAAgB,IAAIX,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACxD;EAEA,IAAM0D,cAAc,GAClB,CAAC3F,KAAK,GAAG9B,yBAAyB,GAAGyE,UAAU,IAAIyB,UAAU;EAE/D,IAAMwB,WAAW,GAAGvH,oBAAoB,CAACsG,WAAW,EAAEtB,MAAM,EAAEsC,cAAc,CAAC;EAE7E,IAAME,WAAW,GAAGzH,yBAAyB,CAC3CuG,WAAW,EACXiB,WAAW,EACXxB,UACF,CAAC;EAED,IAAIf,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAExCyC,OAAO,CAACC,IAAI,CAAC,kDAAkD,CAAC;EAClE;EAEA,IAAMC,eAAe,GAAG;IACtB3C,MAAM,EAAEA,MAAM,GAAG,CAACA,MAAM,GAAG,IAAI;IAC/BwB,eAAe,EAAfA,eAAe;IACfoB,MAAM,EAAE7B,UAAU;IAClB5D,SAAS,EAAEA,SAAS,GAAGA,SAAS,GAAG,IAAI;IACvCR,KAAK,EAAEA,KAAK,GAAGA,KAAK,GAAG,IAAI;IAC3B6F,WAAW,EAAXA,WAAW;IACX5C,QAAQ,EAARA,QAAQ;IACRG,UAAU,EAAVA,UAAU;IACVjE,KAAK,EAALA,KAAK;IACL+G,KAAK,EAAE3B,SAAS;IAChB4B,SAAS,EAAE9I,QAAQ,CAACuG,EAAE,KAAK,SAAS;IACpCwC,MAAM,EAAEjJ,UAAU,CAAC4F,OAAO,CACxB/C,KAAK,GAAGoG,MAAM,CAACC,kBAAkB,GAAGD,MAAM,CAACE,aAC7C;EACF,CAAC;EAED,IAAMC,GAAG,GAAGjI,gBAAgB,CAAC0H,eAAe,CAAC;EAE7C,IAAMQ,UAAU,GAAGjI,gBAAgB,CAAAkI,aAAA,CAAAA,aAAA,KAAMT,eAAe;IAAEO,GAAA,EAAAA;EAAA,EAAK,CAAC;EAEhE,IAAMG,mBAAmB,GAAG,CAAC7B,eAAe,IAAIgB,WAAW,GAAGxB,OAAO,CAAC;EAEtE,IAAAsC,aAAA,GAA6C5J,KAAK,CAAC6J,MAAM,CAAC,CACxD,IAAI5J,QAAQ,CAAC6J,KAAK,CAAC,CAAC,CAAC,EACrB,IAAI7J,QAAQ,CAAC6J,KAAK,CAAC,CAAC,CAAC,CACtB,CAAC;IAHeC,uBAAA,GAAAH,aAAA,CAATI,OAAO;EAKf,IAAMC,kBAAkB,GAAGzE,gBAAgB,GACvC9B,WAAW,CAACwG,OAAO,GACnBH,uBAAuB,CAACrG,WAAW,CAACgE,WAAW,CAACyC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;EAErE,IAAMC,gBAAgB,GAAG;IACvBC,QAAQ,EAAE,UAAU;IACpB/F,IAAI,EAAE,CAAC;IACPgG,iBAAiB,EAAE3E;EACrB,CAAC;EAED,IAAM4E,gCAAgC,GAAG7G,WAAW,CAAC8G,kBAAkB,GACnEhG,oBAAoB,WAApBA,oBAAoB,GAAI0C,gBAAgB,GACxC,aAAa;EAEjB,IAAMuD,oBAAgC,GACpCjE,eAAe,KAAK,aAAa,GAC7BrD,KAAK,CAAC8B,MAAM,CAACwB,UAAU,GACvBD,eAAe;EAErB,IAAMkE,UAAU,GAAG;IACjBtI,KAAK,EAALA,KAAK;IACL4B,oBAAoB,EAApBA,oBAAoB;IACpBC,iBAAiB,EAAjBA,iBAAiB;IACjBgG,kBAAkB,EAAlBA,kBAAkB;IAClBU,UAAU,EAAErI,KAAK;IACjB8H,gBAAgB,EAAhBA,gBAAgB;IAChBT,mBAAmB,EAAnBA,mBAAmB;IACnB5B,mBAAmB,EAAnBA,mBAAmB;IACnB3C,IAAI,EAAJA,IAAI;IACJc,QAAQ,EAARA,QAAQ;IACRG,UAAU,EAAVA,UAAU;IACVF,UAAU,EAAVA,UAAU;IACVoB,UAAU,EAAVA,UAAU;IACVqD,aAAa,EAAE7J,qBAAqB;IACpC+H,WAAW,EAAXA,WAAW;IACXtD,gBAAgB,EAAhBA,gBAAgB;IAChByB,WAAW,EAAXA,WAAW;IACXC,gBAAgB,EAAhBA,gBAAgB;IAChBV,eAAe,EAAEiE,oBAAoB;IACrCtD,UAAU,EAAVA,UAAU;IACVc,uBAAuB,EAAvBA,uBAAuB;IACvB9C,SAAS,EAATA,SAAS;IACT0F,qBAAqB,EAAEhG,IAAI,CAACgG,qBAAqB;IACjDnG,MAAM,EAANA,MAAM;IACNC,YAAY,EAAZA,YAAY;IACZmG,oBAAoB,EAAE;MACpBnD,KAAK,EACHjE,WAAW,CAACoH,oBAAoB,CAACnD,KAAK,IACrCc,oBAAoB,IAAIP,mBAAmB,GACxCvC,wBAAwB,GACxB,CAAC;IACT,CAAC;IACDoF,OAAO,EACLrH,WAAW,CAACsH,KAAK,IAAItH,WAAW,CAAC+B,OAAO,GACpC/B,WAAW,CAACgE,WAAW,CAACyC,QAAQ,GAC9B,CAAC,GACD,CAAC,GACH,CAAC;IACPjF,IAAA,EAAAA;EACF,CAAC;EAED,IAAM+F,cAAc,GAAGjL,KAAK,CAACkL,WAAW,CACrC,UAAAC,CAAoB,EAAK;IACxB/G,aAAa,CAAC+G,CAAC,CAAC;IAChB9G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG8G,CAAC,CAAC;EACf,CAAC,EACD,CAAC9G,QAAQ,EAAED,aAAa,CAC1B,CAAC;EAED,IAAMgH,SAAS,GAAI9E,MAAM,KACtBrD,KAAK,GAAG9B,yBAAyB,GAAGyE,UAAU,CAAY;EAE7D,IAAMyF,cAAc,GAClBxC,WAAW,IAAI5F,KAAK,GAAGmE,eAAe,GAAG,CAAC,GAAGC,UAAU,CAAC;EAC1D,IAAQiE,UAAU,GAAkB5H,WAAW,CAAvC4H,UAAU;IAAEC,WAAA,GAAgB7H,WAAW,CAA3B6H,WAAA;EAEpB,IAAMC,oBAAoB,GAAG/J,wCAAwC,CAAC;IACpE6E,MAAM,EAAE+E,cAAc;IACtBI,WAAW,EAAEH,UAAU,CAAChF,MAAM,IAAI,CAAC;IACnCoF,YAAY,EAAE,CAACpE;EACjB,CAAC,CAAC;EAEF,IAAMqE,qBAAqB,GAAGlK,wCAAwC,CAAC;IACrE6E,MAAM,EAAE+E,cAAc;IACtBI,WAAW,EAAEF,WAAW,CAACjF,MAAM,IAAI,CAAC;IACpCoF,YAAY,EAAE,CAACpE;EACjB,CAAC,CAAC;EACF,IAAMsE,eAAe,GAAGnK,wCAAwC,CAAC;IAC/D6E,MAAM,EAAE+E,cAAc;IACtBI,WAAW,EAAEzK,cAAc;IAC3B0K,YAAY,EAAE,CAACpE;EACjB,CAAC,CAAC;EAEF,IAAMuE,eAAe,GAAGtH,KAAK,GACzBgH,WAAW,CAAC5D,KAAK,IAAI3G,cAAc,GACnCA,cAAc;EAElB,IAAM8K,cAAc,GAAGxH,IAAI,GACvBgH,UAAU,CAAC3D,KAAK,IAAI3G,cAAc,GAClCA,cAAc;EAElB,IAAM+K,sCAAsC,GAC1CnL,yCAAyC,CAAC;IACxCoE,eAAe,EAAfA,eAAe;IACf6G,eAAe,EAAfA,eAAe;IACfC,cAAc,EAAdA,cAAc;IACdE,IAAI,EAAE,UAAU;IAChB9G,IAAA,EAAAA;EACF,CAAC,CAAC;EACJ,IAAM+G,gBAAgB,GAAAC,eAAA,CAAAA,eAAA,KACnBzL,aAAa,CAAC8H,IAAI,EAAGiD,oBAAoB,GACzC/K,aAAa,CAACkI,KAAK,EAAGgD,qBAAA,CACxB;EACD,IAAMQ,aAAa,GAAAD,eAAA,CAAAA,eAAA,KAChBzL,aAAa,CAAC8H,IAAI,EAAGrE,uBAAuB,GAC5CzD,aAAa,CAACkI,KAAK,EAAGxE,wBAAA,CACxB;EAED,IAAIiI,cAAuC,GAAG;IAC5CpH,eAAe,EAAfA,eAAe;IACfnB,UAAU,EAAVA,UAAU;IACViF,WAAW,EAAAoD,eAAA,CAAAA,eAAA,KACR1L,aAAa,CAACgI,IAAI,EAAGoD,eAAe,GACpCpL,aAAa,CAAC6L,KAAK,EAAGJ,gBAAA,CACxB;IACDE,aAAa,EAAbA,aAAa;IACbG,kBAAkB,EAAE5I,WAAW,CAAC+B,OAAO;IACvCoF,qBAAqB,EAAEhG,IAAI,CAACgG,qBAAqB;IACjD5I,QAAA,EAAAA;EACF,CAAC;EACD,IAAI+C,eAAe,CAACuH,MAAM,EAAE;IAC1BH,cAAc,GAAA1C,aAAA,CAAAA,aAAA,KACT0C,cAAc;MACjB9H,IAAI,EAAJA,IAAI;MACJC,KAAK,EAALA,KAAK;MACLiI,SAAS,EAAA9C,aAAA,CAAAA,aAAA,KAAOtE,IAAI;QAAEc,QAAQ,EAARA,QAAQ;QAAEG,UAAU,EAAVA,UAAU;QAAEF,UAAA,EAAAA;MAAA,EAAY;MACxDsG,OAAO,EAAE/I,WAAW,CAACwG;IAAA,EACtB;EACH;EAEA,OACElK,KAAA,CAAAuD,aAAA,CAACrD,IAAI;IAACgD,KAAK,EAAEyD;EAAU,GAMrB3G,KAAA,CAAAuD,aAAA,CAAChD,OAAO;IACN2E,IAAI,EAAEA,IAAK;IACXhC,KAAK,EAAEH,YAAa;IACpBX,KAAK,EAAEA,KAAM;IACb+C,SAAS,EAAEA,SAAU;IACrBK,gBAAgB,EAAEA,gBAAiB;IACnCC,OAAO,EAAE/B,WAAW,CAAC+B,OAAQ;IAC7BwB,WAAW,EAAEA,WAAY;IACzBpE,YAAY,EAAEA,YAAa;IAC3B2D,eAAe,EAAEA;EAAgB,CAClC,CAAC,EACFxG,KAAA,CAAAuD,aAAA,CAACrD,IAAI;IACHgD,KAAK,EAAE,CACLmG,MAAM,CAACqD,cAAc,EACrB;MACErF,UAAU,EAAVA,UAAU;MACV+D,SAAA,EAAAA;IACF,CAAC;EACD,GAEDhJ,KAAK,GACJpC,KAAA,CAAAuD,aAAA,CAAC3B,UAAU,EAAA+K,QAAA;IACTzC,OAAO,EAAExG,WAAW,CAACwG,OAAQ;IAC7B5H,KAAK,EAAEoB,WAAW,CAACpB,KAAM;IACzBmD,OAAO,EAAE/B,WAAW,CAAC+B,OAAQ;IAC7Bb,WAAW,EAAEA,WAAY;IACzBgI,MAAM,EAAEC,OAAO,CAACnJ,WAAW,CAACsH,KAAK,IAAIN,UAAU,CAACC,UAAU,CAAE;IAC5DmC,mBAAmB,EAAEpJ,WAAW,CAACgE,WAAW,CAACyC,QAAS;IACtD4C,gBAAgB,EAAErJ,WAAW,CAACgE,WAAW,CAACC,KAAM;IAChDqF,iBAAiB,EAAEtJ,WAAW,CAACgE,WAAW,CAACpB;EAAO,GAC9CoE,UAAU;IACduC,eAAe,EAAEpL,eAAgB;IACjCgJ,qBAAqB,EAAEhG,IAAI,CAACgG;EAAsB,EACnD,CAAC,GACA,IAAI,EACPxH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAAqG,aAAA,CAAAA,aAAA,KACF7E,IAAI;IACPqI,GAAG,EAAEvJ,QAAQ;IACbU,QAAQ,EAAE4G,cAAc;IACxBlH,YAAY,EAAZA,YAAY;IACZoJ,WAAW,EAAEtI,IAAI,CAACsI,WAAW;IAC7BhL,QAAQ,EAAE,CAACF,QAAQ,IAAIE,QAAQ;IAC/BK,cAAc,EAAdA,cAAc;IACdC,WAAW,EACT,OAAOA,WAAW,KAAK,WAAW,GAAGwE,WAAW,GAAGxE,WAAW;IAChE+B,oBAAoB,EAAE+F,gCAAgC;IACtD3G,OAAO,EAAPA,OAAO;IACPE,MAAM,EAANA,MAAM;IACNsJ,qBAAqB,EAAE,aAAa;IACpC3J,SAAS,EAATA,SAAS;IACTP,KAAK,EAAE,CACLmG,MAAM,CAACgE,KAAK,EACZ,CAAC5J,SAAS,IAAKA,SAAS,IAAI6C,MAAO,GAAG;MAAEA,MAAM,EAAEuC;IAAY,CAAC,GAAG,CAAC,CAAC,EAClEY,UAAU,EAAAC,aAAA,CAAAA,aAAA,KAELtE,IAAI;MACPc,QAAQ,EAARA,QAAQ;MACRG,UAAU,EAAVA,UAAU;MACVF,UAAU,EAAVA,UAAU;MACVmH,KAAK,EAAEtG,cAAc;MACrBuG,iBAAiB,EAAE9J,SAAS,GAAG,KAAK,GAAG,QAAQ;MAC/CiD,SAAS,EAAEA,SAAS,GAChBA,SAAS,GACTrG,WAAW,CAACsB,YAAY,CAAC,CAAC,CAACqG,KAAK,GAChC,OAAO,GACP,MAAM;MACVsC,iBAAiB,EAAE3E,wBAAwB;MAC3C6H,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAChBhK,WAAW,CAACiK,eAAe,CAAChG,KAAK,GAC/B,CAAC,GAAGhC,wBAAwB,EAC9BG,SACF;IAAA,IAEFxF,QAAQ,CAACuG,EAAE,KAAK,KAAK,GAAG;MAAE+G,OAAO,EAAE;IAAO,CAAC,GAAG9G,SAAS,EACvDiF,sCAAsC,EACtCpH,YAAY,CACb;IACDD,MAAA,EAAAA;EAAA,EACc,CACZ,CAAC,EACP1E,KAAA,CAAAuD,aAAA,CAAC7C,kBAAkB,EAAK0L,cAAiB,CACrC,CAAC;AAEX,CAAC;AAED,eAAetK,iBAAiB;AAEhC,IAAMuH,MAAM,GAAGjJ,UAAU,CAACyN,MAAM,CAAC;EAC/BnB,cAAc,EAAE;IACdoB,aAAa,EAAE,CAAC;IAChBC,QAAQ,EAAE;EACZ,CAAC;EACDV,KAAK,EAAE;IACLW,MAAM,EAAE,CAAC;IACTD,QAAQ,EAAE;EACZ,CAAC;EACDxE,aAAa,EAAE;IACblC,UAAU,EAAE,CAAC;IACbyG,aAAa,EAAE;EACjB,CAAC;EACDxE,kBAAkB,EAAE;IAClBjC,UAAU,EAAE,CAAC;IACbyG,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}