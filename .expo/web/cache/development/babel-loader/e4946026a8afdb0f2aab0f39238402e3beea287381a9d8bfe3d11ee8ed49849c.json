{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"enterTouchDelay\", \"leaveTouchDelay\", \"title\", \"theme\", \"titleMaxFontSizeMultiplier\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport { getTooltipPosition } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { addEventListener } from \"../../utils/addEventListener\";\nimport Portal from \"../Portal/Portal\";\nimport Text from \"../Typography/Text\";\nvar Tooltip = function Tooltip(_ref) {\n  var children = _ref.children,\n    _ref$enterTouchDelay = _ref.enterTouchDelay,\n    enterTouchDelay = _ref$enterTouchDelay === void 0 ? 500 : _ref$enterTouchDelay,\n    _ref$leaveTouchDelay = _ref.leaveTouchDelay,\n    leaveTouchDelay = _ref$leaveTouchDelay === void 0 ? 1500 : _ref$leaveTouchDelay,\n    title = _ref.title,\n    themeOverrides = _ref.theme,\n    titleMaxFontSizeMultiplier = _ref.titleMaxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var isWeb = Platform.OS === 'web';\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useState3 = React.useState({\n      children: {},\n      tooltip: {},\n      measured: false\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    measurement = _React$useState4[0],\n    setMeasurement = _React$useState4[1];\n  var showTooltipTimer = React.useRef([]);\n  var hideTooltipTimer = React.useRef([]);\n  var childrenWrapperRef = React.useRef();\n  var touched = React.useRef(false);\n  React.useEffect(function () {\n    return function () {\n      if (showTooltipTimer.current.length) {\n        showTooltipTimer.current.forEach(function (t) {\n          return clearTimeout(t);\n        });\n        showTooltipTimer.current = [];\n      }\n      if (hideTooltipTimer.current.length) {\n        hideTooltipTimer.current.forEach(function (t) {\n          return clearTimeout(t);\n        });\n        hideTooltipTimer.current = [];\n      }\n    };\n  }, []);\n  React.useEffect(function () {\n    var subscription = addEventListener(Dimensions, 'change', function () {\n      return setVisible(false);\n    });\n    return function () {\n      return subscription.remove();\n    };\n  }, []);\n  var handleOnLayout = function handleOnLayout(_ref2) {\n    var layout = _ref2.nativeEvent.layout;\n    childrenWrapperRef.current.measure(function (_x, _y, width, height, pageX, pageY) {\n      setMeasurement({\n        children: {\n          pageX: pageX,\n          pageY: pageY,\n          height: height,\n          width: width\n        },\n        tooltip: _objectSpread({}, layout),\n        measured: true\n      });\n    });\n  };\n  var handleTouchStart = function handleTouchStart() {\n    if (hideTooltipTimer.current.length) {\n      hideTooltipTimer.current.forEach(function (t) {\n        return clearTimeout(t);\n      });\n      hideTooltipTimer.current = [];\n    }\n    if (isWeb) {\n      var id = setTimeout(function () {\n        touched.current = true;\n        setVisible(true);\n      }, enterTouchDelay);\n      showTooltipTimer.current.push(id);\n    } else {\n      touched.current = true;\n      setVisible(true);\n    }\n  };\n  var handleTouchEnd = function handleTouchEnd() {\n    touched.current = false;\n    if (showTooltipTimer.current.length) {\n      showTooltipTimer.current.forEach(function (t) {\n        return clearTimeout(t);\n      });\n      showTooltipTimer.current = [];\n    }\n    var id = setTimeout(function () {\n      setVisible(false);\n      setMeasurement({\n        children: {},\n        tooltip: {},\n        measured: false\n      });\n    }, leaveTouchDelay);\n    hideTooltipTimer.current.push(id);\n  };\n  var mobilePressProps = {\n    onPress: React.useCallback(function () {\n      if (touched.current) {\n        return null;\n      } else {\n        var _children$props$onPre, _children$props;\n        if (children.props.disabled) return null;\n        return (_children$props$onPre = (_children$props = children.props).onPress) === null || _children$props$onPre === void 0 ? void 0 : _children$props$onPre.call(_children$props);\n      }\n    }, [children.props]),\n    onLongPress: function onLongPress() {\n      return handleTouchStart();\n    },\n    onPressOut: function onPressOut() {\n      return handleTouchEnd();\n    },\n    delayLongPress: enterTouchDelay\n  };\n  var webPressProps = {\n    onHoverIn: function onHoverIn() {\n      var _children$props$onHov, _children$props2;\n      handleTouchStart();\n      (_children$props$onHov = (_children$props2 = children.props).onHoverIn) === null || _children$props$onHov === void 0 ? void 0 : _children$props$onHov.call(_children$props2);\n    },\n    onHoverOut: function onHoverOut() {\n      var _children$props$onHov2, _children$props3;\n      handleTouchEnd();\n      (_children$props$onHov2 = (_children$props3 = children.props).onHoverOut) === null || _children$props$onHov2 === void 0 ? void 0 : _children$props$onHov2.call(_children$props3);\n    }\n  };\n  return React.createElement(React.Fragment, null, visible && React.createElement(Portal, null, React.createElement(View, {\n    onLayout: handleOnLayout,\n    style: [styles.tooltip, _objectSpread(_objectSpread({\n      backgroundColor: theme.isV3 ? theme.colors.onSurface : theme.colors.tooltip\n    }, getTooltipPosition(measurement, children)), {}, {\n      borderRadius: theme.roundness\n    }, measurement.measured ? styles.visible : styles.hidden)],\n    testID: \"tooltip-container\"\n  }, React.createElement(Text, {\n    accessibilityLiveRegion: \"polite\",\n    numberOfLines: 1,\n    selectable: false,\n    variant: \"labelLarge\",\n    style: {\n      color: theme.colors.surface\n    },\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }, title))), React.createElement(Pressable, _extends({\n    ref: childrenWrapperRef,\n    style: styles.pressContainer\n  }, isWeb ? webPressProps : mobilePressProps), React.cloneElement(children, _objectSpread(_objectSpread({}, rest), isWeb ? webPressProps : mobilePressProps))));\n};\nTooltip.displayName = 'Tooltip';\nvar styles = StyleSheet.create({\n  tooltip: {\n    alignSelf: 'flex-start',\n    justifyContent: 'center',\n    paddingHorizontal: 16,\n    height: 32,\n    maxHeight: 32\n  },\n  visible: {\n    opacity: 1\n  },\n  hidden: {\n    opacity: 0\n  },\n  pressContainer: _objectSpread({}, Platform.OS === 'web' && {\n    cursor: 'default'\n  })\n});\nexport default Tooltip;", "map": {"version": 3, "names": ["React", "Dimensions", "View", "StyleSheet", "Platform", "Pressable", "getTooltipPosition", "useInternalTheme", "addEventListener", "Portal", "Text", "<PERSON><PERSON><PERSON>", "_ref", "children", "_ref$enterTouchDelay", "enterTouchDelay", "_ref$leaveTouchDelay", "leaveTouchDelay", "title", "themeOverrides", "theme", "titleMaxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "isWeb", "OS", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "visible", "setVisible", "_React$useState3", "tooltip", "measured", "_React$useState4", "measurement", "setMeasurement", "showTooltipTimer", "useRef", "hideTooltipTimer", "childrenWrapperRef", "touched", "useEffect", "current", "length", "for<PERSON>ach", "t", "clearTimeout", "subscription", "remove", "handleOnLayout", "_ref2", "layout", "nativeEvent", "measure", "_x", "_y", "width", "height", "pageX", "pageY", "_objectSpread", "handleTouchStart", "id", "setTimeout", "push", "handleTouchEnd", "mobilePressProps", "onPress", "useCallback", "_children$props$onPre", "_children$props", "props", "disabled", "call", "onLongPress", "onPressOut", "delayLongPress", "webPressProps", "onHoverIn", "_children$props$onHov", "_children$props2", "onHoverOut", "_children$props$onHov2", "_children$props3", "createElement", "Fragment", "onLayout", "style", "styles", "backgroundColor", "isV3", "colors", "onSurface", "borderRadius", "roundness", "hidden", "testID", "accessibilityLiveRegion", "numberOfLines", "selectable", "variant", "color", "surface", "maxFontSizeMultiplier", "_extends", "ref", "pressContainer", "cloneElement", "displayName", "create", "alignSelf", "justifyContent", "paddingHorizontal", "maxHeight", "opacity", "cursor"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Tooltip/Tooltip.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Dimensions,\n  View,\n  LayoutChangeEvent,\n  StyleSheet,\n  Platform,\n  Pressable,\n  ViewStyle,\n} from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { getTooltipPosition, Measurement } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport { addEventListener } from '../../utils/addEventListener';\nimport Portal from '../Portal/Portal';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Tooltip reference element. Needs to be able to hold a ref.\n   */\n  children: React.ReactElement;\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   */\n  enterTouchDelay?: number;\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   */\n  leaveTouchDelay?: number;\n  /**\n   * Tooltip title\n   */\n  title: string;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Tooltips display informative text when users hover over, focus on, or tap an element.\n *\n * Plain tooltips, when activated, display a text label identifying an element, such as a description of its function. Tooltips should include only short, descriptive text and avoid restating visible UI text.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { IconButton, Tooltip } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Tooltip title=\"Selected Camera\">\n *     <IconButton icon=\"camera\" selected size={24} onPress={() => {}} />\n *   </Tooltip>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Tooltip = ({\n  children,\n  enterTouchDelay = 500,\n  leaveTouchDelay = 1500,\n  title,\n  theme: themeOverrides,\n  titleMaxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const isWeb = Platform.OS === 'web';\n\n  const theme = useInternalTheme(themeOverrides);\n  const [visible, setVisible] = React.useState(false);\n\n  const [measurement, setMeasurement] = React.useState({\n    children: {},\n    tooltip: {},\n    measured: false,\n  });\n  const showTooltipTimer = React.useRef<NodeJS.Timeout[]>([]);\n  const hideTooltipTimer = React.useRef<NodeJS.Timeout[]>([]);\n  const childrenWrapperRef = React.useRef() as React.MutableRefObject<View>;\n  const touched = React.useRef(false);\n\n  React.useEffect(() => {\n    return () => {\n      if (showTooltipTimer.current.length) {\n        showTooltipTimer.current.forEach((t) => clearTimeout(t));\n        showTooltipTimer.current = [];\n      }\n\n      if (hideTooltipTimer.current.length) {\n        hideTooltipTimer.current.forEach((t) => clearTimeout(t));\n        hideTooltipTimer.current = [];\n      }\n    };\n  }, []);\n\n  React.useEffect(() => {\n    const subscription = addEventListener(Dimensions, 'change', () =>\n      setVisible(false)\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  const handleOnLayout = ({ nativeEvent: { layout } }: LayoutChangeEvent) => {\n    childrenWrapperRef.current.measure(\n      (_x, _y, width, height, pageX, pageY) => {\n        setMeasurement({\n          children: { pageX, pageY, height, width },\n          tooltip: { ...layout },\n          measured: true,\n        });\n      }\n    );\n  };\n\n  const handleTouchStart = () => {\n    if (hideTooltipTimer.current.length) {\n      hideTooltipTimer.current.forEach((t) => clearTimeout(t));\n      hideTooltipTimer.current = [];\n    }\n\n    if (isWeb) {\n      let id = setTimeout(() => {\n        touched.current = true;\n        setVisible(true);\n      }, enterTouchDelay) as unknown as NodeJS.Timeout;\n      showTooltipTimer.current.push(id);\n    } else {\n      touched.current = true;\n      setVisible(true);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    touched.current = false;\n    if (showTooltipTimer.current.length) {\n      showTooltipTimer.current.forEach((t) => clearTimeout(t));\n      showTooltipTimer.current = [];\n    }\n\n    let id = setTimeout(() => {\n      setVisible(false);\n      setMeasurement({ children: {}, tooltip: {}, measured: false });\n    }, leaveTouchDelay) as unknown as NodeJS.Timeout;\n    hideTooltipTimer.current.push(id);\n  };\n\n  const mobilePressProps = {\n    onPress: React.useCallback(() => {\n      if (touched.current) {\n        return null;\n      } else {\n        if (children.props.disabled) return null;\n        return children.props.onPress?.();\n      }\n    }, [children.props]),\n    onLongPress: () => handleTouchStart(),\n    onPressOut: () => handleTouchEnd(),\n    delayLongPress: enterTouchDelay,\n  };\n\n  const webPressProps = {\n    onHoverIn: () => {\n      handleTouchStart();\n      children.props.onHoverIn?.();\n    },\n    onHoverOut: () => {\n      handleTouchEnd();\n      children.props.onHoverOut?.();\n    },\n  };\n\n  return (\n    <>\n      {visible && (\n        <Portal>\n          <View\n            onLayout={handleOnLayout}\n            style={[\n              styles.tooltip,\n              {\n                backgroundColor: theme.isV3\n                  ? theme.colors.onSurface\n                  : theme.colors.tooltip,\n                ...getTooltipPosition(measurement as Measurement, children),\n                borderRadius: theme.roundness,\n                ...(measurement.measured ? styles.visible : styles.hidden),\n              },\n            ]}\n            testID=\"tooltip-container\"\n          >\n            <Text\n              accessibilityLiveRegion=\"polite\"\n              numberOfLines={1}\n              selectable={false}\n              variant=\"labelLarge\"\n              style={{ color: theme.colors.surface }}\n              maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n            >\n              {title}\n            </Text>\n          </View>\n        </Portal>\n      )}\n      {/* Need the xxPressProps in both places */}\n      <Pressable\n        ref={childrenWrapperRef}\n        style={styles.pressContainer}\n        {...(isWeb ? webPressProps : mobilePressProps)}\n      >\n        {React.cloneElement(children, {\n          ...rest,\n          ...(isWeb ? webPressProps : mobilePressProps),\n        })}\n      </Pressable>\n    </>\n  );\n};\n\nTooltip.displayName = 'Tooltip';\n\nconst styles = StyleSheet.create({\n  tooltip: {\n    alignSelf: 'flex-start',\n    justifyContent: 'center',\n    paddingHorizontal: 16,\n    height: 32,\n    maxHeight: 32,\n  },\n  visible: {\n    opacity: 1,\n  },\n  hidden: {\n    opacity: 0,\n  },\n  pressContainer: {\n    ...(Platform.OS === 'web' && { cursor: 'default' }),\n  } as ViewStyle,\n});\n\nexport default Tooltip;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,SAAA;AAa9B,SAASC,kBAAkB;AAC3B,SAASC,gBAAgB;AACzB,SAASC,gBAAgB;AACzB,OAAOC,MAAM;AACb,OAAOC,IAAI;AAgDX,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAGC,IAAA,EAQH;EAAA,IAPXC,QAAQ,GAOFD,IAAA,CAPNC,QAAQ;IAAAC,oBAAA,GAOFF,IAAA,CANNG,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,GAAG,GAAAA,oBAAA;IAAAE,oBAAA,GAMfJ,IAAA,CALNK,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,IAAI,GAAAA,oBAAA;IACtBE,KAAK,GAICN,IAAA,CAJNM,KAAK;IACEC,cAAc,GAGfP,IAAA,CAHNQ,KAAK;IACLC,0BAA0B,GAEpBT,IAAA,CAFNS,0BAA0B;IACvBC,IAAA,GAAAC,wBAAA,CACGX,IAAA,EAAAY,SAAA;EACN,IAAMC,KAAK,GAAGrB,QAAQ,CAACsB,EAAE,KAAK,KAAK;EAEnC,IAAMN,KAAK,GAAGb,gBAAgB,CAACY,cAAc,CAAC;EAC9C,IAAAQ,eAAA,GAA8B3B,KAAK,CAAC4B,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA5CI,OAAO,GAAAF,gBAAA;IAAEG,UAAU,GAAAH,gBAAA;EAE1B,IAAAI,gBAAA,GAAsCjC,KAAK,CAAC4B,QAAQ,CAAC;MACnDf,QAAQ,EAAE,CAAC,CAAC;MACZqB,OAAO,EAAE,CAAC,CAAC;MACXC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAAAC,gBAAA,GAAAN,cAAA,CAAAG,gBAAA;IAJKI,WAAW,GAAAD,gBAAA;IAAEE,cAAc,GAAAF,gBAAA;EAKlC,IAAMG,gBAAgB,GAAGvC,KAAK,CAACwC,MAAM,CAAmB,EAAE,CAAC;EAC3D,IAAMC,gBAAgB,GAAGzC,KAAK,CAACwC,MAAM,CAAmB,EAAE,CAAC;EAC3D,IAAME,kBAAkB,GAAG1C,KAAK,CAACwC,MAAM,CAAC,CAAiC;EACzE,IAAMG,OAAO,GAAG3C,KAAK,CAACwC,MAAM,CAAC,KAAK,CAAC;EAEnCxC,KAAK,CAAC4C,SAAS,CAAC,YAAM;IACpB,OAAO,YAAM;MACX,IAAIL,gBAAgB,CAACM,OAAO,CAACC,MAAM,EAAE;QACnCP,gBAAgB,CAACM,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;UAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;QAAA,EAAC;QACxDT,gBAAgB,CAACM,OAAO,GAAG,EAAE;MAC/B;MAEA,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,MAAM,EAAE;QACnCL,gBAAgB,CAACI,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;UAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;QAAA,EAAC;QACxDP,gBAAgB,CAACI,OAAO,GAAG,EAAE;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN7C,KAAK,CAAC4C,SAAS,CAAC,YAAM;IACpB,IAAMM,YAAY,GAAG1C,gBAAgB,CAACP,UAAU,EAAE,QAAQ,EAAE;MAAA,OAC1D+B,UAAU,CAAC,KAAK,CAClB;IAAA,EAAC;IAED,OAAO;MAAA,OAAMkB,YAAY,CAACC,MAAM,CAAC,CAAC;IAAA;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,KAAA,EAAoD;IAAA,IAAlCC,MAAA,GAA6BD,KAAA,CAA5CE,WAAW,CAAID,MAAA;IACvCZ,kBAAkB,CAACG,OAAO,CAACW,OAAO,CAChC,UAACC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAK;MACvCxB,cAAc,CAAC;QACbzB,QAAQ,EAAE;UAAEgD,KAAK,EAALA,KAAK;UAAEC,KAAK,EAALA,KAAK;UAAEF,MAAM,EAANA,MAAM;UAAED,KAAA,EAAAA;QAAM,CAAC;QACzCzB,OAAO,EAAA6B,aAAA,KAAOT,MAAA,CAAQ;QACtBnB,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CACF,CAAC;EACH,CAAC;EAED,IAAM6B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7B,IAAIvB,gBAAgB,CAACI,OAAO,CAACC,MAAM,EAAE;MACnCL,gBAAgB,CAACI,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;QAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;MAAA,EAAC;MACxDP,gBAAgB,CAACI,OAAO,GAAG,EAAE;IAC/B;IAEA,IAAIpB,KAAK,EAAE;MACT,IAAIwC,EAAE,GAAGC,UAAU,CAAC,YAAM;QACxBvB,OAAO,CAACE,OAAO,GAAG,IAAI;QACtBb,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,EAAEjB,eAAe,CAA8B;MAChDwB,gBAAgB,CAACM,OAAO,CAACsB,IAAI,CAACF,EAAE,CAAC;IACnC,CAAC,MAAM;MACLtB,OAAO,CAACE,OAAO,GAAG,IAAI;MACtBb,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC;EAED,IAAMoC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3BzB,OAAO,CAACE,OAAO,GAAG,KAAK;IACvB,IAAIN,gBAAgB,CAACM,OAAO,CAACC,MAAM,EAAE;MACnCP,gBAAgB,CAACM,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;QAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;MAAA,EAAC;MACxDT,gBAAgB,CAACM,OAAO,GAAG,EAAE;IAC/B;IAEA,IAAIoB,EAAE,GAAGC,UAAU,CAAC,YAAM;MACxBlC,UAAU,CAAC,KAAK,CAAC;MACjBM,cAAc,CAAC;QAAEzB,QAAQ,EAAE,CAAC,CAAC;QAAEqB,OAAO,EAAE,CAAC,CAAC;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC;IAChE,CAAC,EAAElB,eAAe,CAA8B;IAChDwB,gBAAgB,CAACI,OAAO,CAACsB,IAAI,CAACF,EAAE,CAAC;EACnC,CAAC;EAED,IAAMI,gBAAgB,GAAG;IACvBC,OAAO,EAAEtE,KAAK,CAACuE,WAAW,CAAC,YAAM;MAC/B,IAAI5B,OAAO,CAACE,OAAO,EAAE;QACnB,OAAO,IAAI;MACb,CAAC,MAAM;QAAA,IAAA2B,qBAAA,EAAAC,eAAA;QACL,IAAI5D,QAAQ,CAAC6D,KAAK,CAACC,QAAQ,EAAE,OAAO,IAAI;QACxC,QAAAH,qBAAA,GAAO,CAAAC,eAAA,GAAA5D,QAAQ,CAAC6D,KAAK,EAACJ,OAAO,cAAAE,qBAAA,uBAAtBA,qBAAA,CAAAI,IAAA,CAAAH,eAAyB,CAAC;MACnC;IACF,CAAC,EAAE,CAAC5D,QAAQ,CAAC6D,KAAK,CAAC,CAAC;IACpBG,WAAW,EAAE,SAAbA,WAAWA,CAAA;MAAA,OAAQb,gBAAgB,CAAC,CAAC;IAAA;IACrCc,UAAU,EAAE,SAAZA,UAAUA,CAAA;MAAA,OAAQV,cAAc,CAAC,CAAC;IAAA;IAClCW,cAAc,EAAEhE;EAClB,CAAC;EAED,IAAMiE,aAAa,GAAG;IACpBC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;MAAA,IAAAC,qBAAA,EAAAC,gBAAA;MACfnB,gBAAgB,CAAC,CAAC;MAClB,CAAAkB,qBAAA,IAAAC,gBAAA,GAAAtE,QAAQ,CAAC6D,KAAK,EAACO,SAAS,cAAAC,qBAAA,uBAAxBA,qBAAA,CAAAN,IAAA,CAAAO,gBAA2B,CAAC;IAC9B,CAAC;IACDC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAA,IAAAC,sBAAA,EAAAC,gBAAA;MAChBlB,cAAc,CAAC,CAAC;MAChB,CAAAiB,sBAAA,IAAAC,gBAAA,GAAAzE,QAAQ,CAAC6D,KAAK,EAACU,UAAU,cAAAC,sBAAA,uBAAzBA,sBAAA,CAAAT,IAAA,CAAAU,gBAA4B,CAAC;IAC/B;EACF,CAAC;EAED,OACEtF,KAAA,CAAAuF,aAAA,CAAAvF,KAAA,CAAAwF,QAAA,QACGzD,OAAO,IACN/B,KAAA,CAAAuF,aAAA,CAAC9E,MAAM,QACLT,KAAA,CAAAuF,aAAA,CAACrF,IAAI;IACHuF,QAAQ,EAAErC,cAAe;IACzBsC,KAAK,EAAE,CACLC,MAAM,CAACzD,OAAO,EAAA6B,aAAA,CAAAA,aAAA;MAEZ6B,eAAe,EAAExE,KAAK,CAACyE,IAAI,GACvBzE,KAAK,CAAC0E,MAAM,CAACC,SAAS,GACtB3E,KAAK,CAAC0E,MAAM,CAAC5D;IAAO,GACrB5B,kBAAkB,CAAC+B,WAAW,EAAiBxB,QAAQ,CAAC;MAC3DmF,YAAY,EAAE5E,KAAK,CAAC6E;IAAS,GACzB5D,WAAW,CAACF,QAAQ,GAAGwD,MAAM,CAAC5D,OAAO,GAAG4D,MAAM,CAACO,MAAM,EAE3D;IACFC,MAAM,EAAC;EAAmB,GAE1BnG,KAAA,CAAAuF,aAAA,CAAC7E,IAAI;IACH0F,uBAAuB,EAAC,QAAQ;IAChCC,aAAa,EAAE,CAAE;IACjBC,UAAU,EAAE,KAAM;IAClBC,OAAO,EAAC,YAAY;IACpBb,KAAK,EAAE;MAAEc,KAAK,EAAEpF,KAAK,CAAC0E,MAAM,CAACW;IAAQ,CAAE;IACvCC,qBAAqB,EAAErF;EAA2B,GAEjDH,KACG,CACF,CACA,CACT,EAEDlB,KAAA,CAAAuF,aAAA,CAAClF,SAAS,EAAAsG,QAAA;IACRC,GAAG,EAAElE,kBAAmB;IACxBgD,KAAK,EAAEC,MAAM,CAACkB;EAAe,GACxBpF,KAAK,GAAGuD,aAAa,GAAGX,gBAAgB,GAE5CrE,KAAK,CAAC8G,YAAY,CAACjG,QAAQ,EAAAkD,aAAA,CAAAA,aAAA,KACvBzC,IAAI,GACHG,KAAK,GAAGuD,aAAa,GAAGX,gBAAgB,CAC7C,CACQ,CACX,CAAC;AAEP,CAAC;AAED1D,OAAO,CAACoG,WAAW,GAAG,SAAS;AAE/B,IAAMpB,MAAM,GAAGxF,UAAU,CAAC6G,MAAM,CAAC;EAC/B9E,OAAO,EAAE;IACP+E,SAAS,EAAE,YAAY;IACvBC,cAAc,EAAE,QAAQ;IACxBC,iBAAiB,EAAE,EAAE;IACrBvD,MAAM,EAAE,EAAE;IACVwD,SAAS,EAAE;EACb,CAAC;EACDrF,OAAO,EAAE;IACPsF,OAAO,EAAE;EACX,CAAC;EACDnB,MAAM,EAAE;IACNmB,OAAO,EAAE;EACX,CAAC;EACDR,cAAc,EAAA9C,aAAA,KACR3D,QAAQ,CAACsB,EAAE,KAAK,KAAK,IAAI;IAAE4F,MAAM,EAAE;EAAU,CAAC;AAEtD,CAAC,CAAC;AAEF,eAAe3G,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}