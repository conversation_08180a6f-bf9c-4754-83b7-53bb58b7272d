{"ast": null, "code": "import { Asset } from \"./Asset\";\nimport { IS_ENV_WITH_UPDATES_ENABLED } from \"./PlatformUtils\";\nimport { setCustomSourceTransformer } from \"./resolveAssetSource\";\nif (IS_ENV_WITH_UPDATES_ENABLED) {\n  setCustomSourceTransformer(function (resolver) {\n    try {\n      if (resolver.asset.fileHashes) {\n        var asset = Asset.fromMetadata(resolver.asset);\n        return resolver.fromSource(asset.downloaded ? asset.localUri : asset.uri);\n      } else {\n        return resolver.defaultAsset();\n      }\n    } catch (_unused) {\n      return resolver.defaultAsset();\n    }\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "IS_ENV_WITH_UPDATES_ENABLED", "setCustomSourceTransformer", "resolver", "asset", "fileHashes", "fromMetadata", "fromSource", "downloaded", "localUri", "uri", "defaultAsset", "_unused"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-asset/src/Asset.fx.ts"], "sourcesContent": ["import { Asset } from './Asset';\nimport { IS_ENV_WITH_UPDATES_ENABLED } from './PlatformUtils';\nimport { setCustomSourceTransformer } from './resolveAssetSource';\n\n// Override React Native's asset resolution for `Image` components in contexts where it matters\nif (IS_ENV_WITH_UPDATES_ENABLED) {\n  setCustomSourceTransformer((resolver) => {\n    try {\n      // B<PERSON><PERSON> is using the hashAssetFiles plugin if and only if the fileHashes property exists\n      if (resolver.asset.fileHashes) {\n        const asset = Asset.fromMetadata(resolver.asset);\n        return resolver.fromSource(asset.downloaded ? asset.localUri! : asset.uri);\n      } else {\n        return resolver.defaultAsset();\n      }\n    } catch {\n      return resolver.defaultAsset();\n    }\n  });\n}\n"], "mappings": "AAAA,SAASA,KAAK;AACd,SAASC,2BAA2B;AACpC,SAASC,0BAA0B;AAGnC,IAAID,2BAA2B,EAAE;EAC/BC,0BAA0B,CAAC,UAACC,QAAQ,EAAI;IACtC,IAAI;MAEF,IAAIA,QAAQ,CAACC,KAAK,CAACC,UAAU,EAAE;QAC7B,IAAMD,KAAK,GAAGJ,KAAK,CAACM,YAAY,CAACH,QAAQ,CAACC,KAAK,CAAC;QAChD,OAAOD,QAAQ,CAACI,UAAU,CAACH,KAAK,CAACI,UAAU,GAAGJ,KAAK,CAACK,QAAS,GAAGL,KAAK,CAACM,GAAG,CAAC;OAC3E,MAAM;QACL,OAAOP,QAAQ,CAACQ,YAAY,EAAE;;KAEjC,CAAC,OAAAC,OAAA,EAAM;MACN,OAAOT,QAAQ,CAACQ,YAAY,EAAE;;EAElC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}