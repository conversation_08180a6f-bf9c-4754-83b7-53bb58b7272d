{"ast": null, "code": "var unitlessNumbers = {\n  animationIterationCount: true,\n  aspectRatio: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  flex: true,\n  flexGrow: true,\n  flexOrder: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  fontWeight: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowGap: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnGap: true,\n  gridColumnStart: true,\n  lineClamp: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true,\n  scale: true,\n  scaleX: true,\n  scaleY: true,\n  scaleZ: true,\n  shadowOpacity: true\n};\nvar prefixes = ['ms', 'Moz', 'O', 'Webkit'];\nvar prefixKey = function prefixKey(prefix, key) {\n  return prefix + key.charAt(0).toUpperCase() + key.substring(1);\n};\nObject.keys(unitlessNumbers).forEach(function (prop) {\n  prefixes.forEach(function (prefix) {\n    unitlessNumbers[prefixKey(prefix, prop)] = unitlessNumbers[prop];\n  });\n});\nexport default unitlessNumbers;", "map": {"version": 3, "names": ["unitlessNumbers", "animationIterationCount", "aspectRatio", "borderImageOutset", "borderImageSlice", "borderImageWidth", "boxFlex", "boxFlexGroup", "boxOrdinalGroup", "columnCount", "flex", "flexGrow", "flexOrder", "flexPositive", "flexShrink", "flexNegative", "fontWeight", "gridRow", "gridRowEnd", "gridRowGap", "gridRowStart", "gridColumn", "gridColumnEnd", "gridColumnGap", "gridColumnStart", "lineClamp", "opacity", "order", "orphans", "tabSize", "widows", "zIndex", "zoom", "fillOpacity", "floodOpacity", "stopOpacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeMiterlimit", "strokeOpacity", "strokeWidth", "scale", "scaleX", "scaleY", "scaleZ", "shadowOpacity", "prefixes", "prefixKey", "prefix", "key", "char<PERSON>t", "toUpperCase", "substring", "Object", "keys", "for<PERSON>ach", "prop"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/modules/unitlessNumbers/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar unitlessNumbers = {\n  animationIterationCount: true,\n  aspectRatio: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  flex: true,\n  flexGrow: true,\n  flexOrder: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  fontWeight: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowGap: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnGap: true,\n  gridColumnStart: true,\n  lineClamp: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true,\n  // transform types\n  scale: true,\n  scaleX: true,\n  scaleY: true,\n  scaleZ: true,\n  // RN properties\n  shadowOpacity: true\n};\n\n/**\n * Support style names that may come passed in prefixed by adding permutations\n * of vendor prefixes.\n */\nvar prefixes = ['ms', 'Moz', 'O', 'Webkit'];\nvar prefixKey = (prefix, key) => {\n  return prefix + key.charAt(0).toUpperCase() + key.substring(1);\n};\nObject.keys(unitlessNumbers).forEach(prop => {\n  prefixes.forEach(prefix => {\n    unitlessNumbers[prefixKey(prefix, prop)] = unitlessNumbers[prop];\n  });\n});\nexport default unitlessNumbers;"], "mappings": "AAUA,IAAIA,eAAe,GAAG;EACpBC,uBAAuB,EAAE,IAAI;EAC7BC,WAAW,EAAE,IAAI;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,IAAI;EACrBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,IAAI;EAEVC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,IAAI;EAEjBC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EAEZC,aAAa,EAAE;AACjB,CAAC;AAMD,IAAIC,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC3C,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAIC,MAAM,EAAEC,GAAG,EAAK;EAC/B,OAAOD,MAAM,GAAGC,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,SAAS,CAAC,CAAC,CAAC;AAChE,CAAC;AACDC,MAAM,CAACC,IAAI,CAACtD,eAAe,CAAC,CAACuD,OAAO,CAAC,UAAAC,IAAI,EAAI;EAC3CV,QAAQ,CAACS,OAAO,CAAC,UAAAP,MAAM,EAAI;IACzBhD,eAAe,CAAC+C,SAAS,CAACC,MAAM,EAAEQ,IAAI,CAAC,CAAC,GAAGxD,eAAe,CAACwD,IAAI,CAAC;EAClE,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,eAAexD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}