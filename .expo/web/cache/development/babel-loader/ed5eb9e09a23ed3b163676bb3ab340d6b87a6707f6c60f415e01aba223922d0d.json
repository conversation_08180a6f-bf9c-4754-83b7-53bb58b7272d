{"ast": null, "code": "export { default } from \"react-native-web/dist/exports/DeviceEventEmitter\";", "map": {"version": 3, "names": [], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-modules-core/src/SyntheticPlatformEmitter.ts"], "sourcesContent": ["/**\n * This emitter is used for sending synthetic native events to listeners\n * registered in the API layer with `NativeEventEmitter`.\n */\nexport { DeviceEventEmitter as default } from 'react-native';\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}