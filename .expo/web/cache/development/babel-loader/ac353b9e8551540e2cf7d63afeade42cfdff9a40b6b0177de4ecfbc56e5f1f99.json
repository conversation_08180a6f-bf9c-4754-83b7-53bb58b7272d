{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport { Card, Text, IconButton, Chip, TouchableRipple, Button } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { saveFood } from \"../services/databaseService\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar FoodItem = function FoodItem(_ref) {\n  var _food$nutrients, _food$nutrients$find, _food$nutrients2, _food$nutrients2$find, _food$nutrients3, _food$nutrients3$find, _food$nutrients4, _food$nutrients4$find;\n  var food = _ref.food,\n    onSelect = _ref.onSelect,\n    _ref$showDetails = _ref.showDetails,\n    showDetails = _ref$showDetails === void 0 ? false : _ref$showDetails,\n    onEdit = _ref.onEdit;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _useState = useState(showDetails),\n    _useState2 = _slicedToArray(_useState, 2),\n    expanded = _useState2[0],\n    setExpanded = _useState2[1];\n  var _useState3 = useState(food.is_favorite === 1),\n    _useState4 = _slicedToArray(_useState3, 2),\n    isFavorite = _useState4[0],\n    setIsFavorite = _useState4[1];\n  var calories = ((_food$nutrients = food.nutrients) == null ? void 0 : (_food$nutrients$find = _food$nutrients.find(function (n) {\n    return n.name === 'Calories';\n  })) == null ? void 0 : _food$nutrients$find.amount) || 0;\n  var protein = ((_food$nutrients2 = food.nutrients) == null ? void 0 : (_food$nutrients2$find = _food$nutrients2.find(function (n) {\n    return n.name === 'Protein';\n  })) == null ? void 0 : _food$nutrients2$find.amount) || 0;\n  var carbs = ((_food$nutrients3 = food.nutrients) == null ? void 0 : (_food$nutrients3$find = _food$nutrients3.find(function (n) {\n    return n.name === 'Carbohydrates';\n  })) == null ? void 0 : _food$nutrients3$find.amount) || 0;\n  var fat = ((_food$nutrients4 = food.nutrients) == null ? void 0 : (_food$nutrients4$find = _food$nutrients4.find(function (n) {\n    return n.name === 'Fat';\n  })) == null ? void 0 : _food$nutrients4$find.amount) || 0;\n  var toggleFavorite = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      try {\n        var newFavoriteStatus = !isFavorite;\n        setIsFavorite(newFavoriteStatus);\n        yield saveFood(_objectSpread(_objectSpread({}, food), {}, {\n          is_favorite: newFavoriteStatus ? 1 : 0\n        }));\n      } catch (error) {\n        console.error('Error toggling favorite:', error);\n        setIsFavorite(!isFavorite);\n      }\n    });\n    return function toggleFavorite() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  return _jsxs(Card, {\n    style: styles.card,\n    mode: \"outlined\",\n    children: [_jsx(TouchableRipple, {\n      onPress: function onPress() {\n        return setExpanded(!expanded);\n      },\n      children: _jsx(Card.Title, {\n        title: food.name,\n        subtitle: food.brand || 'No brand',\n        left: function left(props) {\n          return food.image_url ? _jsx(Image, {\n            source: {\n              uri: food.image_url\n            },\n            style: styles.image\n          }) : _jsx(IconButton, _objectSpread(_objectSpread({}, props), {}, {\n            icon: \"food\"\n          }));\n        },\n        right: function right(props) {\n          return _jsx(IconButton, _objectSpread(_objectSpread({}, props), {}, {\n            icon: isFavorite ? 'star' : 'star-outline',\n            iconColor: isFavorite ? theme.colors.primary : theme.colors.onSurface,\n            onPress: toggleFavorite\n          }));\n        }\n      })\n    }), _jsxs(Card.Content, {\n      children: [_jsxs(View, {\n        style: styles.macrosContainer,\n        children: [_jsxs(Chip, {\n          icon: \"fire\",\n          style: styles.macroChip,\n          children: [calories, \" kcal\"]\n        }), _jsxs(Chip, {\n          icon: \"protein\",\n          style: styles.macroChip,\n          children: [protein, \"g\"]\n        }), _jsxs(Chip, {\n          icon: \"grain\",\n          style: styles.macroChip,\n          children: [carbs, \"g\"]\n        }), _jsxs(Chip, {\n          icon: \"oil\",\n          style: styles.macroChip,\n          children: [fat, \"g\"]\n        })]\n      }), expanded && _jsxs(View, {\n        style: styles.detailsContainer,\n        children: [food.description && _jsx(Text, {\n          style: styles.description,\n          children: food.description\n        }), food.barcode && _jsxs(Text, {\n          style: styles.barcode,\n          children: [\"Barcode: \", food.barcode]\n        }), _jsxs(Text, {\n          style: styles.servingSize,\n          children: [\"Serving size: \", food.serving_size, \" \", food.serving_unit]\n        }), food.nutrients && food.nutrients.length > 0 && _jsxs(View, {\n          style: styles.nutrientsContainer,\n          children: [_jsx(Text, {\n            style: styles.sectionTitle,\n            children: \"Nutrition Facts\"\n          }), food.nutrients.map(function (nutrient) {\n            return _jsxs(View, {\n              style: styles.nutrientRow,\n              children: [_jsx(Text, {\n                children: nutrient.name\n              }), _jsxs(Text, {\n                children: [nutrient.amount, \" \", nutrient.unit]\n              })]\n            }, nutrient.nutrient_id);\n          })]\n        }), food.ingredients && food.ingredients.length > 0 && _jsxs(View, {\n          style: styles.ingredientsContainer,\n          children: [_jsx(Text, {\n            style: styles.sectionTitle,\n            children: \"Ingredients\"\n          }), _jsx(View, {\n            style: styles.ingredientChips,\n            children: food.ingredients.map(function (ingredient) {\n              return _jsx(Chip, {\n                style: [styles.ingredientChip, ingredient.is_allergen === 1 && styles.allergenChip],\n                textStyle: ingredient.is_allergen === 1 ? styles.allergenText : null,\n                children: ingredient.name\n              }, ingredient.ingredient_id);\n            })\n          })]\n        })]\n      })]\n    }), _jsxs(Card.Actions, {\n      children: [onSelect && _jsx(Button, {\n        onPress: function onPress() {\n          return onSelect(food);\n        },\n        children: \"Select\"\n      }), onEdit && _jsx(Button, {\n        onPress: function onPress() {\n          return onEdit(food);\n        },\n        children: \"Edit\"\n      }), _jsx(Button, {\n        onPress: function onPress() {\n          return setExpanded(!expanded);\n        },\n        children: expanded ? 'Hide Details' : 'Show Details'\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  card: {\n    marginVertical: 8,\n    marginHorizontal: 16,\n    elevation: 2\n  },\n  image: {\n    width: 40,\n    height: 40,\n    borderRadius: 20\n  },\n  macrosContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    marginTop: 8\n  },\n  macroChip: {\n    marginRight: 8,\n    marginBottom: 8\n  },\n  detailsContainer: {\n    marginTop: 16\n  },\n  description: {\n    marginBottom: 8\n  },\n  barcode: {\n    marginBottom: 8,\n    fontStyle: 'italic'\n  },\n  servingSize: {\n    marginBottom: 16\n  },\n  sectionTitle: {\n    fontWeight: 'bold',\n    fontSize: 16,\n    marginBottom: 8,\n    marginTop: 16\n  },\n  nutrientsContainer: {\n    marginBottom: 16\n  },\n  nutrientRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    paddingVertical: 4,\n    borderBottomWidth: StyleSheet.hairlineWidth,\n    borderBottomColor: '#ccc'\n  },\n  ingredientsContainer: {\n    marginBottom: 16\n  },\n  ingredientChips: {\n    flexDirection: 'row',\n    flexWrap: 'wrap'\n  },\n  ingredientChip: {\n    marginRight: 8,\n    marginBottom: 8\n  },\n  allergenChip: {\n    backgroundColor: '#ffcdd2'\n  },\n  allergenText: {\n    color: '#b71c1c'\n  }\n});\nexport default FoodItem;", "map": {"version": 3, "names": ["React", "useState", "StyleSheet", "View", "Image", "Card", "Text", "IconButton", "Chip", "TouchableRipple", "<PERSON><PERSON>", "useTheme", "saveFood", "jsx", "_jsx", "jsxs", "_jsxs", "FoodItem", "_ref", "_food$nutrients", "_food$nutrients$find", "_food$nutrients2", "_food$nutrients2$find", "_food$nutrients3", "_food$nutrients3$find", "_food$nutrients4", "_food$nutrients4$find", "food", "onSelect", "_ref$showDetails", "showDetails", "onEdit", "_useTheme", "theme", "_useState", "_useState2", "_slicedToArray", "expanded", "setExpanded", "_useState3", "is_favorite", "_useState4", "isFavorite", "setIsFavorite", "calories", "nutrients", "find", "n", "name", "amount", "protein", "carbs", "fat", "toggleFavorite", "_ref2", "_asyncToGenerator", "newFavoriteStatus", "_objectSpread", "error", "console", "apply", "arguments", "style", "styles", "card", "mode", "children", "onPress", "Title", "title", "subtitle", "brand", "left", "props", "image_url", "source", "uri", "image", "icon", "right", "iconColor", "colors", "primary", "onSurface", "Content", "macrosContainer", "macroChip", "detailsContainer", "description", "barcode", "servingSize", "serving_size", "serving_unit", "length", "nutrientsContainer", "sectionTitle", "map", "nutrient", "nutrientRow", "unit", "nutrient_id", "ingredients", "ingredientsContainer", "ingredientChips", "ingredient", "ingredientChip", "is_allergen", "allergenChip", "textStyle", "allergenText", "ingredient_id", "Actions", "create", "marginVertical", "marginHorizontal", "elevation", "width", "height", "borderRadius", "flexDirection", "flexWrap", "marginTop", "marginRight", "marginBottom", "fontStyle", "fontWeight", "fontSize", "justifyContent", "paddingVertical", "borderBottomWidth", "hairlineWidth", "borderBottomColor", "backgroundColor", "color"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/components/FoodItem.js"], "sourcesContent": ["/**\n * Food Item Component for ZnüniZähler\n * Displays a food item with its nutritional information\n */\n\nimport React, { useState } from 'react';\nimport { StyleSheet, View, Image } from 'react-native';\nimport { Card, Text, IconButton, Chip, TouchableRipple, Button } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { saveFood } from '../services/databaseService';\n\n/**\n * Food Item Component\n * @param {Object} props - Component props\n * @param {Object} props.food - Food data\n * @param {Function} props.onSelect - Callback function when food is selected\n * @param {boolean} props.showDetails - Whether to show detailed information\n * @param {Function} props.onEdit - Callback function when food is edited\n * @returns {JSX.Element} - Food item component\n */\nconst FoodItem = ({ food, onSelect, showDetails = false, onEdit }) => {\n  const { theme } = useTheme();\n  const [expanded, setExpanded] = useState(showDetails);\n  const [isFavorite, setIsFavorite] = useState(food.is_favorite === 1);\n  \n  // Get macronutrients\n  const calories = food.nutrients?.find(n => n.name === 'Calories')?.amount || 0;\n  const protein = food.nutrients?.find(n => n.name === 'Protein')?.amount || 0;\n  const carbs = food.nutrients?.find(n => n.name === 'Carbohydrates')?.amount || 0;\n  const fat = food.nutrients?.find(n => n.name === 'Fat')?.amount || 0;\n  \n  // Toggle favorite\n  const toggleFavorite = async () => {\n    try {\n      const newFavoriteStatus = !isFavorite;\n      setIsFavorite(newFavoriteStatus);\n      \n      // Update food in database\n      await saveFood({\n        ...food,\n        is_favorite: newFavoriteStatus ? 1 : 0\n      });\n    } catch (error) {\n      console.error('Error toggling favorite:', error);\n      // Revert UI state if error\n      setIsFavorite(!isFavorite);\n    }\n  };\n  \n  return (\n    <Card style={styles.card} mode=\"outlined\">\n      <TouchableRipple onPress={() => setExpanded(!expanded)}>\n        <Card.Title\n          title={food.name}\n          subtitle={food.brand || 'No brand'}\n          left={(props) => (\n            food.image_url ? (\n              <Image source={{ uri: food.image_url }} style={styles.image} />\n            ) : (\n              <IconButton {...props} icon=\"food\" />\n            )\n          )}\n          right={(props) => (\n            <IconButton\n              {...props}\n              icon={isFavorite ? 'star' : 'star-outline'}\n              iconColor={isFavorite ? theme.colors.primary : theme.colors.onSurface}\n              onPress={toggleFavorite}\n            />\n          )}\n        />\n      </TouchableRipple>\n      \n      <Card.Content>\n        <View style={styles.macrosContainer}>\n          <Chip icon=\"fire\" style={styles.macroChip}>\n            {calories} kcal\n          </Chip>\n          <Chip icon=\"protein\" style={styles.macroChip}>\n            {protein}g\n          </Chip>\n          <Chip icon=\"grain\" style={styles.macroChip}>\n            {carbs}g\n          </Chip>\n          <Chip icon=\"oil\" style={styles.macroChip}>\n            {fat}g\n          </Chip>\n        </View>\n        \n        {expanded && (\n          <View style={styles.detailsContainer}>\n            {food.description && (\n              <Text style={styles.description}>{food.description}</Text>\n            )}\n            \n            {food.barcode && (\n              <Text style={styles.barcode}>Barcode: {food.barcode}</Text>\n            )}\n            \n            <Text style={styles.servingSize}>\n              Serving size: {food.serving_size} {food.serving_unit}\n            </Text>\n            \n            {food.nutrients && food.nutrients.length > 0 && (\n              <View style={styles.nutrientsContainer}>\n                <Text style={styles.sectionTitle}>Nutrition Facts</Text>\n                {food.nutrients.map((nutrient) => (\n                  <View key={nutrient.nutrient_id} style={styles.nutrientRow}>\n                    <Text>{nutrient.name}</Text>\n                    <Text>\n                      {nutrient.amount} {nutrient.unit}\n                    </Text>\n                  </View>\n                ))}\n              </View>\n            )}\n            \n            {food.ingredients && food.ingredients.length > 0 && (\n              <View style={styles.ingredientsContainer}>\n                <Text style={styles.sectionTitle}>Ingredients</Text>\n                <View style={styles.ingredientChips}>\n                  {food.ingredients.map((ingredient) => (\n                    <Chip\n                      key={ingredient.ingredient_id}\n                      style={[\n                        styles.ingredientChip,\n                        ingredient.is_allergen === 1 && styles.allergenChip\n                      ]}\n                      textStyle={ingredient.is_allergen === 1 ? styles.allergenText : null}\n                    >\n                      {ingredient.name}\n                    </Chip>\n                  ))}\n                </View>\n              </View>\n            )}\n          </View>\n        )}\n      </Card.Content>\n      \n      <Card.Actions>\n        {onSelect && (\n          <Button onPress={() => onSelect(food)}>Select</Button>\n        )}\n        {onEdit && (\n          <Button onPress={() => onEdit(food)}>Edit</Button>\n        )}\n        <Button onPress={() => setExpanded(!expanded)}>\n          {expanded ? 'Hide Details' : 'Show Details'}\n        </Button>\n      </Card.Actions>\n    </Card>\n  );\n};\n\nconst styles = StyleSheet.create({\n  card: {\n    marginVertical: 8,\n    marginHorizontal: 16,\n    elevation: 2,\n  },\n  image: {\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n  },\n  macrosContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    marginTop: 8,\n  },\n  macroChip: {\n    marginRight: 8,\n    marginBottom: 8,\n  },\n  detailsContainer: {\n    marginTop: 16,\n  },\n  description: {\n    marginBottom: 8,\n  },\n  barcode: {\n    marginBottom: 8,\n    fontStyle: 'italic',\n  },\n  servingSize: {\n    marginBottom: 16,\n  },\n  sectionTitle: {\n    fontWeight: 'bold',\n    fontSize: 16,\n    marginBottom: 8,\n    marginTop: 16,\n  },\n  nutrientsContainer: {\n    marginBottom: 16,\n  },\n  nutrientRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    paddingVertical: 4,\n    borderBottomWidth: StyleSheet.hairlineWidth,\n    borderBottomColor: '#ccc',\n  },\n  ingredientsContainer: {\n    marginBottom: 16,\n  },\n  ingredientChips: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n  },\n  ingredientChip: {\n    marginRight: 8,\n    marginBottom: 8,\n  },\n  allergenChip: {\n    backgroundColor: '#ffcdd2',\n  },\n  allergenText: {\n    color: '#b71c1c',\n  },\n});\n\nexport default FoodItem;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAExC,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,IAAI,EAAEC,eAAe,EAAEC,MAAM,QAAQ,oBAAoB;AAC1F,SAASC,QAAQ;AACjB,SAASC,QAAQ;AAAsC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAWvD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAAwD;EAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAAA,IAAlDC,IAAI,GAAAT,IAAA,CAAJS,IAAI;IAAEC,QAAQ,GAAAV,IAAA,CAARU,QAAQ;IAAAC,gBAAA,GAAAX,IAAA,CAAEY,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,KAAK,GAAAA,gBAAA;IAAEE,MAAM,GAAAb,IAAA,CAANa,MAAM;EAC7D,IAAAC,SAAA,GAAkBrB,QAAQ,CAAC,CAAC;IAApBsB,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,SAAA,GAAgCjC,QAAQ,CAAC6B,WAAW,CAAC;IAAAK,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA9CG,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAC5B,IAAAI,UAAA,GAAoCtC,QAAQ,CAAC0B,IAAI,CAACa,WAAW,KAAK,CAAC,CAAC;IAAAC,UAAA,GAAAL,cAAA,CAAAG,UAAA;IAA7DG,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAGhC,IAAMG,QAAQ,GAAG,EAAAzB,eAAA,GAAAQ,IAAI,CAACkB,SAAS,sBAAAzB,oBAAA,GAAdD,eAAA,CAAgB2B,IAAI,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACC,IAAI,KAAK,UAAU;EAAA,EAAC,qBAAhD5B,oBAAA,CAAkD6B,MAAM,KAAI,CAAC;EAC9E,IAAMC,OAAO,GAAG,EAAA7B,gBAAA,GAAAM,IAAI,CAACkB,SAAS,sBAAAvB,qBAAA,GAAdD,gBAAA,CAAgByB,IAAI,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACC,IAAI,KAAK,SAAS;EAAA,EAAC,qBAA/C1B,qBAAA,CAAiD2B,MAAM,KAAI,CAAC;EAC5E,IAAME,KAAK,GAAG,EAAA5B,gBAAA,GAAAI,IAAI,CAACkB,SAAS,sBAAArB,qBAAA,GAAdD,gBAAA,CAAgBuB,IAAI,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACC,IAAI,KAAK,eAAe;EAAA,EAAC,qBAArDxB,qBAAA,CAAuDyB,MAAM,KAAI,CAAC;EAChF,IAAMG,GAAG,GAAG,EAAA3B,gBAAA,GAAAE,IAAI,CAACkB,SAAS,sBAAAnB,qBAAA,GAAdD,gBAAA,CAAgBqB,IAAI,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACC,IAAI,KAAK,KAAK;EAAA,EAAC,qBAA3CtB,qBAAA,CAA6CuB,MAAM,KAAI,CAAC;EAGpE,IAAMI,cAAc;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MACjC,IAAI;QACF,IAAMC,iBAAiB,GAAG,CAACd,UAAU;QACrCC,aAAa,CAACa,iBAAiB,CAAC;QAGhC,MAAM5C,QAAQ,CAAA6C,aAAA,CAAAA,aAAA,KACT9B,IAAI;UACPa,WAAW,EAAEgB,iBAAiB,GAAG,CAAC,GAAG;QAAC,EACvC,CAAC;MACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAEhDf,aAAa,CAAC,CAACD,UAAU,CAAC;MAC5B;IACF,CAAC;IAAA,gBAfKW,cAAcA,CAAA;MAAA,OAAAC,KAAA,CAAAM,KAAA,OAAAC,SAAA;IAAA;EAAA,GAenB;EAED,OACE7C,KAAA,CAACX,IAAI;IAACyD,KAAK,EAAEC,MAAM,CAACC,IAAK;IAACC,IAAI,EAAC,UAAU;IAAAC,QAAA,GACvCpD,IAAA,CAACL,eAAe;MAAC0D,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQ7B,WAAW,CAAC,CAACD,QAAQ,CAAC;MAAA,CAAC;MAAA6B,QAAA,EACrDpD,IAAA,CAACT,IAAI,CAAC+D,KAAK;QACTC,KAAK,EAAE1C,IAAI,CAACqB,IAAK;QACjBsB,QAAQ,EAAE3C,IAAI,CAAC4C,KAAK,IAAI,UAAW;QACnCC,IAAI,EAAE,SAANA,IAAIA,CAAGC,KAAK;UAAA,OACV9C,IAAI,CAAC+C,SAAS,GACZ5D,IAAA,CAACV,KAAK;YAACuE,MAAM,EAAE;cAAEC,GAAG,EAAEjD,IAAI,CAAC+C;YAAU,CAAE;YAACZ,KAAK,EAAEC,MAAM,CAACc;UAAM,CAAE,CAAC,GAE/D/D,IAAA,CAACP,UAAU,EAAAkD,aAAA,CAAAA,aAAA,KAAKgB,KAAK;YAAEK,IAAI,EAAC;UAAM,EAAE,CACrC;QAAA,CACD;QACFC,KAAK,EAAE,SAAPA,KAAKA,CAAGN,KAAK;UAAA,OACX3D,IAAA,CAACP,UAAU,EAAAkD,aAAA,CAAAA,aAAA,KACLgB,KAAK;YACTK,IAAI,EAAEpC,UAAU,GAAG,MAAM,GAAG,cAAe;YAC3CsC,SAAS,EAAEtC,UAAU,GAAGT,KAAK,CAACgD,MAAM,CAACC,OAAO,GAAGjD,KAAK,CAACgD,MAAM,CAACE,SAAU;YACtEhB,OAAO,EAAEd;UAAe,EACzB,CAAC;QAAA;MACF,CACH;IAAC,CACa,CAAC,EAElBrC,KAAA,CAACX,IAAI,CAAC+E,OAAO;MAAAlB,QAAA,GACXlD,KAAA,CAACb,IAAI;QAAC2D,KAAK,EAAEC,MAAM,CAACsB,eAAgB;QAAAnB,QAAA,GAClClD,KAAA,CAACR,IAAI;UAACsE,IAAI,EAAC,MAAM;UAAChB,KAAK,EAAEC,MAAM,CAACuB,SAAU;UAAApB,QAAA,GACvCtB,QAAQ,EAAC,OACZ;QAAA,CAAM,CAAC,EACP5B,KAAA,CAACR,IAAI;UAACsE,IAAI,EAAC,SAAS;UAAChB,KAAK,EAAEC,MAAM,CAACuB,SAAU;UAAApB,QAAA,GAC1ChB,OAAO,EAAC,GACX;QAAA,CAAM,CAAC,EACPlC,KAAA,CAACR,IAAI;UAACsE,IAAI,EAAC,OAAO;UAAChB,KAAK,EAAEC,MAAM,CAACuB,SAAU;UAAApB,QAAA,GACxCf,KAAK,EAAC,GACT;QAAA,CAAM,CAAC,EACPnC,KAAA,CAACR,IAAI;UAACsE,IAAI,EAAC,KAAK;UAAChB,KAAK,EAAEC,MAAM,CAACuB,SAAU;UAAApB,QAAA,GACtCd,GAAG,EAAC,GACP;QAAA,CAAM,CAAC;MAAA,CACH,CAAC,EAENf,QAAQ,IACPrB,KAAA,CAACb,IAAI;QAAC2D,KAAK,EAAEC,MAAM,CAACwB,gBAAiB;QAAArB,QAAA,GAClCvC,IAAI,CAAC6D,WAAW,IACf1E,IAAA,CAACR,IAAI;UAACwD,KAAK,EAAEC,MAAM,CAACyB,WAAY;UAAAtB,QAAA,EAAEvC,IAAI,CAAC6D;QAAW,CAAO,CAC1D,EAEA7D,IAAI,CAAC8D,OAAO,IACXzE,KAAA,CAACV,IAAI;UAACwD,KAAK,EAAEC,MAAM,CAAC0B,OAAQ;UAAAvB,QAAA,GAAC,WAAS,EAACvC,IAAI,CAAC8D,OAAO;QAAA,CAAO,CAC3D,EAEDzE,KAAA,CAACV,IAAI;UAACwD,KAAK,EAAEC,MAAM,CAAC2B,WAAY;UAAAxB,QAAA,GAAC,gBACjB,EAACvC,IAAI,CAACgE,YAAY,EAAC,GAAC,EAAChE,IAAI,CAACiE,YAAY;QAAA,CAChD,CAAC,EAENjE,IAAI,CAACkB,SAAS,IAAIlB,IAAI,CAACkB,SAAS,CAACgD,MAAM,GAAG,CAAC,IAC1C7E,KAAA,CAACb,IAAI;UAAC2D,KAAK,EAAEC,MAAM,CAAC+B,kBAAmB;UAAA5B,QAAA,GACrCpD,IAAA,CAACR,IAAI;YAACwD,KAAK,EAAEC,MAAM,CAACgC,YAAa;YAAA7B,QAAA,EAAC;UAAe,CAAM,CAAC,EACvDvC,IAAI,CAACkB,SAAS,CAACmD,GAAG,CAAC,UAACC,QAAQ;YAAA,OAC3BjF,KAAA,CAACb,IAAI;cAA4B2D,KAAK,EAAEC,MAAM,CAACmC,WAAY;cAAAhC,QAAA,GACzDpD,IAAA,CAACR,IAAI;gBAAA4D,QAAA,EAAE+B,QAAQ,CAACjD;cAAI,CAAO,CAAC,EAC5BhC,KAAA,CAACV,IAAI;gBAAA4D,QAAA,GACF+B,QAAQ,CAAChD,MAAM,EAAC,GAAC,EAACgD,QAAQ,CAACE,IAAI;cAAA,CAC5B,CAAC;YAAA,GAJEF,QAAQ,CAACG,WAKd,CAAC;UAAA,CACR,CAAC;QAAA,CACE,CACP,EAEAzE,IAAI,CAAC0E,WAAW,IAAI1E,IAAI,CAAC0E,WAAW,CAACR,MAAM,GAAG,CAAC,IAC9C7E,KAAA,CAACb,IAAI;UAAC2D,KAAK,EAAEC,MAAM,CAACuC,oBAAqB;UAAApC,QAAA,GACvCpD,IAAA,CAACR,IAAI;YAACwD,KAAK,EAAEC,MAAM,CAACgC,YAAa;YAAA7B,QAAA,EAAC;UAAW,CAAM,CAAC,EACpDpD,IAAA,CAACX,IAAI;YAAC2D,KAAK,EAAEC,MAAM,CAACwC,eAAgB;YAAArC,QAAA,EACjCvC,IAAI,CAAC0E,WAAW,CAACL,GAAG,CAAC,UAACQ,UAAU;cAAA,OAC/B1F,IAAA,CAACN,IAAI;gBAEHsD,KAAK,EAAE,CACLC,MAAM,CAAC0C,cAAc,EACrBD,UAAU,CAACE,WAAW,KAAK,CAAC,IAAI3C,MAAM,CAAC4C,YAAY,CACnD;gBACFC,SAAS,EAAEJ,UAAU,CAACE,WAAW,KAAK,CAAC,GAAG3C,MAAM,CAAC8C,YAAY,GAAG,IAAK;gBAAA3C,QAAA,EAEpEsC,UAAU,CAACxD;cAAI,GAPXwD,UAAU,CAACM,aAQZ,CAAC;YAAA,CACR;UAAC,CACE,CAAC;QAAA,CACH,CACP;MAAA,CACG,CACP;IAAA,CACW,CAAC,EAEf9F,KAAA,CAACX,IAAI,CAAC0G,OAAO;MAAA7C,QAAA,GACVtC,QAAQ,IACPd,IAAA,CAACJ,MAAM;QAACyD,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQvC,QAAQ,CAACD,IAAI,CAAC;QAAA,CAAC;QAAAuC,QAAA,EAAC;MAAM,CAAQ,CACtD,EACAnC,MAAM,IACLjB,IAAA,CAACJ,MAAM;QAACyD,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQpC,MAAM,CAACJ,IAAI,CAAC;QAAA,CAAC;QAAAuC,QAAA,EAAC;MAAI,CAAQ,CAClD,EACDpD,IAAA,CAACJ,MAAM;QAACyD,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQ7B,WAAW,CAAC,CAACD,QAAQ,CAAC;QAAA,CAAC;QAAA6B,QAAA,EAC3C7B,QAAQ,GAAG,cAAc,GAAG;MAAc,CACrC,CAAC;IAAA,CACG,CAAC;EAAA,CACX,CAAC;AAEX,CAAC;AAED,IAAM0B,MAAM,GAAG7D,UAAU,CAAC8G,MAAM,CAAC;EAC/BhD,IAAI,EAAE;IACJiD,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE;EACb,CAAC;EACDtC,KAAK,EAAE;IACLuC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE;EAChB,CAAC;EACDjC,eAAe,EAAE;IACfkC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE;EACb,CAAC;EACDnC,SAAS,EAAE;IACToC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE;EAChB,CAAC;EACDpC,gBAAgB,EAAE;IAChBkC,SAAS,EAAE;EACb,CAAC;EACDjC,WAAW,EAAE;IACXmC,YAAY,EAAE;EAChB,CAAC;EACDlC,OAAO,EAAE;IACPkC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDlC,WAAW,EAAE;IACXiC,YAAY,EAAE;EAChB,CAAC;EACD5B,YAAY,EAAE;IACZ8B,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,EAAE;IACZH,YAAY,EAAE,CAAC;IACfF,SAAS,EAAE;EACb,CAAC;EACD3B,kBAAkB,EAAE;IAClB6B,YAAY,EAAE;EAChB,CAAC;EACDzB,WAAW,EAAE;IACXqB,aAAa,EAAE,KAAK;IACpBQ,cAAc,EAAE,eAAe;IAC/BC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE/H,UAAU,CAACgI,aAAa;IAC3CC,iBAAiB,EAAE;EACrB,CAAC;EACD7B,oBAAoB,EAAE;IACpBqB,YAAY,EAAE;EAChB,CAAC;EACDpB,eAAe,EAAE;IACfgB,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE;EACZ,CAAC;EACDf,cAAc,EAAE;IACdiB,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE;EAChB,CAAC;EACDhB,YAAY,EAAE;IACZyB,eAAe,EAAE;EACnB,CAAC;EACDvB,YAAY,EAAE;IACZwB,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAepH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}