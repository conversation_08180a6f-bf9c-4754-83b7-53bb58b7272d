{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"label\", \"size\", \"style\", \"labelStyle\", \"color\", \"theme\", \"maxFontSizeMultiplier\"],\n  _excluded2 = [\"backgroundColor\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport useWindowDimensions from \"react-native-web/dist/exports/useWindowDimensions\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { white } from \"../../styles/themes/v2/colors\";\nimport getContrastingColor from \"../../utils/getContrastingColor\";\nimport Text from \"../Typography/Text\";\nvar defaultSize = 64;\nvar AvatarText = function AvatarText(_ref) {\n  var _theme$colors;\n  var label = _ref.label,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? defaultSize : _ref$size,\n    style = _ref.style,\n    labelStyle = _ref.labelStyle,\n    customColor = _ref.color,\n    themeOverrides = _ref.theme,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$backgroundColor = _ref2.backgroundColor,\n    backgroundColor = _ref2$backgroundColor === void 0 ? (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.primary : _ref2$backgroundColor,\n    restStyle = _objectWithoutProperties(_ref2, _excluded2);\n  var textColor = customColor != null ? customColor : getContrastingColor(backgroundColor, white, 'rgba(0, 0, 0, .54)');\n  var _useWindowDimensions = useWindowDimensions(),\n    fontScale = _useWindowDimensions.fontScale;\n  return React.createElement(View, _extends({\n    style: [{\n      width: size,\n      height: size,\n      borderRadius: size / 2,\n      backgroundColor: backgroundColor\n    }, styles.container, restStyle]\n  }, rest), React.createElement(Text, {\n    style: [styles.text, {\n      color: textColor,\n      fontSize: size / 2,\n      lineHeight: size / fontScale\n    }, labelStyle],\n    numberOfLines: 1,\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, label));\n};\nAvatarText.displayName = 'Avatar.Text';\nvar styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  text: {\n    textAlign: 'center',\n    textAlignVertical: 'center'\n  }\n});\nexport default AvatarText;", "map": {"version": 3, "names": ["React", "StyleSheet", "useWindowDimensions", "View", "useInternalTheme", "white", "getContrastingColor", "Text", "defaultSize", "AvatarText", "_ref", "_theme$colors", "label", "_ref$size", "size", "style", "labelStyle", "customColor", "color", "themeOverrides", "theme", "maxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "_ref2", "flatten", "_ref2$backgroundColor", "backgroundColor", "colors", "primary", "restStyle", "_excluded2", "textColor", "_useWindowDimensions", "fontScale", "createElement", "_extends", "width", "height", "borderRadius", "styles", "container", "text", "fontSize", "lineHeight", "numberOfLines", "displayName", "create", "justifyContent", "alignItems", "textAlign", "textAlignVertical"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Avatar/AvatarText.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  useWindowDimensions,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport { white } from '../../styles/themes/v2/colors';\nimport type { ThemeProp } from '../../types';\nimport getContrastingColor from '../../utils/getContrastingColor';\nimport Text from '../Typography/Text';\n\nconst defaultSize = 64;\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Initials to show as the text in the `Avatar`.\n   */\n  label: string;\n  /**\n   * Size of the avatar.\n   */\n  size?: number;\n  /**\n   * Custom color for the text.\n   */\n  color?: string;\n  /**\n   * Style for text container\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Style for the title.\n   */\n  labelStyle?: StyleProp<TextStyle>;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Avatars can be used to represent people in a graphical way.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Avatar } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Avatar.Text size={24} label=\"XD\" />\n * );\n * ```\n */\nconst AvatarText = ({\n  label,\n  size = defaultSize,\n  style,\n  labelStyle,\n  color: customColor,\n  theme: themeOverrides,\n  maxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { backgroundColor = theme.colors?.primary, ...restStyle } =\n    StyleSheet.flatten(style) || {};\n  const textColor =\n    customColor ??\n    getContrastingColor(backgroundColor, white, 'rgba(0, 0, 0, .54)');\n  const { fontScale } = useWindowDimensions();\n\n  return (\n    <View\n      style={[\n        {\n          width: size,\n          height: size,\n          borderRadius: size / 2,\n          backgroundColor,\n        },\n        styles.container,\n        restStyle,\n      ]}\n      {...rest}\n    >\n      <Text\n        style={[\n          styles.text,\n          {\n            color: textColor,\n            fontSize: size / 2,\n            lineHeight: size / fontScale,\n          },\n          labelStyle,\n        ]}\n        numberOfLines={1}\n        maxFontSizeMultiplier={maxFontSizeMultiplier}\n      >\n        {label}\n      </Text>\n    </View>\n  );\n};\n\nAvatarText.displayName = 'Avatar.Text';\n\nconst styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  text: {\n    textAlign: 'center',\n    textAlignVertical: 'center',\n  },\n});\n\nexport default AvatarText;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,mBAAA;AAAA,OAAAC,IAAA;AAU9B,SAASC,gBAAgB;AACzB,SAASC,KAAK;AAEd,OAAOC,mBAAmB;AAC1B,OAAOC,IAAI;AAEX,IAAMC,WAAW,GAAG,EAAE;AA8CtB,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAGC,IAAA,EASN;EAAA,IAAAC,aAAA;EAAA,IARXC,KAAK,GAQCF,IAAA,CARNE,KAAK;IAAAC,SAAA,GAQCH,IAAA,CAPNI,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAGL,WAAW,GAAAK,SAAA;IAClBE,KAAK,GAMCL,IAAA,CANNK,KAAK;IACLC,UAAU,GAKJN,IAAA,CALNM,UAAU;IACHC,WAAW,GAIZP,IAAA,CAJNQ,KAAK;IACEC,cAAc,GAGfT,IAAA,CAHNU,KAAK;IACLC,qBAAqB,GAEfX,IAAA,CAFNW,qBAAqB;IAClBC,IAAA,GAAAC,wBAAA,CACGb,IAAA,EAAAc,SAAA;EACN,IAAMJ,KAAK,GAAGhB,gBAAgB,CAACe,cAAc,CAAC;EAC9C,IAAAM,KAAA,GACExB,UAAU,CAACyB,OAAO,CAACX,KAAK,CAAC,IAAI,CAAC,CAAC;IAAAY,qBAAA,GAAAF,KAAA,CADzBG,eAAe;IAAfA,eAAe,GAAAD,qBAAA,eAAAhB,aAAA,GAAGS,KAAK,CAACS,MAAM,cAAAlB,aAAA,uBAAZA,aAAA,CAAcmB,OAAO,GAAAH,qBAAA;IAAKI,SAAA,GAAAR,wBAAA,CAAAE,KAAA,EAAAO,UAAA;EAEpD,IAAMC,SAAS,GACbhB,WAAW,WAAXA,WAAW,GACXX,mBAAmB,CAACsB,eAAe,EAAEvB,KAAK,EAAE,oBAAoB,CAAC;EACnE,IAAA6B,oBAAA,GAAsBhC,mBAAmB,CAAC,CAAC;IAAnCiC,SAAA,GAAAD,oBAAA,CAAAC,SAAA;EAER,OACEnC,KAAA,CAAAoC,aAAA,CAACjC,IAAI,EAAAkC,QAAA;IACHtB,KAAK,EAAE,CACL;MACEuB,KAAK,EAAExB,IAAI;MACXyB,MAAM,EAAEzB,IAAI;MACZ0B,YAAY,EAAE1B,IAAI,GAAG,CAAC;MACtBc,eAAA,EAAAA;IACF,CAAC,EACDa,MAAM,CAACC,SAAS,EAChBX,SAAS;EACT,GACET,IAAI,GAERtB,KAAA,CAAAoC,aAAA,CAAC7B,IAAI;IACHQ,KAAK,EAAE,CACL0B,MAAM,CAACE,IAAI,EACX;MACEzB,KAAK,EAAEe,SAAS;MAChBW,QAAQ,EAAE9B,IAAI,GAAG,CAAC;MAClB+B,UAAU,EAAE/B,IAAI,GAAGqB;IACrB,CAAC,EACDnB,UAAU,CACV;IACF8B,aAAa,EAAE,CAAE;IACjBzB,qBAAqB,EAAEA;EAAsB,GAE5CT,KACG,CACF,CAAC;AAEX,CAAC;AAEDH,UAAU,CAACsC,WAAW,GAAG,aAAa;AAEtC,IAAMN,MAAM,GAAGxC,UAAU,CAAC+C,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDP,IAAI,EAAE;IACJQ,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAe3C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}