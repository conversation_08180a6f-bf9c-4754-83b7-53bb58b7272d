{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\nvar StateSafePureComponent = function (_React$PureComponent) {\n  function StateSafePureComponent(props) {\n    var _this;\n    _classCallCheck(this, StateSafePureComponent);\n    _this = _callSuper(this, StateSafePureComponent, [props]);\n    _this._inAsyncStateUpdate = false;\n    _this._installSetStateHooks();\n    return _this;\n  }\n  _inherits(StateSafePureComponent, _React$PureComponent);\n  return _createClass(StateSafePureComponent, [{\n    key: \"setState\",\n    value: function setState(partialState, callback) {\n      var _this2 = this;\n      if (typeof partialState === 'function') {\n        _superPropGet(StateSafePureComponent, \"setState\", this, 3)([function (state, props) {\n          _this2._inAsyncStateUpdate = true;\n          var ret;\n          try {\n            ret = partialState(state, props);\n          } catch (err) {\n            throw err;\n          } finally {\n            _this2._inAsyncStateUpdate = false;\n          }\n          return ret;\n        }, callback]);\n      } else {\n        _superPropGet(StateSafePureComponent, \"setState\", this, 3)([partialState, callback]);\n      }\n    }\n  }, {\n    key: \"_installSetStateHooks\",\n    value: function _installSetStateHooks() {\n      var that = this;\n      var props = this.props,\n        state = this.state;\n      Object.defineProperty(this, 'props', {\n        get: function get() {\n          invariant(!that._inAsyncStateUpdate, '\"this.props\" should not be accessed during state updates');\n          return props;\n        },\n        set: function set(newProps) {\n          props = newProps;\n        }\n      });\n      Object.defineProperty(this, 'state', {\n        get: function get() {\n          invariant(!that._inAsyncStateUpdate, '\"this.state\" should not be acceessed during state updates');\n          return state;\n        },\n        set: function set(newState) {\n          state = newState;\n        }\n      });\n    }\n  }]);\n}(React.PureComponent);\nexport { StateSafePureComponent as default };", "map": {"version": 3, "names": ["invariant", "React", "StateSafePureComponent", "_React$PureComponent", "props", "_this", "_classCallCheck", "_callSuper", "_inAsyncStateUpdate", "_installSetStateHooks", "_inherits", "_createClass", "key", "value", "setState", "partialState", "callback", "_this2", "_superPropGet", "state", "ret", "err", "that", "Object", "defineProperty", "get", "set", "newProps", "newState", "PureComponent", "default"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/StateSafePureComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\n\n/**\n * `setState` is called asynchronously, and should not rely on the value of\n * `this.props` or `this.state`:\n * https://reactjs.org/docs/state-and-lifecycle.html#state-updates-may-be-asynchronous\n *\n * SafePureComponent adds runtime enforcement, to catch cases where these\n * variables are read in a state updater function, instead of the ones passed\n * in.\n */\nexport default class StateSafePureComponent extends React.PureComponent {\n  constructor(props) {\n    super(props);\n    this._inAsyncStateUpdate = false;\n    this._installSetStateHooks();\n  }\n  setState(partialState, callback) {\n    if (typeof partialState === 'function') {\n      super.setState((state, props) => {\n        this._inAsyncStateUpdate = true;\n        var ret;\n        try {\n          ret = partialState(state, props);\n        } catch (err) {\n          throw err;\n        } finally {\n          this._inAsyncStateUpdate = false;\n        }\n        return ret;\n      }, callback);\n    } else {\n      super.setState(partialState, callback);\n    }\n  }\n  _installSetStateHooks() {\n    var that = this;\n    var props = this.props,\n      state = this.state;\n    Object.defineProperty(this, 'props', {\n      get() {\n        invariant(!that._inAsyncStateUpdate, '\"this.props\" should not be accessed during state updates');\n        return props;\n      },\n      set(newProps) {\n        props = newProps;\n      }\n    });\n    Object.defineProperty(this, 'state', {\n      get() {\n        invariant(!that._inAsyncStateUpdate, '\"this.state\" should not be acceessed during state updates');\n        return state;\n      },\n      set(newState) {\n        state = newState;\n      }\n    });\n  }\n}"], "mappings": ";;;;;;;;;AAUA,OAAOA,SAAS,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAC,IAWVC,sBAAsB,aAAAC,oBAAA;EACzC,SAAAD,uBAAYE,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAJ,sBAAA;IACjBG,KAAA,GAAAE,UAAA,OAAAL,sBAAA,GAAME,KAAK;IACXC,KAAA,CAAKG,mBAAmB,GAAG,KAAK;IAChCH,KAAA,CAAKI,qBAAqB,CAAC,CAAC;IAAC,OAAAJ,KAAA;EAC/B;EAACK,SAAA,CAAAR,sBAAA,EAAAC,oBAAA;EAAA,OAAAQ,YAAA,CAAAT,sBAAA;IAAAU,GAAA;IAAAC,KAAA,EACD,SAAAC,QAAQA,CAACC,YAAY,EAAEC,QAAQ,EAAE;MAAA,IAAAC,MAAA;MAC/B,IAAI,OAAOF,YAAY,KAAK,UAAU,EAAE;QACtCG,aAAA,CAAAhB,sBAAA,wBAAe,UAACiB,KAAK,EAAEf,KAAK,EAAK;UAC/Ba,MAAI,CAACT,mBAAmB,GAAG,IAAI;UAC/B,IAAIY,GAAG;UACP,IAAI;YACFA,GAAG,GAAGL,YAAY,CAACI,KAAK,EAAEf,KAAK,CAAC;UAClC,CAAC,CAAC,OAAOiB,GAAG,EAAE;YACZ,MAAMA,GAAG;UACX,CAAC,SAAS;YACRJ,MAAI,CAACT,mBAAmB,GAAG,KAAK;UAClC;UACA,OAAOY,GAAG;QACZ,CAAC,EAAEJ,QAAQ;MACb,CAAC,MAAM;QACLE,aAAA,CAAAhB,sBAAA,wBAAea,YAAY,EAAEC,QAAQ;MACvC;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAJ,qBAAqBA,CAAA,EAAG;MACtB,IAAIa,IAAI,GAAG,IAAI;MACf,IAAIlB,KAAK,GAAG,IAAI,CAACA,KAAK;QACpBe,KAAK,GAAG,IAAI,CAACA,KAAK;MACpBI,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;QACnCC,GAAG,WAAHA,GAAGA,CAAA,EAAG;UACJzB,SAAS,CAAC,CAACsB,IAAI,CAACd,mBAAmB,EAAE,0DAA0D,CAAC;UAChG,OAAOJ,KAAK;QACd,CAAC;QACDsB,GAAG,WAAHA,GAAGA,CAACC,QAAQ,EAAE;UACZvB,KAAK,GAAGuB,QAAQ;QAClB;MACF,CAAC,CAAC;MACFJ,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;QACnCC,GAAG,WAAHA,GAAGA,CAAA,EAAG;UACJzB,SAAS,CAAC,CAACsB,IAAI,CAACd,mBAAmB,EAAE,2DAA2D,CAAC;UACjG,OAAOW,KAAK;QACd,CAAC;QACDO,GAAG,WAAHA,GAAGA,CAACE,QAAQ,EAAE;UACZT,KAAK,GAAGS,QAAQ;QAClB;MACF,CAAC,CAAC;IACJ;EAAC;AAAA,EA9CiD3B,KAAK,CAAC4B,aAAa;AAAA,SAAlD3B,sBAAsB,IAAA4B,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}