{"ast": null, "code": "import * as React from 'react';\nimport PreventRemoveContext from \"./PreventRemoveContext\";\nexport default function usePreventRemoveContext() {\n  var value = React.useContext(PreventRemoveContext);\n  if (value == null) {\n    throw new Error(\"Couldn't find the prevent remove context. Is your component inside NavigationContent?\");\n  }\n  return value;\n}", "map": {"version": 3, "names": ["React", "PreventRemoveContext", "usePreventRemoveContext", "value", "useContext", "Error"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/usePreventRemoveContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport PreventRemoveContext from './PreventRemoveContext';\n\nexport default function usePreventRemoveContext() {\n  const value = React.useContext(PreventRemoveContext);\n\n  if (value == null) {\n    throw new Error(\n      \"Couldn't find the prevent remove context. Is your component inside NavigationContent?\"\n    );\n  }\n\n  return value;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,oBAAoB;AAE3B,eAAe,SAASC,uBAAuBA,CAAA,EAAG;EAChD,IAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,oBAAoB,CAAC;EAEpD,IAAIE,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIE,KAAK,CACb,uFAAuF,CACxF;EACH;EAEA,OAAOF,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}