{"ast": null, "code": "import * as React from 'react';\nvar ServerContext = React.createContext(undefined);\nexport default ServerContext;", "map": {"version": 3, "names": ["React", "ServerContext", "createContext", "undefined"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/native/src/ServerContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport type ServerContextType = {\n  location?: {\n    pathname: string;\n    search: string;\n  };\n};\n\nconst ServerContext = React.createContext<ServerContextType | undefined>(\n  undefined\n);\n\nexport default ServerContext;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAS9B,IAAMC,aAAa,GAAGD,KAAK,CAACE,aAAa,CACvCC,SAAS,CACV;AAED,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}