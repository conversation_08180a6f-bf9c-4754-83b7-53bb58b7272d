{"ast": null, "code": "import 'expo/build/Expo.fx';\nimport * as React from 'react';\nimport AppRegistry from \"react-native-web/dist/exports/AppRegistry\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { createRoot } from \"./createRoot\";\nexport default function registerRootComponent(component) {\n  var qualifiedComponent = component;\n  if (process.env.NODE_ENV !== 'production') {\n    var _require = require(\"./withDevTools\"),\n      withDevTools = _require.withDevTools;\n    qualifiedComponent = withDevTools(component);\n  }\n  if (Platform.OS !== 'web') {\n    AppRegistry.registerComponent('main', function () {\n      return qualifiedComponent;\n    });\n  } else if (typeof document !== 'undefined') {\n    var tag = document.getElementById('root');\n    if (!tag) {\n      tag = document.getElementById('main');\n      if (process.env.NODE_ENV !== 'production') {\n        if (tag) {\n          console.warn('Mounting the root React component to an HTML element with id \"main\" is deprecated. Use id \"root\" instead.');\n        }\n      }\n    }\n    if (!tag) {\n      throw new Error('Required HTML element with id \"root\" was not found in the document HTML. This is required for mounting the root React component.');\n    }\n    var rootTag = createRoot(tag);\n    rootTag.render(React.createElement(qualifiedComponent));\n  }\n}", "map": {"version": 3, "names": ["React", "AppRegistry", "Platform", "createRoot", "registerRootComponent", "component", "qualifiedComponent", "process", "env", "NODE_ENV", "_require", "require", "withDevTools", "OS", "registerComponent", "document", "tag", "getElementById", "console", "warn", "Error", "rootTag", "render", "createElement"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo/src/launch/registerRootComponent.tsx"], "sourcesContent": ["import 'expo/build/Expo.fx';\n\nimport * as React from 'react';\nimport { AppRegistry, Platform } from 'react-native';\n\nimport { createRoot } from './createRoot';\n\ntype InitialProps = {\n  exp: {\n    notification?: any;\n    manifestString?: string;\n    [key: string]: any;\n  };\n  shell?: boolean;\n  shellManifestUrl?: string;\n  [key: string]: any;\n};\n\nexport default function registerRootComponent<P extends InitialProps>(\n  component: React.ComponentType<P>\n): void {\n  let qualifiedComponent = component;\n\n  if (process.env.NODE_ENV !== 'production') {\n    const { withDevTools } = require('./withDevTools') as typeof import('./withDevTools');\n    qualifiedComponent = withDevTools(component);\n  }\n\n  if (Platform.OS !== 'web') {\n    AppRegistry.registerComponent('main', () => qualifiedComponent);\n  } else if (\n    // Skip querying the DOM if we're in a Node.js environment.\n    typeof document !== 'undefined'\n  ) {\n    let tag = document.getElementById('root');\n\n    if (!tag) {\n      tag = document.getElementById('main');\n      if (process.env.NODE_ENV !== 'production') {\n        // This block will be removed in production\n        if (tag) {\n          console.warn(\n            'Mounting the root React component to an HTML element with id \"main\" is deprecated. Use id \"root\" instead.'\n          );\n        }\n      }\n    }\n\n    if (!tag) {\n      throw new Error(\n        'Required HTML element with id \"root\" was not found in the document HTML. This is required for mounting the root React component.'\n      );\n    }\n\n    const rootTag = createRoot(tag);\n    rootTag.render(React.createElement(qualifiedComponent));\n  }\n}\n"], "mappings": "AAAA,OAAO,oBAAoB;AAE3B,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAC,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAG/B,SAASC,UAAU;AAanB,eAAc,SAAUC,qBAAqBA,CAC3CC,SAAiC;EAEjC,IAAIC,kBAAkB,GAAGD,SAAS;EAElC,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAAC,QAAA,GAAyBC,OAAO,iBAAiB,CAAoC;MAA7EC,YAAY,GAAAF,QAAA,CAAZE,YAAY;IACpBN,kBAAkB,GAAGM,YAAY,CAACP,SAAS,CAAC;;EAG9C,IAAIH,QAAQ,CAACW,EAAE,KAAK,KAAK,EAAE;IACzBZ,WAAW,CAACa,iBAAiB,CAAC,MAAM,EAAE;MAAA,OAAMR,kBAAkB;IAAA,EAAC;GAChE,MAAM,IAEL,OAAOS,QAAQ,KAAK,WAAW,EAC/B;IACA,IAAIC,GAAG,GAAGD,QAAQ,CAACE,cAAc,CAAC,MAAM,CAAC;IAEzC,IAAI,CAACD,GAAG,EAAE;MACRA,GAAG,GAAGD,QAAQ,CAACE,cAAc,CAAC,MAAM,CAAC;MACrC,IAAIV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QAEzC,IAAIO,GAAG,EAAE;UACPE,OAAO,CAACC,IAAI,CACV,2GAA2G,CAC5G;;;;IAKP,IAAI,CAACH,GAAG,EAAE;MACR,MAAM,IAAII,KAAK,CACb,kIAAkI,CACnI;;IAGH,IAAMC,OAAO,GAAGlB,UAAU,CAACa,GAAG,CAAC;IAC/BK,OAAO,CAACC,MAAM,CAACtB,KAAK,CAACuB,aAAa,CAACjB,kBAAkB,CAAC,CAAC;;AAE3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}