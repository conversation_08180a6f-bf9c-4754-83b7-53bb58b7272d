{"ast": null, "code": "'use strict';\n\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n    case 1:\n      return x ^ y ^ z;\n    case 2:\n      return x & y ^ x & z ^ y & z;\n    case 3:\n      return x ^ y ^ z;\n    default:\n      return 0;\n  }\n}\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\nfunction sha1(bytes) {\n  var K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  var H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n  if (typeof bytes == 'string') {\n    var msg = unescape(encodeURIComponent(bytes));\n    bytes = new Array(msg.length);\n    for (var i = 0; i < msg.length; i++) bytes[i] = msg.charCodeAt(i);\n  }\n  bytes.push(0x80);\n  var l = bytes.length / 4 + 2;\n  var N = Math.ceil(l / 16);\n  var M = new Array(N);\n  for (var _i = 0; _i < N; _i++) {\n    M[_i] = new Array(16);\n    for (var j = 0; j < 16; j++) {\n      M[_i][j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];\n    }\n  }\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n  for (var _i2 = 0; _i2 < N; _i2++) {\n    var W = new Array(80);\n    for (var t = 0; t < 16; t++) W[t] = M[_i2][t];\n    for (var _t = 16; _t < 80; _t++) {\n      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);\n    }\n    var a = H[0];\n    var b = H[1];\n    var c = H[2];\n    var d = H[3];\n    var e = H[4];\n    for (var _t2 = 0; _t2 < 80; _t2++) {\n      var s = Math.floor(_t2 / 20);\n      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\nexport default sha1;", "map": {"version": 3, "names": ["f", "s", "x", "y", "z", "ROTL", "n", "sha1", "bytes", "K", "H", "msg", "unescape", "encodeURIComponent", "Array", "length", "i", "charCodeAt", "push", "l", "N", "Math", "ceil", "M", "j", "pow", "floor", "W", "t", "a", "b", "c", "d", "e", "T"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/expo-modules-core/src/uuid/lib/sha1.ts"], "sourcesContent": ["// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\n'use strict';\n\nfunction f(s: number, x: number, y: number, z: number) {\n  switch (s) {\n    case 0:\n      return (x & y) ^ (~x & z);\n    case 1:\n      return x ^ y ^ z;\n    case 2:\n      return (x & y) ^ (x & z) ^ (y & z);\n    case 3:\n      return x ^ y ^ z;\n    default:\n      return 0;\n  }\n}\n\nfunction ROTL(x: number, n: number) {\n  return (x << n) | (x >>> (32 - n));\n}\n\nfunction sha1(bytes: number[] | string) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes == 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n    bytes = new Array(msg.length);\n    for (let i = 0; i < msg.length; i++) bytes[i] = msg.charCodeAt(i);\n  }\n\n  bytes.push(0x80);\n\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n\n  for (let i = 0; i < N; i++) {\n    M[i] = new Array(16);\n    for (let j = 0; j < 16; j++) {\n      M[i][j] =\n        (bytes[i * 64 + j * 4] << 24) |\n        (bytes[i * 64 + j * 4 + 1] << 16) |\n        (bytes[i * 64 + j * 4 + 2] << 8) |\n        bytes[i * 64 + j * 4 + 3];\n    }\n  }\n\n  M[N - 1][14] = ((bytes.length - 1) * 8) / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = ((bytes.length - 1) * 8) & 0xffffffff;\n\n  for (let i = 0; i < N; i++) {\n    const W = new Array(80);\n\n    for (let t = 0; t < 16; t++) W[t] = M[i][t];\n    for (let t = 16; t < 80; t++) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n\n    for (let t = 0; t < 80; t++) {\n      const s = Math.floor(t / 20);\n      const T = (ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t]) >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = (H[0] + a) >>> 0;\n    H[1] = (H[1] + b) >>> 0;\n    H[2] = (H[2] + c) >>> 0;\n    H[3] = (H[3] + d) >>> 0;\n    H[4] = (H[4] + e) >>> 0;\n  }\n\n  return [\n    (H[0] >> 24) & 0xff,\n    (H[0] >> 16) & 0xff,\n    (H[0] >> 8) & 0xff,\n    H[0] & 0xff,\n    (H[1] >> 24) & 0xff,\n    (H[1] >> 16) & 0xff,\n    (H[1] >> 8) & 0xff,\n    H[1] & 0xff,\n    (H[2] >> 24) & 0xff,\n    (H[2] >> 16) & 0xff,\n    (H[2] >> 8) & 0xff,\n    H[2] & 0xff,\n    (H[3] >> 24) & 0xff,\n    (H[3] >> 16) & 0xff,\n    (H[3] >> 8) & 0xff,\n    H[3] & 0xff,\n    (H[4] >> 24) & 0xff,\n    (H[4] >> 16) & 0xff,\n    (H[4] >> 8) & 0xff,\n    H[4] & 0xff,\n  ];\n}\n\nexport default sha1;\n"], "mappings": "AAEA,YAAY;;AAEZ,SAASA,CAACA,CAACC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAE;EACrD,QAAQH,CAAC;IACP,KAAK,CAAC;MACJ,OAAQC,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE;IAC3B,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;IAClB,KAAK,CAAC;MACJ,OAAQF,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGE,CAAE,GAAID,CAAC,GAAGC,CAAE;IACpC,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;IAClB;MACE,OAAO,CAAC;EACZ;AACF;AAEA,SAASC,IAAIA,CAACH,CAAS,EAAEI,CAAS,EAAE;EAClC,OAAQJ,CAAC,IAAII,CAAC,GAAKJ,CAAC,KAAM,EAAE,GAAGI,CAAG;AACpC;AAEA,SAASC,IAAIA,CAACC,KAAwB,EAAE;EACtC,IAAMC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAC1D,IAAMC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAEtE,IAAI,OAAOF,KAAK,IAAI,QAAQ,EAAE;IAC5B,IAAMG,GAAG,GAAGC,QAAQ,CAACC,kBAAkB,CAACL,KAAK,CAAC,CAAC;IAC/CA,KAAK,GAAG,IAAIM,KAAK,CAACH,GAAG,CAACI,MAAM,CAAC;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAER,KAAK,CAACQ,CAAC,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACD,CAAC,CAAC;EACnE;EAEAR,KAAK,CAACU,IAAI,CAAC,IAAI,CAAC;EAEhB,IAAMC,CAAC,GAAGX,KAAK,CAACO,MAAM,GAAG,CAAC,GAAG,CAAC;EAC9B,IAAMK,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACH,CAAC,GAAG,EAAE,CAAC;EAC3B,IAAMI,CAAC,GAAG,IAAIT,KAAK,CAACM,CAAC,CAAC;EAEtB,KAAK,IAAIJ,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGI,CAAC,EAAEJ,EAAC,EAAE,EAAE;IAC1BO,CAAC,CAACP,EAAC,CAAC,GAAG,IAAIF,KAAK,CAAC,EAAE,CAAC;IACpB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BD,CAAC,CAACP,EAAC,CAAC,CAACQ,CAAC,CAAC,GACJhB,KAAK,CAACQ,EAAC,GAAG,EAAE,GAAGQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAC3BhB,KAAK,CAACQ,EAAC,GAAG,EAAE,GAAGQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,GAChChB,KAAK,CAACQ,EAAC,GAAG,EAAE,GAAGQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,GAChChB,KAAK,CAACQ,EAAC,GAAG,EAAE,GAAGQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7B;EACF;EAEAD,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAI,CAACZ,KAAK,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,GAAIM,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EACzDF,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACK,KAAK,CAACH,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACvCG,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAI,CAACZ,KAAK,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,GAAI,UAAU;EAEpD,KAAK,IAAIC,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGI,CAAC,EAAEJ,GAAC,EAAE,EAAE;IAC1B,IAAMW,CAAC,GAAG,IAAIb,KAAK,CAAC,EAAE,CAAC;IAEvB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGL,CAAC,CAACP,GAAC,CAAC,CAACY,CAAC,CAAC;IAC3C,KAAK,IAAIA,EAAC,GAAG,EAAE,EAAEA,EAAC,GAAG,EAAE,EAAEA,EAAC,EAAE,EAAE;MAC5BD,CAAC,CAACC,EAAC,CAAC,GAAGvB,IAAI,CAACsB,CAAC,CAACC,EAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,CAACC,EAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,CAACC,EAAC,GAAG,EAAE,CAAC,GAAGD,CAAC,CAACC,EAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7D;IAEA,IAAIC,CAAC,GAAGnB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIoB,CAAC,GAAGpB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIqB,CAAC,GAAGrB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIsB,CAAC,GAAGtB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIuB,CAAC,GAAGvB,CAAC,CAAC,CAAC,CAAC;IAEZ,KAAK,IAAIkB,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAG,EAAE,EAAEA,GAAC,EAAE,EAAE;MAC3B,IAAM3B,CAAC,GAAGoB,IAAI,CAACK,KAAK,CAACE,GAAC,GAAG,EAAE,CAAC;MAC5B,IAAMM,CAAC,GAAI7B,IAAI,CAACwB,CAAC,EAAE,CAAC,CAAC,GAAG7B,CAAC,CAACC,CAAC,EAAE6B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGC,CAAC,GAAGxB,CAAC,CAACR,CAAC,CAAC,GAAG0B,CAAC,CAACC,GAAC,CAAC,KAAM,CAAC;MAC9DK,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAG1B,IAAI,CAACyB,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;MACrBA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGK,CAAC;IACP;IAEAxB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGmB,CAAC,KAAM,CAAC;IACvBnB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGoB,CAAC,KAAM,CAAC;IACvBpB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGqB,CAAC,KAAM,CAAC;IACvBrB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGsB,CAAC,KAAM,CAAC;IACvBtB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,KAAM,CAAC;EACzB;EAEA,OAAO,CACJvB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EACVA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EACVA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EACVA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EACVA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,IAAI,EAClBA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CACZ;AACH;AAEA,eAAeH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}