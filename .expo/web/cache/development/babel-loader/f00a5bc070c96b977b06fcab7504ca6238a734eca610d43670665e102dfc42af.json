{"ast": null, "code": "import registerRootComponent from 'expo/build/launch/registerRootComponent';\nimport App from \"../../App\";\nregisterRootComponent(App);", "map": {"version": 3, "names": ["registerRootComponent", "App"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo/AppEntry.js"], "sourcesContent": ["import registerRootComponent from 'expo/build/launch/registerRootComponent';\n\nimport App from '../../App';\n\nregisterRootComponent(App);\n"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,yCAAyC;AAE3E,OAAOC,GAAG;AAEVD,qBAAqB,CAACC,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}