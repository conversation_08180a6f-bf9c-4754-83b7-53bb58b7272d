{"ast": null, "code": "import StatusBar from \"react-native-web/dist/exports/StatusBar\";\nexport default function setStatusBarHidden(hidden, animation) {\n  StatusBar.setHidden(hidden, animation);\n}", "map": {"version": 3, "names": ["setStatusBarHidden", "hidden", "animation", "StatusBar", "setHidden"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-status-bar/src/setStatusBarHidden.ts"], "sourcesContent": ["import { StatusBar } from 'react-native';\n\nimport { StatusBarAnimation } from './StatusBar.types';\n\n// @needsAudit\n/**\n * Toggle visibility of the status bar.\n * @param hidden If the status bar should be hidden.\n * @param animation Animation to use when toggling hidden, defaults to `'none'`.\n */\nexport default function setStatusBarHidden(hidden: boolean, animation: StatusBarAnimation) {\n  StatusBar.setHidden(hidden, animation);\n}\n"], "mappings": ";AAUA,eAAc,SAAUA,kBAAkBA,CAACC,MAAe,EAAEC,SAA6B;EACvFC,SAAS,CAACC,SAAS,CAACH,MAAM,EAAEC,SAAS,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}