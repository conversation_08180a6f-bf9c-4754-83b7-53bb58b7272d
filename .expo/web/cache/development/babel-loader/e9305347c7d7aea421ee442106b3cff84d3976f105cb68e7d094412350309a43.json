{"ast": null, "code": "import Animated from \"react-native-web/dist/exports/Animated\";\nimport useLazyRef from \"./useLazyRef\";\nexport default function useAnimatedValue(initialValue) {\n  var _useLazyRef = useLazyRef(function () {\n      return new Animated.Value(initialValue);\n    }),\n    current = _useLazyRef.current;\n  return current;\n}", "map": {"version": 3, "names": ["useLazyRef", "useAnimatedValue", "initialValue", "_useLazyRef", "Animated", "Value", "current"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-paper/src/utils/useAnimatedValue.tsx"], "sourcesContent": ["import { Animated } from 'react-native';\n\nimport useLazyRef from './useLazyRef';\n\nexport default function useAnimatedValue(initialValue: number) {\n  const { current } = useLazyRef(() => new Animated.Value(initialValue));\n\n  return current;\n}\n"], "mappings": ";AAEA,OAAOA,UAAU;AAEjB,eAAe,SAASC,gBAAgBA,CAACC,YAAoB,EAAE;EAC7D,IAAAC,WAAA,GAAoBH,UAAU,CAAC;MAAA,OAAM,IAAII,QAAQ,CAACC,KAAK,CAACH,YAAY,CAAC;IAAA,EAAC;IAA9DI,OAAA,GAAAH,WAAA,CAAAG,OAAA;EAER,OAAOA,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}