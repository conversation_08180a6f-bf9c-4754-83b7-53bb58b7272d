{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"id\", \"initialRouteName\", \"backBehavior\", \"children\", \"screenListeners\", \"screenOptions\", \"sceneContainerStyle\"],\n  _excluded2 = [\"lazy\", \"tabBarOptions\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { createNavigatorFactory, TabRouter, useNavigationBuilder } from '@react-navigation/native';\nimport * as React from 'react';\nimport warnOnce from 'warn-once';\nimport BottomTabView from \"../views/BottomTabView\";\nfunction BottomTabNavigator(_ref) {\n  var id = _ref.id,\n    initialRouteName = _ref.initialRouteName,\n    backBehavior = _ref.backBehavior,\n    children = _ref.children,\n    screenListeners = _ref.screenListeners,\n    screenOptions = _ref.screenOptions,\n    sceneContainerStyle = _ref.sceneContainerStyle,\n    restWithDeprecated = _objectWithoutProperties(_ref, _excluded);\n  var lazy = restWithDeprecated.lazy,\n    tabBarOptions = restWithDeprecated.tabBarOptions,\n    rest = _objectWithoutProperties(restWithDeprecated, _excluded2);\n  var defaultScreenOptions = {};\n  if (tabBarOptions) {\n    var _tabBarOptions$labelP;\n    Object.assign(defaultScreenOptions, {\n      tabBarHideOnKeyboard: tabBarOptions.keyboardHidesTabBar,\n      tabBarActiveTintColor: tabBarOptions.activeTintColor,\n      tabBarInactiveTintColor: tabBarOptions.inactiveTintColor,\n      tabBarActiveBackgroundColor: tabBarOptions.activeBackgroundColor,\n      tabBarInactiveBackgroundColor: tabBarOptions.inactiveBackgroundColor,\n      tabBarAllowFontScaling: tabBarOptions.allowFontScaling,\n      tabBarShowLabel: tabBarOptions.showLabel,\n      tabBarLabelStyle: tabBarOptions.labelStyle,\n      tabBarIconStyle: tabBarOptions.iconStyle,\n      tabBarItemStyle: tabBarOptions.tabStyle,\n      tabBarLabelPosition: (_tabBarOptions$labelP = tabBarOptions.labelPosition) != null ? _tabBarOptions$labelP : tabBarOptions.adaptive === false ? 'below-icon' : undefined,\n      tabBarStyle: [{\n        display: tabBarOptions.tabBarVisible ? 'none' : 'flex'\n      }, defaultScreenOptions.tabBarStyle]\n    });\n    Object.keys(defaultScreenOptions).forEach(function (key) {\n      if (defaultScreenOptions[key] === undefined) {\n        delete defaultScreenOptions[key];\n      }\n    });\n    warnOnce(tabBarOptions, `Bottom Tab Navigator: 'tabBarOptions' is deprecated. Migrate the options to 'screenOptions' instead.\\n\\nPlace the following in 'screenOptions' in your code to keep current behavior:\\n\\n${JSON.stringify(defaultScreenOptions, null, 2)}\\n\\nSee https://reactnavigation.org/docs/bottom-tab-navigator#options for more details.`);\n  }\n  if (typeof lazy === 'boolean') {\n    defaultScreenOptions.lazy = lazy;\n    warnOnce(true, `Bottom Tab Navigator: 'lazy' in props is deprecated. Move it to 'screenOptions' instead.\\n\\nSee https://reactnavigation.org/docs/bottom-tab-navigator/#lazy for more details.`);\n  }\n  var _useNavigationBuilder = useNavigationBuilder(TabRouter, {\n      id: id,\n      initialRouteName: initialRouteName,\n      backBehavior: backBehavior,\n      children: children,\n      screenListeners: screenListeners,\n      screenOptions: screenOptions,\n      defaultScreenOptions: defaultScreenOptions\n    }),\n    state = _useNavigationBuilder.state,\n    descriptors = _useNavigationBuilder.descriptors,\n    navigation = _useNavigationBuilder.navigation,\n    NavigationContent = _useNavigationBuilder.NavigationContent;\n  return React.createElement(NavigationContent, null, React.createElement(BottomTabView, _extends({}, rest, {\n    state: state,\n    navigation: navigation,\n    descriptors: descriptors,\n    sceneContainerStyle: sceneContainerStyle\n  })));\n}\nexport default createNavigatorFactory(BottomTabNavigator);", "map": {"version": 3, "names": ["createNavigatorFactory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "warnOnce", "BottomTabView", "BottomTabNavigator", "_ref", "id", "initialRouteName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "screenListeners", "screenOptions", "sceneContainerStyle", "restWithDeprecated", "_objectWithoutProperties", "_excluded", "lazy", "tabBarOptions", "rest", "_excluded2", "defaultScreenOptions", "_tabBarOptions$labelP", "Object", "assign", "tabBarHideOnKeyboard", "keyboardHidesTabBar", "tabBarActiveTintColor", "activeTintColor", "tabBarInactiveTintColor", "inactiveTintColor", "tabBarActiveBackgroundColor", "activeBackgroundColor", "tabBarInactiveBackgroundColor", "inactiveBackgroundColor", "tabBarAllowFontScaling", "allowFontScaling", "tabBarShowLabel", "showLabel", "tabBarLabelStyle", "labelStyle", "tabBarIconStyle", "iconStyle", "tabBarItemStyle", "tabStyle", "tabBarLabelPosition", "labelPosition", "adaptive", "undefined", "tabBarStyle", "display", "tabBarVisible", "keys", "for<PERSON>ach", "key", "JSON", "stringify", "_useNavigationBuilder", "state", "descriptors", "navigation", "NavigationContent", "createElement", "_extends"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/bottom-tabs/src/navigators/createBottomTabNavigator.tsx"], "sourcesContent": ["import {\n  createNavi<PERSON>or<PERSON><PERSON>y,\n  DefaultNavigatorOptions,\n  ParamListBase,\n  TabActionHelpers,\n  TabNavigationState,\n  TabRouter,\n  TabRouterOptions,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport warnOnce from 'warn-once';\n\nimport type {\n  BottomTabNavigationConfig,\n  BottomTabNavigationEventMap,\n  BottomTabNavigationOptions,\n} from '../types';\nimport BottomTabView from '../views/BottomTabView';\n\ntype Props = DefaultNavigatorOptions<\n  ParamListBase,\n  TabNavigationState<ParamListBase>,\n  BottomTabNavigationOptions,\n  BottomTabNavigationEventMap\n> &\n  TabRouterOptions &\n  BottomTabNavigationConfig;\n\nfunction BottomTabNavigator({\n  id,\n  initialRouteName,\n  backBehavior,\n  children,\n  screenListeners,\n  screenOptions,\n  sceneContainerStyle,\n  ...restWithDeprecated\n}: Props) {\n  const {\n    // @ts-expect-error: lazy is deprecated\n    lazy,\n    // @ts-expect-error: tabBarOptions is deprecated\n    tabBarOptions,\n    ...rest\n  } = restWithDeprecated;\n\n  let defaultScreenOptions: BottomTabNavigationOptions = {};\n\n  if (tabBarOptions) {\n    Object.assign(defaultScreenOptions, {\n      tabBarHideOnKeyboard: tabBarOptions.keyboardHidesTabBar,\n      tabBarActiveTintColor: tabBarOptions.activeTintColor,\n      tabBarInactiveTintColor: tabBarOptions.inactiveTintColor,\n      tabBarActiveBackgroundColor: tabBarOptions.activeBackgroundColor,\n      tabBarInactiveBackgroundColor: tabBarOptions.inactiveBackgroundColor,\n      tabBarAllowFontScaling: tabBarOptions.allowFontScaling,\n      tabBarShowLabel: tabBarOptions.showLabel,\n      tabBarLabelStyle: tabBarOptions.labelStyle,\n      tabBarIconStyle: tabBarOptions.iconStyle,\n      tabBarItemStyle: tabBarOptions.tabStyle,\n      tabBarLabelPosition:\n        tabBarOptions.labelPosition ??\n        (tabBarOptions.adaptive === false ? 'below-icon' : undefined),\n      tabBarStyle: [\n        { display: tabBarOptions.tabBarVisible ? 'none' : 'flex' },\n        defaultScreenOptions.tabBarStyle,\n      ],\n    });\n\n    (\n      Object.keys(defaultScreenOptions) as (keyof BottomTabNavigationOptions)[]\n    ).forEach((key) => {\n      if (defaultScreenOptions[key] === undefined) {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete defaultScreenOptions[key];\n      }\n    });\n\n    warnOnce(\n      tabBarOptions,\n      `Bottom Tab Navigator: 'tabBarOptions' is deprecated. Migrate the options to 'screenOptions' instead.\\n\\nPlace the following in 'screenOptions' in your code to keep current behavior:\\n\\n${JSON.stringify(\n        defaultScreenOptions,\n        null,\n        2\n      )}\\n\\nSee https://reactnavigation.org/docs/bottom-tab-navigator#options for more details.`\n    );\n  }\n\n  if (typeof lazy === 'boolean') {\n    defaultScreenOptions.lazy = lazy;\n\n    warnOnce(\n      true,\n      `Bottom Tab Navigator: 'lazy' in props is deprecated. Move it to 'screenOptions' instead.\\n\\nSee https://reactnavigation.org/docs/bottom-tab-navigator/#lazy for more details.`\n    );\n  }\n\n  const { state, descriptors, navigation, NavigationContent } =\n    useNavigationBuilder<\n      TabNavigationState<ParamListBase>,\n      TabRouterOptions,\n      TabActionHelpers<ParamListBase>,\n      BottomTabNavigationOptions,\n      BottomTabNavigationEventMap\n    >(TabRouter, {\n      id,\n      initialRouteName,\n      backBehavior,\n      children,\n      screenListeners,\n      screenOptions,\n      defaultScreenOptions,\n    });\n\n  return (\n    <NavigationContent>\n      <BottomTabView\n        {...rest}\n        state={state}\n        navigation={navigation}\n        descriptors={descriptors}\n        sceneContainerStyle={sceneContainerStyle}\n      />\n    </NavigationContent>\n  );\n}\n\nexport default createNavigatorFactory<\n  TabNavigationState<ParamListBase>,\n  BottomTabNavigationOptions,\n  BottomTabNavigationEventMap,\n  typeof BottomTabNavigator\n>(BottomTabNavigator);\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SACEA,sBAAsB,EAKtBC,SAAS,EAETC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAOhC,OAAOC,aAAa;AAWpB,SAASC,kBAAkBA,CAAAC,IAAA,EASjB;EAAA,IARRC,EAAE,GAQID,IAAA,CARNC,EAAE;IACFC,gBAAgB,GAOVF,IAAA,CAPNE,gBAAgB;IAChBC,YAAY,GAMNH,IAAA,CANNG,YAAY;IACZC,QAAQ,GAKFJ,IAAA,CALNI,QAAQ;IACRC,eAAe,GAITL,IAAA,CAJNK,eAAe;IACfC,aAAa,GAGPN,IAAA,CAHNM,aAAa;IACbC,mBAAmB,GAEbP,IAAA,CAFNO,mBAAmB;IAChBC,kBAAA,GAAAC,wBAAA,CACGT,IAAA,EAAAU,SAAA;EACN,IAEEC,IAAI,GAIFH,kBAAkB,CAJpBG,IAAI;IAEJC,aAAa,GAEXJ,kBAAkB,CAFpBI,aAAa;IACVC,IAAA,GAAAJ,wBAAA,CACDD,kBAAkB,EAAAM,UAAA;EAEtB,IAAIC,oBAAgD,GAAG,CAAC,CAAC;EAEzD,IAAIH,aAAa,EAAE;IAAA,IAAAI,qBAAA;IACjBC,MAAM,CAACC,MAAM,CAACH,oBAAoB,EAAE;MAClCI,oBAAoB,EAAEP,aAAa,CAACQ,mBAAmB;MACvDC,qBAAqB,EAAET,aAAa,CAACU,eAAe;MACpDC,uBAAuB,EAAEX,aAAa,CAACY,iBAAiB;MACxDC,2BAA2B,EAAEb,aAAa,CAACc,qBAAqB;MAChEC,6BAA6B,EAAEf,aAAa,CAACgB,uBAAuB;MACpEC,sBAAsB,EAAEjB,aAAa,CAACkB,gBAAgB;MACtDC,eAAe,EAAEnB,aAAa,CAACoB,SAAS;MACxCC,gBAAgB,EAAErB,aAAa,CAACsB,UAAU;MAC1CC,eAAe,EAAEvB,aAAa,CAACwB,SAAS;MACxCC,eAAe,EAAEzB,aAAa,CAAC0B,QAAQ;MACvCC,mBAAmB,GAAAvB,qBAAA,GACjBJ,aAAa,CAAC4B,aAAa,YAAAxB,qBAAA,GAC1BJ,aAAa,CAAC6B,QAAQ,KAAK,KAAK,GAAG,YAAY,GAAGC,SAAU;MAC/DC,WAAW,EAAE,CACX;QAAEC,OAAO,EAAEhC,aAAa,CAACiC,aAAa,GAAG,MAAM,GAAG;MAAO,CAAC,EAC1D9B,oBAAoB,CAAC4B,WAAW;IAEpC,CAAC,CAAC;IAGA1B,MAAM,CAAC6B,IAAI,CAAC/B,oBAAoB,CAAC,CACjCgC,OAAO,CAAE,UAAAC,GAAG,EAAK;MACjB,IAAIjC,oBAAoB,CAACiC,GAAG,CAAC,KAAKN,SAAS,EAAE;QAE3C,OAAO3B,oBAAoB,CAACiC,GAAG,CAAC;MAClC;IACF,CAAC,CAAC;IAEFnD,QAAQ,CACNe,aAAa,EACZ,4LAA2LqC,IAAI,CAACC,SAAS,CACxMnC,oBAAoB,EACpB,IAAI,EACJ,CAAC,CACD,yFAAwF,CAC3F;EACH;EAEA,IAAI,OAAOJ,IAAI,KAAK,SAAS,EAAE;IAC7BI,oBAAoB,CAACJ,IAAI,GAAGA,IAAI;IAEhCd,QAAQ,CACN,IAAI,EACH,+KAA8K,CAChL;EACH;EAEA,IAAAsD,qBAAA,GACExD,oBAAoB,CAMlBD,SAAS,EAAE;MACXO,EAAE,EAAFA,EAAE;MACFC,gBAAgB,EAAhBA,gBAAgB;MAChBC,YAAY,EAAZA,YAAY;MACZC,QAAQ,EAARA,QAAQ;MACRC,eAAe,EAAfA,eAAe;MACfC,aAAa,EAAbA,aAAa;MACbS,oBAAA,EAAAA;IACF,CAAC,CAAC;IAfIqC,KAAK,GAAAD,qBAAA,CAALC,KAAK;IAAEC,WAAW,GAAAF,qBAAA,CAAXE,WAAW;IAAEC,UAAU,GAAAH,qBAAA,CAAVG,UAAU;IAAEC,iBAAA,GAAAJ,qBAAA,CAAAI,iBAAA;EAiBxC,OACE3D,KAAA,CAAA4D,aAAA,CAACD,iBAAiB,QAChB3D,KAAA,CAAA4D,aAAA,CAAC1D,aAAa,EAAA2D,QAAA,KACR5C,IAAI;IACRuC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA,WAAY;IACzB9C,mBAAmB,EAAEA;EAAoB,GACzC,CACgB;AAExB;AAEA,eAAed,sBAAsB,CAKnCM,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}