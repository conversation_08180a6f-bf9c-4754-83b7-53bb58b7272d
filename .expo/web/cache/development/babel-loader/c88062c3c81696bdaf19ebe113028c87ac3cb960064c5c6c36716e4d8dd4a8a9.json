{"ast": null, "code": "import * as React from 'react';\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nimport NavigationStateContext from \"./NavigationStateContext\";\nexport default function useOptionsGetters(_ref) {\n  var key = _ref.key,\n    options = _ref.options,\n    navigation = _ref.navigation;\n  var optionsRef = React.useRef(options);\n  var optionsGettersFromChildRef = React.useRef({});\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    onOptionsChange = _React$useContext.onOptionsChange;\n  var _React$useContext2 = React.useContext(NavigationStateContext),\n    parentAddOptionsGetter = _React$useContext2.addOptionsGetter;\n  var optionsChangeListener = React.useCallback(function () {\n    var _ref2;\n    var isFocused = (_ref2 = navigation === null || navigation === void 0 ? void 0 : navigation.isFocused()) != null ? _ref2 : true;\n    var hasChildren = Object.keys(optionsGettersFromChildRef.current).length;\n    if (isFocused && !hasChildren) {\n      var _optionsRef$current;\n      onOptionsChange((_optionsRef$current = optionsRef.current) != null ? _optionsRef$current : {});\n    }\n  }, [navigation, onOptionsChange]);\n  React.useEffect(function () {\n    optionsRef.current = options;\n    optionsChangeListener();\n    return navigation === null || navigation === void 0 ? void 0 : navigation.addListener('focus', optionsChangeListener);\n  }, [navigation, options, optionsChangeListener]);\n  var getOptionsFromListener = React.useCallback(function () {\n    for (var _key in optionsGettersFromChildRef.current) {\n      if (optionsGettersFromChildRef.current.hasOwnProperty(_key)) {\n        var _optionsGettersFromCh, _optionsGettersFromCh2;\n        var result = (_optionsGettersFromCh = (_optionsGettersFromCh2 = optionsGettersFromChildRef.current)[_key]) === null || _optionsGettersFromCh === void 0 ? void 0 : _optionsGettersFromCh.call(_optionsGettersFromCh2);\n        if (result !== null) {\n          return result;\n        }\n      }\n    }\n    return null;\n  }, []);\n  var getCurrentOptions = React.useCallback(function () {\n    var _ref3;\n    var isFocused = (_ref3 = navigation === null || navigation === void 0 ? void 0 : navigation.isFocused()) != null ? _ref3 : true;\n    if (!isFocused) {\n      return null;\n    }\n    var optionsFromListener = getOptionsFromListener();\n    if (optionsFromListener !== null) {\n      return optionsFromListener;\n    }\n    return optionsRef.current;\n  }, [navigation, getOptionsFromListener]);\n  React.useEffect(function () {\n    return parentAddOptionsGetter === null || parentAddOptionsGetter === void 0 ? void 0 : parentAddOptionsGetter(key, getCurrentOptions);\n  }, [getCurrentOptions, parentAddOptionsGetter, key]);\n  var addOptionsGetter = React.useCallback(function (key, getter) {\n    optionsGettersFromChildRef.current[key] = getter;\n    optionsChangeListener();\n    return function () {\n      delete optionsGettersFromChildRef.current[key];\n      optionsChangeListener();\n    };\n  }, [optionsChangeListener]);\n  return {\n    addOptionsGetter: addOptionsGetter,\n    getCurrentOptions: getCurrentOptions\n  };\n}", "map": {"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationStateContext", "useOptionsGetters", "_ref", "key", "options", "navigation", "optionsRef", "useRef", "optionsGettersFromChildRef", "_React$useContext", "useContext", "onOptionsChange", "_React$useContext2", "parentAddOptionsGetter", "addOptionsGetter", "optionsChangeListener", "useCallback", "_ref2", "isFocused", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "current", "length", "_optionsRef$current", "useEffect", "addListener", "getOptionsFromListener", "hasOwnProperty", "_optionsGettersFromCh", "_optionsGettersFromCh2", "result", "call", "getCurrentOptions", "_ref3", "optionsFromListener", "getter"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/useOptionsGetters.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationBuilderContext from './NavigationBuilderContext';\nimport NavigationStateContext from './NavigationStateContext';\nimport type { NavigationProp } from './types';\n\ntype Options = {\n  key?: string;\n  navigation?: NavigationProp<ParamListBase>;\n  options?: object | undefined;\n};\n\nexport default function useOptionsGetters({\n  key,\n  options,\n  navigation,\n}: Options) {\n  const optionsRef = React.useRef<object | undefined>(options);\n  const optionsGettersFromChildRef = React.useRef<\n    Record<string, () => object | undefined | null>\n  >({});\n\n  const { onOptionsChange } = React.useContext(NavigationBuilderContext);\n  const { addOptionsGetter: parentAddOptionsGetter } = React.useContext(\n    NavigationStateContext\n  );\n\n  const optionsChangeListener = React.useCallback(() => {\n    const isFocused = navigation?.isFocused() ?? true;\n    const hasChildren = Object.keys(optionsGettersFromChildRef.current).length;\n\n    if (isFocused && !hasChildren) {\n      onOptionsChange(optionsRef.current ?? {});\n    }\n  }, [navigation, onOptionsChange]);\n\n  React.useEffect(() => {\n    optionsRef.current = options;\n    optionsChangeListener();\n\n    return navigation?.addListener('focus', optionsChangeListener);\n  }, [navigation, options, optionsChangeListener]);\n\n  const getOptionsFromListener = React.useCallback(() => {\n    for (let key in optionsGettersFromChildRef.current) {\n      if (optionsGettersFromChildRef.current.hasOwnProperty(key)) {\n        const result = optionsGettersFromChildRef.current[key]?.();\n\n        // null means unfocused route\n        if (result !== null) {\n          return result;\n        }\n      }\n    }\n\n    return null;\n  }, []);\n\n  const getCurrentOptions = React.useCallback(() => {\n    const isFocused = navigation?.isFocused() ?? true;\n\n    if (!isFocused) {\n      return null;\n    }\n\n    const optionsFromListener = getOptionsFromListener();\n\n    if (optionsFromListener !== null) {\n      return optionsFromListener;\n    }\n\n    return optionsRef.current;\n  }, [navigation, getOptionsFromListener]);\n\n  React.useEffect(() => {\n    return parentAddOptionsGetter?.(key!, getCurrentOptions);\n  }, [getCurrentOptions, parentAddOptionsGetter, key]);\n\n  const addOptionsGetter = React.useCallback(\n    (key: string, getter: () => object | undefined | null) => {\n      optionsGettersFromChildRef.current[key] = getter;\n      optionsChangeListener();\n\n      return () => {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete optionsGettersFromChildRef.current[key];\n        optionsChangeListener();\n      };\n    },\n    [optionsChangeListener]\n  );\n\n  return {\n    addOptionsGetter,\n    getCurrentOptions,\n  };\n}\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAC/B,OAAOC,sBAAsB;AAS7B,eAAe,SAASC,iBAAiBA,CAAAC,IAAA,EAI7B;EAAA,IAHVC,GAAG,GAGKD,IAAA,CAHRC,GAAG;IACHC,OAAO,GAECF,IAAA,CAFRE,OAAO;IACPC,UAAA,GACQH,IAAA,CADRG,UAAA;EAEA,IAAMC,UAAU,GAAGR,KAAK,CAACS,MAAM,CAAqBH,OAAO,CAAC;EAC5D,IAAMI,0BAA0B,GAAGV,KAAK,CAACS,MAAM,CAE7C,CAAC,CAAC,CAAC;EAEL,IAAAE,iBAAA,GAA4BX,KAAK,CAACY,UAAU,CAACX,wBAAwB,CAAC;IAA9DY,eAAA,GAAAF,iBAAA,CAAAE,eAAA;EACR,IAAAC,kBAAA,GAAqDd,KAAK,CAACY,UAAU,CACnEV,sBAAsB,CACvB;IAFyBa,sBAAA,GAAAD,kBAAA,CAAlBE,gBAAgB;EAIxB,IAAMC,qBAAqB,GAAGjB,KAAK,CAACkB,WAAW,CAAC,YAAM;IAAA,IAAAC,KAAA;IACpD,IAAMC,SAAS,IAAAD,KAAA,GAAGZ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,SAAS,EAAE,YAAAD,KAAA,GAAI,IAAI;IACjD,IAAME,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACb,0BAA0B,CAACc,OAAO,CAAC,CAACC,MAAM;IAE1E,IAAIL,SAAS,IAAI,CAACC,WAAW,EAAE;MAAA,IAAAK,mBAAA;MAC7Bb,eAAe,EAAAa,mBAAA,GAAClB,UAAU,CAACgB,OAAO,YAAAE,mBAAA,GAAI,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACnB,UAAU,EAAEM,eAAe,CAAC,CAAC;EAEjCb,KAAK,CAAC2B,SAAS,CAAC,YAAM;IACpBnB,UAAU,CAACgB,OAAO,GAAGlB,OAAO;IAC5BW,qBAAqB,EAAE;IAEvB,OAAOV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,WAAW,CAAC,OAAO,EAAEX,qBAAqB,CAAC;EAChE,CAAC,EAAE,CAACV,UAAU,EAAED,OAAO,EAAEW,qBAAqB,CAAC,CAAC;EAEhD,IAAMY,sBAAsB,GAAG7B,KAAK,CAACkB,WAAW,CAAC,YAAM;IACrD,KAAK,IAAIb,IAAG,IAAIK,0BAA0B,CAACc,OAAO,EAAE;MAClD,IAAId,0BAA0B,CAACc,OAAO,CAACM,cAAc,CAACzB,IAAG,CAAC,EAAE;QAAA,IAAA0B,qBAAA,EAAAC,sBAAA;QAC1D,IAAMC,MAAM,IAAAF,qBAAA,GAAG,CAAAC,sBAAA,GAAAtB,0BAA0B,CAACc,OAAO,EAACnB,IAAG,CAAC,cAAA0B,qBAAA,uBAAvCA,qBAAA,CAAAG,IAAA,CAAAF,sBAAA,CAA2C;QAG1D,IAAIC,MAAM,KAAK,IAAI,EAAE;UACnB,OAAOA,MAAM;QACf;MACF;IACF;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,IAAME,iBAAiB,GAAGnC,KAAK,CAACkB,WAAW,CAAC,YAAM;IAAA,IAAAkB,KAAA;IAChD,IAAMhB,SAAS,IAAAgB,KAAA,GAAG7B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,SAAS,EAAE,YAAAgB,KAAA,GAAI,IAAI;IAEjD,IAAI,CAAChB,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IAEA,IAAMiB,mBAAmB,GAAGR,sBAAsB,EAAE;IAEpD,IAAIQ,mBAAmB,KAAK,IAAI,EAAE;MAChC,OAAOA,mBAAmB;IAC5B;IAEA,OAAO7B,UAAU,CAACgB,OAAO;EAC3B,CAAC,EAAE,CAACjB,UAAU,EAAEsB,sBAAsB,CAAC,CAAC;EAExC7B,KAAK,CAAC2B,SAAS,CAAC,YAAM;IACpB,OAAOZ,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAGV,GAAG,EAAG8B,iBAAiB,CAAC;EAC1D,CAAC,EAAE,CAACA,iBAAiB,EAAEpB,sBAAsB,EAAEV,GAAG,CAAC,CAAC;EAEpD,IAAMW,gBAAgB,GAAGhB,KAAK,CAACkB,WAAW,CACxC,UAACb,GAAW,EAAEiC,MAAuC,EAAK;IACxD5B,0BAA0B,CAACc,OAAO,CAACnB,GAAG,CAAC,GAAGiC,MAAM;IAChDrB,qBAAqB,EAAE;IAEvB,OAAO,YAAM;MAEX,OAAOP,0BAA0B,CAACc,OAAO,CAACnB,GAAG,CAAC;MAC9CY,qBAAqB,EAAE;IACzB,CAAC;EACH,CAAC,EACD,CAACA,qBAAqB,CAAC,CACxB;EAED,OAAO;IACLD,gBAAgB,EAAhBA,gBAAgB;IAChBmB,iBAAA,EAAAA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}