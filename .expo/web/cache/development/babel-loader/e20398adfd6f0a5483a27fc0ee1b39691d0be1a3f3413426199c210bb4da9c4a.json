{"ast": null, "code": "export function splitStyles(styles) {\n  for (var _len = arguments.length, filters = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    filters[_key - 1] = arguments[_key];\n  }\n  if (process.env.NODE_ENV !== 'production' && filters.length === 0) {\n    console.error('No filters were passed when calling splitStyles');\n  }\n  var newStyles = filters.map(function () {\n    return [];\n  });\n  var rest = [];\n  outer: for (var item of Object.entries(styles)) {\n    for (var i = 0; i < filters.length; i++) {\n      if (filters[i](item[0])) {\n        newStyles[i].push(item);\n        continue outer;\n      }\n    }\n    rest.push(item);\n  }\n  newStyles.unshift(rest);\n  return newStyles.map(function (styles) {\n    return Object.fromEntries(styles);\n  });\n}", "map": {"version": 3, "names": ["splitStyles", "styles", "_len", "arguments", "length", "filters", "Array", "_key", "process", "env", "NODE_ENV", "console", "error", "newStyles", "map", "rest", "outer", "item", "Object", "entries", "i", "push", "unshift", "fromEntries"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/react-native-paper/src/utils/splitStyles.ts"], "sourcesContent": ["import type { ViewStyle } from 'react-native';\n\ntype FiltersArray = readonly ((style: keyof ViewStyle) => boolean)[];\n\ntype MappedTuple<Tuple extends FiltersArray> = {\n  [Index in keyof Tuple]: ViewStyle;\n} & { length: Tuple['length'] };\n\ntype Style = ViewStyle[keyof ViewStyle];\ntype Entry = [keyof ViewStyle, Style];\n\n/**\n * Utility function to extract styles in separate objects\n *\n * @param styles The style object you want to filter\n * @param filters The filters by which you want to split the styles\n * @returns An array of filtered style objects:\n * - The first style object contains the properties that didn't match any filter\n * - After that there will be a style object for each filter you passed in the same order as the matching filters\n * - A style property will exist in a single style object, the first filter it matched\n */\nexport function splitStyles<Tuple extends FiltersArray>(\n  styles: ViewStyle,\n  ...filters: Tuple\n) {\n  if (process.env.NODE_ENV !== 'production' && filters.length === 0) {\n    console.error('No filters were passed when calling splitStyles');\n  }\n\n  // `Object.entries` will be used to iterate over the styles and `Object.fromEntries` will be called before returning\n  // Entries which match the given filters will be temporarily stored in `newStyles`\n  const newStyles = filters.map(() => [] as Entry[]);\n\n  // Entries which match no filter\n  const rest: Entry[] = [];\n\n  // Iterate every style property\n  outer: for (const item of Object.entries(styles) as Entry[]) {\n    // Check each filter\n    for (let i = 0; i < filters.length; i++) {\n      // Check if filter matches\n      if (filters[i](item[0])) {\n        newStyles[i].push(item); // Push to temporary filtered entries array\n        continue outer; // Skip to checking next style property\n      }\n    }\n\n    // Adds to rest styles if not filtered\n    rest.push(item);\n  }\n\n  // Put unmatched styles in the beginning\n  newStyles.unshift(rest);\n\n  // Convert arrays of entries into objects\n  return newStyles.map((styles) => Object.fromEntries(styles)) as unknown as [\n    ViewStyle,\n    ...MappedTuple<Tuple>\n  ];\n}\n"], "mappings": "AAqBA,OAAO,SAASA,WAAWA,CACzBC,MAAiB,EAEjB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADGC,OAAO,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAPF,OAAO,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEV,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIL,OAAO,CAACD,MAAM,KAAK,CAAC,EAAE;IACjEO,OAAO,CAACC,KAAK,CAAC,iDAAiD,CAAC;EAClE;EAIA,IAAMC,SAAS,GAAGR,OAAO,CAACS,GAAG,CAAC;IAAA,OAAM,EAAa;EAAA,EAAC;EAGlD,IAAMC,IAAa,GAAG,EAAE;EAGxBC,KAAK,EAAE,KAAK,IAAMC,IAAI,IAAIC,MAAM,CAACC,OAAO,CAAClB,MAAM,CAAC,EAAa;IAE3D,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,OAAO,CAACD,MAAM,EAAEgB,CAAC,EAAE,EAAE;MAEvC,IAAIf,OAAO,CAACe,CAAC,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QACvBJ,SAAS,CAACO,CAAC,CAAC,CAACC,IAAI,CAACJ,IAAI,CAAC;QACvB,SAASD,KAAK;MAChB;IACF;IAGAD,IAAI,CAACM,IAAI,CAACJ,IAAI,CAAC;EACjB;EAGAJ,SAAS,CAACS,OAAO,CAACP,IAAI,CAAC;EAGvB,OAAOF,SAAS,CAACC,GAAG,CAAE,UAAAb,MAAM;IAAA,OAAKiB,MAAM,CAACK,WAAW,CAACtB,MAAM,CAAC;EAAA,EAAC;AAI9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}