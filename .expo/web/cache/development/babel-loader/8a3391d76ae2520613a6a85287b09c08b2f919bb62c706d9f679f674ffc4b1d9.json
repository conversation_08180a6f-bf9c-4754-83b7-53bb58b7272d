{"ast": null, "code": "import * as React from 'react';\nexport default React.createContext(undefined);", "map": {"version": 3, "names": ["React", "createContext", "undefined"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-screens/lib/module/TransitionProgressContext.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated } from 'react-native';\n\ntype TransitionProgressContextBody = {\n  progress: Animated.Value;\n  closing: Animated.Value;\n  goingForward: Animated.Value;\n};\n\nexport default React.createContext<TransitionProgressContextBody | undefined>(\n  undefined\n);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAS9B,eAAeA,KAAK,CAACC,aAAa,CAChCC,SACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}