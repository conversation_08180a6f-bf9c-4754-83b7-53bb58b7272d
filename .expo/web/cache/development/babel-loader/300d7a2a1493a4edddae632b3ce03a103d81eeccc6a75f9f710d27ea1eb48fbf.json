{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport useColorScheme from \"react-native-web/dist/exports/useColorScheme\";\nimport { Provider as PaperProvider, MD3DarkTheme, MD3LightTheme, configureFonts } from 'react-native-paper';\nimport { getCurrentUser, updateUserSettings } from \"../services/databaseService\";\nimport MaterialCommunityIcon from \"../utils/MaterialCommunityIcon\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar ThemeContext = createContext({\n  isDarkMode: true,\n  toggleTheme: function toggleTheme() {},\n  theme: MD3DarkTheme\n});\nexport var useTheme = function useTheme() {\n  return useContext(ThemeContext);\n};\nvar lightTheme = _objectSpread(_objectSpread({}, MD3LightTheme), {}, {\n  colors: _objectSpread(_objectSpread({}, MD3LightTheme.colors), {}, {\n    primary: '#4CAF50',\n    secondary: '#8BC34A',\n    tertiary: '#CDDC39',\n    background: '#F5F5F5',\n    surface: '#FFFFFF',\n    error: '#B00020',\n    text: '#121212',\n    onSurface: '#121212',\n    disabled: '#9E9E9E',\n    placeholder: '#9E9E9E',\n    backdrop: 'rgba(0, 0, 0, 0.5)',\n    notification: '#FF9800'\n  })\n});\nvar darkTheme = _objectSpread(_objectSpread({}, MD3DarkTheme), {}, {\n  colors: _objectSpread(_objectSpread({}, MD3DarkTheme.colors), {}, {\n    primary: '#81C784',\n    secondary: '#AED581',\n    tertiary: '#DCE775',\n    background: '#121212',\n    surface: '#1E1E1E',\n    error: '#CF6679',\n    text: '#FFFFFF',\n    onSurface: '#FFFFFF',\n    disabled: '#757575',\n    placeholder: '#757575',\n    backdrop: 'rgba(0, 0, 0, 0.5)',\n    notification: '#FFB74D'\n  })\n});\nexport var ThemeProvider = function ThemeProvider(_ref) {\n  var children = _ref.children;\n  var deviceColorScheme = useColorScheme();\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isDarkMode = _useState2[0],\n    setIsDarkMode = _useState2[1];\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    isLoading = _useState4[0],\n    setIsLoading = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    userId = _useState6[0],\n    setUserId = _useState6[1];\n  useEffect(function () {\n    var loadUserPreferences = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        try {\n          var user = yield getCurrentUser();\n          if (user) {\n            setUserId(user.id);\n            setIsDarkMode(user.dark_mode_enabled === 1);\n          } else {\n            setIsDarkMode(deviceColorScheme === 'dark');\n          }\n        } catch (error) {\n          console.error('Error loading user preferences:', error);\n          setIsDarkMode(true);\n        } finally {\n          setIsLoading(false);\n        }\n      });\n      return function loadUserPreferences() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    loadUserPreferences();\n  }, [deviceColorScheme]);\n  var toggleTheme = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      var newMode = !isDarkMode;\n      setIsDarkMode(newMode);\n      if (userId) {\n        try {\n          yield updateUserSettings(userId, {\n            darkModeEnabled: newMode\n          });\n        } catch (error) {\n          console.error('Error saving theme preference:', error);\n        }\n      }\n    });\n    return function toggleTheme() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var theme = isDarkMode ? darkTheme : lightTheme;\n  var contextValue = {\n    isDarkMode: isDarkMode,\n    toggleTheme: toggleTheme,\n    theme: theme\n  };\n  if (isLoading) {\n    return null;\n  }\n  return _jsx(ThemeContext.Provider, {\n    value: contextValue,\n    children: _jsx(PaperProvider, {\n      theme: theme,\n      settings: {\n        icon: function icon(props) {\n          return _jsx(MaterialCommunityIcon, _objectSpread({}, props));\n        }\n      },\n      children: children\n    })\n  });\n};", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useColorScheme", "Provider", "PaperProvider", "MD3DarkTheme", "MD3LightTheme", "configure<PERSON>onts", "getCurrentUser", "updateUserSettings", "MaterialCommunityIcon", "jsx", "_jsx", "ThemeContext", "isDarkMode", "toggleTheme", "theme", "useTheme", "lightTheme", "_objectSpread", "colors", "primary", "secondary", "tertiary", "background", "surface", "error", "text", "onSurface", "disabled", "placeholder", "backdrop", "notification", "darkTheme", "ThemeProvider", "_ref", "children", "deviceColorScheme", "_useState", "_useState2", "_slicedToArray", "setIsDarkMode", "_useState3", "_useState4", "isLoading", "setIsLoading", "_useState5", "_useState6", "userId", "setUserId", "loadUserPreferences", "_ref2", "_asyncToGenerator", "user", "id", "dark_mode_enabled", "console", "apply", "arguments", "_ref3", "newMode", "darkModeEnabled", "contextValue", "value", "settings", "icon", "props"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/theme/ThemeProvider.js"], "sourcesContent": ["/**\n * Theme Provider for Znü<PERSON>Zähler\n * Provides theme context and management for the app\n */\n\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useColorScheme } from 'react-native';\nimport { Provider as PaperProvider, MD3DarkTheme, MD3LightTheme, configureFonts } from 'react-native-paper';\nimport { getCurrentUser, updateUserSettings } from '../services/databaseService';\nimport MaterialCommunityIcon from '../utils/MaterialCommunityIcon';\n\n// Create theme context\nconst ThemeContext = createContext({\n  isDarkMode: true,\n  toggleTheme: () => {},\n  theme: MD3DarkTheme\n});\n\n// Custom hook to use theme\nexport const useTheme = () => useContext(ThemeContext);\n\n// Define custom themes\nconst lightTheme = {\n  ...MD3LightTheme,\n  colors: {\n    ...MD3LightTheme.colors,\n    primary: '#4CAF50',\n    secondary: '#8BC34A',\n    tertiary: '#CDDC39',\n    background: '#F5F5F5',\n    surface: '#FFFFFF',\n    error: '#B00020',\n    text: '#121212',\n    onSurface: '#121212',\n    disabled: '#9E9E9E',\n    placeholder: '#9E9E9E',\n    backdrop: 'rgba(0, 0, 0, 0.5)',\n    notification: '#FF9800'\n  }\n};\n\nconst darkTheme = {\n  ...MD3DarkTheme,\n  colors: {\n    ...MD3DarkTheme.colors,\n    primary: '#81C784',\n    secondary: '#AED581',\n    tertiary: '#DCE775',\n    background: '#121212',\n    surface: '#1E1E1E',\n    error: '#CF6679',\n    text: '#FFFFFF',\n    onSurface: '#FFFFFF',\n    disabled: '#757575',\n    placeholder: '#757575',\n    backdrop: 'rgba(0, 0, 0, 0.5)',\n    notification: '#FFB74D'\n  }\n};\n\n/**\n * Theme Provider Component\n * @param {Object} props - Component props\n * @returns {JSX.Element} - Theme provider component\n */\nexport const ThemeProvider = ({ children }) => {\n  // Get device color scheme\n  const deviceColorScheme = useColorScheme();\n\n  // State for dark mode\n  const [isDarkMode, setIsDarkMode] = useState(true);\n  const [isLoading, setIsLoading] = useState(true);\n  const [userId, setUserId] = useState(null);\n\n  // Load user preferences\n  useEffect(() => {\n    const loadUserPreferences = async () => {\n      try {\n        const user = await getCurrentUser();\n        if (user) {\n          setUserId(user.id);\n          setIsDarkMode(user.dark_mode_enabled === 1);\n        } else {\n          // Default to device preference if no user settings\n          setIsDarkMode(deviceColorScheme === 'dark');\n        }\n      } catch (error) {\n        console.error('Error loading user preferences:', error);\n        // Default to dark mode if error\n        setIsDarkMode(true);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadUserPreferences();\n  }, [deviceColorScheme]);\n\n  // Toggle theme function\n  const toggleTheme = async () => {\n    const newMode = !isDarkMode;\n    setIsDarkMode(newMode);\n\n    // Save user preference if user exists\n    if (userId) {\n      try {\n        await updateUserSettings(userId, { darkModeEnabled: newMode });\n      } catch (error) {\n        console.error('Error saving theme preference:', error);\n      }\n    }\n  };\n\n  // Get current theme\n  const theme = isDarkMode ? darkTheme : lightTheme;\n\n  // Context value\n  const contextValue = {\n    isDarkMode,\n    toggleTheme,\n    theme\n  };\n\n  // Show nothing while loading\n  if (isLoading) {\n    return null;\n  }\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      <PaperProvider\n        theme={theme}\n        settings={{\n          icon: props => <MaterialCommunityIcon {...props} />\n        }}\n      >\n        {children}\n      </PaperProvider>\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,cAAA;AAE9E,SAASC,QAAQ,IAAIC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,oBAAoB;AAC3G,SAASC,cAAc,EAAEC,kBAAkB;AAC3C,OAAOC,qBAAqB;AAAuC,SAAAC,GAAA,IAAAC,IAAA;AAGnE,IAAMC,YAAY,GAAGf,aAAa,CAAC;EACjCgB,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAQ,CAAC,CAAC;EACrBC,KAAK,EAAEX;AACT,CAAC,CAAC;AAGF,OAAO,IAAMY,QAAQ,GAAG,SAAXA,QAAQA,CAAA;EAAA,OAASjB,UAAU,CAACa,YAAY,CAAC;AAAA;AAGtD,IAAMK,UAAU,GAAAC,aAAA,CAAAA,aAAA,KACXb,aAAa;EAChBc,MAAM,EAAAD,aAAA,CAAAA,aAAA,KACDb,aAAa,CAACc,MAAM;IACvBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,SAAS;IACtBC,QAAQ,EAAE,oBAAoB;IAC9BC,YAAY,EAAE;EAAS;AACxB,EACF;AAED,IAAMC,SAAS,GAAAd,aAAA,CAAAA,aAAA,KACVd,YAAY;EACfe,MAAM,EAAAD,aAAA,CAAAA,aAAA,KACDd,YAAY,CAACe,MAAM;IACtBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,SAAS;IACtBC,QAAQ,EAAE,oBAAoB;IAC9BC,YAAY,EAAE;EAAS;AACxB,EACF;AAOD,OAAO,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAEtC,IAAMC,iBAAiB,GAAGnC,cAAc,CAAC,CAAC;EAG1C,IAAAoC,SAAA,GAAoCvC,QAAQ,CAAC,IAAI,CAAC;IAAAwC,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA3CxB,UAAU,GAAAyB,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAAkC3C,QAAQ,CAAC,IAAI,CAAC;IAAA4C,UAAA,GAAAH,cAAA,CAAAE,UAAA;IAAzCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4B/C,QAAQ,CAAC,IAAI,CAAC;IAAAgD,UAAA,GAAAP,cAAA,CAAAM,UAAA;IAAnCE,MAAM,GAAAD,UAAA;IAAEE,SAAS,GAAAF,UAAA;EAGxB9C,SAAS,CAAC,YAAM;IACd,IAAMiD,mBAAmB;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QACtC,IAAI;UACF,IAAMC,IAAI,SAAS7C,cAAc,CAAC,CAAC;UACnC,IAAI6C,IAAI,EAAE;YACRJ,SAAS,CAACI,IAAI,CAACC,EAAE,CAAC;YAClBb,aAAa,CAACY,IAAI,CAACE,iBAAiB,KAAK,CAAC,CAAC;UAC7C,CAAC,MAAM;YAELd,aAAa,CAACJ,iBAAiB,KAAK,MAAM,CAAC;UAC7C;QACF,CAAC,CAAC,OAAOX,KAAK,EAAE;UACd8B,OAAO,CAAC9B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UAEvDe,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,SAAS;UACRI,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;MAAA,gBAjBKK,mBAAmBA,CAAA;QAAA,OAAAC,KAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;IAAA,GAiBxB;IAEDR,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACb,iBAAiB,CAAC,CAAC;EAGvB,IAAMtB,WAAW;IAAA,IAAA4C,KAAA,GAAAP,iBAAA,CAAG,aAAY;MAC9B,IAAMQ,OAAO,GAAG,CAAC9C,UAAU;MAC3B2B,aAAa,CAACmB,OAAO,CAAC;MAGtB,IAAIZ,MAAM,EAAE;QACV,IAAI;UACF,MAAMvC,kBAAkB,CAACuC,MAAM,EAAE;YAAEa,eAAe,EAAED;UAAQ,CAAC,CAAC;QAChE,CAAC,CAAC,OAAOlC,KAAK,EAAE;UACd8B,OAAO,CAAC9B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC;IAAA,gBAZKX,WAAWA,CAAA;MAAA,OAAA4C,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAYhB;EAGD,IAAM1C,KAAK,GAAGF,UAAU,GAAGmB,SAAS,GAAGf,UAAU;EAGjD,IAAM4C,YAAY,GAAG;IACnBhD,UAAU,EAAVA,UAAU;IACVC,WAAW,EAAXA,WAAW;IACXC,KAAK,EAALA;EACF,CAAC;EAGD,IAAI4B,SAAS,EAAE;IACb,OAAO,IAAI;EACb;EAEA,OACEhC,IAAA,CAACC,YAAY,CAACV,QAAQ;IAAC4D,KAAK,EAAED,YAAa;IAAA1B,QAAA,EACzCxB,IAAA,CAACR,aAAa;MACZY,KAAK,EAAEA,KAAM;MACbgD,QAAQ,EAAE;QACRC,IAAI,EAAE,SAANA,IAAIA,CAAEC,KAAK;UAAA,OAAItD,IAAA,CAACF,qBAAqB,EAAAS,aAAA,KAAK+C,KAAK,CAAG,CAAC;QAAA;MACrD,CAAE;MAAA9B,QAAA,EAEDA;IAAQ,CACI;EAAC,CACK,CAAC;AAE5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}