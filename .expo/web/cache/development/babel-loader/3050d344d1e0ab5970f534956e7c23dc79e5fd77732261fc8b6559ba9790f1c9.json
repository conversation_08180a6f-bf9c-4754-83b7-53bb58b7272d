{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { TextInput, Button, Chip, Divider, Text, HelperText, IconButton } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { getAllNutrients, getAllergens, saveFood } from \"../services/databaseService\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar FoodForm = function FoodForm(_ref) {\n  var initialFood = _ref.initialFood,\n    onSave = _ref.onSave,\n    onCancel = _ref.onCancel;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    nutrients = _useState2[0],\n    setNutrients = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    allergens = _useState4[0],\n    setAllergens = _useState4[1];\n  var _useState5 = useState([]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    selectedAllergens = _useState6[0],\n    setSelectedAllergens = _useState6[1];\n  var _useState7 = useState(false),\n    _useState8 = _slicedToArray(_useState7, 2),\n    isLoading = _useState8[0],\n    setIsLoading = _useState8[1];\n  var _useState9 = useState({}),\n    _useState0 = _slicedToArray(_useState9, 2),\n    errors = _useState0[0],\n    setErrors = _useState0[1];\n  var _useState1 = useState(initialFood || {\n      name: '',\n      description: '',\n      barcode: '',\n      brand: '',\n      serving_size: 100,\n      serving_unit: 'g',\n      is_custom: 1,\n      is_favorite: 0,\n      nutrients: [],\n      ingredients: []\n    }),\n    _useState10 = _slicedToArray(_useState1, 2),\n    food = _useState10[0],\n    setFood = _useState10[1];\n  useEffect(function () {\n    var loadData = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        try {\n          setIsLoading(true);\n          var nutrientList = yield getAllNutrients();\n          setNutrients(nutrientList);\n          var allergenList = yield getAllergens();\n          setAllergens(allergenList);\n          if (!initialFood || !initialFood.nutrients || initialFood.nutrients.length === 0) {\n            var defaultNutrients = nutrientList.filter(function (n) {\n              return n.is_macro === 1 || n.name.toLowerCase() === 'calories';\n            }).map(function (n) {\n              return {\n                nutrient_id: n.id,\n                name: n.name,\n                amount: 0,\n                unit: n.unit\n              };\n            });\n            setFood(function (prev) {\n              return _objectSpread(_objectSpread({}, prev), {}, {\n                nutrients: defaultNutrients\n              });\n            });\n          }\n          if (initialFood && initialFood.ingredients) {\n            var allergenIds = initialFood.ingredients.filter(function (i) {\n              return i.is_allergen === 1;\n            }).map(function (i) {\n              return i.ingredient_id;\n            });\n            setSelectedAllergens(allergenIds);\n          }\n        } catch (error) {\n          console.error('Error loading data:', error);\n          Alert.alert('Error', 'Failed to load nutrients and allergens');\n        } finally {\n          setIsLoading(false);\n        }\n      });\n      return function loadData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    loadData();\n  }, [initialFood]);\n  var validateForm = function validateForm() {\n    var newErrors = {};\n    if (!food.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    if (food.barcode && !/^\\d+$/.test(food.barcode)) {\n      newErrors.barcode = 'Barcode must contain only numbers';\n    }\n    if (!food.serving_size || food.serving_size <= 0) {\n      newErrors.serving_size = 'Serving size must be greater than 0';\n    }\n    if (!food.serving_unit.trim()) {\n      newErrors.serving_unit = 'Serving unit is required';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  var handleSave = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      if (!validateForm()) {\n        return;\n      }\n      try {\n        setIsLoading(true);\n        var ingredients = selectedAllergens.map(function (allergenId) {\n          var allergen = allergens.find(function (a) {\n            return a.id === allergenId;\n          });\n          return {\n            ingredient_id: allergenId,\n            name: allergen ? allergen.name : 'Unknown',\n            is_allergen: 1\n          };\n        });\n        var foodToSave = _objectSpread(_objectSpread({}, food), {}, {\n          ingredients: ingredients\n        });\n        var savedFood = yield saveFood(foodToSave);\n        if (onSave) {\n          onSave(savedFood);\n        }\n      } catch (error) {\n        console.error('Error saving food:', error);\n        Alert.alert('Error', 'Failed to save food');\n      } finally {\n        setIsLoading(false);\n      }\n    });\n    return function handleSave() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var handleChange = function handleChange(field, value) {\n    setFood(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, _defineProperty({}, field, value));\n    });\n    if (errors[field]) {\n      setErrors(function (prev) {\n        return _objectSpread(_objectSpread({}, prev), {}, _defineProperty({}, field, undefined));\n      });\n    }\n  };\n  var handleNutrientChange = function handleNutrientChange(nutrientId, value) {\n    setFood(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        nutrients: prev.nutrients.map(function (n) {\n          return n.nutrient_id === nutrientId ? _objectSpread(_objectSpread({}, n), {}, {\n            amount: parseFloat(value) || 0\n          }) : n;\n        })\n      });\n    });\n  };\n  var handleAllergenToggle = function handleAllergenToggle(allergenId) {\n    setSelectedAllergens(function (prev) {\n      if (prev.includes(allergenId)) {\n        return prev.filter(function (id) {\n          return id !== allergenId;\n        });\n      } else {\n        return [].concat(_toConsumableArray(prev), [allergenId]);\n      }\n    });\n  };\n  var toggleFavorite = function toggleFavorite() {\n    setFood(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        is_favorite: prev.is_favorite === 1 ? 0 : 1\n      });\n    });\n  };\n  return _jsxs(ScrollView, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsx(Text, {\n        style: styles.headerTitle,\n        children: initialFood ? 'Edit Food' : 'Add New Food'\n      }), _jsx(IconButton, {\n        icon: food.is_favorite === 1 ? 'star' : 'star-outline',\n        iconColor: food.is_favorite === 1 ? theme.colors.primary : theme.colors.onSurface,\n        onPress: toggleFavorite\n      })]\n    }), _jsx(TextInput, {\n      label: \"Name\",\n      value: food.name,\n      onChangeText: function onChangeText(text) {\n        return handleChange('name', text);\n      },\n      style: styles.input,\n      mode: \"outlined\",\n      error: !!errors.name\n    }), errors.name && _jsx(HelperText, {\n      type: \"error\",\n      children: errors.name\n    }), _jsx(TextInput, {\n      label: \"Description\",\n      value: food.description,\n      onChangeText: function onChangeText(text) {\n        return handleChange('description', text);\n      },\n      style: styles.input,\n      mode: \"outlined\",\n      multiline: true\n    }), _jsx(TextInput, {\n      label: \"Brand\",\n      value: food.brand,\n      onChangeText: function onChangeText(text) {\n        return handleChange('brand', text);\n      },\n      style: styles.input,\n      mode: \"outlined\"\n    }), _jsx(TextInput, {\n      label: \"Barcode\",\n      value: food.barcode,\n      onChangeText: function onChangeText(text) {\n        return handleChange('barcode', text);\n      },\n      style: styles.input,\n      mode: \"outlined\",\n      keyboardType: \"numeric\",\n      error: !!errors.barcode\n    }), errors.barcode && _jsx(HelperText, {\n      type: \"error\",\n      children: errors.barcode\n    }), _jsxs(View, {\n      style: styles.row,\n      children: [_jsxs(View, {\n        style: styles.halfInput,\n        children: [_jsx(TextInput, {\n          label: \"Serving Size\",\n          value: food.serving_size.toString(),\n          onChangeText: function onChangeText(text) {\n            return handleChange('serving_size', parseFloat(text) || 0);\n          },\n          style: styles.input,\n          mode: \"outlined\",\n          keyboardType: \"numeric\",\n          error: !!errors.serving_size\n        }), errors.serving_size && _jsx(HelperText, {\n          type: \"error\",\n          children: errors.serving_size\n        })]\n      }), _jsxs(View, {\n        style: styles.halfInput,\n        children: [_jsx(TextInput, {\n          label: \"Unit\",\n          value: food.serving_unit,\n          onChangeText: function onChangeText(text) {\n            return handleChange('serving_unit', text);\n          },\n          style: styles.input,\n          mode: \"outlined\",\n          error: !!errors.serving_unit\n        }), errors.serving_unit && _jsx(HelperText, {\n          type: \"error\",\n          children: errors.serving_unit\n        })]\n      })]\n    }), _jsx(Text, {\n      style: styles.sectionTitle,\n      children: \"Nutrition Facts (per 100g)\"\n    }), food.nutrients && food.nutrients.map(function (nutrient) {\n      return _jsxs(View, {\n        style: styles.nutrientRow,\n        children: [_jsx(Text, {\n          style: styles.nutrientName,\n          children: nutrient.name\n        }), _jsx(TextInput, {\n          value: nutrient.amount.toString(),\n          onChangeText: function onChangeText(text) {\n            return handleNutrientChange(nutrient.nutrient_id, text);\n          },\n          style: styles.nutrientInput,\n          mode: \"outlined\",\n          keyboardType: \"numeric\",\n          right: _jsx(TextInput.Affix, {\n            text: nutrient.unit\n          })\n        })]\n      }, nutrient.nutrient_id);\n    }), _jsx(Divider, {\n      style: styles.divider\n    }), _jsx(Text, {\n      style: styles.sectionTitle,\n      children: \"Allergens\"\n    }), _jsx(Text, {\n      style: styles.helperText,\n      children: \"Select all allergens present in this food:\"\n    }), _jsx(View, {\n      style: styles.allergensContainer,\n      children: allergens.map(function (allergen) {\n        return _jsx(Chip, {\n          selected: selectedAllergens.includes(allergen.id),\n          onPress: function onPress() {\n            return handleAllergenToggle(allergen.id);\n          },\n          style: [styles.allergenChip, selectedAllergens.includes(allergen.id) && styles.selectedAllergenChip],\n          mode: selectedAllergens.includes(allergen.id) ? 'flat' : 'outlined',\n          children: allergen.name\n        }, allergen.id);\n      })\n    }), _jsxs(View, {\n      style: styles.buttonContainer,\n      children: [_jsx(Button, {\n        mode: \"outlined\",\n        onPress: onCancel,\n        style: styles.button,\n        disabled: isLoading,\n        children: \"Cancel\"\n      }), _jsx(Button, {\n        mode: \"contained\",\n        onPress: handleSave,\n        style: styles.button,\n        loading: isLoading,\n        disabled: isLoading,\n        children: \"Save\"\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 16\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 16\n  },\n  headerTitle: {\n    fontSize: 24,\n    fontWeight: 'bold'\n  },\n  input: {\n    marginBottom: 8\n  },\n  row: {\n    flexDirection: 'row',\n    justifyContent: 'space-between'\n  },\n  halfInput: {\n    width: '48%'\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginTop: 24,\n    marginBottom: 16\n  },\n  helperText: {\n    marginBottom: 8,\n    fontStyle: 'italic'\n  },\n  nutrientRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 8\n  },\n  nutrientName: {\n    flex: 1\n  },\n  nutrientInput: {\n    width: 120\n  },\n  divider: {\n    marginVertical: 24\n  },\n  allergensContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    marginBottom: 24\n  },\n  allergenChip: {\n    margin: 4\n  },\n  selectedAllergenChip: {\n    backgroundColor: '#ffcdd2'\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 16,\n    marginBottom: 32\n  },\n  button: {\n    width: '48%'\n  }\n});\nexport default FoodForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "ScrollView", "<PERSON><PERSON>", "TextInput", "<PERSON><PERSON>", "Chip", "Divider", "Text", "HelperText", "IconButton", "useTheme", "getAllNutrients", "getAllergens", "saveFood", "jsx", "_jsx", "jsxs", "_jsxs", "FoodForm", "_ref", "initialFood", "onSave", "onCancel", "_useTheme", "theme", "_useState", "_useState2", "_slicedToArray", "nutrients", "setNutrients", "_useState3", "_useState4", "allergens", "<PERSON><PERSON><PERSON><PERSON>", "_useState5", "_useState6", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAllergens", "_useState7", "_useState8", "isLoading", "setIsLoading", "_useState9", "_useState0", "errors", "setErrors", "_useState1", "name", "description", "barcode", "brand", "serving_size", "serving_unit", "is_custom", "is_favorite", "ingredients", "_useState10", "food", "setFood", "loadData", "_ref2", "_asyncToGenerator", "nutrientList", "allergenList", "length", "defaultNutrients", "filter", "n", "is_macro", "toLowerCase", "map", "nutrient_id", "id", "amount", "unit", "prev", "_objectSpread", "allergenIds", "i", "is_allergen", "ingredient_id", "error", "console", "alert", "apply", "arguments", "validateForm", "newErrors", "trim", "test", "Object", "keys", "handleSave", "_ref3", "allergenId", "allergen", "find", "a", "foodToSave", "savedFood", "handleChange", "field", "value", "_defineProperty", "undefined", "handleNutrientChange", "nutrientId", "parseFloat", "handleAllergenToggle", "includes", "concat", "_toConsumableArray", "toggleFavorite", "style", "styles", "container", "backgroundColor", "colors", "background", "children", "header", "headerTitle", "icon", "iconColor", "primary", "onSurface", "onPress", "label", "onChangeText", "text", "input", "mode", "type", "multiline", "keyboardType", "row", "halfInput", "toString", "sectionTitle", "nutrient", "nutrientRow", "nutrientName", "nutrientInput", "right", "Affix", "divider", "helperText", "allergensContainer", "selected", "allergenChip", "selectedAllergenChip", "buttonContainer", "button", "disabled", "loading", "create", "flex", "padding", "flexDirection", "justifyContent", "alignItems", "marginBottom", "fontSize", "fontWeight", "width", "marginTop", "fontStyle", "marginVertical", "flexWrap", "margin"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/components/FoodForm.js"], "sourcesContent": ["/**\n * Food Form Component for Znü<PERSON>Zähler\n * Provides a form for adding and editing food items\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, ScrollView, Alert } from 'react-native';\nimport { TextInput, Button, Chip, Divider, Text, HelperText, IconButton } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { getAllNutrients, getAllergens, saveFood } from '../services/databaseService';\n\n/**\n * Food Form Component\n * @param {Object} props - Component props\n * @param {Object} props.initialFood - Initial food data (for editing)\n * @param {Function} props.onSave - Callback function when food is saved\n * @param {Function} props.onCancel - Callback function to cancel\n * @returns {JSX.Element} - Food form component\n */\nconst FoodForm = ({ initialFood, onSave, onCancel }) => {\n  const { theme } = useTheme();\n  const [nutrients, setNutrients] = useState([]);\n  const [allergens, setAllergens] = useState([]);\n  const [selectedAllergens, setSelectedAllergens] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  \n  // Initialize food state\n  const [food, setFood] = useState(initialFood || {\n    name: '',\n    description: '',\n    barcode: '',\n    brand: '',\n    serving_size: 100,\n    serving_unit: 'g',\n    is_custom: 1,\n    is_favorite: 0,\n    nutrients: [],\n    ingredients: []\n  });\n  \n  // Load nutrients and allergens\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n        \n        // Load nutrients\n        const nutrientList = await getAllNutrients();\n        setNutrients(nutrientList);\n        \n        // Load allergens\n        const allergenList = await getAllergens();\n        setAllergens(allergenList);\n        \n        // Initialize nutrients if not provided\n        if (!initialFood || !initialFood.nutrients || initialFood.nutrients.length === 0) {\n          const defaultNutrients = nutrientList\n            .filter(n => n.is_macro === 1 || n.name.toLowerCase() === 'calories')\n            .map(n => ({\n              nutrient_id: n.id,\n              name: n.name,\n              amount: 0,\n              unit: n.unit\n            }));\n          \n          setFood(prev => ({\n            ...prev,\n            nutrients: defaultNutrients\n          }));\n        }\n        \n        // Set selected allergens if editing\n        if (initialFood && initialFood.ingredients) {\n          const allergenIds = initialFood.ingredients\n            .filter(i => i.is_allergen === 1)\n            .map(i => i.ingredient_id);\n          \n          setSelectedAllergens(allergenIds);\n        }\n      } catch (error) {\n        console.error('Error loading data:', error);\n        Alert.alert('Error', 'Failed to load nutrients and allergens');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    loadData();\n  }, [initialFood]);\n  \n  // Validate form\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!food.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    \n    if (food.barcode && !/^\\d+$/.test(food.barcode)) {\n      newErrors.barcode = 'Barcode must contain only numbers';\n    }\n    \n    if (!food.serving_size || food.serving_size <= 0) {\n      newErrors.serving_size = 'Serving size must be greater than 0';\n    }\n    \n    if (!food.serving_unit.trim()) {\n      newErrors.serving_unit = 'Serving unit is required';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  \n  // Handle save\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    \n    try {\n      setIsLoading(true);\n      \n      // Prepare ingredients with allergens\n      const ingredients = selectedAllergens.map(allergenId => {\n        const allergen = allergens.find(a => a.id === allergenId);\n        return {\n          ingredient_id: allergenId,\n          name: allergen ? allergen.name : 'Unknown',\n          is_allergen: 1\n        };\n      });\n      \n      // Save food\n      const foodToSave = {\n        ...food,\n        ingredients\n      };\n      \n      const savedFood = await saveFood(foodToSave);\n      \n      // Call onSave callback\n      if (onSave) {\n        onSave(savedFood);\n      }\n    } catch (error) {\n      console.error('Error saving food:', error);\n      Alert.alert('Error', 'Failed to save food');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  \n  // Handle field change\n  const handleChange = (field, value) => {\n    setFood(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error for this field\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: undefined\n      }));\n    }\n  };\n  \n  // Handle nutrient change\n  const handleNutrientChange = (nutrientId, value) => {\n    setFood(prev => ({\n      ...prev,\n      nutrients: prev.nutrients.map(n => \n        n.nutrient_id === nutrientId ? { ...n, amount: parseFloat(value) || 0 } : n\n      )\n    }));\n  };\n  \n  // Handle allergen toggle\n  const handleAllergenToggle = (allergenId) => {\n    setSelectedAllergens(prev => {\n      if (prev.includes(allergenId)) {\n        return prev.filter(id => id !== allergenId);\n      } else {\n        return [...prev, allergenId];\n      }\n    });\n  };\n  \n  // Toggle favorite\n  const toggleFavorite = () => {\n    setFood(prev => ({\n      ...prev,\n      is_favorite: prev.is_favorite === 1 ? 0 : 1\n    }));\n  };\n  \n  return (\n    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <View style={styles.header}>\n        <Text style={styles.headerTitle}>\n          {initialFood ? 'Edit Food' : 'Add New Food'}\n        </Text>\n        <IconButton\n          icon={food.is_favorite === 1 ? 'star' : 'star-outline'}\n          iconColor={food.is_favorite === 1 ? theme.colors.primary : theme.colors.onSurface}\n          onPress={toggleFavorite}\n        />\n      </View>\n      \n      <TextInput\n        label=\"Name\"\n        value={food.name}\n        onChangeText={(text) => handleChange('name', text)}\n        style={styles.input}\n        mode=\"outlined\"\n        error={!!errors.name}\n      />\n      {errors.name && <HelperText type=\"error\">{errors.name}</HelperText>}\n      \n      <TextInput\n        label=\"Description\"\n        value={food.description}\n        onChangeText={(text) => handleChange('description', text)}\n        style={styles.input}\n        mode=\"outlined\"\n        multiline\n      />\n      \n      <TextInput\n        label=\"Brand\"\n        value={food.brand}\n        onChangeText={(text) => handleChange('brand', text)}\n        style={styles.input}\n        mode=\"outlined\"\n      />\n      \n      <TextInput\n        label=\"Barcode\"\n        value={food.barcode}\n        onChangeText={(text) => handleChange('barcode', text)}\n        style={styles.input}\n        mode=\"outlined\"\n        keyboardType=\"numeric\"\n        error={!!errors.barcode}\n      />\n      {errors.barcode && <HelperText type=\"error\">{errors.barcode}</HelperText>}\n      \n      <View style={styles.row}>\n        <View style={styles.halfInput}>\n          <TextInput\n            label=\"Serving Size\"\n            value={food.serving_size.toString()}\n            onChangeText={(text) => handleChange('serving_size', parseFloat(text) || 0)}\n            style={styles.input}\n            mode=\"outlined\"\n            keyboardType=\"numeric\"\n            error={!!errors.serving_size}\n          />\n          {errors.serving_size && <HelperText type=\"error\">{errors.serving_size}</HelperText>}\n        </View>\n        \n        <View style={styles.halfInput}>\n          <TextInput\n            label=\"Unit\"\n            value={food.serving_unit}\n            onChangeText={(text) => handleChange('serving_unit', text)}\n            style={styles.input}\n            mode=\"outlined\"\n            error={!!errors.serving_unit}\n          />\n          {errors.serving_unit && <HelperText type=\"error\">{errors.serving_unit}</HelperText>}\n        </View>\n      </View>\n      \n      <Text style={styles.sectionTitle}>Nutrition Facts (per 100g)</Text>\n      \n      {food.nutrients && food.nutrients.map((nutrient) => (\n        <View key={nutrient.nutrient_id} style={styles.nutrientRow}>\n          <Text style={styles.nutrientName}>{nutrient.name}</Text>\n          <TextInput\n            value={nutrient.amount.toString()}\n            onChangeText={(text) => handleNutrientChange(nutrient.nutrient_id, text)}\n            style={styles.nutrientInput}\n            mode=\"outlined\"\n            keyboardType=\"numeric\"\n            right={<TextInput.Affix text={nutrient.unit} />}\n          />\n        </View>\n      ))}\n      \n      <Divider style={styles.divider} />\n      \n      <Text style={styles.sectionTitle}>Allergens</Text>\n      <Text style={styles.helperText}>Select all allergens present in this food:</Text>\n      \n      <View style={styles.allergensContainer}>\n        {allergens.map((allergen) => (\n          <Chip\n            key={allergen.id}\n            selected={selectedAllergens.includes(allergen.id)}\n            onPress={() => handleAllergenToggle(allergen.id)}\n            style={[\n              styles.allergenChip,\n              selectedAllergens.includes(allergen.id) && styles.selectedAllergenChip\n            ]}\n            mode={selectedAllergens.includes(allergen.id) ? 'flat' : 'outlined'}\n          >\n            {allergen.name}\n          </Chip>\n        ))}\n      </View>\n      \n      <View style={styles.buttonContainer}>\n        <Button \n          mode=\"outlined\" \n          onPress={onCancel}\n          style={styles.button}\n          disabled={isLoading}\n        >\n          Cancel\n        </Button>\n        <Button \n          mode=\"contained\" \n          onPress={handleSave}\n          style={styles.button}\n          loading={isLoading}\n          disabled={isLoading}\n        >\n          Save\n        </Button>\n      </View>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 16,\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 16,\n  },\n  headerTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n  },\n  input: {\n    marginBottom: 8,\n  },\n  row: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n  halfInput: {\n    width: '48%',\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginTop: 24,\n    marginBottom: 16,\n  },\n  helperText: {\n    marginBottom: 8,\n    fontStyle: 'italic',\n  },\n  nutrientRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 8,\n  },\n  nutrientName: {\n    flex: 1,\n  },\n  nutrientInput: {\n    width: 120,\n  },\n  divider: {\n    marginVertical: 24,\n  },\n  allergensContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    marginBottom: 24,\n  },\n  allergenChip: {\n    margin: 4,\n  },\n  selectedAllergenChip: {\n    backgroundColor: '#ffcdd2',\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 16,\n    marginBottom: 32,\n  },\n  button: {\n    width: '48%',\n  },\n});\n\nexport default FoodForm;\n"], "mappings": ";;;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAEnD,SAASC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AACnG,SAASC,QAAQ;AACjB,SAASC,eAAe,EAAEC,YAAY,EAAEC,QAAQ;AAAsC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAUtF,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAA0C;EAAA,IAApCC,WAAW,GAAAD,IAAA,CAAXC,WAAW;IAAEC,MAAM,GAAAF,IAAA,CAANE,MAAM;IAAEC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;EAC/C,IAAAC,SAAA,GAAkBb,QAAQ,CAAC,CAAC;IAApBc,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,SAAA,GAAkC5B,QAAQ,CAAC,EAAE,CAAC;IAAA6B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAvCG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAkCjC,QAAQ,CAAC,EAAE,CAAC;IAAAkC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAvCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAkDrC,QAAQ,CAAC,EAAE,CAAC;IAAAsC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAvDE,iBAAiB,GAAAD,UAAA;IAAEE,oBAAoB,GAAAF,UAAA;EAC9C,IAAAG,UAAA,GAAkCzC,QAAQ,CAAC,KAAK,CAAC;IAAA0C,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4B7C,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAA8C,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAAjCE,MAAM,GAAAD,UAAA;IAAEE,SAAS,GAAAF,UAAA;EAGxB,IAAAG,UAAA,GAAwBjD,QAAQ,CAACuB,WAAW,IAAI;MAC9C2B,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,GAAG;MACjBC,YAAY,EAAE,GAAG;MACjBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,CAAC;MACd1B,SAAS,EAAE,EAAE;MACb2B,WAAW,EAAE;IACf,CAAC,CAAC;IAAAC,WAAA,GAAA7B,cAAA,CAAAmB,UAAA;IAXKW,IAAI,GAAAD,WAAA;IAAEE,OAAO,GAAAF,WAAA;EAcpB1D,SAAS,CAAC,YAAM;IACd,IAAM6D,QAAQ;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAC3B,IAAI;UACFpB,YAAY,CAAC,IAAI,CAAC;UAGlB,IAAMqB,YAAY,SAASnD,eAAe,CAAC,CAAC;UAC5CkB,YAAY,CAACiC,YAAY,CAAC;UAG1B,IAAMC,YAAY,SAASnD,YAAY,CAAC,CAAC;UACzCqB,YAAY,CAAC8B,YAAY,CAAC;UAG1B,IAAI,CAAC3C,WAAW,IAAI,CAACA,WAAW,CAACQ,SAAS,IAAIR,WAAW,CAACQ,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;YAChF,IAAMC,gBAAgB,GAAGH,YAAY,CAClCI,MAAM,CAAC,UAAAC,CAAC;cAAA,OAAIA,CAAC,CAACC,QAAQ,KAAK,CAAC,IAAID,CAAC,CAACpB,IAAI,CAACsB,WAAW,CAAC,CAAC,KAAK,UAAU;YAAA,EAAC,CACpEC,GAAG,CAAC,UAAAH,CAAC;cAAA,OAAK;gBACTI,WAAW,EAAEJ,CAAC,CAACK,EAAE;gBACjBzB,IAAI,EAAEoB,CAAC,CAACpB,IAAI;gBACZ0B,MAAM,EAAE,CAAC;gBACTC,IAAI,EAAEP,CAAC,CAACO;cACV,CAAC;YAAA,CAAC,CAAC;YAELhB,OAAO,CAAC,UAAAiB,IAAI;cAAA,OAAAC,aAAA,CAAAA,aAAA,KACPD,IAAI;gBACP/C,SAAS,EAAEqC;cAAgB;YAAA,CAC3B,CAAC;UACL;UAGA,IAAI7C,WAAW,IAAIA,WAAW,CAACmC,WAAW,EAAE;YAC1C,IAAMsB,WAAW,GAAGzD,WAAW,CAACmC,WAAW,CACxCW,MAAM,CAAC,UAAAY,CAAC;cAAA,OAAIA,CAAC,CAACC,WAAW,KAAK,CAAC;YAAA,EAAC,CAChCT,GAAG,CAAC,UAAAQ,CAAC;cAAA,OAAIA,CAAC,CAACE,aAAa;YAAA,EAAC;YAE5B3C,oBAAoB,CAACwC,WAAW,CAAC;UACnC;QACF,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC3C/E,KAAK,CAACiF,KAAK,CAAC,OAAO,EAAE,wCAAwC,CAAC;QAChE,CAAC,SAAS;UACR1C,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;MAAA,gBA3CKkB,QAAQA,CAAA;QAAA,OAAAC,KAAA,CAAAwB,KAAA,OAAAC,SAAA;MAAA;IAAA,GA2Cb;IAED1B,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACvC,WAAW,CAAC,CAAC;EAGjB,IAAMkE,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC9B,IAAI,CAACV,IAAI,CAACyC,IAAI,CAAC,CAAC,EAAE;MACrBD,SAAS,CAACxC,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAIU,IAAI,CAACR,OAAO,IAAI,CAAC,OAAO,CAACwC,IAAI,CAAChC,IAAI,CAACR,OAAO,CAAC,EAAE;MAC/CsC,SAAS,CAACtC,OAAO,GAAG,mCAAmC;IACzD;IAEA,IAAI,CAACQ,IAAI,CAACN,YAAY,IAAIM,IAAI,CAACN,YAAY,IAAI,CAAC,EAAE;MAChDoC,SAAS,CAACpC,YAAY,GAAG,qCAAqC;IAChE;IAEA,IAAI,CAACM,IAAI,CAACL,YAAY,CAACoC,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACnC,YAAY,GAAG,0BAA0B;IACrD;IAEAP,SAAS,CAAC0C,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACvB,MAAM,KAAK,CAAC;EAC5C,CAAC;EAGD,IAAM4B,UAAU;IAAA,IAAAC,KAAA,GAAAhC,iBAAA,CAAG,aAAY;MAC7B,IAAI,CAACyB,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;MAEA,IAAI;QACF7C,YAAY,CAAC,IAAI,CAAC;QAGlB,IAAMc,WAAW,GAAGnB,iBAAiB,CAACkC,GAAG,CAAC,UAAAwB,UAAU,EAAI;UACtD,IAAMC,QAAQ,GAAG/D,SAAS,CAACgE,IAAI,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACzB,EAAE,KAAKsB,UAAU;UAAA,EAAC;UACzD,OAAO;YACLd,aAAa,EAAEc,UAAU;YACzB/C,IAAI,EAAEgD,QAAQ,GAAGA,QAAQ,CAAChD,IAAI,GAAG,SAAS;YAC1CgC,WAAW,EAAE;UACf,CAAC;QACH,CAAC,CAAC;QAGF,IAAMmB,UAAU,GAAAtB,aAAA,CAAAA,aAAA,KACXnB,IAAI;UACPF,WAAW,EAAXA;QAAW,EACZ;QAED,IAAM4C,SAAS,SAAStF,QAAQ,CAACqF,UAAU,CAAC;QAG5C,IAAI7E,MAAM,EAAE;UACVA,MAAM,CAAC8E,SAAS,CAAC;QACnB;MACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C/E,KAAK,CAACiF,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC;MAC7C,CAAC,SAAS;QACR1C,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBApCKmD,UAAUA,CAAA;MAAA,OAAAC,KAAA,CAAAT,KAAA,OAAAC,SAAA;IAAA;EAAA,GAoCf;EAGD,IAAMe,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAEC,KAAK,EAAK;IACrC5C,OAAO,CAAC,UAAAiB,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KACPD,IAAI,OAAA4B,eAAA,KACNF,KAAK,EAAGC,KAAK;IAAA,CACd,CAAC;IAGH,IAAI1D,MAAM,CAACyD,KAAK,CAAC,EAAE;MACjBxD,SAAS,CAAC,UAAA8B,IAAI;QAAA,OAAAC,aAAA,CAAAA,aAAA,KACTD,IAAI,OAAA4B,eAAA,KACNF,KAAK,EAAGG,SAAS;MAAA,CAClB,CAAC;IACL;EACF,CAAC;EAGD,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,UAAU,EAAEJ,KAAK,EAAK;IAClD5C,OAAO,CAAC,UAAAiB,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KACPD,IAAI;QACP/C,SAAS,EAAE+C,IAAI,CAAC/C,SAAS,CAAC0C,GAAG,CAAC,UAAAH,CAAC;UAAA,OAC7BA,CAAC,CAACI,WAAW,KAAKmC,UAAU,GAAA9B,aAAA,CAAAA,aAAA,KAAQT,CAAC;YAAEM,MAAM,EAAEkC,UAAU,CAACL,KAAK,CAAC,IAAI;UAAC,KAAKnC,CAAC;QAAA,CAC7E;MAAC;IAAA,CACD,CAAC;EACL,CAAC;EAGD,IAAMyC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAId,UAAU,EAAK;IAC3CzD,oBAAoB,CAAC,UAAAsC,IAAI,EAAI;MAC3B,IAAIA,IAAI,CAACkC,QAAQ,CAACf,UAAU,CAAC,EAAE;QAC7B,OAAOnB,IAAI,CAACT,MAAM,CAAC,UAAAM,EAAE;UAAA,OAAIA,EAAE,KAAKsB,UAAU;QAAA,EAAC;MAC7C,CAAC,MAAM;QACL,UAAAgB,MAAA,CAAAC,kBAAA,CAAWpC,IAAI,IAAEmB,UAAU;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC;EAGD,IAAMkB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3BtD,OAAO,CAAC,UAAAiB,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KACPD,IAAI;QACPrB,WAAW,EAAEqB,IAAI,CAACrB,WAAW,KAAK,CAAC,GAAG,CAAC,GAAG;MAAC;IAAA,CAC3C,CAAC;EACL,CAAC;EAED,OACErC,KAAA,CAAChB,UAAU;IAACgH,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAE5F,KAAK,CAAC6F,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,GAClFtG,KAAA,CAACjB,IAAI;MAACiH,KAAK,EAAEC,MAAM,CAACM,MAAO;MAAAD,QAAA,GACzBxG,IAAA,CAACR,IAAI;QAAC0G,KAAK,EAAEC,MAAM,CAACO,WAAY;QAAAF,QAAA,EAC7BnG,WAAW,GAAG,WAAW,GAAG;MAAc,CACvC,CAAC,EACPL,IAAA,CAACN,UAAU;QACTiH,IAAI,EAAEjE,IAAI,CAACH,WAAW,KAAK,CAAC,GAAG,MAAM,GAAG,cAAe;QACvDqE,SAAS,EAAElE,IAAI,CAACH,WAAW,KAAK,CAAC,GAAG9B,KAAK,CAAC6F,MAAM,CAACO,OAAO,GAAGpG,KAAK,CAAC6F,MAAM,CAACQ,SAAU;QAClFC,OAAO,EAAEd;MAAe,CACzB,CAAC;IAAA,CACE,CAAC,EAEPjG,IAAA,CAACZ,SAAS;MACR4H,KAAK,EAAC,MAAM;MACZzB,KAAK,EAAE7C,IAAI,CAACV,IAAK;MACjBiF,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;QAAA,OAAK7B,YAAY,CAAC,MAAM,EAAE6B,IAAI,CAAC;MAAA,CAAC;MACnDhB,KAAK,EAAEC,MAAM,CAACgB,KAAM;MACpBC,IAAI,EAAC,UAAU;MACflD,KAAK,EAAE,CAAC,CAACrC,MAAM,CAACG;IAAK,CACtB,CAAC,EACDH,MAAM,CAACG,IAAI,IAAIhC,IAAA,CAACP,UAAU;MAAC4H,IAAI,EAAC,OAAO;MAAAb,QAAA,EAAE3E,MAAM,CAACG;IAAI,CAAa,CAAC,EAEnEhC,IAAA,CAACZ,SAAS;MACR4H,KAAK,EAAC,aAAa;MACnBzB,KAAK,EAAE7C,IAAI,CAACT,WAAY;MACxBgF,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;QAAA,OAAK7B,YAAY,CAAC,aAAa,EAAE6B,IAAI,CAAC;MAAA,CAAC;MAC1DhB,KAAK,EAAEC,MAAM,CAACgB,KAAM;MACpBC,IAAI,EAAC,UAAU;MACfE,SAAS;IAAA,CACV,CAAC,EAEFtH,IAAA,CAACZ,SAAS;MACR4H,KAAK,EAAC,OAAO;MACbzB,KAAK,EAAE7C,IAAI,CAACP,KAAM;MAClB8E,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;QAAA,OAAK7B,YAAY,CAAC,OAAO,EAAE6B,IAAI,CAAC;MAAA,CAAC;MACpDhB,KAAK,EAAEC,MAAM,CAACgB,KAAM;MACpBC,IAAI,EAAC;IAAU,CAChB,CAAC,EAEFpH,IAAA,CAACZ,SAAS;MACR4H,KAAK,EAAC,SAAS;MACfzB,KAAK,EAAE7C,IAAI,CAACR,OAAQ;MACpB+E,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;QAAA,OAAK7B,YAAY,CAAC,SAAS,EAAE6B,IAAI,CAAC;MAAA,CAAC;MACtDhB,KAAK,EAAEC,MAAM,CAACgB,KAAM;MACpBC,IAAI,EAAC,UAAU;MACfG,YAAY,EAAC,SAAS;MACtBrD,KAAK,EAAE,CAAC,CAACrC,MAAM,CAACK;IAAQ,CACzB,CAAC,EACDL,MAAM,CAACK,OAAO,IAAIlC,IAAA,CAACP,UAAU;MAAC4H,IAAI,EAAC,OAAO;MAAAb,QAAA,EAAE3E,MAAM,CAACK;IAAO,CAAa,CAAC,EAEzEhC,KAAA,CAACjB,IAAI;MAACiH,KAAK,EAAEC,MAAM,CAACqB,GAAI;MAAAhB,QAAA,GACtBtG,KAAA,CAACjB,IAAI;QAACiH,KAAK,EAAEC,MAAM,CAACsB,SAAU;QAAAjB,QAAA,GAC5BxG,IAAA,CAACZ,SAAS;UACR4H,KAAK,EAAC,cAAc;UACpBzB,KAAK,EAAE7C,IAAI,CAACN,YAAY,CAACsF,QAAQ,CAAC,CAAE;UACpCT,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;YAAA,OAAK7B,YAAY,CAAC,cAAc,EAAEO,UAAU,CAACsB,IAAI,CAAC,IAAI,CAAC,CAAC;UAAA,CAAC;UAC5EhB,KAAK,EAAEC,MAAM,CAACgB,KAAM;UACpBC,IAAI,EAAC,UAAU;UACfG,YAAY,EAAC,SAAS;UACtBrD,KAAK,EAAE,CAAC,CAACrC,MAAM,CAACO;QAAa,CAC9B,CAAC,EACDP,MAAM,CAACO,YAAY,IAAIpC,IAAA,CAACP,UAAU;UAAC4H,IAAI,EAAC,OAAO;UAAAb,QAAA,EAAE3E,MAAM,CAACO;QAAY,CAAa,CAAC;MAAA,CAC/E,CAAC,EAEPlC,KAAA,CAACjB,IAAI;QAACiH,KAAK,EAAEC,MAAM,CAACsB,SAAU;QAAAjB,QAAA,GAC5BxG,IAAA,CAACZ,SAAS;UACR4H,KAAK,EAAC,MAAM;UACZzB,KAAK,EAAE7C,IAAI,CAACL,YAAa;UACzB4E,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;YAAA,OAAK7B,YAAY,CAAC,cAAc,EAAE6B,IAAI,CAAC;UAAA,CAAC;UAC3DhB,KAAK,EAAEC,MAAM,CAACgB,KAAM;UACpBC,IAAI,EAAC,UAAU;UACflD,KAAK,EAAE,CAAC,CAACrC,MAAM,CAACQ;QAAa,CAC9B,CAAC,EACDR,MAAM,CAACQ,YAAY,IAAIrC,IAAA,CAACP,UAAU;UAAC4H,IAAI,EAAC,OAAO;UAAAb,QAAA,EAAE3E,MAAM,CAACQ;QAAY,CAAa,CAAC;MAAA,CAC/E,CAAC;IAAA,CACH,CAAC,EAEPrC,IAAA,CAACR,IAAI;MAAC0G,KAAK,EAAEC,MAAM,CAACwB,YAAa;MAAAnB,QAAA,EAAC;IAA0B,CAAM,CAAC,EAElE9D,IAAI,CAAC7B,SAAS,IAAI6B,IAAI,CAAC7B,SAAS,CAAC0C,GAAG,CAAC,UAACqE,QAAQ;MAAA,OAC7C1H,KAAA,CAACjB,IAAI;QAA4BiH,KAAK,EAAEC,MAAM,CAAC0B,WAAY;QAAArB,QAAA,GACzDxG,IAAA,CAACR,IAAI;UAAC0G,KAAK,EAAEC,MAAM,CAAC2B,YAAa;UAAAtB,QAAA,EAAEoB,QAAQ,CAAC5F;QAAI,CAAO,CAAC,EACxDhC,IAAA,CAACZ,SAAS;UACRmG,KAAK,EAAEqC,QAAQ,CAAClE,MAAM,CAACgE,QAAQ,CAAC,CAAE;UAClCT,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;YAAA,OAAKxB,oBAAoB,CAACkC,QAAQ,CAACpE,WAAW,EAAE0D,IAAI,CAAC;UAAA,CAAC;UACzEhB,KAAK,EAAEC,MAAM,CAAC4B,aAAc;UAC5BX,IAAI,EAAC,UAAU;UACfG,YAAY,EAAC,SAAS;UACtBS,KAAK,EAAEhI,IAAA,CAACZ,SAAS,CAAC6I,KAAK;YAACf,IAAI,EAAEU,QAAQ,CAACjE;UAAK,CAAE;QAAE,CACjD,CAAC;MAAA,GATOiE,QAAQ,CAACpE,WAUd,CAAC;IAAA,CACR,CAAC,EAEFxD,IAAA,CAACT,OAAO;MAAC2G,KAAK,EAAEC,MAAM,CAAC+B;IAAQ,CAAE,CAAC,EAElClI,IAAA,CAACR,IAAI;MAAC0G,KAAK,EAAEC,MAAM,CAACwB,YAAa;MAAAnB,QAAA,EAAC;IAAS,CAAM,CAAC,EAClDxG,IAAA,CAACR,IAAI;MAAC0G,KAAK,EAAEC,MAAM,CAACgC,UAAW;MAAA3B,QAAA,EAAC;IAA0C,CAAM,CAAC,EAEjFxG,IAAA,CAACf,IAAI;MAACiH,KAAK,EAAEC,MAAM,CAACiC,kBAAmB;MAAA5B,QAAA,EACpCvF,SAAS,CAACsC,GAAG,CAAC,UAACyB,QAAQ;QAAA,OACtBhF,IAAA,CAACV,IAAI;UAEH+I,QAAQ,EAAEhH,iBAAiB,CAACyE,QAAQ,CAACd,QAAQ,CAACvB,EAAE,CAAE;UAClDsD,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQlB,oBAAoB,CAACb,QAAQ,CAACvB,EAAE,CAAC;UAAA,CAAC;UACjDyC,KAAK,EAAE,CACLC,MAAM,CAACmC,YAAY,EACnBjH,iBAAiB,CAACyE,QAAQ,CAACd,QAAQ,CAACvB,EAAE,CAAC,IAAI0C,MAAM,CAACoC,oBAAoB,CACtE;UACFnB,IAAI,EAAE/F,iBAAiB,CAACyE,QAAQ,CAACd,QAAQ,CAACvB,EAAE,CAAC,GAAG,MAAM,GAAG,UAAW;UAAA+C,QAAA,EAEnExB,QAAQ,CAAChD;QAAI,GATTgD,QAAQ,CAACvB,EAUV,CAAC;MAAA,CACR;IAAC,CACE,CAAC,EAEPvD,KAAA,CAACjB,IAAI;MAACiH,KAAK,EAAEC,MAAM,CAACqC,eAAgB;MAAAhC,QAAA,GAClCxG,IAAA,CAACX,MAAM;QACL+H,IAAI,EAAC,UAAU;QACfL,OAAO,EAAExG,QAAS;QAClB2F,KAAK,EAAEC,MAAM,CAACsC,MAAO;QACrBC,QAAQ,EAAEjH,SAAU;QAAA+E,QAAA,EACrB;MAED,CAAQ,CAAC,EACTxG,IAAA,CAACX,MAAM;QACL+H,IAAI,EAAC,WAAW;QAChBL,OAAO,EAAElC,UAAW;QACpBqB,KAAK,EAAEC,MAAM,CAACsC,MAAO;QACrBE,OAAO,EAAElH,SAAU;QACnBiH,QAAQ,EAAEjH,SAAU;QAAA+E,QAAA,EACrB;MAED,CAAQ,CAAC;IAAA,CACL,CAAC;EAAA,CACG,CAAC;AAEjB,CAAC;AAED,IAAML,MAAM,GAAGnH,UAAU,CAAC4J,MAAM,CAAC;EAC/BxC,SAAS,EAAE;IACTyC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE;EACX,CAAC;EACDrC,MAAM,EAAE;IACNsC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDxC,WAAW,EAAE;IACXyC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDjC,KAAK,EAAE;IACL+B,YAAY,EAAE;EAChB,CAAC;EACD1B,GAAG,EAAE;IACHuB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDvB,SAAS,EAAE;IACT4B,KAAK,EAAE;EACT,CAAC;EACD1B,YAAY,EAAE;IACZwB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBE,SAAS,EAAE,EAAE;IACbJ,YAAY,EAAE;EAChB,CAAC;EACDf,UAAU,EAAE;IACVe,YAAY,EAAE,CAAC;IACfK,SAAS,EAAE;EACb,CAAC;EACD1B,WAAW,EAAE;IACXkB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDpB,YAAY,EAAE;IACZe,IAAI,EAAE;EACR,CAAC;EACDd,aAAa,EAAE;IACbsB,KAAK,EAAE;EACT,CAAC;EACDnB,OAAO,EAAE;IACPsB,cAAc,EAAE;EAClB,CAAC;EACDpB,kBAAkB,EAAE;IAClBW,aAAa,EAAE,KAAK;IACpBU,QAAQ,EAAE,MAAM;IAChBP,YAAY,EAAE;EAChB,CAAC;EACDZ,YAAY,EAAE;IACZoB,MAAM,EAAE;EACV,CAAC;EACDnB,oBAAoB,EAAE;IACpBlC,eAAe,EAAE;EACnB,CAAC;EACDmC,eAAe,EAAE;IACfO,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BM,SAAS,EAAE,EAAE;IACbJ,YAAY,EAAE;EAChB,CAAC;EACDT,MAAM,EAAE;IACNY,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAelJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}