{"ast": null, "code": "import Platform from \"react-native-web/dist/exports/Platform\";\nimport setColor from 'color';\nimport { grey400, grey800, grey50, grey700, white, black } from \"../../styles/themes/v2/colors\";\nvar getCheckedColor = function getCheckedColor(_ref) {\n  var theme = _ref.theme,\n    color = _ref.color;\n  if (color) {\n    return color;\n  }\n  if (theme.isV3) {\n    return theme.colors.primary;\n  }\n  return theme.colors.accent;\n};\nvar getThumbTintColor = function getThumbTintColor(_ref2) {\n  var theme = _ref2.theme,\n    disabled = _ref2.disabled,\n    value = _ref2.value,\n    checkedColor = _ref2.checkedColor;\n  var isIOS = Platform.OS === 'ios';\n  if (isIOS) {\n    return undefined;\n  }\n  if (disabled) {\n    if (theme.dark) {\n      return grey800;\n    }\n    return grey400;\n  }\n  if (value) {\n    return checkedColor;\n  }\n  if (theme.dark) {\n    return grey400;\n  }\n  return grey50;\n};\nvar getOnTintColor = function getOnTintColor(_ref3) {\n  var theme = _ref3.theme,\n    disabled = _ref3.disabled,\n    value = _ref3.value,\n    checkedColor = _ref3.checkedColor;\n  var isIOS = Platform.OS === 'ios';\n  if (isIOS) {\n    return checkedColor;\n  }\n  if (disabled) {\n    if (theme.dark) {\n      if (theme.isV3) {\n        return setColor(white).alpha(0.06).rgb().string();\n      }\n      return setColor(white).alpha(0.1).rgb().string();\n    }\n    return setColor(black).alpha(0.12).rgb().string();\n  }\n  if (value) {\n    return setColor(checkedColor).alpha(0.5).rgb().string();\n  }\n  if (theme.dark) {\n    return grey700;\n  }\n  return 'rgb(178, 175, 177)';\n};\nexport var getSwitchColor = function getSwitchColor(_ref4) {\n  var theme = _ref4.theme,\n    disabled = _ref4.disabled,\n    value = _ref4.value,\n    color = _ref4.color;\n  var checkedColor = getCheckedColor({\n    theme: theme,\n    color: color\n  });\n  return {\n    onTintColor: getOnTintColor({\n      theme: theme,\n      disabled: disabled,\n      value: value,\n      checkedColor: checkedColor\n    }),\n    thumbTintColor: getThumbTintColor({\n      theme: theme,\n      disabled: disabled,\n      value: value,\n      checkedColor: checkedColor\n    }),\n    checkedColor: checkedColor\n  };\n};", "map": {"version": 3, "names": ["setColor", "grey400", "grey800", "grey50", "grey700", "white", "black", "getCheckedColor", "_ref", "theme", "color", "isV3", "colors", "primary", "accent", "getThumbTintColor", "_ref2", "disabled", "value", "checkedColor", "isIOS", "Platform", "OS", "undefined", "dark", "getOnTintColor", "_ref3", "alpha", "rgb", "string", "getSwitchColor", "_ref4", "onTintColor", "thumbTintColor"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Switch/utils.ts"], "sourcesContent": ["import { Platform } from 'react-native';\n\nimport setColor from 'color';\n\nimport {\n  grey400,\n  grey800,\n  grey50,\n  grey700,\n  white,\n  black,\n} from '../../styles/themes/v2/colors';\nimport type { InternalTheme } from '../../types';\n\ntype BaseProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n  value?: boolean;\n};\n\nconst getCheckedColor = ({\n  theme,\n  color,\n}: {\n  theme: InternalTheme;\n  color?: string;\n}) => {\n  if (color) {\n    return color;\n  }\n\n  if (theme.isV3) {\n    return theme.colors.primary;\n  }\n\n  return theme.colors.accent;\n};\n\nconst getThumbTintColor = ({\n  theme,\n  disabled,\n  value,\n  checkedColor,\n}: BaseProps & { checkedColor: string }) => {\n  const isIOS = Platform.OS === 'ios';\n\n  if (isIOS) {\n    return undefined;\n  }\n\n  if (disabled) {\n    if (theme.dark) {\n      return grey800;\n    }\n    return grey400;\n  }\n\n  if (value) {\n    return checkedColor;\n  }\n\n  if (theme.dark) {\n    return grey400;\n  }\n  return grey50;\n};\n\nconst getOnTintColor = ({\n  theme,\n  disabled,\n  value,\n  checkedColor,\n}: BaseProps & { checkedColor: string }) => {\n  const isIOS = Platform.OS === 'ios';\n\n  if (isIOS) {\n    return checkedColor;\n  }\n\n  if (disabled) {\n    if (theme.dark) {\n      if (theme.isV3) {\n        return setColor(white).alpha(0.06).rgb().string();\n      }\n      return setColor(white).alpha(0.1).rgb().string();\n    }\n    return setColor(black).alpha(0.12).rgb().string();\n  }\n\n  if (value) {\n    return setColor(checkedColor).alpha(0.5).rgb().string();\n  }\n\n  if (theme.dark) {\n    return grey700;\n  }\n  return 'rgb(178, 175, 177)';\n};\n\nexport const getSwitchColor = ({\n  theme,\n  disabled,\n  value,\n  color,\n}: BaseProps & { color?: string }) => {\n  const checkedColor = getCheckedColor({ theme, color });\n\n  return {\n    onTintColor: getOnTintColor({ theme, disabled, value, checkedColor }),\n    thumbTintColor: getThumbTintColor({ theme, disabled, value, checkedColor }),\n    checkedColor,\n  };\n};\n"], "mappings": ";AAEA,OAAOA,QAAQ,MAAM,OAAO;AAE5B,SACEC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,KAAK;AAUP,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,IAAA,EAMlB;EAAA,IALJC,KAAK,GAKND,IAAA,CALCC,KAAK;IACLC,KAAA,GAIDF,IAAA,CAJCE,KAAA;EAKA,IAAIA,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EAEA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACC,OAAO;EAC7B;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,MAAM;AAC5B,CAAC;AAED,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAGC,KAAA,EAKkB;EAAA,IAJ1CP,KAAK,GAIgCO,KAAA,CAJrCP,KAAK;IACLQ,QAAQ,GAG6BD,KAAA,CAHrCC,QAAQ;IACRC,KAAK,GAEgCF,KAAA,CAFrCE,KAAK;IACLC,YAAA,GACqCH,KAAA,CADrCG,YAAA;EAEA,IAAMC,KAAK,GAAGC,QAAQ,CAACC,EAAE,KAAK,KAAK;EAEnC,IAAIF,KAAK,EAAE;IACT,OAAOG,SAAS;EAClB;EAEA,IAAIN,QAAQ,EAAE;IACZ,IAAIR,KAAK,CAACe,IAAI,EAAE;MACd,OAAOtB,OAAO;IAChB;IACA,OAAOD,OAAO;EAChB;EAEA,IAAIiB,KAAK,EAAE;IACT,OAAOC,YAAY;EACrB;EAEA,IAAIV,KAAK,CAACe,IAAI,EAAE;IACd,OAAOvB,OAAO;EAChB;EACA,OAAOE,MAAM;AACf,CAAC;AAED,IAAMsB,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,KAAA,EAKqB;EAAA,IAJ1CjB,KAAK,GAIgCiB,KAAA,CAJrCjB,KAAK;IACLQ,QAAQ,GAG6BS,KAAA,CAHrCT,QAAQ;IACRC,KAAK,GAEgCQ,KAAA,CAFrCR,KAAK;IACLC,YAAA,GACqCO,KAAA,CADrCP,YAAA;EAEA,IAAMC,KAAK,GAAGC,QAAQ,CAACC,EAAE,KAAK,KAAK;EAEnC,IAAIF,KAAK,EAAE;IACT,OAAOD,YAAY;EACrB;EAEA,IAAIF,QAAQ,EAAE;IACZ,IAAIR,KAAK,CAACe,IAAI,EAAE;MACd,IAAIf,KAAK,CAACE,IAAI,EAAE;QACd,OAAOX,QAAQ,CAACK,KAAK,CAAC,CAACsB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACnD;MACA,OAAO7B,QAAQ,CAACK,KAAK,CAAC,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAClD;IACA,OAAO7B,QAAQ,CAACM,KAAK,CAAC,CAACqB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACnD;EAEA,IAAIX,KAAK,EAAE;IACT,OAAOlB,QAAQ,CAACmB,YAAY,CAAC,CAACQ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACzD;EAEA,IAAIpB,KAAK,CAACe,IAAI,EAAE;IACd,OAAOpB,OAAO;EAChB;EACA,OAAO,oBAAoB;AAC7B,CAAC;AAED,OAAO,IAAM0B,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,KAAA,EAKQ;EAAA,IAJpCtB,KAAK,GAI0BsB,KAAA,CAJ/BtB,KAAK;IACLQ,QAAQ,GAGuBc,KAAA,CAH/Bd,QAAQ;IACRC,KAAK,GAE0Ba,KAAA,CAF/Bb,KAAK;IACLR,KAAA,GAC+BqB,KAAA,CAD/BrB,KAAA;EAEA,IAAMS,YAAY,GAAGZ,eAAe,CAAC;IAAEE,KAAK,EAALA,KAAK;IAAEC,KAAA,EAAAA;EAAM,CAAC,CAAC;EAEtD,OAAO;IACLsB,WAAW,EAAEP,cAAc,CAAC;MAAEhB,KAAK,EAALA,KAAK;MAAEQ,QAAQ,EAARA,QAAQ;MAAEC,KAAK,EAALA,KAAK;MAAEC,YAAA,EAAAA;IAAa,CAAC,CAAC;IACrEc,cAAc,EAAElB,iBAAiB,CAAC;MAAEN,KAAK,EAALA,KAAK;MAAEQ,QAAQ,EAARA,QAAQ;MAAEC,KAAK,EAALA,KAAK;MAAEC,YAAA,EAAAA;IAAa,CAAC,CAAC;IAC3EA,YAAA,EAAAA;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}