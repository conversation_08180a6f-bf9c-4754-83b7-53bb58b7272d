{"ast": null, "code": "export * from \"./FileSystem\";\nexport * from \"./FileSystem.types\";", "map": {"version": 3, "names": [], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-file-system/src/index.ts"], "sourcesContent": ["export * from './FileSystem';\nexport * from './FileSystem.types';\n"], "mappings": "AAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}