{"ast": null, "code": "import AppbarComponent from \"./Appbar\";\nimport AppbarAction from \"./AppbarAction\";\nimport AppbarBackAction from \"./AppbarBackAction\";\nimport AppbarContent from \"./AppbarContent\";\nimport AppbarHeader from \"./AppbarHeader\";\nvar Appbar = Object.assign(AppbarComponent, {\n  Content: AppbarContent,\n  Action: AppbarAction,\n  BackAction: AppbarBackAction,\n  Header: AppbarHeader\n});\nexport default Appbar;", "map": {"version": 3, "names": ["AppbarComponent", "AppbarAction", "AppbarBackAction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Appbar", "Object", "assign", "Content", "Action", "BackAction", "Header"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Appbar/index.ts"], "sourcesContent": ["import AppbarComponent from './Appbar';\nimport AppbarAction from './AppbarAction';\nimport AppbarBackAction from './AppbarBackAction';\nimport AppbarContent from './AppbarContent';\nimport AppbarHeader from './AppbarHeader';\n\nconst Appbar = Object.assign(\n  // @component ./Appbar.tsx\n  AppbarComponent,\n  {\n    // @component ./AppbarContent.tsx\n    Content: AppbarContent,\n    // @component ./AppbarAction.tsx\n    Action: AppbarAction,\n    // @component ./AppbarBackAction.tsx\n    BackAction: AppbarBackAction,\n    // @component ./AppbarHeader.tsx\n    Header: AppbarHeader,\n  }\n);\n\nexport default Appbar;\n"], "mappings": "AAAA,OAAOA,eAAe;AACtB,OAAOC,YAAY;AACnB,OAAOC,gBAAgB;AACvB,OAAOC,aAAa;AACpB,OAAOC,YAAY;AAEnB,IAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAE1BP,eAAe,EACf;EAEEQ,OAAO,EAAEL,aAAa;EAEtBM,MAAM,EAAER,YAAY;EAEpBS,UAAU,EAAER,gBAAgB;EAE5BS,MAAM,EAAEP;AACV,CACF,CAAC;AAED,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}