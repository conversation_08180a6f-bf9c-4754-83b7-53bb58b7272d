{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport View from \"../../../exports/View\";\nimport StyleSheet from \"../../../exports/StyleSheet\";\nimport { VirtualizedListCellContextProvider } from \"./VirtualizedListContext.js\";\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\nvar CellRenderer = function (_React$Component) {\n  function CellRenderer() {\n    var _this;\n    _classCallCheck(this, CellRenderer);\n    _this = _callSuper(this, CellRenderer, arguments);\n    _this.state = {\n      separatorProps: {\n        highlighted: false,\n        leadingItem: _this.props.item\n      }\n    };\n    _this._separators = {\n      highlight: function highlight() {\n        var _this$props = _this.props,\n          cellKey = _this$props.cellKey,\n          prevCellKey = _this$props.prevCellKey;\n        _this.props.onUpdateSeparators([cellKey, prevCellKey], {\n          highlighted: true\n        });\n      },\n      unhighlight: function unhighlight() {\n        var _this$props2 = _this.props,\n          cellKey = _this$props2.cellKey,\n          prevCellKey = _this$props2.prevCellKey;\n        _this.props.onUpdateSeparators([cellKey, prevCellKey], {\n          highlighted: false\n        });\n      },\n      updateProps: function updateProps(select, newProps) {\n        var _this$props3 = _this.props,\n          cellKey = _this$props3.cellKey,\n          prevCellKey = _this$props3.prevCellKey;\n        _this.props.onUpdateSeparators([select === 'leading' ? prevCellKey : cellKey], newProps);\n      }\n    };\n    _this._onLayout = function (nativeEvent) {\n      _this.props.onCellLayout && _this.props.onCellLayout(nativeEvent, _this.props.cellKey, _this.props.index);\n    };\n    return _this;\n  }\n  _inherits(CellRenderer, _React$Component);\n  return _createClass(CellRenderer, [{\n    key: \"updateSeparatorProps\",\n    value: function updateSeparatorProps(newProps) {\n      this.setState(function (state) {\n        return {\n          separatorProps: _objectSpread(_objectSpread({}, state.separatorProps), newProps)\n        };\n      });\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.props.onUnmount(this.props.cellKey);\n    }\n  }, {\n    key: \"_renderElement\",\n    value: function _renderElement(renderItem, ListItemComponent, item, index) {\n      if (renderItem && ListItemComponent) {\n        console.warn('VirtualizedList: Both ListItemComponent and renderItem props are present. ListItemComponent will take' + ' precedence over renderItem.');\n      }\n      if (ListItemComponent) {\n        return React.createElement(ListItemComponent, {\n          item: item,\n          index: index,\n          separators: this._separators\n        });\n      }\n      if (renderItem) {\n        return renderItem({\n          item: item,\n          index: index,\n          separators: this._separators\n        });\n      }\n      invariant(false, 'VirtualizedList: Either ListItemComponent or renderItem props are required but none were found.');\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        CellRendererComponent = _this$props4.CellRendererComponent,\n        ItemSeparatorComponent = _this$props4.ItemSeparatorComponent,\n        ListItemComponent = _this$props4.ListItemComponent,\n        cellKey = _this$props4.cellKey,\n        horizontal = _this$props4.horizontal,\n        item = _this$props4.item,\n        index = _this$props4.index,\n        inversionStyle = _this$props4.inversionStyle,\n        onCellFocusCapture = _this$props4.onCellFocusCapture,\n        onCellLayout = _this$props4.onCellLayout,\n        renderItem = _this$props4.renderItem;\n      var element = this._renderElement(renderItem, ListItemComponent, item, index);\n      var itemSeparator = React.isValidElement(ItemSeparatorComponent) ? ItemSeparatorComponent : ItemSeparatorComponent && React.createElement(ItemSeparatorComponent, this.state.separatorProps);\n      var cellStyle = inversionStyle ? horizontal ? [styles.rowReverse, inversionStyle] : [styles.columnReverse, inversionStyle] : horizontal ? [styles.row, inversionStyle] : inversionStyle;\n      var result = !CellRendererComponent ? React.createElement(View, _extends({\n        style: cellStyle,\n        onFocusCapture: onCellFocusCapture\n      }, onCellLayout && {\n        onLayout: this._onLayout\n      }), element, itemSeparator) : React.createElement(CellRendererComponent, _extends({\n        cellKey: cellKey,\n        index: index,\n        item: item,\n        style: cellStyle,\n        onFocusCapture: onCellFocusCapture\n      }, onCellLayout && {\n        onLayout: this._onLayout\n      }), element, itemSeparator);\n      return React.createElement(VirtualizedListCellContextProvider, {\n        cellKey: this.props.cellKey\n      }, result);\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      return {\n        separatorProps: _objectSpread(_objectSpread({}, prevState.separatorProps), {}, {\n          leadingItem: props.item\n        })\n      };\n    }\n  }]);\n}(React.Component);\nexport { CellRenderer as default };\nvar styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row'\n  },\n  rowReverse: {\n    flexDirection: 'row-reverse'\n  },\n  columnReverse: {\n    flexDirection: 'column-reverse'\n  }\n});", "map": {"version": 3, "names": ["_extends", "_objectSpread", "View", "StyleSheet", "VirtualizedListCellContextProvider", "invariant", "React", "<PERSON><PERSON><PERSON><PERSON>", "_React$Component", "_this", "_classCallCheck", "_callSuper", "arguments", "state", "separatorProps", "highlighted", "leadingItem", "props", "item", "_separators", "highlight", "_this$props", "cellKey", "prevCell<PERSON>ey", "onUpdateSeparators", "unhighlight", "_this$props2", "updateProps", "select", "newProps", "_this$props3", "_onLayout", "nativeEvent", "onCellLayout", "index", "_inherits", "_createClass", "key", "value", "updateSeparatorProps", "setState", "componentWillUnmount", "onUnmount", "_renderElement", "renderItem", "ListItemComponent", "console", "warn", "createElement", "separators", "render", "_this$props4", "CellRendererComponent", "ItemSeparatorComponent", "horizontal", "inversionStyle", "onCellFocusCapture", "element", "itemSeparator", "isValidElement", "cellStyle", "styles", "rowReverse", "columnReverse", "row", "result", "style", "onFocusCapture", "onLayout", "getDerivedStateFromProps", "prevState", "Component", "default", "create", "flexDirection"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/VirtualizedListCellRenderer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport View from '../../../exports/View';\nimport StyleSheet from '../../../exports/StyleSheet';\nimport { VirtualizedListCellContextProvider } from './VirtualizedListContext.js';\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\nexport default class CellRenderer extends React.Component {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      separatorProps: {\n        highlighted: false,\n        leadingItem: this.props.item\n      }\n    };\n    this._separators = {\n      highlight: () => {\n        var _this$props = this.props,\n          cellKey = _this$props.cellKey,\n          prevCellKey = _this$props.prevCellKey;\n        this.props.onUpdateSeparators([cell<PERSON>ey, prevCellKey], {\n          highlighted: true\n        });\n      },\n      unhighlight: () => {\n        var _this$props2 = this.props,\n          cellKey = _this$props2.cellKey,\n          prevCellKey = _this$props2.prevCellKey;\n        this.props.onUpdateSeparators([cellKey, prevCellKey], {\n          highlighted: false\n        });\n      },\n      updateProps: (select, newProps) => {\n        var _this$props3 = this.props,\n          cellKey = _this$props3.cellKey,\n          prevCellKey = _this$props3.prevCellKey;\n        this.props.onUpdateSeparators([select === 'leading' ? prevCellKey : cellKey], newProps);\n      }\n    };\n    this._onLayout = nativeEvent => {\n      this.props.onCellLayout && this.props.onCellLayout(nativeEvent, this.props.cellKey, this.props.index);\n    };\n  }\n  static getDerivedStateFromProps(props, prevState) {\n    return {\n      separatorProps: _objectSpread(_objectSpread({}, prevState.separatorProps), {}, {\n        leadingItem: props.item\n      })\n    };\n  }\n\n  // TODO: consider factoring separator stuff out of VirtualizedList into FlatList since it's not\n  // reused by SectionList and we can keep VirtualizedList simpler.\n  // $FlowFixMe[missing-local-annot]\n\n  updateSeparatorProps(newProps) {\n    this.setState(state => ({\n      separatorProps: _objectSpread(_objectSpread({}, state.separatorProps), newProps)\n    }));\n  }\n  componentWillUnmount() {\n    this.props.onUnmount(this.props.cellKey);\n  }\n  _renderElement(renderItem, ListItemComponent, item, index) {\n    if (renderItem && ListItemComponent) {\n      console.warn('VirtualizedList: Both ListItemComponent and renderItem props are present. ListItemComponent will take' + ' precedence over renderItem.');\n    }\n    if (ListItemComponent) {\n      /* $FlowFixMe[not-a-component] (>=0.108.0 site=react_native_fb) This\n       * comment suppresses an error found when Flow v0.108 was deployed. To\n       * see the error, delete this comment and run Flow. */\n      /* $FlowFixMe[incompatible-type-arg] (>=0.108.0 site=react_native_fb)\n       * This comment suppresses an error found when Flow v0.108 was deployed.\n       * To see the error, delete this comment and run Flow. */\n      return /*#__PURE__*/React.createElement(ListItemComponent, {\n        item,\n        index,\n        separators: this._separators\n      });\n    }\n    if (renderItem) {\n      return renderItem({\n        item,\n        index,\n        separators: this._separators\n      });\n    }\n    invariant(false, 'VirtualizedList: Either ListItemComponent or renderItem props are required but none were found.');\n  }\n  render() {\n    var _this$props4 = this.props,\n      CellRendererComponent = _this$props4.CellRendererComponent,\n      ItemSeparatorComponent = _this$props4.ItemSeparatorComponent,\n      ListItemComponent = _this$props4.ListItemComponent,\n      cellKey = _this$props4.cellKey,\n      horizontal = _this$props4.horizontal,\n      item = _this$props4.item,\n      index = _this$props4.index,\n      inversionStyle = _this$props4.inversionStyle,\n      onCellFocusCapture = _this$props4.onCellFocusCapture,\n      onCellLayout = _this$props4.onCellLayout,\n      renderItem = _this$props4.renderItem;\n    var element = this._renderElement(renderItem, ListItemComponent, item, index);\n\n    // NOTE: that when this is a sticky header, `onLayout` will get automatically extracted and\n    // called explicitly by `ScrollViewStickyHeader`.\n    var itemSeparator = /*#__PURE__*/React.isValidElement(ItemSeparatorComponent) ?\n    // $FlowFixMe[incompatible-type]\n    ItemSeparatorComponent :\n    // $FlowFixMe[incompatible-type]\n    ItemSeparatorComponent && /*#__PURE__*/React.createElement(ItemSeparatorComponent, this.state.separatorProps);\n    var cellStyle = inversionStyle ? horizontal ? [styles.rowReverse, inversionStyle] : [styles.columnReverse, inversionStyle] : horizontal ? [styles.row, inversionStyle] : inversionStyle;\n    var result = !CellRendererComponent ? /*#__PURE__*/React.createElement(View, _extends({\n      style: cellStyle,\n      onFocusCapture: onCellFocusCapture\n    }, onCellLayout && {\n      onLayout: this._onLayout\n    }), element, itemSeparator) : /*#__PURE__*/React.createElement(CellRendererComponent, _extends({\n      cellKey: cellKey,\n      index: index,\n      item: item,\n      style: cellStyle,\n      onFocusCapture: onCellFocusCapture\n    }, onCellLayout && {\n      onLayout: this._onLayout\n    }), element, itemSeparator);\n    return /*#__PURE__*/React.createElement(VirtualizedListCellContextProvider, {\n      cellKey: this.props.cellKey\n    }, result);\n  }\n}\nvar styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row'\n  },\n  rowReverse: {\n    flexDirection: 'row-reverse'\n  },\n  columnReverse: {\n    flexDirection: 'column-reverse'\n  }\n});"], "mappings": ";;;;;;;AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,aAAa,MAAM,sCAAsC;AAWhE,OAAOC,IAAI;AACX,OAAOC,UAAU;AACjB,SAASC,kCAAkC;AAC3C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAC,IACVC,YAAY,aAAAC,gBAAA;EAC/B,SAAAD,aAAA,EAAc;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,YAAA;IACZE,KAAA,GAAAE,UAAA,OAAAJ,YAAA,EAASK,SAAS;IAClBH,KAAA,CAAKI,KAAK,GAAG;MACXC,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAEP,KAAA,CAAKQ,KAAK,CAACC;MAC1B;IACF,CAAC;IACDT,KAAA,CAAKU,WAAW,GAAG;MACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;QACf,IAAIC,WAAW,GAAGZ,KAAA,CAAKQ,KAAK;UAC1BK,OAAO,GAAGD,WAAW,CAACC,OAAO;UAC7BC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACvCd,KAAA,CAAKQ,KAAK,CAACO,kBAAkB,CAAC,CAACF,OAAO,EAAEC,WAAW,CAAC,EAAE;UACpDR,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC;MACDU,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAQ;QACjB,IAAIC,YAAY,GAAGjB,KAAA,CAAKQ,KAAK;UAC3BK,OAAO,GAAGI,YAAY,CAACJ,OAAO;UAC9BC,WAAW,GAAGG,YAAY,CAACH,WAAW;QACxCd,KAAA,CAAKQ,KAAK,CAACO,kBAAkB,CAAC,CAACF,OAAO,EAAEC,WAAW,CAAC,EAAE;UACpDR,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC;MACDY,WAAW,EAAE,SAAbA,WAAWA,CAAGC,MAAM,EAAEC,QAAQ,EAAK;QACjC,IAAIC,YAAY,GAAGrB,KAAA,CAAKQ,KAAK;UAC3BK,OAAO,GAAGQ,YAAY,CAACR,OAAO;UAC9BC,WAAW,GAAGO,YAAY,CAACP,WAAW;QACxCd,KAAA,CAAKQ,KAAK,CAACO,kBAAkB,CAAC,CAACI,MAAM,KAAK,SAAS,GAAGL,WAAW,GAAGD,OAAO,CAAC,EAAEO,QAAQ,CAAC;MACzF;IACF,CAAC;IACDpB,KAAA,CAAKsB,SAAS,GAAG,UAAAC,WAAW,EAAI;MAC9BvB,KAAA,CAAKQ,KAAK,CAACgB,YAAY,IAAIxB,KAAA,CAAKQ,KAAK,CAACgB,YAAY,CAACD,WAAW,EAAEvB,KAAA,CAAKQ,KAAK,CAACK,OAAO,EAAEb,KAAA,CAAKQ,KAAK,CAACiB,KAAK,CAAC;IACvG,CAAC;IAAC,OAAAzB,KAAA;EACJ;EAAC0B,SAAA,CAAA5B,YAAA,EAAAC,gBAAA;EAAA,OAAA4B,YAAA,CAAA7B,YAAA;IAAA8B,GAAA;IAAAC,KAAA,EAaD,SAAAC,oBAAoBA,CAACV,QAAQ,EAAE;MAC7B,IAAI,CAACW,QAAQ,CAAC,UAAA3B,KAAK;QAAA,OAAK;UACtBC,cAAc,EAAEb,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEY,KAAK,CAACC,cAAc,CAAC,EAAEe,QAAQ;QACjF,CAAC;MAAA,CAAC,CAAC;IACL;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EACD,SAAAG,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACxB,KAAK,CAACyB,SAAS,CAAC,IAAI,CAACzB,KAAK,CAACK,OAAO,CAAC;IAC1C;EAAC;IAAAe,GAAA;IAAAC,KAAA,EACD,SAAAK,cAAcA,CAACC,UAAU,EAAEC,iBAAiB,EAAE3B,IAAI,EAAEgB,KAAK,EAAE;MACzD,IAAIU,UAAU,IAAIC,iBAAiB,EAAE;QACnCC,OAAO,CAACC,IAAI,CAAC,uGAAuG,GAAG,8BAA8B,CAAC;MACxJ;MACA,IAAIF,iBAAiB,EAAE;QAOrB,OAAoBvC,KAAK,CAAC0C,aAAa,CAACH,iBAAiB,EAAE;UACzD3B,IAAI,EAAJA,IAAI;UACJgB,KAAK,EAALA,KAAK;UACLe,UAAU,EAAE,IAAI,CAAC9B;QACnB,CAAC,CAAC;MACJ;MACA,IAAIyB,UAAU,EAAE;QACd,OAAOA,UAAU,CAAC;UAChB1B,IAAI,EAAJA,IAAI;UACJgB,KAAK,EAALA,KAAK;UACLe,UAAU,EAAE,IAAI,CAAC9B;QACnB,CAAC,CAAC;MACJ;MACAd,SAAS,CAAC,KAAK,EAAE,iGAAiG,CAAC;IACrH;EAAC;IAAAgC,GAAA;IAAAC,KAAA,EACD,SAAAY,MAAMA,CAAA,EAAG;MACP,IAAIC,YAAY,GAAG,IAAI,CAAClC,KAAK;QAC3BmC,qBAAqB,GAAGD,YAAY,CAACC,qBAAqB;QAC1DC,sBAAsB,GAAGF,YAAY,CAACE,sBAAsB;QAC5DR,iBAAiB,GAAGM,YAAY,CAACN,iBAAiB;QAClDvB,OAAO,GAAG6B,YAAY,CAAC7B,OAAO;QAC9BgC,UAAU,GAAGH,YAAY,CAACG,UAAU;QACpCpC,IAAI,GAAGiC,YAAY,CAACjC,IAAI;QACxBgB,KAAK,GAAGiB,YAAY,CAACjB,KAAK;QAC1BqB,cAAc,GAAGJ,YAAY,CAACI,cAAc;QAC5CC,kBAAkB,GAAGL,YAAY,CAACK,kBAAkB;QACpDvB,YAAY,GAAGkB,YAAY,CAAClB,YAAY;QACxCW,UAAU,GAAGO,YAAY,CAACP,UAAU;MACtC,IAAIa,OAAO,GAAG,IAAI,CAACd,cAAc,CAACC,UAAU,EAAEC,iBAAiB,EAAE3B,IAAI,EAAEgB,KAAK,CAAC;MAI7E,IAAIwB,aAAa,GAAgBpD,KAAK,CAACqD,cAAc,CAACN,sBAAsB,CAAC,GAE7EA,sBAAsB,GAEtBA,sBAAsB,IAAiB/C,KAAK,CAAC0C,aAAa,CAACK,sBAAsB,EAAE,IAAI,CAACxC,KAAK,CAACC,cAAc,CAAC;MAC7G,IAAI8C,SAAS,GAAGL,cAAc,GAAGD,UAAU,GAAG,CAACO,MAAM,CAACC,UAAU,EAAEP,cAAc,CAAC,GAAG,CAACM,MAAM,CAACE,aAAa,EAAER,cAAc,CAAC,GAAGD,UAAU,GAAG,CAACO,MAAM,CAACG,GAAG,EAAET,cAAc,CAAC,GAAGA,cAAc;MACvL,IAAIU,MAAM,GAAG,CAACb,qBAAqB,GAAgB9C,KAAK,CAAC0C,aAAa,CAAC9C,IAAI,EAAEF,QAAQ,CAAC;QACpFkE,KAAK,EAAEN,SAAS;QAChBO,cAAc,EAAEX;MAClB,CAAC,EAAEvB,YAAY,IAAI;QACjBmC,QAAQ,EAAE,IAAI,CAACrC;MACjB,CAAC,CAAC,EAAE0B,OAAO,EAAEC,aAAa,CAAC,GAAgBpD,KAAK,CAAC0C,aAAa,CAACI,qBAAqB,EAAEpD,QAAQ,CAAC;QAC7FsB,OAAO,EAAEA,OAAO;QAChBY,KAAK,EAAEA,KAAK;QACZhB,IAAI,EAAEA,IAAI;QACVgD,KAAK,EAAEN,SAAS;QAChBO,cAAc,EAAEX;MAClB,CAAC,EAAEvB,YAAY,IAAI;QACjBmC,QAAQ,EAAE,IAAI,CAACrC;MACjB,CAAC,CAAC,EAAE0B,OAAO,EAAEC,aAAa,CAAC;MAC3B,OAAoBpD,KAAK,CAAC0C,aAAa,CAAC5C,kCAAkC,EAAE;QAC1EkB,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK;MACtB,CAAC,EAAE2C,MAAM,CAAC;IACZ;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAtFD,SAAO+B,wBAAwBA,CAACpD,KAAK,EAAEqD,SAAS,EAAE;MAChD,OAAO;QACLxD,cAAc,EAAEb,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqE,SAAS,CAACxD,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7EE,WAAW,EAAEC,KAAK,CAACC;QACrB,CAAC;MACH,CAAC;IACH;EAAC;AAAA,EA3CuCZ,KAAK,CAACiE,SAAS;AAAA,SAApChE,YAAY,IAAAiE,OAAA;AA6HjC,IAAIX,MAAM,GAAG1D,UAAU,CAACsE,MAAM,CAAC;EAC7BT,GAAG,EAAE;IACHU,aAAa,EAAE;EACjB,CAAC;EACDZ,UAAU,EAAE;IACVY,aAAa,EAAE;EACjB,CAAC;EACDX,aAAa,EAAE;IACbW,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}