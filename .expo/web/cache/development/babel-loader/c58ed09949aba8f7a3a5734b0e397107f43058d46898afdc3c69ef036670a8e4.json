{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Card, Text, Switch, Button, TextInput, Divider, List } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { getCurrentUser, updateUserSettings, getDatabaseStats, resetDatabase, backupDatabase } from \"../services/databaseService\";\nimport SyncManager from \"../components/SyncManager\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ProfileScreen = function ProfileScreen() {\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme,\n    isDarkMode = _useTheme.isDarkMode,\n    toggleTheme = _useTheme.toggleTheme;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    user = _useState2[0],\n    setUser = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    isEditing = _useState4[0],\n    setIsEditing = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    editedUser = _useState6[0],\n    setEditedUser = _useState6[1];\n  var _useState7 = useState(null),\n    _useState8 = _slicedToArray(_useState7, 2),\n    dbStats = _useState8[0],\n    setDbStats = _useState8[1];\n  var _useState9 = useState(true),\n    _useState0 = _slicedToArray(_useState9, 2),\n    isLoading = _useState0[0],\n    setIsLoading = _useState0[1];\n  var _useState1 = useState(false),\n    _useState10 = _slicedToArray(_useState1, 2),\n    showSync = _useState10[0],\n    setShowSync = _useState10[1];\n  useEffect(function () {\n    var loadData = function () {\n      var _ref = _asyncToGenerator(function* () {\n        try {\n          setIsLoading(true);\n          var userData = yield getCurrentUser();\n          setUser(userData);\n          setEditedUser(userData);\n          var stats = yield getDatabaseStats();\n          setDbStats(stats);\n        } catch (error) {\n          console.error('Error loading user data:', error);\n        } finally {\n          setIsLoading(false);\n        }\n      });\n      return function loadData() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    loadData();\n  }, []);\n  var handleSaveProfile = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      try {\n        if (!editedUser.name) {\n          Alert.alert('Error', 'Name is required');\n          return;\n        }\n        yield updateUserSettings(editedUser.id, {\n          name: editedUser.name,\n          email: editedUser.email,\n          preferredLanguage: editedUser.preferred_language,\n          measurementUnit: editedUser.measurement_unit\n        });\n        setUser(editedUser);\n        setIsEditing(false);\n        Alert.alert('Success', 'Profile updated successfully');\n      } catch (error) {\n        console.error('Error saving profile:', error);\n        Alert.alert('Error', 'Failed to update profile');\n      }\n    });\n    return function handleSaveProfile() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var handleCancelEdit = function handleCancelEdit() {\n    setEditedUser(user);\n    setIsEditing(false);\n  };\n  var handleChange = function handleChange(field, value) {\n    setEditedUser(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, _defineProperty({}, field, value));\n    });\n  };\n  var handleBackupDatabase = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      try {\n        var backupPath = yield backupDatabase();\n        Alert.alert('Success', `Database backed up to: ${backupPath}`);\n      } catch (error) {\n        console.error('Error backing up database:', error);\n        Alert.alert('Error', 'Failed to backup database');\n      }\n    });\n    return function handleBackupDatabase() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var handleResetDatabase = function handleResetDatabase() {\n    Alert.alert('Confirm Reset', 'Are you sure you want to reset the database? This will delete all data and cannot be undone.', [{\n      text: 'Cancel',\n      style: 'cancel'\n    }, {\n      text: 'Reset',\n      style: 'destructive',\n      onPress: function () {\n        var _onPress = _asyncToGenerator(function* () {\n          try {\n            yield resetDatabase();\n            Alert.alert('Success', 'Database reset successfully');\n            var userData = yield getCurrentUser();\n            setUser(userData);\n            setEditedUser(userData);\n            var stats = yield getDatabaseStats();\n            setDbStats(stats);\n          } catch (error) {\n            console.error('Error resetting database:', error);\n            Alert.alert('Error', 'Failed to reset database');\n          }\n        });\n        function onPress() {\n          return _onPress.apply(this, arguments);\n        }\n        return onPress;\n      }()\n    }]);\n  };\n  if (isLoading) {\n    return _jsx(View, {\n      style: [styles.container, {\n        backgroundColor: theme.colors.background\n      }],\n      children: _jsx(Text, {\n        children: \"Loading...\"\n      })\n    });\n  }\n  return _jsxs(ScrollView, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: [_jsxs(Card, {\n      style: styles.card,\n      children: [_jsx(Card.Title, {\n        title: \"User Profile\"\n      }), _jsx(Card.Content, {\n        children: isEditing ? _jsxs(View, {\n          children: [_jsx(TextInput, {\n            label: \"Name\",\n            value: editedUser.name,\n            onChangeText: function onChangeText(text) {\n              return handleChange('name', text);\n            },\n            style: styles.input,\n            mode: \"outlined\"\n          }), _jsx(TextInput, {\n            label: \"Email\",\n            value: editedUser.email,\n            onChangeText: function onChangeText(text) {\n              return handleChange('email', text);\n            },\n            style: styles.input,\n            mode: \"outlined\",\n            keyboardType: \"email-address\"\n          }), _jsxs(View, {\n            style: styles.buttonContainer,\n            children: [_jsx(Button, {\n              onPress: handleCancelEdit,\n              style: styles.button,\n              children: \"Cancel\"\n            }), _jsx(Button, {\n              mode: \"contained\",\n              onPress: handleSaveProfile,\n              style: styles.button,\n              children: \"Save\"\n            })]\n          })]\n        }) : _jsxs(View, {\n          children: [_jsx(List.Item, {\n            title: \"Name\",\n            description: user.name,\n            left: function left(props) {\n              return _jsx(List.Icon, _objectSpread(_objectSpread({}, props), {}, {\n                icon: \"account\"\n              }));\n            }\n          }), _jsx(List.Item, {\n            title: \"Email\",\n            description: user.email || 'Not set',\n            left: function left(props) {\n              return _jsx(List.Icon, _objectSpread(_objectSpread({}, props), {}, {\n                icon: \"email\"\n              }));\n            }\n          }), _jsx(Button, {\n            mode: \"outlined\",\n            onPress: function onPress() {\n              return setIsEditing(true);\n            },\n            style: styles.editButton,\n            children: \"Edit Profile\"\n          })]\n        })\n      })]\n    }), _jsxs(Card, {\n      style: styles.card,\n      children: [_jsx(Card.Title, {\n        title: \"Appearance\"\n      }), _jsx(Card.Content, {\n        children: _jsx(List.Item, {\n          title: \"Dark Mode\",\n          left: function left(props) {\n            return _jsx(List.Icon, _objectSpread(_objectSpread({}, props), {}, {\n              icon: isDarkMode ? \"weather-night\" : \"weather-sunny\"\n            }));\n          },\n          right: function right() {\n            return _jsx(Switch, {\n              value: isDarkMode,\n              onValueChange: toggleTheme\n            });\n          }\n        })\n      })]\n    }), _jsxs(Card, {\n      style: styles.card,\n      children: [_jsx(Card.Title, {\n        title: \"Database\"\n      }), _jsxs(Card.Content, {\n        children: [dbStats && _jsxs(View, {\n          style: styles.statsContainer,\n          children: [_jsxs(Text, {\n            style: styles.statsText,\n            children: [\"Version: \", dbStats.version]\n          }), _jsxs(Text, {\n            style: styles.statsText,\n            children: [\"Tables: \", dbStats.tables.length]\n          }), _jsxs(Text, {\n            style: styles.statsText,\n            children: [\"Total Records: \", dbStats.totalRecords]\n          })]\n        }), _jsxs(View, {\n          style: styles.buttonRow,\n          children: [_jsx(Button, {\n            mode: \"outlined\",\n            onPress: handleBackupDatabase,\n            style: styles.databaseButton,\n            children: \"Backup Database\"\n          }), _jsx(Button, {\n            mode: \"outlined\",\n            onPress: handleResetDatabase,\n            style: [styles.databaseButton, styles.dangerButton],\n            children: \"Reset Database\"\n          })]\n        }), _jsx(Button, {\n          mode: \"outlined\",\n          icon: \"database\",\n          onPress: function onPress() {\n            return navigation.navigate('FoodDatabase');\n          },\n          style: styles.syncButton,\n          children: \"Food Database\"\n        }), _jsx(Button, {\n          mode: \"outlined\",\n          onPress: function onPress() {\n            return setShowSync(!showSync);\n          },\n          style: styles.syncButton,\n          children: showSync ? 'Hide Sync Options' : 'Show Sync Options'\n        }), showSync && _jsx(SyncManager, {})]\n      })]\n    }), _jsxs(Card, {\n      style: styles.card,\n      children: [_jsx(Card.Title, {\n        title: \"About\"\n      }), _jsxs(Card.Content, {\n        children: [_jsx(Text, {\n          style: styles.aboutText,\n          children: \"Zn\\xFCniZ\\xE4hler v1.0.0\"\n        }), _jsx(Text, {\n          style: styles.aboutText,\n          children: \"A nutrition tracking app for monitoring your food intake\"\n        })]\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  card: {\n    margin: 16,\n    marginBottom: 8,\n    elevation: 2\n  },\n  input: {\n    marginBottom: 16\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 16\n  },\n  button: {\n    width: '48%'\n  },\n  editButton: {\n    marginTop: 16\n  },\n  statsContainer: {\n    marginBottom: 16\n  },\n  statsText: {\n    marginBottom: 4\n  },\n  buttonRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between'\n  },\n  databaseButton: {\n    width: '48%'\n  },\n  dangerButton: {\n    borderColor: '#B00020',\n    color: '#B00020'\n  },\n  syncButton: {\n    marginTop: 16\n  },\n  aboutText: {\n    marginBottom: 8,\n    textAlign: 'center'\n  }\n});\nexport default ProfileScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "ScrollView", "<PERSON><PERSON>", "Card", "Text", "Switch", "<PERSON><PERSON>", "TextInput", "Divider", "List", "useTheme", "getCurrentUser", "updateUserSettings", "getDatabaseStats", "resetDatabase", "backupDatabase", "SyncManager", "jsx", "_jsx", "jsxs", "_jsxs", "ProfileScreen", "_useTheme", "theme", "isDarkMode", "toggleTheme", "_useState", "_useState2", "_slicedToArray", "user", "setUser", "_useState3", "_useState4", "isEditing", "setIsEditing", "_useState5", "_useState6", "editedUser", "setEditedUser", "_useState7", "_useState8", "dbStats", "setDbStats", "_useState9", "_useState0", "isLoading", "setIsLoading", "_useState1", "_useState10", "showSync", "setShowSync", "loadData", "_ref", "_asyncToGenerator", "userData", "stats", "error", "console", "apply", "arguments", "handleSaveProfile", "_ref2", "name", "alert", "id", "email", "preferredLanguage", "preferred_language", "measurementUnit", "measurement_unit", "handleCancelEdit", "handleChange", "field", "value", "prev", "_objectSpread", "_defineProperty", "handleBackupDatabase", "_ref3", "<PERSON><PERSON><PERSON>", "handleResetDatabase", "text", "style", "onPress", "_onPress", "styles", "container", "backgroundColor", "colors", "background", "children", "card", "Title", "title", "Content", "label", "onChangeText", "input", "mode", "keyboardType", "buttonContainer", "button", "<PERSON><PERSON>", "description", "left", "props", "Icon", "icon", "edit<PERSON><PERSON><PERSON>", "right", "onValueChange", "stats<PERSON><PERSON><PERSON>", "statsText", "version", "tables", "length", "totalRecords", "buttonRow", "databaseButton", "dangerButton", "navigation", "navigate", "syncButton", "aboutText", "create", "flex", "margin", "marginBottom", "elevation", "flexDirection", "justifyContent", "marginTop", "width", "borderColor", "color", "textAlign"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/screens/ProfileScreen.js"], "sourcesContent": ["/**\n * Profile Screen for Znü<PERSON>Zähler\n * Displays user profile and settings\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, ScrollView, Alert } from 'react-native';\nimport { Card, Text, Switch, Button, TextInput, Divider, List } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { getCurrentUser, updateUserSettings, getDatabaseStats, resetDatabase, backupDatabase } from '../services/databaseService';\nimport SyncManager from '../components/SyncManager';\n\n/**\n * Profile Screen Component\n * @returns {JSX.Element} - Profile screen component\n */\nconst ProfileScreen = () => {\n  const { theme, isDarkMode, toggleTheme } = useTheme();\n  const [user, setUser] = useState(null);\n  const [isEditing, setIsEditing] = useState(false);\n  const [editedUser, setEditedUser] = useState(null);\n  const [dbStats, setDbStats] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showSync, setShowSync] = useState(false);\n\n  // Load user data\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n\n        // Get current user\n        const userData = await getCurrentUser();\n        setUser(userData);\n        setEditedUser(userData);\n\n        // Get database stats\n        const stats = await getDatabaseStats();\n        setDbStats(stats);\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Handle save profile\n  const handleSaveProfile = async () => {\n    try {\n      // Validate required fields\n      if (!editedUser.name) {\n        Alert.alert('Error', 'Name is required');\n        return;\n      }\n\n      // Update user settings\n      await updateUserSettings(editedUser.id, {\n        name: editedUser.name,\n        email: editedUser.email,\n        preferredLanguage: editedUser.preferred_language,\n        measurementUnit: editedUser.measurement_unit\n      });\n\n      // Update user state\n      setUser(editedUser);\n\n      // Exit edit mode\n      setIsEditing(false);\n\n      // Show success message\n      Alert.alert('Success', 'Profile updated successfully');\n    } catch (error) {\n      console.error('Error saving profile:', error);\n      Alert.alert('Error', 'Failed to update profile');\n    }\n  };\n\n  // Handle cancel edit\n  const handleCancelEdit = () => {\n    setEditedUser(user);\n    setIsEditing(false);\n  };\n\n  // Handle field change\n  const handleChange = (field, value) => {\n    setEditedUser(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Handle backup database\n  const handleBackupDatabase = async () => {\n    try {\n      const backupPath = await backupDatabase();\n      Alert.alert('Success', `Database backed up to: ${backupPath}`);\n    } catch (error) {\n      console.error('Error backing up database:', error);\n      Alert.alert('Error', 'Failed to backup database');\n    }\n  };\n\n  // Handle reset database\n  const handleResetDatabase = () => {\n    Alert.alert(\n      'Confirm Reset',\n      'Are you sure you want to reset the database? This will delete all data and cannot be undone.',\n      [\n        {\n          text: 'Cancel',\n          style: 'cancel'\n        },\n        {\n          text: 'Reset',\n          style: 'destructive',\n          onPress: async () => {\n            try {\n              await resetDatabase();\n              Alert.alert('Success', 'Database reset successfully');\n\n              // Reload page\n              const userData = await getCurrentUser();\n              setUser(userData);\n              setEditedUser(userData);\n\n              const stats = await getDatabaseStats();\n              setDbStats(stats);\n            } catch (error) {\n              console.error('Error resetting database:', error);\n              Alert.alert('Error', 'Failed to reset database');\n            }\n          }\n        }\n      ]\n    );\n  };\n\n  if (isLoading) {\n    return (\n      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n        <Text>Loading...</Text>\n      </View>\n    );\n  }\n\n  return (\n    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <Card style={styles.card}>\n        <Card.Title title=\"User Profile\" />\n        <Card.Content>\n          {isEditing ? (\n            // Edit mode\n            <View>\n              <TextInput\n                label=\"Name\"\n                value={editedUser.name}\n                onChangeText={(text) => handleChange('name', text)}\n                style={styles.input}\n                mode=\"outlined\"\n              />\n\n              <TextInput\n                label=\"Email\"\n                value={editedUser.email}\n                onChangeText={(text) => handleChange('email', text)}\n                style={styles.input}\n                mode=\"outlined\"\n                keyboardType=\"email-address\"\n              />\n\n              <View style={styles.buttonContainer}>\n                <Button onPress={handleCancelEdit} style={styles.button}>\n                  Cancel\n                </Button>\n                <Button mode=\"contained\" onPress={handleSaveProfile} style={styles.button}>\n                  Save\n                </Button>\n              </View>\n            </View>\n          ) : (\n            // View mode\n            <View>\n              <List.Item\n                title=\"Name\"\n                description={user.name}\n                left={props => <List.Icon {...props} icon=\"account\" />}\n              />\n\n              <List.Item\n                title=\"Email\"\n                description={user.email || 'Not set'}\n                left={props => <List.Icon {...props} icon=\"email\" />}\n              />\n\n              <Button\n                mode=\"outlined\"\n                onPress={() => setIsEditing(true)}\n                style={styles.editButton}\n              >\n                Edit Profile\n              </Button>\n            </View>\n          )}\n        </Card.Content>\n      </Card>\n\n      <Card style={styles.card}>\n        <Card.Title title=\"Appearance\" />\n        <Card.Content>\n          <List.Item\n            title=\"Dark Mode\"\n            left={props => <List.Icon {...props} icon={isDarkMode ? \"weather-night\" : \"weather-sunny\"} />}\n            right={() => (\n              <Switch\n                value={isDarkMode}\n                onValueChange={toggleTheme}\n              />\n            )}\n          />\n        </Card.Content>\n      </Card>\n\n      <Card style={styles.card}>\n        <Card.Title title=\"Database\" />\n        <Card.Content>\n          {dbStats && (\n            <View style={styles.statsContainer}>\n              <Text style={styles.statsText}>Version: {dbStats.version}</Text>\n              <Text style={styles.statsText}>Tables: {dbStats.tables.length}</Text>\n              <Text style={styles.statsText}>Total Records: {dbStats.totalRecords}</Text>\n            </View>\n          )}\n\n          <View style={styles.buttonRow}>\n            <Button\n              mode=\"outlined\"\n              onPress={handleBackupDatabase}\n              style={styles.databaseButton}\n            >\n              Backup Database\n            </Button>\n\n            <Button\n              mode=\"outlined\"\n              onPress={handleResetDatabase}\n              style={[styles.databaseButton, styles.dangerButton]}\n            >\n              Reset Database\n            </Button>\n          </View>\n\n          <Button\n            mode=\"outlined\"\n            icon=\"database\"\n            onPress={() => navigation.navigate('FoodDatabase')}\n            style={styles.syncButton}\n          >\n            Food Database\n          </Button>\n\n          <Button\n            mode=\"outlined\"\n            onPress={() => setShowSync(!showSync)}\n            style={styles.syncButton}\n          >\n            {showSync ? 'Hide Sync Options' : 'Show Sync Options'}\n          </Button>\n\n          {showSync && <SyncManager />}\n        </Card.Content>\n      </Card>\n\n      <Card style={styles.card}>\n        <Card.Title title=\"About\" />\n        <Card.Content>\n          <Text style={styles.aboutText}>\n            ZnüniZähler v1.0.0\n          </Text>\n          <Text style={styles.aboutText}>\n            A nutrition tracking app for monitoring your food intake\n          </Text>\n        </Card.Content>\n      </Card>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  card: {\n    margin: 16,\n    marginBottom: 8,\n    elevation: 2,\n  },\n  input: {\n    marginBottom: 16,\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 16,\n  },\n  button: {\n    width: '48%',\n  },\n  editButton: {\n    marginTop: 16,\n  },\n  statsContainer: {\n    marginBottom: 16,\n  },\n  statsText: {\n    marginBottom: 4,\n  },\n  buttonRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n  databaseButton: {\n    width: '48%',\n  },\n  dangerButton: {\n    borderColor: '#B00020',\n    color: '#B00020',\n  },\n  syncButton: {\n    marginTop: 16,\n  },\n  aboutText: {\n    marginBottom: 8,\n    textAlign: 'center',\n  },\n});\n\nexport default ProfileScreen;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAEnD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,IAAI,QAAQ,oBAAoB;AACzF,SAASC,QAAQ;AACjB,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc;AAC5F,OAAOC,WAAW;AAAkC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAMpD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,IAAAC,SAAA,GAA2CZ,QAAQ,CAAC,CAAC;IAA7Ca,KAAK,GAAAD,SAAA,CAALC,KAAK;IAAEC,UAAU,GAAAF,SAAA,CAAVE,UAAU;IAAEC,WAAW,GAAAH,SAAA,CAAXG,WAAW;EACtC,IAAAC,SAAA,GAAwB7B,QAAQ,CAAC,IAAI,CAAC;IAAA8B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA/BG,IAAI,GAAAF,UAAA;IAAEG,OAAO,GAAAH,UAAA;EACpB,IAAAI,UAAA,GAAkClC,QAAQ,CAAC,KAAK,CAAC;IAAAmC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAoCtC,QAAQ,CAAC,IAAI,CAAC;IAAAuC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA3CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAA8B1C,QAAQ,CAAC,IAAI,CAAC;IAAA2C,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAkC9C,QAAQ,CAAC,IAAI,CAAC;IAAA+C,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAAzCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAgClD,QAAQ,CAAC,KAAK,CAAC;IAAAmD,WAAA,GAAApB,cAAA,CAAAmB,UAAA;IAAxCE,QAAQ,GAAAD,WAAA;IAAEE,WAAW,GAAAF,WAAA;EAG5BlD,SAAS,CAAC,YAAM;IACd,IAAMqD,QAAQ;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;QAC3B,IAAI;UACFP,YAAY,CAAC,IAAI,CAAC;UAGlB,IAAMQ,QAAQ,SAAS3C,cAAc,CAAC,CAAC;UACvCmB,OAAO,CAACwB,QAAQ,CAAC;UACjBhB,aAAa,CAACgB,QAAQ,CAAC;UAGvB,IAAMC,KAAK,SAAS1C,gBAAgB,CAAC,CAAC;UACtC6B,UAAU,CAACa,KAAK,CAAC;QACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD,CAAC,SAAS;UACRV,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;MAAA,gBAjBKK,QAAQA,CAAA;QAAA,OAAAC,IAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;IAAA,GAiBb;IAEDR,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMS,iBAAiB;IAAA,IAAAC,KAAA,GAAAR,iBAAA,CAAG,aAAY;MACpC,IAAI;QAEF,IAAI,CAAChB,UAAU,CAACyB,IAAI,EAAE;UACpB5D,KAAK,CAAC6D,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC;UACxC;QACF;QAGA,MAAMnD,kBAAkB,CAACyB,UAAU,CAAC2B,EAAE,EAAE;UACtCF,IAAI,EAAEzB,UAAU,CAACyB,IAAI;UACrBG,KAAK,EAAE5B,UAAU,CAAC4B,KAAK;UACvBC,iBAAiB,EAAE7B,UAAU,CAAC8B,kBAAkB;UAChDC,eAAe,EAAE/B,UAAU,CAACgC;QAC9B,CAAC,CAAC;QAGFvC,OAAO,CAACO,UAAU,CAAC;QAGnBH,YAAY,CAAC,KAAK,CAAC;QAGnBhC,KAAK,CAAC6D,KAAK,CAAC,SAAS,EAAE,8BAA8B,CAAC;MACxD,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CtD,KAAK,CAAC6D,KAAK,CAAC,OAAO,EAAE,0BAA0B,CAAC;MAClD;IACF,CAAC;IAAA,gBA5BKH,iBAAiBA,CAAA;MAAA,OAAAC,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GA4BtB;EAGD,IAAMW,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7BhC,aAAa,CAACT,IAAI,CAAC;IACnBK,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAGD,IAAMqC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAEC,KAAK,EAAK;IACrCnC,aAAa,CAAC,UAAAoC,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KACbD,IAAI,OAAAE,eAAA,KACNJ,KAAK,EAAGC,KAAK;IAAA,CACd,CAAC;EACL,CAAC;EAGD,IAAMI,oBAAoB;IAAA,IAAAC,KAAA,GAAAzB,iBAAA,CAAG,aAAY;MACvC,IAAI;QACF,IAAM0B,UAAU,SAAShE,cAAc,CAAC,CAAC;QACzCb,KAAK,CAAC6D,KAAK,CAAC,SAAS,EAAE,0BAA0BgB,UAAU,EAAE,CAAC;MAChE,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDtD,KAAK,CAAC6D,KAAK,CAAC,OAAO,EAAE,2BAA2B,CAAC;MACnD;IACF,CAAC;IAAA,gBARKc,oBAAoBA,CAAA;MAAA,OAAAC,KAAA,CAAApB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAQzB;EAGD,IAAMqB,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC9E,KAAK,CAAC6D,KAAK,CACT,eAAe,EACf,8FAA8F,EAC9F,CACE;MACEkB,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;IACT,CAAC,EACD;MACED,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO;QAAA,IAAAC,QAAA,GAAA/B,iBAAA,CAAE,aAAY;UACnB,IAAI;YACF,MAAMvC,aAAa,CAAC,CAAC;YACrBZ,KAAK,CAAC6D,KAAK,CAAC,SAAS,EAAE,6BAA6B,CAAC;YAGrD,IAAMT,QAAQ,SAAS3C,cAAc,CAAC,CAAC;YACvCmB,OAAO,CAACwB,QAAQ,CAAC;YACjBhB,aAAa,CAACgB,QAAQ,CAAC;YAEvB,IAAMC,KAAK,SAAS1C,gBAAgB,CAAC,CAAC;YACtC6B,UAAU,CAACa,KAAK,CAAC;UACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;YACjDtD,KAAK,CAAC6D,KAAK,CAAC,OAAO,EAAE,0BAA0B,CAAC;UAClD;QACF,CAAC;QAAA,SAhBDoB,OAAOA,CAAA;UAAA,OAAAC,QAAA,CAAA1B,KAAA,OAAAC,SAAA;QAAA;QAAA,OAAPwB,OAAO;MAAA;IAiBT,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAItC,SAAS,EAAE;IACb,OACE3B,IAAA,CAAClB,IAAI;MAACkF,KAAK,EAAE,CAACG,MAAM,CAACC,SAAS,EAAE;QAAEC,eAAe,EAAEhE,KAAK,CAACiE,MAAM,CAACC;MAAW,CAAC,CAAE;MAAAC,QAAA,EAC5ExE,IAAA,CAACd,IAAI;QAAAsF,QAAA,EAAC;MAAU,CAAM;IAAC,CACnB,CAAC;EAEX;EAEA,OACEtE,KAAA,CAACnB,UAAU;IAACiF,KAAK,EAAE,CAACG,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAEhE,KAAK,CAACiE,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,GAClFtE,KAAA,CAACjB,IAAI;MAAC+E,KAAK,EAAEG,MAAM,CAACM,IAAK;MAAAD,QAAA,GACvBxE,IAAA,CAACf,IAAI,CAACyF,KAAK;QAACC,KAAK,EAAC;MAAc,CAAE,CAAC,EACnC3E,IAAA,CAACf,IAAI,CAAC2F,OAAO;QAAAJ,QAAA,EACVzD,SAAS,GAERb,KAAA,CAACpB,IAAI;UAAA0F,QAAA,GACHxE,IAAA,CAACX,SAAS;YACRwF,KAAK,EAAC,MAAM;YACZtB,KAAK,EAAEpC,UAAU,CAACyB,IAAK;YACvBkC,YAAY,EAAE,SAAdA,YAAYA,CAAGf,IAAI;cAAA,OAAKV,YAAY,CAAC,MAAM,EAAEU,IAAI,CAAC;YAAA,CAAC;YACnDC,KAAK,EAAEG,MAAM,CAACY,KAAM;YACpBC,IAAI,EAAC;UAAU,CAChB,CAAC,EAEFhF,IAAA,CAACX,SAAS;YACRwF,KAAK,EAAC,OAAO;YACbtB,KAAK,EAAEpC,UAAU,CAAC4B,KAAM;YACxB+B,YAAY,EAAE,SAAdA,YAAYA,CAAGf,IAAI;cAAA,OAAKV,YAAY,CAAC,OAAO,EAAEU,IAAI,CAAC;YAAA,CAAC;YACpDC,KAAK,EAAEG,MAAM,CAACY,KAAM;YACpBC,IAAI,EAAC,UAAU;YACfC,YAAY,EAAC;UAAe,CAC7B,CAAC,EAEF/E,KAAA,CAACpB,IAAI;YAACkF,KAAK,EAAEG,MAAM,CAACe,eAAgB;YAAAV,QAAA,GAClCxE,IAAA,CAACZ,MAAM;cAAC6E,OAAO,EAAEb,gBAAiB;cAACY,KAAK,EAAEG,MAAM,CAACgB,MAAO;cAAAX,QAAA,EAAC;YAEzD,CAAQ,CAAC,EACTxE,IAAA,CAACZ,MAAM;cAAC4F,IAAI,EAAC,WAAW;cAACf,OAAO,EAAEvB,iBAAkB;cAACsB,KAAK,EAAEG,MAAM,CAACgB,MAAO;cAAAX,QAAA,EAAC;YAE3E,CAAQ,CAAC;UAAA,CACL,CAAC;QAAA,CACH,CAAC,GAGPtE,KAAA,CAACpB,IAAI;UAAA0F,QAAA,GACHxE,IAAA,CAACT,IAAI,CAAC6F,IAAI;YACRT,KAAK,EAAC,MAAM;YACZU,WAAW,EAAE1E,IAAI,CAACiC,IAAK;YACvB0C,IAAI,EAAE,SAANA,IAAIA,CAAEC,KAAK;cAAA,OAAIvF,IAAA,CAACT,IAAI,CAACiG,IAAI,EAAA/B,aAAA,CAAAA,aAAA,KAAK8B,KAAK;gBAAEE,IAAI,EAAC;cAAS,EAAE,CAAC;YAAA;UAAC,CACxD,CAAC,EAEFzF,IAAA,CAACT,IAAI,CAAC6F,IAAI;YACRT,KAAK,EAAC,OAAO;YACbU,WAAW,EAAE1E,IAAI,CAACoC,KAAK,IAAI,SAAU;YACrCuC,IAAI,EAAE,SAANA,IAAIA,CAAEC,KAAK;cAAA,OAAIvF,IAAA,CAACT,IAAI,CAACiG,IAAI,EAAA/B,aAAA,CAAAA,aAAA,KAAK8B,KAAK;gBAAEE,IAAI,EAAC;cAAO,EAAE,CAAC;YAAA;UAAC,CACtD,CAAC,EAEFzF,IAAA,CAACZ,MAAM;YACL4F,IAAI,EAAC,UAAU;YACff,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQjD,YAAY,CAAC,IAAI,CAAC;YAAA,CAAC;YAClCgD,KAAK,EAAEG,MAAM,CAACuB,UAAW;YAAAlB,QAAA,EAC1B;UAED,CAAQ,CAAC;QAAA,CACL;MACP,CACW,CAAC;IAAA,CACX,CAAC,EAEPtE,KAAA,CAACjB,IAAI;MAAC+E,KAAK,EAAEG,MAAM,CAACM,IAAK;MAAAD,QAAA,GACvBxE,IAAA,CAACf,IAAI,CAACyF,KAAK;QAACC,KAAK,EAAC;MAAY,CAAE,CAAC,EACjC3E,IAAA,CAACf,IAAI,CAAC2F,OAAO;QAAAJ,QAAA,EACXxE,IAAA,CAACT,IAAI,CAAC6F,IAAI;UACRT,KAAK,EAAC,WAAW;UACjBW,IAAI,EAAE,SAANA,IAAIA,CAAEC,KAAK;YAAA,OAAIvF,IAAA,CAACT,IAAI,CAACiG,IAAI,EAAA/B,aAAA,CAAAA,aAAA,KAAK8B,KAAK;cAAEE,IAAI,EAAEnF,UAAU,GAAG,eAAe,GAAG;YAAgB,EAAE,CAAC;UAAA,CAAC;UAC9FqF,KAAK,EAAE,SAAPA,KAAKA,CAAA;YAAA,OACH3F,IAAA,CAACb,MAAM;cACLoE,KAAK,EAAEjD,UAAW;cAClBsF,aAAa,EAAErF;YAAY,CAC5B,CAAC;UAAA;QACF,CACH;MAAC,CACU,CAAC;IAAA,CACX,CAAC,EAEPL,KAAA,CAACjB,IAAI;MAAC+E,KAAK,EAAEG,MAAM,CAACM,IAAK;MAAAD,QAAA,GACvBxE,IAAA,CAACf,IAAI,CAACyF,KAAK;QAACC,KAAK,EAAC;MAAU,CAAE,CAAC,EAC/BzE,KAAA,CAACjB,IAAI,CAAC2F,OAAO;QAAAJ,QAAA,GACVjD,OAAO,IACNrB,KAAA,CAACpB,IAAI;UAACkF,KAAK,EAAEG,MAAM,CAAC0B,cAAe;UAAArB,QAAA,GACjCtE,KAAA,CAAChB,IAAI;YAAC8E,KAAK,EAAEG,MAAM,CAAC2B,SAAU;YAAAtB,QAAA,GAAC,WAAS,EAACjD,OAAO,CAACwE,OAAO;UAAA,CAAO,CAAC,EAChE7F,KAAA,CAAChB,IAAI;YAAC8E,KAAK,EAAEG,MAAM,CAAC2B,SAAU;YAAAtB,QAAA,GAAC,UAAQ,EAACjD,OAAO,CAACyE,MAAM,CAACC,MAAM;UAAA,CAAO,CAAC,EACrE/F,KAAA,CAAChB,IAAI;YAAC8E,KAAK,EAAEG,MAAM,CAAC2B,SAAU;YAAAtB,QAAA,GAAC,iBAAe,EAACjD,OAAO,CAAC2E,YAAY;UAAA,CAAO,CAAC;QAAA,CACvE,CACP,EAEDhG,KAAA,CAACpB,IAAI;UAACkF,KAAK,EAAEG,MAAM,CAACgC,SAAU;UAAA3B,QAAA,GAC5BxE,IAAA,CAACZ,MAAM;YACL4F,IAAI,EAAC,UAAU;YACff,OAAO,EAAEN,oBAAqB;YAC9BK,KAAK,EAAEG,MAAM,CAACiC,cAAe;YAAA5B,QAAA,EAC9B;UAED,CAAQ,CAAC,EAETxE,IAAA,CAACZ,MAAM;YACL4F,IAAI,EAAC,UAAU;YACff,OAAO,EAAEH,mBAAoB;YAC7BE,KAAK,EAAE,CAACG,MAAM,CAACiC,cAAc,EAAEjC,MAAM,CAACkC,YAAY,CAAE;YAAA7B,QAAA,EACrD;UAED,CAAQ,CAAC;QAAA,CACL,CAAC,EAEPxE,IAAA,CAACZ,MAAM;UACL4F,IAAI,EAAC,UAAU;UACfS,IAAI,EAAC,UAAU;UACfxB,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQqC,UAAU,CAACC,QAAQ,CAAC,cAAc,CAAC;UAAA,CAAC;UACnDvC,KAAK,EAAEG,MAAM,CAACqC,UAAW;UAAAhC,QAAA,EAC1B;QAED,CAAQ,CAAC,EAETxE,IAAA,CAACZ,MAAM;UACL4F,IAAI,EAAC,UAAU;UACff,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQjC,WAAW,CAAC,CAACD,QAAQ,CAAC;UAAA,CAAC;UACtCiC,KAAK,EAAEG,MAAM,CAACqC,UAAW;UAAAhC,QAAA,EAExBzC,QAAQ,GAAG,mBAAmB,GAAG;QAAmB,CAC/C,CAAC,EAERA,QAAQ,IAAI/B,IAAA,CAACF,WAAW,IAAE,CAAC;MAAA,CAChB,CAAC;IAAA,CACX,CAAC,EAEPI,KAAA,CAACjB,IAAI;MAAC+E,KAAK,EAAEG,MAAM,CAACM,IAAK;MAAAD,QAAA,GACvBxE,IAAA,CAACf,IAAI,CAACyF,KAAK;QAACC,KAAK,EAAC;MAAO,CAAE,CAAC,EAC5BzE,KAAA,CAACjB,IAAI,CAAC2F,OAAO;QAAAJ,QAAA,GACXxE,IAAA,CAACd,IAAI;UAAC8E,KAAK,EAAEG,MAAM,CAACsC,SAAU;UAAAjC,QAAA,EAAC;QAE/B,CAAM,CAAC,EACPxE,IAAA,CAACd,IAAI;UAAC8E,KAAK,EAAEG,MAAM,CAACsC,SAAU;UAAAjC,QAAA,EAAC;QAE/B,CAAM,CAAC;MAAA,CACK,CAAC;IAAA,CACX,CAAC;EAAA,CACG,CAAC;AAEjB,CAAC;AAED,IAAML,MAAM,GAAGtF,UAAU,CAAC6H,MAAM,CAAC;EAC/BtC,SAAS,EAAE;IACTuC,IAAI,EAAE;EACR,CAAC;EACDlC,IAAI,EAAE;IACJmC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACD/B,KAAK,EAAE;IACL8B,YAAY,EAAE;EAChB,CAAC;EACD3B,eAAe,EAAE;IACf6B,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,SAAS,EAAE;EACb,CAAC;EACD9B,MAAM,EAAE;IACN+B,KAAK,EAAE;EACT,CAAC;EACDxB,UAAU,EAAE;IACVuB,SAAS,EAAE;EACb,CAAC;EACDpB,cAAc,EAAE;IACdgB,YAAY,EAAE;EAChB,CAAC;EACDf,SAAS,EAAE;IACTe,YAAY,EAAE;EAChB,CAAC;EACDV,SAAS,EAAE;IACTY,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDZ,cAAc,EAAE;IACdc,KAAK,EAAE;EACT,CAAC;EACDb,YAAY,EAAE;IACZc,WAAW,EAAE,SAAS;IACtBC,KAAK,EAAE;EACT,CAAC;EACDZ,UAAU,EAAE;IACVS,SAAS,EAAE;EACb,CAAC;EACDR,SAAS,EAAE;IACTI,YAAY,EAAE,CAAC;IACfQ,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAelH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}