{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"../StyleSheet\";\nimport createElement from \"../createElement\";\nvar ANIMATION_DURATION = 300;\nfunction getAnimationStyle(animationType, visible) {\n  if (animationType === 'slide') {\n    return visible ? animatedSlideInStyles : animatedSlideOutStyles;\n  }\n  if (animationType === 'fade') {\n    return visible ? animatedFadeInStyles : animatedFadeOutStyles;\n  }\n  return visible ? styles.container : styles.hidden;\n}\nfunction ModalAnimation(props) {\n  var animationType = props.animationType,\n    children = props.children,\n    onDismiss = props.onDismiss,\n    onShow = props.onShow,\n    visible = props.visible;\n  var _React$useState = React.useState(false),\n    isRendering = _React$useState[0],\n    setIsRendering = _React$useState[1];\n  var wasVisible = React.useRef(false);\n  var wasRendering = React.useRef(false);\n  var isAnimated = animationType && animationType !== 'none';\n  var animationEndCallback = React.useCallback(function (e) {\n    if (e && e.currentTarget !== e.target) {\n      return;\n    }\n    if (visible) {\n      if (onShow) {\n        onShow();\n      }\n    } else {\n      setIsRendering(false);\n    }\n  }, [onShow, visible]);\n  React.useEffect(function () {\n    if (wasRendering.current && !isRendering && onDismiss) {\n      onDismiss();\n    }\n    wasRendering.current = isRendering;\n  }, [isRendering, onDismiss]);\n  React.useEffect(function () {\n    if (visible) {\n      setIsRendering(true);\n    }\n    if (visible !== wasVisible.current && !isAnimated) {\n      animationEndCallback();\n    }\n    wasVisible.current = visible;\n  }, [isAnimated, visible, animationEndCallback]);\n  return isRendering || visible ? createElement('div', {\n    style: isRendering ? getAnimationStyle(animationType, visible) : styles.hidden,\n    onAnimationEnd: animationEndCallback,\n    children: children\n  }) : null;\n}\nvar styles = StyleSheet.create({\n  container: {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    zIndex: 9999\n  },\n  animatedIn: {\n    animationDuration: ANIMATION_DURATION + \"ms\",\n    animationTimingFunction: 'ease-in'\n  },\n  animatedOut: {\n    pointerEvents: 'none',\n    animationDuration: ANIMATION_DURATION + \"ms\",\n    animationTimingFunction: 'ease-out'\n  },\n  fadeIn: {\n    opacity: 1,\n    animationKeyframes: {\n      '0%': {\n        opacity: 0\n      },\n      '100%': {\n        opacity: 1\n      }\n    }\n  },\n  fadeOut: {\n    opacity: 0,\n    animationKeyframes: {\n      '0%': {\n        opacity: 1\n      },\n      '100%': {\n        opacity: 0\n      }\n    }\n  },\n  slideIn: {\n    transform: 'translateY(0%)',\n    animationKeyframes: {\n      '0%': {\n        transform: 'translateY(100%)'\n      },\n      '100%': {\n        transform: 'translateY(0%)'\n      }\n    }\n  },\n  slideOut: {\n    transform: 'translateY(100%)',\n    animationKeyframes: {\n      '0%': {\n        transform: 'translateY(0%)'\n      },\n      '100%': {\n        transform: 'translateY(100%)'\n      }\n    }\n  },\n  hidden: {\n    opacity: 0\n  }\n});\nvar animatedSlideInStyles = [styles.container, styles.animatedIn, styles.slideIn];\nvar animatedSlideOutStyles = [styles.container, styles.animatedOut, styles.slideOut];\nvar animatedFadeInStyles = [styles.container, styles.animatedIn, styles.fadeIn];\nvar animatedFadeOutStyles = [styles.container, styles.animatedOut, styles.fadeOut];\nexport default ModalAnimation;", "map": {"version": 3, "names": ["React", "StyleSheet", "createElement", "ANIMATION_DURATION", "getAnimationStyle", "animationType", "visible", "animatedSlideInStyles", "animatedSlideOutStyles", "animatedFadeInStyles", "animatedFadeOutStyles", "styles", "container", "hidden", "ModalAnimation", "props", "children", "on<PERSON><PERSON><PERSON>", "onShow", "_React$useState", "useState", "isRendering", "setIsRendering", "wasVisible", "useRef", "wasRendering", "isAnimated", "animationEndCallback", "useCallback", "e", "currentTarget", "target", "useEffect", "current", "style", "onAnimationEnd", "create", "position", "top", "right", "bottom", "left", "zIndex", "animatedIn", "animationDuration", "animationTimingFunction", "animatedOut", "pointerEvents", "fadeIn", "opacity", "animationKeyframes", "fadeOut", "slideIn", "transform", "slideOut"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Modal/ModalAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport StyleSheet from '../StyleSheet';\nimport createElement from '../createElement';\nvar ANIMATION_DURATION = 300;\nfunction getAnimationStyle(animationType, visible) {\n  if (animationType === 'slide') {\n    return visible ? animatedSlideInStyles : animatedSlideOutStyles;\n  }\n  if (animationType === 'fade') {\n    return visible ? animatedFadeInStyles : animatedFadeOutStyles;\n  }\n  return visible ? styles.container : styles.hidden;\n}\nfunction ModalAnimation(props) {\n  var animationType = props.animationType,\n    children = props.children,\n    onDismiss = props.onDismiss,\n    onShow = props.onShow,\n    visible = props.visible;\n  var _React$useState = React.useState(false),\n    isRendering = _React$useState[0],\n    setIsRendering = _React$useState[1];\n  var wasVisible = React.useRef(false);\n  var wasRendering = React.useRef(false);\n  var isAnimated = animationType && animationType !== 'none';\n  var animationEndCallback = React.useCallback(e => {\n    if (e && e.currentTarget !== e.target) {\n      // If the event was generated for something NOT this element we\n      // should ignore it as it's not relevant to us\n      return;\n    }\n    if (visible) {\n      if (onShow) {\n        onShow();\n      }\n    } else {\n      setIsRendering(false);\n    }\n  }, [onShow, visible]);\n  React.useEffect(() => {\n    if (wasRendering.current && !isRendering && onDismiss) {\n      onDismiss();\n    }\n    wasRendering.current = isRendering;\n  }, [isRendering, onDismiss]);\n  React.useEffect(() => {\n    if (visible) {\n      setIsRendering(true);\n    }\n    if (visible !== wasVisible.current && !isAnimated) {\n      // Manually call `animationEndCallback` if no animation is used\n      animationEndCallback();\n    }\n    wasVisible.current = visible;\n  }, [isAnimated, visible, animationEndCallback]);\n  return isRendering || visible ? createElement('div', {\n    style: isRendering ? getAnimationStyle(animationType, visible) : styles.hidden,\n    onAnimationEnd: animationEndCallback,\n    children\n  }) : null;\n}\nvar styles = StyleSheet.create({\n  container: {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    zIndex: 9999\n  },\n  animatedIn: {\n    animationDuration: ANIMATION_DURATION + \"ms\",\n    animationTimingFunction: 'ease-in'\n  },\n  animatedOut: {\n    pointerEvents: 'none',\n    animationDuration: ANIMATION_DURATION + \"ms\",\n    animationTimingFunction: 'ease-out'\n  },\n  fadeIn: {\n    opacity: 1,\n    animationKeyframes: {\n      '0%': {\n        opacity: 0\n      },\n      '100%': {\n        opacity: 1\n      }\n    }\n  },\n  fadeOut: {\n    opacity: 0,\n    animationKeyframes: {\n      '0%': {\n        opacity: 1\n      },\n      '100%': {\n        opacity: 0\n      }\n    }\n  },\n  slideIn: {\n    transform: 'translateY(0%)',\n    animationKeyframes: {\n      '0%': {\n        transform: 'translateY(100%)'\n      },\n      '100%': {\n        transform: 'translateY(0%)'\n      }\n    }\n  },\n  slideOut: {\n    transform: 'translateY(100%)',\n    animationKeyframes: {\n      '0%': {\n        transform: 'translateY(0%)'\n      },\n      '100%': {\n        transform: 'translateY(100%)'\n      }\n    }\n  },\n  hidden: {\n    opacity: 0\n  }\n});\nvar animatedSlideInStyles = [styles.container, styles.animatedIn, styles.slideIn];\nvar animatedSlideOutStyles = [styles.container, styles.animatedOut, styles.slideOut];\nvar animatedFadeInStyles = [styles.container, styles.animatedIn, styles.fadeIn];\nvar animatedFadeOutStyles = [styles.container, styles.animatedOut, styles.fadeOut];\nexport default ModalAnimation;"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,aAAa;AACpB,IAAIC,kBAAkB,GAAG,GAAG;AAC5B,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EACjD,IAAID,aAAa,KAAK,OAAO,EAAE;IAC7B,OAAOC,OAAO,GAAGC,qBAAqB,GAAGC,sBAAsB;EACjE;EACA,IAAIH,aAAa,KAAK,MAAM,EAAE;IAC5B,OAAOC,OAAO,GAAGG,oBAAoB,GAAGC,qBAAqB;EAC/D;EACA,OAAOJ,OAAO,GAAGK,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACE,MAAM;AACnD;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIV,aAAa,GAAGU,KAAK,CAACV,aAAa;IACrCW,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IACzBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBZ,OAAO,GAAGS,KAAK,CAACT,OAAO;EACzB,IAAIa,eAAe,GAAGnB,KAAK,CAACoB,QAAQ,CAAC,KAAK,CAAC;IACzCC,WAAW,GAAGF,eAAe,CAAC,CAAC,CAAC;IAChCG,cAAc,GAAGH,eAAe,CAAC,CAAC,CAAC;EACrC,IAAII,UAAU,GAAGvB,KAAK,CAACwB,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIC,YAAY,GAAGzB,KAAK,CAACwB,MAAM,CAAC,KAAK,CAAC;EACtC,IAAIE,UAAU,GAAGrB,aAAa,IAAIA,aAAa,KAAK,MAAM;EAC1D,IAAIsB,oBAAoB,GAAG3B,KAAK,CAAC4B,WAAW,CAAC,UAAAC,CAAC,EAAI;IAChD,IAAIA,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAKD,CAAC,CAACE,MAAM,EAAE;MAGrC;IACF;IACA,IAAIzB,OAAO,EAAE;MACX,IAAIY,MAAM,EAAE;QACVA,MAAM,CAAC,CAAC;MACV;IACF,CAAC,MAAM;MACLI,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACJ,MAAM,EAAEZ,OAAO,CAAC,CAAC;EACrBN,KAAK,CAACgC,SAAS,CAAC,YAAM;IACpB,IAAIP,YAAY,CAACQ,OAAO,IAAI,CAACZ,WAAW,IAAIJ,SAAS,EAAE;MACrDA,SAAS,CAAC,CAAC;IACb;IACAQ,YAAY,CAACQ,OAAO,GAAGZ,WAAW;EACpC,CAAC,EAAE,CAACA,WAAW,EAAEJ,SAAS,CAAC,CAAC;EAC5BjB,KAAK,CAACgC,SAAS,CAAC,YAAM;IACpB,IAAI1B,OAAO,EAAE;MACXgB,cAAc,CAAC,IAAI,CAAC;IACtB;IACA,IAAIhB,OAAO,KAAKiB,UAAU,CAACU,OAAO,IAAI,CAACP,UAAU,EAAE;MAEjDC,oBAAoB,CAAC,CAAC;IACxB;IACAJ,UAAU,CAACU,OAAO,GAAG3B,OAAO;EAC9B,CAAC,EAAE,CAACoB,UAAU,EAAEpB,OAAO,EAAEqB,oBAAoB,CAAC,CAAC;EAC/C,OAAON,WAAW,IAAIf,OAAO,GAAGJ,aAAa,CAAC,KAAK,EAAE;IACnDgC,KAAK,EAAEb,WAAW,GAAGjB,iBAAiB,CAACC,aAAa,EAAEC,OAAO,CAAC,GAAGK,MAAM,CAACE,MAAM;IAC9EsB,cAAc,EAAER,oBAAoB;IACpCX,QAAQ,EAARA;EACF,CAAC,CAAC,GAAG,IAAI;AACX;AACA,IAAIL,MAAM,GAAGV,UAAU,CAACmC,MAAM,CAAC;EAC7BxB,SAAS,EAAE;IACTyB,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE;IACVC,iBAAiB,EAAEzC,kBAAkB,GAAG,IAAI;IAC5C0C,uBAAuB,EAAE;EAC3B,CAAC;EACDC,WAAW,EAAE;IACXC,aAAa,EAAE,MAAM;IACrBH,iBAAiB,EAAEzC,kBAAkB,GAAG,IAAI;IAC5C0C,uBAAuB,EAAE;EAC3B,CAAC;EACDG,MAAM,EAAE;IACNC,OAAO,EAAE,CAAC;IACVC,kBAAkB,EAAE;MAClB,IAAI,EAAE;QACJD,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNA,OAAO,EAAE;MACX;IACF;EACF,CAAC;EACDE,OAAO,EAAE;IACPF,OAAO,EAAE,CAAC;IACVC,kBAAkB,EAAE;MAClB,IAAI,EAAE;QACJD,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNA,OAAO,EAAE;MACX;IACF;EACF,CAAC;EACDG,OAAO,EAAE;IACPC,SAAS,EAAE,gBAAgB;IAC3BH,kBAAkB,EAAE;MAClB,IAAI,EAAE;QACJG,SAAS,EAAE;MACb,CAAC;MACD,MAAM,EAAE;QACNA,SAAS,EAAE;MACb;IACF;EACF,CAAC;EACDC,QAAQ,EAAE;IACRD,SAAS,EAAE,kBAAkB;IAC7BH,kBAAkB,EAAE;MAClB,IAAI,EAAE;QACJG,SAAS,EAAE;MACb,CAAC;MACD,MAAM,EAAE;QACNA,SAAS,EAAE;MACb;IACF;EACF,CAAC;EACDxC,MAAM,EAAE;IACNoC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,IAAI1C,qBAAqB,GAAG,CAACI,MAAM,CAACC,SAAS,EAAED,MAAM,CAACgC,UAAU,EAAEhC,MAAM,CAACyC,OAAO,CAAC;AACjF,IAAI5C,sBAAsB,GAAG,CAACG,MAAM,CAACC,SAAS,EAAED,MAAM,CAACmC,WAAW,EAAEnC,MAAM,CAAC2C,QAAQ,CAAC;AACpF,IAAI7C,oBAAoB,GAAG,CAACE,MAAM,CAACC,SAAS,EAAED,MAAM,CAACgC,UAAU,EAAEhC,MAAM,CAACqC,MAAM,CAAC;AAC/E,IAAItC,qBAAqB,GAAG,CAACC,MAAM,CAACC,SAAS,EAAED,MAAM,CAACmC,WAAW,EAAEnC,MAAM,CAACwC,OAAO,CAAC;AAClF,eAAerC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}