{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar ListImage = function ListImage(_ref) {\n  var style = _ref.style,\n    source = _ref.source,\n    _ref$variant = _ref.variant,\n    variant = _ref$variant === void 0 ? 'image' : _ref$variant,\n    themeOverrides = _ref.theme;\n  var theme = useInternalTheme(themeOverrides);\n  var getStyles = function getStyles() {\n    if (variant === 'video') {\n      if (!theme.isV3) {\n        return [style, styles.video];\n      }\n      return [style, styles.videoV3];\n    }\n    return [style, styles.image];\n  };\n  return React.createElement(Image, {\n    style: getStyles(),\n    source: source,\n    accessibilityIgnoresInvertColors: true,\n    testID: \"list-image\"\n  });\n};\nvar styles = StyleSheet.create({\n  image: {\n    width: 56,\n    height: 56\n  },\n  video: {\n    width: 100,\n    height: 64,\n    marginLeft: 0\n  },\n  videoV3: {\n    width: 114,\n    height: 64,\n    marginLeft: 0\n  }\n});\nListImage.displayName = 'List.Image';\nexport default ListImage;", "map": {"version": 3, "names": ["React", "StyleSheet", "Image", "useInternalTheme", "ListImage", "_ref", "style", "source", "_ref$variant", "variant", "themeOverrides", "theme", "getStyles", "isV3", "styles", "video", "videoV3", "image", "createElement", "accessibilityIgnoresInvertColors", "testID", "create", "width", "height", "marginLeft", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/List/ListImage.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleSheet,\n  StyleProp,\n  Image,\n  ImageSourcePropType,\n  ImageStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\n\nexport type Props = {\n  source: ImageSourcePropType;\n  variant?: 'image' | 'video';\n  style?: StyleProp<ImageStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to show image in a list item.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { List, MD3Colors } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <>\n *     <List.Image variant=\"image\" source={{uri: 'https://www.someurl.com/apple'}} />\n *     <List.Image variant=\"video\" source={require('../../some-apple.png')} />\n *   </>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst ListImage = ({\n  style,\n  source,\n  variant = 'image',\n  theme: themeOverrides,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const getStyles = () => {\n    if (variant === 'video') {\n      if (!theme.isV3) {\n        return [style, styles.video];\n      }\n\n      return [style, styles.videoV3];\n    }\n\n    return [style, styles.image];\n  };\n\n  return (\n    <Image\n      style={getStyles()}\n      source={source}\n      accessibilityIgnoresInvertColors\n      testID=\"list-image\"\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  image: {\n    width: 56,\n    height: 56,\n  },\n  video: {\n    width: 100,\n    height: 64,\n    marginLeft: 0,\n  },\n  videoV3: {\n    width: 114,\n    height: 64,\n    marginLeft: 0,\n  },\n});\n\nListImage.displayName = 'List.Image';\n\nexport default ListImage;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAS9B,SAASC,gBAAgB;AA+BzB,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAGC,IAAA,EAKL;EAAA,IAJXC,KAAK,GAICD,IAAA,CAJNC,KAAK;IACLC,MAAM,GAGAF,IAAA,CAHNE,MAAM;IAAAC,YAAA,GAGAH,IAAA,CAFNI,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,OAAO,GAAAA,YAAA;IACVE,cAAA,GACDL,IAAA,CADNM,KAAK;EAEL,IAAMA,KAAK,GAAGR,gBAAgB,CAACO,cAAc,CAAC;EAC9C,IAAME,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtB,IAAIH,OAAO,KAAK,OAAO,EAAE;MACvB,IAAI,CAACE,KAAK,CAACE,IAAI,EAAE;QACf,OAAO,CAACP,KAAK,EAAEQ,MAAM,CAACC,KAAK,CAAC;MAC9B;MAEA,OAAO,CAACT,KAAK,EAAEQ,MAAM,CAACE,OAAO,CAAC;IAChC;IAEA,OAAO,CAACV,KAAK,EAAEQ,MAAM,CAACG,KAAK,CAAC;EAC9B,CAAC;EAED,OACEjB,KAAA,CAAAkB,aAAA,CAAChB,KAAK;IACJI,KAAK,EAAEM,SAAS,CAAC,CAAE;IACnBL,MAAM,EAAEA,MAAO;IACfY,gCAAgC;IAChCC,MAAM,EAAC;EAAY,CACpB,CAAC;AAEN,CAAC;AAED,IAAMN,MAAM,GAAGb,UAAU,CAACoB,MAAM,CAAC;EAC/BJ,KAAK,EAAE;IACLK,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC;EACDR,KAAK,EAAE;IACLO,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE;EACd,CAAC;EACDR,OAAO,EAAE;IACPM,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEFpB,SAAS,CAACqB,WAAW,GAAG,YAAY;AAEpC,eAAerB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}