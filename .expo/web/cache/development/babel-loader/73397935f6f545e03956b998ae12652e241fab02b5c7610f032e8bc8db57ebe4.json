{"ast": null, "code": "import color from 'color';\nexport function getTextColor(_ref) {\n  var _theme$colors;\n  var theme = _ref.theme,\n    disabled = _ref.disabled;\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  return color((_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.text).alpha(theme.dark ? 0.7 : 0.54).rgb().string();\n}\nexport function getIconColor(_ref2) {\n  var theme = _ref2.theme,\n    isTextInputFocused = _ref2.isTextInputFocused,\n    disabled = _ref2.disabled,\n    customColor = _ref2.customColor;\n  if (typeof customColor === 'function') {\n    return customColor(isTextInputFocused);\n  }\n  if (customColor) {\n    return customColor;\n  }\n  if (!theme.isV3) {\n    return theme.colors.text;\n  }\n  if (disabled) {\n    return theme.colors.onSurfaceDisabled;\n  }\n  return theme.colors.onSurfaceVariant;\n}", "map": {"version": 3, "names": ["color", "getTextColor", "_ref", "_theme$colors", "theme", "disabled", "isV3", "colors", "onSurfaceDisabled", "onSurfaceVariant", "text", "alpha", "dark", "rgb", "string", "getIconColor", "_ref2", "isTextInputFocused", "customColor"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/TextInput/Adornment/utils.ts"], "sourcesContent": ["import color from 'color';\n\nimport type { InternalTheme } from '../../../types';\n\ntype BaseProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n};\n\nexport function getTextColor({ theme, disabled }: BaseProps) {\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  return color(theme.colors?.text)\n    .alpha(theme.dark ? 0.7 : 0.54)\n    .rgb()\n    .string();\n}\n\nexport function getIconColor({\n  theme,\n  isTextInputFocused,\n  disabled,\n  customColor,\n}: BaseProps & {\n  isTextInputFocused: boolean;\n  customColor?: ((isTextInputFocused: boolean) => string | undefined) | string;\n}) {\n  if (typeof customColor === 'function') {\n    return customColor(isTextInputFocused);\n  }\n  if (customColor) {\n    return customColor;\n  }\n\n  if (!theme.isV3) {\n    return theme.colors.text;\n  }\n\n  if (disabled) {\n    return theme.colors.onSurfaceDisabled;\n  }\n\n  return theme.colors.onSurfaceVariant;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AASzB,OAAO,SAASC,YAAYA,CAAAC,IAAA,EAAiC;EAAA,IAAAC,aAAA;EAAA,IAA9BC,KAAK,GAAuBF,IAAA,CAA5BE,KAAK;IAAEC,QAAA,GAAqBH,IAAA,CAArBG,QAAA;EACpC,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,IAAID,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACG,MAAM,CAACC,iBAAiB;IACvC;IACA,OAAOJ,KAAK,CAACG,MAAM,CAACE,gBAAgB;EACtC;EACA,OAAOT,KAAK,EAAAG,aAAA,GAACC,KAAK,CAACG,MAAM,cAAAJ,aAAA,uBAAZA,aAAA,CAAcO,IAAI,CAAC,CAC7BC,KAAK,CAACP,KAAK,CAACQ,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAC9BC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb;AAEA,OAAO,SAASC,YAAYA,CAAAC,KAAA,EAQzB;EAAA,IAPDZ,KAAK,GAONY,KAAA,CAPCZ,KAAK;IACLa,kBAAkB,GAMnBD,KAAA,CANCC,kBAAkB;IAClBZ,QAAQ,GAKTW,KAAA,CALCX,QAAQ;IACRa,WAAA,GAIDF,KAAA,CAJCE,WAAA;EAKA,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IACrC,OAAOA,WAAW,CAACD,kBAAkB,CAAC;EACxC;EACA,IAAIC,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EAEA,IAAI,CAACd,KAAK,CAACE,IAAI,EAAE;IACf,OAAOF,KAAK,CAACG,MAAM,CAACG,IAAI;EAC1B;EAEA,IAAIL,QAAQ,EAAE;IACZ,OAAOD,KAAK,CAACG,MAAM,CAACC,iBAAiB;EACvC;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,gBAAgB;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}