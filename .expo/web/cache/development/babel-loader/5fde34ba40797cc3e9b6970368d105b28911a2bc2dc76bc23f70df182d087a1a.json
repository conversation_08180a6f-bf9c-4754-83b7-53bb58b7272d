{"ast": null, "code": "import DeviceEventEmitter from \"react-native-web/dist/exports/DeviceEventEmitter\";\nimport { EventEmitter } from \"./EventEmitter\";\nimport NativeModulesProxy from \"./NativeModulesProxy\";\nimport { requireNativeViewManager } from \"./NativeViewManagerAdapter\";\nimport Platform from \"./Platform\";\nimport SyntheticPlatformEmitter from \"./SyntheticPlatformEmitter\";\nimport { CodedError } from \"./errors/CodedError\";\nimport { UnavailabilityError } from \"./errors/UnavailabilityError\";\nimport \"./sweet/setUpErrorManager.fx\";\nexport { default as deprecate } from \"./deprecate\";\nexport { DeviceEventEmitter, EventEmitter, NativeModulesProxy, Platform, SyntheticPlatformEmitter, requireNativeViewManager, CodedError, UnavailabilityError };\nexport * from \"./requireNativeModule\";\nexport * from \"./TypedArrays.types\";\nexport var RCTDeviceEventEmitter = DeviceEventEmitter;\nexport * from \"./PermissionsInterface\";\nexport * from \"./PermissionsHook\";", "map": {"version": 3, "names": ["EventEmitter", "NativeModulesProxy", "requireNativeViewManager", "Platform", "SyntheticPlatformEmitter", "CodedError", "UnavailabilityError", "default", "deprecate", "DeviceEventEmitter", "RCTDeviceEventEmitter"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-modules-core/src/index.ts"], "sourcesContent": ["import { DeviceEventEmitter } from 'react-native';\n\nimport { EventEmitter, Subscription } from './EventEmitter';\nimport NativeModulesProxy from './NativeModulesProxy';\nimport { ProxyNativeModule } from './NativeModulesProxy.types';\nimport { requireNativeViewManager } from './NativeViewManagerAdapter';\nimport Platform from './Platform';\nimport SyntheticPlatformEmitter from './SyntheticPlatformEmitter';\nimport { CodedError } from './errors/CodedError';\nimport { UnavailabilityError } from './errors/UnavailabilityError';\n\nimport './sweet/setUpErrorManager.fx';\n\nexport { default as deprecate } from './deprecate';\n\nexport {\n  DeviceEventEmitter,\n  EventEmitter,\n  NativeModulesProxy,\n  ProxyNativeModule,\n  Platform,\n  Subscription,\n  SyntheticPlatformEmitter,\n  requireNativeViewManager,\n  // Errors\n  CodedError,\n  UnavailabilityError,\n};\n\nexport * from './requireNativeModule';\nexport * from './TypedArrays.types';\n\n/**\n * @deprecated renamed to `DeviceEventEmitter`\n */\nexport const RCTDeviceEventEmitter = DeviceEventEmitter;\n\nexport * from './PermissionsInterface';\nexport * from './PermissionsHook';\n"], "mappings": ";AAEA,SAASA,YAAY;AACrB,OAAOC,kBAAkB;AAEzB,SAASC,wBAAwB;AACjC,OAAOC,QAAQ;AACf,OAAOC,wBAAwB;AAC/B,SAASC,UAAU;AACnB,SAASC,mBAAmB;AAE5B;AAEA,SAASC,OAAO,IAAIC,SAAS;AAE7B,SACEC,kBAAkB,EAClBT,YAAY,EACZC,kBAAkB,EAElBE,QAAQ,EAERC,wBAAwB,EACxBF,wBAAwB,EAExBG,UAAU,EACVC,mBAAmB;AAGrB;AACA;AAKA,OAAO,IAAMI,qBAAqB,GAAGD,kBAAkB;AAEvD;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}