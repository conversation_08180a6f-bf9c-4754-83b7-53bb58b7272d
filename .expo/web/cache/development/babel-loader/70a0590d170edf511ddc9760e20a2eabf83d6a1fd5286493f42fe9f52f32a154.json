{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"style\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport DataTableCell from \"./DataTableCell\";\nimport DataTableHeader from \"./DataTableHeader\";\nimport DataTablePagination from \"./DataTablePagination\";\nimport DataTableRow from \"./DataTableRow\";\nimport DataTableTitle from \"./DataTableTitle\";\nvar DataTable = function DataTable(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return React.createElement(View, _extends({}, rest, {\n    style: [styles.container, style]\n  }), children);\n};\nDataTable.Header = DataTableHeader;\nDataTable.Title = DataTableTitle;\nDataTable.Row = DataTableRow;\nDataTable.Cell = DataTableCell;\nDataTable.Pagination = DataTablePagination;\nvar styles = StyleSheet.create({\n  container: {\n    width: '100%'\n  }\n});\nexport default DataTable;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "DataTableCell", "DataTableHeader", "DataTablePagination", "DataTableRow", "DataTableTitle", "DataTable", "_ref", "children", "style", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_extends", "styles", "container", "Header", "Title", "Row", "Cell", "Pagination", "create", "width"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/DataTable/DataTable.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleSheet, StyleProp, View, ViewStyle } from 'react-native';\n\nimport DataTableCell from './DataTableCell';\nimport DataTableHeader, {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  DataTableHeader as _DataTableHeader,\n} from './DataTableHeader';\nimport DataTablePagination, {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  DataTablePagination as _DataTablePagination,\n} from './DataTablePagination';\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport DataTableRow, { DataTableRow as _DataTableRow } from './DataTableRow';\nimport DataTableTitle, {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  DataTableTitle as _DataTableTitle,\n} from './DataTableTitle';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Content of the `DataTable`.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n};\n\n/**\n * Data tables allow displaying sets of data.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { DataTable } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [page, setPage] = React.useState<number>(0);\n *   const [numberOfItemsPerPageList] = React.useState([2, 3, 4]);\n *   const [itemsPerPage, onItemsPerPageChange] = React.useState(\n *     numberOfItemsPerPageList[0]\n *   );\n *\n *   const [items] = React.useState([\n *    {\n *      key: 1,\n *      name: 'Cupcake',\n *      calories: 356,\n *      fat: 16,\n *    },\n *    {\n *      key: 2,\n *      name: 'Eclair',\n *      calories: 262,\n *      fat: 16,\n *    },\n *    {\n *      key: 3,\n *      name: 'Frozen yogurt',\n *      calories: 159,\n *      fat: 6,\n *    },\n *    {\n *      key: 4,\n *      name: 'Gingerbread',\n *      calories: 305,\n *      fat: 3.7,\n *    },\n *   ]);\n *\n *   const from = page * itemsPerPage;\n *   const to = Math.min((page + 1) * itemsPerPage, items.length);\n *\n *   React.useEffect(() => {\n *     setPage(0);\n *   }, [itemsPerPage]);\n *\n *   return (\n *     <DataTable>\n *       <DataTable.Header>\n *         <DataTable.Title>Dessert</DataTable.Title>\n *         <DataTable.Title numeric>Calories</DataTable.Title>\n *         <DataTable.Title numeric>Fat</DataTable.Title>\n *       </DataTable.Header>\n *\n *       {items.slice(from, to).map((item) => (\n *         <DataTable.Row key={item.key}>\n *           <DataTable.Cell>{item.name}</DataTable.Cell>\n *           <DataTable.Cell numeric>{item.calories}</DataTable.Cell>\n *           <DataTable.Cell numeric>{item.fat}</DataTable.Cell>\n *         </DataTable.Row>\n *       ))}\n *\n *       <DataTable.Pagination\n *         page={page}\n *         numberOfPages={Math.ceil(items.length / itemsPerPage)}\n *         onPageChange={(page) => setPage(page)}\n *         label={`${from + 1}-${to} of ${items.length}`}\n *         numberOfItemsPerPageList={numberOfItemsPerPageList}\n *         numberOfItemsPerPage={itemsPerPage}\n *         onItemsPerPageChange={onItemsPerPageChange}\n *         showFastPaginationControls\n *         selectPageDropdownLabel={'Rows per page'}\n *       />\n *     </DataTable>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst DataTable = ({ children, style, ...rest }: Props) => (\n  <View {...rest} style={[styles.container, style]}>\n    {children}\n  </View>\n);\n\n// @component ./DataTableHeader.tsx\nDataTable.Header = DataTableHeader;\n\n// @component ./DataTableTitle.tsx\nDataTable.Title = DataTableTitle;\n\n// @component ./DataTableRow.tsx\nDataTable.Row = DataTableRow;\n\n// @component ./DataTableCell.tsx\nDataTable.Cell = DataTableCell;\n\n// @component ./DataTablePagination.tsx\nDataTable.Pagination = DataTablePagination;\n\nconst styles = StyleSheet.create({\n  container: {\n    width: '100%',\n  },\n});\n\nexport default DataTable;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,aAAa;AACpB,OAAOC,eAAA;AAIP,OAAOC,mBAAA;AAKP,OAAOC,YAAY;AACnB,OAAOC,cAAA;AAgGP,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAGC,IAAA;EAAA,IAAGC,QAAQ,GAAyBD,IAAA,CAAjCC,QAAQ;IAAEC,KAAK,GAAkBF,IAAA,CAAvBE,KAAK;IAAKC,IAAA,GAAAC,wBAAA,CAAaJ,IAAA,EAAAK,SAAA;EAAA,OACpDd,KAAA,CAAAe,aAAA,CAACb,IAAI,EAAAc,QAAA,KAAKJ,IAAI;IAAED,KAAK,EAAE,CAACM,MAAM,CAACC,SAAS,EAAEP,KAAK;EAAE,IAC9CD,QACG,CAAC;AAAA,CACR;AAGDF,SAAS,CAACW,MAAM,GAAGf,eAAe;AAGlCI,SAAS,CAACY,KAAK,GAAGb,cAAc;AAGhCC,SAAS,CAACa,GAAG,GAAGf,YAAY;AAG5BE,SAAS,CAACc,IAAI,GAAGnB,aAAa;AAG9BK,SAAS,CAACe,UAAU,GAAGlB,mBAAmB;AAE1C,IAAMY,MAAM,GAAGhB,UAAU,CAACuB,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAejB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}