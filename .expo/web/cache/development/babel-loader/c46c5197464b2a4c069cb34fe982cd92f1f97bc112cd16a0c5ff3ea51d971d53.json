{"ast": null, "code": "var v1 = require(\"./v1\");\nvar v4 = require(\"./v4\");\nvar uuid = v4;\nuuid.v1 = v1;\nuuid.v4 = v4;\nmodule.exports = uuid;", "map": {"version": 3, "names": ["v1", "require", "v4", "uuid", "module", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-constants/node_modules/uuid/index.js"], "sourcesContent": ["var v1 = require('./v1');\nvar v4 = require('./v4');\n\nvar uuid = v4;\nuuid.v1 = v1;\nuuid.v4 = v4;\n\nmodule.exports = uuid;\n"], "mappings": "AAAA,IAAIA,EAAE,GAAGC,OAAO,OAAO,CAAC;AACxB,IAAIC,EAAE,GAAGD,OAAO,OAAO,CAAC;AAExB,IAAIE,IAAI,GAAGD,EAAE;AACbC,IAAI,CAACH,EAAE,GAAGA,EAAE;AACZG,IAAI,CAACD,EAAE,GAAGA,EAAE;AAEZE,MAAM,CAACC,OAAO,GAAGF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}