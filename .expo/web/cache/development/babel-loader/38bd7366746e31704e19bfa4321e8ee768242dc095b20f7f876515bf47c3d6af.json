{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nimport { useState } from 'react';\nimport useNavigation from \"./useNavigation\";\nexport default function useIsFocused() {\n  var navigation = useNavigation();\n  var _useState = useState(navigation.isFocused),\n    _useState2 = _slicedToArray(_useState, 2),\n    isFocused = _useState2[0],\n    setIsFocused = _useState2[1];\n  var valueToReturn = navigation.isFocused();\n  if (isFocused !== valueToReturn) {\n    setIsFocused(valueToReturn);\n  }\n  React.useEffect(function () {\n    var unsubscribeFocus = navigation.addListener('focus', function () {\n      return setIsFocused(true);\n    });\n    var unsubscribeBlur = navigation.addListener('blur', function () {\n      return setIsFocused(false);\n    });\n    return function () {\n      unsubscribeFocus();\n      unsubscribeBlur();\n    };\n  }, [navigation]);\n  React.useDebugValue(valueToReturn);\n  return valueToReturn;\n}", "map": {"version": 3, "names": ["React", "useState", "useNavigation", "useIsFocused", "navigation", "_useState", "isFocused", "_useState2", "_slicedToArray", "setIsFocused", "valueToReturn", "useEffect", "unsubscribeFocus", "addListener", "unsubscribeBlur", "useDebugValue"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/useIsFocused.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useState } from 'react';\n\nimport useNavigation from './useNavigation';\n\n/**\n * Hook to get the current focus state of the screen. Returns a `true` if screen is focused, otherwise `false`.\n * This can be used if a component needs to render something based on the focus state.\n */\nexport default function useIsFocused(): boolean {\n  const navigation = useNavigation();\n  const [isFocused, setIsFocused] = useState(navigation.isFocused);\n\n  const valueToReturn = navigation.isFocused();\n\n  if (isFocused !== valueToReturn) {\n    // If the value has changed since the last render, we need to update it.\n    // This could happen if we missed an update from the event listeners during re-render.\n    // React will process this update immediately, so the old subscription value won't be committed.\n    // It is still nice to avoid returning a mismatched value though, so let's override the return value.\n    // This is the same logic as in https://github.com/facebook/react/tree/master/packages/use-subscription\n    setIsFocused(valueToReturn);\n  }\n\n  React.useEffect(() => {\n    const unsubscribeFocus = navigation.addListener('focus', () =>\n      setIsFocused(true)\n    );\n\n    const unsubscribeBlur = navigation.addListener('blur', () =>\n      setIsFocused(false)\n    );\n\n    return () => {\n      unsubscribeFocus();\n      unsubscribeBlur();\n    };\n  }, [navigation]);\n\n  React.useDebugValue(valueToReturn);\n\n  return valueToReturn;\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAEhC,OAAOC,aAAa;AAMpB,eAAe,SAASC,YAAYA,CAAA,EAAY;EAC9C,IAAMC,UAAU,GAAGF,aAAa,EAAE;EAClC,IAAAG,SAAA,GAAkCJ,QAAQ,CAACG,UAAU,CAACE,SAAS,CAAC;IAAAC,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAzDC,SAAS,GAAAC,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAE9B,IAAMG,aAAa,GAAGN,UAAU,CAACE,SAAS,EAAE;EAE5C,IAAIA,SAAS,KAAKI,aAAa,EAAE;IAM/BD,YAAY,CAACC,aAAa,CAAC;EAC7B;EAEAV,KAAK,CAACW,SAAS,CAAC,YAAM;IACpB,IAAMC,gBAAgB,GAAGR,UAAU,CAACS,WAAW,CAAC,OAAO,EAAE;MAAA,OACvDJ,YAAY,CAAC,IAAI,CAAC;IAAA,EACnB;IAED,IAAMK,eAAe,GAAGV,UAAU,CAACS,WAAW,CAAC,MAAM,EAAE;MAAA,OACrDJ,YAAY,CAAC,KAAK,CAAC;IAAA,EACpB;IAED,OAAO,YAAM;MACXG,gBAAgB,EAAE;MAClBE,eAAe,EAAE;IACnB,CAAC;EACH,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;EAEhBJ,KAAK,CAACe,aAAa,CAACL,aAAa,CAAC;EAElC,OAAOA,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}