{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"label\", \"background\", \"accessibilityLabel\", \"accessibilityState\", \"color\", \"rippleColor\", \"disabled\", \"onPress\", \"onLongPress\", \"delayLongPress\", \"theme\", \"style\", \"visible\", \"uppercase\", \"testID\", \"animateFrom\", \"extended\", \"iconMode\", \"variant\", \"labelMaxFontSizeMultiplier\"],\n  _excluded2 = [\"backgroundColor\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Easing from \"react-native-web/dist/exports/Easing\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { getCombinedStyles, getFABColors, getLabelSizeWeb } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Icon from \"../Icon\";\nimport Surface from \"../Surface\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport AnimatedText from \"../Typography/AnimatedText\";\nvar SIZE = 56;\nvar SCALE = 0.9;\nvar AnimatedFAB = function AnimatedFAB(_ref) {\n  var _ref3, _ref4;\n  var icon = _ref.icon,\n    label = _ref.label,\n    background = _ref.background,\n    _ref$accessibilityLab = _ref.accessibilityLabel,\n    accessibilityLabel = _ref$accessibilityLab === void 0 ? label : _ref$accessibilityLab,\n    accessibilityState = _ref.accessibilityState,\n    customColor = _ref.color,\n    customRippleColor = _ref.rippleColor,\n    disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    delayLongPress = _ref.delayLongPress,\n    themeOverrides = _ref.theme,\n    style = _ref.style,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible,\n    uppercaseProp = _ref.uppercase,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'animated-fab' : _ref$testID,\n    _ref$animateFrom = _ref.animateFrom,\n    animateFrom = _ref$animateFrom === void 0 ? 'right' : _ref$animateFrom,\n    _ref$extended = _ref.extended,\n    extended = _ref$extended === void 0 ? false : _ref$extended,\n    _ref$iconMode = _ref.iconMode,\n    iconMode = _ref$iconMode === void 0 ? 'dynamic' : _ref$iconMode,\n    _ref$variant = _ref.variant,\n    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,\n    labelMaxFontSizeMultiplier = _ref.labelMaxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var uppercase = uppercaseProp != null ? uppercaseProp : !theme.isV3;\n  var isIOS = Platform.OS === 'ios';\n  var isWeb = Platform.OS === 'web';\n  var isAnimatedFromRight = animateFrom === 'right';\n  var isIconStatic = iconMode === 'static';\n  var isRTL = I18nManager.isRTL;\n  var labelRef = React.useRef(null);\n  var _React$useRef = React.useRef(new Animated.Value(visible ? 1 : 0)),\n    visibility = _React$useRef.current;\n  var _React$useRef2 = React.useRef(new Animated.Value(0)),\n    animFAB = _React$useRef2.current;\n  var isV3 = theme.isV3,\n    animation = theme.animation;\n  var scale = animation.scale;\n  var labelSize = isWeb ? getLabelSizeWeb(labelRef) : null;\n  var _React$useState = React.useState((_ref3 = labelSize === null || labelSize === void 0 ? void 0 : labelSize.width) != null ? _ref3 : 0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    textWidth = _React$useState2[0],\n    setTextWidth = _React$useState2[1];\n  var _React$useState3 = React.useState((_ref4 = labelSize === null || labelSize === void 0 ? void 0 : labelSize.height) != null ? _ref4 : 0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    textHeight = _React$useState4[0],\n    setTextHeight = _React$useState4[1];\n  var borderRadius = SIZE / (isV3 ? 3.5 : 2);\n  React.useEffect(function () {\n    if (!isWeb) {\n      return;\n    }\n    var updateTextSize = function updateTextSize() {\n      if (labelRef.current) {\n        var _labelSize = getLabelSizeWeb(labelRef);\n        if (_labelSize) {\n          var _labelSize$height, _labelSize$width;\n          setTextHeight((_labelSize$height = _labelSize.height) != null ? _labelSize$height : 0);\n          setTextWidth((_labelSize$width = _labelSize.width) != null ? _labelSize$width : 0);\n        }\n      }\n    };\n    updateTextSize();\n    window.addEventListener('resize', updateTextSize);\n    return function () {\n      if (!isWeb) {\n        return;\n      }\n      window.removeEventListener('resize', updateTextSize);\n    };\n  }, [isWeb]);\n  React.useEffect(function () {\n    if (visible) {\n      Animated.timing(visibility, {\n        toValue: 1,\n        duration: 200 * scale,\n        useNativeDriver: true\n      }).start();\n    } else {\n      Animated.timing(visibility, {\n        toValue: 0,\n        duration: 150 * scale,\n        useNativeDriver: true\n      }).start();\n    }\n  }, [visible, scale, visibility]);\n  var _ref5 = StyleSheet.flatten(style) || {},\n    customBackgroundColor = _ref5.backgroundColor,\n    restStyle = _objectWithoutProperties(_ref5, _excluded2);\n  var _getFABColors = getFABColors({\n      theme: theme,\n      variant: variant,\n      disabled: disabled,\n      customColor: customColor,\n      customBackgroundColor: customBackgroundColor\n    }),\n    backgroundColor = _getFABColors.backgroundColor,\n    foregroundColor = _getFABColors.foregroundColor;\n  var rippleColor = customRippleColor || color(foregroundColor).alpha(0.12).rgb().string();\n  var extendedWidth = textWidth + SIZE + borderRadius;\n  var distance = isAnimatedFromRight ? -textWidth - borderRadius : textWidth + borderRadius;\n  React.useEffect(function () {\n    Animated.timing(animFAB, {\n      toValue: !extended ? 0 : distance,\n      duration: 150 * scale,\n      useNativeDriver: true,\n      easing: Easing.linear\n    }).start();\n  }, [animFAB, scale, distance, extended]);\n  var onTextLayout = function onTextLayout(_ref2) {\n    var nativeEvent = _ref2.nativeEvent;\n    var currentWidth = Math.ceil(nativeEvent.lines[0].width);\n    var currentHeight = Math.ceil(nativeEvent.lines[0].height);\n    if (currentWidth !== textWidth || currentHeight !== textHeight) {\n      setTextHeight(currentHeight);\n      if (isIOS) {\n        return setTextWidth(currentWidth - 12);\n      }\n      setTextWidth(currentWidth);\n    }\n  };\n  var propForDirection = function propForDirection(right) {\n    if (isAnimatedFromRight) {\n      return right;\n    }\n    return right.reverse();\n  };\n  var combinedStyles = getCombinedStyles({\n    isAnimatedFromRight: isAnimatedFromRight,\n    isIconStatic: isIconStatic,\n    distance: distance,\n    animFAB: animFAB\n  });\n  var font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n  var textStyle = _objectSpread({\n    color: foregroundColor\n  }, font);\n  var md2Elevation = disabled || !isIOS ? 0 : 6;\n  var md3Elevation = disabled || !isIOS ? 0 : 3;\n  var shadowStyle = isV3 ? styles.v3Shadow : styles.shadow;\n  var baseStyle = [StyleSheet.absoluteFill, disabled ? styles.disabled : shadowStyle];\n  var newAccessibilityState = _objectSpread(_objectSpread({}, accessibilityState), {}, {\n    disabled: disabled\n  });\n  return React.createElement(Surface, _extends({}, rest, {\n    testID: `${testID}-container`,\n    style: [{\n      opacity: visibility,\n      transform: [{\n        scale: visibility\n      }],\n      borderRadius: borderRadius\n    }, !isV3 && {\n      elevation: md2Elevation\n    }, styles.container, restStyle]\n  }, isV3 && {\n    elevation: md3Elevation\n  }, {\n    theme: theme\n  }), React.createElement(Animated.View, {\n    style: [!isV3 && {\n      transform: [{\n        scaleY: animFAB.interpolate({\n          inputRange: propForDirection([distance, 0]),\n          outputRange: propForDirection([SCALE, 1])\n        })\n      }]\n    }, styles.standard, {\n      borderRadius: borderRadius\n    }]\n  }, React.createElement(View, {\n    style: [StyleSheet.absoluteFill, styles.shadowWrapper]\n  }, React.createElement(Animated.View, {\n    pointerEvents: \"none\",\n    style: [baseStyle, {\n      width: extendedWidth,\n      opacity: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0.9 * distance, 0]),\n        outputRange: propForDirection([1, 0.15, 0])\n      }),\n      borderRadius: borderRadius\n    }],\n    testID: `${testID}-extended-shadow`\n  }), React.createElement(Animated.View, {\n    pointerEvents: \"none\",\n    style: [baseStyle, {\n      opacity: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0.9 * distance, 0]),\n        outputRange: propForDirection([0, 0.85, 1])\n      }),\n      width: SIZE,\n      borderRadius: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0]),\n        outputRange: propForDirection([SIZE / (extendedWidth / SIZE), borderRadius])\n      })\n    }, combinedStyles.absoluteFill],\n    testID: `${testID}-shadow`\n  })), React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: [styles.innerWrapper, {\n      borderRadius: borderRadius\n    }]\n  }, React.createElement(Animated.View, {\n    style: [styles.standard, {\n      width: extendedWidth,\n      backgroundColor: backgroundColor,\n      borderRadius: borderRadius\n    }, combinedStyles.innerWrapper]\n  }, React.createElement(TouchableRipple, {\n    borderless: true,\n    background: background,\n    onPress: onPress,\n    onLongPress: onLongPress,\n    delayLongPress: delayLongPress,\n    rippleColor: rippleColor,\n    disabled: disabled,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"button\",\n    accessibilityState: newAccessibilityState,\n    testID: testID,\n    style: {\n      borderRadius: borderRadius\n    },\n    theme: theme\n  }, React.createElement(View, {\n    style: [styles.standard, {\n      width: extendedWidth,\n      borderRadius: borderRadius\n    }]\n  }))))), React.createElement(Animated.View, {\n    style: [styles.iconWrapper, combinedStyles.iconWrapper],\n    pointerEvents: \"none\"\n  }, React.createElement(Icon, {\n    source: icon,\n    size: 24,\n    color: foregroundColor,\n    theme: theme\n  })), React.createElement(View, {\n    pointerEvents: \"none\"\n  }, React.createElement(AnimatedText, {\n    ref: isWeb ? labelRef : null,\n    variant: \"labelLarge\",\n    numberOfLines: 1,\n    onTextLayout: isIOS ? onTextLayout : undefined,\n    ellipsizeMode: 'tail',\n    style: [_defineProperty({}, isAnimatedFromRight || isRTL ? 'right' : 'left', isIconStatic ? textWidth - SIZE + borderRadius / (isV3 ? 1 : 2) : borderRadius), {\n      minWidth: textWidth,\n      top: -SIZE / 2 - textHeight / 2,\n      opacity: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0.7 * distance, 0]),\n        outputRange: propForDirection([1, 0, 0])\n      }),\n      transform: [{\n        translateX: animFAB.interpolate({\n          inputRange: propForDirection([distance, 0]),\n          outputRange: propForDirection([0, SIZE])\n        })\n      }]\n    }, styles.label, uppercase && styles.uppercaseLabel, textStyle],\n    theme: theme,\n    testID: `${testID}-text`,\n    maxFontSizeMultiplier: labelMaxFontSizeMultiplier\n  }, label)), !isIOS && React.createElement(ScrollView, {\n    style: styles.textPlaceholderContainer\n  }, React.createElement(AnimatedText, {\n    variant: \"labelLarge\",\n    numberOfLines: 1,\n    onTextLayout: onTextLayout,\n    ellipsizeMode: 'tail',\n    style: [styles.label, uppercase && styles.uppercaseLabel, textStyle],\n    theme: theme\n  }, label)));\n};\nvar styles = StyleSheet.create({\n  standard: {\n    height: SIZE\n  },\n  disabled: {\n    elevation: 0\n  },\n  container: {\n    position: 'absolute',\n    backgroundColor: 'transparent'\n  },\n  innerWrapper: {\n    flexDirection: 'row',\n    overflow: 'hidden'\n  },\n  shadowWrapper: {\n    elevation: 0\n  },\n  shadow: {\n    elevation: 6\n  },\n  v3Shadow: {\n    elevation: 3\n  },\n  iconWrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    position: 'absolute',\n    height: SIZE,\n    width: SIZE\n  },\n  label: {\n    position: 'absolute'\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase'\n  },\n  textPlaceholderContainer: {\n    height: 0,\n    position: 'absolute'\n  }\n});\nexport default AnimatedFAB;", "map": {"version": 3, "names": ["React", "Animated", "Easing", "I18nManager", "Platform", "ScrollView", "StyleSheet", "View", "color", "getCombinedStyles", "getFABColors", "getLabelSizeWeb", "useInternalTheme", "Icon", "Surface", "TouchableRipple", "AnimatedText", "SIZE", "SCALE", "AnimatedFAB", "_ref", "_ref3", "_ref4", "icon", "label", "background", "_ref$accessibilityLab", "accessibilityLabel", "accessibilityState", "customColor", "customRippleColor", "rippleColor", "disabled", "onPress", "onLongPress", "delayLongPress", "themeOverrides", "theme", "style", "_ref$visible", "visible", "uppercaseProp", "uppercase", "_ref$testID", "testID", "_ref$animateFrom", "animateFrom", "_ref$extended", "extended", "_ref$iconMode", "iconMode", "_ref$variant", "variant", "labelMaxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "isV3", "isIOS", "OS", "isWeb", "isAnimatedFromRight", "isIconStatic", "isRTL", "labelRef", "useRef", "_React$useRef", "Value", "visibility", "current", "_React$useRef2", "animFAB", "animation", "scale", "labelSize", "_React$useState", "useState", "width", "_React$useState2", "_slicedToArray", "textWidth", "setTextWidth", "_React$useState3", "height", "_React$useState4", "textHeight", "setTextHeight", "borderRadius", "useEffect", "updateTextSize", "_labelSize$height", "_labelSize$width", "window", "addEventListener", "removeEventListener", "timing", "toValue", "duration", "useNativeDriver", "start", "_ref5", "flatten", "customBackgroundColor", "backgroundColor", "restStyle", "_excluded2", "_getFABColors", "foregroundColor", "alpha", "rgb", "string", "extendedWidth", "distance", "easing", "linear", "onTextLayout", "_ref2", "nativeEvent", "currentWidth", "Math", "ceil", "lines", "currentHeight", "propForDirection", "right", "reverse", "combinedStyles", "font", "fonts", "labelLarge", "medium", "textStyle", "_objectSpread", "md2Elevation", "md3Elevation", "shadowStyle", "styles", "v3Shadow", "shadow", "baseStyle", "absoluteFill", "newAccessibilityState", "createElement", "_extends", "opacity", "transform", "elevation", "container", "scaleY", "interpolate", "inputRange", "outputRange", "standard", "shadowWrapper", "pointerEvents", "innerWrapper", "borderless", "accessibilityRole", "iconWrapper", "source", "size", "ref", "numberOfLines", "undefined", "ellipsizeMode", "_defineProperty", "min<PERSON><PERSON><PERSON>", "top", "translateX", "uppercase<PERSON>abel", "maxFontSizeMultiplier", "textPlaceholderContainer", "create", "position", "flexDirection", "overflow", "alignItems", "justifyContent", "textTransform"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/FAB/AnimatedFAB.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type {\n  AccessibilityState,\n  ColorValue,\n  NativeSyntheticEvent,\n  PressableAndroidRippleConfig,\n  TextLayoutEventData,\n} from 'react-native';\nimport {\n  Animated,\n  Easing,\n  GestureResponderEvent,\n  I18nManager,\n  Platform,\n  ScrollView,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n  Text,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { getCombinedStyles, getFABColors, getLabelSizeWeb } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $Omit, $RemoveChildren, ThemeProp } from '../../types';\nimport type { IconSource } from '../Icon';\nimport Icon from '../Icon';\nimport Surface from '../Surface';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport AnimatedText from '../Typography/AnimatedText';\n\nexport type AnimatedFABIconMode = 'static' | 'dynamic';\nexport type AnimatedFABAnimateFrom = 'left' | 'right';\n\nexport type Props = $Omit<$RemoveChildren<typeof Surface>, 'mode'> & {\n  /**\n   * Icon to display for the `FAB`.\n   */\n  icon: IconSource;\n  /**\n   * Label for extended `FAB`.\n   */\n  label: string;\n  /**\n   * Make the label text uppercased.\n   */\n  uppercase?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the FAB. This is read by the screen reader when the user taps the FAB.\n   * Uses `label` by default if specified.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Accessibility state for the FAB. This is read by the screen reader when the user taps the FAB.\n   */\n  accessibilityState?: AccessibilityState;\n  /**\n   * Custom color for the icon and label of the `FAB`.\n   */\n  color?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Whether `FAB` is disabled. A disabled button is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Whether `FAB` is currently visible.\n   */\n  visible?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * The number of milliseconds a user must touch the element before executing `onLongPress`.\n   */\n  delayLongPress?: number;\n  /**\n   * Whether icon should be translated to the end of extended `FAB` or be static and stay in the same place. The default value is `dynamic`.\n   */\n  iconMode?: AnimatedFABIconMode;\n  /**\n   * Indicates from which direction animation should be performed. The default value is `right`.\n   */\n  animateFrom?: AnimatedFABAnimateFrom;\n  /**\n   * Whether `FAB` should start animation to extend.\n   */\n  extended: boolean;\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Color mappings variant for combinations of container and icon colors.\n   */\n  variant?: 'primary' | 'secondary' | 'tertiary' | 'surface';\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n};\n\nconst SIZE = 56;\nconst SCALE = 0.9;\n\n/**\n * An animated, extending horizontally floating action button represents the primary action in an application.\n *\n * ## Usage\n * ```js\n * import React from 'react';\n * import {\n *   StyleProp,\n *   ViewStyle,\n *   Animated,\n *   StyleSheet,\n *   Platform,\n *   ScrollView,\n *   Text,\n *   SafeAreaView,\n *   I18nManager,\n * } from 'react-native';\n * import { AnimatedFAB } from 'react-native-paper';\n *\n * const MyComponent = ({\n *   animatedValue,\n *   visible,\n *   extended,\n *   label,\n *   animateFrom,\n *   style,\n *   iconMode,\n * }) => {\n *   const [isExtended, setIsExtended] = React.useState(true);\n *\n *   const isIOS = Platform.OS === 'ios';\n *\n *   const onScroll = ({ nativeEvent }) => {\n *     const currentScrollPosition =\n *       Math.floor(nativeEvent?.contentOffset?.y) ?? 0;\n *\n *     setIsExtended(currentScrollPosition <= 0);\n *   };\n *\n *   const fabStyle = { [animateFrom]: 16 };\n *\n *   return (\n *     <SafeAreaView style={styles.container}>\n *       <ScrollView onScroll={onScroll}>\n *         {[...new Array(100).keys()].map((_, i) => (\n *           <Text>{i}</Text>\n *         ))}\n *       </ScrollView>\n *       <AnimatedFAB\n *         icon={'plus'}\n *         label={'Label'}\n *         extended={isExtended}\n *         onPress={() => console.log('Pressed')}\n *         visible={visible}\n *         animateFrom={'right'}\n *         iconMode={'static'}\n *         style={[styles.fabStyle, style, fabStyle]}\n *       />\n *     </SafeAreaView>\n *   );\n * };\n *\n * export default MyComponent;\n *\n * const styles = StyleSheet.create({\n *   container: {\n *     flexGrow: 1,\n *   },\n *   fabStyle: {\n *     bottom: 16,\n *     right: 16,\n *     position: 'absolute',\n *   },\n * });\n * ```\n */\nconst AnimatedFAB = ({\n  icon,\n  label,\n  background,\n  accessibilityLabel = label,\n  accessibilityState,\n  color: customColor,\n  rippleColor: customRippleColor,\n  disabled,\n  onPress,\n  onLongPress,\n  delayLongPress,\n  theme: themeOverrides,\n  style,\n  visible = true,\n  uppercase: uppercaseProp,\n  testID = 'animated-fab',\n  animateFrom = 'right',\n  extended = false,\n  iconMode = 'dynamic',\n  variant = 'primary',\n  labelMaxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const uppercase: boolean = uppercaseProp ?? !theme.isV3;\n  const isIOS = Platform.OS === 'ios';\n  const isWeb = Platform.OS === 'web';\n  const isAnimatedFromRight = animateFrom === 'right';\n  const isIconStatic = iconMode === 'static';\n  const { isRTL } = I18nManager;\n  const labelRef = React.useRef<Text & HTMLElement>(null);\n  const { current: visibility } = React.useRef<Animated.Value>(\n    new Animated.Value(visible ? 1 : 0)\n  );\n  const { current: animFAB } = React.useRef<Animated.Value>(\n    new Animated.Value(0)\n  );\n  const { isV3, animation } = theme;\n  const { scale } = animation;\n\n  const labelSize = isWeb ? getLabelSizeWeb(labelRef) : null;\n  const [textWidth, setTextWidth] = React.useState<number>(\n    labelSize?.width ?? 0\n  );\n  const [textHeight, setTextHeight] = React.useState<number>(\n    labelSize?.height ?? 0\n  );\n\n  const borderRadius = SIZE / (isV3 ? 3.5 : 2);\n\n  React.useEffect(() => {\n    if (!isWeb) {\n      return;\n    }\n\n    const updateTextSize = () => {\n      if (labelRef.current) {\n        const labelSize = getLabelSizeWeb(labelRef);\n\n        if (labelSize) {\n          setTextHeight(labelSize.height ?? 0);\n          setTextWidth(labelSize.width ?? 0);\n        }\n      }\n    };\n\n    updateTextSize();\n    window.addEventListener('resize', updateTextSize);\n\n    return () => {\n      if (!isWeb) {\n        return;\n      }\n\n      window.removeEventListener('resize', updateTextSize);\n    };\n  }, [isWeb]);\n\n  React.useEffect(() => {\n    if (visible) {\n      Animated.timing(visibility, {\n        toValue: 1,\n        duration: 200 * scale,\n        useNativeDriver: true,\n      }).start();\n    } else {\n      Animated.timing(visibility, {\n        toValue: 0,\n        duration: 150 * scale,\n        useNativeDriver: true,\n      }).start();\n    }\n  }, [visible, scale, visibility]);\n\n  const { backgroundColor: customBackgroundColor, ...restStyle } =\n    (StyleSheet.flatten(style) || {}) as ViewStyle;\n\n  const { backgroundColor, foregroundColor } = getFABColors({\n    theme,\n    variant,\n    disabled,\n    customColor,\n    customBackgroundColor,\n  });\n\n  const rippleColor =\n    customRippleColor || color(foregroundColor).alpha(0.12).rgb().string();\n\n  const extendedWidth = textWidth + SIZE + borderRadius;\n\n  const distance = isAnimatedFromRight\n    ? -textWidth - borderRadius\n    : textWidth + borderRadius;\n\n  React.useEffect(() => {\n    Animated.timing(animFAB, {\n      toValue: !extended ? 0 : distance,\n      duration: 150 * scale,\n      useNativeDriver: true,\n      easing: Easing.linear,\n    }).start();\n  }, [animFAB, scale, distance, extended]);\n\n  const onTextLayout = ({\n    nativeEvent,\n  }: NativeSyntheticEvent<TextLayoutEventData>) => {\n    const currentWidth = Math.ceil(nativeEvent.lines[0].width);\n    const currentHeight = Math.ceil(nativeEvent.lines[0].height);\n\n    if (currentWidth !== textWidth || currentHeight !== textHeight) {\n      setTextHeight(currentHeight);\n\n      if (isIOS) {\n        return setTextWidth(currentWidth - 12);\n      }\n\n      setTextWidth(currentWidth);\n    }\n  };\n\n  const propForDirection = <T,>(right: T[]): T[] => {\n    if (isAnimatedFromRight) {\n      return right;\n    }\n\n    return right.reverse();\n  };\n\n  const combinedStyles = getCombinedStyles({\n    isAnimatedFromRight,\n    isIconStatic,\n    distance,\n    animFAB,\n  });\n\n  const font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n\n  const textStyle = {\n    color: foregroundColor,\n    ...font,\n  };\n\n  const md2Elevation = disabled || !isIOS ? 0 : 6;\n  const md3Elevation = disabled || !isIOS ? 0 : 3;\n\n  const shadowStyle = isV3 ? styles.v3Shadow : styles.shadow;\n  const baseStyle = [\n    StyleSheet.absoluteFill,\n    disabled ? styles.disabled : shadowStyle,\n  ];\n\n  const newAccessibilityState = { ...accessibilityState, disabled };\n\n  return (\n    <Surface\n      {...rest}\n      testID={`${testID}-container`}\n      style={[\n        {\n          opacity: visibility,\n          transform: [\n            {\n              scale: visibility,\n            },\n          ],\n          borderRadius,\n        },\n        !isV3 && {\n          elevation: md2Elevation,\n        },\n        styles.container,\n        restStyle,\n      ]}\n      {...(isV3 && { elevation: md3Elevation })}\n      theme={theme}\n    >\n      <Animated.View\n        style={[\n          !isV3 && {\n            transform: [\n              {\n                scaleY: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0]),\n                  outputRange: propForDirection([SCALE, 1]),\n                }),\n              },\n            ],\n          },\n          styles.standard,\n          { borderRadius },\n        ]}\n      >\n        <View style={[StyleSheet.absoluteFill, styles.shadowWrapper]}>\n          <Animated.View\n            pointerEvents=\"none\"\n            style={[\n              baseStyle,\n              {\n                width: extendedWidth,\n                opacity: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0.9 * distance, 0]),\n                  outputRange: propForDirection([1, 0.15, 0]),\n                }),\n                borderRadius,\n              },\n            ]}\n            testID={`${testID}-extended-shadow`}\n          />\n          <Animated.View\n            pointerEvents=\"none\"\n            style={[\n              baseStyle,\n              {\n                opacity: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0.9 * distance, 0]),\n                  outputRange: propForDirection([0, 0.85, 1]),\n                }),\n                width: SIZE,\n                borderRadius: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0]),\n                  outputRange: propForDirection([\n                    SIZE / (extendedWidth / SIZE),\n                    borderRadius,\n                  ]),\n                }),\n              },\n              combinedStyles.absoluteFill,\n            ]}\n            testID={`${testID}-shadow`}\n          />\n        </View>\n        <Animated.View\n          pointerEvents=\"box-none\"\n          style={[styles.innerWrapper, { borderRadius }]}\n        >\n          <Animated.View\n            style={[\n              styles.standard,\n              {\n                width: extendedWidth,\n                backgroundColor,\n                borderRadius,\n              },\n              combinedStyles.innerWrapper,\n            ]}\n          >\n            <TouchableRipple\n              borderless\n              background={background}\n              onPress={onPress}\n              onLongPress={onLongPress}\n              delayLongPress={delayLongPress}\n              rippleColor={rippleColor}\n              disabled={disabled}\n              accessibilityLabel={accessibilityLabel}\n              accessibilityRole=\"button\"\n              accessibilityState={newAccessibilityState}\n              testID={testID}\n              style={{ borderRadius }}\n              theme={theme}\n            >\n              <View\n                style={[\n                  styles.standard,\n                  {\n                    width: extendedWidth,\n                    borderRadius,\n                  },\n                ]}\n              />\n            </TouchableRipple>\n          </Animated.View>\n        </Animated.View>\n      </Animated.View>\n\n      <Animated.View\n        style={[styles.iconWrapper, combinedStyles.iconWrapper]}\n        pointerEvents=\"none\"\n      >\n        <Icon source={icon} size={24} color={foregroundColor} theme={theme} />\n      </Animated.View>\n\n      <View pointerEvents=\"none\">\n        <AnimatedText\n          ref={isWeb ? labelRef : null}\n          variant=\"labelLarge\"\n          numberOfLines={1}\n          onTextLayout={isIOS ? onTextLayout : undefined}\n          ellipsizeMode={'tail'}\n          style={[\n            {\n              [isAnimatedFromRight || isRTL ? 'right' : 'left']: isIconStatic\n                ? textWidth - SIZE + borderRadius / (isV3 ? 1 : 2)\n                : borderRadius,\n            },\n            {\n              minWidth: textWidth,\n              top: -SIZE / 2 - textHeight / 2,\n              opacity: animFAB.interpolate({\n                inputRange: propForDirection([distance, 0.7 * distance, 0]),\n                outputRange: propForDirection([1, 0, 0]),\n              }) as unknown as number,\n              // TODO: check\n              transform: [\n                {\n                  translateX: animFAB.interpolate({\n                    inputRange: propForDirection([distance, 0]),\n                    outputRange: propForDirection([0, SIZE]),\n                  }),\n                },\n              ],\n            },\n            styles.label,\n            uppercase && styles.uppercaseLabel,\n            textStyle,\n          ]}\n          theme={theme}\n          testID={`${testID}-text`}\n          maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n        >\n          {label}\n        </AnimatedText>\n      </View>\n\n      {!isIOS && (\n        // Method `onTextLayout` on Android returns sizes of text visible on the screen,\n        // however during render the text in `FAB` isn't fully visible. In order to get\n        // proper text measurements there is a need to additionaly render that text, but\n        // wrapped in absolutely positioned `ScrollView` which height is 0.\n        <ScrollView style={styles.textPlaceholderContainer}>\n          <AnimatedText\n            variant=\"labelLarge\"\n            numberOfLines={1}\n            onTextLayout={onTextLayout}\n            ellipsizeMode={'tail'}\n            style={[\n              styles.label,\n              uppercase && styles.uppercaseLabel,\n              textStyle,\n            ]}\n            theme={theme}\n          >\n            {label}\n          </AnimatedText>\n        </ScrollView>\n      )}\n    </Surface>\n  );\n};\n\nconst styles = StyleSheet.create({\n  standard: {\n    height: SIZE,\n  },\n  disabled: {\n    elevation: 0,\n  },\n  // eslint-disable-next-line react-native/no-color-literals\n  container: {\n    position: 'absolute',\n    backgroundColor: 'transparent',\n  },\n  innerWrapper: {\n    flexDirection: 'row',\n    overflow: 'hidden',\n  },\n  shadowWrapper: {\n    elevation: 0,\n  },\n  shadow: {\n    elevation: 6,\n  },\n  v3Shadow: {\n    elevation: 3,\n  },\n  iconWrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    position: 'absolute',\n    height: SIZE,\n    width: SIZE,\n  },\n  label: {\n    position: 'absolute',\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase',\n  },\n  textPlaceholderContainer: {\n    height: 0,\n    position: 'absolute',\n  },\n});\n\nexport default AnimatedFAB;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAsB9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,iBAAiB,EAAEC,YAAY,EAAEC,eAAe;AACzD,SAASC,gBAAgB;AAGzB,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,eAAe;AACtB,OAAOC,YAAY;AA6FnB,IAAMC,IAAI,GAAG,EAAE;AACf,IAAMC,KAAK,GAAG,GAAG;AA8EjB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAGC,IAAA,EAuBP;EAAA,IAAAC,KAAA,EAAAC,KAAA;EAAA,IAtBXC,IAAI,GAsBEH,IAAA,CAtBNG,IAAI;IACJC,KAAK,GAqBCJ,IAAA,CArBNI,KAAK;IACLC,UAAU,GAoBJL,IAAA,CApBNK,UAAU;IAAAC,qBAAA,GAoBJN,IAAA,CAnBNO,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGF,KAAK,GAAAE,qBAAA;IAC1BE,kBAAkB,GAkBZR,IAAA,CAlBNQ,kBAAkB;IACXC,WAAW,GAiBZT,IAAA,CAjBNZ,KAAK;IACQsB,iBAAiB,GAgBxBV,IAAA,CAhBNW,WAAW;IACXC,QAAQ,GAeFZ,IAAA,CAfNY,QAAQ;IACRC,OAAO,GAcDb,IAAA,CAdNa,OAAO;IACPC,WAAW,GAaLd,IAAA,CAbNc,WAAW;IACXC,cAAc,GAYRf,IAAA,CAZNe,cAAc;IACPC,cAAc,GAWfhB,IAAA,CAXNiB,KAAK;IACLC,KAAK,GAUClB,IAAA,CAVNkB,KAAK;IAAAC,YAAA,GAUCnB,IAAA,CATNoB,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IACHE,aAAa,GAQlBrB,IAAA,CARNsB,SAAS;IAAAC,WAAA,GAQHvB,IAAA,CAPNwB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,cAAc,GAAAA,WAAA;IAAAE,gBAAA,GAOjBzB,IAAA,CANN0B,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,OAAO,GAAAA,gBAAA;IAAAE,aAAA,GAMf3B,IAAA,CALN4B,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,aAAA,GAKV7B,IAAA,CAJN8B,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,SAAS,GAAAA,aAAA;IAAAE,YAAA,GAId/B,IAAA,CAHNgC,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,SAAS,GAAAA,YAAA;IACnBE,0BAA0B,GAEpBjC,IAAA,CAFNiC,0BAA0B;IACvBC,IAAA,GAAAC,wBAAA,CACGnC,IAAA,EAAAoC,SAAA;EACN,IAAMnB,KAAK,GAAGzB,gBAAgB,CAACwB,cAAc,CAAC;EAC9C,IAAMM,SAAkB,GAAGD,aAAa,WAAbA,aAAa,GAAI,CAACJ,KAAK,CAACoB,IAAI;EACvD,IAAMC,KAAK,GAAGtD,QAAQ,CAACuD,EAAE,KAAK,KAAK;EACnC,IAAMC,KAAK,GAAGxD,QAAQ,CAACuD,EAAE,KAAK,KAAK;EACnC,IAAME,mBAAmB,GAAGf,WAAW,KAAK,OAAO;EACnD,IAAMgB,YAAY,GAAGZ,QAAQ,KAAK,QAAQ;EAC1C,IAAQa,KAAA,GAAU5D,WAAW,CAArB4D,KAAA;EACR,IAAMC,QAAQ,GAAGhE,KAAK,CAACiE,MAAM,CAAqB,IAAI,CAAC;EACvD,IAAAC,aAAA,GAAgClE,KAAK,CAACiE,MAAM,CAC1C,IAAIhE,QAAQ,CAACkE,KAAK,CAAC3B,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;IAFgB4B,UAAA,GAAAF,aAAA,CAATG,OAAO;EAGf,IAAAC,cAAA,GAA6BtE,KAAK,CAACiE,MAAM,CACvC,IAAIhE,QAAQ,CAACkE,KAAK,CAAC,CAAC,CACtB,CAAC;IAFgBI,OAAA,GAAAD,cAAA,CAATD,OAAO;EAGf,IAAQZ,IAAI,GAAgBpB,KAAK,CAAzBoB,IAAI;IAAEe,SAAA,GAAcnC,KAAK,CAAnBmC,SAAA;EACd,IAAQC,KAAA,GAAUD,SAAS,CAAnBC,KAAA;EAER,IAAMC,SAAS,GAAGd,KAAK,GAAGjD,eAAe,CAACqD,QAAQ,CAAC,GAAG,IAAI;EAC1D,IAAAW,eAAA,GAAkC3E,KAAK,CAAC4E,QAAQ,EAAAvD,KAAA,GAC9CqD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,KAAK,YAAAxD,KAAA,GAAI,CACtB,CAAC;IAAAyD,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;IAFMK,SAAS,GAAAF,gBAAA;IAAEG,YAAY,GAAAH,gBAAA;EAG9B,IAAAI,gBAAA,GAAoClF,KAAK,CAAC4E,QAAQ,EAAAtD,KAAA,GAChDoD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,MAAM,YAAA7D,KAAA,GAAI,CACvB,CAAC;IAAA8D,gBAAA,GAAAL,cAAA,CAAAG,gBAAA;IAFMG,UAAU,GAAAD,gBAAA;IAAEE,aAAa,GAAAF,gBAAA;EAIhC,IAAMG,YAAY,GAAGtE,IAAI,IAAIwC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;EAE5CzD,KAAK,CAACwF,SAAS,CAAC,YAAM;IACpB,IAAI,CAAC5B,KAAK,EAAE;MACV;IACF;IAEA,IAAM6B,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAIzB,QAAQ,CAACK,OAAO,EAAE;QACpB,IAAMK,UAAS,GAAG/D,eAAe,CAACqD,QAAQ,CAAC;QAE3C,IAAIU,UAAS,EAAE;UAAA,IAAAgB,iBAAA,EAAAC,gBAAA;UACbL,aAAa,EAAAI,iBAAA,GAAChB,UAAS,CAACS,MAAM,YAAAO,iBAAA,GAAI,CAAC,CAAC;UACpCT,YAAY,EAAAU,gBAAA,GAACjB,UAAS,CAACG,KAAK,YAAAc,gBAAA,GAAI,CAAC,CAAC;QACpC;MACF;IACF,CAAC;IAEDF,cAAc,CAAC,CAAC;IAChBG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,cAAc,CAAC;IAEjD,OAAO,YAAM;MACX,IAAI,CAAC7B,KAAK,EAAE;QACV;MACF;MAEAgC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEL,cAAc,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAAC7B,KAAK,CAAC,CAAC;EAEX5D,KAAK,CAACwF,SAAS,CAAC,YAAM;IACpB,IAAIhD,OAAO,EAAE;MACXvC,QAAQ,CAAC8F,MAAM,CAAC3B,UAAU,EAAE;QAC1B4B,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGxB,KAAK;QACrByB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACLlG,QAAQ,CAAC8F,MAAM,CAAC3B,UAAU,EAAE;QAC1B4B,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGxB,KAAK;QACrByB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAC3D,OAAO,EAAEiC,KAAK,EAAEL,UAAU,CAAC,CAAC;EAEhC,IAAAgC,KAAA,GACG9F,UAAU,CAAC+F,OAAO,CAAC/D,KAAK,CAAC,IAAI,CAAC,CAAe;IADvBgE,qBAAqB,GAAAF,KAAA,CAAtCG,eAAe;IAA4BC,SAAA,GAAAjD,wBAAA,CAAA6C,KAAA,EAAAK,UAAA;EAGnD,IAAAC,aAAA,GAA6ChG,YAAY,CAAC;MACxD2B,KAAK,EAALA,KAAK;MACLe,OAAO,EAAPA,OAAO;MACPpB,QAAQ,EAARA,QAAQ;MACRH,WAAW,EAAXA,WAAW;MACXyE,qBAAA,EAAAA;IACF,CAAC,CAAC;IANMC,eAAe,GAAAG,aAAA,CAAfH,eAAe;IAAEI,eAAA,GAAAD,aAAA,CAAAC,eAAA;EAQzB,IAAM5E,WAAW,GACfD,iBAAiB,IAAItB,KAAK,CAACmG,eAAe,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAExE,IAAMC,aAAa,GAAG/B,SAAS,GAAG/D,IAAI,GAAGsE,YAAY;EAErD,IAAMyB,QAAQ,GAAGnD,mBAAmB,GAChC,CAACmB,SAAS,GAAGO,YAAY,GACzBP,SAAS,GAAGO,YAAY;EAE5BvF,KAAK,CAACwF,SAAS,CAAC,YAAM;IACpBvF,QAAQ,CAAC8F,MAAM,CAACxB,OAAO,EAAE;MACvByB,OAAO,EAAE,CAAChD,QAAQ,GAAG,CAAC,GAAGgE,QAAQ;MACjCf,QAAQ,EAAE,GAAG,GAAGxB,KAAK;MACrByB,eAAe,EAAE,IAAI;MACrBe,MAAM,EAAE/G,MAAM,CAACgH;IACjB,CAAC,CAAC,CAACf,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC5B,OAAO,EAAEE,KAAK,EAAEuC,QAAQ,EAAEhE,QAAQ,CAAC,CAAC;EAExC,IAAMmE,YAAY,GAAG,SAAfA,YAAYA,CAAGC,KAAA,EAE4B;IAAA,IAD/CC,WAAA,GAC0CD,KAAA,CAD1CC,WAAA;IAEA,IAAMC,YAAY,GAAGC,IAAI,CAACC,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC5C,KAAK,CAAC;IAC1D,IAAM6C,aAAa,GAAGH,IAAI,CAACC,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC,CAACtC,MAAM,CAAC;IAE5D,IAAImC,YAAY,KAAKtC,SAAS,IAAI0C,aAAa,KAAKrC,UAAU,EAAE;MAC9DC,aAAa,CAACoC,aAAa,CAAC;MAE5B,IAAIhE,KAAK,EAAE;QACT,OAAOuB,YAAY,CAACqC,YAAY,GAAG,EAAE,CAAC;MACxC;MAEArC,YAAY,CAACqC,YAAY,CAAC;IAC5B;EACF,CAAC;EAED,IAAMK,gBAAgB,GAAQ,SAAxBA,gBAAgBA,CAAQC,KAAU,EAAU;IAChD,IAAI/D,mBAAmB,EAAE;MACvB,OAAO+D,KAAK;IACd;IAEA,OAAOA,KAAK,CAACC,OAAO,CAAC,CAAC;EACxB,CAAC;EAED,IAAMC,cAAc,GAAGrH,iBAAiB,CAAC;IACvCoD,mBAAmB,EAAnBA,mBAAmB;IACnBC,YAAY,EAAZA,YAAY;IACZkD,QAAQ,EAARA,QAAQ;IACRzC,OAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAMwD,IAAI,GAAGtE,IAAI,GAAGpB,KAAK,CAAC2F,KAAK,CAACC,UAAU,GAAG5F,KAAK,CAAC2F,KAAK,CAACE,MAAM;EAE/D,IAAMC,SAAS,GAAAC,aAAA;IACb5H,KAAK,EAAEmG;EAAe,GACnBoB,IAAA,CACJ;EAED,IAAMM,YAAY,GAAGrG,QAAQ,IAAI,CAAC0B,KAAK,GAAG,CAAC,GAAG,CAAC;EAC/C,IAAM4E,YAAY,GAAGtG,QAAQ,IAAI,CAAC0B,KAAK,GAAG,CAAC,GAAG,CAAC;EAE/C,IAAM6E,WAAW,GAAG9E,IAAI,GAAG+E,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACE,MAAM;EAC1D,IAAMC,SAAS,GAAG,CAChBrI,UAAU,CAACsI,YAAY,EACvB5G,QAAQ,GAAGwG,MAAM,CAACxG,QAAQ,GAAGuG,WAAW,CACzC;EAED,IAAMM,qBAAqB,GAAAT,aAAA,CAAAA,aAAA,KAAQxG,kBAAkB;IAAEI,QAAA,EAAAA;EAAA,EAAU;EAEjE,OACEhC,KAAA,CAAA8I,aAAA,CAAChI,OAAO,EAAAiI,QAAA,KACFzF,IAAI;IACRV,MAAM,EAAG,GAAEA,MAAO,YAAY;IAC9BN,KAAK,EAAE,CACL;MACE0G,OAAO,EAAE5E,UAAU;MACnB6E,SAAS,EAAE,CACT;QACExE,KAAK,EAAEL;MACT,CAAC,CACF;MACDmB,YAAA,EAAAA;IACF,CAAC,EACD,CAAC9B,IAAI,IAAI;MACPyF,SAAS,EAAEb;IACb,CAAC,EACDG,MAAM,CAACW,SAAS,EAChB3C,SAAS;EACT,GACG/C,IAAI,IAAI;IAAEyF,SAAS,EAAEZ;EAAa,CAAC;IACxCjG,KAAK,EAAEA;EAAM,IAEbrC,KAAA,CAAA8I,aAAA,CAAC7I,QAAQ,CAACM,IAAI;IACZ+B,KAAK,EAAE,CACL,CAACmB,IAAI,IAAI;MACPwF,SAAS,EAAE,CACT;QACEG,MAAM,EAAE7E,OAAO,CAAC8E,WAAW,CAAC;UAC1BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACX,QAAQ,EAAE,CAAC,CAAC,CAAC;UAC3CuC,WAAW,EAAE5B,gBAAgB,CAAC,CAACzG,KAAK,EAAE,CAAC,CAAC;QAC1C,CAAC;MACH,CAAC;IAEL,CAAC,EACDsH,MAAM,CAACgB,QAAQ,EACf;MAAEjE,YAAA,EAAAA;IAAa,CAAC;EAChB,GAEFvF,KAAA,CAAA8I,aAAA,CAACvI,IAAI;IAAC+B,KAAK,EAAE,CAAChC,UAAU,CAACsI,YAAY,EAAEJ,MAAM,CAACiB,aAAa;EAAE,GAC3DzJ,KAAA,CAAA8I,aAAA,CAAC7I,QAAQ,CAACM,IAAI;IACZmJ,aAAa,EAAC,MAAM;IACpBpH,KAAK,EAAE,CACLqG,SAAS,EACT;MACE9D,KAAK,EAAEkC,aAAa;MACpBiC,OAAO,EAAEzE,OAAO,CAAC8E,WAAW,CAAC;QAC3BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACX,QAAQ,EAAE,GAAG,GAAGA,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3DuC,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;MAC5C,CAAC,CAAC;MACFpC,YAAA,EAAAA;IACF,CAAC,CACD;IACF3C,MAAM,EAAG,GAAEA,MAAO;EAAkB,CACrC,CAAC,EACF5C,KAAA,CAAA8I,aAAA,CAAC7I,QAAQ,CAACM,IAAI;IACZmJ,aAAa,EAAC,MAAM;IACpBpH,KAAK,EAAE,CACLqG,SAAS,EACT;MACEK,OAAO,EAAEzE,OAAO,CAAC8E,WAAW,CAAC;QAC3BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACX,QAAQ,EAAE,GAAG,GAAGA,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3DuC,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;MAC5C,CAAC,CAAC;MACF9C,KAAK,EAAE5D,IAAI;MACXsE,YAAY,EAAEhB,OAAO,CAAC8E,WAAW,CAAC;QAChCC,UAAU,EAAE3B,gBAAgB,CAAC,CAACX,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3CuC,WAAW,EAAE5B,gBAAgB,CAAC,CAC5B1G,IAAI,IAAI8F,aAAa,GAAG9F,IAAI,CAAC,EAC7BsE,YAAY,CACb;MACH,CAAC;IACH,CAAC,EACDuC,cAAc,CAACc,YAAY,CAC3B;IACFhG,MAAM,EAAG,GAAEA,MAAO;EAAS,CAC5B,CACG,CAAC,EACP5C,KAAA,CAAA8I,aAAA,CAAC7I,QAAQ,CAACM,IAAI;IACZmJ,aAAa,EAAC,UAAU;IACxBpH,KAAK,EAAE,CAACkG,MAAM,CAACmB,YAAY,EAAE;MAAEpE,YAAA,EAAAA;IAAa,CAAC;EAAE,GAE/CvF,KAAA,CAAA8I,aAAA,CAAC7I,QAAQ,CAACM,IAAI;IACZ+B,KAAK,EAAE,CACLkG,MAAM,CAACgB,QAAQ,EACf;MACE3E,KAAK,EAAEkC,aAAa;MACpBR,eAAe,EAAfA,eAAe;MACfhB,YAAA,EAAAA;IACF,CAAC,EACDuC,cAAc,CAAC6B,YAAY;EAC3B,GAEF3J,KAAA,CAAA8I,aAAA,CAAC/H,eAAe;IACd6I,UAAU;IACVnI,UAAU,EAAEA,UAAW;IACvBQ,OAAO,EAAEA,OAAQ;IACjBC,WAAW,EAAEA,WAAY;IACzBC,cAAc,EAAEA,cAAe;IAC/BJ,WAAW,EAAEA,WAAY;IACzBC,QAAQ,EAAEA,QAAS;IACnBL,kBAAkB,EAAEA,kBAAmB;IACvCkI,iBAAiB,EAAC,QAAQ;IAC1BjI,kBAAkB,EAAEiH,qBAAsB;IAC1CjG,MAAM,EAAEA,MAAO;IACfN,KAAK,EAAE;MAAEiD,YAAA,EAAAA;IAAa,CAAE;IACxBlD,KAAK,EAAEA;EAAM,GAEbrC,KAAA,CAAA8I,aAAA,CAACvI,IAAI;IACH+B,KAAK,EAAE,CACLkG,MAAM,CAACgB,QAAQ,EACf;MACE3E,KAAK,EAAEkC,aAAa;MACpBxB,YAAA,EAAAA;IACF,CAAC;EACD,CACH,CACc,CACJ,CACF,CACF,CAAC,EAEhBvF,KAAA,CAAA8I,aAAA,CAAC7I,QAAQ,CAACM,IAAI;IACZ+B,KAAK,EAAE,CAACkG,MAAM,CAACsB,WAAW,EAAEhC,cAAc,CAACgC,WAAW,CAAE;IACxDJ,aAAa,EAAC;EAAM,GAEpB1J,KAAA,CAAA8I,aAAA,CAACjI,IAAI;IAACkJ,MAAM,EAAExI,IAAK;IAACyI,IAAI,EAAE,EAAG;IAACxJ,KAAK,EAAEmG,eAAgB;IAACtE,KAAK,EAAEA;EAAM,CAAE,CACxD,CAAC,EAEhBrC,KAAA,CAAA8I,aAAA,CAACvI,IAAI;IAACmJ,aAAa,EAAC;EAAM,GACxB1J,KAAA,CAAA8I,aAAA,CAAC9H,YAAY;IACXiJ,GAAG,EAAErG,KAAK,GAAGI,QAAQ,GAAG,IAAK;IAC7BZ,OAAO,EAAC,YAAY;IACpB8G,aAAa,EAAE,CAAE;IACjB/C,YAAY,EAAEzD,KAAK,GAAGyD,YAAY,GAAGgD,SAAU;IAC/CC,aAAa,EAAE,MAAO;IACtB9H,KAAK,EAAE,CAAA+H,eAAA,KAEFxG,mBAAmB,IAAIE,KAAK,GAAG,OAAO,GAAG,MAAM,EAAGD,YAAY,GAC3DkB,SAAS,GAAG/D,IAAI,GAAGsE,YAAY,IAAI9B,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAChD8B,YAAA,GAEN;MACE+E,QAAQ,EAAEtF,SAAS;MACnBuF,GAAG,EAAE,CAACtJ,IAAI,GAAG,CAAC,GAAGoE,UAAU,GAAG,CAAC;MAC/B2D,OAAO,EAAEzE,OAAO,CAAC8E,WAAW,CAAC;QAC3BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACX,QAAQ,EAAE,GAAG,GAAGA,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3DuC,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzC,CAAC,CAAsB;MAEvBsB,SAAS,EAAE,CACT;QACEuB,UAAU,EAAEjG,OAAO,CAAC8E,WAAW,CAAC;UAC9BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACX,QAAQ,EAAE,CAAC,CAAC,CAAC;UAC3CuC,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE1G,IAAI,CAAC;QACzC,CAAC;MACH,CAAC;IAEL,CAAC,EACDuH,MAAM,CAAChH,KAAK,EACZkB,SAAS,IAAI8F,MAAM,CAACiC,cAAc,EAClCtC,SAAS,CACT;IACF9F,KAAK,EAAEA,KAAM;IACbO,MAAM,EAAG,GAAEA,MAAO,OAAO;IACzB8H,qBAAqB,EAAErH;EAA2B,GAEjD7B,KACW,CACV,CAAC,EAEN,CAACkC,KAAK,IAKL1D,KAAA,CAAA8I,aAAA,CAACzI,UAAU;IAACiC,KAAK,EAAEkG,MAAM,CAACmC;EAAyB,GACjD3K,KAAA,CAAA8I,aAAA,CAAC9H,YAAY;IACXoC,OAAO,EAAC,YAAY;IACpB8G,aAAa,EAAE,CAAE;IACjB/C,YAAY,EAAEA,YAAa;IAC3BiD,aAAa,EAAE,MAAO;IACtB9H,KAAK,EAAE,CACLkG,MAAM,CAAChH,KAAK,EACZkB,SAAS,IAAI8F,MAAM,CAACiC,cAAc,EAClCtC,SAAS,CACT;IACF9F,KAAK,EAAEA;EAAM,GAEZb,KACW,CACJ,CAEP,CAAC;AAEd,CAAC;AAED,IAAMgH,MAAM,GAAGlI,UAAU,CAACsK,MAAM,CAAC;EAC/BpB,QAAQ,EAAE;IACRrE,MAAM,EAAElE;EACV,CAAC;EACDe,QAAQ,EAAE;IACRkH,SAAS,EAAE;EACb,CAAC;EAEDC,SAAS,EAAE;IACT0B,QAAQ,EAAE,UAAU;IACpBtE,eAAe,EAAE;EACnB,CAAC;EACDoD,YAAY,EAAE;IACZmB,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE;EACZ,CAAC;EACDtB,aAAa,EAAE;IACbP,SAAS,EAAE;EACb,CAAC;EACDR,MAAM,EAAE;IACNQ,SAAS,EAAE;EACb,CAAC;EACDT,QAAQ,EAAE;IACRS,SAAS,EAAE;EACb,CAAC;EACDY,WAAW,EAAE;IACXkB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBJ,QAAQ,EAAE,UAAU;IACpB1F,MAAM,EAAElE,IAAI;IACZ4D,KAAK,EAAE5D;EACT,CAAC;EACDO,KAAK,EAAE;IACLqJ,QAAQ,EAAE;EACZ,CAAC;EACDJ,cAAc,EAAE;IACdS,aAAa,EAAE;EACjB,CAAC;EACDP,wBAAwB,EAAE;IACxBxF,MAAM,EAAE,CAAC;IACT0F,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,eAAe1J,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}