{"ast": null, "code": "import ExpoFontLoader from \"./ExpoFontLoader\";\nexport var loadPromises = {};\nvar cache = {};\nexport function markLoaded(fontFamily) {\n  cache[fontFamily] = true;\n}\nexport function isLoadedInCache(fontFamily) {\n  return fontFamily in cache;\n}\nexport function isLoadedNative(fontFamily) {\n  if (isLoadedInCache(fontFamily)) {\n    return true;\n  } else {\n    var loadedNativeFonts = ExpoFontLoader.getLoadedFonts();\n    if (!(loadedNativeFonts != null && loadedNativeFonts.length)) {\n      return false;\n    }\n    loadedNativeFonts.forEach(function (font) {\n      cache[font] = true;\n    });\n    return fontFamily in cache;\n  }\n}\nexport function purgeFontFamilyFromCache(fontFamily) {\n  delete cache[fontFamily];\n}\nexport function purgeCache() {\n  cache = {};\n}", "map": {"version": 3, "names": ["ExpoFontLoader", "loadPromises", "cache", "markLoaded", "fontFamily", "isLoadedInCache", "isLoadedNative", "loadedNativeFonts", "getLoadedFonts", "length", "for<PERSON>ach", "font", "purgeFontFamilyFromCache", "purge<PERSON>ache"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/expo-font/src/memory.ts"], "sourcesContent": ["import ExpoFontLoader from './ExpoFontLoader';\n\nexport const loadPromises: { [name: string]: Promise<void> } = {};\n\n// cache the value on the js side for fast access to the fonts that are loaded\nlet cache: { [name: string]: boolean } = {};\n\nexport function markLoaded(fontFamily: string) {\n  cache[fontFamily] = true;\n}\n\nexport function isLoadedInCache(fontFamily: string): boolean {\n  return fontFamily in cache;\n}\n\nexport function isLoadedNative(fontFamily: string): boolean {\n  if (isLoadedInCache(fontFamily)) {\n    return true;\n  } else {\n    const loadedNativeFonts: string[] = ExpoFontLoader.getLoadedFonts();\n\n    // NOTE(brentvatne): Bail out here if there are no loaded fonts. This\n    // is functionally equivalent to the behavior below if the returned array\n    // is empty, but this handles improper mocking of `getLoadedFonts`.\n    if (!loadedNativeFonts?.length) {\n      return false;\n    }\n\n    loadedNativeFonts.forEach((font) => {\n      cache[font] = true;\n    });\n    return fontFamily in cache;\n  }\n}\n\nexport function purgeFontFamilyFromCache(fontFamily: string): void {\n  delete cache[fontFamily];\n}\n\nexport function purgeCache(): void {\n  cache = {};\n}\n"], "mappings": "AAAA,OAAOA,cAAc;AAErB,OAAO,IAAMC,YAAY,GAAsC,EAAE;AAGjE,IAAIC,KAAK,GAAgC,EAAE;AAE3C,OAAM,SAAUC,UAAUA,CAACC,UAAkB;EAC3CF,KAAK,CAACE,UAAU,CAAC,GAAG,IAAI;AAC1B;AAEA,OAAM,SAAUC,eAAeA,CAACD,UAAkB;EAChD,OAAOA,UAAU,IAAIF,KAAK;AAC5B;AAEA,OAAM,SAAUI,cAAcA,CAACF,UAAkB;EAC/C,IAAIC,eAAe,CAACD,UAAU,CAAC,EAAE;IAC/B,OAAO,IAAI;EACb,CAAC,MAAM;IACL,IAAMG,iBAAiB,GAAaP,cAAc,CAACQ,cAAc,EAAE;IAKnE,IAAI,EAACD,iBAAiB,YAAjBA,iBAAiB,CAAEE,MAAM,GAAE;MAC9B,OAAO,KAAK;IACd;IAEAF,iBAAiB,CAACG,OAAO,CAAC,UAACC,IAAI,EAAI;MACjCT,KAAK,CAACS,IAAI,CAAC,GAAG,IAAI;IACpB,CAAC,CAAC;IACF,OAAOP,UAAU,IAAIF,KAAK;EAC5B;AACF;AAEA,OAAM,SAAUU,wBAAwBA,CAACR,UAAkB;EACzD,OAAOF,KAAK,CAACE,UAAU,CAAC;AAC1B;AAEA,OAAM,SAAUS,UAAUA,CAAA;EACxBX,KAAK,GAAG,EAAE;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}