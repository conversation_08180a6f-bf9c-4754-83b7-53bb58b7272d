{"ast": null, "code": "function emptyFunction() {}\nexport var BackHandler = {\n  exitApp: emptyFunction,\n  addEventListener: function addEventListener() {\n    return {\n      remove: emptyFunction\n    };\n  },\n  removeEventListener: emptyFunction\n};", "map": {"version": 3, "names": ["emptyFunction", "<PERSON><PERSON><PERSON><PERSON>", "exitApp", "addEventListener", "remove", "removeEventListener"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/utils/BackHandler/BackHandler.tsx"], "sourcesContent": ["function emptyFunction() {}\n\nexport const BackHandler = {\n  exitApp: emptyFunction,\n  addEventListener(): { remove: () => void } {\n    return {\n      remove: emptyFunction,\n    };\n  },\n  removeEventListener: emptyFunction,\n};\n"], "mappings": "AAAA,SAASA,aAAaA,CAAA,EAAG,CAAC;AAE1B,OAAO,IAAMC,WAAW,GAAG;EACzBC,OAAO,EAAEF,aAAa;EACtBG,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAA2B;IACzC,OAAO;MACLC,MAAM,EAAEJ;IACV,CAAC;EACH,CAAC;EACDK,mBAAmB,EAAEL;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}