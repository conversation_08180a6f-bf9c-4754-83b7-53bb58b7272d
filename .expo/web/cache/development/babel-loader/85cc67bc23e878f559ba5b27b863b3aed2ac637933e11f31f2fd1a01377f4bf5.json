{"ast": null, "code": "'use client';\n\nimport SectionList from \"../../vendor/react-native/SectionList\";\nexport default SectionList;", "map": {"version": 3, "names": ["SectionList"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/SectionList/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport SectionList from '../../vendor/react-native/SectionList';\nexport default SectionList;"], "mappings": "AASA,YAAY;;AAEZ,OAAOA,WAAW;AAClB,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}