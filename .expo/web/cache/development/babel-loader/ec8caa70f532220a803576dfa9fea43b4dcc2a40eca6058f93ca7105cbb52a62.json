{"ast": null, "code": "import color from 'color';\nimport { black, white } from \"../../styles/themes/v2/colors\";\nexport var MIN_WIDTH = 112;\nexport var MAX_WIDTH = 280;\nvar getDisabledColor = function getDisabledColor(theme) {\n  if (theme.isV3) {\n    return theme.colors.onSurfaceDisabled;\n  }\n  return color(theme.dark ? white : black).alpha(0.32).rgb().string();\n};\nvar getTitleColor = function getTitleColor(_ref) {\n  var theme = _ref.theme,\n    disabled = _ref.disabled;\n  if (disabled) {\n    return getDisabledColor(theme);\n  }\n  if (theme.isV3) {\n    return theme.colors.onSurface;\n  }\n  return color(theme.colors.text).alpha(0.87).rgb().string();\n};\nvar getIconColor = function getIconColor(_ref2) {\n  var theme = _ref2.theme,\n    disabled = _ref2.disabled;\n  if (disabled) {\n    return getDisabledColor(theme);\n  }\n  if (theme.isV3) {\n    return theme.colors.onSurfaceVariant;\n  }\n  return color(theme.colors.text).alpha(0.54).rgb().string();\n};\nvar getRippleColor = function getRippleColor(_ref3) {\n  var theme = _ref3.theme,\n    customRippleColor = _ref3.customRippleColor;\n  if (customRippleColor) {\n    return customRippleColor;\n  }\n  if (theme.isV3) {\n    return color(theme.colors.onSurfaceVariant).alpha(0.12).rgb().string();\n  }\n  return undefined;\n};\nexport var getMenuItemColor = function getMenuItemColor(_ref4) {\n  var theme = _ref4.theme,\n    disabled = _ref4.disabled,\n    customRippleColor = _ref4.customRippleColor;\n  return {\n    titleColor: getTitleColor({\n      theme: theme,\n      disabled: disabled\n    }),\n    iconColor: getIconColor({\n      theme: theme,\n      disabled: disabled\n    }),\n    rippleColor: getRippleColor({\n      theme: theme,\n      customRippleColor: customRippleColor\n    })\n  };\n};\nexport var getContentMaxWidth = function getContentMaxWidth(_ref5) {\n  var isV3 = _ref5.isV3,\n    iconWidth = _ref5.iconWidth,\n    leadingIcon = _ref5.leadingIcon,\n    trailingIcon = _ref5.trailingIcon;\n  if (isV3) {\n    if (leadingIcon && trailingIcon) {\n      return MAX_WIDTH - (2 * iconWidth + 24);\n    }\n    if (leadingIcon || trailingIcon) {\n      return MAX_WIDTH - (iconWidth + 24);\n    }\n    return MAX_WIDTH - 12;\n  }\n  if (leadingIcon) {\n    return MAX_WIDTH - (iconWidth + 48);\n  }\n  return MAX_WIDTH - 16;\n};", "map": {"version": 3, "names": ["color", "black", "white", "MIN_WIDTH", "MAX_WIDTH", "getDisabledColor", "theme", "isV3", "colors", "onSurfaceDisabled", "dark", "alpha", "rgb", "string", "getTitleColor", "_ref", "disabled", "onSurface", "text", "getIconColor", "_ref2", "onSurfaceVariant", "getRippleColor", "_ref3", "customRippleColor", "undefined", "getMenuItemColor", "_ref4", "titleColor", "iconColor", "rippleColor", "getContentMaxWidth", "_ref5", "iconWidth", "leadingIcon", "trailingIcon"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/react-native-paper/src/components/Menu/utils.ts"], "sourcesContent": ["import type { ColorValue } from 'react-native';\n\nimport color from 'color';\n\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { InternalTheme } from '../../types';\nimport type { IconSource } from '../Icon';\n\nexport const MIN_WIDTH = 112;\nexport const MAX_WIDTH = 280;\n\ntype ContentProps = {\n  isV3: boolean;\n  iconWidth: number;\n  leadingIcon?: IconSource;\n  trailingIcon?: IconSource;\n};\n\ntype ColorProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n  customRippleColor?: ColorValue;\n};\n\nconst getDisabledColor = (theme: InternalTheme) => {\n  if (theme.isV3) {\n    return theme.colors.onSurfaceDisabled;\n  }\n\n  return color(theme.dark ? white : black)\n    .alpha(0.32)\n    .rgb()\n    .string();\n};\n\nconst getTitleColor = ({ theme, disabled }: ColorProps) => {\n  if (disabled) {\n    return getDisabledColor(theme);\n  }\n\n  if (theme.isV3) {\n    return theme.colors.onSurface;\n  }\n\n  return color(theme.colors.text).alpha(0.87).rgb().string();\n};\n\nconst getIconColor = ({ theme, disabled }: ColorProps) => {\n  if (disabled) {\n    return getDisabledColor(theme);\n  }\n\n  if (theme.isV3) {\n    return theme.colors.onSurfaceVariant;\n  }\n\n  return color(theme.colors.text).alpha(0.54).rgb().string();\n};\n\nconst getRippleColor = ({\n  theme,\n  customRippleColor,\n}: Omit<ColorProps, 'disabled'>) => {\n  if (customRippleColor) {\n    return customRippleColor;\n  }\n\n  if (theme.isV3) {\n    return color(theme.colors.onSurfaceVariant).alpha(0.12).rgb().string();\n  }\n\n  return undefined;\n};\n\nexport const getMenuItemColor = ({\n  theme,\n  disabled,\n  customRippleColor,\n}: ColorProps) => {\n  return {\n    titleColor: getTitleColor({ theme, disabled }),\n    iconColor: getIconColor({ theme, disabled }),\n    rippleColor: getRippleColor({ theme, customRippleColor }),\n  };\n};\n\nexport const getContentMaxWidth = ({\n  isV3,\n  iconWidth,\n  leadingIcon,\n  trailingIcon,\n}: ContentProps) => {\n  if (isV3) {\n    if (leadingIcon && trailingIcon) {\n      return MAX_WIDTH - (2 * iconWidth + 24);\n    }\n\n    if (leadingIcon || trailingIcon) {\n      return MAX_WIDTH - (iconWidth + 24);\n    }\n\n    return MAX_WIDTH - 12;\n  }\n\n  if (leadingIcon) {\n    return MAX_WIDTH - (iconWidth + 48);\n  }\n\n  return MAX_WIDTH - 16;\n};\n"], "mappings": "AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,KAAK;AAIrB,OAAO,IAAMC,SAAS,GAAG,GAAG;AAC5B,OAAO,IAAMC,SAAS,GAAG,GAAG;AAe5B,IAAMC,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,KAAoB,EAAK;EACjD,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACC,iBAAiB;EACvC;EAEA,OAAOT,KAAK,CAACM,KAAK,CAACI,IAAI,GAAGR,KAAK,GAAGD,KAAK,CAAC,CACrCU,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb,CAAC;AAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAA,EAAqC;EAAA,IAAlCT,KAAK,GAAwBS,IAAA,CAA7BT,KAAK;IAAEU,QAAA,GAAsBD,IAAA,CAAtBC,QAAA;EAC9B,IAAIA,QAAQ,EAAE;IACZ,OAAOX,gBAAgB,CAACC,KAAK,CAAC;EAChC;EAEA,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACS,SAAS;EAC/B;EAEA,OAAOjB,KAAK,CAACM,KAAK,CAACE,MAAM,CAACU,IAAI,CAAC,CAACP,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,IAAMM,YAAY,GAAG,SAAfA,YAAYA,CAAGC,KAAA,EAAqC;EAAA,IAAlCd,KAAK,GAAwBc,KAAA,CAA7Bd,KAAK;IAAEU,QAAA,GAAsBI,KAAA,CAAtBJ,QAAA;EAC7B,IAAIA,QAAQ,EAAE;IACZ,OAAOX,gBAAgB,CAACC,KAAK,CAAC;EAChC;EAEA,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACa,gBAAgB;EACtC;EAEA,OAAOrB,KAAK,CAACM,KAAK,CAACE,MAAM,CAACU,IAAI,CAAC,CAACP,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,IAAMS,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,KAAA,EAGa;EAAA,IAFlCjB,KAAK,GAEwBiB,KAAA,CAF7BjB,KAAK;IACLkB,iBAAA,GAC6BD,KAAA,CAD7BC,iBAAA;EAEA,IAAIA,iBAAiB,EAAE;IACrB,OAAOA,iBAAiB;EAC1B;EAEA,IAAIlB,KAAK,CAACC,IAAI,EAAE;IACd,OAAOP,KAAK,CAACM,KAAK,CAACE,MAAM,CAACa,gBAAgB,CAAC,CAACV,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACxE;EAEA,OAAOY,SAAS;AAClB,CAAC;AAED,OAAO,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAGC,KAAA,EAId;EAAA,IAHhBrB,KAAK,GAGMqB,KAAA,CAHXrB,KAAK;IACLU,QAAQ,GAEGW,KAAA,CAFXX,QAAQ;IACRQ,iBAAA,GACWG,KAAA,CADXH,iBAAA;EAEA,OAAO;IACLI,UAAU,EAAEd,aAAa,CAAC;MAAER,KAAK,EAALA,KAAK;MAAEU,QAAA,EAAAA;IAAS,CAAC,CAAC;IAC9Ca,SAAS,EAAEV,YAAY,CAAC;MAAEb,KAAK,EAALA,KAAK;MAAEU,QAAA,EAAAA;IAAS,CAAC,CAAC;IAC5Cc,WAAW,EAAER,cAAc,CAAC;MAAEhB,KAAK,EAALA,KAAK;MAAEkB,iBAAA,EAAAA;IAAkB,CAAC;EAC1D,CAAC;AACH,CAAC;AAED,OAAO,IAAMO,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGC,KAAA,EAKd;EAAA,IAJlBzB,IAAI,GAISyB,KAAA,CAJbzB,IAAI;IACJ0B,SAAS,GAGID,KAAA,CAHbC,SAAS;IACTC,WAAW,GAEEF,KAAA,CAFbE,WAAW;IACXC,YAAA,GACaH,KAAA,CADbG,YAAA;EAEA,IAAI5B,IAAI,EAAE;IACR,IAAI2B,WAAW,IAAIC,YAAY,EAAE;MAC/B,OAAO/B,SAAS,IAAI,CAAC,GAAG6B,SAAS,GAAG,EAAE,CAAC;IACzC;IAEA,IAAIC,WAAW,IAAIC,YAAY,EAAE;MAC/B,OAAO/B,SAAS,IAAI6B,SAAS,GAAG,EAAE,CAAC;IACrC;IAEA,OAAO7B,SAAS,GAAG,EAAE;EACvB;EAEA,IAAI8B,WAAW,EAAE;IACf,OAAO9B,SAAS,IAAI6B,SAAS,GAAG,EAAE,CAAC;EACrC;EAEA,OAAO7B,SAAS,GAAG,EAAE;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}