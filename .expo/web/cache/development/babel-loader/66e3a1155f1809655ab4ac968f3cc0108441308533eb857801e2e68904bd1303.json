{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"statusBarHeight\", \"style\", \"dark\", \"mode\", \"elevated\", \"theme\", \"testID\"],\n  _excluded2 = [\"height\", \"elevation\", \"backgroundColor\", \"zIndex\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { Appbar } from \"./Appbar\";\nimport { DEFAULT_APPBAR_HEIGHT, getAppbarBackgroundColor, modeAppbarHeight, getAppbarBorders } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport shadow from \"../../styles/shadow\";\nvar AppbarHeader = function AppbarHeader(_ref) {\n  var statusBarHeight = _ref.statusBarHeight,\n    style = _ref.style,\n    dark = _ref.dark,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? Platform.OS === 'ios' ? 'center-aligned' : 'small' : _ref$mode,\n    _ref$elevated = _ref.elevated,\n    elevated = _ref$elevated === void 0 ? false : _ref$elevated,\n    themeOverrides = _ref.theme,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'appbar-header' : _ref$testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3;\n  var flattenedStyle = StyleSheet.flatten(style);\n  var _ref2 = flattenedStyle || {},\n    _ref2$height = _ref2.height,\n    height = _ref2$height === void 0 ? isV3 ? modeAppbarHeight[mode] : DEFAULT_APPBAR_HEIGHT : _ref2$height,\n    _ref2$elevation = _ref2.elevation,\n    elevation = _ref2$elevation === void 0 ? isV3 ? elevated ? 2 : 0 : 4 : _ref2$elevation,\n    customBackground = _ref2.backgroundColor,\n    _ref2$zIndex = _ref2.zIndex,\n    zIndex = _ref2$zIndex === void 0 ? isV3 && elevated ? 1 : 0 : _ref2$zIndex,\n    restStyle = _objectWithoutProperties(_ref2, _excluded2);\n  var borderRadius = getAppbarBorders(restStyle);\n  var backgroundColor = getAppbarBackgroundColor(theme, elevation, customBackground, elevated);\n  var _useSafeAreaInsets = useSafeAreaInsets(),\n    top = _useSafeAreaInsets.top,\n    left = _useSafeAreaInsets.left,\n    right = _useSafeAreaInsets.right;\n  return React.createElement(View, {\n    testID: `${testID}-root-layer`,\n    style: [{\n      backgroundColor: backgroundColor,\n      zIndex: zIndex,\n      elevation: elevation,\n      paddingTop: statusBarHeight != null ? statusBarHeight : top,\n      paddingHorizontal: Math.max(left, right)\n    }, borderRadius, shadow(elevation)]\n  }, React.createElement(Appbar, _extends({\n    testID: testID,\n    style: [{\n      height: height,\n      backgroundColor: backgroundColor\n    }, styles.appbar, restStyle],\n    dark: dark\n  }, isV3 && {\n    mode: mode\n  }, rest, {\n    theme: theme\n  })));\n};\nAppbarHeader.displayName = 'Appbar.Header';\nvar styles = StyleSheet.create({\n  appbar: {\n    elevation: 0\n  }\n});\nexport default AppbarHeader;\nexport { AppbarHeader };", "map": {"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "useSafeAreaInsets", "Appbar", "DEFAULT_APPBAR_HEIGHT", "getAppbarBackgroundColor", "modeAppbarHeight", "getAppbarBorders", "useInternalTheme", "shadow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "statusBarHeight", "style", "dark", "_ref$mode", "mode", "OS", "_ref$elevated", "elevated", "themeOverrides", "theme", "_ref$testID", "testID", "rest", "_objectWithoutProperties", "_excluded", "isV3", "flattenedStyle", "flatten", "_ref2", "_ref2$height", "height", "_ref2$elevation", "elevation", "customBackground", "backgroundColor", "_ref2$zIndex", "zIndex", "restStyle", "_excluded2", "borderRadius", "_useSafeAreaInsets", "top", "left", "right", "createElement", "paddingTop", "paddingHorizontal", "Math", "max", "_extends", "styles", "appbar", "displayName", "create"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Appbar/AppbarHeader.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  ColorValue,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\n\nimport { Appbar } from './Appbar';\nimport {\n  DEFAULT_APPBAR_HEIGHT,\n  getAppbarBackgroundColor,\n  modeAppbarHeight,\n  getAppbarBorders,\n} from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport shadow from '../../styles/shadow';\nimport type { ThemeProp } from '../../types';\n\nexport type Props = Omit<\n  React.ComponentProps<typeof Appbar>,\n  'safeAreaInsets'\n> & {\n  /**\n   * Whether the background color is a dark color. A dark header will render light text and vice-versa.\n   */\n  dark?: boolean;\n  /**\n   * Extra padding to add at the top of header to account for translucent status bar.\n   * This is automatically handled on iOS >= 11 including iPhone X using `SafeAreaView`.\n   * If you are using Expo, we assume translucent status bar and set a height for status bar automatically.\n   * Pass `0` or a custom value to disable the default behaviour, and customize the height.\n   */\n  statusBarHeight?: number;\n  /**\n   * Content of the header.\n   */\n  children: React.ReactNode;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Mode of the Appbar.\n   * - `small` - Appbar with default height (64).\n   * - `medium` - Appbar with medium height (112).\n   * - `large` - Appbar with large height (152).\n   * - `center-aligned` - Appbar with default height and center-aligned title.\n   */\n  mode?: 'small' | 'medium' | 'large' | 'center-aligned';\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Whether Appbar background should have the elevation along with primary color pigment.\n   */\n  elevated?: boolean;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n};\n\n/**\n * A component to use as a header at the top of the screen.\n * It can contain the screen title, controls such as navigation buttons, menu button etc.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Appbar } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const _goBack = () => console.log('Went back');\n *\n *   const _handleSearch = () => console.log('Searching');\n *\n *   const _handleMore = () => console.log('Shown more');\n *\n *   return (\n *     <Appbar.Header>\n *       <Appbar.BackAction onPress={_goBack} />\n *       <Appbar.Content title=\"Title\" />\n *       <Appbar.Action icon=\"magnify\" onPress={_handleSearch} />\n *       <Appbar.Action icon=\"dots-vertical\" onPress={_handleMore} />\n *     </Appbar.Header>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst AppbarHeader = ({\n  // Don't use default props since we check it to know whether we should use SafeAreaView\n  statusBarHeight,\n  style,\n  dark,\n  mode = Platform.OS === 'ios' ? 'center-aligned' : 'small',\n  elevated = false,\n  theme: themeOverrides,\n  testID = 'appbar-header',\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { isV3 } = theme;\n\n  const flattenedStyle = StyleSheet.flatten(style);\n  const {\n    height = isV3 ? modeAppbarHeight[mode] : DEFAULT_APPBAR_HEIGHT,\n    elevation = isV3 ? (elevated ? 2 : 0) : 4,\n    backgroundColor: customBackground,\n    zIndex = isV3 && elevated ? 1 : 0,\n    ...restStyle\n  } = (flattenedStyle || {}) as Exclude<typeof flattenedStyle, number> & {\n    height?: number;\n    elevation?: number;\n    backgroundColor?: ColorValue;\n    zIndex?: number;\n  };\n\n  const borderRadius = getAppbarBorders(restStyle);\n\n  const backgroundColor = getAppbarBackgroundColor(\n    theme,\n    elevation,\n    customBackground,\n    elevated\n  );\n\n  const { top, left, right } = useSafeAreaInsets();\n\n  return (\n    <View\n      testID={`${testID}-root-layer`}\n      style={[\n        {\n          backgroundColor,\n          zIndex,\n          elevation,\n          paddingTop: statusBarHeight ?? top,\n          paddingHorizontal: Math.max(left, right),\n        },\n        borderRadius,\n        shadow(elevation) as ViewStyle,\n      ]}\n    >\n      <Appbar\n        testID={testID}\n        style={[{ height, backgroundColor }, styles.appbar, restStyle]}\n        dark={dark}\n        {...(isV3 && {\n          mode,\n        })}\n        {...rest}\n        theme={theme}\n      />\n    </View>\n  );\n};\n\nAppbarHeader.displayName = 'Appbar.Header';\n\nconst styles = StyleSheet.create({\n  appbar: {\n    elevation: 0,\n  },\n});\n\nexport default AppbarHeader;\n\n// @component-docs ignore-next-line\nexport { AppbarHeader };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAW9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,SAASC,MAAM;AACf,SACEC,qBAAqB,EACrBC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB;AAElB,SAASC,gBAAgB;AACzB,OAAOC,MAAM;AAyEb,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAGC,IAAA,EAUR;EAAA,IARXC,eAAe,GAQTD,IAAA,CARNC,eAAe;IACfC,KAAK,GAOCF,IAAA,CAPNE,KAAK;IACLC,IAAI,GAMEH,IAAA,CANNG,IAAI;IAAAC,SAAA,GAMEJ,IAAA,CALNK,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAGhB,QAAQ,CAACkB,EAAE,KAAK,KAAK,GAAG,gBAAgB,GAAG,OAAO,GAAAF,SAAA;IAAAG,aAAA,GAKnDP,IAAA,CAJNQ,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IACTE,cAAc,GAGfT,IAAA,CAHNU,KAAK;IAAAC,WAAA,GAGCX,IAAA,CAFNY,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,eAAe,GAAAA,WAAA;IACrBE,IAAA,GAAAC,wBAAA,CACGd,IAAA,EAAAe,SAAA;EACN,IAAML,KAAK,GAAGb,gBAAgB,CAACY,cAAc,CAAC;EAC9C,IAAQO,IAAA,GAASN,KAAK,CAAdM,IAAA;EAER,IAAMC,cAAc,GAAG5B,UAAU,CAAC6B,OAAO,CAAChB,KAAK,CAAC;EAChD,IAAAiB,KAAA,GAMKF,cAAc,IAAI,CAAC,CAKvB;IAAAG,YAAA,GAAAD,KAAA,CAVCE,MAAM;IAANA,MAAM,GAAAD,YAAA,cAAGJ,IAAI,GAAGrB,gBAAgB,CAACU,IAAI,CAAC,GAAGZ,qBAAqB,GAAA2B,YAAA;IAAAE,eAAA,GAAAH,KAAA,CAC9DI,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAGN,IAAI,GAAIR,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAI,CAAC,GAAAc,eAAA;IACxBE,gBAAgB,GAAAL,KAAA,CAAjCM,eAAe;IAAAC,YAAA,GAAAP,KAAA,CACfQ,MAAM;IAANA,MAAM,GAAAD,YAAA,cAAGV,IAAI,IAAIR,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAAkB,YAAA;IAC9BE,SAAA,GAAAd,wBAAA,CAAAK,KAAA,EAAAU,UAAA;EAQL,IAAMC,YAAY,GAAGlC,gBAAgB,CAACgC,SAAS,CAAC;EAEhD,IAAMH,eAAe,GAAG/B,wBAAwB,CAC9CgB,KAAK,EACLa,SAAS,EACTC,gBAAgB,EAChBhB,QACF,CAAC;EAED,IAAAuB,kBAAA,GAA6BxC,iBAAiB,CAAC,CAAC;IAAxCyC,GAAG,GAAAD,kBAAA,CAAHC,GAAG;IAAEC,IAAI,GAAAF,kBAAA,CAAJE,IAAI;IAAEC,KAAA,GAAAH,kBAAA,CAAAG,KAAA;EAEnB,OACE/C,KAAA,CAAAgD,aAAA,CAAC7C,IAAI;IACHsB,MAAM,EAAG,GAAEA,MAAO,aAAa;IAC/BV,KAAK,EAAE,CACL;MACEuB,eAAe,EAAfA,eAAe;MACfE,MAAM,EAANA,MAAM;MACNJ,SAAS,EAATA,SAAS;MACTa,UAAU,EAAEnC,eAAe,WAAfA,eAAe,GAAI+B,GAAG;MAClCK,iBAAiB,EAAEC,IAAI,CAACC,GAAG,CAACN,IAAI,EAAEC,KAAK;IACzC,CAAC,EACDJ,YAAY,EACZhC,MAAM,CAACyB,SAAS,CAAC;EACjB,GAEFpC,KAAA,CAAAgD,aAAA,CAAC3C,MAAM,EAAAgD,QAAA;IACL5B,MAAM,EAAEA,MAAO;IACfV,KAAK,EAAE,CAAC;MAAEmB,MAAM,EAANA,MAAM;MAAEI,eAAA,EAAAA;IAAgB,CAAC,EAAEgB,MAAM,CAACC,MAAM,EAAEd,SAAS,CAAE;IAC/DzB,IAAI,EAAEA;EAAK,GACNa,IAAI,IAAI;IACXX,IAAA,EAAAA;EACF,CAAC,EACGQ,IAAI;IACRH,KAAK,EAAEA;EAAM,EACd,CACG,CAAC;AAEX,CAAC;AAEDX,YAAY,CAAC4C,WAAW,GAAG,eAAe;AAE1C,IAAMF,MAAM,GAAGpD,UAAU,CAACuD,MAAM,CAAC;EAC/BF,MAAM,EAAE;IACNnB,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAexB,YAAY;AAG3B,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}