{"ast": null, "code": "var safeIsNaN = Number.isNaN || function ponyfill(value) {\n  return typeof value === 'number' && value !== value;\n};\nfunction isEqual(first, second) {\n  if (first === second) {\n    return true;\n  }\n  if (safeIsNaN(first) && safeIsNaN(second)) {\n    return true;\n  }\n  return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n  for (var i = 0; i < newInputs.length; i++) {\n    if (!isEqual(newInputs[i], lastInputs[i])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction memoizeOne(resultFn, isEqual) {\n  if (isEqual === void 0) {\n    isEqual = areInputsEqual;\n  }\n  var cache = null;\n  function memoized() {\n    var newArgs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      newArgs[_i] = arguments[_i];\n    }\n    if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n      return cache.lastResult;\n    }\n    var lastResult = resultFn.apply(this, newArgs);\n    cache = {\n      lastResult: lastResult,\n      lastArgs: newArgs,\n      lastThis: this\n    };\n    return lastResult;\n  }\n  memoized.clear = function clear() {\n    cache = null;\n  };\n  return memoized;\n}\nexport { memoizeOne as default };", "map": {"version": 3, "names": ["safeIsNaN", "Number", "isNaN", "ponyfill", "value", "isEqual", "first", "second", "areInputsEqual", "newInputs", "lastInputs", "length", "i", "memoizeOne", "resultFn", "cache", "memoized", "newArgs", "_i", "arguments", "lastThis", "lastArgs", "lastResult", "apply", "clear", "default"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/node_modules/memoize-one/dist/memoize-one.esm.js"], "sourcesContent": ["var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var cache = null;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n            return cache.lastResult;\n        }\n        var lastResult = resultFn.apply(this, newArgs);\n        cache = {\n            lastResult: lastResult,\n            lastArgs: newArgs,\n            lastThis: this,\n        };\n        return lastResult;\n    }\n    memoized.clear = function clear() {\n        cache = null;\n    };\n    return memoized;\n}\n\nexport { memoizeOne as default };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,MAAM,CAACC,KAAK,IACxB,SAASC,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAKA,KAAK;AACvD,CAAC;AACL,SAASC,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC5B,IAAID,KAAK,KAAKC,MAAM,EAAE;IAClB,OAAO,IAAI;EACf;EACA,IAAIP,SAAS,CAACM,KAAK,CAAC,IAAIN,SAAS,CAACO,MAAM,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,SAASC,cAAcA,CAACC,SAAS,EAAEC,UAAU,EAAE;EAC3C,IAAID,SAAS,CAACE,MAAM,KAAKD,UAAU,CAACC,MAAM,EAAE;IACxC,OAAO,KAAK;EAChB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACvC,IAAI,CAACP,OAAO,CAACI,SAAS,CAACG,CAAC,CAAC,EAAEF,UAAU,CAACE,CAAC,CAAC,CAAC,EAAE;MACvC,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AAEA,SAASC,UAAUA,CAACC,QAAQ,EAAET,OAAO,EAAE;EACnC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAGG,cAAc;EAAE;EACpD,IAAIO,KAAK,GAAG,IAAI;EAChB,SAASC,QAAQA,CAAA,EAAG;IAChB,IAAIC,OAAO,GAAG,EAAE;IAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACR,MAAM,EAAEO,EAAE,EAAE,EAAE;MAC1CD,OAAO,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC/B;IACA,IAAIH,KAAK,IAAIA,KAAK,CAACK,QAAQ,KAAK,IAAI,IAAIf,OAAO,CAACY,OAAO,EAAEF,KAAK,CAACM,QAAQ,CAAC,EAAE;MACtE,OAAON,KAAK,CAACO,UAAU;IAC3B;IACA,IAAIA,UAAU,GAAGR,QAAQ,CAACS,KAAK,CAAC,IAAI,EAAEN,OAAO,CAAC;IAC9CF,KAAK,GAAG;MACJO,UAAU,EAAEA,UAAU;MACtBD,QAAQ,EAAEJ,OAAO;MACjBG,QAAQ,EAAE;IACd,CAAC;IACD,OAAOE,UAAU;EACrB;EACAN,QAAQ,CAACQ,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9BT,KAAK,GAAG,IAAI;EAChB,CAAC;EACD,OAAOC,QAAQ;AACnB;AAEA,SAASH,UAAU,IAAIY,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}