{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"textStyle\", \"style\", \"numeric\", \"maxFontSizeMultiplier\", \"testID\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar DataTableCell = function DataTableCell(_ref) {\n  var children = _ref.children,\n    textStyle = _ref.textStyle,\n    style = _ref.style,\n    numeric = _ref.numeric,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return React.createElement(TouchableRipple, _extends({}, rest, {\n    testID: testID,\n    style: [styles.container, numeric && styles.right, style]\n  }), React.createElement(CellContent, {\n    textStyle: textStyle,\n    testID: testID,\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, children));\n};\nvar CellContent = function CellContent(_ref2) {\n  var children = _ref2.children,\n    textStyle = _ref2.textStyle,\n    maxFontSizeMultiplier = _ref2.maxFontSizeMultiplier,\n    testID = _ref2.testID;\n  if (React.isValidElement(children)) {\n    return children;\n  }\n  return React.createElement(Text, {\n    style: textStyle,\n    numberOfLines: 1,\n    maxFontSizeMultiplier: maxFontSizeMultiplier,\n    testID: `${testID}-text-container`\n  }, children);\n};\nDataTableCell.displayName = 'DataTable.Cell';\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'center'\n  },\n  right: {\n    justifyContent: 'flex-end'\n  }\n});\nexport default DataTableCell;", "map": {"version": 3, "names": ["React", "StyleSheet", "TouchableRipple", "Text", "DataTableCell", "_ref", "children", "textStyle", "style", "numeric", "maxFontSizeMultiplier", "testID", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_extends", "styles", "container", "right", "CellContent", "_ref2", "isValidElement", "numberOfLines", "displayName", "create", "flex", "flexDirection", "alignItems", "justifyContent"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/DataTable/DataTableCell.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleSheet,\n  StyleProp,\n  ViewStyle,\n  TextStyle,\n  GestureResponderEvent,\n} from 'react-native';\n\nimport type { $RemoveChildren } from '../../types';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = $RemoveChildren<typeof TouchableRipple> & {\n  /**\n   * Content of the `DataTableCell`.\n   */\n  children: React.ReactNode;\n  /**\n   * Align the text to the right. Generally monetary or number fields are aligned to right.\n   */\n  numeric?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Text content style of the `DataTableCell`.\n   */\n  textStyle?: StyleProp<TextStyle>;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n/**\n * A component to show a single cell inside of a table.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { DataTable } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *      <DataTable.Row>\n *        <DataTable.Cell numeric>1</DataTable.Cell>\n *        <DataTable.Cell numeric>2</DataTable.Cell>\n *        <DataTable.Cell numeric>3</DataTable.Cell>\n *        <DataTable.Cell numeric>4</DataTable.Cell>\n *      </DataTable.Row>\n * );\n *\n * export default MyComponent;\n * ```\n *\n * If you want to support multiline text, please use View instead, as multiline text doesn't comply with\n * MD Guidelines (https://github.com/callstack/react-native-paper/issues/2381).\n *\n * @extends TouchableRipple props https://callstack.github.io/react-native-paper/docs/components/TouchableRipple\n */\nconst DataTableCell = ({\n  children,\n  textStyle,\n  style,\n  numeric,\n  maxFontSizeMultiplier,\n  testID,\n  ...rest\n}: Props) => {\n  return (\n    <TouchableRipple\n      {...rest}\n      testID={testID}\n      style={[styles.container, numeric && styles.right, style]}\n    >\n      <CellContent\n        textStyle={textStyle}\n        testID={testID}\n        maxFontSizeMultiplier={maxFontSizeMultiplier}\n      >\n        {children}\n      </CellContent>\n    </TouchableRipple>\n  );\n};\n\nconst CellContent = ({\n  children,\n  textStyle,\n  maxFontSizeMultiplier,\n  testID,\n}: Pick<\n  Props,\n  'children' | 'textStyle' | 'testID' | 'maxFontSizeMultiplier'\n>) => {\n  if (React.isValidElement(children)) {\n    return children;\n  }\n\n  return (\n    <Text\n      style={textStyle}\n      numberOfLines={1}\n      maxFontSizeMultiplier={maxFontSizeMultiplier}\n      testID={`${testID}-text-container`}\n    >\n      {children}\n    </Text>\n  );\n};\n\nDataTableCell.displayName = 'DataTable.Cell';\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n\n  right: {\n    justifyContent: 'flex-end',\n  },\n});\n\nexport default DataTableCell;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAU9B,OAAOC,eAAe;AACtB,OAAOC,IAAI;AAuDX,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAA,EAQT;EAAA,IAPXC,QAAQ,GAOFD,IAAA,CAPNC,QAAQ;IACRC,SAAS,GAMHF,IAAA,CANNE,SAAS;IACTC,KAAK,GAKCH,IAAA,CALNG,KAAK;IACLC,OAAO,GAIDJ,IAAA,CAJNI,OAAO;IACPC,qBAAqB,GAGfL,IAAA,CAHNK,qBAAqB;IACrBC,MAAM,GAEAN,IAAA,CAFNM,MAAM;IACHC,IAAA,GAAAC,wBAAA,CACGR,IAAA,EAAAS,SAAA;EACN,OACEd,KAAA,CAAAe,aAAA,CAACb,eAAe,EAAAc,QAAA,KACVJ,IAAI;IACRD,MAAM,EAAEA,MAAO;IACfH,KAAK,EAAE,CAACS,MAAM,CAACC,SAAS,EAAET,OAAO,IAAIQ,MAAM,CAACE,KAAK,EAAEX,KAAK;EAAE,IAE1DR,KAAA,CAAAe,aAAA,CAACK,WAAW;IACVb,SAAS,EAAEA,SAAU;IACrBI,MAAM,EAAEA,MAAO;IACfD,qBAAqB,EAAEA;EAAsB,GAE5CJ,QACU,CACE,CAAC;AAEtB,CAAC;AAED,IAAMc,WAAW,GAAG,SAAdA,WAAWA,CAAGC,KAAA,EAQd;EAAA,IAPJf,QAAQ,GAOTe,KAAA,CAPCf,QAAQ;IACRC,SAAS,GAMVc,KAAA,CANCd,SAAS;IACTG,qBAAqB,GAKtBW,KAAA,CALCX,qBAAqB;IACrBC,MAAA,GAIDU,KAAA,CAJCV,MAAA;EAKA,IAAIX,KAAK,CAACsB,cAAc,CAAChB,QAAQ,CAAC,EAAE;IAClC,OAAOA,QAAQ;EACjB;EAEA,OACEN,KAAA,CAAAe,aAAA,CAACZ,IAAI;IACHK,KAAK,EAAED,SAAU;IACjBgB,aAAa,EAAE,CAAE;IACjBb,qBAAqB,EAAEA,qBAAsB;IAC7CC,MAAM,EAAG,GAAEA,MAAO;EAAiB,GAElCL,QACG,CAAC;AAEX,CAAC;AAEDF,aAAa,CAACoB,WAAW,GAAG,gBAAgB;AAE5C,IAAMP,MAAM,GAAGhB,UAAU,CAACwB,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EAEDT,KAAK,EAAE;IACLU,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAezB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}