{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar PortalManager = function (_React$PureComponent) {\n  function PortalManager() {\n    var _this;\n    _classCallCheck(this, PortalManager);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, PortalManager, [].concat(args));\n    _this.state = {\n      portals: []\n    };\n    _this.mount = function (key, children) {\n      _this.setState(function (state) {\n        return {\n          portals: [].concat(_toConsumableArray(state.portals), [{\n            key: key,\n            children: children\n          }])\n        };\n      });\n    };\n    _this.update = function (key, children) {\n      return _this.setState(function (state) {\n        return {\n          portals: state.portals.map(function (item) {\n            if (item.key === key) {\n              return _objectSpread(_objectSpread({}, item), {}, {\n                children: children\n              });\n            }\n            return item;\n          })\n        };\n      });\n    };\n    _this.unmount = function (key) {\n      return _this.setState(function (state) {\n        return {\n          portals: state.portals.filter(function (item) {\n            return item.key !== key;\n          })\n        };\n      });\n    };\n    return _this;\n  }\n  _inherits(PortalManager, _React$PureComponent);\n  return _createClass(PortalManager, [{\n    key: \"render\",\n    value: function render() {\n      return this.state.portals.map(function (_ref) {\n        var key = _ref.key,\n          children = _ref.children;\n        return React.createElement(View, {\n          key: key,\n          collapsable: false,\n          pointerEvents: \"box-none\",\n          style: StyleSheet.absoluteFill\n        }, children);\n      });\n    }\n  }]);\n}(React.PureComponent);\nexport { PortalManager as default };", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "PortalManager", "_React$PureComponent", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper", "concat", "state", "portals", "mount", "key", "children", "setState", "_toConsumableArray", "update", "map", "item", "_objectSpread", "unmount", "filter", "_inherits", "_createClass", "value", "render", "_ref", "createElement", "collapsable", "pointerEvents", "style", "absoluteFill", "PureComponent", "default"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Portal/PortalManager.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { View, StyleSheet } from 'react-native';\n\ntype State = {\n  portals: Array<{\n    key: number;\n    children: React.ReactNode;\n  }>;\n};\n\n/**\n * Portal host is the component which actually renders all Portals.\n */\nexport default class PortalManager extends React.PureComponent<{}, State> {\n  state: State = {\n    portals: [],\n  };\n\n  mount = (key: number, children: React.ReactNode) => {\n    this.setState((state) => ({\n      portals: [...state.portals, { key, children }],\n    }));\n  };\n\n  update = (key: number, children: React.ReactNode) =>\n    this.setState((state) => ({\n      portals: state.portals.map((item) => {\n        if (item.key === key) {\n          return { ...item, children };\n        }\n        return item;\n      }),\n    }));\n\n  unmount = (key: number) =>\n    this.setState((state) => ({\n      portals: state.portals.filter((item) => item.key !== key),\n    }));\n\n  render() {\n    return this.state.portals.map(({ key, children }) => (\n      <View\n        key={key}\n        collapsable={\n          false /* Need collapsable=false here to clip the elevations, otherwise they appear above sibling components */\n        }\n        pointerEvents=\"box-none\"\n        style={StyleSheet.absoluteFill}\n      >\n        {children}\n      </View>\n    ));\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,IAaTC,aAAa,aAAAC,oBAAA;EAAA,SAAAD,cAAA;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,aAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAQ,UAAA,OAAAV,aAAA,KAAAW,MAAA,CAAAJ,IAAA;IAAAL,KAAA,CAChCU,KAAK,GAAU;MACbC,OAAO,EAAE;IACX,CAAC;IAAAX,KAAA,CAEDY,KAAK,GAAG,UAACC,GAAW,EAAEC,QAAyB,EAAK;MAClDd,KAAA,CAAKe,QAAQ,CAAE,UAAAL,KAAK;QAAA,OAAM;UACxBC,OAAO,KAAAF,MAAA,CAAAO,kBAAA,CAAMN,KAAK,CAACC,OAAO,IAAE;YAAEE,GAAG,EAAHA,GAAG;YAAEC,QAAA,EAAAA;UAAS,CAAC;QAC/C,CAAC;MAAA,CAAC,CAAC;IACL,CAAC;IAAAd,KAAA,CAEDiB,MAAM,GAAG,UAACJ,GAAW,EAAEC,QAAyB;MAAA,OAC9Cd,KAAA,CAAKe,QAAQ,CAAE,UAAAL,KAAK;QAAA,OAAM;UACxBC,OAAO,EAAED,KAAK,CAACC,OAAO,CAACO,GAAG,CAAE,UAAAC,IAAI,EAAK;YACnC,IAAIA,IAAI,CAACN,GAAG,KAAKA,GAAG,EAAE;cACpB,OAAAO,aAAA,CAAAA,aAAA,KAAYD,IAAI;gBAAEL,QAAA,EAAAA;cAAA;YACpB;YACA,OAAOK,IAAI;UACb,CAAC;QACH,CAAC;MAAA,CAAC,CAAC;IAAA;IAAAnB,KAAA,CAELqB,OAAO,GAAI,UAAAR,GAAW;MAAA,OACpBb,KAAA,CAAKe,QAAQ,CAAE,UAAAL,KAAK;QAAA,OAAM;UACxBC,OAAO,EAAED,KAAK,CAACC,OAAO,CAACW,MAAM,CAAE,UAAAH,IAAI;YAAA,OAAKA,IAAI,CAACN,GAAG,KAAKA,GAAG;UAAA;QAC1D,CAAC;MAAA,CAAC,CAAC;IAAA;IAAA,OAAAb,KAAA;EAAA;EAAAuB,SAAA,CAAAzB,aAAA,EAAAC,oBAAA;EAAA,OAAAyB,YAAA,CAAA1B,aAAA;IAAAe,GAAA;IAAAY,KAAA,EAEL,SAAAC,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAAChB,KAAK,CAACC,OAAO,CAACO,GAAG,CAAC,UAAAS,IAAA;QAAA,IAAGd,GAAG,GAAYc,IAAA,CAAfd,GAAG;UAAEC,QAAA,GAAUa,IAAA,CAAVb,QAAA;QAAU,OAC9CnB,KAAA,CAAAiC,aAAA,CAAChC,IAAI;UACHiB,GAAG,EAAEA,GAAI;UACTgB,WAAW,EACT,KAAK;UAEPC,aAAa,EAAC,UAAU;UACxBC,KAAK,EAAElC,UAAU,CAACmC;QAAa,GAE9BlB,QACG,CAAC;MAAA,CACR,CAAC;IACJ;EAAA;AAAA,EAvCyCnB,KAAK,CAACsC,aAAa;AAAA,SAAzCnC,aAAa,IAAAoC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}