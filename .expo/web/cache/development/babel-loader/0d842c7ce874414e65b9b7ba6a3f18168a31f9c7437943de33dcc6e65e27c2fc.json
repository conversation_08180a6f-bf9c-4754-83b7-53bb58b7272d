{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"icon\", \"children\", \"actions\", \"contentStyle\", \"elevation\", \"style\", \"theme\", \"onShowAnimationFinished\", \"onHideAnimationFinished\", \"maxFontSizeMultiplier\"],\n  _excluded2 = [\"label\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport useLatestCallback from 'use-latest-callback';\nimport Button from \"./Button/Button\";\nimport Icon from \"./Icon\";\nimport Surface from \"./Surface\";\nimport Text from \"./Typography/Text\";\nimport { useInternalTheme } from \"../core/theming\";\nvar DEFAULT_MAX_WIDTH = 960;\nvar Banner = function Banner(_ref) {\n  var visible = _ref.visible,\n    icon = _ref.icon,\n    children = _ref.children,\n    _ref$actions = _ref.actions,\n    actions = _ref$actions === void 0 ? [] : _ref$actions,\n    contentStyle = _ref.contentStyle,\n    _ref$elevation = _ref.elevation,\n    elevation = _ref$elevation === void 0 ? 1 : _ref$elevation,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    _ref$onShowAnimationF = _ref.onShowAnimationFinished,\n    onShowAnimationFinished = _ref$onShowAnimationF === void 0 ? function () {} : _ref$onShowAnimationF,\n    _ref$onHideAnimationF = _ref.onHideAnimationFinished,\n    onHideAnimationFinished = _ref$onHideAnimationF === void 0 ? function () {} : _ref$onHideAnimationF,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(visible ? 1 : 0)),\n    position = _React$useRef.current;\n  var _React$useState = React.useState({\n      height: 0,\n      measured: false\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    layout = _React$useState2[0],\n    setLayout = _React$useState2[1];\n  var showCallback = useLatestCallback(onShowAnimationFinished);\n  var hideCallback = useLatestCallback(onHideAnimationFinished);\n  var scale = theme.animation.scale;\n  var opacity = position.interpolate({\n    inputRange: [0, 0.1, 1],\n    outputRange: [0, 1, 1]\n  });\n  React.useEffect(function () {\n    if (visible) {\n      Animated.timing(position, {\n        duration: 250 * scale,\n        toValue: 1,\n        useNativeDriver: false\n      }).start(showCallback);\n    } else {\n      Animated.timing(position, {\n        duration: 200 * scale,\n        toValue: 0,\n        useNativeDriver: false\n      }).start(hideCallback);\n    }\n  }, [visible, position, scale]);\n  var handleLayout = function handleLayout(_ref2) {\n    var nativeEvent = _ref2.nativeEvent;\n    var height = nativeEvent.layout.height;\n    setLayout({\n      height: height,\n      measured: true\n    });\n  };\n  var height = Animated.multiply(position, layout.height);\n  var translateY = Animated.multiply(Animated.add(position, -1), layout.height);\n  return React.createElement(Surface, _extends({}, rest, {\n    style: [!theme.isV3 && styles.elevation, {\n      opacity: opacity\n    }, style],\n    theme: theme\n  }, theme.isV3 && {\n    elevation: elevation\n  }), React.createElement(View, {\n    style: [styles.wrapper, contentStyle]\n  }, React.createElement(Animated.View, {\n    style: {\n      height: height\n    }\n  }), React.createElement(Animated.View, {\n    onLayout: handleLayout,\n    style: [layout.measured || !visible ? [styles.absolute, {\n      transform: [{\n        translateY: translateY\n      }]\n    }] : null, !layout.measured && !visible ? styles.transparent : null]\n  }, React.createElement(View, {\n    style: styles.content\n  }, icon ? React.createElement(View, {\n    style: styles.icon\n  }, React.createElement(Icon, {\n    source: icon,\n    size: 40\n  })) : null, React.createElement(Text, {\n    style: [styles.message, {\n      color: theme.isV3 ? theme.colors.onSurface : theme.colors.text\n    }],\n    accessibilityLiveRegion: visible ? 'polite' : 'none',\n    accessibilityRole: \"alert\",\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, children)), React.createElement(View, {\n    style: styles.actions\n  }, actions.map(function (_ref3, i) {\n    var _theme$colors;\n    var label = _ref3.label,\n      others = _objectWithoutProperties(_ref3, _excluded2);\n    return React.createElement(Button, _extends({\n      key: i,\n      compact: true,\n      mode: \"text\",\n      style: styles.button,\n      textColor: (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.primary,\n      theme: theme\n    }, others), label);\n  })))));\n};\nvar styles = StyleSheet.create({\n  wrapper: {\n    overflow: 'hidden',\n    alignSelf: 'center',\n    width: '100%',\n    maxWidth: DEFAULT_MAX_WIDTH\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    width: '100%'\n  },\n  content: {\n    flexDirection: 'row',\n    justifyContent: 'flex-start',\n    marginHorizontal: 8,\n    marginTop: 16,\n    marginBottom: 0\n  },\n  icon: {\n    margin: 8\n  },\n  message: {\n    flex: 1,\n    margin: 8\n  },\n  actions: {\n    flexDirection: 'row',\n    justifyContent: 'flex-end',\n    margin: 4\n  },\n  button: {\n    margin: 4\n  },\n  elevation: {\n    elevation: 1\n  },\n  transparent: {\n    opacity: 0\n  }\n});\nexport default Banner;", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "View", "useLatestCallback", "<PERSON><PERSON>", "Icon", "Surface", "Text", "useInternalTheme", "DEFAULT_MAX_WIDTH", "Banner", "_ref", "visible", "icon", "children", "_ref$actions", "actions", "contentStyle", "_ref$elevation", "elevation", "style", "themeOverrides", "theme", "_ref$onShowAnimationF", "onShowAnimationFinished", "_ref$onHideAnimationF", "onHideAnimationFinished", "maxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "_React$useRef", "useRef", "Value", "position", "current", "_React$useState", "useState", "height", "measured", "_React$useState2", "_slicedToArray", "layout", "setLayout", "showCallback", "hide<PERSON>allback", "scale", "animation", "opacity", "interpolate", "inputRange", "outputRange", "useEffect", "timing", "duration", "toValue", "useNativeDriver", "start", "handleLayout", "_ref2", "nativeEvent", "multiply", "translateY", "add", "createElement", "_extends", "isV3", "styles", "wrapper", "onLayout", "absolute", "transform", "transparent", "content", "source", "size", "message", "color", "colors", "onSurface", "text", "accessibilityLiveRegion", "accessibilityRole", "map", "_ref3", "i", "_theme$colors", "label", "others", "_excluded2", "key", "compact", "mode", "button", "textColor", "primary", "create", "overflow", "alignSelf", "width", "max<PERSON><PERSON><PERSON>", "top", "flexDirection", "justifyContent", "marginHorizontal", "marginTop", "marginBottom", "margin", "flex"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Banner.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\nimport type { LayoutChangeEvent } from 'react-native';\n\nimport useLatestCallback from 'use-latest-callback';\n\nimport Button from './Button/Button';\nimport Icon, { IconSource } from './Icon';\nimport Surface from './Surface';\nimport Text from './Typography/Text';\nimport { useInternalTheme } from '../core/theming';\nimport type { $Omit, $RemoveChildren, ThemeProp } from '../types';\n\nconst DEFAULT_MAX_WIDTH = 960;\n\nexport type Props = $Omit<$RemoveChildren<typeof Surface>, 'mode'> & {\n  /**\n   * Whether banner is currently visible.\n   */\n  visible: boolean;\n  /**\n   * Content that will be displayed inside banner.\n   */\n  children: React.ReactNode;\n  /**\n   * Icon to display for the `Banner`. Can be an image.\n   */\n  icon?: IconSource;\n  /**\n   * Action items to shown in the banner.\n   * An action item should contain the following properties:\n   *\n   * - `label`: label of the action button (required)\n   * - `onPress`: callback that is called when button is pressed (required)\n   *\n   * To customize button you can pass other props that button component takes.\n   */\n  actions?: Array<\n    {\n      label: string;\n    } & $RemoveChildren<typeof Button>\n  >;\n  /**\n   * Style of banner's inner content.\n   * Use this prop to apply custom width for wide layouts.\n   */\n  contentStyle?: StyleProp<ViewStyle>;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Changes Banner shadow and background on iOS and Android.\n   */\n  elevation?: 0 | 1 | 2 | 3 | 4 | 5 | Animated.Value;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  ref?: React.RefObject<View>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * @optional\n   * Optional callback that will be called after the opening animation finished running normally\n   */\n  onShowAnimationFinished?: Animated.EndCallback;\n  /**\n   * @optional\n   * Optional callback that will be called after the closing animation finished running normally\n   */\n  onHideAnimationFinished?: Animated.EndCallback;\n};\n\n/**\n * Banner displays a prominent message and related actions.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Image } from 'react-native';\n * import { Banner } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(true);\n *\n *   return (\n *     <Banner\n *       visible={visible}\n *       actions={[\n *         {\n *           label: 'Fix it',\n *           onPress: () => setVisible(false),\n *         },\n *         {\n *           label: 'Learn more',\n *           onPress: () => setVisible(false),\n *         },\n *       ]}\n *       icon={({size}) => (\n *         <Image\n *           source={{\n *             uri: 'https://avatars3.githubusercontent.com/u/17571969?s=400&v=4',\n *           }}\n *           style={{\n *             width: size,\n *             height: size,\n *           }}\n *         />\n *       )}>\n *       There was a problem processing a transaction on your credit card.\n *     </Banner>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst Banner = ({\n  visible,\n  icon,\n  children,\n  actions = [],\n  contentStyle,\n  elevation = 1,\n  style,\n  theme: themeOverrides,\n  onShowAnimationFinished = () => {},\n  onHideAnimationFinished = () => {},\n  maxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { current: position } = React.useRef<Animated.Value>(\n    new Animated.Value(visible ? 1 : 0)\n  );\n  const [layout, setLayout] = React.useState<{\n    height: number;\n    measured: boolean;\n  }>({\n    height: 0,\n    measured: false,\n  });\n\n  const showCallback = useLatestCallback(onShowAnimationFinished);\n  const hideCallback = useLatestCallback(onHideAnimationFinished);\n\n  const { scale } = theme.animation;\n\n  const opacity = position.interpolate({\n    inputRange: [0, 0.1, 1],\n    outputRange: [0, 1, 1],\n  });\n\n  React.useEffect(() => {\n    if (visible) {\n      // show\n      Animated.timing(position, {\n        duration: 250 * scale,\n        toValue: 1,\n        useNativeDriver: false,\n      }).start(showCallback);\n    } else {\n      // hide\n      Animated.timing(position, {\n        duration: 200 * scale,\n        toValue: 0,\n        useNativeDriver: false,\n      }).start(hideCallback);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [visible, position, scale]);\n\n  const handleLayout = ({ nativeEvent }: LayoutChangeEvent) => {\n    const { height } = nativeEvent.layout;\n    setLayout({ height, measured: true });\n  };\n\n  // The banner animation has 2 parts:\n  // 1. Blank spacer element which animates its height to move the content\n  // 2. Actual banner which animates its translateY\n  // In initial render, we position everything normally and measure the height of the banner\n  // Once we have the height, we apply the height to the spacer and switch the banner to position: absolute\n  // We need this because we need to move the content below as if banner's height was being animated\n  // However we can't animated banner's height directly as it'll also resize the content inside\n  const height = Animated.multiply(position, layout.height);\n\n  const translateY = Animated.multiply(\n    Animated.add(position, -1),\n    layout.height\n  );\n  return (\n    <Surface\n      {...rest}\n      style={[!theme.isV3 && styles.elevation, { opacity }, style]}\n      theme={theme}\n      {...(theme.isV3 && { elevation })}\n    >\n      <View style={[styles.wrapper, contentStyle]}>\n        <Animated.View style={{ height }} />\n        <Animated.View\n          onLayout={handleLayout}\n          style={[\n            layout.measured || !visible\n              ? // If we have measured banner's height or it's invisible,\n                // Position it absolutely, the layout will be taken care of the spacer\n                [styles.absolute, { transform: [{ translateY }] }]\n              : // Otherwise position it normally\n                null,\n            !layout.measured && !visible\n              ? // If we haven't measured banner's height yet and it's invisible,\n                // hide it with opacity: 0 so user doesn't see it\n                styles.transparent\n              : null,\n          ]}\n        >\n          <View style={styles.content}>\n            {icon ? (\n              <View style={styles.icon}>\n                <Icon source={icon} size={40} />\n              </View>\n            ) : null}\n            <Text\n              style={[\n                styles.message,\n                {\n                  color: theme.isV3\n                    ? theme.colors.onSurface\n                    : theme.colors.text,\n                },\n              ]}\n              accessibilityLiveRegion={visible ? 'polite' : 'none'}\n              accessibilityRole=\"alert\"\n              maxFontSizeMultiplier={maxFontSizeMultiplier}\n            >\n              {children}\n            </Text>\n          </View>\n          <View style={styles.actions}>\n            {actions.map(({ label, ...others }, i) => (\n              <Button\n                key={/* eslint-disable-line react/no-array-index-key */ i}\n                compact\n                mode=\"text\"\n                style={styles.button}\n                textColor={theme.colors?.primary}\n                theme={theme}\n                {...others}\n              >\n                {label}\n              </Button>\n            ))}\n          </View>\n        </Animated.View>\n      </View>\n    </Surface>\n  );\n};\n\nconst styles = StyleSheet.create({\n  wrapper: {\n    overflow: 'hidden',\n    alignSelf: 'center',\n    width: '100%',\n    maxWidth: DEFAULT_MAX_WIDTH,\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    width: '100%',\n  },\n  content: {\n    flexDirection: 'row',\n    justifyContent: 'flex-start',\n    marginHorizontal: 8,\n    marginTop: 16,\n    marginBottom: 0,\n  },\n  icon: {\n    margin: 8,\n  },\n  message: {\n    flex: 1,\n    margin: 8,\n  },\n  actions: {\n    flexDirection: 'row',\n    justifyContent: 'flex-end',\n    margin: 4,\n  },\n  button: {\n    margin: 4,\n  },\n  elevation: {\n    elevation: 1,\n  },\n  transparent: {\n    opacity: 0,\n  },\n});\n\nexport default Banner;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAI9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,OAAOC,MAAM;AACb,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,SAASC,gBAAgB;AAGzB,IAAMC,iBAAiB,GAAG,GAAG;AAyG7B,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAGC,IAAA,EAaF;EAAA,IAZXC,OAAO,GAYDD,IAAA,CAZNC,OAAO;IACPC,IAAI,GAWEF,IAAA,CAXNE,IAAI;IACJC,QAAQ,GAUFH,IAAA,CAVNG,QAAQ;IAAAC,YAAA,GAUFJ,IAAA,CATNK,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,EAAE,GAAAA,YAAA;IACZE,YAAY,GAQNN,IAAA,CARNM,YAAY;IAAAC,cAAA,GAQNP,IAAA,CAPNQ,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,CAAC,GAAAA,cAAA;IACbE,KAAK,GAMCT,IAAA,CANNS,KAAK;IACEC,cAAc,GAKfV,IAAA,CALNW,KAAK;IAAAC,qBAAA,GAKCZ,IAAA,CAJNa,uBAAuB;IAAvBA,uBAAuB,GAAAD,qBAAA,cAAG,YAAM,CAAC,CAAC,GAAAA,qBAAA;IAAAE,qBAAA,GAI5Bd,IAAA,CAHNe,uBAAuB;IAAvBA,uBAAuB,GAAAD,qBAAA,cAAG,YAAM,CAAC,CAAC,GAAAA,qBAAA;IAClCE,qBAAqB,GAEfhB,IAAA,CAFNgB,qBAAqB;IAClBC,IAAA,GAAAC,wBAAA,CACGlB,IAAA,EAAAmB,SAAA;EACN,IAAMR,KAAK,GAAGd,gBAAgB,CAACa,cAAc,CAAC;EAC9C,IAAAU,aAAA,GAA8BhC,KAAK,CAACiC,MAAM,CACxC,IAAIhC,QAAQ,CAACiC,KAAK,CAACrB,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;IAFgBsB,QAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAAC,eAAA,GAA4BrC,KAAK,CAACsC,QAAQ,CAGvC;MACDC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAL,eAAA;IANKM,MAAM,GAAAF,gBAAA;IAAEG,SAAS,GAAAH,gBAAA;EAQxB,IAAMI,YAAY,GAAGzC,iBAAiB,CAACqB,uBAAuB,CAAC;EAC/D,IAAMqB,YAAY,GAAG1C,iBAAiB,CAACuB,uBAAuB,CAAC;EAE/D,IAAQoB,KAAA,GAAUxB,KAAK,CAACyB,SAAS,CAAzBD,KAAA;EAER,IAAME,OAAO,GAAGd,QAAQ,CAACe,WAAW,CAAC;IACnCC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IACvBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;EAEFpD,KAAK,CAACqD,SAAS,CAAC,YAAM;IACpB,IAAIxC,OAAO,EAAE;MAEXZ,QAAQ,CAACqD,MAAM,CAACnB,QAAQ,EAAE;QACxBoB,QAAQ,EAAE,GAAG,GAAGR,KAAK;QACrBS,OAAO,EAAE,CAAC;QACVC,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAACb,YAAY,CAAC;IACxB,CAAC,MAAM;MAEL5C,QAAQ,CAACqD,MAAM,CAACnB,QAAQ,EAAE;QACxBoB,QAAQ,EAAE,GAAG,GAAGR,KAAK;QACrBS,OAAO,EAAE,CAAC;QACVC,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAACZ,YAAY,CAAC;IACxB;EAEF,CAAC,EAAE,CAACjC,OAAO,EAAEsB,QAAQ,EAAEY,KAAK,CAAC,CAAC;EAE9B,IAAMY,YAAY,GAAG,SAAfA,YAAYA,CAAGC,KAAA,EAAwC;IAAA,IAArCC,WAAA,GAAgCD,KAAA,CAAhCC,WAAA;IACtB,IAAQtB,MAAA,GAAWsB,WAAW,CAAClB,MAAM,CAA7BJ,MAAA;IACRK,SAAS,CAAC;MAAEL,MAAM,EAANA,MAAM;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EACvC,CAAC;EASD,IAAMD,MAAM,GAAGtC,QAAQ,CAAC6D,QAAQ,CAAC3B,QAAQ,EAAEQ,MAAM,CAACJ,MAAM,CAAC;EAEzD,IAAMwB,UAAU,GAAG9D,QAAQ,CAAC6D,QAAQ,CAClC7D,QAAQ,CAAC+D,GAAG,CAAC7B,QAAQ,EAAE,CAAC,CAAC,CAAC,EAC1BQ,MAAM,CAACJ,MACT,CAAC;EACD,OACEvC,KAAA,CAAAiE,aAAA,CAAC1D,OAAO,EAAA2D,QAAA,KACFrC,IAAI;IACRR,KAAK,EAAE,CAAC,CAACE,KAAK,CAAC4C,IAAI,IAAIC,MAAM,CAAChD,SAAS,EAAE;MAAE6B,OAAA,EAAAA;IAAQ,CAAC,EAAE5B,KAAK,CAAE;IAC7DE,KAAK,EAAEA;EAAM,GACRA,KAAK,CAAC4C,IAAI,IAAI;IAAE/C,SAAA,EAAAA;EAAU,CAAC,GAEhCpB,KAAA,CAAAiE,aAAA,CAAC9D,IAAI;IAACkB,KAAK,EAAE,CAAC+C,MAAM,CAACC,OAAO,EAAEnD,YAAY;EAAE,GAC1ClB,KAAA,CAAAiE,aAAA,CAAChE,QAAQ,CAACE,IAAI;IAACkB,KAAK,EAAE;MAAEkB,MAAA,EAAAA;IAAO;EAAE,CAAE,CAAC,EACpCvC,KAAA,CAAAiE,aAAA,CAAChE,QAAQ,CAACE,IAAI;IACZmE,QAAQ,EAAEX,YAAa;IACvBtC,KAAK,EAAE,CACLsB,MAAM,CAACH,QAAQ,IAAI,CAAC3B,OAAO,GAGvB,CAACuD,MAAM,CAACG,QAAQ,EAAE;MAAEC,SAAS,EAAE,CAAC;QAAET,UAAA,EAAAA;MAAW,CAAC;IAAE,CAAC,CAAC,GAElD,IAAI,EACR,CAACpB,MAAM,CAACH,QAAQ,IAAI,CAAC3B,OAAO,GAGxBuD,MAAM,CAACK,WAAW,GAClB,IAAI;EACR,GAEFzE,KAAA,CAAAiE,aAAA,CAAC9D,IAAI;IAACkB,KAAK,EAAE+C,MAAM,CAACM;EAAQ,GACzB5D,IAAI,GACHd,KAAA,CAAAiE,aAAA,CAAC9D,IAAI;IAACkB,KAAK,EAAE+C,MAAM,CAACtD;EAAK,GACvBd,KAAA,CAAAiE,aAAA,CAAC3D,IAAI;IAACqE,MAAM,EAAE7D,IAAK;IAAC8D,IAAI,EAAE;EAAG,CAAE,CAC3B,CAAC,GACL,IAAI,EACR5E,KAAA,CAAAiE,aAAA,CAACzD,IAAI;IACHa,KAAK,EAAE,CACL+C,MAAM,CAACS,OAAO,EACd;MACEC,KAAK,EAAEvD,KAAK,CAAC4C,IAAI,GACb5C,KAAK,CAACwD,MAAM,CAACC,SAAS,GACtBzD,KAAK,CAACwD,MAAM,CAACE;IACnB,CAAC,CACD;IACFC,uBAAuB,EAAErE,OAAO,GAAG,QAAQ,GAAG,MAAO;IACrDsE,iBAAiB,EAAC,OAAO;IACzBvD,qBAAqB,EAAEA;EAAsB,GAE5Cb,QACG,CACF,CAAC,EACPf,KAAA,CAAAiE,aAAA,CAAC9D,IAAI;IAACkB,KAAK,EAAE+C,MAAM,CAACnD;EAAQ,GACzBA,OAAO,CAACmE,GAAG,CAAC,UAAAC,KAAA,EAAuBC,CAAC;IAAA,IAAAC,aAAA;IAAA,IAArBC,KAAK,GAAaH,KAAA,CAAlBG,KAAK;MAAKC,MAAA,GAAA3D,wBAAA,CAAQuD,KAAA,EAAAK,UAAA;IAAA,OAChC1F,KAAA,CAAAiE,aAAA,CAAC5D,MAAM,EAAA6D,QAAA;MACLyB,GAAG,EAAqDL,CAAE;MAC1DM,OAAO;MACPC,IAAI,EAAC,MAAM;MACXxE,KAAK,EAAE+C,MAAM,CAAC0B,MAAO;MACrBC,SAAS,GAAAR,aAAA,GAAEhE,KAAK,CAACwD,MAAM,cAAAQ,aAAA,uBAAZA,aAAA,CAAcS,OAAQ;MACjCzE,KAAK,EAAEA;IAAM,GACTkE,MAAM,GAETD,KACK,CAAC;EAAA,CACV,CACG,CACO,CACX,CACC,CAAC;AAEd,CAAC;AAED,IAAMpB,MAAM,GAAGlE,UAAU,CAAC+F,MAAM,CAAC;EAC/B5B,OAAO,EAAE;IACP6B,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE3F;EACZ,CAAC;EACD6D,QAAQ,EAAE;IACRpC,QAAQ,EAAE,UAAU;IACpBmE,GAAG,EAAE,CAAC;IACNF,KAAK,EAAE;EACT,CAAC;EACD1B,OAAO,EAAE;IACP6B,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,YAAY;IAC5BC,gBAAgB,EAAE,CAAC;IACnBC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE;EAChB,CAAC;EACD7F,IAAI,EAAE;IACJ8F,MAAM,EAAE;EACV,CAAC;EACD/B,OAAO,EAAE;IACPgC,IAAI,EAAE,CAAC;IACPD,MAAM,EAAE;EACV,CAAC;EACD3F,OAAO,EAAE;IACPsF,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,UAAU;IAC1BI,MAAM,EAAE;EACV,CAAC;EACDd,MAAM,EAAE;IACNc,MAAM,EAAE;EACV,CAAC;EACDxF,SAAS,EAAE;IACTA,SAAS,EAAE;EACb,CAAC;EACDqD,WAAW,EAAE;IACXxB,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAetC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}