{"ast": null, "code": "\"use strict\";\n\nvar _globalThis$expo, _globalThis$expo$modu, _globalThis$expo2, _globalThis$expo2$mod, _globalThis$expo3;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.setDynamicLoadingErrorCallback = exports.setDynamicLoadingEnabled = exports.isDynamicLoadingSupported = exports.isDynamicLoadingEnabled = exports.getErrorCallback = void 0;\nvar hasNecessaryExpoModules = !!((_globalThis$expo = globalThis.expo) != null && (_globalThis$expo$modu = _globalThis$expo.modules) != null && _globalThis$expo$modu.ExpoAsset) && !!((_globalThis$expo2 = globalThis.expo) != null && (_globalThis$expo2$mod = _globalThis$expo2.modules) != null && _globalThis$expo2$mod.ExpoFontLoader);\nvar hasNecessaryExpoFeatures = hasNecessaryExpoModules && typeof ((_globalThis$expo3 = globalThis.expo) == null ? void 0 : _globalThis$expo3.modules.ExpoFontLoader.getLoadedFonts) === 'function';\nvar dynamicFontLoadingEnabled = hasNecessaryExpoFeatures;\nvar isDynamicLoadingSupported = function isDynamicLoadingSupported() {\n  return hasNecessaryExpoFeatures;\n};\nexports.isDynamicLoadingSupported = isDynamicLoadingSupported;\nvar setDynamicLoadingEnabled = function setDynamicLoadingEnabled(value) {\n  if (!hasNecessaryExpoFeatures) {\n    if (process.env.NODE_ENV !== 'production' && !!value) {\n      var message = hasNecessaryExpoModules ? 'Expo is installed, but does not support dynamic font loading. Make sure to use Expo SDK 52 or newer.' : 'Necessary Expo modules not found. Dynamic font loading is not available on Web or when necessary Expo modules are not present.';\n      console.error(message);\n    }\n    return false;\n  }\n  dynamicFontLoadingEnabled = !!value;\n  return true;\n};\nexports.setDynamicLoadingEnabled = setDynamicLoadingEnabled;\nvar isDynamicLoadingEnabled = function isDynamicLoadingEnabled() {\n  return dynamicFontLoadingEnabled;\n};\nexports.isDynamicLoadingEnabled = isDynamicLoadingEnabled;\nvar dynamicLoadingErrorCallback;\nvar setDynamicLoadingErrorCallback = function setDynamicLoadingErrorCallback(callback) {\n  dynamicLoadingErrorCallback = callback;\n};\nexports.setDynamicLoadingErrorCallback = setDynamicLoadingErrorCallback;\nvar getErrorCallback = function getErrorCallback() {\n  return dynamicLoadingErrorCallback;\n};\nexports.getErrorCallback = getErrorCallback;", "map": {"version": 3, "names": ["hasNecessaryExpoModules", "_globalThis$expo", "globalThis", "expo", "_globalThis$expo$modu", "modules", "ExpoAsset", "_globalThis$expo2", "_globalThis$expo2$mod", "ExpoFontLoader", "hasNecessaryExpoFeatures", "_globalThis$expo3", "getLoadedFonts", "dynamicFontLoadingEnabled", "isDynamicLoadingSupported", "exports", "setDynamicLoadingEnabled", "value", "process", "env", "NODE_ENV", "message", "console", "error", "isDynamicLoadingEnabled", "dynamicLoadingErrorCallback", "setDynamicLoadingErrorCallback", "callback", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-vector-icons/common/src/dynamicLoading/dynamic-loading-setting.ts"], "sourcesContent": ["import type { FontSource } from './types';\n\ndeclare global {\n  interface ExpoGlobal {\n    modules: {\n      ExpoAsset: {\n        // definition from\n        // https://github.com/expo/expo/blob/1f5a5991d14aad09282d1ce1612b44d30e7e7d3d/packages/expo-asset/ios/AssetModule.swift#L23\n        downloadAsync: (uri: string, hash: string | undefined, type: string) => Promise<string>;\n      };\n      ExpoFontLoader: {\n        // definition from\n        // https://github.com/expo/expo/blob/1f5a5991d14aad09282d1ce1612b44d30e7e7d3d/packages/expo-font/ios/FontLoaderModule.swift#L18\n        getLoadedFonts: () => string[];\n        loadAsync: (fontFamilyAlias: string, fileUri: string) => Promise<void>;\n      };\n    };\n  }\n\n  // eslint-disable-next-line vars-on-top\n  var expo: ExpoGlobal | undefined;\n}\n\nconst hasNecessaryExpoModules = !!globalThis.expo?.modules?.ExpoAsset && !!globalThis.expo?.modules?.ExpoFontLoader;\n\nconst hasNecessaryExpoFeatures =\n  hasNecessaryExpoModules && typeof globalThis.expo?.modules.ExpoFontLoader.getLoadedFonts === 'function';\n\nlet dynamicFontLoadingEnabled = hasNecessaryExpoFeatures;\n\nexport const isDynamicLoadingSupported = () => hasNecessaryExpoFeatures;\n\n/**\n * Set whether dynamic loading of fonts is enabled.\n * Currently, the presence of Expo Asset and Font Loader modules is a prerequisite for enabling.\n * In the future, React Native core apis will be used for dynamic font loading.\n *\n * @param value - whether dynamic loading of fonts is enabled\n * @returns `true` if dynamic loading of fonts was successfully set. `false` otherwise.\n * */\nexport const setDynamicLoadingEnabled = (value: boolean): boolean => {\n  if (!hasNecessaryExpoFeatures) {\n    if (process.env.NODE_ENV !== 'production' && !!value) {\n      const message = hasNecessaryExpoModules\n        ? 'Expo is installed, but does not support dynamic font loading. Make sure to use Expo SDK 52 or newer.'\n        : 'Necessary Expo modules not found. Dynamic font loading is not available on Web or when necessary Expo modules are not present.';\n      console.error(message); // eslint-disable-line no-console\n    }\n    return false;\n  }\n\n  dynamicFontLoadingEnabled = !!value;\n\n  return true;\n};\n\n/**\n * Whether dynamic loading of fonts is enabled.\n * */\nexport const isDynamicLoadingEnabled = () => dynamicFontLoadingEnabled;\n\ntype ErrorCallback = (args: {\n  error: Error;\n  fontFamily: string;\n  fontSource: FontSource;\n}) => void;\n\nlet dynamicLoadingErrorCallback: undefined | ErrorCallback;\n\n/**\n * Set a callback to be called when an error occurs during dynamic font loading.\n * */\nexport const setDynamicLoadingErrorCallback = (callback: ErrorCallback) => {\n  dynamicLoadingErrorCallback = callback;\n};\n\nexport const getErrorCallback = () => dynamicLoadingErrorCallback;\n"], "mappings": ";;;;;;;AAuBA,IAAMA,uBAAuB,GAAG,CAAC,GAAAC,gBAAA,GAACC,UAAU,CAACC,IAAI,cAAAC,qBAAA,GAAfH,gBAAA,CAAiBI,OAAO,aAAxBD,qBAAA,CAA0BE,SAAS,KAAI,CAAC,GAAAC,iBAAA,GAACL,UAAU,CAACC,IAAI,cAAAK,qBAAA,GAAfD,iBAAA,CAAiBF,OAAO,aAAxBG,qBAAA,CAA0BC,cAAc;AAEnH,IAAMC,wBAAwB,GAC5BV,uBAAuB,IAAI,SAAAW,iBAAA,GAAOT,UAAU,CAACC,IAAI,qBAAfQ,iBAAA,CAAiBN,OAAO,CAACI,cAAc,CAACG,cAAc,MAAK,UAAU;AAEzG,IAAIC,yBAAyB,GAAGH,wBAAwB;AAEjD,IAAMI,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAA;EAAA,OAASJ,wBAAwB;AAAA;AAEvEK,OAAA,CAAAD,yBAAA,GAAAA,yBAAA;AAQO,IAAME,wBAAwB,GAAI,SAA5BA,wBAAwBA,CAAIC,KAAc,EAAc;EACnE,IAAI,CAACP,wBAAwB,EAAE;IAC7B,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC,CAACH,KAAK,EAAE;MACpD,IAAMI,OAAO,GAAGrB,uBAAuB,GACnC,sGAAsG,GACtG,gIAAgI;MACpIsB,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;IACA,OAAO,KAAK;EACd;EAEAR,yBAAyB,GAAG,CAAC,CAACI,KAAK;EAEnC,OAAO,IAAI;AACb,CAAC;AAEDF,OAAA,CAAAC,wBAAA,GAAAA,wBAAA;AAGO,IAAMQ,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;EAAA,OAASX,yBAAyB;AAAA;AAACE,OAAA,CAAAS,uBAAA,GAAAA,uBAAA;AAQvE,IAAIC,2BAAsD;AAKnD,IAAMC,8BAA8B,GAAI,SAAlCA,8BAA8BA,CAAIC,QAAuB,EAAK;EACzEF,2BAA2B,GAAGE,QAAQ;AACxC,CAAC;AAACZ,OAAA,CAAAW,8BAAA,GAAAA,8BAAA;AAEK,IAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OAASH,2BAA2B;AAAA;AAACV,OAAA,CAAAa,gBAAA,GAAAA,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}