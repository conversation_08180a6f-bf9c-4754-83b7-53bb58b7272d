{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport * as SQLite from 'expo-sqlite';\nimport * as FileSystem from 'expo-file-system';\nimport { Asset } from 'expo-asset';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nvar DatabaseManager = function () {\n  function DatabaseManager() {\n    _classCallCheck(this, DatabaseManager);\n    this.db = null;\n    this.initialized = false;\n  }\n  return _createClass(DatabaseManager, [{\n    key: \"init\",\n    value: (function () {\n      var _init = _asyncToGenerator(function* () {\n        if (this.initialized) return;\n        try {\n          if (Platform.OS === 'web') {\n            console.warn('SQLite not available on web platform. Using mock database.');\n            this.db = this._createMockDatabase();\n            this.initialized = true;\n            return;\n          }\n          this.db = SQLite.openDatabase('znunizaehler.db');\n          var isInitialized = yield this._checkIfInitialized();\n          if (!isInitialized) {\n            console.log('Initializing database...');\n            yield this._initializeDatabase();\n          } else {\n            console.log('Database already initialized');\n            yield this._checkMigrations();\n          }\n          this.initialized = true;\n        } catch (error) {\n          console.error('Database initialization error:', error);\n          throw error;\n        }\n      });\n      function init() {\n        return _init.apply(this, arguments);\n      }\n      return init;\n    }())\n  }, {\n    key: \"isFoodDatabaseImported\",\n    value: (function () {\n      var _isFoodDatabaseImported = _asyncToGenerator(function* (source) {\n        try {\n          var result = yield this.executeQuery(\"SELECT COUNT(*) as count FROM Food WHERE source LIKE ?\", [`${source}%`]);\n          return result.rows.item(0).count > 0;\n        } catch (error) {\n          console.error(`Error checking if ${source} database is imported:`, error);\n          return false;\n        }\n      });\n      function isFoodDatabaseImported(_x) {\n        return _isFoodDatabaseImported.apply(this, arguments);\n      }\n      return isFoodDatabaseImported;\n    }())\n  }, {\n    key: \"_checkIfInitialized\",\n    value: function _checkIfInitialized() {\n      var _this = this;\n      return new Promise(function (resolve) {\n        _this.db.transaction(function (tx) {\n          tx.executeSql(\"SELECT name FROM sqlite_master WHERE type='table' AND name='User'\", [], function (_, result) {\n            resolve(result.rows.length > 0);\n          }, function (_, error) {\n            console.error('Error checking database initialization:', error);\n            resolve(false);\n            return false;\n          });\n        });\n      });\n    }\n  }, {\n    key: \"_initializeDatabase\",\n    value: (function () {\n      var _initializeDatabase2 = _asyncToGenerator(function* () {\n        var _this2 = this;\n        try {\n          var asset = Asset.fromModule(require(\"../assets/database/schema.sql\"));\n          yield asset.downloadAsync();\n          var sqlSchema = yield FileSystem.readAsStringAsync(asset.localUri);\n          var statements = sqlSchema.split(';').filter(function (stmt) {\n            return stmt.trim().length > 0;\n          });\n          return new Promise(function (resolve, reject) {\n            _this2.db.transaction(function (tx) {\n              statements.forEach(function (statement) {\n                tx.executeSql(statement, [], function () {}, function (_, error) {\n                  console.error('Error executing SQL statement:', error, statement);\n                  return false;\n                });\n              });\n            }, function (error) {\n              console.error('Transaction error during initialization:', error);\n              reject(error);\n            }, function () {\n              console.log('Database initialized successfully');\n              resolve();\n            });\n          });\n        } catch (error) {\n          console.error('Error in database initialization:', error);\n          throw error;\n        }\n      });\n      function _initializeDatabase() {\n        return _initializeDatabase2.apply(this, arguments);\n      }\n      return _initializeDatabase;\n    }())\n  }, {\n    key: \"_checkMigrations\",\n    value: (function () {\n      var _checkMigrations2 = _asyncToGenerator(function* () {\n        try {\n          var versionResult = yield this.executeQuery('SELECT version FROM DatabaseVersion ORDER BY version DESC LIMIT 1');\n          if (versionResult.rows.length === 0) {\n            console.warn('No database version found. Creating initial version record.');\n            yield this.executeQuery('INSERT INTO DatabaseVersion (version, description) VALUES (1, \"Initial schema\")');\n            return;\n          }\n          var currentVersion = versionResult.rows.item(0).version;\n          console.log(`Current database version: ${currentVersion}`);\n          switch (currentVersion) {\n            case 1:\n            default:\n              break;\n          }\n        } catch (error) {\n          console.error('Error checking migrations:', error);\n          throw error;\n        }\n      });\n      function _checkMigrations() {\n        return _checkMigrations2.apply(this, arguments);\n      }\n      return _checkMigrations;\n    }())\n  }, {\n    key: \"_applyMigration\",\n    value: (function () {\n      var _applyMigration2 = _asyncToGenerator(function* (version, description, queries) {\n        try {\n          queries.push({\n            sql: 'INSERT INTO DatabaseVersion (version, description) VALUES (?, ?)',\n            params: [version, description]\n          });\n          yield this.executeTransaction(queries);\n          console.log(`Applied migration to version ${version}: ${description}`);\n        } catch (error) {\n          console.error(`Error applying migration to version ${version}:`, error);\n          throw error;\n        }\n      });\n      function _applyMigration(_x2, _x3, _x4) {\n        return _applyMigration2.apply(this, arguments);\n      }\n      return _applyMigration;\n    }())\n  }, {\n    key: \"executeQuery\",\n    value: function executeQuery(sql) {\n      var _this3 = this;\n      var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n      return new Promise(function (resolve, reject) {\n        if (!_this3.initialized) {\n          reject(new Error('Database not initialized. Call init() first.'));\n          return;\n        }\n        _this3.db.transaction(function (tx) {\n          tx.executeSql(sql, params, function (_, result) {\n            resolve(result);\n          }, function (_, error) {\n            console.error('SQL Error:', error);\n            reject(error);\n            return false;\n          });\n        });\n      });\n    }\n  }, {\n    key: \"executeTransaction\",\n    value: function executeTransaction(queries) {\n      var _this4 = this;\n      return new Promise(function (resolve, reject) {\n        if (!_this4.initialized) {\n          reject(new Error('Database not initialized. Call init() first.'));\n          return;\n        }\n        var results = [];\n        _this4.db.transaction(function (tx) {\n          queries.forEach(function (_ref) {\n            var sql = _ref.sql,\n              _ref$params = _ref.params,\n              params = _ref$params === void 0 ? [] : _ref$params;\n            tx.executeSql(sql, params, function (_, result) {\n              results.push(result);\n            }, function (_, error) {\n              console.error('Transaction SQL Error:', error);\n              return false;\n            });\n          });\n        }, function (error) {\n          console.error('Transaction error:', error);\n          reject(error);\n        }, function () {\n          resolve(results);\n        });\n      });\n    }\n  }, {\n    key: \"getAll\",\n    value: function getAll(table) {\n      return this.executeQuery(`SELECT * FROM ${table} WHERE is_active = 1 AND deleted_at IS NULL`).then(function (result) {\n        var items = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          items.push(result.rows.item(i));\n        }\n        return items;\n      });\n    }\n  }, {\n    key: \"getById\",\n    value: function getById(table, id) {\n      var idField = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      var idColumn = idField || `${table.replace(/s$/, '')}_id`;\n      return this.executeQuery(`SELECT * FROM ${table} WHERE ${idColumn} = ? AND is_active = 1 AND deleted_at IS NULL`, [id]).then(function (result) {\n        return result.rows.length > 0 ? result.rows.item(0) : null;\n      });\n    }\n  }, {\n    key: \"insert\",\n    value: function insert(table, data) {\n      var columns = Object.keys(data).join(', ');\n      var placeholders = Object.keys(data).map(function () {\n        return '?';\n      }).join(', ');\n      var values = Object.values(data);\n      return this.executeQuery(`INSERT INTO ${table} (${columns}) VALUES (${placeholders})`, values).then(function (result) {\n        return result.insertId;\n      });\n    }\n  }, {\n    key: \"update\",\n    value: function update(table, id, data) {\n      var idField = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n      var idColumn = idField || `${table.replace(/s$/, '')}_id`;\n      var setClause = Object.keys(data).map(function (key) {\n        return `${key} = ?`;\n      }).join(', ');\n      var values = [].concat(_toConsumableArray(Object.values(data)), [id]);\n      return this.executeQuery(`UPDATE ${table} SET ${setClause} WHERE ${idColumn} = ?`, values).then(function (result) {\n        return result.rowsAffected;\n      });\n    }\n  }, {\n    key: \"softDelete\",\n    value: function softDelete(table, id) {\n      var idField = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      var idColumn = idField || `${table.replace(/s$/, '')}_id`;\n      return this.executeQuery(`UPDATE ${table} SET is_active = 0, deleted_at = datetime('now') WHERE ${idColumn} = ?`, [id]).then(function (result) {\n        return result.rowsAffected;\n      });\n    }\n  }, {\n    key: \"hardDelete\",\n    value: function hardDelete(table, id) {\n      var idField = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      var idColumn = idField || `${table.replace(/s$/, '')}_id`;\n      return this.executeQuery(`DELETE FROM ${table} WHERE ${idColumn} = ?`, [id]).then(function (result) {\n        return result.rowsAffected;\n      });\n    }\n  }, {\n    key: \"query\",\n    value: function query(table) {\n      var conditions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var whereClause = 'is_active = 1 AND deleted_at IS NULL';\n      var params = [];\n      Object.entries(conditions).forEach(function (_ref2) {\n        var _ref3 = _slicedToArray(_ref2, 2),\n          key = _ref3[0],\n          value = _ref3[1];\n        whereClause += ` AND ${key} = ?`;\n        params.push(value);\n      });\n      return this.executeQuery(`SELECT * FROM ${table} WHERE ${whereClause}`, params).then(function (result) {\n        var items = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          items.push(result.rows.item(i));\n        }\n        return items;\n      });\n    }\n  }, {\n    key: \"getDatabaseVersion\",\n    value: (function () {\n      var _getDatabaseVersion = _asyncToGenerator(function* () {\n        try {\n          var result = yield this.executeQuery('SELECT version FROM DatabaseVersion ORDER BY version DESC LIMIT 1');\n          if (result.rows.length === 0) {\n            return 0;\n          }\n          return result.rows.item(0).version;\n        } catch (error) {\n          console.error('Error getting database version:', error);\n          return 0;\n        }\n      });\n      function getDatabaseVersion() {\n        return _getDatabaseVersion.apply(this, arguments);\n      }\n      return getDatabaseVersion;\n    }())\n  }, {\n    key: \"getDatabaseStats\",\n    value: (function () {\n      var _getDatabaseStats = _asyncToGenerator(function* () {\n        try {\n          var tables = yield this.executeQuery(\"SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'\");\n          var stats = {\n            version: yield this.getDatabaseVersion(),\n            tables: [],\n            totalRecords: 0\n          };\n          for (var i = 0; i < tables.rows.length; i++) {\n            var tableName = tables.rows.item(i).name;\n            var countResult = yield this.executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`);\n            var count = countResult.rows.item(0).count;\n            stats.tables.push({\n              name: tableName,\n              records: count\n            });\n            stats.totalRecords += count;\n          }\n          return stats;\n        } catch (error) {\n          console.error('Error getting database stats:', error);\n          throw error;\n        }\n      });\n      function getDatabaseStats() {\n        return _getDatabaseStats.apply(this, arguments);\n      }\n      return getDatabaseStats;\n    }())\n  }, {\n    key: \"backupDatabase\",\n    value: (function () {\n      var _backupDatabase = _asyncToGenerator(function* () {\n        try {\n          var _FileSystem = require('expo-file-system');\n          var dbDir = _FileSystem.documentDirectory + 'SQLite/';\n          var dbPath = dbDir + 'znunizaehler.db';\n          var backupPath = _FileSystem.documentDirectory + 'backup_' + new Date().toISOString().replace(/[:.]/g, '_') + '.db';\n          yield _FileSystem.copyAsync({\n            from: dbPath,\n            to: backupPath\n          });\n          return backupPath;\n        } catch (error) {\n          console.error('Error backing up database:', error);\n          throw error;\n        }\n      });\n      function backupDatabase() {\n        return _backupDatabase.apply(this, arguments);\n      }\n      return backupDatabase;\n    }())\n  }, {\n    key: \"resetDatabase\",\n    value: (function () {\n      var _resetDatabase = _asyncToGenerator(function* () {\n        try {\n          if (this.db) {\n            this.db._db.close();\n          }\n          var _FileSystem2 = require('expo-file-system');\n          var dbDir = _FileSystem2.documentDirectory + 'SQLite/';\n          var dbPath = dbDir + 'znunizaehler.db';\n          yield _FileSystem2.deleteAsync(dbPath, {\n            idempotent: true\n          });\n          this.db = null;\n          this.initialized = false;\n          yield this.init();\n          console.log('Database reset successfully');\n        } catch (error) {\n          console.error('Error resetting database:', error);\n          throw error;\n        }\n      });\n      function resetDatabase() {\n        return _resetDatabase.apply(this, arguments);\n      }\n      return resetDatabase;\n    }())\n  }, {\n    key: \"close\",\n    value: function close() {\n      if (this.db) {\n        this.db._db.close();\n        this.db = null;\n        this.initialized = false;\n      }\n    }\n  }]);\n}();\nvar dbManager = new DatabaseManager();\nexport default dbManager;", "map": {"version": 3, "names": ["SQLite", "FileSystem", "<PERSON><PERSON>", "Platform", "DatabaseManager", "_classCallCheck", "db", "initialized", "_createClass", "key", "value", "_init", "_asyncToGenerator", "OS", "console", "warn", "_createMockDatabase", "openDatabase", "isInitialized", "_checkIfInitialized", "log", "_initializeDatabase", "_checkMigrations", "error", "init", "apply", "arguments", "_isFoodDatabaseImported", "source", "result", "execute<PERSON>uery", "rows", "item", "count", "isFoodDatabaseImported", "_x", "_this", "Promise", "resolve", "transaction", "tx", "executeSql", "_", "length", "_initializeDatabase2", "_this2", "asset", "fromModule", "require", "downloadAsync", "sqlSchema", "readAsStringAsync", "localUri", "statements", "split", "filter", "stmt", "trim", "reject", "for<PERSON>ach", "statement", "_checkMigrations2", "versionResult", "currentVersion", "version", "_applyMigration2", "description", "queries", "push", "sql", "params", "executeTransaction", "_applyMigration", "_x2", "_x3", "_x4", "_this3", "undefined", "Error", "_this4", "results", "_ref", "_ref$params", "getAll", "table", "then", "items", "i", "getById", "id", "idField", "idColumn", "replace", "insert", "data", "columns", "Object", "keys", "join", "placeholders", "map", "values", "insertId", "update", "<PERSON><PERSON><PERSON><PERSON>", "concat", "_toConsumableArray", "rowsAffected", "softDelete", "hardDelete", "query", "conditions", "<PERSON><PERSON><PERSON><PERSON>", "entries", "_ref2", "_ref3", "_slicedToArray", "_getDatabaseVersion", "getDatabaseVersion", "_getDatabaseStats", "tables", "stats", "totalRecords", "tableName", "name", "count<PERSON><PERSON><PERSON>", "records", "getDatabaseStats", "_backupDatabase", "dbDir", "documentDirectory", "db<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Date", "toISOString", "copyAsync", "from", "to", "backupDatabase", "_resetDatabase", "_db", "close", "deleteAsync", "idempotent", "resetDatabase", "db<PERSON><PERSON><PERSON>"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/database/DatabaseManager.js"], "sourcesContent": ["import * as SQLite from 'expo-sqlite';\nimport * as FileSystem from 'expo-file-system';\nimport { Asset } from 'expo-asset';\nimport { Platform } from 'react-native';\n\n/**\n * Database Manager for ZnüniZähler\n * Handles database initialization, migrations, and provides access methods\n */\nclass DatabaseManager {\n  constructor() {\n    this.db = null;\n    this.initialized = false;\n  }\n\n  /**\n   * Initialize the database\n   * @returns {Promise<void>}\n   */\n  async init() {\n    if (this.initialized) return;\n\n    try {\n      // Check if we're running on web\n      if (Platform.OS === 'web') {\n        console.warn('SQLite not available on web platform. Using mock database.');\n        this.db = this._createMockDatabase();\n        this.initialized = true;\n        return;\n      }\n\n      // Open the database\n      this.db = SQLite.openDatabase('znunizaehler.db');\n\n      // Check if database needs initialization\n      const isInitialized = await this._checkIfInitialized();\n\n      if (!isInitialized) {\n        console.log('Initializing database...');\n        await this._initializeDatabase();\n      } else {\n        console.log('Database already initialized');\n        // Check for migrations\n        await this._checkMigrations();\n      }\n\n      this.initialized = true;\n    } catch (error) {\n      console.error('Database initialization error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Check if food database is imported\n   * @param {string} source - Database source (e.g., 'USDA', 'FoodB')\n   * @returns {Promise<boolean>}\n   */\n  async isFoodDatabaseImported(source) {\n    try {\n      const result = await this.executeQuery(\n        \"SELECT COUNT(*) as count FROM Food WHERE source LIKE ?\",\n        [`${source}%`]\n      );\n\n      return result.rows.item(0).count > 0;\n    } catch (error) {\n      console.error(`Error checking if ${source} database is imported:`, error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if the database has been initialized\n   * @returns {Promise<boolean>}\n   * @private\n   */\n  _checkIfInitialized() {\n    return new Promise((resolve) => {\n      this.db.transaction(tx => {\n        tx.executeSql(\n          \"SELECT name FROM sqlite_master WHERE type='table' AND name='User'\",\n          [],\n          (_, result) => {\n            resolve(result.rows.length > 0);\n          },\n          (_, error) => {\n            console.error('Error checking database initialization:', error);\n            resolve(false);\n            return false;\n          }\n        );\n      });\n    });\n  }\n\n  /**\n   * Initialize the database with schema\n   * @returns {Promise<void>}\n   * @private\n   */\n  async _initializeDatabase() {\n    try {\n      // Load SQL schema from asset\n      const asset = Asset.fromModule(require('../assets/database/schema.sql'));\n      await asset.downloadAsync();\n\n      const sqlSchema = await FileSystem.readAsStringAsync(asset.localUri);\n      const statements = sqlSchema.split(';').filter(stmt => stmt.trim().length > 0);\n\n      return new Promise((resolve, reject) => {\n        this.db.transaction(\n          tx => {\n            statements.forEach(statement => {\n              tx.executeSql(statement, [],\n                () => { },\n                (_, error) => {\n                  console.error('Error executing SQL statement:', error, statement);\n                  return false;\n                }\n              );\n            });\n          },\n          error => {\n            console.error('Transaction error during initialization:', error);\n            reject(error);\n          },\n          () => {\n            console.log('Database initialized successfully');\n            resolve();\n          }\n        );\n      });\n    } catch (error) {\n      console.error('Error in database initialization:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Check for and apply database migrations\n   * @returns {Promise<void>}\n   * @private\n   */\n  async _checkMigrations() {\n    try {\n      // Get current database version\n      const versionResult = await this.executeQuery(\n        'SELECT version FROM DatabaseVersion ORDER BY version DESC LIMIT 1'\n      );\n\n      if (versionResult.rows.length === 0) {\n        console.warn('No database version found. Creating initial version record.');\n        await this.executeQuery(\n          'INSERT INTO DatabaseVersion (version, description) VALUES (1, \"Initial schema\")'\n        );\n        return;\n      }\n\n      const currentVersion = versionResult.rows.item(0).version;\n      console.log(`Current database version: ${currentVersion}`);\n\n      // Apply migrations based on current version\n      switch (currentVersion) {\n        case 1:\n        // No migrations yet, but this is where we would add them\n        // await this._applyMigrationToVersion2();\n        // currentVersion = 2;\n        // falls through to next case\n\n        // case 2:\n        // await this._applyMigrationToVersion3();\n        // currentVersion = 3;\n        // falls through to next case\n\n        default:\n          // No migrations needed\n          break;\n      }\n    } catch (error) {\n      console.error('Error checking migrations:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Apply a migration and update the version\n   * @param {number} version - New version number\n   * @param {string} description - Migration description\n   * @param {Array<{sql: string, params: Array}>} queries - Migration queries\n   * @returns {Promise<void>}\n   * @private\n   */\n  async _applyMigration(version, description, queries) {\n    try {\n      // Add version update query\n      queries.push({\n        sql: 'INSERT INTO DatabaseVersion (version, description) VALUES (?, ?)',\n        params: [version, description]\n      });\n\n      // Execute all queries in a transaction\n      await this.executeTransaction(queries);\n\n      console.log(`Applied migration to version ${version}: ${description}`);\n    } catch (error) {\n      console.error(`Error applying migration to version ${version}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Execute a SQL query with parameters\n   * @param {string} sql - SQL query\n   * @param {Array} params - Query parameters\n   * @returns {Promise<Object>} - Query result\n   */\n  executeQuery(sql, params = []) {\n    return new Promise((resolve, reject) => {\n      if (!this.initialized) {\n        reject(new Error('Database not initialized. Call init() first.'));\n        return;\n      }\n\n      this.db.transaction(tx => {\n        tx.executeSql(\n          sql,\n          params,\n          (_, result) => {\n            resolve(result);\n          },\n          (_, error) => {\n            console.error('SQL Error:', error);\n            reject(error);\n            return false;\n          }\n        );\n      });\n    });\n  }\n\n  /**\n   * Execute multiple SQL queries in a transaction\n   * @param {Array<{sql: string, params: Array}>} queries - Array of query objects\n   * @returns {Promise<Array>} - Array of results\n   */\n  executeTransaction(queries) {\n    return new Promise((resolve, reject) => {\n      if (!this.initialized) {\n        reject(new Error('Database not initialized. Call init() first.'));\n        return;\n      }\n\n      const results = [];\n\n      this.db.transaction(\n        tx => {\n          queries.forEach(({ sql, params = [] }) => {\n            tx.executeSql(\n              sql,\n              params,\n              (_, result) => {\n                results.push(result);\n              },\n              (_, error) => {\n                console.error('Transaction SQL Error:', error);\n                return false;\n              }\n            );\n          });\n        },\n        error => {\n          console.error('Transaction error:', error);\n          reject(error);\n        },\n        () => {\n          resolve(results);\n        }\n      );\n    });\n  }\n\n  /**\n   * Get all active records from a table\n   * @param {string} table - Table name\n   * @returns {Promise<Array>} - Array of records\n   */\n  getAll(table) {\n    return this.executeQuery(`SELECT * FROM ${table} WHERE is_active = 1 AND deleted_at IS NULL`)\n      .then(result => {\n        const items = [];\n        for (let i = 0; i < result.rows.length; i++) {\n          items.push(result.rows.item(i));\n        }\n        return items;\n      });\n  }\n\n  /**\n   * Get a record by ID\n   * @param {string} table - Table name\n   * @param {number} id - Record ID\n   * @param {string} idField - ID field name (default: table_id)\n   * @returns {Promise<Object>} - Record object\n   */\n  getById(table, id, idField = null) {\n    const idColumn = idField || `${table.replace(/s$/, '')}_id`;\n    return this.executeQuery(\n      `SELECT * FROM ${table} WHERE ${idColumn} = ? AND is_active = 1 AND deleted_at IS NULL`,\n      [id]\n    ).then(result => {\n      return result.rows.length > 0 ? result.rows.item(0) : null;\n    });\n  }\n\n  /**\n   * Insert a record\n   * @param {string} table - Table name\n   * @param {Object} data - Record data\n   * @returns {Promise<number>} - Inserted record ID\n   */\n  insert(table, data) {\n    const columns = Object.keys(data).join(', ');\n    const placeholders = Object.keys(data).map(() => '?').join(', ');\n    const values = Object.values(data);\n\n    return this.executeQuery(\n      `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`,\n      values\n    ).then(result => {\n      return result.insertId;\n    });\n  }\n\n  /**\n   * Update a record\n   * @param {string} table - Table name\n   * @param {number} id - Record ID\n   * @param {Object} data - Record data\n   * @param {string} idField - ID field name (default: table_id)\n   * @returns {Promise<number>} - Number of rows affected\n   */\n  update(table, id, data, idField = null) {\n    const idColumn = idField || `${table.replace(/s$/, '')}_id`;\n    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');\n    const values = [...Object.values(data), id];\n\n    return this.executeQuery(\n      `UPDATE ${table} SET ${setClause} WHERE ${idColumn} = ?`,\n      values\n    ).then(result => {\n      return result.rowsAffected;\n    });\n  }\n\n  /**\n   * Soft delete a record\n   * @param {string} table - Table name\n   * @param {number} id - Record ID\n   * @param {string} idField - ID field name (default: table_id)\n   * @returns {Promise<number>} - Number of rows affected\n   */\n  softDelete(table, id, idField = null) {\n    const idColumn = idField || `${table.replace(/s$/, '')}_id`;\n    return this.executeQuery(\n      `UPDATE ${table} SET is_active = 0, deleted_at = datetime('now') WHERE ${idColumn} = ?`,\n      [id]\n    ).then(result => {\n      return result.rowsAffected;\n    });\n  }\n\n  /**\n   * Hard delete a record (use with caution)\n   * @param {string} table - Table name\n   * @param {number} id - Record ID\n   * @param {string} idField - ID field name (default: table_id)\n   * @returns {Promise<number>} - Number of rows affected\n   */\n  hardDelete(table, id, idField = null) {\n    const idColumn = idField || `${table.replace(/s$/, '')}_id`;\n    return this.executeQuery(\n      `DELETE FROM ${table} WHERE ${idColumn} = ?`,\n      [id]\n    ).then(result => {\n      return result.rowsAffected;\n    });\n  }\n\n  /**\n   * Query records with custom conditions\n   * @param {string} table - Table name\n   * @param {Object} conditions - Conditions object\n   * @returns {Promise<Array>} - Array of records\n   */\n  query(table, conditions = {}) {\n    let whereClause = 'is_active = 1 AND deleted_at IS NULL';\n    const params = [];\n\n    Object.entries(conditions).forEach(([key, value]) => {\n      whereClause += ` AND ${key} = ?`;\n      params.push(value);\n    });\n\n    return this.executeQuery(\n      `SELECT * FROM ${table} WHERE ${whereClause}`,\n      params\n    ).then(result => {\n      const items = [];\n      for (let i = 0; i < result.rows.length; i++) {\n        items.push(result.rows.item(i));\n      }\n      return items;\n    });\n  }\n\n  /**\n   * Get database version\n   * @returns {Promise<number>} - Current database version\n   */\n  async getDatabaseVersion() {\n    try {\n      const result = await this.executeQuery(\n        'SELECT version FROM DatabaseVersion ORDER BY version DESC LIMIT 1'\n      );\n\n      if (result.rows.length === 0) {\n        return 0;\n      }\n\n      return result.rows.item(0).version;\n    } catch (error) {\n      console.error('Error getting database version:', error);\n      return 0;\n    }\n  }\n\n  /**\n   * Get database statistics\n   * @returns {Promise<Object>} - Database statistics\n   */\n  async getDatabaseStats() {\n    try {\n      const tables = await this.executeQuery(\n        \"SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'\"\n      );\n\n      const stats = {\n        version: await this.getDatabaseVersion(),\n        tables: [],\n        totalRecords: 0\n      };\n\n      for (let i = 0; i < tables.rows.length; i++) {\n        const tableName = tables.rows.item(i).name;\n        const countResult = await this.executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`);\n        const count = countResult.rows.item(0).count;\n\n        stats.tables.push({\n          name: tableName,\n          records: count\n        });\n\n        stats.totalRecords += count;\n      }\n\n      return stats;\n    } catch (error) {\n      console.error('Error getting database stats:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Backup the database\n   * @returns {Promise<string>} - Backup file path\n   */\n  async backupDatabase() {\n    try {\n      const FileSystem = require('expo-file-system');\n\n      const dbDir = FileSystem.documentDirectory + 'SQLite/';\n      const dbPath = dbDir + 'znunizaehler.db';\n      const backupPath = FileSystem.documentDirectory + 'backup_' +\n        new Date().toISOString().replace(/[:.]/g, '_') + '.db';\n\n      await FileSystem.copyAsync({\n        from: dbPath,\n        to: backupPath\n      });\n\n      return backupPath;\n    } catch (error) {\n      console.error('Error backing up database:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Reset the database (use with caution)\n   * @returns {Promise<void>}\n   */\n  async resetDatabase() {\n    try {\n      // Close the current connection\n      if (this.db) {\n        this.db._db.close();\n      }\n\n      // Delete the database file\n      const FileSystem = require('expo-file-system');\n      const dbDir = FileSystem.documentDirectory + 'SQLite/';\n      const dbPath = dbDir + 'znunizaehler.db';\n\n      await FileSystem.deleteAsync(dbPath, { idempotent: true });\n\n      // Reinitialize\n      this.db = null;\n      this.initialized = false;\n      await this.init();\n\n      console.log('Database reset successfully');\n    } catch (error) {\n      console.error('Error resetting database:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Close the database connection\n   */\n  close() {\n    if (this.db) {\n      this.db._db.close();\n      this.db = null;\n      this.initialized = false;\n    }\n  }\n}\n\n// Export a singleton instance\nconst dbManager = new DatabaseManager();\nexport default dbManager;\n"], "mappings": ";;;;;AAAA,OAAO,KAAKA,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,YAAY;AAAC,OAAAC,QAAA;AAAA,IAO7BC,eAAe;EACnB,SAAAA,gBAAA,EAAc;IAAAC,eAAA,OAAAD,eAAA;IACZ,IAAI,CAACE,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,WAAW,GAAG,KAAK;EAC1B;EAAC,OAAAC,YAAA,CAAAJ,eAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAMD,aAAa;QACX,IAAI,IAAI,CAACL,WAAW,EAAE;QAEtB,IAAI;UAEF,IAAIJ,QAAQ,CAACU,EAAE,KAAK,KAAK,EAAE;YACzBC,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;YAC1E,IAAI,CAACT,EAAE,GAAG,IAAI,CAACU,mBAAmB,CAAC,CAAC;YACpC,IAAI,CAACT,WAAW,GAAG,IAAI;YACvB;UACF;UAGA,IAAI,CAACD,EAAE,GAAGN,MAAM,CAACiB,YAAY,CAAC,iBAAiB,CAAC;UAGhD,IAAMC,aAAa,SAAS,IAAI,CAACC,mBAAmB,CAAC,CAAC;UAEtD,IAAI,CAACD,aAAa,EAAE;YAClBJ,OAAO,CAACM,GAAG,CAAC,0BAA0B,CAAC;YACvC,MAAM,IAAI,CAACC,mBAAmB,CAAC,CAAC;UAClC,CAAC,MAAM;YACLP,OAAO,CAACM,GAAG,CAAC,8BAA8B,CAAC;YAE3C,MAAM,IAAI,CAACE,gBAAgB,CAAC,CAAC;UAC/B;UAEA,IAAI,CAACf,WAAW,GAAG,IAAI;QACzB,CAAC,CAAC,OAAOgB,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAhCKC,IAAIA,CAAA;QAAA,OAAAb,KAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAJF,IAAI;IAAA;EAAA;IAAAf,GAAA;IAAAC,KAAA;MAAA,IAAAiB,uBAAA,GAAAf,iBAAA,CAuCV,WAA6BgB,MAAM,EAAE;QACnC,IAAI;UACF,IAAMC,MAAM,SAAS,IAAI,CAACC,YAAY,CACpC,wDAAwD,EACxD,CAAC,GAAGF,MAAM,GAAG,CACf,CAAC;UAED,OAAOC,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,GAAG,CAAC;QACtC,CAAC,CAAC,OAAOV,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,qBAAqBK,MAAM,wBAAwB,EAAEL,KAAK,CAAC;UACzE,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SAZKW,sBAAsBA,CAAAC,EAAA;QAAA,OAAAR,uBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBQ,sBAAsB;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA,EAmB5B,SAAAS,mBAAmBA,CAAA,EAAG;MAAA,IAAAiB,KAAA;MACpB,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAK;QAC9BF,KAAI,CAAC9B,EAAE,CAACiC,WAAW,CAAC,UAAAC,EAAE,EAAI;UACxBA,EAAE,CAACC,UAAU,CACX,mEAAmE,EACnE,EAAE,EACF,UAACC,CAAC,EAAEb,MAAM,EAAK;YACbS,OAAO,CAACT,MAAM,CAACE,IAAI,CAACY,MAAM,GAAG,CAAC,CAAC;UACjC,CAAC,EACD,UAACD,CAAC,EAAEnB,KAAK,EAAK;YACZT,OAAO,CAACS,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;YAC/De,OAAO,CAAC,KAAK,CAAC;YACd,OAAO,KAAK;UACd,CACF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAC;IAAA7B,GAAA;IAAAC,KAAA;MAAA,IAAAkC,oBAAA,GAAAhC,iBAAA,CAOD,aAA4B;QAAA,IAAAiC,MAAA;QAC1B,IAAI;UAEF,IAAMC,KAAK,GAAG5C,KAAK,CAAC6C,UAAU,CAACC,OAAO,gCAAgC,CAAC,CAAC;UACxE,MAAMF,KAAK,CAACG,aAAa,CAAC,CAAC;UAE3B,IAAMC,SAAS,SAASjD,UAAU,CAACkD,iBAAiB,CAACL,KAAK,CAACM,QAAQ,CAAC;UACpE,IAAMC,UAAU,GAAGH,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,UAAAC,IAAI;YAAA,OAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAACd,MAAM,GAAG,CAAC;UAAA,EAAC;UAE9E,OAAO,IAAIN,OAAO,CAAC,UAACC,OAAO,EAAEoB,MAAM,EAAK;YACtCb,MAAI,CAACvC,EAAE,CAACiC,WAAW,CACjB,UAAAC,EAAE,EAAI;cACJa,UAAU,CAACM,OAAO,CAAC,UAAAC,SAAS,EAAI;gBAC9BpB,EAAE,CAACC,UAAU,CAACmB,SAAS,EAAE,EAAE,EACzB,YAAM,CAAE,CAAC,EACT,UAAClB,CAAC,EAAEnB,KAAK,EAAK;kBACZT,OAAO,CAACS,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEqC,SAAS,CAAC;kBACjE,OAAO,KAAK;gBACd,CACF,CAAC;cACH,CAAC,CAAC;YACJ,CAAC,EACD,UAAArC,KAAK,EAAI;cACPT,OAAO,CAACS,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;cAChEmC,MAAM,CAACnC,KAAK,CAAC;YACf,CAAC,EACD,YAAM;cACJT,OAAO,CAACM,GAAG,CAAC,mCAAmC,CAAC;cAChDkB,OAAO,CAAC,CAAC;YACX,CACF,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOf,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UACzD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SApCKF,mBAAmBA,CAAA;QAAA,OAAAuB,oBAAA,CAAAnB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBL,mBAAmB;IAAA;EAAA;IAAAZ,GAAA;IAAAC,KAAA;MAAA,IAAAmD,iBAAA,GAAAjD,iBAAA,CA2CzB,aAAyB;QACvB,IAAI;UAEF,IAAMkD,aAAa,SAAS,IAAI,CAAChC,YAAY,CAC3C,mEACF,CAAC;UAED,IAAIgC,aAAa,CAAC/B,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE;YACnC7B,OAAO,CAACC,IAAI,CAAC,6DAA6D,CAAC;YAC3E,MAAM,IAAI,CAACe,YAAY,CACrB,iFACF,CAAC;YACD;UACF;UAEA,IAAMiC,cAAc,GAAGD,aAAa,CAAC/B,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACgC,OAAO;UACzDlD,OAAO,CAACM,GAAG,CAAC,6BAA6B2C,cAAc,EAAE,CAAC;UAG1D,QAAQA,cAAc;YACpB,KAAK,CAAC;YAWN;cAEE;UACJ;QACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAvCKD,gBAAgBA,CAAA;QAAA,OAAAuC,iBAAA,CAAApC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBJ,gBAAgB;IAAA;EAAA;IAAAb,GAAA;IAAAC,KAAA;MAAA,IAAAuD,gBAAA,GAAArD,iBAAA,CAiDtB,WAAsBoD,OAAO,EAAEE,WAAW,EAAEC,OAAO,EAAE;QACnD,IAAI;UAEFA,OAAO,CAACC,IAAI,CAAC;YACXC,GAAG,EAAE,kEAAkE;YACvEC,MAAM,EAAE,CAACN,OAAO,EAAEE,WAAW;UAC/B,CAAC,CAAC;UAGF,MAAM,IAAI,CAACK,kBAAkB,CAACJ,OAAO,CAAC;UAEtCrD,OAAO,CAACM,GAAG,CAAC,gCAAgC4C,OAAO,KAAKE,WAAW,EAAE,CAAC;QACxE,CAAC,CAAC,OAAO3C,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,uCAAuCyC,OAAO,GAAG,EAAEzC,KAAK,CAAC;UACvE,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAhBKiD,eAAeA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAV,gBAAA,CAAAxC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAf8C,eAAe;IAAA;EAAA;IAAA/D,GAAA;IAAAC,KAAA,EAwBrB,SAAAoB,YAAYA,CAACuC,GAAG,EAAe;MAAA,IAAAO,MAAA;MAAA,IAAbN,MAAM,GAAA5C,SAAA,CAAAiB,MAAA,QAAAjB,SAAA,QAAAmD,SAAA,GAAAnD,SAAA,MAAG,EAAE;MAC3B,OAAO,IAAIW,OAAO,CAAC,UAACC,OAAO,EAAEoB,MAAM,EAAK;QACtC,IAAI,CAACkB,MAAI,CAACrE,WAAW,EAAE;UACrBmD,MAAM,CAAC,IAAIoB,KAAK,CAAC,8CAA8C,CAAC,CAAC;UACjE;QACF;QAEAF,MAAI,CAACtE,EAAE,CAACiC,WAAW,CAAC,UAAAC,EAAE,EAAI;UACxBA,EAAE,CAACC,UAAU,CACX4B,GAAG,EACHC,MAAM,EACN,UAAC5B,CAAC,EAAEb,MAAM,EAAK;YACbS,OAAO,CAACT,MAAM,CAAC;UACjB,CAAC,EACD,UAACa,CAAC,EAAEnB,KAAK,EAAK;YACZT,OAAO,CAACS,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;YAClCmC,MAAM,CAACnC,KAAK,CAAC;YACb,OAAO,KAAK;UACd,CACF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAOD,SAAA6D,kBAAkBA,CAACJ,OAAO,EAAE;MAAA,IAAAY,MAAA;MAC1B,OAAO,IAAI1C,OAAO,CAAC,UAACC,OAAO,EAAEoB,MAAM,EAAK;QACtC,IAAI,CAACqB,MAAI,CAACxE,WAAW,EAAE;UACrBmD,MAAM,CAAC,IAAIoB,KAAK,CAAC,8CAA8C,CAAC,CAAC;UACjE;QACF;QAEA,IAAME,OAAO,GAAG,EAAE;QAElBD,MAAI,CAACzE,EAAE,CAACiC,WAAW,CACjB,UAAAC,EAAE,EAAI;UACJ2B,OAAO,CAACR,OAAO,CAAC,UAAAsB,IAAA,EAA0B;YAAA,IAAvBZ,GAAG,GAAAY,IAAA,CAAHZ,GAAG;cAAAa,WAAA,GAAAD,IAAA,CAAEX,MAAM;cAANA,MAAM,GAAAY,WAAA,cAAG,EAAE,GAAAA,WAAA;YACjC1C,EAAE,CAACC,UAAU,CACX4B,GAAG,EACHC,MAAM,EACN,UAAC5B,CAAC,EAAEb,MAAM,EAAK;cACbmD,OAAO,CAACZ,IAAI,CAACvC,MAAM,CAAC;YACtB,CAAC,EACD,UAACa,CAAC,EAAEnB,KAAK,EAAK;cACZT,OAAO,CAACS,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;cAC9C,OAAO,KAAK;YACd,CACF,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,EACD,UAAAA,KAAK,EAAI;UACPT,OAAO,CAACS,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CmC,MAAM,CAACnC,KAAK,CAAC;QACf,CAAC,EACD,YAAM;UACJe,OAAO,CAAC0C,OAAO,CAAC;QAClB,CACF,CAAC;MACH,CAAC,CAAC;IACJ;EAAC;IAAAvE,GAAA;IAAAC,KAAA,EAOD,SAAAyE,MAAMA,CAACC,KAAK,EAAE;MACZ,OAAO,IAAI,CAACtD,YAAY,CAAC,iBAAiBsD,KAAK,6CAA6C,CAAC,CAC1FC,IAAI,CAAC,UAAAxD,MAAM,EAAI;QACd,IAAMyD,KAAK,GAAG,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1D,MAAM,CAACE,IAAI,CAACY,MAAM,EAAE4C,CAAC,EAAE,EAAE;UAC3CD,KAAK,CAAClB,IAAI,CAACvC,MAAM,CAACE,IAAI,CAACC,IAAI,CAACuD,CAAC,CAAC,CAAC;QACjC;QACA,OAAOD,KAAK;MACd,CAAC,CAAC;IACN;EAAC;IAAA7E,GAAA;IAAAC,KAAA,EASD,SAAA8E,OAAOA,CAACJ,KAAK,EAAEK,EAAE,EAAkB;MAAA,IAAhBC,OAAO,GAAAhE,SAAA,CAAAiB,MAAA,QAAAjB,SAAA,QAAAmD,SAAA,GAAAnD,SAAA,MAAG,IAAI;MAC/B,IAAMiE,QAAQ,GAAGD,OAAO,IAAI,GAAGN,KAAK,CAACQ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK;MAC3D,OAAO,IAAI,CAAC9D,YAAY,CACtB,iBAAiBsD,KAAK,UAAUO,QAAQ,+CAA+C,EACvF,CAACF,EAAE,CACL,CAAC,CAACJ,IAAI,CAAC,UAAAxD,MAAM,EAAI;QACf,OAAOA,MAAM,CAACE,IAAI,CAACY,MAAM,GAAG,CAAC,GAAGd,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;MAC5D,CAAC,CAAC;IACJ;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EAQD,SAAAmF,MAAMA,CAACT,KAAK,EAAEU,IAAI,EAAE;MAClB,IAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC;MAC5C,IAAMC,YAAY,GAAGH,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACM,GAAG,CAAC;QAAA,OAAM,GAAG;MAAA,EAAC,CAACF,IAAI,CAAC,IAAI,CAAC;MAChE,IAAMG,MAAM,GAAGL,MAAM,CAACK,MAAM,CAACP,IAAI,CAAC;MAElC,OAAO,IAAI,CAAChE,YAAY,CACtB,eAAesD,KAAK,KAAKW,OAAO,aAAaI,YAAY,GAAG,EAC5DE,MACF,CAAC,CAAChB,IAAI,CAAC,UAAAxD,MAAM,EAAI;QACf,OAAOA,MAAM,CAACyE,QAAQ;MACxB,CAAC,CAAC;IACJ;EAAC;IAAA7F,GAAA;IAAAC,KAAA,EAUD,SAAA6F,MAAMA,CAACnB,KAAK,EAAEK,EAAE,EAAEK,IAAI,EAAkB;MAAA,IAAhBJ,OAAO,GAAAhE,SAAA,CAAAiB,MAAA,QAAAjB,SAAA,QAAAmD,SAAA,GAAAnD,SAAA,MAAG,IAAI;MACpC,IAAMiE,QAAQ,GAAGD,OAAO,IAAI,GAAGN,KAAK,CAACQ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK;MAC3D,IAAMY,SAAS,GAAGR,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACM,GAAG,CAAC,UAAA3F,GAAG;QAAA,OAAI,GAAGA,GAAG,MAAM;MAAA,EAAC,CAACyF,IAAI,CAAC,IAAI,CAAC;MACvE,IAAMG,MAAM,MAAAI,MAAA,CAAAC,kBAAA,CAAOV,MAAM,CAACK,MAAM,CAACP,IAAI,CAAC,IAAEL,EAAE,EAAC;MAE3C,OAAO,IAAI,CAAC3D,YAAY,CACtB,UAAUsD,KAAK,QAAQoB,SAAS,UAAUb,QAAQ,MAAM,EACxDU,MACF,CAAC,CAAChB,IAAI,CAAC,UAAAxD,MAAM,EAAI;QACf,OAAOA,MAAM,CAAC8E,YAAY;MAC5B,CAAC,CAAC;IACJ;EAAC;IAAAlG,GAAA;IAAAC,KAAA,EASD,SAAAkG,UAAUA,CAACxB,KAAK,EAAEK,EAAE,EAAkB;MAAA,IAAhBC,OAAO,GAAAhE,SAAA,CAAAiB,MAAA,QAAAjB,SAAA,QAAAmD,SAAA,GAAAnD,SAAA,MAAG,IAAI;MAClC,IAAMiE,QAAQ,GAAGD,OAAO,IAAI,GAAGN,KAAK,CAACQ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK;MAC3D,OAAO,IAAI,CAAC9D,YAAY,CACtB,UAAUsD,KAAK,0DAA0DO,QAAQ,MAAM,EACvF,CAACF,EAAE,CACL,CAAC,CAACJ,IAAI,CAAC,UAAAxD,MAAM,EAAI;QACf,OAAOA,MAAM,CAAC8E,YAAY;MAC5B,CAAC,CAAC;IACJ;EAAC;IAAAlG,GAAA;IAAAC,KAAA,EASD,SAAAmG,UAAUA,CAACzB,KAAK,EAAEK,EAAE,EAAkB;MAAA,IAAhBC,OAAO,GAAAhE,SAAA,CAAAiB,MAAA,QAAAjB,SAAA,QAAAmD,SAAA,GAAAnD,SAAA,MAAG,IAAI;MAClC,IAAMiE,QAAQ,GAAGD,OAAO,IAAI,GAAGN,KAAK,CAACQ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK;MAC3D,OAAO,IAAI,CAAC9D,YAAY,CACtB,eAAesD,KAAK,UAAUO,QAAQ,MAAM,EAC5C,CAACF,EAAE,CACL,CAAC,CAACJ,IAAI,CAAC,UAAAxD,MAAM,EAAI;QACf,OAAOA,MAAM,CAAC8E,YAAY;MAC5B,CAAC,CAAC;IACJ;EAAC;IAAAlG,GAAA;IAAAC,KAAA,EAQD,SAAAoG,KAAKA,CAAC1B,KAAK,EAAmB;MAAA,IAAjB2B,UAAU,GAAArF,SAAA,CAAAiB,MAAA,QAAAjB,SAAA,QAAAmD,SAAA,GAAAnD,SAAA,MAAG,CAAC,CAAC;MAC1B,IAAIsF,WAAW,GAAG,sCAAsC;MACxD,IAAM1C,MAAM,GAAG,EAAE;MAEjB0B,MAAM,CAACiB,OAAO,CAACF,UAAU,CAAC,CAACpD,OAAO,CAAC,UAAAuD,KAAA,EAAkB;QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,KAAA;UAAhBzG,GAAG,GAAA0G,KAAA;UAAEzG,KAAK,GAAAyG,KAAA;QAC7CH,WAAW,IAAI,QAAQvG,GAAG,MAAM;QAChC6D,MAAM,CAACF,IAAI,CAAC1D,KAAK,CAAC;MACpB,CAAC,CAAC;MAEF,OAAO,IAAI,CAACoB,YAAY,CACtB,iBAAiBsD,KAAK,UAAU4B,WAAW,EAAE,EAC7C1C,MACF,CAAC,CAACe,IAAI,CAAC,UAAAxD,MAAM,EAAI;QACf,IAAMyD,KAAK,GAAG,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1D,MAAM,CAACE,IAAI,CAACY,MAAM,EAAE4C,CAAC,EAAE,EAAE;UAC3CD,KAAK,CAAClB,IAAI,CAACvC,MAAM,CAACE,IAAI,CAACC,IAAI,CAACuD,CAAC,CAAC,CAAC;QACjC;QACA,OAAOD,KAAK;MACd,CAAC,CAAC;IACJ;EAAC;IAAA7E,GAAA;IAAAC,KAAA;MAAA,IAAA2G,mBAAA,GAAAzG,iBAAA,CAMD,aAA2B;QACzB,IAAI;UACF,IAAMiB,MAAM,SAAS,IAAI,CAACC,YAAY,CACpC,mEACF,CAAC;UAED,IAAID,MAAM,CAACE,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO,CAAC;UACV;UAEA,OAAOd,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACgC,OAAO;QACpC,CAAC,CAAC,OAAOzC,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,OAAO,CAAC;QACV;MACF,CAAC;MAAA,SAfK+F,kBAAkBA,CAAA;QAAA,OAAAD,mBAAA,CAAA5F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlB4F,kBAAkB;IAAA;EAAA;IAAA7G,GAAA;IAAAC,KAAA;MAAA,IAAA6G,iBAAA,GAAA3G,iBAAA,CAqBxB,aAAyB;QACvB,IAAI;UACF,IAAM4G,MAAM,SAAS,IAAI,CAAC1F,YAAY,CACpC,gFACF,CAAC;UAED,IAAM2F,KAAK,GAAG;YACZzD,OAAO,QAAQ,IAAI,CAACsD,kBAAkB,CAAC,CAAC;YACxCE,MAAM,EAAE,EAAE;YACVE,YAAY,EAAE;UAChB,CAAC;UAED,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,MAAM,CAACzF,IAAI,CAACY,MAAM,EAAE4C,CAAC,EAAE,EAAE;YAC3C,IAAMoC,SAAS,GAAGH,MAAM,CAACzF,IAAI,CAACC,IAAI,CAACuD,CAAC,CAAC,CAACqC,IAAI;YAC1C,IAAMC,WAAW,SAAS,IAAI,CAAC/F,YAAY,CAAC,iCAAiC6F,SAAS,EAAE,CAAC;YACzF,IAAM1F,KAAK,GAAG4F,WAAW,CAAC9F,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK;YAE5CwF,KAAK,CAACD,MAAM,CAACpD,IAAI,CAAC;cAChBwD,IAAI,EAAED,SAAS;cACfG,OAAO,EAAE7F;YACX,CAAC,CAAC;YAEFwF,KAAK,CAACC,YAAY,IAAIzF,KAAK;UAC7B;UAEA,OAAOwF,KAAK;QACd,CAAC,CAAC,OAAOlG,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SA9BKwG,gBAAgBA,CAAA;QAAA,OAAAR,iBAAA,CAAA9F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBqG,gBAAgB;IAAA;EAAA;IAAAtH,GAAA;IAAAC,KAAA;MAAA,IAAAsH,eAAA,GAAApH,iBAAA,CAoCtB,aAAuB;QACrB,IAAI;UACF,IAAMX,WAAU,GAAG+C,OAAO,CAAC,kBAAkB,CAAC;UAE9C,IAAMiF,KAAK,GAAGhI,WAAU,CAACiI,iBAAiB,GAAG,SAAS;UACtD,IAAMC,MAAM,GAAGF,KAAK,GAAG,iBAAiB;UACxC,IAAMG,UAAU,GAAGnI,WAAU,CAACiI,iBAAiB,GAAG,SAAS,GACzD,IAAIG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC1C,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,KAAK;UAExD,MAAM3F,WAAU,CAACsI,SAAS,CAAC;YACzBC,IAAI,EAAEL,MAAM;YACZM,EAAE,EAAEL;UACN,CAAC,CAAC;UAEF,OAAOA,UAAU;QACnB,CAAC,CAAC,OAAO7G,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAnBKmH,cAAcA,CAAA;QAAA,OAAAV,eAAA,CAAAvG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdgH,cAAc;IAAA;EAAA;IAAAjI,GAAA;IAAAC,KAAA;MAAA,IAAAiI,cAAA,GAAA/H,iBAAA,CAyBpB,aAAsB;QACpB,IAAI;UAEF,IAAI,IAAI,CAACN,EAAE,EAAE;YACX,IAAI,CAACA,EAAE,CAACsI,GAAG,CAACC,KAAK,CAAC,CAAC;UACrB;UAGA,IAAM5I,YAAU,GAAG+C,OAAO,CAAC,kBAAkB,CAAC;UAC9C,IAAMiF,KAAK,GAAGhI,YAAU,CAACiI,iBAAiB,GAAG,SAAS;UACtD,IAAMC,MAAM,GAAGF,KAAK,GAAG,iBAAiB;UAExC,MAAMhI,YAAU,CAAC6I,WAAW,CAACX,MAAM,EAAE;YAAEY,UAAU,EAAE;UAAK,CAAC,CAAC;UAG1D,IAAI,CAACzI,EAAE,GAAG,IAAI;UACd,IAAI,CAACC,WAAW,GAAG,KAAK;UACxB,MAAM,IAAI,CAACiB,IAAI,CAAC,CAAC;UAEjBV,OAAO,CAACM,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAxBKyH,aAAaA,CAAA;QAAA,OAAAL,cAAA,CAAAlH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbsH,aAAa;IAAA;EAAA;IAAAvI,GAAA;IAAAC,KAAA,EA6BnB,SAAAmI,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAACvI,EAAE,EAAE;QACX,IAAI,CAACA,EAAE,CAACsI,GAAG,CAACC,KAAK,CAAC,CAAC;QACnB,IAAI,CAACvI,EAAE,GAAG,IAAI;QACd,IAAI,CAACC,WAAW,GAAG,KAAK;MAC1B;IACF;EAAC;AAAA;AAIH,IAAM0I,SAAS,GAAG,IAAI7I,eAAe,CAAC,CAAC;AACvC,eAAe6I,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}