{"ast": null, "code": "import createElement from \"../createElement\";\nexport default function PickerItem(props) {\n  var color = props.color,\n    label = props.label,\n    testID = props.testID,\n    value = props.value;\n  var style = {\n    color: color\n  };\n  return createElement('option', {\n    children: label,\n    style: style,\n    testID: testID,\n    value: value\n  });\n}", "map": {"version": 3, "names": ["createElement", "PickerItem", "props", "color", "label", "testID", "value", "style", "children"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Picker/PickerItem.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport createElement from '../createElement';\nexport default function PickerItem(props) {\n  var color = props.color,\n    label = props.label,\n    testID = props.testID,\n    value = props.value;\n  var style = {\n    color\n  };\n  return createElement('option', {\n    children: label,\n    style,\n    testID,\n    value\n  });\n}"], "mappings": "AAUA,OAAOA,aAAa;AACpB,eAAe,SAASC,UAAUA,CAACC,KAAK,EAAE;EACxC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACrBC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;EACrB,IAAIC,KAAK,GAAG;IACVJ,KAAK,EAALA;EACF,CAAC;EACD,OAAOH,aAAa,CAAC,QAAQ,EAAE;IAC7BQ,QAAQ,EAAEJ,KAAK;IACfG,KAAK,EAALA,KAAK;IACLF,MAAM,EAANA,MAAM;IACNC,KAAK,EAALA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}