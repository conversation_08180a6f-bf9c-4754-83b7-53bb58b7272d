{"ast": null, "code": "var conversions = require(\"./conversions\");\nvar route = require(\"./route\");\nvar convert = {};\nvar models = Object.keys(conversions);\nfunction wrapRaw(fn) {\n  var wrappedFn = function wrappedFn(args) {\n    if (args === undefined || args === null) {\n      return args;\n    }\n    if (arguments.length > 1) {\n      args = Array.prototype.slice.call(arguments);\n    }\n    return fn(args);\n  };\n  if ('conversion' in fn) {\n    wrappedFn.conversion = fn.conversion;\n  }\n  return wrappedFn;\n}\nfunction wrapRounded(fn) {\n  var wrappedFn = function wrappedFn(args) {\n    if (args === undefined || args === null) {\n      return args;\n    }\n    if (arguments.length > 1) {\n      args = Array.prototype.slice.call(arguments);\n    }\n    var result = fn(args);\n    if (typeof result === 'object') {\n      for (var len = result.length, i = 0; i < len; i++) {\n        result[i] = Math.round(result[i]);\n      }\n    }\n    return result;\n  };\n  if ('conversion' in fn) {\n    wrappedFn.conversion = fn.conversion;\n  }\n  return wrappedFn;\n}\nmodels.forEach(function (fromModel) {\n  convert[fromModel] = {};\n  Object.defineProperty(convert[fromModel], 'channels', {\n    value: conversions[fromModel].channels\n  });\n  Object.defineProperty(convert[fromModel], 'labels', {\n    value: conversions[fromModel].labels\n  });\n  var routes = route(fromModel);\n  var routeModels = Object.keys(routes);\n  routeModels.forEach(function (toModel) {\n    var fn = routes[toModel];\n    convert[fromModel][toModel] = wrapRounded(fn);\n    convert[fromModel][toModel].raw = wrapRaw(fn);\n  });\n});\nmodule.exports = convert;", "map": {"version": 3, "names": ["conversions", "require", "route", "convert", "models", "Object", "keys", "wrapRaw", "fn", "wrappedFn", "args", "undefined", "arguments", "length", "Array", "prototype", "slice", "call", "conversion", "wrapRounded", "result", "len", "i", "Math", "round", "for<PERSON>ach", "fromModel", "defineProperty", "value", "channels", "labels", "routes", "routeModels", "toModel", "raw", "module", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/node_modules/color-convert/index.js"], "sourcesContent": ["var conversions = require('./conversions');\nvar route = require('./route');\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,gBAAgB,CAAC;AAC1C,IAAIC,KAAK,GAAGD,OAAO,UAAU,CAAC;AAE9B,IAAIE,OAAO,GAAG,CAAC,CAAC;AAEhB,IAAIC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC;AAErC,SAASO,OAAOA,CAACC,EAAE,EAAE;EACpB,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAaC,IAAI,EAAE;IAC/B,IAAIA,IAAI,KAAKC,SAAS,IAAID,IAAI,KAAK,IAAI,EAAE;MACxC,OAAOA,IAAI;IACZ;IAEA,IAAIE,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACzBH,IAAI,GAAGI,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IAC7C;IAEA,OAAOJ,EAAE,CAACE,IAAI,CAAC;EAChB,CAAC;EAGD,IAAI,YAAY,IAAIF,EAAE,EAAE;IACvBC,SAAS,CAACS,UAAU,GAAGV,EAAE,CAACU,UAAU;EACrC;EAEA,OAAOT,SAAS;AACjB;AAEA,SAASU,WAAWA,CAACX,EAAE,EAAE;EACxB,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAaC,IAAI,EAAE;IAC/B,IAAIA,IAAI,KAAKC,SAAS,IAAID,IAAI,KAAK,IAAI,EAAE;MACxC,OAAOA,IAAI;IACZ;IAEA,IAAIE,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACzBH,IAAI,GAAGI,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IAC7C;IAEA,IAAIQ,MAAM,GAAGZ,EAAE,CAACE,IAAI,CAAC;IAKrB,IAAI,OAAOU,MAAM,KAAK,QAAQ,EAAE;MAC/B,KAAK,IAAIC,GAAG,GAAGD,MAAM,CAACP,MAAM,EAAES,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;QAClDF,MAAM,CAACE,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,CAACE,CAAC,CAAC,CAAC;MAClC;IACD;IAEA,OAAOF,MAAM;EACd,CAAC;EAGD,IAAI,YAAY,IAAIZ,EAAE,EAAE;IACvBC,SAAS,CAACS,UAAU,GAAGV,EAAE,CAACU,UAAU;EACrC;EAEA,OAAOT,SAAS;AACjB;AAEAL,MAAM,CAACqB,OAAO,CAAC,UAAUC,SAAS,EAAE;EACnCvB,OAAO,CAACuB,SAAS,CAAC,GAAG,CAAC,CAAC;EAEvBrB,MAAM,CAACsB,cAAc,CAACxB,OAAO,CAACuB,SAAS,CAAC,EAAE,UAAU,EAAE;IAACE,KAAK,EAAE5B,WAAW,CAAC0B,SAAS,CAAC,CAACG;EAAQ,CAAC,CAAC;EAC/FxB,MAAM,CAACsB,cAAc,CAACxB,OAAO,CAACuB,SAAS,CAAC,EAAE,QAAQ,EAAE;IAACE,KAAK,EAAE5B,WAAW,CAAC0B,SAAS,CAAC,CAACI;EAAM,CAAC,CAAC;EAE3F,IAAIC,MAAM,GAAG7B,KAAK,CAACwB,SAAS,CAAC;EAC7B,IAAIM,WAAW,GAAG3B,MAAM,CAACC,IAAI,CAACyB,MAAM,CAAC;EAErCC,WAAW,CAACP,OAAO,CAAC,UAAUQ,OAAO,EAAE;IACtC,IAAIzB,EAAE,GAAGuB,MAAM,CAACE,OAAO,CAAC;IAExB9B,OAAO,CAACuB,SAAS,CAAC,CAACO,OAAO,CAAC,GAAGd,WAAW,CAACX,EAAE,CAAC;IAC7CL,OAAO,CAACuB,SAAS,CAAC,CAACO,OAAO,CAAC,CAACC,GAAG,GAAG3B,OAAO,CAACC,EAAE,CAAC;EAC9C,CAAC,CAAC;AACH,CAAC,CAAC;AAEF2B,MAAM,CAACC,OAAO,GAAGjC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}