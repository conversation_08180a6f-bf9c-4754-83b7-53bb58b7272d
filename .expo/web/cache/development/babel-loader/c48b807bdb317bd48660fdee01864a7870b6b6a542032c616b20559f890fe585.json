{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport BackHandler from \"react-native-web/dist/exports/BackHandler\";\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { Appbar, FAB, Portal, Snackbar } from 'react-native-paper';\nimport ConsumptionEntry from \"../components/ConsumptionEntry\";\nimport FoodList from \"../components/FoodList\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar AddConsumptionScreen = function AddConsumptionScreen(_ref) {\n  var route = _ref.route,\n    navigation = _ref.navigation;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _ref2 = route.params || {},\n    consumption = _ref2.consumption,\n    date = _ref2.date;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    showFoodList = _useState2[0],\n    setShowFoodList = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedFoods = _useState4[0],\n    setSelectedFoods = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    snackbarVisible = _useState6[0],\n    setSnackbarVisible = _useState6[1];\n  var _useState7 = useState(''),\n    _useState8 = _slicedToArray(_useState7, 2),\n    snackbarMessage = _useState8[0],\n    setSnackbarMessage = _useState8[1];\n  useEffect(function () {\n    var backHandler = BackHandler.addEventListener('hardwareBackPress', function () {\n      if (showFoodList) {\n        setShowFoodList(false);\n        return true;\n      }\n      return false;\n    });\n    return function () {\n      return backHandler.remove();\n    };\n  }, [showFoodList]);\n  var handleSaveConsumption = function handleSaveConsumption(savedConsumption) {\n    setSnackbarMessage('Consumption saved successfully');\n    setSnackbarVisible(true);\n    setTimeout(function () {\n      navigation.goBack();\n    }, 1500);\n  };\n  var handleCancel = function handleCancel() {\n    navigation.goBack();\n  };\n  var handleSelectFood = function handleSelectFood(food) {\n    var existingIndex = selectedFoods.findIndex(function (f) {\n      return f.id === food.id;\n    });\n    if (existingIndex >= 0) {\n      var updatedFoods = _toConsumableArray(selectedFoods);\n      updatedFoods[existingIndex] = _objectSpread(_objectSpread({}, updatedFoods[existingIndex]), {}, {\n        quantity: updatedFoods[existingIndex].quantity + 100\n      });\n      setSelectedFoods(updatedFoods);\n    } else {\n      setSelectedFoods([].concat(_toConsumableArray(selectedFoods), [_objectSpread(_objectSpread({}, food), {}, {\n        quantity: 100,\n        unit: food.serving_unit || 'g'\n      })]));\n    }\n    setSnackbarMessage(`${food.name} added to meal`);\n    setSnackbarVisible(true);\n  };\n  var handleRemoveFood = function handleRemoveFood(foodId) {\n    setSelectedFoods(selectedFoods.filter(function (food) {\n      return food.id !== foodId;\n    }));\n  };\n  var handleQuantityChange = function handleQuantityChange(foodId, quantity) {\n    setSelectedFoods(selectedFoods.map(function (food) {\n      return food.id === foodId ? _objectSpread(_objectSpread({}, food), {}, {\n        quantity: quantity\n      }) : food;\n    }));\n  };\n  var handleContinueToEntry = function handleContinueToEntry() {\n    setShowFoodList(false);\n  };\n  var renderAppBar = function renderAppBar() {\n    return _jsxs(Appbar.Header, {\n      children: [_jsx(Appbar.BackAction, {\n        onPress: function onPress() {\n          if (showFoodList) {\n            setShowFoodList(false);\n          } else {\n            navigation.goBack();\n          }\n        }\n      }), _jsx(Appbar.Content, {\n        title: showFoodList ? \"Select Foods\" : \"Log Consumption\"\n      }), showFoodList && selectedFoods.length > 0 && _jsx(Appbar.Action, {\n        icon: \"check\",\n        onPress: handleContinueToEntry\n      })]\n    });\n  };\n  return _jsxs(View, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: [renderAppBar(), showFoodList ? _jsxs(View, {\n      style: styles.container,\n      children: [_jsx(FoodList, {\n        onSelectFood: handleSelectFood,\n        onAddFood: function onAddFood() {\n          return navigation.navigate('FoodDetail', {\n            isNew: true\n          });\n        }\n      }), selectedFoods.length > 0 && _jsx(FAB, {\n        icon: \"check\",\n        label: `Continue with ${selectedFoods.length} food${selectedFoods.length > 1 ? 's' : ''}`,\n        style: [styles.fab, {\n          backgroundColor: theme.colors.primary\n        }],\n        onPress: handleContinueToEntry,\n        extended: true\n      })]\n    }) : _jsx(ConsumptionEntry, {\n      consumption: consumption,\n      onSave: handleSaveConsumption,\n      onCancel: handleCancel,\n      onSelectFood: function onSelectFood() {\n        return setShowFoodList(true);\n      },\n      selectedFoods: selectedFoods,\n      onRemoveFood: handleRemoveFood,\n      onQuantityChange: handleQuantityChange,\n      date: date\n    }), _jsx(Portal, {\n      children: _jsx(Snackbar, {\n        visible: snackbarVisible,\n        onDismiss: function onDismiss() {\n          return setSnackbarVisible(false);\n        },\n        duration: 2000,\n        action: {\n          label: 'OK',\n          onPress: function onPress() {\n            return setSnackbarVisible(false);\n          }\n        },\n        children: snackbarMessage\n      })\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0\n  }\n});\nexport default AddConsumptionScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "<PERSON><PERSON><PERSON><PERSON>", "useTheme", "Appbar", "FAB", "Portal", "Snackbar", "ConsumptionEntry", "FoodList", "jsx", "_jsx", "jsxs", "_jsxs", "AddConsumptionScreen", "_ref", "route", "navigation", "_useTheme", "theme", "_ref2", "params", "consumption", "date", "_useState", "_useState2", "_slicedToArray", "showFoodList", "setShowFoodList", "_useState3", "_useState4", "selectedFoods", "setSelectedFoods", "_useState5", "_useState6", "snackbarVisible", "setSnackbarVisible", "_useState7", "_useState8", "snackbarMessage", "setSnackbarMessage", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "remove", "handleSaveConsumption", "savedConsumption", "setTimeout", "goBack", "handleCancel", "handleSelectFood", "food", "existingIndex", "findIndex", "f", "id", "updatedFoods", "_toConsumableArray", "_objectSpread", "quantity", "concat", "unit", "serving_unit", "name", "handleRemoveFood", "foodId", "filter", "handleQuantityChange", "map", "handleContinueToEntry", "renderAppBar", "Header", "children", "BackAction", "onPress", "Content", "title", "length", "Action", "icon", "style", "styles", "container", "backgroundColor", "colors", "background", "onSelectFood", "onAddFood", "navigate", "isNew", "label", "fab", "primary", "extended", "onSave", "onCancel", "onRemoveFood", "onQuantityChange", "visible", "on<PERSON><PERSON><PERSON>", "duration", "action", "create", "flex", "position", "margin", "right", "bottom"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/screens/AddConsumptionScreen.js"], "sourcesContent": ["/**\n * Add Consumption Screen for Znü<PERSON>Zähler\n * Allows users to add or edit food consumption\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, BackHandler } from 'react-native';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { Appbar, FAB, Portal, Snackbar } from 'react-native-paper';\nimport ConsumptionEntry from '../components/ConsumptionEntry';\nimport FoodList from '../components/FoodList';\n\n/**\n * Add Consumption Screen Component\n * @param {Object} props - Component props\n * @param {Object} props.route - Route object\n * @param {Object} props.navigation - Navigation object\n * @returns {JSX.Element} - Add consumption screen component\n */\nconst AddConsumptionScreen = ({ route, navigation }) => {\n  const { theme } = useTheme();\n  const { consumption, date } = route.params || {};\n  const [showFoodList, setShowFoodList] = useState(false);\n  const [selectedFoods, setSelectedFoods] = useState([]);\n  const [snackbarVisible, setSnackbarVisible] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n\n  // Handle back button press\n  useEffect(() => {\n    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {\n      if (showFoodList) {\n        setShowFoodList(false);\n        return true;\n      }\n      return false;\n    });\n\n    return () => backHandler.remove();\n  }, [showFoodList]);\n\n  // Handle save consumption\n  const handleSaveConsumption = (savedConsumption) => {\n    setSnackbarMessage('Consumption saved successfully');\n    setSnackbarVisible(true);\n\n    // Navigate back after a short delay\n    setTimeout(() => {\n      navigation.goBack();\n    }, 1500);\n  };\n\n  // Handle cancel\n  const handleCancel = () => {\n    navigation.goBack();\n  };\n\n  // Handle select food\n  const handleSelectFood = (food) => {\n    // Check if food is already selected\n    const existingIndex = selectedFoods.findIndex(f => f.id === food.id);\n\n    if (existingIndex >= 0) {\n      // Update quantity if already selected\n      const updatedFoods = [...selectedFoods];\n      updatedFoods[existingIndex] = {\n        ...updatedFoods[existingIndex],\n        quantity: updatedFoods[existingIndex].quantity + 100\n      };\n      setSelectedFoods(updatedFoods);\n    } else {\n      // Add new food with default quantity\n      setSelectedFoods([...selectedFoods, {\n        ...food,\n        quantity: 100,\n        unit: food.serving_unit || 'g'\n      }]);\n    }\n\n    // Show snackbar\n    setSnackbarMessage(`${food.name} added to meal`);\n    setSnackbarVisible(true);\n  };\n\n  // Handle remove food\n  const handleRemoveFood = (foodId) => {\n    setSelectedFoods(selectedFoods.filter(food => food.id !== foodId));\n  };\n\n  // Handle quantity change\n  const handleQuantityChange = (foodId, quantity) => {\n    setSelectedFoods(selectedFoods.map(food =>\n      food.id === foodId ? { ...food, quantity } : food\n    ));\n  };\n\n  // Handle continue to entry\n  const handleContinueToEntry = () => {\n    setShowFoodList(false);\n  };\n\n  // Render app bar\n  const renderAppBar = () => (\n    <Appbar.Header>\n      <Appbar.BackAction onPress={() => {\n        if (showFoodList) {\n          setShowFoodList(false);\n        } else {\n          navigation.goBack();\n        }\n      }} />\n      <Appbar.Content title={showFoodList ? \"Select Foods\" : \"Log Consumption\"} />\n      {showFoodList && selectedFoods.length > 0 && (\n        <Appbar.Action\n          icon=\"check\"\n          onPress={handleContinueToEntry}\n        />\n      )}\n    </Appbar.Header>\n  );\n\n  return (\n    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      {renderAppBar()}\n\n      {showFoodList ? (\n        <View style={styles.container}>\n          <FoodList\n            onSelectFood={handleSelectFood}\n            onAddFood={() => navigation.navigate('FoodDetail', { isNew: true })}\n          />\n\n          {selectedFoods.length > 0 && (\n            <FAB\n              icon=\"check\"\n              label={`Continue with ${selectedFoods.length} food${selectedFoods.length > 1 ? 's' : ''}`}\n              style={[styles.fab, { backgroundColor: theme.colors.primary }]}\n              onPress={handleContinueToEntry}\n              extended\n            />\n          )}\n        </View>\n      ) : (\n        <ConsumptionEntry\n          consumption={consumption}\n          onSave={handleSaveConsumption}\n          onCancel={handleCancel}\n          onSelectFood={() => setShowFoodList(true)}\n          selectedFoods={selectedFoods}\n          onRemoveFood={handleRemoveFood}\n          onQuantityChange={handleQuantityChange}\n          date={date}\n        />\n      )}\n\n      <Portal>\n        <Snackbar\n          visible={snackbarVisible}\n          onDismiss={() => setSnackbarVisible(false)}\n          duration={2000}\n          action={{\n            label: 'OK',\n            onPress: () => setSnackbarVisible(false),\n          }}\n        >\n          {snackbarMessage}\n        </Snackbar>\n      </Portal>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0,\n  },\n});\n\nexport default AddConsumptionScreen;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,WAAA;AAEnD,SAASC,QAAQ;AACjB,SAASC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AAClE,OAAOC,gBAAgB;AACvB,OAAOC,QAAQ;AAA+B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAS9C,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,IAAA,EAA8B;EAAA,IAAxBC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;EAC/C,IAAAC,SAAA,GAAkBf,QAAQ,CAAC,CAAC;IAApBgB,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,KAAA,GAA8BJ,KAAK,CAACK,MAAM,IAAI,CAAC,CAAC;IAAxCC,WAAW,GAAAF,KAAA,CAAXE,WAAW;IAAEC,IAAI,GAAAH,KAAA,CAAJG,IAAI;EACzB,IAAAC,SAAA,GAAwC1B,QAAQ,CAAC,KAAK,CAAC;IAAA2B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAhDG,YAAY,GAAAF,UAAA;IAAEG,eAAe,GAAAH,UAAA;EACpC,IAAAI,UAAA,GAA0C/B,QAAQ,CAAC,EAAE,CAAC;IAAAgC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA/CE,aAAa,GAAAD,UAAA;IAAEE,gBAAgB,GAAAF,UAAA;EACtC,IAAAG,UAAA,GAA8CnC,QAAQ,CAAC,KAAK,CAAC;IAAAoC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAtDE,eAAe,GAAAD,UAAA;IAAEE,kBAAkB,GAAAF,UAAA;EAC1C,IAAAG,UAAA,GAA8CvC,QAAQ,CAAC,EAAE,CAAC;IAAAwC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAAnDE,eAAe,GAAAD,UAAA;IAAEE,kBAAkB,GAAAF,UAAA;EAG1CvC,SAAS,CAAC,YAAM;IACd,IAAM0C,WAAW,GAAGvC,WAAW,CAACwC,gBAAgB,CAAC,mBAAmB,EAAE,YAAM;MAC1E,IAAIf,YAAY,EAAE;QAChBC,eAAe,CAAC,KAAK,CAAC;QACtB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC;IAEF,OAAO;MAAA,OAAMa,WAAW,CAACE,MAAM,CAAC,CAAC;IAAA;EACnC,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;EAGlB,IAAMiB,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,gBAAgB,EAAK;IAClDL,kBAAkB,CAAC,gCAAgC,CAAC;IACpDJ,kBAAkB,CAAC,IAAI,CAAC;IAGxBU,UAAU,CAAC,YAAM;MACf7B,UAAU,CAAC8B,MAAM,CAAC,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAGD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB/B,UAAU,CAAC8B,MAAM,CAAC,CAAC;EACrB,CAAC;EAGD,IAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;IAEjC,IAAMC,aAAa,GAAGpB,aAAa,CAACqB,SAAS,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACI,EAAE;IAAA,EAAC;IAEpE,IAAIH,aAAa,IAAI,CAAC,EAAE;MAEtB,IAAMI,YAAY,GAAAC,kBAAA,CAAOzB,aAAa,CAAC;MACvCwB,YAAY,CAACJ,aAAa,CAAC,GAAAM,aAAA,CAAAA,aAAA,KACtBF,YAAY,CAACJ,aAAa,CAAC;QAC9BO,QAAQ,EAAEH,YAAY,CAACJ,aAAa,CAAC,CAACO,QAAQ,GAAG;MAAG,EACrD;MACD1B,gBAAgB,CAACuB,YAAY,CAAC;IAChC,CAAC,MAAM;MAELvB,gBAAgB,IAAA2B,MAAA,CAAAH,kBAAA,CAAKzB,aAAa,IAAA0B,aAAA,CAAAA,aAAA,KAC7BP,IAAI;QACPQ,QAAQ,EAAE,GAAG;QACbE,IAAI,EAAEV,IAAI,CAACW,YAAY,IAAI;MAAG,IAC9B,CAAC;IACL;IAGArB,kBAAkB,CAAC,GAAGU,IAAI,CAACY,IAAI,gBAAgB,CAAC;IAChD1B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAGD,IAAM2B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,MAAM,EAAK;IACnChC,gBAAgB,CAACD,aAAa,CAACkC,MAAM,CAAC,UAAAf,IAAI;MAAA,OAAIA,IAAI,CAACI,EAAE,KAAKU,MAAM;IAAA,EAAC,CAAC;EACpE,CAAC;EAGD,IAAME,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIF,MAAM,EAAEN,QAAQ,EAAK;IACjD1B,gBAAgB,CAACD,aAAa,CAACoC,GAAG,CAAC,UAAAjB,IAAI;MAAA,OACrCA,IAAI,CAACI,EAAE,KAAKU,MAAM,GAAAP,aAAA,CAAAA,aAAA,KAAQP,IAAI;QAAEQ,QAAQ,EAARA;MAAQ,KAAKR,IAAI;IAAA,CACnD,CAAC,CAAC;EACJ,CAAC;EAGD,IAAMkB,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClCxC,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAGD,IAAMyC,YAAY,GAAG,SAAfA,YAAYA,CAAA;IAAA,OAChBxD,KAAA,CAACT,MAAM,CAACkE,MAAM;MAAAC,QAAA,GACZ5D,IAAA,CAACP,MAAM,CAACoE,UAAU;QAACC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;UAChC,IAAI9C,YAAY,EAAE;YAChBC,eAAe,CAAC,KAAK,CAAC;UACxB,CAAC,MAAM;YACLX,UAAU,CAAC8B,MAAM,CAAC,CAAC;UACrB;QACF;MAAE,CAAE,CAAC,EACLpC,IAAA,CAACP,MAAM,CAACsE,OAAO;QAACC,KAAK,EAAEhD,YAAY,GAAG,cAAc,GAAG;MAAkB,CAAE,CAAC,EAC3EA,YAAY,IAAII,aAAa,CAAC6C,MAAM,GAAG,CAAC,IACvCjE,IAAA,CAACP,MAAM,CAACyE,MAAM;QACZC,IAAI,EAAC,OAAO;QACZL,OAAO,EAAEL;MAAsB,CAChC,CACF;IAAA,CACY,CAAC;EAAA,CACjB;EAED,OACEvD,KAAA,CAACZ,IAAI;IAAC8E,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAE/D,KAAK,CAACgE,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAb,QAAA,GAC3EF,YAAY,CAAC,CAAC,EAEd1C,YAAY,GACXd,KAAA,CAACZ,IAAI;MAAC8E,KAAK,EAAEC,MAAM,CAACC,SAAU;MAAAV,QAAA,GAC5B5D,IAAA,CAACF,QAAQ;QACP4E,YAAY,EAAEpC,gBAAiB;QAC/BqC,SAAS,EAAE,SAAXA,SAASA,CAAA;UAAA,OAAQrE,UAAU,CAACsE,QAAQ,CAAC,YAAY,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAC,CAAC;QAAA;MAAC,CACrE,CAAC,EAEDzD,aAAa,CAAC6C,MAAM,GAAG,CAAC,IACvBjE,IAAA,CAACN,GAAG;QACFyE,IAAI,EAAC,OAAO;QACZW,KAAK,EAAE,iBAAiB1D,aAAa,CAAC6C,MAAM,QAAQ7C,aAAa,CAAC6C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAG;QAC1FG,KAAK,EAAE,CAACC,MAAM,CAACU,GAAG,EAAE;UAAER,eAAe,EAAE/D,KAAK,CAACgE,MAAM,CAACQ;QAAQ,CAAC,CAAE;QAC/DlB,OAAO,EAAEL,qBAAsB;QAC/BwB,QAAQ;MAAA,CACT,CACF;IAAA,CACG,CAAC,GAEPjF,IAAA,CAACH,gBAAgB;MACfc,WAAW,EAAEA,WAAY;MACzBuE,MAAM,EAAEjD,qBAAsB;MAC9BkD,QAAQ,EAAE9C,YAAa;MACvBqC,YAAY,EAAE,SAAdA,YAAYA,CAAA;QAAA,OAAQzD,eAAe,CAAC,IAAI,CAAC;MAAA,CAAC;MAC1CG,aAAa,EAAEA,aAAc;MAC7BgE,YAAY,EAAEhC,gBAAiB;MAC/BiC,gBAAgB,EAAE9B,oBAAqB;MACvC3C,IAAI,EAAEA;IAAK,CACZ,CACF,EAEDZ,IAAA,CAACL,MAAM;MAAAiE,QAAA,EACL5D,IAAA,CAACJ,QAAQ;QACP0F,OAAO,EAAE9D,eAAgB;QACzB+D,SAAS,EAAE,SAAXA,SAASA,CAAA;UAAA,OAAQ9D,kBAAkB,CAAC,KAAK,CAAC;QAAA,CAAC;QAC3C+D,QAAQ,EAAE,IAAK;QACfC,MAAM,EAAE;UACNX,KAAK,EAAE,IAAI;UACXhB,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQrC,kBAAkB,CAAC,KAAK,CAAC;UAAA;QAC1C,CAAE;QAAAmC,QAAA,EAEDhC;MAAe,CACR;IAAC,CACL,CAAC;EAAA,CACL,CAAC;AAEX,CAAC;AAED,IAAMyC,MAAM,GAAGhF,UAAU,CAACqG,MAAM,CAAC;EAC/BpB,SAAS,EAAE;IACTqB,IAAI,EAAE;EACR,CAAC;EACDZ,GAAG,EAAE;IACHa,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAe5F,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}