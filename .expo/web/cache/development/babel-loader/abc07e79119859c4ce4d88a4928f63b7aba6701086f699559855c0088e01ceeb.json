{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _common = require(\"@react-native-vector-icons/common\");\nvar _MaterialDesignIcons = _interopRequireDefault(require(\"../../glyphmaps/MaterialDesignIcons.json\"));\nfunction _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    default: e\n  };\n}\nvar Icon = (0, _common.createIconSet)(_MaterialDesignIcons.default, {\n  postScriptName: 'MaterialDesignIcons',\n  fontFileName: 'MaterialDesignIcons.ttf',\n  fontSource: require(\"../../fonts/MaterialDesignIcons.ttf\")\n});\nvar _default = exports.default = Icon;", "map": {"version": 3, "names": ["_common", "require", "_MaterialDesignIcons", "_interopRequireDefault", "e", "__esModule", "default", "Icon", "createIconSet", "postScriptName", "fontFileName", "fontSource", "_default", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-vector-icons/material-design-icons/src/index.ts"], "sourcesContent": ["// NOTE:This file was generated from packages/generator-react-native-vector-icons/src/app/templates\n// If you're contributing to react-native-vector-icons, make the change there, otherwise it'll be lost\n\n/**\n * This is a generated file. If you modify it manually, your changes will be lost!\n * Instead, modify the template in `generator-react-native-vector-icons`.\n *\n * MaterialDesignIcons icon set component.\n * Usage: <MaterialDesignIcons name=\"icon-name\" size={20} color=\"#4F8EF7\" />\n */\n\nimport { createIconSet } from '@react-native-vector-icons/common';\nimport glyphMap from '../glyphmaps/MaterialDesignIcons.json';\n\nconst Icon = createIconSet(glyphMap, {\n  postScriptName: 'MaterialDesignIcons',\n  fontFileName: 'MaterialDesignIcons.ttf',\n  fontSource: require('../fonts/MaterialDesignIcons.ttf'), // eslint-disable-line @typescript-eslint/no-require-imports, global-require\n});\n\nexport default Icon;\n"], "mappings": ";;;;;;AAWA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA6D,SAAAE,uBAAAC,CAAA;EAAA,OAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA;IAAAE,OAAA,EAAAF;EAAA;AAAA;AAE7D,IAAMG,IAAI,GAAG,IAAAP,OAAA,CAAAQ,aAAa,EAACN,oBAAA,CAAAI,OAAQ,EAAE;EACnCG,cAAc,EAAE,qBAAqB;EACrCC,YAAY,EAAE,yBAAyB;EACvCC,UAAU,EAAEV,OAAO,sCAAsC;AAC3D,CAAC,CAAC;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAAP,OAAA,GAEYC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}