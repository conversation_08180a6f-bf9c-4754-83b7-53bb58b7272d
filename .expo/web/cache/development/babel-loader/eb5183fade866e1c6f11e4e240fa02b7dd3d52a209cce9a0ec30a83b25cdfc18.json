{"ast": null, "code": "var byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex[i] = (i + 0x100).toString(16).substr(1);\n}\nfunction bytesToUuid(buf, offset) {\n  var i = offset || 0;\n  var bth = byteToHex;\n  return [bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]]].join('');\n}\nmodule.exports = bytesToUuid;", "map": {"version": 3, "names": ["byteToHex", "i", "toString", "substr", "bytesToUuid", "buf", "offset", "bth", "join", "module", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-constants/node_modules/uuid/lib/bytesToUuid.js"], "sourcesContent": ["/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\nvar byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex[i] = (i + 0x100).toString(16).substr(1);\n}\n\nfunction bytesToUuid(buf, offset) {\n  var i = offset || 0;\n  var bth = byteToHex;\n  // join used to fix memory issue caused by concatenation: https://bugs.chromium.org/p/v8/issues/detail?id=3175#c4\n  return ([\n    bth[buf[i++]], bth[buf[i++]],\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]],\n    bth[buf[i++]], bth[buf[i++]],\n    bth[buf[i++]], bth[buf[i++]]\n  ]).join('');\n}\n\nmodule.exports = bytesToUuid;\n"], "mappings": "AAIA,IAAIA,SAAS,GAAG,EAAE;AAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;EAC5BD,SAAS,CAACC,CAAC,CAAC,GAAG,CAACA,CAAC,GAAG,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;AACnD;AAEA,SAASC,WAAWA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAChC,IAAIL,CAAC,GAAGK,MAAM,IAAI,CAAC;EACnB,IAAIC,GAAG,GAAGP,SAAS;EAEnB,OAAQ,CACNO,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAC5BM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EACjCM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EACjCM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EACjCM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EACjCM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAC5BM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAC5BM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EAAEM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,CAC7B,CAAEO,IAAI,CAAC,EAAE,CAAC;AACb;AAEAC,MAAM,CAACC,OAAO,GAAGN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}