{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport invariant from 'fbjs/lib/invariant';\nvar Share = function () {\n  function Share() {\n    _classCallCheck(this, Share);\n  }\n  return _createClass(Share, null, [{\n    key: \"share\",\n    value: function share(content, options) {\n      if (options === void 0) {\n        options = {};\n      }\n      invariant(typeof content === 'object' && content !== null, 'Content to share must be a valid object');\n      invariant(typeof content.url === 'string' || typeof content.message === 'string', 'At least one of URL and message is required');\n      invariant(typeof options === 'object' && options !== null, 'Options must be a valid object');\n      invariant(!content.title || typeof content.title === 'string', 'Invalid title: title should be a string.');\n      if (window.navigator.share !== undefined) {\n        return window.navigator.share({\n          title: content.title,\n          text: content.message,\n          url: content.url\n        });\n      } else {\n        return Promise.reject(new Error('Share is not supported in this browser'));\n      }\n    }\n  }, {\n    key: \"sharedAction\",\n    get: function get() {\n      return 'sharedAction';\n    }\n  }, {\n    key: \"dismissedAction\",\n    get: function get() {\n      return 'dismissedAction';\n    }\n  }]);\n}();\nexport default Share;", "map": {"version": 3, "names": ["invariant", "Share", "_classCallCheck", "_createClass", "key", "value", "share", "content", "options", "url", "message", "title", "window", "navigator", "undefined", "text", "Promise", "reject", "Error", "get"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Share/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport invariant from 'fbjs/lib/invariant';\nclass Share {\n  static share(content, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    invariant(typeof content === 'object' && content !== null, 'Content to share must be a valid object');\n    invariant(typeof content.url === 'string' || typeof content.message === 'string', 'At least one of URL and message is required');\n    invariant(typeof options === 'object' && options !== null, 'Options must be a valid object');\n    invariant(!content.title || typeof content.title === 'string', 'Invalid title: title should be a string.');\n    if (window.navigator.share !== undefined) {\n      return window.navigator.share({\n        title: content.title,\n        text: content.message,\n        url: content.url\n      });\n    } else {\n      return Promise.reject(new Error('Share is not supported in this browser'));\n    }\n  }\n\n  /**\n   * The content was successfully shared.\n   */\n  static get sharedAction() {\n    return 'sharedAction';\n  }\n\n  /**\n   * The dialog has been dismissed.\n   * @platform ios\n   */\n  static get dismissedAction() {\n    return 'dismissedAction';\n  }\n}\nexport default Share;"], "mappings": ";;AAUA,OAAOA,SAAS,MAAM,oBAAoB;AAAC,IACrCC,KAAK;EAAA,SAAAA,MAAA;IAAAC,eAAA,OAAAD,KAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,KAAA;IAAAG,GAAA;IAAAC,KAAA,EACT,SAAOC,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAE;MAC7B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;QACtBA,OAAO,GAAG,CAAC,CAAC;MACd;MACAR,SAAS,CAAC,OAAOO,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,yCAAyC,CAAC;MACrGP,SAAS,CAAC,OAAOO,OAAO,CAACE,GAAG,KAAK,QAAQ,IAAI,OAAOF,OAAO,CAACG,OAAO,KAAK,QAAQ,EAAE,6CAA6C,CAAC;MAChIV,SAAS,CAAC,OAAOQ,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,gCAAgC,CAAC;MAC5FR,SAAS,CAAC,CAACO,OAAO,CAACI,KAAK,IAAI,OAAOJ,OAAO,CAACI,KAAK,KAAK,QAAQ,EAAE,0CAA0C,CAAC;MAC1G,IAAIC,MAAM,CAACC,SAAS,CAACP,KAAK,KAAKQ,SAAS,EAAE;QACxC,OAAOF,MAAM,CAACC,SAAS,CAACP,KAAK,CAAC;UAC5BK,KAAK,EAAEJ,OAAO,CAACI,KAAK;UACpBI,IAAI,EAAER,OAAO,CAACG,OAAO;UACrBD,GAAG,EAAEF,OAAO,CAACE;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOO,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,wCAAwC,CAAC,CAAC;MAC5E;IACF;EAAC;IAAAd,GAAA;IAAAe,GAAA,EAKD,SAAAA,IAAA,EAA0B;MACxB,OAAO,cAAc;IACvB;EAAC;IAAAf,GAAA;IAAAe,GAAA,EAMD,SAAAA,IAAA,EAA6B;MAC3B,OAAO,iBAAiB;IAC1B;EAAC;AAAA;AAEH,eAAelB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}