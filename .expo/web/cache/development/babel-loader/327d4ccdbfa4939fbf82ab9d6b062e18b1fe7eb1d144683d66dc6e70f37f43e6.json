{"ast": null, "code": "import * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nvar CSSTransitions = {\n  WebkitTransition: 'webkitTransitionEnd',\n  Transition: 'transitionEnd',\n  MozTransition: 'transitionend',\n  MSTransition: 'msTransitionEnd',\n  OTransition: 'oTransitionEnd'\n};\nexport function NativeSafeAreaProvider(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    onInsetsChange = _ref.onInsetsChange;\n  React.useEffect(function () {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    var element = createContextElement();\n    document.body.appendChild(element);\n    var onEnd = function onEnd() {\n      var _window$getComputedSt = window.getComputedStyle(element),\n        paddingTop = _window$getComputedSt.paddingTop,\n        paddingBottom = _window$getComputedSt.paddingBottom,\n        paddingLeft = _window$getComputedSt.paddingLeft,\n        paddingRight = _window$getComputedSt.paddingRight;\n      var insets = {\n        top: paddingTop ? parseInt(paddingTop, 10) : 0,\n        bottom: paddingBottom ? parseInt(paddingBottom, 10) : 0,\n        left: paddingLeft ? parseInt(paddingLeft, 10) : 0,\n        right: paddingRight ? parseInt(paddingRight, 10) : 0\n      };\n      var frame = {\n        x: 0,\n        y: 0,\n        width: document.documentElement.offsetWidth,\n        height: document.documentElement.offsetHeight\n      };\n      onInsetsChange({\n        nativeEvent: {\n          insets: insets,\n          frame: frame\n        }\n      });\n    };\n    element.addEventListener(getSupportedTransitionEvent(), onEnd);\n    onEnd();\n    return function () {\n      document.body.removeChild(element);\n      element.removeEventListener(getSupportedTransitionEvent(), onEnd);\n    };\n  }, [onInsetsChange]);\n  return React.createElement(View, {\n    style: style\n  }, children);\n}\nvar _supportedTransitionEvent = null;\nfunction getSupportedTransitionEvent() {\n  if (_supportedTransitionEvent != null) {\n    return _supportedTransitionEvent;\n  }\n  var element = document.createElement('invalidtype');\n  _supportedTransitionEvent = CSSTransitions.Transition;\n  for (var key in CSSTransitions) {\n    if (element.style[key] !== undefined) {\n      _supportedTransitionEvent = CSSTransitions[key];\n      break;\n    }\n  }\n  return _supportedTransitionEvent;\n}\nvar _supportedEnv = null;\nfunction getSupportedEnv() {\n  if (_supportedEnv !== null) {\n    return _supportedEnv;\n  }\n  var _window = window,\n    CSS = _window.CSS;\n  if (CSS && CSS.supports && CSS.supports('top: constant(safe-area-inset-top)')) {\n    _supportedEnv = 'constant';\n  } else {\n    _supportedEnv = 'env';\n  }\n  return _supportedEnv;\n}\nfunction getInset(side) {\n  return `${getSupportedEnv()}(safe-area-inset-${side})`;\n}\nfunction createContextElement() {\n  var element = document.createElement('div');\n  var style = element.style;\n  style.position = 'fixed';\n  style.left = '0';\n  style.top = '0';\n  style.width = '0';\n  style.height = '0';\n  style.zIndex = '-1';\n  style.overflow = 'hidden';\n  style.visibility = 'hidden';\n  style.transitionDuration = '0.05s';\n  style.transitionProperty = 'padding';\n  style.transitionDelay = '0s';\n  style.paddingTop = getInset('top');\n  style.paddingBottom = getInset('bottom');\n  style.paddingLeft = getInset('left');\n  style.paddingRight = getInset('right');\n  return element;\n}", "map": {"version": 3, "names": ["React", "View", "CSSTransitions", "WebkitTransition", "Transition", "MozTransition", "MSTransition", "OTransition", "NativeSafeAreaProvider", "_ref", "children", "style", "onInsetsChange", "useEffect", "document", "element", "createContextElement", "body", "append<PERSON><PERSON><PERSON>", "onEnd", "_window$getComputedSt", "window", "getComputedStyle", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "insets", "top", "parseInt", "bottom", "left", "right", "frame", "x", "y", "width", "documentElement", "offsetWidth", "height", "offsetHeight", "nativeEvent", "addEventListener", "getSupportedTransitionEvent", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "createElement", "_supportedTransitionEvent", "key", "undefined", "_supportedEnv", "getSupportedEnv", "_window", "CSS", "supports", "getInset", "side", "position", "zIndex", "overflow", "visibility", "transitionDuration", "transitionProperty", "transitionDelay"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-safe-area-context/src/NativeSafeAreaProvider.web.tsx"], "sourcesContent": ["/* eslint-env browser */\n\nimport * as React from 'react';\nimport { View } from 'react-native';\nimport type { NativeSafeAreaProviderProps } from './SafeArea.types';\n\n/**\n * TODO:\n * Currently insets and frame are based on the window and are not\n * relative to the provider view. This is inconsistent with iOS and Android.\n * However in most cases if the provider view covers the screen this is not\n * an issue.\n */\n\nconst CSSTransitions: Record<string, string> = {\n  WebkitTransition: 'webkitTransitionEnd',\n  Transition: 'transitionEnd',\n  MozTransition: 'transitionend',\n  MSTransition: 'msTransitionEnd',\n  OTransition: 'oTransitionEnd',\n};\n\nexport function NativeSafeAreaProvider({\n  children,\n  style,\n  onInsetsChange,\n}: NativeSafeAreaProviderProps) {\n  React.useEffect(() => {\n    // Skip for SSR.\n    if (typeof document === 'undefined') {\n      return;\n    }\n\n    const element = createContextElement();\n    document.body.appendChild(element);\n    const onEnd = () => {\n      const { paddingTop, paddingBottom, paddingLeft, paddingRight } =\n        window.getComputedStyle(element);\n\n      const insets = {\n        top: paddingTop ? parseInt(paddingTop, 10) : 0,\n        bottom: paddingBottom ? parseInt(paddingBottom, 10) : 0,\n        left: paddingLeft ? parseInt(paddingLeft, 10) : 0,\n        right: paddingRight ? parseInt(paddingRight, 10) : 0,\n      };\n      const frame = {\n        x: 0,\n        y: 0,\n        width: document.documentElement.offsetWidth,\n        height: document.documentElement.offsetHeight,\n      };\n      // @ts-ignore: missing properties\n      onInsetsChange({ nativeEvent: { insets, frame } });\n    };\n    element.addEventListener(getSupportedTransitionEvent(), onEnd);\n    onEnd();\n    return () => {\n      document.body.removeChild(element);\n      element.removeEventListener(getSupportedTransitionEvent(), onEnd);\n    };\n  }, [onInsetsChange]);\n\n  return <View style={style}>{children}</View>;\n}\n\nlet _supportedTransitionEvent: string | null | undefined = null;\nfunction getSupportedTransitionEvent(): string {\n  if (_supportedTransitionEvent != null) {\n    return _supportedTransitionEvent;\n  }\n  const element = document.createElement('invalidtype');\n\n  _supportedTransitionEvent = CSSTransitions.Transition;\n  for (const key in CSSTransitions) {\n    if (element.style[key as keyof CSSStyleDeclaration] !== undefined) {\n      _supportedTransitionEvent = CSSTransitions[key];\n      break;\n    }\n  }\n  return _supportedTransitionEvent as string;\n}\n\ntype CssEnv = 'constant' | 'env';\n\nlet _supportedEnv: CssEnv | null = null;\nfunction getSupportedEnv(): CssEnv {\n  if (_supportedEnv !== null) {\n    return _supportedEnv;\n  }\n  const { CSS } = window;\n  if (\n    CSS &&\n    CSS.supports &&\n    CSS.supports('top: constant(safe-area-inset-top)')\n  ) {\n    _supportedEnv = 'constant';\n  } else {\n    _supportedEnv = 'env';\n  }\n  return _supportedEnv;\n}\n\nfunction getInset(side: string): string {\n  return `${getSupportedEnv()}(safe-area-inset-${side})`;\n}\n\nfunction createContextElement(): HTMLElement {\n  const element = document.createElement('div');\n  const { style } = element;\n  style.position = 'fixed';\n  style.left = '0';\n  style.top = '0';\n  style.width = '0';\n  style.height = '0';\n  style.zIndex = '-1';\n  style.overflow = 'hidden';\n  style.visibility = 'hidden';\n  // Bacon: Anything faster than this and the callback will be invoked too early with the wrong insets\n  style.transitionDuration = '0.05s';\n  style.transitionProperty = 'padding';\n  style.transitionDelay = '0s';\n  style.paddingTop = getInset('top');\n  style.paddingBottom = getInset('bottom');\n  style.paddingLeft = getInset('left');\n  style.paddingRight = getInset('right');\n  return element;\n}\n"], "mappings": "AAEA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAY9B,IAAMC,cAAsC,GAAG;EAC7CC,gBAAgB,EAAE,qBAAqB;EACvCC,UAAU,EAAE,eAAe;EAC3BC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,iBAAiB;EAC/BC,WAAW,EAAE;AACf,CAAC;AAED,OAAO,SAASC,sBAAsBA,CAAAC,IAAA,EAIN;EAAA,IAH9BC,QAAQ,GAGoBD,IAAA,CAH5BC,QAAQ;IACRC,KAAK,GAEuBF,IAAA,CAF5BE,KAAK;IACLC,cAAA,GAC4BH,IAAA,CAD5BG,cAAA;EAEAZ,KAAK,CAACa,SAAS,CAAC,YAAM;IAEpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IAEA,IAAMC,OAAO,GAAGC,oBAAoB,EAAE;IACtCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACH,OAAO,CAAC;IAClC,IAAMI,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;MAClB,IAAAC,qBAAA,GACEC,MAAM,CAACC,gBAAgB,CAACP,OAAO,CAAC;QAD1BQ,UAAU,GAAAH,qBAAA,CAAVG,UAAU;QAAEC,aAAa,GAAAJ,qBAAA,CAAbI,aAAa;QAAEC,WAAW,GAAAL,qBAAA,CAAXK,WAAW;QAAEC,YAAA,GAAAN,qBAAA,CAAAM,YAAA;MAGhD,IAAMC,MAAM,GAAG;QACbC,GAAG,EAAEL,UAAU,GAAGM,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC;QAC9CO,MAAM,EAAEN,aAAa,GAAGK,QAAQ,CAACL,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC;QACvDO,IAAI,EAAEN,WAAW,GAAGI,QAAQ,CAACJ,WAAW,EAAE,EAAE,CAAC,GAAG,CAAC;QACjDO,KAAK,EAAEN,YAAY,GAAGG,QAAQ,CAACH,YAAY,EAAE,EAAE,CAAC,GAAG;MACrD,CAAC;MACD,IAAMO,KAAK,GAAG;QACZC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAEtB,QAAQ,CAACuB,eAAe,CAACC,WAAW;QAC3CC,MAAM,EAAEzB,QAAQ,CAACuB,eAAe,CAACG;MACnC,CAAC;MAED5B,cAAc,CAAC;QAAE6B,WAAW,EAAE;UAAEd,MAAM,EAANA,MAAM;UAAEM,KAAA,EAAAA;QAAM;MAAE,CAAC,CAAC;IACpD,CAAC;IACDlB,OAAO,CAAC2B,gBAAgB,CAACC,2BAA2B,EAAE,EAAExB,KAAK,CAAC;IAC9DA,KAAK,EAAE;IACP,OAAO,YAAM;MACXL,QAAQ,CAACG,IAAI,CAAC2B,WAAW,CAAC7B,OAAO,CAAC;MAClCA,OAAO,CAAC8B,mBAAmB,CAACF,2BAA2B,EAAE,EAAExB,KAAK,CAAC;IACnE,CAAC;EACH,CAAC,EAAE,CAACP,cAAc,CAAC,CAAC;EAEpB,OAAOZ,KAAA,CAAA8C,aAAA,CAAC7C,IAAI;IAACU,KAAK,EAAEA;EAAM,GAAED,QAAQ,CAAQ;AAC9C;AAEA,IAAIqC,yBAAoD,GAAG,IAAI;AAC/D,SAASJ,2BAA2BA,CAAA,EAAW;EAC7C,IAAII,yBAAyB,IAAI,IAAI,EAAE;IACrC,OAAOA,yBAAyB;EAClC;EACA,IAAMhC,OAAO,GAAGD,QAAQ,CAACgC,aAAa,CAAC,aAAa,CAAC;EAErDC,yBAAyB,GAAG7C,cAAc,CAACE,UAAU;EACrD,KAAK,IAAM4C,GAAG,IAAI9C,cAAc,EAAE;IAChC,IAAIa,OAAO,CAACJ,KAAK,CAACqC,GAAG,CAA8B,KAAKC,SAAS,EAAE;MACjEF,yBAAyB,GAAG7C,cAAc,CAAC8C,GAAG,CAAC;MAC/C;IACF;EACF;EACA,OAAOD,yBAAyB;AAClC;AAIA,IAAIG,aAA4B,GAAG,IAAI;AACvC,SAASC,eAAeA,CAAA,EAAW;EACjC,IAAID,aAAa,KAAK,IAAI,EAAE;IAC1B,OAAOA,aAAa;EACtB;EACA,IAAAE,OAAA,GAAgB/B,MAAM;IAAdgC,GAAA,GAAAD,OAAA,CAAAC,GAAA;EACR,IACEA,GAAG,IACHA,GAAG,CAACC,QAAQ,IACZD,GAAG,CAACC,QAAQ,CAAC,oCAAoC,CAAC,EAClD;IACAJ,aAAa,GAAG,UAAU;EAC5B,CAAC,MAAM;IACLA,aAAa,GAAG,KAAK;EACvB;EACA,OAAOA,aAAa;AACtB;AAEA,SAASK,QAAQA,CAACC,IAAY,EAAU;EACtC,OAAQ,GAAEL,eAAe,EAAG,oBAAmBK,IAAK,GAAE;AACxD;AAEA,SAASxC,oBAAoBA,CAAA,EAAgB;EAC3C,IAAMD,OAAO,GAAGD,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;EAC7C,IAAQnC,KAAA,GAAUI,OAAO,CAAjBJ,KAAA;EACRA,KAAK,CAAC8C,QAAQ,GAAG,OAAO;EACxB9C,KAAK,CAACoB,IAAI,GAAG,GAAG;EAChBpB,KAAK,CAACiB,GAAG,GAAG,GAAG;EACfjB,KAAK,CAACyB,KAAK,GAAG,GAAG;EACjBzB,KAAK,CAAC4B,MAAM,GAAG,GAAG;EAClB5B,KAAK,CAAC+C,MAAM,GAAG,IAAI;EACnB/C,KAAK,CAACgD,QAAQ,GAAG,QAAQ;EACzBhD,KAAK,CAACiD,UAAU,GAAG,QAAQ;EAE3BjD,KAAK,CAACkD,kBAAkB,GAAG,OAAO;EAClClD,KAAK,CAACmD,kBAAkB,GAAG,SAAS;EACpCnD,KAAK,CAACoD,eAAe,GAAG,IAAI;EAC5BpD,KAAK,CAACY,UAAU,GAAGgC,QAAQ,CAAC,KAAK,CAAC;EAClC5C,KAAK,CAACa,aAAa,GAAG+B,QAAQ,CAAC,QAAQ,CAAC;EACxC5C,KAAK,CAACc,WAAW,GAAG8B,QAAQ,CAAC,MAAM,CAAC;EACpC5C,KAAK,CAACe,YAAY,GAAG6B,QAAQ,CAAC,OAAO,CAAC;EACtC,OAAOxC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}