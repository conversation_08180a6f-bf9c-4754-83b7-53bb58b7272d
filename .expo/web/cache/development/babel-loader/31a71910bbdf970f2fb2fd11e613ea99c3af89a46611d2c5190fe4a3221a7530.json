{"ast": null, "code": "export function addEventListener(Module) {\n  var _Module$addEventListe;\n  for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    rest[_key - 1] = arguments[_key];\n  }\n  var eventName = rest[0],\n    handler = rest[1];\n  var removed = false;\n  var subscription = (_Module$addEventListe = Module.addEventListener(eventName, handler)) != null ? _Module$addEventListe : {\n    remove: function remove() {\n      var _Module$removeEventLi, _Module$remove;\n      if (removed) {\n        return;\n      }\n      (_Module$removeEventLi = Module.removeEventListener) === null || _Module$removeEventLi === void 0 ? void 0 : _Module$removeEventLi.call(Module, eventName, handler);\n      (_Module$remove = Module.remove) === null || _Module$remove === void 0 ? void 0 : _Module$remove.call(Module, eventName, handler);\n      removed = true;\n    }\n  };\n  return subscription;\n}\nexport function addListener(Module) {\n  var _Module$addListener;\n  for (var _len2 = arguments.length, rest = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    rest[_key2 - 1] = arguments[_key2];\n  }\n  var eventName = rest[0],\n    handler = rest[1];\n  var removed = false;\n  var subscription = (_Module$addListener = Module.addListener(eventName, handler)) != null ? _Module$addListener : {\n    remove: function remove() {\n      if (removed) {\n        return;\n      }\n      Module.removeEventListener(eventName, handler);\n      removed = true;\n    }\n  };\n  return subscription;\n}", "map": {"version": 3, "names": ["addEventListener", "<PERSON><PERSON><PERSON>", "_Module$addEventListe", "_len", "arguments", "length", "rest", "Array", "_key", "eventName", "handler", "removed", "subscription", "remove", "_Module$removeEventLi", "_Module$remove", "removeEventListener", "call", "addListener", "_Module$addListener", "_len2", "_key2"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/utils/addEventListener.tsx"], "sourcesContent": ["import type {\n  NativeEventSubscription,\n  EmitterSubscription,\n} from 'react-native';\n\nexport function addEventListener<\n  T extends {\n    addEventListener: (\n      ...args: any\n    ) => NativeEventSubscription | EmitterSubscription;\n  } & { removeEventListener?: (...args: any) => void } & {\n    remove?: (...args: any) => void;\n  }\n>(Module: T, ...rest: Parameters<typeof Module.addEventListener>) {\n  const [eventName, handler] = rest;\n\n  let removed = false;\n\n  const subscription = Module.addEventListener(eventName, handler) ?? {\n    remove: () => {\n      if (removed) {\n        return;\n      }\n\n      Module.removeEventListener?.(eventName, handler);\n      Module.remove?.(eventName, handler);\n      removed = true;\n    },\n  };\n\n  return subscription;\n}\n\nexport function addListener<\n  T extends {\n    addListener: (...args: any) => EmitterSubscription;\n    removeEventListener: (...args: any) => void;\n  }\n>(Module: T, ...rest: Parameters<typeof Module.addListener>) {\n  const [eventName, handler] = rest;\n\n  let removed = false;\n\n  const subscription = Module.addListener(eventName, handler) ?? {\n    remove: () => {\n      if (removed) {\n        return;\n      }\n\n      Module.removeEventListener(eventName, handler);\n      removed = true;\n    },\n  };\n\n  return subscription;\n}\n"], "mappings": "AAKA,OAAO,SAASA,gBAAgBA,CAQ9BC,MAAS,EAAuD;EAAA,IAAAC,qBAAA;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAlDC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAClB,IAAOC,SAAS,GAAaH,IAAI;IAAfI,OAAO,GAAIJ,IAAI;EAEjC,IAAIK,OAAO,GAAG,KAAK;EAEnB,IAAMC,YAAY,IAAAV,qBAAA,GAAGD,MAAM,CAACD,gBAAgB,CAACS,SAAS,EAAEC,OAAO,CAAC,YAAAR,qBAAA,GAAI;IAClEW,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;MAAA,IAAAC,qBAAA,EAAAC,cAAA;MACZ,IAAIJ,OAAO,EAAE;QACX;MACF;MAEA,CAAAG,qBAAA,GAAAb,MAAM,CAACe,mBAAmB,cAAAF,qBAAA,uBAA1BA,qBAAA,CAAAG,IAAA,CAAAhB,MAAM,EAAuBQ,SAAS,EAAEC,OAAO,CAAC;MAChD,CAAAK,cAAA,GAAAd,MAAM,CAACY,MAAM,cAAAE,cAAA,uBAAbA,cAAA,CAAAE,IAAA,CAAAhB,MAAM,EAAUQ,SAAS,EAAEC,OAAO,CAAC;MACnCC,OAAO,GAAG,IAAI;IAChB;EACF,CAAC;EAED,OAAOC,YAAY;AACrB;AAEA,OAAO,SAASM,WAAWA,CAKzBjB,MAAS,EAAkD;EAAA,IAAAkB,mBAAA;EAAA,SAAAC,KAAA,GAAAhB,SAAA,CAAAC,MAAA,EAA7CC,IAAI,OAAAC,KAAA,CAAAa,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJf,IAAI,CAAAe,KAAA,QAAAjB,SAAA,CAAAiB,KAAA;EAAA;EAClB,IAAOZ,SAAS,GAAaH,IAAI;IAAfI,OAAO,GAAIJ,IAAI;EAEjC,IAAIK,OAAO,GAAG,KAAK;EAEnB,IAAMC,YAAY,IAAAO,mBAAA,GAAGlB,MAAM,CAACiB,WAAW,CAACT,SAAS,EAAEC,OAAO,CAAC,YAAAS,mBAAA,GAAI;IAC7DN,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;MACZ,IAAIF,OAAO,EAAE;QACX;MACF;MAEAV,MAAM,CAACe,mBAAmB,CAACP,SAAS,EAAEC,OAAO,CAAC;MAC9CC,OAAO,GAAG,IAAI;IAChB;EACF,CAAC;EAED,OAAOC,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}