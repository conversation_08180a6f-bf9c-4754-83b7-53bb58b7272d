{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"initialMetrics\", \"initialSafeAreaInsets\", \"style\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { NativeSafeAreaProvider } from \"./NativeSafeAreaProvider\";\nvar isDev = process.env.NODE_ENV !== 'production';\nexport var SafeAreaInsetsContext = React.createContext(null);\nif (isDev) {\n  SafeAreaInsetsContext.displayName = 'SafeAreaInsetsContext';\n}\nexport var SafeAreaFrameContext = React.createContext(null);\nif (isDev) {\n  SafeAreaFrameContext.displayName = 'SafeAreaFrameContext';\n}\nexport function SafeAreaProvider(_ref) {\n  var _ref2, _ref3, _ref4, _ref5, _ref6;\n  var children = _ref.children,\n    initialMetrics = _ref.initialMetrics,\n    initialSafeAreaInsets = _ref.initialSafeAreaInsets,\n    style = _ref.style,\n    others = _objectWithoutProperties(_ref, _excluded);\n  var parentInsets = useParentSafeAreaInsets();\n  var parentFrame = useParentSafeAreaFrame();\n  var _React$useState = React.useState((_ref2 = (_ref3 = (_ref4 = initialMetrics === null || initialMetrics === void 0 ? void 0 : initialMetrics.insets) != null ? _ref4 : initialSafeAreaInsets) != null ? _ref3 : parentInsets) != null ? _ref2 : null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    insets = _React$useState2[0],\n    setInsets = _React$useState2[1];\n  var _React$useState3 = React.useState((_ref5 = (_ref6 = initialMetrics === null || initialMetrics === void 0 ? void 0 : initialMetrics.frame) != null ? _ref6 : parentFrame) != null ? _ref5 : {\n      x: 0,\n      y: 0,\n      width: Dimensions.get('window').width,\n      height: Dimensions.get('window').height\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    frame = _React$useState4[0],\n    setFrame = _React$useState4[1];\n  var onInsetsChange = React.useCallback(function (event) {\n    var _event$nativeEvent = event.nativeEvent,\n      nextFrame = _event$nativeEvent.frame,\n      nextInsets = _event$nativeEvent.insets;\n    if (nextFrame && (nextFrame.height !== frame.height || nextFrame.width !== frame.width || nextFrame.x !== frame.x || nextFrame.y !== frame.y)) {\n      setFrame(nextFrame);\n    }\n    if (!insets || nextInsets.bottom !== insets.bottom || nextInsets.left !== insets.left || nextInsets.right !== insets.right || nextInsets.top !== insets.top) {\n      setInsets(nextInsets);\n    }\n  }, [frame, insets]);\n  return React.createElement(NativeSafeAreaProvider, _extends({\n    style: [styles.fill, style],\n    onInsetsChange: onInsetsChange\n  }, others), insets != null ? React.createElement(SafeAreaFrameContext.Provider, {\n    value: frame\n  }, React.createElement(SafeAreaInsetsContext.Provider, {\n    value: insets\n  }, children)) : null);\n}\nvar styles = StyleSheet.create({\n  fill: {\n    flex: 1\n  }\n});\nfunction useParentSafeAreaInsets() {\n  return React.useContext(SafeAreaInsetsContext);\n}\nfunction useParentSafeAreaFrame() {\n  return React.useContext(SafeAreaFrameContext);\n}\nvar NO_INSETS_ERROR = 'No safe area value available. Make sure you are rendering `<SafeAreaProvider>` at the top of your app.';\nexport function useSafeAreaInsets() {\n  var insets = React.useContext(SafeAreaInsetsContext);\n  if (insets == null) {\n    throw new Error(NO_INSETS_ERROR);\n  }\n  return insets;\n}\nexport function useSafeAreaFrame() {\n  var frame = React.useContext(SafeAreaFrameContext);\n  if (frame == null) {\n    throw new Error(NO_INSETS_ERROR);\n  }\n  return frame;\n}\nexport function withSafeAreaInsets(WrappedComponent) {\n  return React.forwardRef(function (props, ref) {\n    var insets = useSafeAreaInsets();\n    return React.createElement(WrappedComponent, _extends({}, props, {\n      insets: insets,\n      ref: ref\n    }));\n  });\n}\nexport function useSafeArea() {\n  return useSafeAreaInsets();\n}\nexport var SafeAreaConsumer = SafeAreaInsetsContext.Consumer;\nexport var SafeAreaContext = SafeAreaInsetsContext;", "map": {"version": 3, "names": ["React", "Dimensions", "StyleSheet", "NativeSafeAreaProvider", "isDev", "process", "env", "NODE_ENV", "SafeAreaInsetsContext", "createContext", "displayName", "SafeAreaFrameContext", "SafeAreaProvider", "_ref", "_ref2", "_ref3", "_ref4", "_ref5", "_ref6", "children", "initialMetrics", "initialSafeAreaInsets", "style", "others", "_objectWithoutProperties", "_excluded", "parentInsets", "useParentSafeAreaInsets", "parentFrame", "useParentSafeAreaFrame", "_React$useState", "useState", "insets", "_React$useState2", "_slicedToArray", "setInsets", "_React$useState3", "frame", "x", "y", "width", "get", "height", "_React$useState4", "set<PERSON>rame", "onInsetsChange", "useCallback", "event", "_event$nativeEvent", "nativeEvent", "next<PERSON><PERSON><PERSON>", "nextInsets", "bottom", "left", "right", "top", "createElement", "_extends", "styles", "fill", "Provider", "value", "create", "flex", "useContext", "NO_INSETS_ERROR", "useSafeAreaInsets", "Error", "useSafeAreaFrame", "withSafeAreaInsets", "WrappedComponent", "forwardRef", "props", "ref", "useSafeArea", "SafeAreaConsumer", "Consumer", "SafeAreaContext"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-safe-area-context/src/SafeAreaContext.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Dimensions, StyleSheet, ViewProps } from 'react-native';\nimport { NativeSafeAreaProvider } from './NativeSafeAreaProvider';\nimport type {\n  EdgeInsets,\n  InsetChangedEvent,\n  Metrics,\n  Rect,\n} from './SafeArea.types';\n\nconst isDev = process.env.NODE_ENV !== 'production';\n\nexport const SafeAreaInsetsContext = React.createContext<EdgeInsets | null>(\n  null,\n);\nif (isDev) {\n  SafeAreaInsetsContext.displayName = 'SafeAreaInsetsContext';\n}\n\nexport const SafeAreaFrameContext = React.createContext<Rect | null>(null);\nif (isDev) {\n  SafeAreaFrameContext.displayName = 'SafeAreaFrameContext';\n}\n\nexport interface SafeAreaProviderProps extends ViewProps {\n  children?: React.ReactNode;\n  initialMetrics?: Metrics | null;\n  /**\n   * @deprecated\n   */\n  initialSafeAreaInsets?: EdgeInsets | null;\n}\n\nexport function SafeAreaProvider({\n  children,\n  initialMetrics,\n  initialSafeAreaInsets,\n  style,\n  ...others\n}: SafeAreaProviderProps) {\n  const parentInsets = useParentSafeAreaInsets();\n  const parentFrame = useParentSafeAreaFrame();\n  const [insets, setInsets] = React.useState<EdgeInsets | null>(\n    initialMetrics?.insets ?? initialSafeAreaInsets ?? parentInsets ?? null,\n  );\n  const [frame, setFrame] = React.useState<Rect>(\n    initialMetrics?.frame ??\n      parentFrame ?? {\n        // Backwards compat so we render anyway if we don't have frame.\n        x: 0,\n        y: 0,\n        width: Dimensions.get('window').width,\n        height: Dimensions.get('window').height,\n      },\n  );\n  const onInsetsChange = React.useCallback(\n    (event: InsetChangedEvent) => {\n      const {\n        nativeEvent: { frame: nextFrame, insets: nextInsets },\n      } = event;\n\n      if (\n        // Backwards compat with old native code that won't send frame.\n        nextFrame &&\n        (nextFrame.height !== frame.height ||\n          nextFrame.width !== frame.width ||\n          nextFrame.x !== frame.x ||\n          nextFrame.y !== frame.y)\n      ) {\n        setFrame(nextFrame);\n      }\n\n      if (\n        !insets ||\n        nextInsets.bottom !== insets.bottom ||\n        nextInsets.left !== insets.left ||\n        nextInsets.right !== insets.right ||\n        nextInsets.top !== insets.top\n      ) {\n        setInsets(nextInsets);\n      }\n    },\n    [frame, insets],\n  );\n\n  return (\n    <NativeSafeAreaProvider\n      style={[styles.fill, style]}\n      onInsetsChange={onInsetsChange}\n      {...others}\n    >\n      {insets != null ? (\n        <SafeAreaFrameContext.Provider value={frame}>\n          <SafeAreaInsetsContext.Provider value={insets}>\n            {children}\n          </SafeAreaInsetsContext.Provider>\n        </SafeAreaFrameContext.Provider>\n      ) : null}\n    </NativeSafeAreaProvider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  fill: { flex: 1 },\n});\n\nfunction useParentSafeAreaInsets(): EdgeInsets | null {\n  return React.useContext(SafeAreaInsetsContext);\n}\n\nfunction useParentSafeAreaFrame(): Rect | null {\n  return React.useContext(SafeAreaFrameContext);\n}\n\nconst NO_INSETS_ERROR =\n  'No safe area value available. Make sure you are rendering `<SafeAreaProvider>` at the top of your app.';\n\nexport function useSafeAreaInsets(): EdgeInsets {\n  const insets = React.useContext(SafeAreaInsetsContext);\n  if (insets == null) {\n    throw new Error(NO_INSETS_ERROR);\n  }\n  return insets;\n}\n\nexport function useSafeAreaFrame(): Rect {\n  const frame = React.useContext(SafeAreaFrameContext);\n  if (frame == null) {\n    throw new Error(NO_INSETS_ERROR);\n  }\n  return frame;\n}\n\nexport type WithSafeAreaInsetsProps = {\n  insets: EdgeInsets;\n};\n\nexport function withSafeAreaInsets<T>(\n  WrappedComponent: React.ComponentType<T & WithSafeAreaInsetsProps>,\n): React.ForwardRefExoticComponent<\n  React.PropsWithoutRef<T> & React.RefAttributes<unknown>\n> {\n  return React.forwardRef((props: T, ref: React.Ref<unknown>) => {\n    const insets = useSafeAreaInsets();\n    return <WrappedComponent {...props} insets={insets} ref={ref} />;\n  });\n}\n\n/**\n * @deprecated\n */\nexport function useSafeArea(): EdgeInsets {\n  return useSafeAreaInsets();\n}\n\n/**\n * @deprecated\n */\nexport const SafeAreaConsumer = SafeAreaInsetsContext.Consumer;\n\n/**\n * @deprecated\n */\nexport const SafeAreaContext = SafeAreaInsetsContext;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAE9B,SAASC,sBAAsB;AAQ/B,IAAMC,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AAEnD,OAAO,IAAMC,qBAAqB,GAAGR,KAAK,CAACS,aAAa,CACtD,IAAI,CACL;AACD,IAAIL,KAAK,EAAE;EACTI,qBAAqB,CAACE,WAAW,GAAG,uBAAuB;AAC7D;AAEA,OAAO,IAAMC,oBAAoB,GAAGX,KAAK,CAACS,aAAa,CAAc,IAAI,CAAC;AAC1E,IAAIL,KAAK,EAAE;EACTO,oBAAoB,CAACD,WAAW,GAAG,sBAAsB;AAC3D;AAWA,OAAO,SAASE,gBAAgBA,CAAAC,IAAA,EAMN;EAAA,IAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA;EAAA,IALxBC,QAAQ,GAKcN,IAAA,CALtBM,QAAQ;IACRC,cAAc,GAIQP,IAAA,CAJtBO,cAAc;IACdC,qBAAqB,GAGCR,IAAA,CAHtBQ,qBAAqB;IACrBC,KAAK,GAEiBT,IAAA,CAFtBS,KAAK;IACFC,MAAA,GAAAC,wBAAA,CACmBX,IAAA,EAAAY,SAAA;EACtB,IAAMC,YAAY,GAAGC,uBAAuB,EAAE;EAC9C,IAAMC,WAAW,GAAGC,sBAAsB,EAAE;EAC5C,IAAAC,eAAA,GAA4B9B,KAAK,CAAC+B,QAAQ,EAAAjB,KAAA,IAAAC,KAAA,IAAAC,KAAA,GACxCI,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEY,MAAM,YAAAhB,KAAA,GAAIK,qBAAqB,YAAAN,KAAA,GAAIW,YAAY,YAAAZ,KAAA,GAAI,IAAI,CACxE;IAAAmB,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;IAFME,MAAM,GAAAC,gBAAA;IAAEE,SAAS,GAAAF,gBAAA;EAGxB,IAAAG,gBAAA,GAA0BpC,KAAK,CAAC+B,QAAQ,EAAAd,KAAA,IAAAC,KAAA,GACtCE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiB,KAAK,YAAAnB,KAAA,GACnBU,WAAW,YAAAX,KAAA,GAAI;MAEbqB,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAEvC,UAAU,CAACwC,GAAG,CAAC,QAAQ,CAAC,CAACD,KAAK;MACrCE,MAAM,EAAEzC,UAAU,CAACwC,GAAG,CAAC,QAAQ,CAAC,CAACC;IACnC,CAAC,CACJ;IAAAC,gBAAA,GAAAT,cAAA,CAAAE,gBAAA;IATMC,KAAK,GAAAM,gBAAA;IAAEC,QAAQ,GAAAD,gBAAA;EAUtB,IAAME,cAAc,GAAG7C,KAAK,CAAC8C,WAAW,CACrC,UAAAC,KAAwB,EAAK;IAC5B,IAAAC,kBAAA,GAEID,KAAK,CADPE,WAAW;MAAWC,SAAS,GAAAF,kBAAA,CAAhBX,KAAK;MAAqBc,UAAA,GAAAH,kBAAA,CAARhB,MAAM;IAGzC,IAEEkB,SAAS,KACRA,SAAS,CAACR,MAAM,KAAKL,KAAK,CAACK,MAAM,IAChCQ,SAAS,CAACV,KAAK,KAAKH,KAAK,CAACG,KAAK,IAC/BU,SAAS,CAACZ,CAAC,KAAKD,KAAK,CAACC,CAAC,IACvBY,SAAS,CAACX,CAAC,KAAKF,KAAK,CAACE,CAAC,CAAC,EAC1B;MACAK,QAAQ,CAACM,SAAS,CAAC;IACrB;IAEA,IACE,CAAClB,MAAM,IACPmB,UAAU,CAACC,MAAM,KAAKpB,MAAM,CAACoB,MAAM,IACnCD,UAAU,CAACE,IAAI,KAAKrB,MAAM,CAACqB,IAAI,IAC/BF,UAAU,CAACG,KAAK,KAAKtB,MAAM,CAACsB,KAAK,IACjCH,UAAU,CAACI,GAAG,KAAKvB,MAAM,CAACuB,GAAG,EAC7B;MACApB,SAAS,CAACgB,UAAU,CAAC;IACvB;EACF,CAAC,EACD,CAACd,KAAK,EAAEL,MAAM,CAAC,CAChB;EAED,OACEhC,KAAA,CAAAwD,aAAA,CAACrD,sBAAsB,EAAAsD,QAAA;IACrBnC,KAAK,EAAE,CAACoC,MAAM,CAACC,IAAI,EAAErC,KAAK,CAAE;IAC5BuB,cAAc,EAAEA;EAAe,GAC3BtB,MAAM,GAETS,MAAM,IAAI,IAAI,GACbhC,KAAA,CAAAwD,aAAA,CAAC7C,oBAAoB,CAACiD,QAAQ;IAACC,KAAK,EAAExB;EAAM,GAC1CrC,KAAA,CAAAwD,aAAA,CAAChD,qBAAqB,CAACoD,QAAQ;IAACC,KAAK,EAAE7B;EAAO,GAC3Cb,QAAQ,CACsB,CACH,GAC9B,IAAI,CACe;AAE7B;AAEA,IAAMuC,MAAM,GAAGxD,UAAU,CAAC4D,MAAM,CAAC;EAC/BH,IAAI,EAAE;IAAEI,IAAI,EAAE;EAAE;AAClB,CAAC,CAAC;AAEF,SAASpC,uBAAuBA,CAAA,EAAsB;EACpD,OAAO3B,KAAK,CAACgE,UAAU,CAACxD,qBAAqB,CAAC;AAChD;AAEA,SAASqB,sBAAsBA,CAAA,EAAgB;EAC7C,OAAO7B,KAAK,CAACgE,UAAU,CAACrD,oBAAoB,CAAC;AAC/C;AAEA,IAAMsD,eAAe,GACnB,wGAAwG;AAE1G,OAAO,SAASC,iBAAiBA,CAAA,EAAe;EAC9C,IAAMlC,MAAM,GAAGhC,KAAK,CAACgE,UAAU,CAACxD,qBAAqB,CAAC;EACtD,IAAIwB,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAImC,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAOjC,MAAM;AACf;AAEA,OAAO,SAASoC,gBAAgBA,CAAA,EAAS;EACvC,IAAM/B,KAAK,GAAGrC,KAAK,CAACgE,UAAU,CAACrD,oBAAoB,CAAC;EACpD,IAAI0B,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAI8B,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAO5B,KAAK;AACd;AAMA,OAAO,SAASgC,kBAAkBA,CAChCC,gBAAkE,EAGlE;EACA,OAAOtE,KAAK,CAACuE,UAAU,CAAC,UAACC,KAAQ,EAAEC,GAAuB,EAAK;IAC7D,IAAMzC,MAAM,GAAGkC,iBAAiB,EAAE;IAClC,OAAOlE,KAAA,CAAAwD,aAAA,CAACc,gBAAgB,EAAAb,QAAA,KAAKe,KAAK;MAAExC,MAAM,EAAEA,MAAO;MAACyC,GAAG,EAAEA;IAAI,GAAG;EAClE,CAAC,CAAC;AACJ;AAKA,OAAO,SAASC,WAAWA,CAAA,EAAe;EACxC,OAAOR,iBAAiB,EAAE;AAC5B;AAKA,OAAO,IAAMS,gBAAgB,GAAGnE,qBAAqB,CAACoE,QAAQ;AAK9D,OAAO,IAAMC,eAAe,GAAGrE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}