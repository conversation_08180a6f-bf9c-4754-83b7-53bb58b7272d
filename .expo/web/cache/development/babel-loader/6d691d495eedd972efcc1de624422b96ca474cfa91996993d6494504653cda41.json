{"ast": null, "code": "var DefaultTheme = {\n  dark: false,\n  colors: {\n    primary: 'rgb(0, 122, 255)',\n    background: 'rgb(242, 242, 242)',\n    card: 'rgb(255, 255, 255)',\n    text: 'rgb(28, 28, 30)',\n    border: 'rgb(216, 216, 216)',\n    notification: 'rgb(255, 59, 48)'\n  }\n};\nexport default DefaultTheme;", "map": {"version": 3, "names": ["DefaultTheme", "dark", "colors", "primary", "background", "card", "text", "border", "notification"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/native/src/theming/DefaultTheme.tsx"], "sourcesContent": ["import type { Theme } from '../types';\n\nconst DefaultTheme: Theme = {\n  dark: false,\n  colors: {\n    primary: 'rgb(0, 122, 255)',\n    background: 'rgb(242, 242, 242)',\n    card: 'rgb(255, 255, 255)',\n    text: 'rgb(28, 28, 30)',\n    border: 'rgb(216, 216, 216)',\n    notification: 'rgb(255, 59, 48)',\n  },\n};\n\nexport default DefaultTheme;\n"], "mappings": "AAEA,IAAMA,YAAmB,GAAG;EAC1BC,IAAI,EAAE,KAAK;EACXC,MAAM,EAAE;IACNC,OAAO,EAAE,kBAAkB;IAC3BC,UAAU,EAAE,oBAAoB;IAChCC,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,iBAAiB;IACvBC,MAAM,EAAE,oBAAoB;IAC5BC,YAAY,EAAE;EAChB;AACF,CAAC;AAED,eAAeR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}