{"ast": null, "code": "import FABComponent from \"./FAB\";\nimport FABGroup from \"./FABGroup\";\nvar FAB = Object.assign(FABComponent, {\n  Group: FABGroup\n});\nexport default FAB;", "map": {"version": 3, "names": ["FABComponent", "FABGroup", "FAB", "Object", "assign", "Group"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/react-native-paper/src/components/FAB/index.ts"], "sourcesContent": ["import FABComponent from './FAB';\nimport FABGroup from './FABGroup';\n\nconst FAB = Object.assign(\n  // @component ./FAB.tsx\n  FABComponent,\n  {\n    // @component ./FABGroup.tsx\n    Group: FABGroup,\n  }\n);\n\nexport default FAB;\n"], "mappings": "AAAA,OAAOA,YAAY;AACnB,OAAOC,QAAQ;AAEf,IAAMC,GAAG,GAAGC,MAAM,CAACC,MAAM,CAEvBJ,YAAY,EACZ;EAEEK,KAAK,EAAEJ;AACT,CACF,CAAC;AAED,eAAeC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}