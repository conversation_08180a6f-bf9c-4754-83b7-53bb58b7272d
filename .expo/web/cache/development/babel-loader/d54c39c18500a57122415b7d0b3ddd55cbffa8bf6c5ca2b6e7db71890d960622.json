{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport color from 'color';\nimport { black, pinkA100, white } from \"./colors\";\nimport { MD2LightTheme } from \"./LightTheme\";\nimport configureFonts from \"../../fonts\";\nexport var MD2DarkTheme = _objectSpread(_objectSpread({}, MD2LightTheme), {}, {\n  dark: true,\n  mode: 'adaptive',\n  version: 2,\n  isV3: false,\n  colors: _objectSpread(_objectSpread({}, MD2LightTheme.colors), {}, {\n    primary: '#BB86FC',\n    accent: '#03dac6',\n    background: '#121212',\n    surface: '#121212',\n    error: '#CF6679',\n    onSurface: '#FFFFFF',\n    text: white,\n    disabled: color(white).alpha(0.38).rgb().string(),\n    placeholder: color(white).alpha(0.54).rgb().string(),\n    backdrop: color(black).alpha(0.5).rgb().string(),\n    notification: pinkA100,\n    tooltip: 'rgba(230, 225, 229, 1)'\n  }),\n  fonts: configureFonts({\n    isV3: false\n  })\n});", "map": {"version": 3, "names": ["color", "black", "pinkA100", "white", "MD2LightTheme", "configure<PERSON>onts", "MD2DarkTheme", "_objectSpread", "dark", "mode", "version", "isV3", "colors", "primary", "accent", "background", "surface", "error", "onSurface", "text", "disabled", "alpha", "rgb", "string", "placeholder", "backdrop", "notification", "tooltip", "fonts"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/styles/themes/v2/DarkTheme.tsx"], "sourcesContent": ["import color from 'color';\n\nimport { black, pinkA100, white } from './colors';\nimport { MD2LightTheme } from './LightTheme';\nimport type { Fonts, MD2Theme } from '../../../types';\nimport configureFonts from '../../fonts';\n\nexport const MD2DarkTheme: MD2Theme = {\n  ...MD2LightTheme,\n  dark: true,\n  mode: 'adaptive',\n  version: 2,\n  isV3: false,\n  colors: {\n    ...MD2LightTheme.colors,\n    primary: '#BB86FC',\n    accent: '#03dac6',\n    background: '#121212',\n    surface: '#121212',\n    error: '#CF6679',\n    onSurface: '#FFFFFF',\n    text: white,\n    disabled: color(white).alpha(0.38).rgb().string(),\n    placeholder: color(white).alpha(0.54).rgb().string(),\n    backdrop: color(black).alpha(0.5).rgb().string(),\n    notification: pinkA100,\n    tooltip: 'rgba(230, 225, 229, 1)',\n  },\n  fonts: configureFonts({ isV3: false }) as Fonts,\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,QAAQ,EAAEC,KAAK;AAC/B,SAASC,aAAa;AAEtB,OAAOC,cAAc;AAErB,OAAO,IAAMC,YAAsB,GAAAC,aAAA,CAAAA,aAAA,KAC9BH,aAAa;EAChBI,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,UAAU;EAChBC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,KAAK;EACXC,MAAM,EAAAL,aAAA,CAAAA,aAAA,KACDH,aAAa,CAACQ,MAAM;IACvBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,IAAI,EAAEhB,KAAK;IACXiB,QAAQ,EAAEpB,KAAK,CAACG,KAAK,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjDC,WAAW,EAAExB,KAAK,CAACG,KAAK,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACpDE,QAAQ,EAAEzB,KAAK,CAACC,KAAK,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChDG,YAAY,EAAExB,QAAQ;IACtByB,OAAO,EAAE;EAAA,EACV;EACDC,KAAK,EAAEvB,cAAc,CAAC;IAAEM,IAAI,EAAE;EAAM,CAAC;AAAA,EACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}