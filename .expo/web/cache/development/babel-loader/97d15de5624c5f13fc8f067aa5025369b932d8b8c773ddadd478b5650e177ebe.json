{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport { Card, Chip, Divider, IconButton, Text, Button } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport FoodForm from \"../components/FoodForm\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar FoodDetailScreen = function FoodDetailScreen(_ref) {\n  var route = _ref.route,\n    navigation = _ref.navigation;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _ref2 = route.params || {},\n    initialFood = _ref2.food,\n    _ref2$isEditing = _ref2.isEditing,\n    isEditing = _ref2$isEditing === void 0 ? false : _ref2$isEditing,\n    _ref2$isNew = _ref2.isNew,\n    isNew = _ref2$isNew === void 0 ? false : _ref2$isNew;\n  var _useState = useState(isEditing || isNew),\n    _useState2 = _slicedToArray(_useState, 2),\n    isEditMode = _useState2[0],\n    setIsEditMode = _useState2[1];\n  var _useState3 = useState(initialFood || null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    food = _useState4[0],\n    setFood = _useState4[1];\n  var handleSave = function handleSave(savedFood) {\n    setFood(savedFood);\n    setIsEditMode(false);\n    navigation.setParams({\n      food: savedFood,\n      isEditing: false,\n      isNew: false\n    });\n  };\n  var handleCancel = function handleCancel() {\n    if (isNew) {\n      navigation.goBack();\n    } else {\n      setIsEditMode(false);\n    }\n  };\n  var handleDelete = function handleDelete() {\n    navigation.goBack();\n  };\n  if (isEditMode) {\n    return _jsx(FoodForm, {\n      initialFood: food,\n      onSave: handleSave,\n      onCancel: handleCancel\n    });\n  }\n  if (!food) {\n    return _jsx(View, {\n      style: [styles.container, {\n        backgroundColor: theme.colors.background\n      }],\n      children: _jsx(Text, {\n        children: \"No food data available\"\n      })\n    });\n  }\n  return _jsx(ScrollView, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: _jsx(Card, {\n      style: styles.card,\n      children: _jsxs(Card.Content, {\n        children: [_jsxs(View, {\n          style: styles.header,\n          children: [_jsx(Text, {\n            style: styles.title,\n            children: food.name\n          }), _jsxs(View, {\n            style: styles.headerActions,\n            children: [food.is_favorite === 1 && _jsx(IconButton, {\n              icon: \"star\",\n              iconColor: theme.colors.primary,\n              size: 24\n            }), _jsx(IconButton, {\n              icon: \"pencil\",\n              onPress: function onPress() {\n                return setIsEditMode(true);\n              },\n              size: 24\n            })]\n          })]\n        }), food.brand && _jsx(Text, {\n          style: styles.brand,\n          children: food.brand\n        }), food.description && _jsx(Text, {\n          style: styles.description,\n          children: food.description\n        }), food.barcode && _jsx(Chip, {\n          icon: \"barcode\",\n          style: styles.chip,\n          children: food.barcode\n        }), _jsxs(Text, {\n          style: styles.servingSize,\n          children: [\"Serving size: \", food.serving_size, \" \", food.serving_unit]\n        }), _jsx(Divider, {\n          style: styles.divider\n        }), _jsx(Text, {\n          style: styles.sectionTitle,\n          children: \"Nutrition Facts (per 100g)\"\n        }), food.nutrients && food.nutrients.map(function (nutrient) {\n          return _jsxs(View, {\n            style: styles.nutrientRow,\n            children: [_jsx(Text, {\n              style: styles.nutrientName,\n              children: nutrient.name\n            }), _jsxs(Text, {\n              style: styles.nutrientValue,\n              children: [nutrient.amount, \" \", nutrient.unit]\n            })]\n          }, nutrient.nutrient_id);\n        }), food.ingredients && food.ingredients.length > 0 && _jsxs(View, {\n          children: [_jsx(Divider, {\n            style: styles.divider\n          }), _jsx(Text, {\n            style: styles.sectionTitle,\n            children: \"Allergens\"\n          }), _jsx(View, {\n            style: styles.ingredientsContainer,\n            children: food.ingredients.filter(function (ingredient) {\n              return ingredient.is_allergen === 1;\n            }).map(function (ingredient) {\n              return _jsx(Chip, {\n                style: styles.allergenChip,\n                children: ingredient.name\n              }, ingredient.ingredient_id);\n            })\n          })]\n        }), _jsx(Divider, {\n          style: styles.divider\n        }), _jsx(Button, {\n          mode: \"outlined\",\n          icon: \"delete\",\n          onPress: handleDelete,\n          style: styles.deleteButton,\n          textColor: theme.colors.error,\n          children: \"Delete Food\"\n        })]\n      })\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  card: {\n    margin: 16,\n    elevation: 2\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center'\n  },\n  headerActions: {\n    flexDirection: 'row',\n    alignItems: 'center'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 8,\n    flex: 1\n  },\n  brand: {\n    fontSize: 16,\n    marginBottom: 8\n  },\n  description: {\n    marginBottom: 16\n  },\n  chip: {\n    marginBottom: 16,\n    alignSelf: 'flex-start'\n  },\n  servingSize: {\n    marginBottom: 16\n  },\n  divider: {\n    marginVertical: 16\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginBottom: 16\n  },\n  nutrientRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 8,\n    borderBottomWidth: StyleSheet.hairlineWidth,\n    borderBottomColor: '#e0e0e0',\n    paddingVertical: 8\n  },\n  nutrientName: {\n    flex: 1\n  },\n  nutrientValue: {\n    fontWeight: 'bold'\n  },\n  ingredientsContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    marginBottom: 16\n  },\n  allergenChip: {\n    margin: 4,\n    backgroundColor: '#ffcdd2'\n  },\n  deleteButton: {\n    marginTop: 16,\n    borderColor: '#B00020'\n  }\n});\nexport default FoodDetailScreen;", "map": {"version": 3, "names": ["React", "useState", "StyleSheet", "View", "ScrollView", "Card", "Chip", "Divider", "IconButton", "Text", "<PERSON><PERSON>", "useTheme", "FoodForm", "jsx", "_jsx", "jsxs", "_jsxs", "FoodDetailScreen", "_ref", "route", "navigation", "_useTheme", "theme", "_ref2", "params", "initialFood", "food", "_ref2$isEditing", "isEditing", "_ref2$isNew", "isNew", "_useState", "_useState2", "_slicedToArray", "isEditMode", "setIsEditMode", "_useState3", "_useState4", "setFood", "handleSave", "savedFood", "setParams", "handleCancel", "goBack", "handleDelete", "onSave", "onCancel", "style", "styles", "container", "backgroundColor", "colors", "background", "children", "card", "Content", "header", "title", "name", "headerActions", "is_favorite", "icon", "iconColor", "primary", "size", "onPress", "brand", "description", "barcode", "chip", "servingSize", "serving_size", "serving_unit", "divider", "sectionTitle", "nutrients", "map", "nutrient", "nutrientRow", "nutrientName", "nutrientValue", "amount", "unit", "nutrient_id", "ingredients", "length", "ingredientsContainer", "filter", "ingredient", "is_allergen", "allergenChip", "ingredient_id", "mode", "deleteButton", "textColor", "error", "create", "flex", "margin", "elevation", "flexDirection", "justifyContent", "alignItems", "fontSize", "fontWeight", "marginBottom", "alignSelf", "marginVertical", "borderBottomWidth", "hairlineWidth", "borderBottomColor", "paddingVertical", "flexWrap", "marginTop", "borderColor"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/screens/FoodDetailScreen.js"], "sourcesContent": ["/**\n * Food Detail Screen for ZnüniZähler\n * Displays and allows editing of food details\n */\n\nimport React, { useState } from 'react';\nimport { StyleSheet, View, ScrollView } from 'react-native';\nimport { Card, Chip, Divider, IconButton, Text, Button } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport FoodForm from '../components/FoodForm';\n\n/**\n * Food Detail Screen Component\n * @param {Object} props - Component props\n * @param {Object} props.route - Route object\n * @param {Object} props.navigation - Navigation object\n * @returns {JSX.Element} - Food detail screen component\n */\nconst FoodDetailScreen = ({ route, navigation }) => {\n  const { theme } = useTheme();\n  const { food: initialFood, isEditing = false, isNew = false } = route.params || {};\n\n  const [isEditMode, setIsEditMode] = useState(isEditing || isNew);\n  const [food, setFood] = useState(initialFood || null);\n\n  // Handle save\n  const handleSave = (savedFood) => {\n    setFood(savedFood);\n    setIsEditMode(false);\n\n    // Update route params\n    navigation.setParams({ food: savedFood, isEditing: false, isNew: false });\n  };\n\n  // Handle cancel\n  const handleCancel = () => {\n    if (isNew) {\n      navigation.goBack();\n    } else {\n      setIsEditMode(false);\n    }\n  };\n\n  // Handle delete\n  const handleDelete = () => {\n    // TODO: Implement delete functionality\n    navigation.goBack();\n  };\n\n  if (isEditMode) {\n    return <FoodForm initialFood={food} onSave={handleSave} onCancel={handleCancel} />;\n  }\n\n  if (!food) {\n    return (\n      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n        <Text>No food data available</Text>\n      </View>\n    );\n  }\n\n  return (\n    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <Card style={styles.card}>\n        <Card.Content>\n          <View style={styles.header}>\n            <Text style={styles.title}>{food.name}</Text>\n            <View style={styles.headerActions}>\n              {food.is_favorite === 1 && (\n                <IconButton\n                  icon=\"star\"\n                  iconColor={theme.colors.primary}\n                  size={24}\n                />\n              )}\n              <IconButton\n                icon=\"pencil\"\n                onPress={() => setIsEditMode(true)}\n                size={24}\n              />\n            </View>\n          </View>\n\n          {food.brand && (\n            <Text style={styles.brand}>{food.brand}</Text>\n          )}\n\n          {food.description && (\n            <Text style={styles.description}>{food.description}</Text>\n          )}\n\n          {food.barcode && (\n            <Chip icon=\"barcode\" style={styles.chip}>\n              {food.barcode}\n            </Chip>\n          )}\n\n          <Text style={styles.servingSize}>\n            Serving size: {food.serving_size} {food.serving_unit}\n          </Text>\n\n          <Divider style={styles.divider} />\n\n          <Text style={styles.sectionTitle}>Nutrition Facts (per 100g)</Text>\n\n          {food.nutrients && food.nutrients.map((nutrient) => (\n            <View key={nutrient.nutrient_id} style={styles.nutrientRow}>\n              <Text style={styles.nutrientName}>{nutrient.name}</Text>\n              <Text style={styles.nutrientValue}>\n                {nutrient.amount} {nutrient.unit}\n              </Text>\n            </View>\n          ))}\n\n          {food.ingredients && food.ingredients.length > 0 && (\n            <View>\n              <Divider style={styles.divider} />\n              <Text style={styles.sectionTitle}>Allergens</Text>\n\n              <View style={styles.ingredientsContainer}>\n                {food.ingredients\n                  .filter(ingredient => ingredient.is_allergen === 1)\n                  .map((ingredient) => (\n                    <Chip\n                      key={ingredient.ingredient_id}\n                      style={styles.allergenChip}\n                    >\n                      {ingredient.name}\n                    </Chip>\n                  ))\n                }\n              </View>\n            </View>\n          )}\n\n          <Divider style={styles.divider} />\n\n          <Button\n            mode=\"outlined\"\n            icon=\"delete\"\n            onPress={handleDelete}\n            style={styles.deleteButton}\n            textColor={theme.colors.error}\n          >\n            Delete Food\n          </Button>\n        </Card.Content>\n      </Card>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  card: {\n    margin: 16,\n    elevation: 2,\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n  },\n  headerActions: {\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 8,\n    flex: 1,\n  },\n  brand: {\n    fontSize: 16,\n    marginBottom: 8,\n  },\n  description: {\n    marginBottom: 16,\n  },\n  chip: {\n    marginBottom: 16,\n    alignSelf: 'flex-start',\n  },\n  servingSize: {\n    marginBottom: 16,\n  },\n  divider: {\n    marginVertical: 16,\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginBottom: 16,\n  },\n  nutrientRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 8,\n    borderBottomWidth: StyleSheet.hairlineWidth,\n    borderBottomColor: '#e0e0e0',\n    paddingVertical: 8,\n  },\n  nutrientName: {\n    flex: 1,\n  },\n  nutrientValue: {\n    fontWeight: 'bold',\n  },\n  ingredientsContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    marginBottom: 16,\n  },\n  allergenChip: {\n    margin: 4,\n    backgroundColor: '#ffcdd2',\n  },\n  deleteButton: {\n    marginTop: 16,\n    borderColor: '#B00020',\n  },\n});\n\nexport default FoodDetailScreen;\n"], "mappings": ";AAKA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAExC,SAASC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAEC,MAAM,QAAQ,oBAAoB;AAClF,SAASC,QAAQ;AACjB,OAAOC,QAAQ;AAA+B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAS9C,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,IAAA,EAA8B;EAAA,IAAxBC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;EAC3C,IAAAC,SAAA,GAAkBV,QAAQ,CAAC,CAAC;IAApBW,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,KAAA,GAAgEJ,KAAK,CAACK,MAAM,IAAI,CAAC,CAAC;IAApEC,WAAW,GAAAF,KAAA,CAAjBG,IAAI;IAAAC,eAAA,GAAAJ,KAAA,CAAeK,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAG,KAAK,GAAAA,eAAA;IAAAE,WAAA,GAAAN,KAAA,CAAEO,KAAK;IAALA,KAAK,GAAAD,WAAA,cAAG,KAAK,GAAAA,WAAA;EAE3D,IAAAE,SAAA,GAAoC9B,QAAQ,CAAC2B,SAAS,IAAIE,KAAK,CAAC;IAAAE,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAzDG,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAChC,IAAAI,UAAA,GAAwBnC,QAAQ,CAACwB,WAAW,IAAI,IAAI,CAAC;IAAAY,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA9CV,IAAI,GAAAW,UAAA;IAAEC,OAAO,GAAAD,UAAA;EAGpB,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAIC,SAAS,EAAK;IAChCF,OAAO,CAACE,SAAS,CAAC;IAClBL,aAAa,CAAC,KAAK,CAAC;IAGpBf,UAAU,CAACqB,SAAS,CAAC;MAAEf,IAAI,EAAEc,SAAS;MAAEZ,SAAS,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAM,CAAC,CAAC;EAC3E,CAAC;EAGD,IAAMY,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAIZ,KAAK,EAAE;MACTV,UAAU,CAACuB,MAAM,CAAC,CAAC;IACrB,CAAC,MAAM;MACLR,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAGD,IAAMS,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IAEzBxB,UAAU,CAACuB,MAAM,CAAC,CAAC;EACrB,CAAC;EAED,IAAIT,UAAU,EAAE;IACd,OAAOpB,IAAA,CAACF,QAAQ;MAACa,WAAW,EAAEC,IAAK;MAACmB,MAAM,EAAEN,UAAW;MAACO,QAAQ,EAAEJ;IAAa,CAAE,CAAC;EACpF;EAEA,IAAI,CAAChB,IAAI,EAAE;IACT,OACEZ,IAAA,CAACX,IAAI;MAAC4C,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;QAAEC,eAAe,EAAE5B,KAAK,CAAC6B,MAAM,CAACC;MAAW,CAAC,CAAE;MAAAC,QAAA,EAC5EvC,IAAA,CAACL,IAAI;QAAA4C,QAAA,EAAC;MAAsB,CAAM;IAAC,CAC/B,CAAC;EAEX;EAEA,OACEvC,IAAA,CAACV,UAAU;IAAC2C,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAE5B,KAAK,CAAC6B,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,EAClFvC,IAAA,CAACT,IAAI;MAAC0C,KAAK,EAAEC,MAAM,CAACM,IAAK;MAAAD,QAAA,EACvBrC,KAAA,CAACX,IAAI,CAACkD,OAAO;QAAAF,QAAA,GACXrC,KAAA,CAACb,IAAI;UAAC4C,KAAK,EAAEC,MAAM,CAACQ,MAAO;UAAAH,QAAA,GACzBvC,IAAA,CAACL,IAAI;YAACsC,KAAK,EAAEC,MAAM,CAACS,KAAM;YAAAJ,QAAA,EAAE3B,IAAI,CAACgC;UAAI,CAAO,CAAC,EAC7C1C,KAAA,CAACb,IAAI;YAAC4C,KAAK,EAAEC,MAAM,CAACW,aAAc;YAAAN,QAAA,GAC/B3B,IAAI,CAACkC,WAAW,KAAK,CAAC,IACrB9C,IAAA,CAACN,UAAU;cACTqD,IAAI,EAAC,MAAM;cACXC,SAAS,EAAExC,KAAK,CAAC6B,MAAM,CAACY,OAAQ;cAChCC,IAAI,EAAE;YAAG,CACV,CACF,EACDlD,IAAA,CAACN,UAAU;cACTqD,IAAI,EAAC,QAAQ;cACbI,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQ9B,aAAa,CAAC,IAAI,CAAC;cAAA,CAAC;cACnC6B,IAAI,EAAE;YAAG,CACV,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC,EAENtC,IAAI,CAACwC,KAAK,IACTpD,IAAA,CAACL,IAAI;UAACsC,KAAK,EAAEC,MAAM,CAACkB,KAAM;UAAAb,QAAA,EAAE3B,IAAI,CAACwC;QAAK,CAAO,CAC9C,EAEAxC,IAAI,CAACyC,WAAW,IACfrD,IAAA,CAACL,IAAI;UAACsC,KAAK,EAAEC,MAAM,CAACmB,WAAY;UAAAd,QAAA,EAAE3B,IAAI,CAACyC;QAAW,CAAO,CAC1D,EAEAzC,IAAI,CAAC0C,OAAO,IACXtD,IAAA,CAACR,IAAI;UAACuD,IAAI,EAAC,SAAS;UAACd,KAAK,EAAEC,MAAM,CAACqB,IAAK;UAAAhB,QAAA,EACrC3B,IAAI,CAAC0C;QAAO,CACT,CACP,EAEDpD,KAAA,CAACP,IAAI;UAACsC,KAAK,EAAEC,MAAM,CAACsB,WAAY;UAAAjB,QAAA,GAAC,gBACjB,EAAC3B,IAAI,CAAC6C,YAAY,EAAC,GAAC,EAAC7C,IAAI,CAAC8C,YAAY;QAAA,CAChD,CAAC,EAEP1D,IAAA,CAACP,OAAO;UAACwC,KAAK,EAAEC,MAAM,CAACyB;QAAQ,CAAE,CAAC,EAElC3D,IAAA,CAACL,IAAI;UAACsC,KAAK,EAAEC,MAAM,CAAC0B,YAAa;UAAArB,QAAA,EAAC;QAA0B,CAAM,CAAC,EAElE3B,IAAI,CAACiD,SAAS,IAAIjD,IAAI,CAACiD,SAAS,CAACC,GAAG,CAAC,UAACC,QAAQ;UAAA,OAC7C7D,KAAA,CAACb,IAAI;YAA4B4C,KAAK,EAAEC,MAAM,CAAC8B,WAAY;YAAAzB,QAAA,GACzDvC,IAAA,CAACL,IAAI;cAACsC,KAAK,EAAEC,MAAM,CAAC+B,YAAa;cAAA1B,QAAA,EAAEwB,QAAQ,CAACnB;YAAI,CAAO,CAAC,EACxD1C,KAAA,CAACP,IAAI;cAACsC,KAAK,EAAEC,MAAM,CAACgC,aAAc;cAAA3B,QAAA,GAC/BwB,QAAQ,CAACI,MAAM,EAAC,GAAC,EAACJ,QAAQ,CAACK,IAAI;YAAA,CAC5B,CAAC;UAAA,GAJEL,QAAQ,CAACM,WAKd,CAAC;QAAA,CACR,CAAC,EAEDzD,IAAI,CAAC0D,WAAW,IAAI1D,IAAI,CAAC0D,WAAW,CAACC,MAAM,GAAG,CAAC,IAC9CrE,KAAA,CAACb,IAAI;UAAAkD,QAAA,GACHvC,IAAA,CAACP,OAAO;YAACwC,KAAK,EAAEC,MAAM,CAACyB;UAAQ,CAAE,CAAC,EAClC3D,IAAA,CAACL,IAAI;YAACsC,KAAK,EAAEC,MAAM,CAAC0B,YAAa;YAAArB,QAAA,EAAC;UAAS,CAAM,CAAC,EAElDvC,IAAA,CAACX,IAAI;YAAC4C,KAAK,EAAEC,MAAM,CAACsC,oBAAqB;YAAAjC,QAAA,EACtC3B,IAAI,CAAC0D,WAAW,CACdG,MAAM,CAAC,UAAAC,UAAU;cAAA,OAAIA,UAAU,CAACC,WAAW,KAAK,CAAC;YAAA,EAAC,CAClDb,GAAG,CAAC,UAACY,UAAU;cAAA,OACd1E,IAAA,CAACR,IAAI;gBAEHyC,KAAK,EAAEC,MAAM,CAAC0C,YAAa;gBAAArC,QAAA,EAE1BmC,UAAU,CAAC9B;cAAI,GAHX8B,UAAU,CAACG,aAIZ,CAAC;YAAA,CACR;UAAC,CAEA,CAAC;QAAA,CACH,CACP,EAED7E,IAAA,CAACP,OAAO;UAACwC,KAAK,EAAEC,MAAM,CAACyB;QAAQ,CAAE,CAAC,EAElC3D,IAAA,CAACJ,MAAM;UACLkF,IAAI,EAAC,UAAU;UACf/B,IAAI,EAAC,QAAQ;UACbI,OAAO,EAAErB,YAAa;UACtBG,KAAK,EAAEC,MAAM,CAAC6C,YAAa;UAC3BC,SAAS,EAAExE,KAAK,CAAC6B,MAAM,CAAC4C,KAAM;UAAA1C,QAAA,EAC/B;QAED,CAAQ,CAAC;MAAA,CACG;IAAC,CACX;EAAC,CACG,CAAC;AAEjB,CAAC;AAED,IAAML,MAAM,GAAG9C,UAAU,CAAC8F,MAAM,CAAC;EAC/B/C,SAAS,EAAE;IACTgD,IAAI,EAAE;EACR,CAAC;EACD3C,IAAI,EAAE;IACJ4C,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC;EACD3C,MAAM,EAAE;IACN4C,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACD3C,aAAa,EAAE;IACbyC,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE;EACd,CAAC;EACD7C,KAAK,EAAE;IACL8C,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,CAAC;IACfR,IAAI,EAAE;EACR,CAAC;EACD/B,KAAK,EAAE;IACLqC,QAAQ,EAAE,EAAE;IACZE,YAAY,EAAE;EAChB,CAAC;EACDtC,WAAW,EAAE;IACXsC,YAAY,EAAE;EAChB,CAAC;EACDpC,IAAI,EAAE;IACJoC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDpC,WAAW,EAAE;IACXmC,YAAY,EAAE;EAChB,CAAC;EACDhC,OAAO,EAAE;IACPkC,cAAc,EAAE;EAClB,CAAC;EACDjC,YAAY,EAAE;IACZ6B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD3B,WAAW,EAAE;IACXsB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBG,YAAY,EAAE,CAAC;IACfG,iBAAiB,EAAE1G,UAAU,CAAC2G,aAAa;IAC3CC,iBAAiB,EAAE,SAAS;IAC5BC,eAAe,EAAE;EACnB,CAAC;EACDhC,YAAY,EAAE;IACZkB,IAAI,EAAE;EACR,CAAC;EACDjB,aAAa,EAAE;IACbwB,UAAU,EAAE;EACd,CAAC;EACDlB,oBAAoB,EAAE;IACpBc,aAAa,EAAE,KAAK;IACpBY,QAAQ,EAAE,MAAM;IAChBP,YAAY,EAAE;EAChB,CAAC;EACDf,YAAY,EAAE;IACZQ,MAAM,EAAE,CAAC;IACThD,eAAe,EAAE;EACnB,CAAC;EACD2C,YAAY,EAAE;IACZoB,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AAEF,eAAejG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}