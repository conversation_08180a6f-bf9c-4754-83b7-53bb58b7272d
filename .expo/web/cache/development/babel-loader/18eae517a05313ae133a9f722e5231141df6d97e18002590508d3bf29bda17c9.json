{"ast": null, "code": "import * as React from 'react';\nvar NavigationContent = function NavigationContent(_ref) {\n  var render = _ref.render,\n    children = _ref.children;\n  return render(children);\n};\nexport default function useComponent(render) {\n  var renderRef = React.useRef(render);\n  renderRef.current = render;\n  React.useEffect(function () {\n    renderRef.current = null;\n  });\n  return React.useRef(function (_ref2) {\n    var children = _ref2.children;\n    var render = renderRef.current;\n    if (render === null) {\n      throw new Error('The returned component must be rendered in the same render phase as the hook.');\n    }\n    return React.createElement(NavigationContent, {\n      render: render\n    }, children);\n  }).current;\n}", "map": {"version": 3, "names": ["React", "NavigationContent", "_ref", "render", "children", "useComponent", "renderRef", "useRef", "current", "useEffect", "_ref2", "Error", "createElement"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/useComponent.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Render = (children: React.ReactNode) => JSX.Element;\n\ntype Props = {\n  render: Render;\n  children: React.ReactNode;\n};\n\nconst NavigationContent = ({ render, children }: Props) => {\n  return render(children);\n};\n\nexport default function useComponent(render: Render) {\n  const renderRef = React.useRef<Render | null>(render);\n\n  // Normally refs shouldn't be mutated in render\n  // But we return a component which will be rendered\n  // So it's just for immediate consumption\n  renderRef.current = render;\n\n  React.useEffect(() => {\n    renderRef.current = null;\n  });\n\n  return React.useRef(({ children }: { children: React.ReactNode }) => {\n    const render = renderRef.current;\n\n    if (render === null) {\n      throw new Error(\n        'The returned component must be rendered in the same render phase as the hook.'\n      );\n    }\n\n    return <NavigationContent render={render}>{children}</NavigationContent>;\n  }).current;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAS9B,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAGC,IAAA,EAAiC;EAAA,IAA9BC,MAAM,GAAmBD,IAAA,CAAzBC,MAAM;IAAEC,QAAA,GAAiBF,IAAA,CAAjBE,QAAA;EACnC,OAAOD,MAAM,CAACC,QAAQ,CAAC;AACzB,CAAC;AAED,eAAe,SAASC,YAAYA,CAACF,MAAc,EAAE;EACnD,IAAMG,SAAS,GAAGN,KAAK,CAACO,MAAM,CAAgBJ,MAAM,CAAC;EAKrDG,SAAS,CAACE,OAAO,GAAGL,MAAM;EAE1BH,KAAK,CAACS,SAAS,CAAC,YAAM;IACpBH,SAAS,CAACE,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,OAAOR,KAAK,CAACO,MAAM,CAAC,UAAAG,KAAA,EAAiD;IAAA,IAA9CN,QAAA,GAAyCM,KAAA,CAAzCN,QAAA;IACrB,IAAMD,MAAM,GAAGG,SAAS,CAACE,OAAO;IAEhC,IAAIL,MAAM,KAAK,IAAI,EAAE;MACnB,MAAM,IAAIQ,KAAK,CACb,+EAA+E,CAChF;IACH;IAEA,OAAOX,KAAA,CAAAY,aAAA,CAACX,iBAAiB;MAACE,MAAM,EAAEA;IAAO,GAAEC,QAAQ,CAAqB;EAC1E,CAAC,CAAC,CAACI,OAAO;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}