{"ast": null, "code": "import { Asset, ANDROID_EMBEDDED_URL_BASE_RESOURCE } from \"./Asset\";\nimport { IS_ENV_WITH_LOCAL_ASSETS } from \"./PlatformUtils\";\nimport resolveAssetSource, { setCustomSourceTransformer } from \"./resolveAssetSource\";\nif (IS_ENV_WITH_LOCAL_ASSETS) {\n  var setTransformer = resolveAssetSource.setCustomSourceTransformer || setCustomSourceTransformer;\n  setTransformer(function (resolver) {\n    try {\n      if ('fileHashes' in resolver.asset && resolver.asset.fileHashes) {\n        var asset = Asset.fromMetadata(resolver.asset);\n        if (asset.uri.startsWith(ANDROID_EMBEDDED_URL_BASE_RESOURCE)) {\n          return resolver.resourceIdentifierWithoutScale();\n        }\n        return resolver.fromSource(asset.downloaded ? asset.localUri : asset.uri);\n      } else {\n        return resolver.defaultAsset();\n      }\n    } catch (_unused) {\n      return resolver.defaultAsset();\n    }\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "ANDROID_EMBEDDED_URL_BASE_RESOURCE", "IS_ENV_WITH_LOCAL_ASSETS", "resolveAssetSource", "setCustomSourceTransformer", "setTransformer", "resolver", "asset", "fileHashes", "fromMetadata", "uri", "startsWith", "resourceIdentifierWithoutScale", "fromSource", "downloaded", "localUri", "defaultAsset", "_unused"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/expo-asset/src/Asset.fx.ts"], "sourcesContent": ["import type { default as AssetSourceResolver } from 'react-native/Libraries/Image/AssetSourceResolver';\n\nimport { Asset, ANDROID_EMBEDDED_URL_BASE_RESOURCE } from './Asset';\nimport { IS_ENV_WITH_LOCAL_ASSETS } from './PlatformUtils';\nimport resolveAssetSource, { setCustomSourceTransformer } from './resolveAssetSource';\n\n// Override React Native's asset resolution for `Image` components in contexts where it matters\nif (IS_ENV_WITH_LOCAL_ASSETS) {\n  const setTransformer =\n    resolveAssetSource.setCustomSourceTransformer || setCustomSourceTransformer;\n  setTransformer((resolver) => {\n    try {\n      // Bund<PERSON> is using the hashAssetFiles plugin if and only if the fileHashes property exists\n      if ('fileHashes' in resolver.asset && resolver.asset.fileHashes) {\n        const asset = Asset.fromMetadata(resolver.asset);\n        if (asset.uri.startsWith(ANDROID_EMBEDDED_URL_BASE_RESOURCE)) {\n          // TODO(@kitten): See https://github.com/expo/expo/commit/ec940b57a87d99ab4f1d06d87126e662c3f04f04#r155340943\n          // It's unclear whether this is sound since this may be our own AssetSourceResolver, which doesn't have this method\n          // Please compare `AssetSourceResolver` type from `react-native/Libraries/Image/AssetSourceResolver` against `./AssetSourceResolver`\n          return (\n            resolver as unknown as AssetSourceResolver\n          ).resourceIdentifierWithoutScale() as any;\n        }\n        return resolver.fromSource(asset.downloaded ? asset.localUri! : asset.uri);\n      } else {\n        return resolver.defaultAsset();\n      }\n    } catch {\n      return resolver.defaultAsset();\n    }\n  });\n}\n"], "mappings": "AAEA,SAASA,KAAK,EAAEC,kCAAkC;AAClD,SAASC,wBAAwB;AACjC,OAAOC,kBAAkB,IAAIC,0BAA0B;AAGvD,IAAIF,wBAAwB,EAAE;EAC5B,IAAMG,cAAc,GAClBF,kBAAkB,CAACC,0BAA0B,IAAIA,0BAA0B;EAC7EC,cAAc,CAAC,UAACC,QAAQ,EAAI;IAC1B,IAAI;MAEF,IAAI,YAAY,IAAIA,QAAQ,CAACC,KAAK,IAAID,QAAQ,CAACC,KAAK,CAACC,UAAU,EAAE;QAC/D,IAAMD,KAAK,GAAGP,KAAK,CAACS,YAAY,CAACH,QAAQ,CAACC,KAAK,CAAC;QAChD,IAAIA,KAAK,CAACG,GAAG,CAACC,UAAU,CAACV,kCAAkC,CAAC,EAAE;UAI5D,OACEK,QACD,CAACM,8BAA8B,EAAS;QAC3C;QACA,OAAON,QAAQ,CAACO,UAAU,CAACN,KAAK,CAACO,UAAU,GAAGP,KAAK,CAACQ,QAAS,GAAGR,KAAK,CAACG,GAAG,CAAC;MAC5E,CAAC,MAAM;QACL,OAAOJ,QAAQ,CAACU,YAAY,EAAE;MAChC;IACF,CAAC,CAAC,OAAAC,OAAA,EAAM;MACN,OAAOX,QAAQ,CAACU,YAAY,EAAE;IAChC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}