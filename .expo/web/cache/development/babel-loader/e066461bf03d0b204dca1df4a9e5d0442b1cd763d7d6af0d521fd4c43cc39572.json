{"ast": null, "code": "import * as React from 'react';\nexport var forwardRef = React.forwardRef;", "map": {"version": 3, "names": ["React", "forwardRef"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/utils/forwardRef.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type {\n  ForwardRefRenderFunction,\n  PropsWithoutRef,\n  RefAttributes,\n  ForwardRefExoticComponent,\n} from 'react';\n\nexport type ForwardRefComponent<T, P = {}> = ForwardRefExoticComponent<\n  PropsWithoutRef<P> & RefAttributes<T>\n>;\n\n/**\n * TypeScript generated a large union of props from `ViewProps` in\n * `d.ts` files when using `React.forwardRef`. To prevent this\n * `ForwardRefComponent` was created and exported. Use this\n * `forwardRef` instead of `React.forwardRef` so you don't have to\n * import `ForwardRefComponent`.\n * More info: https://github.com/callstack/react-native-paper/pull/3603\n */\nexport const forwardRef: <T, P = {}>(\n  render: ForwardRefRenderFunction<T, P>\n) => ForwardRefComponent<T, P> = React.forwardRef;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAoB9B,OAAO,IAAMC,UAEiB,GAAGD,KAAK,CAACC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}