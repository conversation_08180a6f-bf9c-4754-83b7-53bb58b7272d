{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"active\", \"children\", \"onRequestClose\", \"transparent\"];\nimport * as React from 'react';\nimport View from \"../View\";\nimport StyleSheet from \"../StyleSheet\";\nimport canUseDOM from \"../../modules/canUseDom\";\nvar ModalContent = React.forwardRef(function (props, forwardedRef) {\n  var active = props.active,\n    children = props.children,\n    onRequestClose = props.onRequestClose,\n    transparent = props.transparent,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  React.useEffect(function () {\n    if (canUseDOM) {\n      var closeOnEscape = function closeOnEscape(e) {\n        if (active && e.key === 'Escape') {\n          e.stopPropagation();\n          if (onRequestClose) {\n            onRequestClose();\n          }\n        }\n      };\n      document.addEventListener('keyup', closeOnEscape, false);\n      return function () {\n        return document.removeEventListener('keyup', closeOnEscape, false);\n      };\n    }\n  }, [active, onRequestClose]);\n  var style = React.useMemo(function () {\n    return [styles.modal, transparent ? styles.modalTransparent : styles.modalOpaque];\n  }, [transparent]);\n  return React.createElement(View, _extends({}, rest, {\n    \"aria-modal\": true,\n    ref: forwardedRef,\n    role: active ? 'dialog' : null,\n    style: style\n  }), React.createElement(View, {\n    style: styles.container\n  }, children));\n});\nvar styles = StyleSheet.create({\n  modal: {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  },\n  modalTransparent: {\n    backgroundColor: 'transparent'\n  },\n  modalOpaque: {\n    backgroundColor: 'white'\n  },\n  container: {\n    top: 0,\n    flex: 1\n  }\n});\nexport default ModalContent;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "View", "StyleSheet", "canUseDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "active", "children", "onRequestClose", "transparent", "rest", "useEffect", "closeOnEscape", "e", "key", "stopPropagation", "document", "addEventListener", "removeEventListener", "style", "useMemo", "styles", "modal", "modalTransparent", "modalOpaque", "createElement", "ref", "role", "container", "create", "position", "top", "right", "bottom", "left", "backgroundColor", "flex"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Modal/ModalContent.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"active\", \"children\", \"onRequestClose\", \"transparent\"];\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport View from '../View';\nimport StyleSheet from '../StyleSheet';\nimport canUseDOM from '../../modules/canUseDom';\nvar ModalContent = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var active = props.active,\n    children = props.children,\n    onRequestClose = props.onRequestClose,\n    transparent = props.transparent,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  React.useEffect(() => {\n    if (canUseDOM) {\n      var closeOnEscape = e => {\n        if (active && e.key === 'Escape') {\n          e.stopPropagation();\n          if (onRequestClose) {\n            onRequestClose();\n          }\n        }\n      };\n      document.addEventListener('keyup', closeOnEscape, false);\n      return () => document.removeEventListener('keyup', closeOnEscape, false);\n    }\n  }, [active, onRequestClose]);\n  var style = React.useMemo(() => {\n    return [styles.modal, transparent ? styles.modalTransparent : styles.modalOpaque];\n  }, [transparent]);\n  return /*#__PURE__*/React.createElement(View, _extends({}, rest, {\n    \"aria-modal\": true,\n    ref: forwardedRef,\n    role: active ? 'dialog' : null,\n    style: style\n  }), /*#__PURE__*/React.createElement(View, {\n    style: styles.container\n  }, children));\n});\nvar styles = StyleSheet.create({\n  modal: {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  },\n  modalTransparent: {\n    backgroundColor: 'transparent'\n  },\n  modalOpaque: {\n    backgroundColor: 'white'\n  },\n  container: {\n    top: 0,\n    flex: 1\n  }\n});\nexport default ModalContent;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,CAAC;AAWvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI;AACX,OAAOC,UAAU;AACjB,OAAOC,SAAS;AAChB,IAAIC,YAAY,GAAgBJ,KAAK,CAACK,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACxE,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACvBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,IAAI,GAAGd,6BAA6B,CAACQ,KAAK,EAAEP,SAAS,CAAC;EACxDC,KAAK,CAACa,SAAS,CAAC,YAAM;IACpB,IAAIV,SAAS,EAAE;MACb,IAAIW,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,CAAC,EAAI;QACvB,IAAIP,MAAM,IAAIO,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;UAChCD,CAAC,CAACE,eAAe,CAAC,CAAC;UACnB,IAAIP,cAAc,EAAE;YAClBA,cAAc,CAAC,CAAC;UAClB;QACF;MACF,CAAC;MACDQ,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEL,aAAa,EAAE,KAAK,CAAC;MACxD,OAAO;QAAA,OAAMI,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEN,aAAa,EAAE,KAAK,CAAC;MAAA;IAC1E;EACF,CAAC,EAAE,CAACN,MAAM,EAAEE,cAAc,CAAC,CAAC;EAC5B,IAAIW,KAAK,GAAGrB,KAAK,CAACsB,OAAO,CAAC,YAAM;IAC9B,OAAO,CAACC,MAAM,CAACC,KAAK,EAAEb,WAAW,GAAGY,MAAM,CAACE,gBAAgB,GAAGF,MAAM,CAACG,WAAW,CAAC;EACnF,CAAC,EAAE,CAACf,WAAW,CAAC,CAAC;EACjB,OAAoBX,KAAK,CAAC2B,aAAa,CAAC1B,IAAI,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEe,IAAI,EAAE;IAC/D,YAAY,EAAE,IAAI;IAClBgB,GAAG,EAAErB,YAAY;IACjBsB,IAAI,EAAErB,MAAM,GAAG,QAAQ,GAAG,IAAI;IAC9Ba,KAAK,EAAEA;EACT,CAAC,CAAC,EAAerB,KAAK,CAAC2B,aAAa,CAAC1B,IAAI,EAAE;IACzCoB,KAAK,EAAEE,MAAM,CAACO;EAChB,CAAC,EAAErB,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF,IAAIc,MAAM,GAAGrB,UAAU,CAAC6B,MAAM,CAAC;EAC7BP,KAAK,EAAE;IACLQ,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;EACDX,gBAAgB,EAAE;IAChBY,eAAe,EAAE;EACnB,CAAC;EACDX,WAAW,EAAE;IACXW,eAAe,EAAE;EACnB,CAAC;EACDP,SAAS,EAAE;IACTG,GAAG,EAAE,CAAC;IACNK,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AACF,eAAelC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}