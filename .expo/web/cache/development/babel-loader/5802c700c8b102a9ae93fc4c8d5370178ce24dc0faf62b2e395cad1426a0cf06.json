{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { nanoid } from 'nanoid/non-secure';\nimport * as React from 'react';\nimport { SingleNavigatorContext } from \"./EnsureSingleNavigator\";\nexport default function useRegisterNavigator() {\n  var _React$useState = React.useState(function () {\n      return nanoid();\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    key = _React$useState2[0];\n  var container = React.useContext(SingleNavigatorContext);\n  if (container === undefined) {\n    throw new Error(\"Couldn't register the navigator. Have you wrapped your app with 'NavigationContainer'?\\n\\nThis can also happen if there are multiple copies of '@react-navigation' packages installed.\");\n  }\n  React.useEffect(function () {\n    var register = container.register,\n      unregister = container.unregister;\n    register(key);\n    return function () {\n      return unregister(key);\n    };\n  }, [container, key]);\n  return key;\n}", "map": {"version": 3, "names": ["nanoid", "React", "SingleNavigatorContext", "useRegisterNavigator", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "key", "container", "useContext", "undefined", "Error", "useEffect", "register", "unregister"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/useRegisterNavigator.tsx"], "sourcesContent": ["import { nanoid } from 'nanoid/non-secure';\nimport * as React from 'react';\n\nimport { SingleNavigatorContext } from './EnsureSingleNavigator';\n\n/**\n * Register a navigator in the parent context (either a navigation container or a screen).\n * This is used to prevent multiple navigators under a single container or screen.\n */\nexport default function useRegisterNavigator() {\n  const [key] = React.useState(() => nanoid());\n  const container = React.useContext(SingleNavigatorContext);\n\n  if (container === undefined) {\n    throw new Error(\n      \"Couldn't register the navigator. Have you wrapped your app with 'NavigationContainer'?\\n\\nThis can also happen if there are multiple copies of '@react-navigation' packages installed.\"\n    );\n  }\n\n  React.useEffect(() => {\n    const { register, unregister } = container;\n\n    register(key);\n\n    return () => unregister(key);\n  }, [container, key]);\n\n  return key;\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,sBAAsB;AAM/B,eAAe,SAASC,oBAAoBA,CAAA,EAAG;EAC7C,IAAAC,eAAA,GAAcH,KAAK,CAACI,QAAQ,CAAC;MAAA,OAAML,MAAM,EAAE;IAAA,EAAC;IAAAM,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAArCI,GAAG,GAAAF,gBAAA;EACV,IAAMG,SAAS,GAAGR,KAAK,CAACS,UAAU,CAACR,sBAAsB,CAAC;EAE1D,IAAIO,SAAS,KAAKE,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,wLAAwL,CACzL;EACH;EAEAX,KAAK,CAACY,SAAS,CAAC,YAAM;IACpB,IAAQC,QAAQ,GAAiBL,SAAS,CAAlCK,QAAQ;MAAEC,UAAA,GAAeN,SAAS,CAAxBM,UAAA;IAElBD,QAAQ,CAACN,GAAG,CAAC;IAEb,OAAO;MAAA,OAAMO,UAAU,CAACP,GAAG,CAAC;IAAA;EAC9B,CAAC,EAAE,CAACC,SAAS,EAAED,GAAG,CAAC,CAAC;EAEpB,OAAOA,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}