{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport { EventEmitter, UnavailabilityError } from 'expo-modules-core';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { v4 as uuidv4 } from 'uuid';\nimport ExponentFileSystem from \"./ExponentFileSystem\";\nimport { FileSystemSessionType, FileSystemUploadType } from \"./FileSystem.types\";\nif (!ExponentFileSystem) {\n  console.warn(\"No native ExponentFileSystem module found, are you sure the expo-file-system's module is linked properly?\");\n}\nvar _unused = new EventEmitter(ExponentFileSystem);\nfunction normalizeEndingSlash(p) {\n  if (p != null) {\n    return p.replace(/\\/*$/, '') + '/';\n  }\n  return null;\n}\nexport var documentDirectory = normalizeEndingSlash(ExponentFileSystem.documentDirectory);\nexport var cacheDirectory = normalizeEndingSlash(ExponentFileSystem.cacheDirectory);\nvar bundledAssets = ExponentFileSystem.bundledAssets,\n  bundleDirectory = ExponentFileSystem.bundleDirectory;\nexport { bundledAssets, bundleDirectory };\nexport function getInfoAsync(_x) {\n  return _getInfoAsync.apply(this, arguments);\n}\nfunction _getInfoAsync() {\n  _getInfoAsync = _asyncToGenerator(function* (fileUri) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!ExponentFileSystem.getInfoAsync) {\n      throw new UnavailabilityError('expo-file-system', 'getInfoAsync');\n    }\n    return yield ExponentFileSystem.getInfoAsync(fileUri, options);\n  });\n  return _getInfoAsync.apply(this, arguments);\n}\nexport function readAsStringAsync(_x2) {\n  return _readAsStringAsync.apply(this, arguments);\n}\nfunction _readAsStringAsync() {\n  _readAsStringAsync = _asyncToGenerator(function* (fileUri) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!ExponentFileSystem.readAsStringAsync) {\n      throw new UnavailabilityError('expo-file-system', 'readAsStringAsync');\n    }\n    return yield ExponentFileSystem.readAsStringAsync(fileUri, options);\n  });\n  return _readAsStringAsync.apply(this, arguments);\n}\nexport function getContentUriAsync(_x3) {\n  return _getContentUriAsync.apply(this, arguments);\n}\nfunction _getContentUriAsync() {\n  _getContentUriAsync = _asyncToGenerator(function* (fileUri) {\n    if (Platform.OS === 'android') {\n      if (!ExponentFileSystem.getContentUriAsync) {\n        throw new UnavailabilityError('expo-file-system', 'getContentUriAsync');\n      }\n      return yield ExponentFileSystem.getContentUriAsync(fileUri);\n    } else {\n      return fileUri;\n    }\n  });\n  return _getContentUriAsync.apply(this, arguments);\n}\nexport function writeAsStringAsync(_x4, _x5) {\n  return _writeAsStringAsync.apply(this, arguments);\n}\nfunction _writeAsStringAsync() {\n  _writeAsStringAsync = _asyncToGenerator(function* (fileUri, contents) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    if (!ExponentFileSystem.writeAsStringAsync) {\n      throw new UnavailabilityError('expo-file-system', 'writeAsStringAsync');\n    }\n    return yield ExponentFileSystem.writeAsStringAsync(fileUri, contents, options);\n  });\n  return _writeAsStringAsync.apply(this, arguments);\n}\nexport function deleteAsync(_x6) {\n  return _deleteAsync.apply(this, arguments);\n}\nfunction _deleteAsync() {\n  _deleteAsync = _asyncToGenerator(function* (fileUri) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!ExponentFileSystem.deleteAsync) {\n      throw new UnavailabilityError('expo-file-system', 'deleteAsync');\n    }\n    return yield ExponentFileSystem.deleteAsync(fileUri, options);\n  });\n  return _deleteAsync.apply(this, arguments);\n}\nexport function deleteLegacyDocumentDirectoryAndroid() {\n  return _deleteLegacyDocumentDirectoryAndroid.apply(this, arguments);\n}\nfunction _deleteLegacyDocumentDirectoryAndroid() {\n  _deleteLegacyDocumentDirectoryAndroid = _asyncToGenerator(function* () {\n    if (Platform.OS !== 'android' || documentDirectory == null) {\n      return;\n    }\n    var legacyDocumentDirectory = `${documentDirectory}ExperienceData/`;\n    return yield deleteAsync(legacyDocumentDirectory, {\n      idempotent: true\n    });\n  });\n  return _deleteLegacyDocumentDirectoryAndroid.apply(this, arguments);\n}\nexport function moveAsync(_x7) {\n  return _moveAsync.apply(this, arguments);\n}\nfunction _moveAsync() {\n  _moveAsync = _asyncToGenerator(function* (options) {\n    if (!ExponentFileSystem.moveAsync) {\n      throw new UnavailabilityError('expo-file-system', 'moveAsync');\n    }\n    return yield ExponentFileSystem.moveAsync(options);\n  });\n  return _moveAsync.apply(this, arguments);\n}\nexport function copyAsync(_x8) {\n  return _copyAsync.apply(this, arguments);\n}\nfunction _copyAsync() {\n  _copyAsync = _asyncToGenerator(function* (options) {\n    if (!ExponentFileSystem.copyAsync) {\n      throw new UnavailabilityError('expo-file-system', 'copyAsync');\n    }\n    return yield ExponentFileSystem.copyAsync(options);\n  });\n  return _copyAsync.apply(this, arguments);\n}\nexport function makeDirectoryAsync(_x9) {\n  return _makeDirectoryAsync.apply(this, arguments);\n}\nfunction _makeDirectoryAsync() {\n  _makeDirectoryAsync = _asyncToGenerator(function* (fileUri) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!ExponentFileSystem.makeDirectoryAsync) {\n      throw new UnavailabilityError('expo-file-system', 'makeDirectoryAsync');\n    }\n    return yield ExponentFileSystem.makeDirectoryAsync(fileUri, options);\n  });\n  return _makeDirectoryAsync.apply(this, arguments);\n}\nexport function readDirectoryAsync(_x0) {\n  return _readDirectoryAsync.apply(this, arguments);\n}\nfunction _readDirectoryAsync() {\n  _readDirectoryAsync = _asyncToGenerator(function* (fileUri) {\n    if (!ExponentFileSystem.readDirectoryAsync) {\n      throw new UnavailabilityError('expo-file-system', 'readDirectoryAsync');\n    }\n    return yield ExponentFileSystem.readDirectoryAsync(fileUri);\n  });\n  return _readDirectoryAsync.apply(this, arguments);\n}\nexport function getFreeDiskStorageAsync() {\n  return _getFreeDiskStorageAsync.apply(this, arguments);\n}\nfunction _getFreeDiskStorageAsync() {\n  _getFreeDiskStorageAsync = _asyncToGenerator(function* () {\n    if (!ExponentFileSystem.getFreeDiskStorageAsync) {\n      throw new UnavailabilityError('expo-file-system', 'getFreeDiskStorageAsync');\n    }\n    return yield ExponentFileSystem.getFreeDiskStorageAsync();\n  });\n  return _getFreeDiskStorageAsync.apply(this, arguments);\n}\nexport function getTotalDiskCapacityAsync() {\n  return _getTotalDiskCapacityAsync.apply(this, arguments);\n}\nfunction _getTotalDiskCapacityAsync() {\n  _getTotalDiskCapacityAsync = _asyncToGenerator(function* () {\n    if (!ExponentFileSystem.getTotalDiskCapacityAsync) {\n      throw new UnavailabilityError('expo-file-system', 'getTotalDiskCapacityAsync');\n    }\n    return yield ExponentFileSystem.getTotalDiskCapacityAsync();\n  });\n  return _getTotalDiskCapacityAsync.apply(this, arguments);\n}\nexport function downloadAsync(_x1, _x10) {\n  return _downloadAsync.apply(this, arguments);\n}\nfunction _downloadAsync() {\n  _downloadAsync = _asyncToGenerator(function* (uri, fileUri) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    if (!ExponentFileSystem.downloadAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadAsync');\n    }\n    return yield ExponentFileSystem.downloadAsync(uri, fileUri, _objectSpread({\n      sessionType: FileSystemSessionType.BACKGROUND\n    }, options));\n  });\n  return _downloadAsync.apply(this, arguments);\n}\nexport function uploadAsync(_x11, _x12) {\n  return _uploadAsync.apply(this, arguments);\n}\nfunction _uploadAsync() {\n  _uploadAsync = _asyncToGenerator(function* (url, fileUri) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    if (!ExponentFileSystem.uploadAsync) {\n      throw new UnavailabilityError('expo-file-system', 'uploadAsync');\n    }\n    return yield ExponentFileSystem.uploadAsync(url, fileUri, _objectSpread(_objectSpread({\n      sessionType: FileSystemSessionType.BACKGROUND,\n      uploadType: FileSystemUploadType.BINARY_CONTENT\n    }, options), {}, {\n      httpMethod: (options.httpMethod || 'POST').toUpperCase()\n    }));\n  });\n  return _uploadAsync.apply(this, arguments);\n}\nexport function createDownloadResumable(uri, fileUri, options, callback, resumeData) {\n  return new DownloadResumable(uri, fileUri, options, callback, resumeData);\n}\nexport function createUploadTask(url, fileUri, options, callback) {\n  return new UploadTask(url, fileUri, options, callback);\n}\nexport var FileSystemCancellableNetworkTask = function () {\n  function FileSystemCancellableNetworkTask() {\n    _classCallCheck(this, FileSystemCancellableNetworkTask);\n    this._uuid = uuidv4();\n    this.taskWasCanceled = false;\n    this.emitter = new EventEmitter(ExponentFileSystem);\n  }\n  return _createClass(FileSystemCancellableNetworkTask, [{\n    key: \"cancelAsync\",\n    value: function () {\n      var _cancelAsync = _asyncToGenerator(function* () {\n        if (!ExponentFileSystem.networkTaskCancelAsync) {\n          throw new UnavailabilityError('expo-file-system', 'networkTaskCancelAsync');\n        }\n        this.removeSubscription();\n        this.taskWasCanceled = true;\n        return yield ExponentFileSystem.networkTaskCancelAsync(this.uuid);\n      });\n      function cancelAsync() {\n        return _cancelAsync.apply(this, arguments);\n      }\n      return cancelAsync;\n    }()\n  }, {\n    key: \"isTaskCancelled\",\n    value: function isTaskCancelled() {\n      if (this.taskWasCanceled) {\n        console.warn('This task was already canceled.');\n        return true;\n      }\n      return false;\n    }\n  }, {\n    key: \"uuid\",\n    get: function get() {\n      return this._uuid;\n    }\n  }, {\n    key: \"addSubscription\",\n    value: function addSubscription() {\n      var _this = this;\n      if (this.subscription) {\n        return;\n      }\n      this.subscription = this.emitter.addListener(this.getEventName(), function (event) {\n        if (event.uuid === _this.uuid) {\n          var callback = _this.getCallback();\n          if (callback) {\n            callback(event.data);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"removeSubscription\",\n    value: function removeSubscription() {\n      if (!this.subscription) {\n        return;\n      }\n      this.emitter.removeSubscription(this.subscription);\n      this.subscription = null;\n    }\n  }]);\n}();\nexport var UploadTask = function (_FileSystemCancellabl) {\n  function UploadTask(url, fileUri, options, callback) {\n    var _options$httpMethod;\n    var _this2;\n    _classCallCheck(this, UploadTask);\n    _this2 = _callSuper(this, UploadTask);\n    _this2.url = url;\n    _this2.fileUri = fileUri;\n    _this2.callback = callback;\n    var httpMethod = (options == null ? void 0 : (_options$httpMethod = options.httpMethod) == null ? void 0 : _options$httpMethod.toUpperCase()) || 'POST';\n    _this2.options = _objectSpread(_objectSpread({\n      sessionType: FileSystemSessionType.BACKGROUND,\n      uploadType: FileSystemUploadType.BINARY_CONTENT\n    }, options), {}, {\n      httpMethod: httpMethod\n    });\n    return _this2;\n  }\n  _inherits(UploadTask, _FileSystemCancellabl);\n  return _createClass(UploadTask, [{\n    key: \"getEventName\",\n    value: function getEventName() {\n      return 'expo-file-system.uploadProgress';\n    }\n  }, {\n    key: \"getCallback\",\n    value: function getCallback() {\n      return this.callback;\n    }\n  }, {\n    key: \"uploadAsync\",\n    value: function () {\n      var _uploadAsync2 = _asyncToGenerator(function* () {\n        if (!ExponentFileSystem.uploadTaskStartAsync) {\n          throw new UnavailabilityError('expo-file-system', 'uploadTaskStartAsync');\n        }\n        if (this.isTaskCancelled()) {\n          return;\n        }\n        this.addSubscription();\n        var result = yield ExponentFileSystem.uploadTaskStartAsync(this.url, this.fileUri, this.uuid, this.options);\n        this.removeSubscription();\n        return result;\n      });\n      function uploadAsync() {\n        return _uploadAsync2.apply(this, arguments);\n      }\n      return uploadAsync;\n    }()\n  }]);\n}(FileSystemCancellableNetworkTask);\nexport var DownloadResumable = function (_FileSystemCancellabl2) {\n  function DownloadResumable(url, _fileUri) {\n    var _this3;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = arguments.length > 3 ? arguments[3] : undefined;\n    var resumeData = arguments.length > 4 ? arguments[4] : undefined;\n    _classCallCheck(this, DownloadResumable);\n    _this3 = _callSuper(this, DownloadResumable);\n    _this3.url = url;\n    _this3._fileUri = _fileUri;\n    _this3.options = options;\n    _this3.callback = callback;\n    _this3.resumeData = resumeData;\n    return _this3;\n  }\n  _inherits(DownloadResumable, _FileSystemCancellabl2);\n  return _createClass(DownloadResumable, [{\n    key: \"fileUri\",\n    get: function get() {\n      return this._fileUri;\n    }\n  }, {\n    key: \"getEventName\",\n    value: function getEventName() {\n      return 'expo-file-system.downloadProgress';\n    }\n  }, {\n    key: \"getCallback\",\n    value: function getCallback() {\n      return this.callback;\n    }\n  }, {\n    key: \"downloadAsync\",\n    value: (function () {\n      var _downloadAsync2 = _asyncToGenerator(function* () {\n        if (!ExponentFileSystem.downloadResumableStartAsync) {\n          throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n        }\n        if (this.isTaskCancelled()) {\n          return;\n        }\n        this.addSubscription();\n        return yield ExponentFileSystem.downloadResumableStartAsync(this.url, this._fileUri, this.uuid, this.options, this.resumeData);\n      });\n      function downloadAsync() {\n        return _downloadAsync2.apply(this, arguments);\n      }\n      return downloadAsync;\n    }())\n  }, {\n    key: \"pauseAsync\",\n    value: (function () {\n      var _pauseAsync = _asyncToGenerator(function* () {\n        if (!ExponentFileSystem.downloadResumablePauseAsync) {\n          throw new UnavailabilityError('expo-file-system', 'downloadResumablePauseAsync');\n        }\n        if (this.isTaskCancelled()) {\n          return {\n            fileUri: this._fileUri,\n            options: this.options,\n            url: this.url\n          };\n        }\n        var pauseResult = yield ExponentFileSystem.downloadResumablePauseAsync(this.uuid);\n        this.removeSubscription();\n        if (pauseResult) {\n          this.resumeData = pauseResult.resumeData;\n          return this.savable();\n        } else {\n          throw new Error('Unable to generate a savable pause state');\n        }\n      });\n      function pauseAsync() {\n        return _pauseAsync.apply(this, arguments);\n      }\n      return pauseAsync;\n    }())\n  }, {\n    key: \"resumeAsync\",\n    value: (function () {\n      var _resumeAsync = _asyncToGenerator(function* () {\n        if (!ExponentFileSystem.downloadResumableStartAsync) {\n          throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n        }\n        if (this.isTaskCancelled()) {\n          return;\n        }\n        this.addSubscription();\n        return yield ExponentFileSystem.downloadResumableStartAsync(this.url, this.fileUri, this.uuid, this.options, this.resumeData);\n      });\n      function resumeAsync() {\n        return _resumeAsync.apply(this, arguments);\n      }\n      return resumeAsync;\n    }())\n  }, {\n    key: \"savable\",\n    value: function savable() {\n      return {\n        url: this.url,\n        fileUri: this.fileUri,\n        options: this.options,\n        resumeData: this.resumeData\n      };\n    }\n  }]);\n}(FileSystemCancellableNetworkTask);\nvar baseReadAsStringAsync = readAsStringAsync;\nvar baseWriteAsStringAsync = writeAsStringAsync;\nvar baseDeleteAsync = deleteAsync;\nvar baseMoveAsync = moveAsync;\nvar baseCopyAsync = copyAsync;\nexport var StorageAccessFramework;\n(function (StorageAccessFramework) {\n  function getUriForDirectoryInRoot(folderName) {\n    return `content://com.android.externalstorage.documents/tree/primary:${folderName}/document/primary:${folderName}`;\n  }\n  StorageAccessFramework.getUriForDirectoryInRoot = getUriForDirectoryInRoot;\n  function requestDirectoryPermissionsAsync() {\n    return _requestDirectoryPermissionsAsync.apply(this, arguments);\n  }\n  function _requestDirectoryPermissionsAsync() {\n    _requestDirectoryPermissionsAsync = _asyncToGenerator(function* () {\n      var initialFileUrl = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      if (!ExponentFileSystem.requestDirectoryPermissionsAsync) {\n        throw new UnavailabilityError('expo-file-system', 'StorageAccessFramework.requestDirectoryPermissionsAsync');\n      }\n      return yield ExponentFileSystem.requestDirectoryPermissionsAsync(initialFileUrl);\n    });\n    return _requestDirectoryPermissionsAsync.apply(this, arguments);\n  }\n  StorageAccessFramework.requestDirectoryPermissionsAsync = requestDirectoryPermissionsAsync;\n  function readDirectoryAsync(_x13) {\n    return _readDirectoryAsync2.apply(this, arguments);\n  }\n  function _readDirectoryAsync2() {\n    _readDirectoryAsync2 = _asyncToGenerator(function* (dirUri) {\n      if (!ExponentFileSystem.readSAFDirectoryAsync) {\n        throw new UnavailabilityError('expo-file-system', 'StorageAccessFramework.readDirectoryAsync');\n      }\n      return yield ExponentFileSystem.readSAFDirectoryAsync(dirUri);\n    });\n    return _readDirectoryAsync2.apply(this, arguments);\n  }\n  StorageAccessFramework.readDirectoryAsync = readDirectoryAsync;\n  function makeDirectoryAsync(_x14, _x15) {\n    return _makeDirectoryAsync2.apply(this, arguments);\n  }\n  function _makeDirectoryAsync2() {\n    _makeDirectoryAsync2 = _asyncToGenerator(function* (parentUri, dirName) {\n      if (!ExponentFileSystem.makeSAFDirectoryAsync) {\n        throw new UnavailabilityError('expo-file-system', 'StorageAccessFramework.makeDirectoryAsync');\n      }\n      return yield ExponentFileSystem.makeSAFDirectoryAsync(parentUri, dirName);\n    });\n    return _makeDirectoryAsync2.apply(this, arguments);\n  }\n  StorageAccessFramework.makeDirectoryAsync = makeDirectoryAsync;\n  function createFileAsync(_x16, _x17, _x18) {\n    return _createFileAsync.apply(this, arguments);\n  }\n  function _createFileAsync() {\n    _createFileAsync = _asyncToGenerator(function* (parentUri, fileName, mimeType) {\n      if (!ExponentFileSystem.createSAFFileAsync) {\n        throw new UnavailabilityError('expo-file-system', 'StorageAccessFramework.createFileAsync');\n      }\n      return yield ExponentFileSystem.createSAFFileAsync(parentUri, fileName, mimeType);\n    });\n    return _createFileAsync.apply(this, arguments);\n  }\n  StorageAccessFramework.createFileAsync = createFileAsync;\n  StorageAccessFramework.writeAsStringAsync = baseWriteAsStringAsync;\n  StorageAccessFramework.readAsStringAsync = baseReadAsStringAsync;\n  StorageAccessFramework.deleteAsync = baseDeleteAsync;\n  StorageAccessFramework.moveAsync = baseMoveAsync;\n  StorageAccessFramework.copyAsync = baseCopyAsync;\n})(StorageAccessFramework || (StorageAccessFramework = {}));", "map": {"version": 3, "names": ["EventEmitter", "UnavailabilityError", "Platform", "v4", "uuidv4", "ExponentFileSystem", "FileSystemSessionType", "FileSystemUploadType", "console", "warn", "_unused", "normalizeEndingSlash", "p", "replace", "documentDirectory", "cacheDirectory", "bundledAssets", "bundleDirectory", "getInfoAsync", "_x", "_getInfoAsync", "apply", "arguments", "_asyncToGenerator", "fileUri", "options", "length", "undefined", "readAsStringAsync", "_x2", "_readAsStringAsync", "getContentUriAsync", "_x3", "_getContentUriAsync", "OS", "writeAsStringAsync", "_x4", "_x5", "_writeAsStringAsync", "contents", "deleteAsync", "_x6", "_deleteAsync", "deleteLegacyDocumentDirectoryAndroid", "_deleteLegacyDocumentDirectoryAndroid", "legacyDocumentDirectory", "idempotent", "moveAsync", "_x7", "_moveAsync", "copyAsync", "_x8", "_copyAsync", "makeDirectoryAsync", "_x9", "_makeDirectoryAsync", "readDirectoryAsync", "_x0", "_readDirectoryAsync", "getFreeDiskStorageAsync", "_getFreeDiskStorageAsync", "getTotalDiskCapacityAsync", "_getTotalDiskCapacityAsync", "downloadAsync", "_x1", "_x10", "_downloadAsync", "uri", "_objectSpread", "sessionType", "BACKGROUND", "uploadAsync", "_x11", "_x12", "_uploadAsync", "url", "uploadType", "BINARY_CONTENT", "httpMethod", "toUpperCase", "createDownloadResumable", "callback", "resumeData", "DownloadResumable", "createUploadTask", "UploadTask", "FileSystemCancellableNetworkTask", "_classCallCheck", "_uuid", "taskWasCanceled", "emitter", "_createClass", "key", "value", "_cancelAsync", "networkTaskCancelAsync", "removeSubscription", "uuid", "cancelAsync", "isTaskCancelled", "get", "addSubscription", "_this", "subscription", "addListener", "getEventName", "event", "get<PERSON>allback", "data", "_FileSystemCancellabl", "_options$httpMethod", "_this2", "_callSuper", "_inherits", "_uploadAsync2", "uploadTaskStartAsync", "result", "_FileSystemCancellabl2", "_fileUri", "_this3", "_downloadAsync2", "downloadResumableStartAsync", "_pauseAsync", "downloadResumablePauseAsync", "pauseResult", "savable", "Error", "pauseAsync", "_resumeAsync", "resumeAsync", "baseReadAsStringAsync", "baseWriteAsStringAsync", "baseDeleteAsync", "baseMoveAsync", "baseCopyAsync", "StorageAccessFramework", "getUriForDirectoryInRoot", "folderName", "requestDirectoryPermissionsAsync", "_requestDirectoryPermissionsAsync", "initialFileUrl", "_x13", "_readDirectoryAsync2", "<PERSON><PERSON><PERSON>", "readSAFDirectoryAsync", "_x14", "_x15", "_makeDirectoryAsync2", "parentUri", "<PERSON><PERSON><PERSON>", "makeSAFDirectoryAsync", "createFileAsync", "_x16", "_x17", "_x18", "_createFileAsync", "fileName", "mimeType", "createSAFFileAsync"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-file-system/src/FileSystem.ts"], "sourcesContent": ["import { EventEmitter, Subscription, UnavailabilityError } from 'expo-modules-core';\nimport { Platform } from 'react-native';\nimport { v4 as uuidv4 } from 'uuid';\n\nimport ExponentFileSystem from './ExponentFileSystem';\nimport {\n  DownloadOptions,\n  DownloadPauseState,\n  FileSystemNetworkTaskProgressCallback,\n  DownloadProgressData,\n  UploadProgressData,\n  FileInfo,\n  FileSystemAcceptedUploadHttpMethod,\n  FileSystemDownloadResult,\n  FileSystemRequestDirectoryPermissionsResult,\n  FileSystemSessionType,\n  FileSystemUploadOptions,\n  FileSystemUploadResult,\n  FileSystemUploadType,\n  ProgressEvent,\n  ReadingOptions,\n  WritingOptions,\n  DeletingOptions,\n  InfoOptions,\n  RelocatingOptions,\n  MakeDirectoryOptions,\n} from './FileSystem.types';\n\nif (!ExponentFileSystem) {\n  console.warn(\n    \"No native ExponentFileSystem module found, are you sure the expo-file-system's module is linked properly?\"\n  );\n}\n// Prevent webpack from pruning this.\nconst _unused = new EventEmitter(ExponentFileSystem); // eslint-disable-line\n\nfunction normalizeEndingSlash(p: string | null): string | null {\n  if (p != null) {\n    return p.replace(/\\/*$/, '') + '/';\n  }\n  return null;\n}\n\n/**\n * `file://` URI pointing to the directory where user documents for this app will be stored.\n * Files stored here will remain until explicitly deleted by the app. Ends with a trailing `/`.\n * Example uses are for files the user saves that they expect to see again.\n */\nexport const documentDirectory = normalizeEndingSlash(ExponentFileSystem.documentDirectory);\n\n/**\n * `file://` URI pointing to the directory where temporary files used by this app will be stored.\n * Files stored here may be automatically deleted by the system when low on storage.\n * Example uses are for downloaded or generated files that the app just needs for one-time usage.\n */\nexport const cacheDirectory = normalizeEndingSlash(ExponentFileSystem.cacheDirectory);\n\n// @docsMissing\nexport const { bundledAssets, bundleDirectory } = ExponentFileSystem;\n\n/**\n * Get metadata information about a file, directory or external content/asset.\n * @param fileUri URI to the file or directory. See [supported URI schemes](#supported-uri-schemes).\n * @param options A map of options represented by [`GetInfoAsyncOptions`](#getinfoasyncoptions) type.\n * @return A Promise that resolves to a `FileInfo` object. If no item exists at this URI,\n * the returned Promise resolves to `FileInfo` object in form of `{ exists: false, isDirectory: false }`.\n */\nexport async function getInfoAsync(fileUri: string, options: InfoOptions = {}): Promise<FileInfo> {\n  if (!ExponentFileSystem.getInfoAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getInfoAsync');\n  }\n  return await ExponentFileSystem.getInfoAsync(fileUri, options);\n}\n\n/**\n * Read the entire contents of a file as a string. Binary will be returned in raw format, you will need to append `data:image/png;base64,` to use it as Base64.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * @param options A map of read options represented by [`ReadingOptions`](#readingoptions) type.\n * @return A Promise that resolves to a string containing the entire contents of the file.\n */\nexport async function readAsStringAsync(\n  fileUri: string,\n  options: ReadingOptions = {}\n): Promise<string> {\n  if (!ExponentFileSystem.readAsStringAsync) {\n    throw new UnavailabilityError('expo-file-system', 'readAsStringAsync');\n  }\n  return await ExponentFileSystem.readAsStringAsync(fileUri, options);\n}\n\n/**\n * Takes a `file://` URI and converts it into content URI (`content://`) so that it can be accessed by other applications outside of Expo.\n * @param fileUri The local URI of the file. If there is no file at this URI, an exception will be thrown.\n * @example\n * ```js\n * FileSystem.getContentUriAsync(uri).then(cUri => {\n *   console.log(cUri);\n *   IntentLauncher.startActivityAsync('android.intent.action.VIEW', {\n *     data: cUri,\n *     flags: 1,\n *   });\n * });\n * ```\n * @return Returns a Promise that resolves to a `string` containing a `content://` URI pointing to the file.\n * The URI is the same as the `fileUri` input parameter but in a different format.\n * @platform android\n */\nexport async function getContentUriAsync(fileUri: string): Promise<string> {\n  if (Platform.OS === 'android') {\n    if (!ExponentFileSystem.getContentUriAsync) {\n      throw new UnavailabilityError('expo-file-system', 'getContentUriAsync');\n    }\n    return await ExponentFileSystem.getContentUriAsync(fileUri);\n  } else {\n    return fileUri;\n  }\n}\n\n/**\n * Write the entire contents of a file as a string.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * > Note: when you're using SAF URI the file needs to exist. You can't create a new file.\n * @param contents The string to replace the contents of the file with.\n * @param options A map of write options represented by [`WritingOptions`](#writingoptions) type.\n */\nexport async function writeAsStringAsync(\n  fileUri: string,\n  contents: string,\n  options: WritingOptions = {}\n): Promise<void> {\n  if (!ExponentFileSystem.writeAsStringAsync) {\n    throw new UnavailabilityError('expo-file-system', 'writeAsStringAsync');\n  }\n  return await ExponentFileSystem.writeAsStringAsync(fileUri, contents, options);\n}\n\n/**\n * Delete a file or directory. If the URI points to a directory, the directory and all its contents are recursively deleted.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * @param options A map of write options represented by [`DeletingOptions`](#deletingoptions) type.\n */\nexport async function deleteAsync(fileUri: string, options: DeletingOptions = {}): Promise<void> {\n  if (!ExponentFileSystem.deleteAsync) {\n    throw new UnavailabilityError('expo-file-system', 'deleteAsync');\n  }\n  return await ExponentFileSystem.deleteAsync(fileUri, options);\n}\n\nexport async function deleteLegacyDocumentDirectoryAndroid(): Promise<void> {\n  if (Platform.OS !== 'android' || documentDirectory == null) {\n    return;\n  }\n  const legacyDocumentDirectory = `${documentDirectory}ExperienceData/`;\n  return await deleteAsync(legacyDocumentDirectory, { idempotent: true });\n}\n\n/**\n * Move a file or directory to a new location.\n * @param options A map of move options represented by [`RelocatingOptions`](#relocatingoptions) type.\n */\nexport async function moveAsync(options: RelocatingOptions): Promise<void> {\n  if (!ExponentFileSystem.moveAsync) {\n    throw new UnavailabilityError('expo-file-system', 'moveAsync');\n  }\n  return await ExponentFileSystem.moveAsync(options);\n}\n\n/**\n * Create a copy of a file or directory. Directories are recursively copied with all of their contents.\n * It can be also used to copy content shared by other apps to local filesystem.\n * @param options A map of move options represented by [`RelocatingOptions`](#relocatingoptions) type.\n */\nexport async function copyAsync(options: RelocatingOptions): Promise<void> {\n  if (!ExponentFileSystem.copyAsync) {\n    throw new UnavailabilityError('expo-file-system', 'copyAsync');\n  }\n  return await ExponentFileSystem.copyAsync(options);\n}\n\n/**\n * Create a new empty directory.\n * @param fileUri `file://` URI to the new directory to create.\n * @param options A map of create directory options represented by [`MakeDirectoryOptions`](#makedirectoryoptions) type.\n */\nexport async function makeDirectoryAsync(\n  fileUri: string,\n  options: MakeDirectoryOptions = {}\n): Promise<void> {\n  if (!ExponentFileSystem.makeDirectoryAsync) {\n    throw new UnavailabilityError('expo-file-system', 'makeDirectoryAsync');\n  }\n  return await ExponentFileSystem.makeDirectoryAsync(fileUri, options);\n}\n\n/**\n * Enumerate the contents of a directory.\n * @param fileUri `file://` URI to the directory.\n * @return A Promise that resolves to an array of strings, each containing the name of a file or directory contained in the directory at `fileUri`.\n */\nexport async function readDirectoryAsync(fileUri: string): Promise<string[]> {\n  if (!ExponentFileSystem.readDirectoryAsync) {\n    throw new UnavailabilityError('expo-file-system', 'readDirectoryAsync');\n  }\n  return await ExponentFileSystem.readDirectoryAsync(fileUri);\n}\n\n/**\n * Gets the available internal disk storage size, in bytes. This returns the free space on the data partition that hosts all of the internal storage for all apps on the device.\n * @return Returns a Promise that resolves to the number of bytes available on the internal disk, or JavaScript's [`MAX_SAFE_INTEGER`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER)\n * if the capacity is greater than 2<sup>53</sup> - 1 bytes.\n */\nexport async function getFreeDiskStorageAsync(): Promise<number> {\n  if (!ExponentFileSystem.getFreeDiskStorageAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getFreeDiskStorageAsync');\n  }\n  return await ExponentFileSystem.getFreeDiskStorageAsync();\n}\n\n/**\n * Gets total internal disk storage size, in bytes. This is the total capacity of the data partition that hosts all the internal storage for all apps on the device.\n * @return Returns a Promise that resolves to a number that specifies the total internal disk storage capacity in bytes, or JavaScript's [`MAX_SAFE_INTEGER`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER)\n * if the capacity is greater than 2<sup>53</sup> - 1 bytes.\n */\nexport async function getTotalDiskCapacityAsync(): Promise<number> {\n  if (!ExponentFileSystem.getTotalDiskCapacityAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getTotalDiskCapacityAsync');\n  }\n  return await ExponentFileSystem.getTotalDiskCapacityAsync();\n}\n\n/**\n * Download the contents at a remote URI to a file in the app's file system. The directory for a local file uri must exist prior to calling this function.\n * @param uri The remote URI to download from.\n * @param fileUri The local URI of the file to download to. If there is no file at this URI, a new one is created.\n * If there is a file at this URI, its contents are replaced. The directory for the file must exist.\n * @param options A map of download options represented by [`DownloadOptions`](#downloadoptions) type.\n * @example\n * ```js\n * FileSystem.downloadAsync(\n *   'http://techslides.com/demos/sample-videos/small.mp4',\n *   FileSystem.documentDirectory + 'small.mp4'\n * )\n *   .then(({ uri }) => {\n *     console.log('Finished downloading to ', uri);\n *   })\n *   .catch(error => {\n *     console.error(error);\n *   });\n * ```\n * @return Returns a Promise that resolves to a `FileSystemDownloadResult` object.\n */\nexport async function downloadAsync(\n  uri: string,\n  fileUri: string,\n  options: DownloadOptions = {}\n): Promise<FileSystemDownloadResult> {\n  if (!ExponentFileSystem.downloadAsync) {\n    throw new UnavailabilityError('expo-file-system', 'downloadAsync');\n  }\n\n  return await ExponentFileSystem.downloadAsync(uri, fileUri, {\n    sessionType: FileSystemSessionType.BACKGROUND,\n    ...options,\n  });\n}\n\n/**\n * Upload the contents of the file pointed by `fileUri` to the remote url.\n * @param url The remote URL, where the file will be sent.\n * @param fileUri The local URI of the file to send. The file must exist.\n * @param options A map of download options represented by [`FileSystemUploadOptions`](#filesystemuploadoptions) type.\n * @example\n * **Client**\n *\n * ```js\n * import * as FileSystem from 'expo-file-system';\n *\n * try {\n *   const response = await FileSystem.uploadAsync(`http://***********:1234/binary-upload`, fileUri, {\n *     fieldName: 'file',\n *     httpMethod: 'PATCH',\n *     uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,\n *   });\n *   console.log(JSON.stringify(response, null, 4));\n * } catch (error) {\n *   console.log(error);\n * }\n * ```\n *\n * **Server**\n *\n * Please refer to the \"[Server: Handling multipart requests](#server-handling-multipart-requests)\" example - there is code for a simple Node.js server.\n * @return Returns a Promise that resolves to `FileSystemUploadResult` object.\n */\nexport async function uploadAsync(\n  url: string,\n  fileUri: string,\n  options: FileSystemUploadOptions = {}\n): Promise<FileSystemUploadResult> {\n  if (!ExponentFileSystem.uploadAsync) {\n    throw new UnavailabilityError('expo-file-system', 'uploadAsync');\n  }\n\n  return await ExponentFileSystem.uploadAsync(url, fileUri, {\n    sessionType: FileSystemSessionType.BACKGROUND,\n    uploadType: FileSystemUploadType.BINARY_CONTENT,\n    ...options,\n    httpMethod: (options.httpMethod || 'POST').toUpperCase(),\n  });\n}\n\n/**\n * Create a `DownloadResumable` object which can start, pause, and resume a download of contents at a remote URI to a file in the app's file system.\n * > Note: You need to call `downloadAsync()`, on a `DownloadResumable` instance to initiate the download.\n * The `DownloadResumable` object has a callback that provides download progress updates.\n * Downloads can be resumed across app restarts by using `AsyncStorage` to store the `DownloadResumable.savable()` object for later retrieval.\n * The `savable` object contains the arguments required to initialize a new `DownloadResumable` object to resume the download after an app restart.\n * The directory for a local file uri must exist prior to calling this function.\n * @param uri The remote URI to download from.\n * @param fileUri The local URI of the file to download to. If there is no file at this URI, a new one is created.\n * If there is a file at this URI, its contents are replaced. The directory for the file must exist.\n * @param options A map of download options represented by [`DownloadOptions`](#downloadoptions) type.\n * @param callback This function is called on each data write to update the download progress.\n * > **Note**: When the app has been moved to the background, this callback won't be fired until it's moved to the foreground.\n * @param resumeData The string which allows the api to resume a paused download. This is set on the `DownloadResumable` object automatically when a download is paused.\n * When initializing a new `DownloadResumable` this should be `null`.\n */\nexport function createDownloadResumable(\n  uri: string,\n  fileUri: string,\n  options?: DownloadOptions,\n  callback?: FileSystemNetworkTaskProgressCallback<DownloadProgressData>,\n  resumeData?: string\n): DownloadResumable {\n  return new DownloadResumable(uri, fileUri, options, callback, resumeData);\n}\n\nexport function createUploadTask(\n  url: string,\n  fileUri: string,\n  options?: FileSystemUploadOptions,\n  callback?: FileSystemNetworkTaskProgressCallback<UploadProgressData>\n): UploadTask {\n  return new UploadTask(url, fileUri, options, callback);\n}\n\nexport abstract class FileSystemCancellableNetworkTask<\n  T extends DownloadProgressData | UploadProgressData\n> {\n  private _uuid = uuidv4();\n  protected taskWasCanceled = false;\n  private emitter = new EventEmitter(ExponentFileSystem);\n  private subscription?: Subscription | null;\n\n  // @docsMissing\n  public async cancelAsync(): Promise<void> {\n    if (!ExponentFileSystem.networkTaskCancelAsync) {\n      throw new UnavailabilityError('expo-file-system', 'networkTaskCancelAsync');\n    }\n\n    this.removeSubscription();\n    this.taskWasCanceled = true;\n    return await ExponentFileSystem.networkTaskCancelAsync(this.uuid);\n  }\n\n  protected isTaskCancelled(): boolean {\n    if (this.taskWasCanceled) {\n      console.warn('This task was already canceled.');\n      return true;\n    }\n\n    return false;\n  }\n\n  protected get uuid(): string {\n    return this._uuid;\n  }\n\n  protected abstract getEventName(): string;\n\n  protected abstract getCallback(): FileSystemNetworkTaskProgressCallback<T> | undefined;\n\n  protected addSubscription() {\n    if (this.subscription) {\n      return;\n    }\n\n    this.subscription = this.emitter.addListener(this.getEventName(), (event: ProgressEvent<T>) => {\n      if (event.uuid === this.uuid) {\n        const callback = this.getCallback();\n        if (callback) {\n          callback(event.data);\n        }\n      }\n    });\n  }\n\n  protected removeSubscription() {\n    if (!this.subscription) {\n      return;\n    }\n    this.emitter.removeSubscription(this.subscription);\n    this.subscription = null;\n  }\n}\n\nexport class UploadTask extends FileSystemCancellableNetworkTask<UploadProgressData> {\n  private options: FileSystemUploadOptions;\n\n  constructor(\n    private url: string,\n    private fileUri: string,\n    options?: FileSystemUploadOptions,\n    private callback?: FileSystemNetworkTaskProgressCallback<UploadProgressData>\n  ) {\n    super();\n\n    const httpMethod = (options?.httpMethod?.toUpperCase() ||\n      'POST') as FileSystemAcceptedUploadHttpMethod;\n\n    this.options = {\n      sessionType: FileSystemSessionType.BACKGROUND,\n      uploadType: FileSystemUploadType.BINARY_CONTENT,\n      ...options,\n      httpMethod,\n    };\n  }\n\n  protected getEventName(): string {\n    return 'expo-file-system.uploadProgress';\n  }\n  protected getCallback(): FileSystemNetworkTaskProgressCallback<UploadProgressData> | undefined {\n    return this.callback;\n  }\n\n  // @docsMissing\n  public async uploadAsync(): Promise<FileSystemUploadResult | undefined> {\n    if (!ExponentFileSystem.uploadTaskStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'uploadTaskStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    const result = await ExponentFileSystem.uploadTaskStartAsync(\n      this.url,\n      this.fileUri,\n      this.uuid,\n      this.options\n    );\n    this.removeSubscription();\n\n    return result;\n  }\n}\n\nexport class DownloadResumable extends FileSystemCancellableNetworkTask<DownloadProgressData> {\n  constructor(\n    private url: string,\n    private _fileUri: string,\n    private options: DownloadOptions = {},\n    private callback?: FileSystemNetworkTaskProgressCallback<DownloadProgressData>,\n    private resumeData?: string\n  ) {\n    super();\n  }\n\n  public get fileUri(): string {\n    return this._fileUri;\n  }\n\n  protected getEventName(): string {\n    return 'expo-file-system.downloadProgress';\n  }\n\n  protected getCallback(): FileSystemNetworkTaskProgressCallback<DownloadProgressData> | undefined {\n    return this.callback;\n  }\n\n  /**\n   * Download the contents at a remote URI to a file in the app's file system.\n   * @return Returns a Promise that resolves to `FileSystemDownloadResult` object, or to `undefined` when task was cancelled.\n   */\n  async downloadAsync(): Promise<FileSystemDownloadResult | undefined> {\n    if (!ExponentFileSystem.downloadResumableStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    return await ExponentFileSystem.downloadResumableStartAsync(\n      this.url,\n      this._fileUri,\n      this.uuid,\n      this.options,\n      this.resumeData\n    );\n  }\n\n  /**\n   * Pause the current download operation. `resumeData` is added to the `DownloadResumable` object after a successful pause operation.\n   * Returns an object that can be saved with `AsyncStorage` for future retrieval (the same object that is returned from calling `FileSystem.DownloadResumable.savable()`).\n   * @return Returns a Promise that resolves to `DownloadPauseState` object.\n   */\n  async pauseAsync(): Promise<DownloadPauseState> {\n    if (!ExponentFileSystem.downloadResumablePauseAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumablePauseAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return {\n        fileUri: this._fileUri,\n        options: this.options,\n        url: this.url,\n      };\n    }\n\n    const pauseResult = await ExponentFileSystem.downloadResumablePauseAsync(this.uuid);\n    this.removeSubscription();\n    if (pauseResult) {\n      this.resumeData = pauseResult.resumeData;\n      return this.savable();\n    } else {\n      throw new Error('Unable to generate a savable pause state');\n    }\n  }\n\n  /**\n   * Resume a paused download operation.\n   * @return Returns a Promise that resolves to `FileSystemDownloadResult` object, or to `undefined` when task was cancelled.\n   */\n  async resumeAsync(): Promise<FileSystemDownloadResult | undefined> {\n    if (!ExponentFileSystem.downloadResumableStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    return await ExponentFileSystem.downloadResumableStartAsync(\n      this.url,\n      this.fileUri,\n      this.uuid,\n      this.options,\n      this.resumeData\n    );\n  }\n\n  /**\n   * Method to get the object which can be saved with `AsyncStorage` for future retrieval.\n   * @returns Returns object in shape of `DownloadPauseState` type.\n   */\n  savable(): DownloadPauseState {\n    return {\n      url: this.url,\n      fileUri: this.fileUri,\n      options: this.options,\n      resumeData: this.resumeData,\n    };\n  }\n}\n\nconst baseReadAsStringAsync = readAsStringAsync;\nconst baseWriteAsStringAsync = writeAsStringAsync;\nconst baseDeleteAsync = deleteAsync;\nconst baseMoveAsync = moveAsync;\nconst baseCopyAsync = copyAsync;\n\n/**\n * The `StorageAccessFramework` is a namespace inside of the `expo-file-system` module, which encapsulates all functions which can be used with [SAF URIs](#saf-uri).\n * You can read more about SAF in the [Android documentation](https://developer.android.com/guide/topics/providers/document-provider).\n *\n * @example\n * # Basic Usage\n *\n * ```ts\n * import { StorageAccessFramework } from 'expo-file-system';\n *\n * // Requests permissions for external directory\n * const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync();\n *\n * if (permissions.granted) {\n *   // Gets SAF URI from response\n *   const uri = permissions.directoryUri;\n *\n *   // Gets all files inside of selected directory\n *   const files = await StorageAccessFramework.readDirectoryAsync(uri);\n *   alert(`Files inside ${uri}:\\n\\n${JSON.stringify(files)}`);\n * }\n * ```\n *\n * # Migrating an album\n *\n * ```ts\n * import * as MediaLibrary from 'expo-media-library';\n * import * as FileSystem from 'expo-file-system';\n * const { StorageAccessFramework } = FileSystem;\n *\n * async function migrateAlbum(albumName: string) {\n *   // Gets SAF URI to the album\n *   const albumUri = StorageAccessFramework.getUriForDirectoryInRoot(albumName);\n *\n *   // Requests permissions\n *   const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync(albumUri);\n *   if (!permissions.granted) {\n *     return;\n *   }\n *\n *   const permittedUri = permissions.directoryUri;\n *   // Checks if users selected the correct folder\n *   if (!permittedUri.includes(albumName)) {\n *     return;\n *   }\n *\n *   const mediaLibraryPermissions = await MediaLibrary.requestPermissionsAsync();\n *   if (!mediaLibraryPermissions.granted) {\n *     return;\n *   }\n *\n *   // Moves files from external storage to internal storage\n *   await StorageAccessFramework.moveAsync({\n *     from: permittedUri,\n *     to: FileSystem.documentDirectory!,\n *   });\n *\n *   const outputDir = FileSystem.documentDirectory! + albumName;\n *   const migratedFiles = await FileSystem.readDirectoryAsync(outputDir);\n *\n *   // Creates assets from local files\n *   const [newAlbumCreator, ...assets] = await Promise.all(\n *     migratedFiles.map<Promise<MediaLibrary.Asset>>(\n *       async fileName => await MediaLibrary.createAssetAsync(outputDir + '/' + fileName)\n *     )\n *   );\n *\n *   // Album was empty\n *   if (!newAlbumCreator) {\n *     return;\n *   }\n *\n *   // Creates a new album in the scoped directory\n *   const newAlbum = await MediaLibrary.createAlbumAsync(albumName, newAlbumCreator, false);\n *   if (assets.length) {\n *     await MediaLibrary.addAssetsToAlbumAsync(assets, newAlbum, false);\n *   }\n * }\n * ```\n * @platform Android\n */\nexport namespace StorageAccessFramework {\n  /**\n   * Gets a [SAF URI](#saf-uri) pointing to a folder in the Android root directory. You can use this function to get URI for\n   * `StorageAccessFramework.requestDirectoryPermissionsAsync()` when you trying to migrate an album. In that case, the name of the album is the folder name.\n   * @param folderName The name of the folder which is located in the Android root directory.\n   * @return Returns a [SAF URI](#saf-uri) to a folder.\n   */\n  export function getUriForDirectoryInRoot(folderName: string) {\n    return `content://com.android.externalstorage.documents/tree/primary:${folderName}/document/primary:${folderName}`;\n  }\n\n  /**\n   * Allows users to select a specific directory, granting your app access to all of the files and sub-directories within that directory.\n   * @param initialFileUrl The [SAF URI](#saf-uri) of the directory that the file picker should display when it first loads.\n   * If URI is incorrect or points to a non-existing folder, it's ignored.\n   * @platform android 11+\n   * @return Returns a Promise that resolves to `FileSystemRequestDirectoryPermissionsResult` object.\n   */\n  export async function requestDirectoryPermissionsAsync(\n    initialFileUrl: string | null = null\n  ): Promise<FileSystemRequestDirectoryPermissionsResult> {\n    if (!ExponentFileSystem.requestDirectoryPermissionsAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.requestDirectoryPermissionsAsync'\n      );\n    }\n\n    return await ExponentFileSystem.requestDirectoryPermissionsAsync(initialFileUrl);\n  }\n\n  /**\n   * Enumerate the contents of a directory.\n   * @param dirUri [SAF](#saf-uri) URI to the directory.\n   * @return A Promise that resolves to an array of strings, each containing the full [SAF URI](#saf-uri) of a file or directory contained in the directory at `fileUri`.\n   */\n  export async function readDirectoryAsync(dirUri: string): Promise<string[]> {\n    if (!ExponentFileSystem.readSAFDirectoryAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.readDirectoryAsync'\n      );\n    }\n    return await ExponentFileSystem.readSAFDirectoryAsync(dirUri);\n  }\n\n  /**\n   * Creates a new empty directory.\n   * @param parentUri The [SAF](#saf-uri) URI to the parent directory.\n   * @param dirName The name of new directory.\n   * @return A Promise that resolves to a [SAF URI](#saf-uri) to the created directory.\n   */\n  export async function makeDirectoryAsync(parentUri: string, dirName: string): Promise<string> {\n    if (!ExponentFileSystem.makeSAFDirectoryAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.makeDirectoryAsync'\n      );\n    }\n    return await ExponentFileSystem.makeSAFDirectoryAsync(parentUri, dirName);\n  }\n\n  /**\n   * Creates a new empty file.\n   * @param parentUri The [SAF](#saf-uri) URI to the parent directory.\n   * @param fileName The name of new file **without the extension**.\n   * @param mimeType The MIME type of new file.\n   * @return A Promise that resolves to a [SAF URI](#saf-uri) to the created file.\n   */\n  export async function createFileAsync(\n    parentUri: string,\n    fileName: string,\n    mimeType: string\n  ): Promise<string> {\n    if (!ExponentFileSystem.createSAFFileAsync) {\n      throw new UnavailabilityError('expo-file-system', 'StorageAccessFramework.createFileAsync');\n    }\n    return await ExponentFileSystem.createSAFFileAsync(parentUri, fileName, mimeType);\n  }\n\n  /**\n   * Alias for [`writeAsStringAsync`](#filesystemwriteasstringasyncfileuri-contents-options) method.\n   */\n  export const writeAsStringAsync = baseWriteAsStringAsync;\n  /**\n   * Alias for [`readAsStringAsync`](#filesystemreadasstringasyncfileuri-options) method.\n   */\n  export const readAsStringAsync = baseReadAsStringAsync;\n  /**\n   * Alias for [`deleteAsync`](#filesystemdeleteasyncfileuri-options) method.\n   */\n  export const deleteAsync = baseDeleteAsync;\n  /**\n   * Alias for [`moveAsync`](#filesystemmoveasyncoptions) method.\n   */\n  export const moveAsync = baseMoveAsync;\n  /**\n   * Alias for [`copyAsync`](#filesystemcopyasyncoptions) method.\n   */\n  export const copyAsync = baseCopyAsync;\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAASA,YAAY,EAAgBC,mBAAmB,QAAQ,mBAAmB;AAAC,OAAAC,QAAA;AAEpF,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAEnC,OAAOC,kBAAkB;AACzB,SAUEC,qBAAqB,EAGrBC,oBAAoB;AAUtB,IAAI,CAACF,kBAAkB,EAAE;EACvBG,OAAO,CAACC,IAAI,CACV,2GAA2G,CAC5G;;AAGH,IAAMC,OAAO,GAAG,IAAIV,YAAY,CAACK,kBAAkB,CAAC;AAEpD,SAASM,oBAAoBA,CAACC,CAAgB;EAC5C,IAAIA,CAAC,IAAI,IAAI,EAAE;IACb,OAAOA,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG;;EAEpC,OAAO,IAAI;AACb;AAOA,OAAO,IAAMC,iBAAiB,GAAGH,oBAAoB,CAACN,kBAAkB,CAACS,iBAAiB,CAAC;AAO3F,OAAO,IAAMC,cAAc,GAAGJ,oBAAoB,CAACN,kBAAkB,CAACU,cAAc,CAAC;AAG9E,IAAQC,aAAa,GAAsBX,kBAAkB,CAArDW,aAAa;EAAEC,eAAe,GAAKZ,kBAAkB,CAAtCY,eAAe;AAAwB,SAAAD,aAAA,EAAAC,eAAA;AASrE,gBAAsBC,YAAYA,CAAAC,EAAA;EAAA,OAAAC,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAKjC,SAAAF,cAAA;EAAAA,aAAA,GAAAG,iBAAA,CALM,WAA4BC,OAAe,EAA2B;IAAA,IAAzBC,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAuB,EAAE;IAC3E,IAAI,CAACjB,kBAAkB,CAACa,YAAY,EAAE;MACpC,MAAM,IAAIjB,mBAAmB,CAAC,kBAAkB,EAAE,cAAc,CAAC;;IAEnE,aAAaI,kBAAkB,CAACa,YAAY,CAACM,OAAO,EAAEC,OAAO,CAAC;EAChE,CAAC;EAAA,OAAAL,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAQD,gBAAsBM,iBAAiBA,CAAAC,GAAA;EAAA,OAAAC,kBAAA,CAAAT,KAAA,OAAAC,SAAA;AAAA;AAQtC,SAAAQ,mBAAA;EAAAA,kBAAA,GAAAP,iBAAA,CARM,WACLC,OAAe,EACa;IAAA,IAA5BC,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAA0B,EAAE;IAE5B,IAAI,CAACjB,kBAAkB,CAACuB,iBAAiB,EAAE;MACzC,MAAM,IAAI3B,mBAAmB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;;IAExE,aAAaI,kBAAkB,CAACuB,iBAAiB,CAACJ,OAAO,EAAEC,OAAO,CAAC;EACrE,CAAC;EAAA,OAAAK,kBAAA,CAAAT,KAAA,OAAAC,SAAA;AAAA;AAmBD,gBAAsBS,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAZ,KAAA,OAAAC,SAAA;AAAA;AASvC,SAAAW,oBAAA;EAAAA,mBAAA,GAAAV,iBAAA,CATM,WAAkCC,OAAe;IACtD,IAAItB,QAAQ,CAACgC,EAAE,KAAK,SAAS,EAAE;MAC7B,IAAI,CAAC7B,kBAAkB,CAAC0B,kBAAkB,EAAE;QAC1C,MAAM,IAAI9B,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;;MAEzE,aAAaI,kBAAkB,CAAC0B,kBAAkB,CAACP,OAAO,CAAC;KAC5D,MAAM;MACL,OAAOA,OAAO;;EAElB,CAAC;EAAA,OAAAS,mBAAA,CAAAZ,KAAA,OAAAC,SAAA;AAAA;AASD,gBAAsBa,kBAAkBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAjB,KAAA,OAAAC,SAAA;AAAA;AASvC,SAAAgB,oBAAA;EAAAA,mBAAA,GAAAf,iBAAA,CATM,WACLC,OAAe,EACfe,QAAgB,EACY;IAAA,IAA5Bd,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAA0B,EAAE;IAE5B,IAAI,CAACjB,kBAAkB,CAAC8B,kBAAkB,EAAE;MAC1C,MAAM,IAAIlC,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;;IAEzE,aAAaI,kBAAkB,CAAC8B,kBAAkB,CAACX,OAAO,EAAEe,QAAQ,EAAEd,OAAO,CAAC;EAChF,CAAC;EAAA,OAAAa,mBAAA,CAAAjB,KAAA,OAAAC,SAAA;AAAA;AAOD,gBAAsBkB,WAAWA,CAAAC,GAAA;EAAA,OAAAC,YAAA,CAAArB,KAAA,OAAAC,SAAA;AAAA;AAKhC,SAAAoB,aAAA;EAAAA,YAAA,GAAAnB,iBAAA,CALM,WAA2BC,OAAe,EAA+B;IAAA,IAA7BC,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAA2B,EAAE;IAC9E,IAAI,CAACjB,kBAAkB,CAACmC,WAAW,EAAE;MACnC,MAAM,IAAIvC,mBAAmB,CAAC,kBAAkB,EAAE,aAAa,CAAC;;IAElE,aAAaI,kBAAkB,CAACmC,WAAW,CAAChB,OAAO,EAAEC,OAAO,CAAC;EAC/D,CAAC;EAAA,OAAAiB,YAAA,CAAArB,KAAA,OAAAC,SAAA;AAAA;AAED,gBAAsBqB,oCAAoCA,CAAA;EAAA,OAAAC,qCAAA,CAAAvB,KAAA,OAAAC,SAAA;AAAA;AAMzD,SAAAsB,sCAAA;EAAAA,qCAAA,GAAArB,iBAAA,CANM,aAAmD;IACxD,IAAIrB,QAAQ,CAACgC,EAAE,KAAK,SAAS,IAAIpB,iBAAiB,IAAI,IAAI,EAAE;MAC1D;;IAEF,IAAM+B,uBAAuB,GAAG,GAAG/B,iBAAiB,iBAAiB;IACrE,aAAa0B,WAAW,CAACK,uBAAuB,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EACzE,CAAC;EAAA,OAAAF,qCAAA,CAAAvB,KAAA,OAAAC,SAAA;AAAA;AAMD,gBAAsByB,SAASA,CAAAC,GAAA;EAAA,OAAAC,UAAA,CAAA5B,KAAA,OAAAC,SAAA;AAAA;AAK9B,SAAA2B,WAAA;EAAAA,UAAA,GAAA1B,iBAAA,CALM,WAAyBE,OAA0B;IACxD,IAAI,CAACpB,kBAAkB,CAAC0C,SAAS,EAAE;MACjC,MAAM,IAAI9C,mBAAmB,CAAC,kBAAkB,EAAE,WAAW,CAAC;;IAEhE,aAAaI,kBAAkB,CAAC0C,SAAS,CAACtB,OAAO,CAAC;EACpD,CAAC;EAAA,OAAAwB,UAAA,CAAA5B,KAAA,OAAAC,SAAA;AAAA;AAOD,gBAAsB4B,SAASA,CAAAC,GAAA;EAAA,OAAAC,UAAA,CAAA/B,KAAA,OAAAC,SAAA;AAAA;AAK9B,SAAA8B,WAAA;EAAAA,UAAA,GAAA7B,iBAAA,CALM,WAAyBE,OAA0B;IACxD,IAAI,CAACpB,kBAAkB,CAAC6C,SAAS,EAAE;MACjC,MAAM,IAAIjD,mBAAmB,CAAC,kBAAkB,EAAE,WAAW,CAAC;;IAEhE,aAAaI,kBAAkB,CAAC6C,SAAS,CAACzB,OAAO,CAAC;EACpD,CAAC;EAAA,OAAA2B,UAAA,CAAA/B,KAAA,OAAAC,SAAA;AAAA;AAOD,gBAAsB+B,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAQvC,SAAAiC,oBAAA;EAAAA,mBAAA,GAAAhC,iBAAA,CARM,WACLC,OAAe,EACmB;IAAA,IAAlCC,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAgC,EAAE;IAElC,IAAI,CAACjB,kBAAkB,CAACgD,kBAAkB,EAAE;MAC1C,MAAM,IAAIpD,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;;IAEzE,aAAaI,kBAAkB,CAACgD,kBAAkB,CAAC7B,OAAO,EAAEC,OAAO,CAAC;EACtE,CAAC;EAAA,OAAA8B,mBAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAOD,gBAAsBkC,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAArC,KAAA,OAAAC,SAAA;AAAA;AAKvC,SAAAoC,oBAAA;EAAAA,mBAAA,GAAAnC,iBAAA,CALM,WAAkCC,OAAe;IACtD,IAAI,CAACnB,kBAAkB,CAACmD,kBAAkB,EAAE;MAC1C,MAAM,IAAIvD,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;;IAEzE,aAAaI,kBAAkB,CAACmD,kBAAkB,CAAChC,OAAO,CAAC;EAC7D,CAAC;EAAA,OAAAkC,mBAAA,CAAArC,KAAA,OAAAC,SAAA;AAAA;AAOD,gBAAsBqC,uBAAuBA,CAAA;EAAA,OAAAC,wBAAA,CAAAvC,KAAA,OAAAC,SAAA;AAAA;AAK5C,SAAAsC,yBAAA;EAAAA,wBAAA,GAAArC,iBAAA,CALM,aAAsC;IAC3C,IAAI,CAAClB,kBAAkB,CAACsD,uBAAuB,EAAE;MAC/C,MAAM,IAAI1D,mBAAmB,CAAC,kBAAkB,EAAE,yBAAyB,CAAC;;IAE9E,aAAaI,kBAAkB,CAACsD,uBAAuB,EAAE;EAC3D,CAAC;EAAA,OAAAC,wBAAA,CAAAvC,KAAA,OAAAC,SAAA;AAAA;AAOD,gBAAsBuC,yBAAyBA,CAAA;EAAA,OAAAC,0BAAA,CAAAzC,KAAA,OAAAC,SAAA;AAAA;AAK9C,SAAAwC,2BAAA;EAAAA,0BAAA,GAAAvC,iBAAA,CALM,aAAwC;IAC7C,IAAI,CAAClB,kBAAkB,CAACwD,yBAAyB,EAAE;MACjD,MAAM,IAAI5D,mBAAmB,CAAC,kBAAkB,EAAE,2BAA2B,CAAC;;IAEhF,aAAaI,kBAAkB,CAACwD,yBAAyB,EAAE;EAC7D,CAAC;EAAA,OAAAC,0BAAA,CAAAzC,KAAA,OAAAC,SAAA;AAAA;AAuBD,gBAAsByC,aAAaA,CAAAC,GAAA,EAAAC,IAAA;EAAA,OAAAC,cAAA,CAAA7C,KAAA,OAAAC,SAAA;AAAA;AAalC,SAAA4C,eAAA;EAAAA,cAAA,GAAA3C,iBAAA,CAbM,WACL4C,GAAW,EACX3C,OAAe,EACc;IAAA,IAA7BC,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAA2B,EAAE;IAE7B,IAAI,CAACjB,kBAAkB,CAAC0D,aAAa,EAAE;MACrC,MAAM,IAAI9D,mBAAmB,CAAC,kBAAkB,EAAE,eAAe,CAAC;;IAGpE,aAAaI,kBAAkB,CAAC0D,aAAa,CAACI,GAAG,EAAE3C,OAAO,EAAA4C,aAAA;MACxDC,WAAW,EAAE/D,qBAAqB,CAACgE;IAAU,GAC1C7C,OAAO,CACX,CAAC;EACJ,CAAC;EAAA,OAAAyC,cAAA,CAAA7C,KAAA,OAAAC,SAAA;AAAA;AA8BD,gBAAsBiD,WAAWA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAArD,KAAA,OAAAC,SAAA;AAAA;AAehC,SAAAoD,aAAA;EAAAA,YAAA,GAAAnD,iBAAA,CAfM,WACLoD,GAAW,EACXnD,OAAe,EACsB;IAAA,IAArCC,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAmC,EAAE;IAErC,IAAI,CAACjB,kBAAkB,CAACkE,WAAW,EAAE;MACnC,MAAM,IAAItE,mBAAmB,CAAC,kBAAkB,EAAE,aAAa,CAAC;;IAGlE,aAAaI,kBAAkB,CAACkE,WAAW,CAACI,GAAG,EAAEnD,OAAO,EAAA4C,aAAA,CAAAA,aAAA;MACtDC,WAAW,EAAE/D,qBAAqB,CAACgE,UAAU;MAC7CM,UAAU,EAAErE,oBAAoB,CAACsE;IAAc,GAC5CpD,OAAO;MACVqD,UAAU,EAAE,CAACrD,OAAO,CAACqD,UAAU,IAAI,MAAM,EAAEC,WAAW;IAAE,EACzD,CAAC;EACJ,CAAC;EAAA,OAAAL,YAAA,CAAArD,KAAA,OAAAC,SAAA;AAAA;AAkBD,OAAM,SAAU0D,uBAAuBA,CACrCb,GAAW,EACX3C,OAAe,EACfC,OAAyB,EACzBwD,QAAsE,EACtEC,UAAmB;EAEnB,OAAO,IAAIC,iBAAiB,CAAChB,GAAG,EAAE3C,OAAO,EAAEC,OAAO,EAAEwD,QAAQ,EAAEC,UAAU,CAAC;AAC3E;AAEA,OAAM,SAAUE,gBAAgBA,CAC9BT,GAAW,EACXnD,OAAe,EACfC,OAAiC,EACjCwD,QAAoE;EAEpE,OAAO,IAAII,UAAU,CAACV,GAAG,EAAEnD,OAAO,EAAEC,OAAO,EAAEwD,QAAQ,CAAC;AACxD;AAEA,WAAsBK,gCAAgC;EAAA,SAAAA,iCAAA;IAAAC,eAAA,OAAAD,gCAAA;IAAA,KAG5CE,KAAK,GAAGpF,MAAM,EAAE;IAAA,KACdqF,eAAe,GAAG,KAAK;IAAA,KACzBC,OAAO,GAAG,IAAI1F,YAAY,CAACK,kBAAkB,CAAC;EAAA;EAAA,OAAAsF,YAAA,CAAAL,gCAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA,IAAAC,YAAA,GAAAvE,iBAAA,CAI/C,aAAiB;QACtB,IAAI,CAAClB,kBAAkB,CAAC0F,sBAAsB,EAAE;UAC9C,MAAM,IAAI9F,mBAAmB,CAAC,kBAAkB,EAAE,wBAAwB,CAAC;;QAG7E,IAAI,CAAC+F,kBAAkB,EAAE;QACzB,IAAI,CAACP,eAAe,GAAG,IAAI;QAC3B,aAAapF,kBAAkB,CAAC0F,sBAAsB,CAAC,IAAI,CAACE,IAAI,CAAC;MACnE,CAAC;MAAA,SARYC,WAAWA,CAAA;QAAA,OAAAJ,YAAA,CAAAzE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX4E,WAAW;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAUd,SAAAM,eAAeA,CAAA;MACvB,IAAI,IAAI,CAACV,eAAe,EAAE;QACxBjF,OAAO,CAACC,IAAI,CAAC,iCAAiC,CAAC;QAC/C,OAAO,IAAI;;MAGb,OAAO,KAAK;IACd;EAAC;IAAAmF,GAAA;IAAAQ,GAAA,EAED,SAAAA,IAAA,EAAkB;MAChB,OAAO,IAAI,CAACZ,KAAK;IACnB;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAMS,SAAAQ,eAAeA,CAAA;MAAA,IAAAC,KAAA;MACvB,IAAI,IAAI,CAACC,YAAY,EAAE;QACrB;;MAGF,IAAI,CAACA,YAAY,GAAG,IAAI,CAACb,OAAO,CAACc,WAAW,CAAC,IAAI,CAACC,YAAY,EAAE,EAAE,UAACC,KAAuB,EAAI;QAC5F,IAAIA,KAAK,CAACT,IAAI,KAAKK,KAAI,CAACL,IAAI,EAAE;UAC5B,IAAMhB,QAAQ,GAAGqB,KAAI,CAACK,WAAW,EAAE;UACnC,IAAI1B,QAAQ,EAAE;YACZA,QAAQ,CAACyB,KAAK,CAACE,IAAI,CAAC;;;MAG1B,CAAC,CAAC;IACJ;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAES,SAAAG,kBAAkBA,CAAA;MAC1B,IAAI,CAAC,IAAI,CAACO,YAAY,EAAE;QACtB;;MAEF,IAAI,CAACb,OAAO,CAACM,kBAAkB,CAAC,IAAI,CAACO,YAAY,CAAC;MAClD,IAAI,CAACA,YAAY,GAAG,IAAI;IAC1B;EAAC;AAAA;AAGH,WAAalB,UAAW,aAAAwB,qBAAA;EAGtB,SAAAxB,WACUV,GAAW,EACXnD,OAAe,EACvBC,OAAiC,EACzBwD,QAAoE;IAAA,IAAA6B,mBAAA;IAAA,IAAAC,MAAA;IAAAxB,eAAA,OAAAF,UAAA;IAE5E0B,MAAA,GAAAC,UAAA,OAAA3B,UAAA;IALQ0B,MAAA,CAAApC,GAAG,GAAHA,GAAG;IACHoC,MAAA,CAAAvF,OAAO,GAAPA,OAAO;IAEPuF,MAAA,CAAA9B,QAAQ,GAARA,QAAQ;IAIhB,IAAMH,UAAU,GAAI,CAAArD,OAAO,qBAAAqF,mBAAA,GAAPrF,OAAO,CAAEqD,UAAU,qBAAnBgC,mBAAA,CAAqB/B,WAAW,EAAE,KACpD,MAA6C;IAE/CgC,MAAA,CAAKtF,OAAO,GAAA2C,aAAA,CAAAA,aAAA;MACVC,WAAW,EAAE/D,qBAAqB,CAACgE,UAAU;MAC7CM,UAAU,EAAErE,oBAAoB,CAACsE;IAAc,GAC5CpD,OAAO;MACVqD,UAAU,EAAVA;IAAU,EACX;IAAC,OAAAiC,MAAA;EACJ;EAACE,SAAA,CAAA5B,UAAA,EAAAwB,qBAAA;EAAA,OAAAlB,YAAA,CAAAN,UAAA;IAAAO,GAAA;IAAAC,KAAA,EAES,SAAAY,YAAYA,CAAA;MACpB,OAAO,iCAAiC;IAC1C;EAAC;IAAAb,GAAA;IAAAC,KAAA,EACS,SAAAc,WAAWA,CAAA;MACnB,OAAO,IAAI,CAAC1B,QAAQ;IACtB;EAAC;IAAAW,GAAA;IAAAC,KAAA;MAAA,IAAAqB,aAAA,GAAA3F,iBAAA,CAGM,aAAiB;QACtB,IAAI,CAAClB,kBAAkB,CAAC8G,oBAAoB,EAAE;UAC5C,MAAM,IAAIlH,mBAAmB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC;;QAG3E,IAAI,IAAI,CAACkG,eAAe,EAAE,EAAE;UAC1B;;QAGF,IAAI,CAACE,eAAe,EAAE;QACtB,IAAMe,MAAM,SAAS/G,kBAAkB,CAAC8G,oBAAoB,CAC1D,IAAI,CAACxC,GAAG,EACR,IAAI,CAACnD,OAAO,EACZ,IAAI,CAACyE,IAAI,EACT,IAAI,CAACxE,OAAO,CACb;QACD,IAAI,CAACuE,kBAAkB,EAAE;QAEzB,OAAOoB,MAAM;MACf,CAAC;MAAA,SAnBY7C,WAAWA,CAAA;QAAA,OAAA2C,aAAA,CAAA7F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXiD,WAAW;IAAA;EAAA;AAAA,EA9BMe,gCAAoD;AAoDpF,WAAaH,iBAAkB,aAAAkC,sBAAA;EAC7B,SAAAlC,kBACUR,GAAW,EACX2C,QAAgB,EAGG;IAAA,IAAAC,MAAA;IAAA,IAFnB9F,OAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAA2B,EAAE;IAAA,IAC7B2D,QAAsE,GAAA3D,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAAK,SAAA;IAAA,IACtEuD,UAAmB,GAAA5D,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAAK,SAAA;IAAA4D,eAAA,OAAAJ,iBAAA;IAE3BoC,MAAA,GAAAP,UAAA,OAAA7B,iBAAA;IANQoC,MAAA,CAAA5C,GAAG,GAAHA,GAAG;IACH4C,MAAA,CAAAD,QAAQ,GAARA,QAAQ;IACRC,MAAA,CAAA9F,OAAO,GAAPA,OAAO;IACP8F,MAAA,CAAAtC,QAAQ,GAARA,QAAQ;IACRsC,MAAA,CAAArC,UAAU,GAAVA,UAAU;IAAS,OAAAqC,MAAA;EAG7B;EAACN,SAAA,CAAA9B,iBAAA,EAAAkC,sBAAA;EAAA,OAAA1B,YAAA,CAAAR,iBAAA;IAAAS,GAAA;IAAAQ,GAAA,EAED,SAAAA,IAAA,EAAkB;MAChB,OAAO,IAAI,CAACkB,QAAQ;IACtB;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EAES,SAAAY,YAAYA,CAAA;MACpB,OAAO,mCAAmC;IAC5C;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAES,SAAAc,WAAWA,CAAA;MACnB,OAAO,IAAI,CAAC1B,QAAQ;IACtB;EAAC;IAAAW,GAAA;IAAAC,KAAA;MAAA,IAAA2B,eAAA,GAAAjG,iBAAA,CAMD,aAAmB;QACjB,IAAI,CAAClB,kBAAkB,CAACoH,2BAA2B,EAAE;UACnD,MAAM,IAAIxH,mBAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC;;QAGlF,IAAI,IAAI,CAACkG,eAAe,EAAE,EAAE;UAC1B;;QAGF,IAAI,CAACE,eAAe,EAAE;QACtB,aAAahG,kBAAkB,CAACoH,2BAA2B,CACzD,IAAI,CAAC9C,GAAG,EACR,IAAI,CAAC2C,QAAQ,EACb,IAAI,CAACrB,IAAI,EACT,IAAI,CAACxE,OAAO,EACZ,IAAI,CAACyD,UAAU,CAChB;MACH,CAAC;MAAA,SAjBKnB,aAAaA,CAAA;QAAA,OAAAyD,eAAA,CAAAnG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbyC,aAAa;IAAA;EAAA;IAAA6B,GAAA;IAAAC,KAAA;MAAA,IAAA6B,WAAA,GAAAnG,iBAAA,CAwBnB,aAAgB;QACd,IAAI,CAAClB,kBAAkB,CAACsH,2BAA2B,EAAE;UACnD,MAAM,IAAI1H,mBAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC;;QAGlF,IAAI,IAAI,CAACkG,eAAe,EAAE,EAAE;UAC1B,OAAO;YACL3E,OAAO,EAAE,IAAI,CAAC8F,QAAQ;YACtB7F,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBkD,GAAG,EAAE,IAAI,CAACA;WACX;;QAGH,IAAMiD,WAAW,SAASvH,kBAAkB,CAACsH,2BAA2B,CAAC,IAAI,CAAC1B,IAAI,CAAC;QACnF,IAAI,CAACD,kBAAkB,EAAE;QACzB,IAAI4B,WAAW,EAAE;UACf,IAAI,CAAC1C,UAAU,GAAG0C,WAAW,CAAC1C,UAAU;UACxC,OAAO,IAAI,CAAC2C,OAAO,EAAE;SACtB,MAAM;UACL,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;;MAE/D,CAAC;MAAA,SArBKC,UAAUA,CAAA;QAAA,OAAAL,WAAA,CAAArG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVyG,UAAU;IAAA;EAAA;IAAAnC,GAAA;IAAAC,KAAA;MAAA,IAAAmC,YAAA,GAAAzG,iBAAA,CA2BhB,aAAiB;QACf,IAAI,CAAClB,kBAAkB,CAACoH,2BAA2B,EAAE;UACnD,MAAM,IAAIxH,mBAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC;;QAGlF,IAAI,IAAI,CAACkG,eAAe,EAAE,EAAE;UAC1B;;QAGF,IAAI,CAACE,eAAe,EAAE;QACtB,aAAahG,kBAAkB,CAACoH,2BAA2B,CACzD,IAAI,CAAC9C,GAAG,EACR,IAAI,CAACnD,OAAO,EACZ,IAAI,CAACyE,IAAI,EACT,IAAI,CAACxE,OAAO,EACZ,IAAI,CAACyD,UAAU,CAChB;MACH,CAAC;MAAA,SAjBK+C,WAAWA,CAAA;QAAA,OAAAD,YAAA,CAAA3G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX2G,WAAW;IAAA;EAAA;IAAArC,GAAA;IAAAC,KAAA,EAuBjB,SAAAgC,OAAOA,CAAA;MACL,OAAO;QACLlD,GAAG,EAAE,IAAI,CAACA,GAAG;QACbnD,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrByD,UAAU,EAAE,IAAI,CAACA;OAClB;IACH;EAAC;AAAA,EA5GoCI,gCAAsD;AA+G7F,IAAM4C,qBAAqB,GAAGtG,iBAAiB;AAC/C,IAAMuG,sBAAsB,GAAGhG,kBAAkB;AACjD,IAAMiG,eAAe,GAAG5F,WAAW;AACnC,IAAM6F,aAAa,GAAGtF,SAAS;AAC/B,IAAMuF,aAAa,GAAGpF,SAAS;AAmF/B,OAAM,IAAWqF,sBAAsB;AAAvC,WAAiBA,sBAAsB;EAOrC,SAAgBC,wBAAwBA,CAACC,UAAkB;IACzD,OAAO,gEAAgEA,UAAU,qBAAqBA,UAAU,EAAE;EACpH;EAFgBF,sBAAA,CAAAC,wBAAwB,GAAAA,wBAEvC;EAAA,SASqBE,gCAAgCA,CAAA;IAAA,OAAAC,iCAAA,CAAAtH,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAAqH,kCAAA;IAAAA,iCAAA,GAAApH,iBAAA,CAA/C,aAC+B;MAAA,IAApCqH,cAAA,GAAAtH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAgC,IAAI;MAEpC,IAAI,CAACjB,kBAAkB,CAACqI,gCAAgC,EAAE;QACxD,MAAM,IAAIzI,mBAAmB,CAC3B,kBAAkB,EAClB,yDAAyD,CAC1D;;MAGH,aAAaI,kBAAkB,CAACqI,gCAAgC,CAACE,cAAc,CAAC;IAClF,CAAC;IAAA,OAAAD,iCAAA,CAAAtH,KAAA,OAAAC,SAAA;EAAA;EAXqBiH,sBAAA,CAAAG,gCAAgC,GAAAA,gCAWrD;EAAA,SAOqBlF,kBAAkBA,CAAAqF,IAAA;IAAA,OAAAC,oBAAA,CAAAzH,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAAwH,qBAAA;IAAAA,oBAAA,GAAAvH,iBAAA,CAAjC,WAAkCwH,MAAc;MACrD,IAAI,CAAC1I,kBAAkB,CAAC2I,qBAAqB,EAAE;QAC7C,MAAM,IAAI/I,mBAAmB,CAC3B,kBAAkB,EAClB,2CAA2C,CAC5C;;MAEH,aAAaI,kBAAkB,CAAC2I,qBAAqB,CAACD,MAAM,CAAC;IAC/D,CAAC;IAAA,OAAAD,oBAAA,CAAAzH,KAAA,OAAAC,SAAA;EAAA;EARqBiH,sBAAA,CAAA/E,kBAAkB,GAAAA,kBAQvC;EAAA,SAQqBH,kBAAkBA,CAAA4F,IAAA,EAAAC,IAAA;IAAA,OAAAC,oBAAA,CAAA9H,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAA6H,qBAAA;IAAAA,oBAAA,GAAA5H,iBAAA,CAAjC,WAAkC6H,SAAiB,EAAEC,OAAe;MACzE,IAAI,CAAChJ,kBAAkB,CAACiJ,qBAAqB,EAAE;QAC7C,MAAM,IAAIrJ,mBAAmB,CAC3B,kBAAkB,EAClB,2CAA2C,CAC5C;;MAEH,aAAaI,kBAAkB,CAACiJ,qBAAqB,CAACF,SAAS,EAAEC,OAAO,CAAC;IAC3E,CAAC;IAAA,OAAAF,oBAAA,CAAA9H,KAAA,OAAAC,SAAA;EAAA;EARqBiH,sBAAA,CAAAlF,kBAAkB,GAAAA,kBAQvC;EAAA,SASqBkG,eAAeA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;IAAA,OAAAC,gBAAA,CAAAtI,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAAqI,iBAAA;IAAAA,gBAAA,GAAApI,iBAAA,CAA9B,WACL6H,SAAiB,EACjBQ,QAAgB,EAChBC,QAAgB;MAEhB,IAAI,CAACxJ,kBAAkB,CAACyJ,kBAAkB,EAAE;QAC1C,MAAM,IAAI7J,mBAAmB,CAAC,kBAAkB,EAAE,wCAAwC,CAAC;;MAE7F,aAAaI,kBAAkB,CAACyJ,kBAAkB,CAACV,SAAS,EAAEQ,QAAQ,EAAEC,QAAQ,CAAC;IACnF,CAAC;IAAA,OAAAF,gBAAA,CAAAtI,KAAA,OAAAC,SAAA;EAAA;EATqBiH,sBAAA,CAAAgB,eAAe,GAAAA,eASpC;EAKYhB,sBAAA,CAAApG,kBAAkB,GAAGgG,sBAAsB;EAI3CI,sBAAA,CAAA3G,iBAAiB,GAAGsG,qBAAqB;EAIzCK,sBAAA,CAAA/F,WAAW,GAAG4F,eAAe;EAI7BG,sBAAA,CAAAxF,SAAS,GAAGsF,aAAa;EAIzBE,sBAAA,CAAArF,SAAS,GAAGoF,aAAa;AACxC,CAAC,EApGgBC,sBAAsB,KAAtBA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}