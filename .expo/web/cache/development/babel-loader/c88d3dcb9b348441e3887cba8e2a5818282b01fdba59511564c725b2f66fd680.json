{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"numeric\", \"children\", \"onPress\", \"sortDirection\", \"textStyle\", \"style\", \"theme\", \"numberOfLines\", \"maxFontSizeMultiplier\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport PixelRatio from \"react-native-web/dist/exports/PixelRatio\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nimport { useInternalTheme } from \"../../core/theming\";\nimport MaterialCommunityIcon from \"../MaterialCommunityIcon\";\nimport Text from \"../Typography/Text\";\nvar DataTableTitle = function DataTableTitle(_ref) {\n  var _theme$colors;\n  var numeric = _ref.numeric,\n    children = _ref.children,\n    onPress = _ref.onPress,\n    sortDirection = _ref.sortDirection,\n    textStyle = _ref.textStyle,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    _ref$numberOfLines = _ref.numberOfLines,\n    numberOfLines = _ref$numberOfLines === void 0 ? 1 : _ref$numberOfLines,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(sortDirection === 'ascending' ? 0 : 1)),\n    spinAnim = _React$useRef.current;\n  React.useEffect(function () {\n    Animated.timing(spinAnim, {\n      toValue: sortDirection === 'ascending' ? 0 : 1,\n      duration: 150,\n      useNativeDriver: true\n    }).start();\n  }, [sortDirection, spinAnim]);\n  var textColor = theme.isV3 ? theme.colors.onSurface : theme === null || theme === void 0 || (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.text;\n  var alphaTextColor = color(textColor).alpha(0.6).rgb().string();\n  var spin = spinAnim.interpolate({\n    inputRange: [0, 1],\n    outputRange: ['0deg', '180deg']\n  });\n  var icon = sortDirection ? React.createElement(Animated.View, {\n    style: [styles.icon, {\n      transform: [{\n        rotate: spin\n      }]\n    }]\n  }, React.createElement(MaterialCommunityIcon, {\n    name: \"arrow-up\",\n    size: 16,\n    color: textColor,\n    direction: I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'\n  })) : null;\n  return React.createElement(Pressable, _extends({\n    disabled: !onPress,\n    onPress: onPress\n  }, rest, {\n    style: [styles.container, numeric && styles.right, style]\n  }), icon, React.createElement(Text, {\n    style: [styles.cell, {\n      maxHeight: 24 * PixelRatio.getFontScale() * numberOfLines\n    }, numberOfLines > 1 ? numeric ? I18nManager.getConstants().isRTL ? styles.leftText : styles.rightText : styles.centerText : {}, sortDirection ? styles.sorted : {\n      color: alphaTextColor\n    }, textStyle],\n    numberOfLines: numberOfLines,\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, children));\n};\nDataTableTitle.displayName = 'DataTable.Title';\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'row',\n    alignContent: 'center',\n    paddingVertical: 12\n  },\n  rightText: {\n    textAlign: 'right'\n  },\n  leftText: {\n    textAlign: 'left'\n  },\n  centerText: {\n    textAlign: 'center'\n  },\n  right: {\n    justifyContent: 'flex-end'\n  },\n  cell: {\n    lineHeight: 24,\n    fontSize: 12,\n    fontWeight: '500',\n    alignItems: 'center'\n  },\n  sorted: {\n    marginLeft: 8\n  },\n  icon: {\n    height: 24,\n    justifyContent: 'center'\n  }\n});\nexport default DataTableTitle;\nexport { DataTableTitle };", "map": {"version": 3, "names": ["React", "Animated", "I18nManager", "PixelRatio", "Pressable", "StyleSheet", "color", "useInternalTheme", "MaterialCommunityIcon", "Text", "DataTableTitle", "_ref", "_theme$colors", "numeric", "children", "onPress", "sortDirection", "textStyle", "style", "themeOverrides", "theme", "_ref$numberOfLines", "numberOfLines", "maxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "_React$useRef", "useRef", "Value", "spinAnim", "current", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "textColor", "isV3", "colors", "onSurface", "text", "alphaTextColor", "alpha", "rgb", "string", "spin", "interpolate", "inputRange", "outputRange", "icon", "createElement", "View", "styles", "transform", "rotate", "name", "size", "direction", "getConstants", "isRTL", "_extends", "disabled", "container", "right", "cell", "maxHeight", "getFontScale", "leftText", "rightText", "centerText", "sorted", "displayName", "create", "flex", "flexDirection", "align<PERSON><PERSON><PERSON>", "paddingVertical", "textAlign", "justifyContent", "lineHeight", "fontSize", "fontWeight", "alignItems", "marginLeft", "height"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/DataTable/DataTableTitle.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  GestureResponderEvent,\n  I18nManager,\n  PixelRatio,\n  Pressable,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport MaterialCommunityIcon from '../MaterialCommunityIcon';\nimport Text from '../Typography/Text';\n\nexport type Props = React.ComponentPropsWithRef<typeof Pressable> & {\n  /**\n   * Text content of the `DataTableTitle`.\n   */\n  children: React.ReactNode;\n  /**\n   * Align the text to the right. Generally monetary or number fields are aligned to right.\n   */\n  numeric?: boolean;\n  /**\n   * Direction of sorting. An arrow indicating the direction is displayed when this is given.\n   */\n  sortDirection?: 'ascending' | 'descending';\n  /**\n   * The number of lines to show.\n   */\n  numberOfLines?: number;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Text content style of the `DataTableTitle`.\n   */\n  textStyle?: StyleProp<TextStyle>;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to display title in table header.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { DataTable } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *       <DataTable>\n *         <DataTable.Header>\n *           <DataTable.Title\n *             sortDirection='descending'\n *           >\n *             Dessert\n *           </DataTable.Title>\n *           <DataTable.Title numeric>Calories</DataTable.Title>\n *           <DataTable.Title numeric>Fat (g)</DataTable.Title>\n *         </DataTable.Header>\n *       </DataTable>\n * );\n *\n * export default MyComponent;\n * ```\n */\n\nconst DataTableTitle = ({\n  numeric,\n  children,\n  onPress,\n  sortDirection,\n  textStyle,\n  style,\n  theme: themeOverrides,\n  numberOfLines = 1,\n  maxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { current: spinAnim } = React.useRef<Animated.Value>(\n    new Animated.Value(sortDirection === 'ascending' ? 0 : 1)\n  );\n\n  React.useEffect(() => {\n    Animated.timing(spinAnim, {\n      toValue: sortDirection === 'ascending' ? 0 : 1,\n      duration: 150,\n      useNativeDriver: true,\n    }).start();\n  }, [sortDirection, spinAnim]);\n\n  const textColor = theme.isV3 ? theme.colors.onSurface : theme?.colors?.text;\n\n  const alphaTextColor = color(textColor).alpha(0.6).rgb().string();\n\n  const spin = spinAnim.interpolate({\n    inputRange: [0, 1],\n    outputRange: ['0deg', '180deg'],\n  });\n\n  const icon = sortDirection ? (\n    <Animated.View style={[styles.icon, { transform: [{ rotate: spin }] }]}>\n      <MaterialCommunityIcon\n        name=\"arrow-up\"\n        size={16}\n        color={textColor}\n        direction={I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'}\n      />\n    </Animated.View>\n  ) : null;\n\n  return (\n    <Pressable\n      disabled={!onPress}\n      onPress={onPress}\n      {...rest}\n      style={[styles.container, numeric && styles.right, style]}\n    >\n      {icon}\n\n      <Text\n        style={[\n          styles.cell,\n          // height must scale with numberOfLines\n          { maxHeight: 24 * PixelRatio.getFontScale() * numberOfLines },\n          // if numberOfLines causes wrap, center is lost. Align directly, sensitive to numeric and RTL\n          numberOfLines > 1\n            ? numeric\n              ? I18nManager.getConstants().isRTL\n                ? styles.leftText\n                : styles.rightText\n              : styles.centerText\n            : {},\n          sortDirection ? styles.sorted : { color: alphaTextColor },\n          textStyle,\n        ]}\n        numberOfLines={numberOfLines}\n        maxFontSizeMultiplier={maxFontSizeMultiplier}\n      >\n        {children}\n      </Text>\n    </Pressable>\n  );\n};\n\nDataTableTitle.displayName = 'DataTable.Title';\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'row',\n    alignContent: 'center',\n    paddingVertical: 12,\n  },\n\n  rightText: {\n    textAlign: 'right',\n  },\n\n  leftText: {\n    textAlign: 'left',\n  },\n\n  centerText: {\n    textAlign: 'center',\n  },\n\n  right: {\n    justifyContent: 'flex-end',\n  },\n\n  cell: {\n    lineHeight: 24,\n    fontSize: 12,\n    fontWeight: '500',\n    alignItems: 'center',\n  },\n\n  sorted: {\n    marginLeft: 8,\n  },\n\n  icon: {\n    height: 24,\n    justifyContent: 'center',\n  },\n});\n\nexport default DataTableTitle;\n\n// @component-docs ignore-next-line\nexport { DataTableTitle };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,UAAA;AAa9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,gBAAgB;AAEzB,OAAOC,qBAAqB;AAC5B,OAAOC,IAAI;AAgEX,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,IAAA,EAWV;EAAA,IAAAC,aAAA;EAAA,IAVXC,OAAO,GAUDF,IAAA,CAVNE,OAAO;IACPC,QAAQ,GASFH,IAAA,CATNG,QAAQ;IACRC,OAAO,GAQDJ,IAAA,CARNI,OAAO;IACPC,aAAa,GAOPL,IAAA,CAPNK,aAAa;IACbC,SAAS,GAMHN,IAAA,CANNM,SAAS;IACTC,KAAK,GAKCP,IAAA,CALNO,KAAK;IACEC,cAAc,GAIfR,IAAA,CAJNS,KAAK;IAAAC,kBAAA,GAICV,IAAA,CAHNW,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,CAAC,GAAAA,kBAAA;IACjBE,qBAAqB,GAEfZ,IAAA,CAFNY,qBAAqB;IAClBC,IAAA,GAAAC,wBAAA,CACGd,IAAA,EAAAe,SAAA;EACN,IAAMN,KAAK,GAAGb,gBAAgB,CAACY,cAAc,CAAC;EAC9C,IAAAQ,aAAA,GAA8B3B,KAAK,CAAC4B,MAAM,CACxC,IAAI3B,QAAQ,CAAC4B,KAAK,CAACb,aAAa,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC,CAC1D,CAAC;IAFgBc,QAAA,GAAAH,aAAA,CAATI,OAAO;EAIf/B,KAAK,CAACgC,SAAS,CAAC,YAAM;IACpB/B,QAAQ,CAACgC,MAAM,CAACH,QAAQ,EAAE;MACxBI,OAAO,EAAElB,aAAa,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC;MAC9CmB,QAAQ,EAAE,GAAG;MACbC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACrB,aAAa,EAAEc,QAAQ,CAAC,CAAC;EAE7B,IAAMQ,SAAS,GAAGlB,KAAK,CAACmB,IAAI,GAAGnB,KAAK,CAACoB,MAAM,CAACC,SAAS,GAAGrB,KAAK,aAALA,KAAK,gBAAAR,aAAA,GAALQ,KAAK,CAAEoB,MAAM,cAAA5B,aAAA,uBAAbA,aAAA,CAAe8B,IAAI;EAE3E,IAAMC,cAAc,GAAGrC,KAAK,CAACgC,SAAS,CAAC,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAEjE,IAAMC,IAAI,GAAGjB,QAAQ,CAACkB,WAAW,CAAC;IAChCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,QAAQ;EAChC,CAAC,CAAC;EAEF,IAAMC,IAAI,GAAGnC,aAAa,GACxBhB,KAAA,CAAAoD,aAAA,CAACnD,QAAQ,CAACoD,IAAI;IAACnC,KAAK,EAAE,CAACoC,MAAM,CAACH,IAAI,EAAE;MAAEI,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAET;MAAK,CAAC;IAAE,CAAC;EAAE,GACrE/C,KAAA,CAAAoD,aAAA,CAAC5C,qBAAqB;IACpBiD,IAAI,EAAC,UAAU;IACfC,IAAI,EAAE,EAAG;IACTpD,KAAK,EAAEgC,SAAU;IACjBqB,SAAS,EAAEzD,WAAW,CAAC0D,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG;EAAM,CAC7D,CACY,CAAC,GACd,IAAI;EAER,OACE7D,KAAA,CAAAoD,aAAA,CAAChD,SAAS,EAAA0D,QAAA;IACRC,QAAQ,EAAE,CAAChD,OAAQ;IACnBA,OAAO,EAAEA;EAAQ,GACbS,IAAI;IACRN,KAAK,EAAE,CAACoC,MAAM,CAACU,SAAS,EAAEnD,OAAO,IAAIyC,MAAM,CAACW,KAAK,EAAE/C,KAAK;EAAE,IAEzDiC,IAAI,EAELnD,KAAA,CAAAoD,aAAA,CAAC3C,IAAI;IACHS,KAAK,EAAE,CACLoC,MAAM,CAACY,IAAI,EAEX;MAAEC,SAAS,EAAE,EAAE,GAAGhE,UAAU,CAACiE,YAAY,CAAC,CAAC,GAAG9C;IAAc,CAAC,EAE7DA,aAAa,GAAG,CAAC,GACbT,OAAO,GACLX,WAAW,CAAC0D,YAAY,CAAC,CAAC,CAACC,KAAK,GAC9BP,MAAM,CAACe,QAAQ,GACff,MAAM,CAACgB,SAAS,GAClBhB,MAAM,CAACiB,UAAU,GACnB,CAAC,CAAC,EACNvD,aAAa,GAAGsC,MAAM,CAACkB,MAAM,GAAG;MAAElE,KAAK,EAAEqC;IAAe,CAAC,EACzD1B,SAAS,CACT;IACFK,aAAa,EAAEA,aAAc;IAC7BC,qBAAqB,EAAEA;EAAsB,GAE5CT,QACG,CACG,CAAC;AAEhB,CAAC;AAEDJ,cAAc,CAAC+D,WAAW,GAAG,iBAAiB;AAE9C,IAAMnB,MAAM,GAAGjD,UAAU,CAACqE,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE,QAAQ;IACtBC,eAAe,EAAE;EACnB,CAAC;EAEDR,SAAS,EAAE;IACTS,SAAS,EAAE;EACb,CAAC;EAEDV,QAAQ,EAAE;IACRU,SAAS,EAAE;EACb,CAAC;EAEDR,UAAU,EAAE;IACVQ,SAAS,EAAE;EACb,CAAC;EAEDd,KAAK,EAAE;IACLe,cAAc,EAAE;EAClB,CAAC;EAEDd,IAAI,EAAE;IACJe,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE;EACd,CAAC;EAEDZ,MAAM,EAAE;IACNa,UAAU,EAAE;EACd,CAAC;EAEDlC,IAAI,EAAE;IACJmC,MAAM,EAAE,EAAE;IACVN,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAetE,cAAc;AAG7B,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}