{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/Zocial.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/Zocial.json\";\nexport default createIconSet(glyphMap, 'zocial', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/src/Zocial.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Zocial.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Zocial.json';\n\nexport default createIconSet(glyphMap, 'zocial', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,QAAQ,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}