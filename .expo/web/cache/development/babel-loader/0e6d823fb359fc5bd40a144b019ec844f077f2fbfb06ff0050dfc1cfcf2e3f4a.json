{"ast": null, "code": "export default Object.freeze({\n  name: 'DevLoadingView',\n  startObserving: function startObserving() {},\n  stopObserving: function stopObserving() {},\n  addListener: function addListener() {},\n  removeListeners: function removeListeners() {}\n});", "map": {"version": 3, "names": ["Object", "freeze", "name", "startObserving", "stopObserving", "addListener", "removeListeners"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo/src/environment/DevLoadingViewNativeModule.ts"], "sourcesContent": ["export default Object.freeze({\n  name: 'DevLoadingView',\n  startObserving() {},\n  stopObserving() {},\n  addListener() {},\n  removeListeners() {},\n});\n"], "mappings": "AAAA,eAAeA,MAAM,CAACC,MAAM,CAAC;EAC3BC,IAAI,EAAE,gBAAgB;EACtBC,cAAc,WAAdA,cAAcA,CAAA,GAAI,CAAC;EACnBC,aAAa,WAAbA,aAAaA,CAAA,GAAI,CAAC;EAClBC,WAAW,WAAXA,WAAWA,CAAA,GAAI,CAAC;EAChBC,eAAe,WAAfA,eAAeA,CAAA,GAAI;CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}