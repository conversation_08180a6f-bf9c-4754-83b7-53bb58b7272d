{"ast": null, "code": "export * from \"./SQLite\";\nexport * from \"./SQLite.types\";", "map": {"version": 3, "names": [], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-sqlite/src/index.ts"], "sourcesContent": ["export * from './SQLite';\nexport * from './SQLite.types';\n"], "mappings": "AAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}