{"ast": null, "code": "import { Platform } from 'expo-modules-core';\nimport { getFilename } from \"./AssetUris\";\nexport function isImageType(type) {\n  return /^(jpeg|jpg|gif|png|bmp|webp|heic)$/i.test(type);\n}\nexport function getImageInfoAsync(url) {\n  if (!Platform.isDOMAvailable) {\n    return Promise.resolve({\n      name: getFilename(url),\n      width: 0,\n      height: 0\n    });\n  }\n  return new Promise(function (resolve, reject) {\n    var img = new Image();\n    img.onerror = reject;\n    img.onload = function () {\n      resolve({\n        name: getFilename(url),\n        width: img.naturalWidth,\n        height: img.naturalHeight\n      });\n    };\n    img.src = url;\n  });\n}", "map": {"version": 3, "names": ["Platform", "getFilename", "isImageType", "type", "test", "getImageInfoAsync", "url", "isDOMAvailable", "Promise", "resolve", "name", "width", "height", "reject", "img", "Image", "onerror", "onload", "naturalWidth", "naturalHeight", "src"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-asset/src/ImageAssets.ts"], "sourcesContent": ["/* eslint-env browser */\nimport { Platform } from 'expo-modules-core';\n\nimport { getFilename } from './AssetUris';\n\ntype ImageInfo = {\n  name: string;\n  width: number;\n  height: number;\n};\n\nexport function isImageType(type: string): boolean {\n  return /^(jpeg|jpg|gif|png|bmp|webp|heic)$/i.test(type);\n}\n\nexport function getImageInfoAsync(url: string): Promise<ImageInfo> {\n  if (!Platform.isDOMAvailable) {\n    return Promise.resolve({ name: getFilename(url), width: 0, height: 0 });\n  }\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onerror = reject;\n    img.onload = () => {\n      resolve({\n        name: getFilename(url),\n        width: img.naturalWidth,\n        height: img.naturalHeight,\n      });\n    };\n    img.src = url;\n  });\n}\n"], "mappings": "AACA,SAASA,QAAQ,QAAQ,mBAAmB;AAE5C,SAASC,WAAW;AAQpB,OAAM,SAAUC,WAAWA,CAACC,IAAY;EACtC,OAAO,qCAAqC,CAACC,IAAI,CAACD,IAAI,CAAC;AACzD;AAEA,OAAM,SAAUE,iBAAiBA,CAACC,GAAW;EAC3C,IAAI,CAACN,QAAQ,CAACO,cAAc,EAAE;IAC5B,OAAOC,OAAO,CAACC,OAAO,CAAC;MAAEC,IAAI,EAAET,WAAW,CAACK,GAAG,CAAC;MAAEK,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAC,CAAE,CAAC;;EAEzE,OAAO,IAAIJ,OAAO,CAAC,UAACC,OAAO,EAAEI,MAAM,EAAI;IACrC,IAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;IACvBD,GAAG,CAACE,OAAO,GAAGH,MAAM;IACpBC,GAAG,CAACG,MAAM,GAAG,YAAK;MAChBR,OAAO,CAAC;QACNC,IAAI,EAAET,WAAW,CAACK,GAAG,CAAC;QACtBK,KAAK,EAAEG,GAAG,CAACI,YAAY;QACvBN,MAAM,EAAEE,GAAG,CAACK;OACb,CAAC;IACJ,CAAC;IACDL,GAAG,CAACM,GAAG,GAAGd,GAAG;EACf,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}