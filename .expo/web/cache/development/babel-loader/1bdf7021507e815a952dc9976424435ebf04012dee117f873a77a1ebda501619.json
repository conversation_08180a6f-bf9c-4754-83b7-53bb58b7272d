{"ast": null, "code": "export var AdornmentType = function (AdornmentType) {\n  AdornmentType[\"Icon\"] = \"icon\";\n  AdornmentType[\"Affix\"] = \"affix\";\n  return AdornmentType;\n}({});\nexport var AdornmentSide = function (AdornmentSide) {\n  AdornmentSide[\"Right\"] = \"right\";\n  AdornmentSide[\"Left\"] = \"left\";\n  return AdornmentSide;\n}({});\nexport var InputMode = function (InputMode) {\n  InputMode[\"Outlined\"] = \"outlined\";\n  InputMode[\"Flat\"] = \"flat\";\n  return InputMode;\n}({});", "map": {"version": 3, "names": ["AdornmentType", "AdornmentSide", "InputMode"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/TextInput/Adornment/enums.tsx"], "sourcesContent": ["export enum AdornmentType {\n  Icon = 'icon',\n  Affix = 'affix',\n}\nexport enum AdornmentSide {\n  Right = 'right',\n  Left = 'left',\n}\nexport enum InputMode {\n  Outlined = 'outlined',\n  Flat = 'flat',\n}\n"], "mappings": "AAAA,WAAYA,aAAa,aAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;AAIzB,WAAYC,aAAa,aAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;AAIzB,WAAYC,SAAS,aAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAAA,OAATA,SAAS;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}