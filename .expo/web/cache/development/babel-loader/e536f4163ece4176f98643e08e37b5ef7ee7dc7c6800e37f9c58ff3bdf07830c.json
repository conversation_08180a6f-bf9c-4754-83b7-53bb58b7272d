{"ast": null, "code": "import RadioButtonComponent from \"./RadioButton\";\nimport RadioButtonAndroid from \"./RadioButtonAndroid\";\nimport RadioButtonGroup from \"./RadioButtonGroup\";\nimport RadioButtonIOS from \"./RadioButtonIOS\";\nimport RadioButtonItem from \"./RadioButtonItem\";\nvar RadioButton = Object.assign(RadioButtonComponent, {\n  Group: RadioButtonGroup,\n  Android: RadioButtonAndroid,\n  IOS: RadioButtonIOS,\n  Item: RadioButtonItem\n});\nexport default RadioButton;", "map": {"version": 3, "names": ["RadioButtonComponent", "RadioButtonAndroid", "RadioButtonGroup", "RadioButtonIOS", "RadioButtonItem", "RadioButton", "Object", "assign", "Group", "Android", "IOS", "<PERSON><PERSON>"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/RadioButton/index.ts"], "sourcesContent": ["import RadioButtonComponent from './RadioButton';\nimport RadioButtonAndroid from './RadioButtonAndroid';\nimport RadioButtonGroup from './RadioButtonGroup';\nimport RadioButtonIOS from './RadioButtonIOS';\nimport RadioButtonItem from './RadioButtonItem';\n\nconst RadioButton = Object.assign(\n  // @component ./RadioButton.tsx\n  RadioButtonComponent,\n  {\n    // @component ./RadioButtonGroup.tsx\n    Group: RadioButtonGroup,\n    // @component ./RadioButtonAndroid.tsx\n    Android: RadioButtonAndroid,\n    // @component ./RadioButtonIOS.tsx\n    IOS: RadioButtonIOS,\n    // @component ./RadioButtonItem.tsx\n    Item: RadioButtonItem,\n  }\n);\n\nexport default RadioButton;\n"], "mappings": "AAAA,OAAOA,oBAAoB;AAC3B,OAAOC,kBAAkB;AACzB,OAAOC,gBAAgB;AACvB,OAAOC,cAAc;AACrB,OAAOC,eAAe;AAEtB,IAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAE/BP,oBAAoB,EACpB;EAEEQ,KAAK,EAAEN,gBAAgB;EAEvBO,OAAO,EAAER,kBAAkB;EAE3BS,GAAG,EAAEP,cAAc;EAEnBQ,IAAI,EAAEP;AACR,CACF,CAAC;AAED,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}