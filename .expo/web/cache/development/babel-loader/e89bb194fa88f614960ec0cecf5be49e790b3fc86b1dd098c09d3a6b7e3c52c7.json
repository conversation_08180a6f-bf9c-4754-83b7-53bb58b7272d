{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nvar _excluded = [\"active\", \"activityState\", \"style\", \"enabled\"];\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Image from \"react-native-web/dist/exports/Image\";\nexport * from \"./types\";\nexport { default as useTransitionProgress } from \"./useTransitionProgress\";\nexport { isSearchBarAvailableForCurrentPlatform, isNewBackTitleImplementation, executeNativeBackPress } from \"./utils\";\nvar ENABLE_SCREENS = true;\nexport function enableScreens() {\n  var shouldEnableScreens = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  ENABLE_SCREENS = shouldEnableScreens;\n}\nexport function screensEnabled() {\n  return ENABLE_SCREENS;\n}\nexport function enableFreeze() {\n  var shouldEnableReactFreeze = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n}\nexport var NativeScreen = function (_React$Component) {\n  function NativeScreen() {\n    _classCallCheck(this, NativeScreen);\n    return _callSuper(this, NativeScreen, arguments);\n  }\n  _inherits(NativeScreen, _React$Component);\n  return _createClass(NativeScreen, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        active = _this$props.active,\n        activityState = _this$props.activityState,\n        style = _this$props.style,\n        _this$props$enabled = _this$props.enabled,\n        enabled = _this$props$enabled === void 0 ? ENABLE_SCREENS : _this$props$enabled,\n        rest = _objectWithoutProperties(_this$props, _excluded);\n      if (enabled) {\n        if (active !== undefined && activityState === undefined) {\n          activityState = active !== 0 ? 2 : 0;\n        }\n        return React.createElement(View, _extends({\n          hidden: activityState === 0,\n          style: [style, {\n            display: activityState !== 0 ? 'flex' : 'none'\n          }]\n        }, rest));\n      }\n      return React.createElement(View, rest);\n    }\n  }]);\n}(React.Component);\nexport var Screen = Animated.createAnimatedComponent(NativeScreen);\nexport var InnerScreen = View;\nexport var ScreenContext = React.createContext(Screen);\nexport var ScreenContainer = View;\nexport var NativeScreenContainer = View;\nexport var NativeScreenNavigationContainer = View;\nexport var ScreenStack = View;\nexport var FullWindowOverlay = View;\nexport var ScreenStackHeaderBackButtonImage = function ScreenStackHeaderBackButtonImage(props) {\n  return React.createElement(View, null, React.createElement(Image, _extends({\n    resizeMode: \"center\",\n    fadeDuration: 0\n  }, props)));\n};\nexport var ScreenStackHeaderRightView = function ScreenStackHeaderRightView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderLeftView = function ScreenStackHeaderLeftView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderCenterView = function ScreenStackHeaderCenterView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderSearchBarView = function ScreenStackHeaderSearchBarView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderConfig = function ScreenStackHeaderConfig(props) {\n  return React.createElement(View, props);\n};\nexport var SearchBar = View;\nexport var ScreenStackHeaderSubview = View;\nexport var shouldUseActivityState = true;", "map": {"version": 3, "names": ["React", "Animated", "View", "Image", "default", "useTransitionProgress", "isSearchBarAvailableForCurrentPlatform", "isNewBackTitleImplementation", "executeNativeBackPress", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "arguments", "length", "undefined", "screensEnabled", "enableFreeze", "shouldEnableReactFreeze", "NativeScreen", "_React$Component", "_classCallCheck", "_callSuper", "_inherits", "_createClass", "key", "value", "render", "_this$props", "props", "active", "activityState", "style", "_this$props$enabled", "enabled", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_extends", "hidden", "display", "Component", "Screen", "createAnimatedComponent", "InnerScreen", "ScreenContext", "createContext", "ScreenContainer", "NativeScreenContainer", "NativeScreenNavigationContainer", "ScreenStack", "FullWindowOverlay", "ScreenStackHeaderBackButtonImage", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "SearchBar", "ScreenStackHeaderSubview", "shouldUseActivityState"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-screens/lib/module/index.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { Animated, View, ViewProps, ImageProps, Image } from 'react-native';\nimport {\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  HeaderSubviewTypes,\n  SearchBarProps,\n} from './types';\n\nexport * from './types';\nexport { default as useTransitionProgress } from './useTransitionProgress';\nexport {\n  isSearchBarAvailableForCurrentPlatform,\n  isNewBackTitleImplementation,\n  executeNativeBackPress,\n} from './utils';\n\nlet ENABLE_SCREENS = true;\n\nexport function enableScreens(shouldEnableScreens = true): void {\n  ENABLE_SCREENS = shouldEnableScreens;\n}\n\nexport function screensEnabled(): boolean {\n  return ENABLE_SCREENS;\n}\n\n// @ts-ignore function stub, freezing logic is located in index.native.tsx\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function enableFreeze(shouldEnableReactFreeze = true): void {\n  // noop\n}\n\nexport class NativeScreen extends React.Component<ScreenProps> {\n  render(): JSX.Element {\n    let {\n      active,\n      activityState,\n      style,\n      enabled = ENABLE_SCREENS,\n      ...rest\n    } = this.props;\n\n    if (enabled) {\n      if (active !== undefined && activityState === undefined) {\n        activityState = active !== 0 ? 2 : 0; // change taken from index.native.tsx\n      }\n      return (\n        <View\n          // @ts-expect-error: hidden exists on web, but not in React Native\n          hidden={activityState === 0}\n          style={[style, { display: activityState !== 0 ? 'flex' : 'none' }]}\n          {...rest}\n        />\n      );\n    }\n\n    return <View {...rest} />;\n  }\n}\n\nexport const Screen = Animated.createAnimatedComponent(NativeScreen);\n\nexport const InnerScreen = View;\n\nexport const ScreenContext = React.createContext(Screen);\n\nexport const ScreenContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const NativeScreenContainer: React.ComponentType<ScreenContainerProps> =\n  View;\n\nexport const NativeScreenNavigationContainer: React.ComponentType<ScreenContainerProps> =\n  View;\n\nexport const ScreenStack: React.ComponentType<ScreenStackProps> = View;\n\nexport const FullWindowOverlay = View as React.ComponentType<{\n  children: ReactNode;\n}>;\n\nexport const ScreenStackHeaderBackButtonImage = (\n  props: ImageProps\n): JSX.Element => (\n  <View>\n    <Image resizeMode=\"center\" fadeDuration={0} {...props} />\n  </View>\n);\n\nexport const ScreenStackHeaderRightView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderLeftView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderCenterView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderSearchBarView = (\n  props: React.PropsWithChildren<Omit<SearchBarProps, 'ref'>>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderConfig = (\n  props: React.PropsWithChildren<ScreenStackHeaderConfigProps>\n): JSX.Element => <View {...props} />;\n\n// @ts-expect-error: search bar props have no common props with View\nexport const SearchBar: React.ComponentType<SearchBarProps> = View;\n\nexport const ScreenStackHeaderSubview: React.ComponentType<\n  React.PropsWithChildren<ViewProps & { type?: HeaderSubviewTypes }>\n> = View;\n\nexport const shouldUseActivityState = true;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAqB,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAWxC;AACA,SAASC,OAAO,IAAIC,qBAAqB;AACzC,SACEC,sCAAsC,EACtCC,4BAA4B,EAC5BC,sBAAsB;AAGxB,IAAIC,cAAc,GAAG,IAAI;AAEzB,OAAO,SAASC,aAAaA,CAAA,EAAmC;EAAA,IAAlCC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACtDH,cAAc,GAAGE,mBAAmB;AACtC;AAEA,OAAO,SAASI,cAAcA,CAAA,EAAY;EACxC,OAAON,cAAc;AACvB;AAIA,OAAO,SAASO,YAAYA,CAAA,EAAuC;EAAA,IAAtCC,uBAAuB,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;AAE3D;AAEA,WAAaM,YAAY,aAAAC,gBAAA;EAAA,SAAAD,aAAA;IAAAE,eAAA,OAAAF,YAAA;IAAA,OAAAG,UAAA,OAAAH,YAAA,EAAAN,SAAA;EAAA;EAAAU,SAAA,CAAAJ,YAAA,EAAAC,gBAAA;EAAA,OAAAI,YAAA,CAAAL,YAAA;IAAAM,GAAA;IAAAC,KAAA,EACvB,SAAAC,MAAMA,CAAA,EAAgB;MACpB,IAAAC,WAAA,GAMI,IAAI,CAACC,KAAK;QALZC,MAAM,GAAAF,WAAA,CAANE,MAAM;QACNC,aAAa,GAAAH,WAAA,CAAbG,aAAa;QACbC,KAAK,GAAAJ,WAAA,CAALI,KAAK;QAAAC,mBAAA,GAAAL,WAAA,CACLM,OAAO;QAAPA,OAAO,GAAAD,mBAAA,cAAGvB,cAAc,GAAAuB,mBAAA;QACrBE,IAAA,GAAAC,wBAAA,CAAAR,WAAA,EAAAS,SAAA;MAGL,IAAIH,OAAO,EAAE;QACX,IAAIJ,MAAM,KAAKf,SAAS,IAAIgB,aAAa,KAAKhB,SAAS,EAAE;UACvDgB,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QACtC;QACA,OACE7B,KAAA,CAAAqC,aAAA,CAACnC,IAAA,EACCoC,QAAA;UACAC,MAAM,EAAET,aAAa,KAAK,CAAE;UAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;YAAES,OAAO,EAAEV,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;UAAO,CAAC;QAAE,GAC/DI,IAAI,CACT,CAAC;MAEN;MAEA,OAAOlC,KAAA,CAAAqC,aAAA,CAACnC,IAAI,EAAKgC,IAAO,CAAC;IAC3B;EAAA;AAAA,EAzBgClC,KAAK,CAACyC,SAAS;AA4BjD,OAAO,IAAMC,MAAM,GAAGzC,QAAQ,CAAC0C,uBAAuB,CAACzB,YAAY,CAAC;AAEpE,OAAO,IAAM0B,WAAW,GAAG1C,IAAI;AAE/B,OAAO,IAAM2C,aAAa,GAAG7C,KAAK,CAAC8C,aAAa,CAACJ,MAAM,CAAC;AAExD,OAAO,IAAMK,eAA0D,GAAG7C,IAAI;AAE9E,OAAO,IAAM8C,qBAAgE,GAC3E9C,IAAI;AAEN,OAAO,IAAM+C,+BAA0E,GACrF/C,IAAI;AAEN,OAAO,IAAMgD,WAAkD,GAAGhD,IAAI;AAEtE,OAAO,IAAMiD,iBAAiB,GAAGjD,IAE/B;AAEF,OAAO,IAAMkD,gCAAgC,GAC3C,SADWA,gCAAgCA,CAC3CxB,KAAiB;EAAA,OAEjB5B,KAAA,CAAAqC,aAAA,CAACnC,IAAI,QACHF,KAAA,CAAAqC,aAAA,CAAClC,KAAK,EAAAmC,QAAA;IAACe,UAAU,EAAC,QAAQ;IAACC,YAAY,EAAE;EAAE,GAAK1B,KAAK,CAAG,CACpD,CACP;AAAA;AAED,OAAO,IAAM2B,0BAA0B,GACrC,SADWA,0BAA0BA,CACrC3B,KAAyC;EAAA,OACzB5B,KAAA,CAAAqC,aAAA,CAACnC,IAAI,EAAK0B,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAM4B,yBAAyB,GACpC,SADWA,yBAAyBA,CACpC5B,KAAyC;EAAA,OACzB5B,KAAA,CAAAqC,aAAA,CAACnC,IAAI,EAAK0B,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAM6B,2BAA2B,GACtC,SADWA,2BAA2BA,CACtC7B,KAAyC;EAAA,OACzB5B,KAAA,CAAAqC,aAAA,CAACnC,IAAI,EAAK0B,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAM8B,8BAA8B,GACzC,SADWA,8BAA8BA,CACzC9B,KAA2D;EAAA,OAC3C5B,KAAA,CAAAqC,aAAA,CAACnC,IAAI,EAAK0B,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAM+B,uBAAuB,GAClC,SADWA,uBAAuBA,CAClC/B,KAA4D;EAAA,OAC5C5B,KAAA,CAAAqC,aAAA,CAACnC,IAAI,EAAK0B,KAAQ,CAAC;AAAA;AAGrC,OAAO,IAAMgC,SAA8C,GAAG1D,IAAI;AAElE,OAAO,IAAM2D,wBAEZ,GAAG3D,IAAI;AAER,OAAO,IAAM4D,sBAAsB,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}