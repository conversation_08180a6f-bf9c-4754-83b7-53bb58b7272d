{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"animationType\", \"children\", \"onDismiss\", \"onRequestClose\", \"onShow\", \"transparent\", \"visible\"];\nimport * as React from 'react';\nimport ModalPortal from \"./ModalPortal\";\nimport ModalAnimation from \"./ModalAnimation\";\nimport ModalContent from \"./ModalContent\";\nimport ModalFocusTrap from \"./ModalFocusTrap\";\nvar uniqueModalIdentifier = 0;\nvar activeModalStack = [];\nvar activeModalListeners = {};\nfunction notifyActiveModalListeners() {\n  if (activeModalStack.length === 0) {\n    return;\n  }\n  var activeModalId = activeModalStack[activeModalStack.length - 1];\n  activeModalStack.forEach(function (modalId) {\n    if (modalId in activeModalListeners) {\n      activeModalListeners[modalId](modalId === activeModalId);\n    }\n  });\n}\nfunction removeActiveModal(modalId) {\n  if (modalId in activeModalListeners) {\n    activeModalListeners[modalId](false);\n    delete activeModalListeners[modalId];\n  }\n  var index = activeModalStack.indexOf(modalId);\n  if (index !== -1) {\n    activeModalStack.splice(index, 1);\n    notifyActiveModalListeners();\n  }\n}\nfunction addActiveModal(modalId, listener) {\n  removeActiveModal(modalId);\n  activeModalStack.push(modalId);\n  activeModalListeners[modalId] = listener;\n  notifyActiveModalListeners();\n}\nvar Modal = React.forwardRef(function (props, forwardedRef) {\n  var animationType = props.animationType,\n    children = props.children,\n    onDismiss = props.onDismiss,\n    onRequestClose = props.onRequestClose,\n    onShow = props.onShow,\n    transparent = props.transparent,\n    _props$visible = props.visible,\n    visible = _props$visible === void 0 ? true : _props$visible,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var modalId = React.useMemo(function () {\n    return uniqueModalIdentifier++;\n  }, []);\n  var _React$useState = React.useState(false),\n    isActive = _React$useState[0],\n    setIsActive = _React$useState[1];\n  var onDismissCallback = React.useCallback(function () {\n    removeActiveModal(modalId);\n    if (onDismiss) {\n      onDismiss();\n    }\n  }, [modalId, onDismiss]);\n  var onShowCallback = React.useCallback(function () {\n    addActiveModal(modalId, setIsActive);\n    if (onShow) {\n      onShow();\n    }\n  }, [modalId, onShow]);\n  React.useEffect(function () {\n    return function () {\n      return removeActiveModal(modalId);\n    };\n  }, [modalId]);\n  return React.createElement(ModalPortal, null, React.createElement(ModalAnimation, {\n    animationType: animationType,\n    onDismiss: onDismissCallback,\n    onShow: onShowCallback,\n    visible: visible\n  }, React.createElement(ModalFocusTrap, {\n    active: isActive\n  }, React.createElement(ModalContent, _extends({}, rest, {\n    active: isActive,\n    onRequestClose: onRequestClose,\n    ref: forwardedRef,\n    transparent: transparent\n  }), children))));\n});\nexport default Modal;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "ModalPortal", "ModalAnimation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModalFocusTrap", "uniqueModalIdentifier", "activeModalStack", "activeModalListeners", "notifyActiveModalListeners", "length", "activeModalId", "for<PERSON>ach", "modalId", "removeActiveModal", "index", "indexOf", "splice", "addActiveModal", "listener", "push", "Modal", "forwardRef", "props", "forwardedRef", "animationType", "children", "on<PERSON><PERSON><PERSON>", "onRequestClose", "onShow", "transparent", "_props$visible", "visible", "rest", "useMemo", "_React$useState", "useState", "isActive", "setIsActive", "onDismissCallback", "useCallback", "onShowCallback", "useEffect", "createElement", "active", "ref"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Modal/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"animationType\", \"children\", \"onDismiss\", \"onRequestClose\", \"onShow\", \"transparent\", \"visible\"];\nimport * as React from 'react';\nimport ModalPortal from './ModalPortal';\nimport ModalAnimation from './ModalAnimation';\nimport ModalContent from './ModalContent';\nimport ModalFocusTrap from './ModalFocusTrap';\nvar uniqueModalIdentifier = 0;\nvar activeModalStack = [];\nvar activeModalListeners = {};\nfunction notifyActiveModalListeners() {\n  if (activeModalStack.length === 0) {\n    return;\n  }\n  var activeModalId = activeModalStack[activeModalStack.length - 1];\n  activeModalStack.forEach(modalId => {\n    if (modalId in activeModalListeners) {\n      activeModalListeners[modalId](modalId === activeModalId);\n    }\n  });\n}\nfunction removeActiveModal(modalId) {\n  if (modalId in activeModalListeners) {\n    // Before removing this listener we should probably tell it\n    // that it's no longer the active modal for sure.\n    activeModalListeners[modalId](false);\n    delete activeModalListeners[modalId];\n  }\n  var index = activeModalStack.indexOf(modalId);\n  if (index !== -1) {\n    activeModalStack.splice(index, 1);\n    notifyActiveModalListeners();\n  }\n}\nfunction addActiveModal(modalId, listener) {\n  removeActiveModal(modalId);\n  activeModalStack.push(modalId);\n  activeModalListeners[modalId] = listener;\n  notifyActiveModalListeners();\n}\nvar Modal = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var animationType = props.animationType,\n    children = props.children,\n    onDismiss = props.onDismiss,\n    onRequestClose = props.onRequestClose,\n    onShow = props.onShow,\n    transparent = props.transparent,\n    _props$visible = props.visible,\n    visible = _props$visible === void 0 ? true : _props$visible,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n\n  // Set a unique model identifier so we can correctly route\n  // dismissals and check the layering of modals.\n  var modalId = React.useMemo(() => uniqueModalIdentifier++, []);\n  var _React$useState = React.useState(false),\n    isActive = _React$useState[0],\n    setIsActive = _React$useState[1];\n  var onDismissCallback = React.useCallback(() => {\n    removeActiveModal(modalId);\n    if (onDismiss) {\n      onDismiss();\n    }\n  }, [modalId, onDismiss]);\n  var onShowCallback = React.useCallback(() => {\n    addActiveModal(modalId, setIsActive);\n    if (onShow) {\n      onShow();\n    }\n  }, [modalId, onShow]);\n  React.useEffect(() => {\n    return () => removeActiveModal(modalId);\n  }, [modalId]);\n  return /*#__PURE__*/React.createElement(ModalPortal, null, /*#__PURE__*/React.createElement(ModalAnimation, {\n    animationType: animationType,\n    onDismiss: onDismissCallback,\n    onShow: onShowCallback,\n    visible: visible\n  }, /*#__PURE__*/React.createElement(ModalFocusTrap, {\n    active: isActive\n  }, /*#__PURE__*/React.createElement(ModalContent, _extends({}, rest, {\n    active: isActive,\n    onRequestClose: onRequestClose,\n    ref: forwardedRef,\n    transparent: transparent\n  }), children))));\n});\nexport default Modal;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,CAAC;AAChH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW;AAClB,OAAOC,cAAc;AACrB,OAAOC,YAAY;AACnB,OAAOC,cAAc;AACrB,IAAIC,qBAAqB,GAAG,CAAC;AAC7B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,oBAAoB,GAAG,CAAC,CAAC;AAC7B,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAIF,gBAAgB,CAACG,MAAM,KAAK,CAAC,EAAE;IACjC;EACF;EACA,IAAIC,aAAa,GAAGJ,gBAAgB,CAACA,gBAAgB,CAACG,MAAM,GAAG,CAAC,CAAC;EACjEH,gBAAgB,CAACK,OAAO,CAAC,UAAAC,OAAO,EAAI;IAClC,IAAIA,OAAO,IAAIL,oBAAoB,EAAE;MACnCA,oBAAoB,CAACK,OAAO,CAAC,CAACA,OAAO,KAAKF,aAAa,CAAC;IAC1D;EACF,CAAC,CAAC;AACJ;AACA,SAASG,iBAAiBA,CAACD,OAAO,EAAE;EAClC,IAAIA,OAAO,IAAIL,oBAAoB,EAAE;IAGnCA,oBAAoB,CAACK,OAAO,CAAC,CAAC,KAAK,CAAC;IACpC,OAAOL,oBAAoB,CAACK,OAAO,CAAC;EACtC;EACA,IAAIE,KAAK,GAAGR,gBAAgB,CAACS,OAAO,CAACH,OAAO,CAAC;EAC7C,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBR,gBAAgB,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IACjCN,0BAA0B,CAAC,CAAC;EAC9B;AACF;AACA,SAASS,cAAcA,CAACL,OAAO,EAAEM,QAAQ,EAAE;EACzCL,iBAAiB,CAACD,OAAO,CAAC;EAC1BN,gBAAgB,CAACa,IAAI,CAACP,OAAO,CAAC;EAC9BL,oBAAoB,CAACK,OAAO,CAAC,GAAGM,QAAQ;EACxCV,0BAA0B,CAAC,CAAC;AAC9B;AACA,IAAIY,KAAK,GAAgBpB,KAAK,CAACqB,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACjE,IAAIC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,cAAc,GAAGR,KAAK,CAACS,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;IAC3DE,IAAI,GAAGlC,6BAA6B,CAACwB,KAAK,EAAEvB,SAAS,CAAC;EAIxD,IAAIa,OAAO,GAAGZ,KAAK,CAACiC,OAAO,CAAC;IAAA,OAAM5B,qBAAqB,EAAE;EAAA,GAAE,EAAE,CAAC;EAC9D,IAAI6B,eAAe,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,KAAK,CAAC;IACzCC,QAAQ,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC7BG,WAAW,GAAGH,eAAe,CAAC,CAAC,CAAC;EAClC,IAAII,iBAAiB,GAAGtC,KAAK,CAACuC,WAAW,CAAC,YAAM;IAC9C1B,iBAAiB,CAACD,OAAO,CAAC;IAC1B,IAAIc,SAAS,EAAE;MACbA,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACd,OAAO,EAAEc,SAAS,CAAC,CAAC;EACxB,IAAIc,cAAc,GAAGxC,KAAK,CAACuC,WAAW,CAAC,YAAM;IAC3CtB,cAAc,CAACL,OAAO,EAAEyB,WAAW,CAAC;IACpC,IAAIT,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAAChB,OAAO,EAAEgB,MAAM,CAAC,CAAC;EACrB5B,KAAK,CAACyC,SAAS,CAAC,YAAM;IACpB,OAAO;MAAA,OAAM5B,iBAAiB,CAACD,OAAO,CAAC;IAAA;EACzC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAoBZ,KAAK,CAAC0C,aAAa,CAACzC,WAAW,EAAE,IAAI,EAAeD,KAAK,CAAC0C,aAAa,CAACxC,cAAc,EAAE;IAC1GsB,aAAa,EAAEA,aAAa;IAC5BE,SAAS,EAAEY,iBAAiB;IAC5BV,MAAM,EAAEY,cAAc;IACtBT,OAAO,EAAEA;EACX,CAAC,EAAe/B,KAAK,CAAC0C,aAAa,CAACtC,cAAc,EAAE;IAClDuC,MAAM,EAAEP;EACV,CAAC,EAAepC,KAAK,CAAC0C,aAAa,CAACvC,YAAY,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEmC,IAAI,EAAE;IACnEW,MAAM,EAAEP,QAAQ;IAChBT,cAAc,EAAEA,cAAc;IAC9BiB,GAAG,EAAErB,YAAY;IACjBM,WAAW,EAAEA;EACf,CAAC,CAAC,EAAEJ,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}