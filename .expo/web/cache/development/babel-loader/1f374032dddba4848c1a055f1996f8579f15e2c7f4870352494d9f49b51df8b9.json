{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport * as React from 'react';\nimport PortalConsumer from \"./PortalConsumer\";\nimport PortalHost, { PortalContext } from \"./PortalHost\";\nimport { Consumer as SettingsConsumer, Provider as SettingsProvider } from \"../../core/settings\";\nimport { ThemeProvider, withInternalTheme } from \"../../core/theming\";\nvar Portal = function (_React$Component) {\n  function Portal() {\n    _classCallCheck(this, Portal);\n    return _callSuper(this, Portal, arguments);\n  }\n  _inherits(Portal, _React$Component);\n  return _createClass(Portal, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        children = _this$props.children,\n        theme = _this$props.theme;\n      return React.createElement(SettingsConsumer, null, function (settings) {\n        return React.createElement(PortalContext.Consumer, null, function (manager) {\n          return React.createElement(PortalConsumer, {\n            manager: manager\n          }, React.createElement(SettingsProvider, {\n            value: settings\n          }, React.createElement(ThemeProvider, {\n            theme: theme\n          }, children)));\n        });\n      });\n    }\n  }]);\n}(React.Component);\nPortal.Host = PortalHost;\nexport default withInternalTheme(Portal);", "map": {"version": 3, "names": ["React", "PortalConsumer", "PortalHost", "PortalContext", "Consumer", "SettingsConsumer", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThemeProvider", "withInternalTheme", "Portal", "_React$Component", "_classCallCheck", "_callSuper", "arguments", "_inherits", "_createClass", "key", "value", "render", "_this$props", "props", "children", "theme", "createElement", "settings", "manager", "Component", "Host"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Portal/Portal.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { InternalTheme } from 'src/types';\n\nimport PortalConsumer from './PortalConsumer';\nimport PortalHost, { PortalContext, PortalMethods } from './PortalHost';\nimport {\n  Consumer as SettingsConsumer,\n  Provider as SettingsProvider,\n} from '../../core/settings';\nimport { ThemeProvider, withInternalTheme } from '../../core/theming';\n\nexport type Props = {\n  /**\n   * Content of the `Portal`.\n   */\n  children: React.ReactNode;\n  /**\n   * @optional\n   */\n  theme: InternalTheme;\n};\n\n/**\n * Portal allows rendering a component at a different place in the parent tree.\n * You can use it to render content which should appear above other elements, similar to `Modal`.\n * It requires a [`Portal.Host`](PortalHost) component to be rendered somewhere in the parent tree.\n * Note that if you're using the `Provider` component, this already includes a `Portal.Host`.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Portal, Text } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Portal>\n *     <Text>This is rendered at a different place</Text>\n *   </Portal>\n * );\n *\n * export default MyComponent;\n * ```\n */\nclass Portal extends React.Component<Props> {\n  // @component ./PortalHost.tsx\n  static Host = PortalHost;\n\n  render() {\n    const { children, theme } = this.props;\n\n    return (\n      <SettingsConsumer>\n        {(settings) => (\n          <PortalContext.Consumer>\n            {(manager) => (\n              <PortalConsumer manager={manager as PortalMethods}>\n                <SettingsProvider value={settings}>\n                  <ThemeProvider theme={theme}>{children}</ThemeProvider>\n                </SettingsProvider>\n              </PortalConsumer>\n            )}\n          </PortalContext.Consumer>\n        )}\n      </SettingsConsumer>\n    );\n  }\n}\n\nexport default withInternalTheme(Portal);\n"], "mappings": ";;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B,OAAOC,cAAc;AACrB,OAAOC,UAAU,IAAIC,aAAa;AAClC,SACEC,QAAQ,IAAIC,gBAAgB,EAC5BC,QAAQ,IAAIC,gBAAgB;AAE9B,SAASC,aAAa,EAAEC,iBAAiB;AAA4B,IAiC/DC,MAAM,aAAAC,gBAAA;EAAA,SAAAD,OAAA;IAAAE,eAAA,OAAAF,MAAA;IAAA,OAAAG,UAAA,OAAAH,MAAA,EAAAI,SAAA;EAAA;EAAAC,SAAA,CAAAL,MAAA,EAAAC,gBAAA;EAAA,OAAAK,YAAA,CAAAN,MAAA;IAAAO,GAAA;IAAAC,KAAA,EAIV,SAAAC,MAAMA,CAAA,EAAG;MACP,IAAAC,WAAA,GAA4B,IAAI,CAACC,KAAK;QAA9BC,QAAQ,GAAAF,WAAA,CAARE,QAAQ;QAAEC,KAAA,GAAAH,WAAA,CAAAG,KAAA;MAElB,OACEvB,KAAA,CAAAwB,aAAA,CAACnB,gBAAgB,QACb,UAAAoB,QAAQ;QAAA,OACRzB,KAAA,CAAAwB,aAAA,CAACrB,aAAa,CAACC,QAAQ,QACnB,UAAAsB,OAAO;UAAA,OACP1B,KAAA,CAAAwB,aAAA,CAACvB,cAAc;YAACyB,OAAO,EAAEA;UAAyB,GAChD1B,KAAA,CAAAwB,aAAA,CAACjB,gBAAgB;YAACW,KAAK,EAAEO;UAAS,GAChCzB,KAAA,CAAAwB,aAAA,CAAChB,aAAa;YAACe,KAAK,EAAEA;UAAM,GAAED,QAAwB,CACtC,CACJ,CAEI;QAAA,EAEV;MAAA,EAAC;IAEvB;EAAA;AAAA,EAtBmBtB,KAAK,CAAC2B,SAAS;AAA9BjB,MAAM,CAEHkB,IAAI,GAAG1B,UAAU;AAuB1B,eAAeO,iBAAiB,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}