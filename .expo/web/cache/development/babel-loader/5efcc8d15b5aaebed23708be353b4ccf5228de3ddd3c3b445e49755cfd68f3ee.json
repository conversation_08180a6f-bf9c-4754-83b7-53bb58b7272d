{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"route\", \"style\", \"children\", \"borderless\", \"centered\", \"rippleColor\"],\n  _excluded2 = [\"key\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { getActiveTintColor, getInactiveTintColor, getLabelColor } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport overlay from \"../../styles/overlay\";\nimport { black, white } from \"../../styles/themes/v2/colors\";\nimport useAnimatedValue from \"../../utils/useAnimatedValue\";\nimport useAnimatedValueArray from \"../../utils/useAnimatedValueArray\";\nimport useIsKeyboardShown from \"../../utils/useIsKeyboardShown\";\nimport useLayout from \"../../utils/useLayout\";\nimport Badge from \"../Badge\";\nimport Icon from \"../Icon\";\nimport Surface from \"../Surface\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar MIN_RIPPLE_SCALE = 0.001;\nvar MIN_TAB_WIDTH = 96;\nvar MAX_TAB_WIDTH = 168;\nvar BAR_HEIGHT = 56;\nvar OUTLINE_WIDTH = 64;\nvar Touchable = function Touchable(_ref) {\n  var _0 = _ref.route,\n    style = _ref.style,\n    children = _ref.children,\n    borderless = _ref.borderless,\n    centered = _ref.centered,\n    rippleColor = _ref.rippleColor,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return TouchableRipple.supported ? React.createElement(TouchableRipple, _extends({}, rest, {\n    disabled: rest.disabled || undefined,\n    borderless: borderless,\n    centered: centered,\n    rippleColor: rippleColor,\n    style: style\n  }), children) : React.createElement(Pressable, _extends({\n    style: style\n  }, rest), children);\n};\nvar BottomNavigationBar = function BottomNavigationBar(_ref2) {\n  var _ref0, _ref1, _ref10;\n  var navigationState = _ref2.navigationState,\n    renderIcon = _ref2.renderIcon,\n    renderLabel = _ref2.renderLabel,\n    _ref2$renderTouchable = _ref2.renderTouchable,\n    renderTouchable = _ref2$renderTouchable === void 0 ? function (_ref3) {\n      var key = _ref3.key,\n        props = _objectWithoutProperties(_ref3, _excluded2);\n      return React.createElement(Touchable, _extends({\n        key: key\n      }, props));\n    } : _ref2$renderTouchable,\n    _ref2$getLabelText = _ref2.getLabelText,\n    getLabelText = _ref2$getLabelText === void 0 ? function (_ref4) {\n      var route = _ref4.route;\n      return route.title;\n    } : _ref2$getLabelText,\n    _ref2$getBadge = _ref2.getBadge,\n    getBadge = _ref2$getBadge === void 0 ? function (_ref5) {\n      var route = _ref5.route;\n      return route.badge;\n    } : _ref2$getBadge,\n    _ref2$getColor = _ref2.getColor,\n    getColor = _ref2$getColor === void 0 ? function (_ref6) {\n      var route = _ref6.route;\n      return route.color;\n    } : _ref2$getColor,\n    _ref2$getAccessibilit = _ref2.getAccessibilityLabel,\n    getAccessibilityLabel = _ref2$getAccessibilit === void 0 ? function (_ref7) {\n      var route = _ref7.route;\n      return route.accessibilityLabel;\n    } : _ref2$getAccessibilit,\n    _ref2$getTestID = _ref2.getTestID,\n    getTestID = _ref2$getTestID === void 0 ? function (_ref8) {\n      var route = _ref8.route;\n      return route.testID;\n    } : _ref2$getTestID,\n    activeColor = _ref2.activeColor,\n    inactiveColor = _ref2.inactiveColor,\n    _ref2$keyboardHidesNa = _ref2.keyboardHidesNavigationBar,\n    keyboardHidesNavigationBar = _ref2$keyboardHidesNa === void 0 ? Platform.OS === 'android' : _ref2$keyboardHidesNa,\n    style = _ref2.style,\n    activeIndicatorStyle = _ref2.activeIndicatorStyle,\n    _ref2$labeled = _ref2.labeled,\n    labeled = _ref2$labeled === void 0 ? true : _ref2$labeled,\n    animationEasing = _ref2.animationEasing,\n    onTabPress = _ref2.onTabPress,\n    onTabLongPress = _ref2.onTabLongPress,\n    shiftingProp = _ref2.shifting,\n    safeAreaInsets = _ref2.safeAreaInsets,\n    _ref2$labelMaxFontSiz = _ref2.labelMaxFontSizeMultiplier,\n    labelMaxFontSizeMultiplier = _ref2$labelMaxFontSiz === void 0 ? 1 : _ref2$labelMaxFontSiz,\n    compactProp = _ref2.compact,\n    _ref2$testID = _ref2.testID,\n    testID = _ref2$testID === void 0 ? 'bottom-navigation-bar' : _ref2$testID,\n    themeOverrides = _ref2.theme;\n  var theme = useInternalTheme(themeOverrides);\n  var _useSafeAreaInsets = useSafeAreaInsets(),\n    bottom = _useSafeAreaInsets.bottom,\n    left = _useSafeAreaInsets.left,\n    right = _useSafeAreaInsets.right;\n  var scale = theme.animation.scale;\n  var compact = compactProp != null ? compactProp : !theme.isV3;\n  var shifting = shiftingProp != null ? shiftingProp : theme.isV3 ? false : navigationState.routes.length > 3;\n  if (shifting && navigationState.routes.length < 2) {\n    shifting = false;\n    console.warn('BottomNavigation.Bar needs at least 2 tabs to run shifting animation');\n  }\n  var visibleAnim = useAnimatedValue(1);\n  var tabsAnims = useAnimatedValueArray(navigationState.routes.map(function (_, i) {\n    return i === navigationState.index ? 1 : 0;\n  }));\n  var indexAnim = useAnimatedValue(navigationState.index);\n  var rippleAnim = useAnimatedValue(MIN_RIPPLE_SCALE);\n  var _useLayout = useLayout(),\n    _useLayout2 = _slicedToArray(_useLayout, 2),\n    layout = _useLayout2[0],\n    onLayout = _useLayout2[1];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    keyboardVisible = _React$useState2[0],\n    setKeyboardVisible = _React$useState2[1];\n  var handleKeyboardShow = React.useCallback(function () {\n    setKeyboardVisible(true);\n    Animated.timing(visibleAnim, {\n      toValue: 0,\n      duration: 150 * scale,\n      useNativeDriver: true\n    }).start();\n  }, [scale, visibleAnim]);\n  var handleKeyboardHide = React.useCallback(function () {\n    Animated.timing(visibleAnim, {\n      toValue: 1,\n      duration: 100 * scale,\n      useNativeDriver: true\n    }).start(function () {\n      setKeyboardVisible(false);\n    });\n  }, [scale, visibleAnim]);\n  var animateToIndex = React.useCallback(function (index) {\n    rippleAnim.setValue(MIN_RIPPLE_SCALE);\n    Animated.parallel([Animated.timing(rippleAnim, {\n      toValue: 1,\n      duration: theme.isV3 || shifting ? 400 * scale : 0,\n      useNativeDriver: true\n    })].concat(_toConsumableArray(navigationState.routes.map(function (_, i) {\n      return Animated.timing(tabsAnims[i], {\n        toValue: i === index ? 1 : 0,\n        duration: theme.isV3 || shifting ? 150 * scale : 0,\n        useNativeDriver: true,\n        easing: animationEasing\n      });\n    })))).start(function () {\n      tabsAnims.map(function (tab, i) {\n        return tab.setValue(i === index ? 1 : 0);\n      });\n      indexAnim.setValue(index);\n      rippleAnim.setValue(MIN_RIPPLE_SCALE);\n    });\n  }, [rippleAnim, theme.isV3, shifting, scale, navigationState.routes, tabsAnims, animationEasing, indexAnim]);\n  React.useEffect(function () {\n    animateToIndex(navigationState.index);\n  }, []);\n  useIsKeyboardShown({\n    onShow: handleKeyboardShow,\n    onHide: handleKeyboardHide\n  });\n  React.useEffect(function () {\n    animateToIndex(navigationState.index);\n  }, [navigationState.index, animateToIndex]);\n  var eventForIndex = function eventForIndex(index) {\n    var event = {\n      route: navigationState.routes[index],\n      defaultPrevented: false,\n      preventDefault: function preventDefault() {\n        event.defaultPrevented = true;\n      }\n    };\n    return event;\n  };\n  var routes = navigationState.routes;\n  var colors = theme.colors,\n    isDarkTheme = theme.dark,\n    mode = theme.mode,\n    isV3 = theme.isV3;\n  var _ref9 = StyleSheet.flatten(style) || {},\n    customBackground = _ref9.backgroundColor,\n    _ref9$elevation = _ref9.elevation,\n    elevation = _ref9$elevation === void 0 ? 4 : _ref9$elevation;\n  var approxBackgroundColor = customBackground ? customBackground : isDarkTheme && mode === 'adaptive' ? overlay(elevation, colors === null || colors === void 0 ? void 0 : colors.surface) : colors === null || colors === void 0 ? void 0 : colors.primary;\n  var v2BackgroundColorInterpolation = shifting ? indexAnim.interpolate({\n    inputRange: routes.map(function (_, i) {\n      return i;\n    }),\n    outputRange: routes.map(function (route) {\n      return getColor({\n        route: route\n      }) || approxBackgroundColor;\n    })\n  }) : approxBackgroundColor;\n  var backgroundColor = isV3 ? customBackground || theme.colors.elevation.level2 : shifting ? v2BackgroundColorInterpolation : approxBackgroundColor;\n  var isDark = typeof approxBackgroundColor === 'string' ? !color(approxBackgroundColor).isLight() : true;\n  var textColor = isDark ? white : black;\n  var activeTintColor = getActiveTintColor({\n    activeColor: activeColor,\n    defaultColor: textColor,\n    theme: theme\n  });\n  var inactiveTintColor = getInactiveTintColor({\n    inactiveColor: inactiveColor,\n    defaultColor: textColor,\n    theme: theme\n  });\n  var touchColor = color(activeTintColor).alpha(0.12).rgb().string();\n  var maxTabWidth = routes.length > 3 ? MIN_TAB_WIDTH : MAX_TAB_WIDTH;\n  var maxTabBarWidth = maxTabWidth * routes.length;\n  var rippleSize = layout.width / 4;\n  var insets = {\n    left: (_ref0 = safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.left) != null ? _ref0 : left,\n    right: (_ref1 = safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.right) != null ? _ref1 : right,\n    bottom: (_ref10 = safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.bottom) != null ? _ref10 : bottom\n  };\n  return React.createElement(Surface, _extends({}, theme.isV3 && {\n    elevation: 0\n  }, {\n    testID: testID,\n    style: [!theme.isV3 && styles.elevation, styles.bar, keyboardHidesNavigationBar ? {\n      transform: [{\n        translateY: visibleAnim.interpolate({\n          inputRange: [0, 1],\n          outputRange: [layout.height, 0]\n        })\n      }],\n      position: keyboardVisible ? 'absolute' : undefined\n    } : null, style],\n    pointerEvents: layout.measured ? keyboardHidesNavigationBar && keyboardVisible ? 'none' : 'auto' : 'none',\n    onLayout: onLayout\n  }), React.createElement(Animated.View, {\n    style: [styles.barContent, {\n      backgroundColor: backgroundColor\n    }],\n    testID: `${testID}-content`\n  }, React.createElement(View, {\n    style: [styles.items, {\n      marginBottom: insets.bottom,\n      marginHorizontal: Math.max(insets.left, insets.right)\n    }, compact && {\n      maxWidth: maxTabBarWidth\n    }],\n    accessibilityRole: 'tablist',\n    testID: `${testID}-content-wrapper`\n  }, shifting && !isV3 ? React.createElement(Animated.View, {\n    pointerEvents: \"none\",\n    style: [styles.ripple, {\n      top: (BAR_HEIGHT - rippleSize) / 2,\n      left: Math.min(layout.width, maxTabBarWidth) / routes.length * (navigationState.index + 0.5) - rippleSize / 2,\n      height: rippleSize,\n      width: rippleSize,\n      borderRadius: rippleSize / 2,\n      backgroundColor: getColor({\n        route: routes[navigationState.index]\n      }),\n      transform: [{\n        scale: rippleAnim.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0, 8]\n        })\n      }],\n      opacity: rippleAnim.interpolate({\n        inputRange: [0, MIN_RIPPLE_SCALE, 0.3, 1],\n        outputRange: [0, 0, 1, 1]\n      })\n    }],\n    testID: `${testID}-content-ripple`\n  }) : null, routes.map(function (route, index) {\n    var focused = navigationState.index === index;\n    var active = tabsAnims[index];\n    var scale = labeled && shifting ? active.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0.5, 1]\n    }) : 1;\n    var translateY = labeled ? shifting ? active.interpolate({\n      inputRange: [0, 1],\n      outputRange: [7, 0]\n    }) : 0 : 7;\n    var activeOpacity = active;\n    var inactiveOpacity = active.interpolate({\n      inputRange: [0, 1],\n      outputRange: [1, 0]\n    });\n    var v3ActiveOpacity = focused ? 1 : 0;\n    var v3InactiveOpacity = shifting ? inactiveOpacity : focused ? 0 : 1;\n    var outlineScale = focused ? active.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0.5, 1]\n    }) : 0;\n    var badge = getBadge({\n      route: route\n    });\n    var activeLabelColor = getLabelColor({\n      tintColor: activeTintColor,\n      hasColor: Boolean(activeColor),\n      focused: focused,\n      defaultColor: textColor,\n      theme: theme\n    });\n    var inactiveLabelColor = getLabelColor({\n      tintColor: inactiveTintColor,\n      hasColor: Boolean(inactiveColor),\n      focused: focused,\n      defaultColor: textColor,\n      theme: theme\n    });\n    var badgeStyle = {\n      top: !isV3 ? -2 : typeof badge === 'boolean' ? 4 : 2,\n      right: (badge != null && typeof badge !== 'boolean' ? String(badge).length * -2 : 0) - (!isV3 ? 2 : 0)\n    };\n    var isLegacyOrV3Shifting = !isV3 || isV3 && shifting && labeled;\n    var font = isV3 ? theme.fonts.labelMedium : {};\n    return renderTouchable({\n      key: route.key,\n      route: route,\n      borderless: true,\n      centered: true,\n      rippleColor: isV3 ? 'transparent' : touchColor,\n      onPress: function onPress() {\n        return onTabPress(eventForIndex(index));\n      },\n      onLongPress: function onLongPress() {\n        return onTabLongPress === null || onTabLongPress === void 0 ? void 0 : onTabLongPress(eventForIndex(index));\n      },\n      testID: getTestID({\n        route: route\n      }),\n      accessibilityLabel: getAccessibilityLabel({\n        route: route\n      }),\n      accessibilityRole: Platform.OS === 'ios' ? 'button' : 'tab',\n      accessibilityState: {\n        selected: focused\n      },\n      style: [styles.item, isV3 && styles.v3Item],\n      children: React.createElement(View, {\n        pointerEvents: \"none\",\n        style: isV3 && (labeled ? styles.v3TouchableContainer : styles.v3NoLabelContainer)\n      }, React.createElement(Animated.View, {\n        style: [styles.iconContainer, isV3 && styles.v3IconContainer, isLegacyOrV3Shifting && {\n          transform: [{\n            translateY: translateY\n          }]\n        }]\n      }, isV3 && focused && React.createElement(Animated.View, {\n        style: [styles.outline, {\n          transform: [{\n            scaleX: outlineScale\n          }],\n          backgroundColor: theme.colors.secondaryContainer\n        }, activeIndicatorStyle]\n      }), React.createElement(Animated.View, {\n        style: [styles.iconWrapper, isV3 && styles.v3IconWrapper, {\n          opacity: isLegacyOrV3Shifting ? activeOpacity : v3ActiveOpacity\n        }]\n      }, renderIcon ? renderIcon({\n        route: route,\n        focused: true,\n        color: activeTintColor\n      }) : React.createElement(Icon, {\n        source: route.focusedIcon,\n        color: activeTintColor,\n        size: 24\n      })), React.createElement(Animated.View, {\n        style: [styles.iconWrapper, isV3 && styles.v3IconWrapper, {\n          opacity: isLegacyOrV3Shifting ? inactiveOpacity : v3InactiveOpacity\n        }]\n      }, renderIcon ? renderIcon({\n        route: route,\n        focused: false,\n        color: inactiveTintColor\n      }) : React.createElement(Icon, {\n        source: theme.isV3 && route.unfocusedIcon !== undefined ? route.unfocusedIcon : route.focusedIcon,\n        color: inactiveTintColor,\n        size: 24\n      })), React.createElement(View, {\n        style: [styles.badgeContainer, badgeStyle]\n      }, typeof badge === 'boolean' ? React.createElement(Badge, {\n        visible: badge,\n        size: isV3 ? 6 : 8\n      }) : React.createElement(Badge, {\n        visible: badge != null,\n        size: 16\n      }, badge))), labeled ? React.createElement(Animated.View, {\n        style: [styles.labelContainer, !isV3 && {\n          transform: [{\n            scale: scale\n          }]\n        }]\n      }, React.createElement(Animated.View, {\n        style: [styles.labelWrapper, {\n          opacity: isLegacyOrV3Shifting ? activeOpacity : v3ActiveOpacity\n        }]\n      }, renderLabel ? renderLabel({\n        route: route,\n        focused: true,\n        color: activeLabelColor\n      }) : React.createElement(Text, {\n        maxFontSizeMultiplier: labelMaxFontSizeMultiplier,\n        variant: \"labelMedium\",\n        style: [styles.label, _objectSpread({\n          color: activeLabelColor\n        }, font)]\n      }, getLabelText({\n        route: route\n      }))), shifting ? null : React.createElement(Animated.View, {\n        style: [styles.labelWrapper, {\n          opacity: isLegacyOrV3Shifting ? inactiveOpacity : v3InactiveOpacity\n        }]\n      }, renderLabel ? renderLabel({\n        route: route,\n        focused: false,\n        color: inactiveLabelColor\n      }) : React.createElement(Text, {\n        maxFontSizeMultiplier: labelMaxFontSizeMultiplier,\n        variant: \"labelMedium\",\n        selectable: false,\n        style: [styles.label, _objectSpread({\n          color: inactiveLabelColor\n        }, font)]\n      }, getLabelText({\n        route: route\n      })))) : !isV3 && React.createElement(View, {\n        style: styles.labelContainer\n      }))\n    });\n  }))));\n};\nBottomNavigationBar.displayName = 'BottomNavigation.Bar';\nexport default BottomNavigationBar;\nvar styles = StyleSheet.create({\n  bar: {\n    left: 0,\n    right: 0,\n    bottom: 0\n  },\n  barContent: {\n    alignItems: 'center',\n    overflow: 'hidden'\n  },\n  items: _objectSpread({\n    flexDirection: 'row'\n  }, Platform.OS === 'web' ? {\n    width: '100%'\n  } : null),\n  item: {\n    flex: 1,\n    paddingVertical: 6\n  },\n  v3Item: {\n    paddingVertical: 0\n  },\n  ripple: {\n    position: 'absolute'\n  },\n  iconContainer: {\n    height: 24,\n    width: 24,\n    marginTop: 2,\n    marginHorizontal: 12,\n    alignSelf: 'center'\n  },\n  v3IconContainer: {\n    height: 32,\n    width: 32,\n    marginBottom: 4,\n    marginTop: 0,\n    justifyContent: 'center'\n  },\n  iconWrapper: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    alignItems: 'center'\n  }),\n  v3IconWrapper: {\n    top: 4\n  },\n  labelContainer: {\n    height: 16,\n    paddingBottom: 2\n  },\n  labelWrapper: _objectSpread({}, StyleSheet.absoluteFillObject),\n  label: _objectSpread({\n    fontSize: 12,\n    height: BAR_HEIGHT,\n    textAlign: 'center',\n    backgroundColor: 'transparent'\n  }, Platform.OS === 'web' ? {\n    whiteSpace: 'nowrap',\n    alignSelf: 'center'\n  } : null),\n  badgeContainer: {\n    position: 'absolute',\n    left: 0\n  },\n  v3TouchableContainer: {\n    paddingTop: 12,\n    paddingBottom: 16\n  },\n  v3NoLabelContainer: {\n    height: 80,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  outline: {\n    width: OUTLINE_WIDTH,\n    height: OUTLINE_WIDTH / 2,\n    borderRadius: OUTLINE_WIDTH / 4,\n    alignSelf: 'center'\n  },\n  elevation: {\n    elevation: 4\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "Pressable", "View", "color", "useSafeAreaInsets", "getActiveTintColor", "getInactiveTintColor", "getLabelColor", "useInternalTheme", "overlay", "black", "white", "useAnimatedValue", "useAnimatedValueArray", "useIsKeyboardShown", "useLayout", "Badge", "Icon", "Surface", "TouchableRipple", "Text", "MIN_RIPPLE_SCALE", "MIN_TAB_WIDTH", "MAX_TAB_WIDTH", "BAR_HEIGHT", "OUTLINE_WIDTH", "Touchable", "_ref", "_0", "route", "style", "children", "borderless", "centered", "rippleColor", "rest", "_objectWithoutProperties", "_excluded", "supported", "createElement", "_extends", "disabled", "undefined", "BottomNavigationBar", "_ref2", "_ref0", "_ref1", "_ref10", "navigationState", "renderIcon", "renderLabel", "_ref2$renderTouchable", "renderTouchable", "_ref3", "key", "props", "_excluded2", "_ref2$getLabelText", "getLabelText", "_ref4", "title", "_ref2$getBadge", "getBadge", "_ref5", "badge", "_ref2$getColor", "getColor", "_ref6", "_ref2$getAccessibilit", "getAccessibilityLabel", "_ref7", "accessibilityLabel", "_ref2$getTestID", "getTestID", "_ref8", "testID", "activeColor", "inactiveColor", "_ref2$keyboardHidesNa", "keyboardHidesNavigationBar", "OS", "activeIndicatorStyle", "_ref2$labeled", "labeled", "animationEasing", "onTabPress", "onTabLongPress", "shiftingProp", "shifting", "safeAreaInsets", "_ref2$labelMaxFontSiz", "labelMaxFontSizeMultiplier", "compactProp", "compact", "_ref2$testID", "themeOverrides", "theme", "_useSafeAreaInsets", "bottom", "left", "right", "scale", "animation", "isV3", "routes", "length", "console", "warn", "visibleAnim", "tabsAnims", "map", "_", "i", "index", "indexAnim", "rippleAnim", "_useLayout", "_useLayout2", "_slicedToArray", "layout", "onLayout", "_React$useState", "useState", "_React$useState2", "keyboardVisible", "setKeyboardVisible", "handleKeyboardShow", "useCallback", "timing", "toValue", "duration", "useNativeDriver", "start", "handleKeyboardHide", "animateToIndex", "setValue", "parallel", "concat", "_toConsumableArray", "easing", "tab", "useEffect", "onShow", "onHide", "eventForIndex", "event", "defaultPrevented", "preventDefault", "colors", "isDarkTheme", "dark", "mode", "_ref9", "flatten", "customBackground", "backgroundColor", "_ref9$elevation", "elevation", "approxBackgroundColor", "surface", "primary", "v2BackgroundColorInterpolation", "interpolate", "inputRange", "outputRange", "level2", "isDark", "isLight", "textColor", "activeTintColor", "defaultColor", "inactiveTintColor", "touchColor", "alpha", "rgb", "string", "max<PERSON>ab<PERSON><PERSON><PERSON>", "max<PERSON>ab<PERSON><PERSON><PERSON><PERSON><PERSON>", "rippleSize", "width", "insets", "styles", "bar", "transform", "translateY", "height", "position", "pointerEvents", "measured", "<PERSON><PERSON><PERSON><PERSON>", "items", "marginBottom", "marginHorizontal", "Math", "max", "max<PERSON><PERSON><PERSON>", "accessibilityRole", "ripple", "top", "min", "borderRadius", "opacity", "focused", "active", "activeOpacity", "inactiveOpacity", "v3ActiveOpacity", "v3InactiveOpacity", "outlineScale", "activeLabelColor", "tintColor", "hasColor", "Boolean", "inactiveLabelColor", "badgeStyle", "String", "isLegacyOrV3Shifting", "font", "fonts", "labelMedium", "onPress", "onLongPress", "accessibilityState", "selected", "item", "v3Item", "v3TouchableContainer", "v3NoLabelContainer", "iconContainer", "v3IconContainer", "outline", "scaleX", "secondaryContainer", "iconWrapper", "v3IconWrapper", "source", "focusedIcon", "size", "unfocusedIcon", "badgeContainer", "visible", "labelContainer", "labelWrapper", "maxFontSizeMultiplier", "variant", "label", "_objectSpread", "selectable", "displayName", "create", "alignItems", "overflow", "flexDirection", "flex", "paddingVertical", "marginTop", "alignSelf", "justifyContent", "absoluteFillObject", "paddingBottom", "fontSize", "textAlign", "whiteSpace", "paddingTop"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/BottomNavigation/BottomNavigationBar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  ColorValue,\n  EasingFunction,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  Pressable,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\n\nimport {\n  getActiveTintColor,\n  getInactiveTintColor,\n  getLabelColor,\n} from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport overlay from '../../styles/overlay';\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { ThemeProp } from '../../types';\nimport useAnimatedValue from '../../utils/useAnimatedValue';\nimport useAnimatedValueArray from '../../utils/useAnimatedValueArray';\nimport useIsKeyboardShown from '../../utils/useIsKeyboardShown';\nimport useLayout from '../../utils/useLayout';\nimport Badge from '../Badge';\nimport Icon, { IconSource } from '../Icon';\nimport Surface from '../Surface';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport { Props as TouchableRippleProps } from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\ntype BaseRoute = {\n  key: string;\n  title?: string;\n  focusedIcon?: IconSource;\n  unfocusedIcon?: IconSource;\n  badge?: string | number | boolean;\n  /**\n   * @deprecated In v5.x works only with theme version 2.\n   */\n  color?: string;\n  accessibilityLabel?: string;\n  testID?: string;\n  lazy?: boolean;\n};\n\ntype NavigationState<Route extends BaseRoute> = {\n  index: number;\n  routes: Route[];\n};\n\ntype TabPressEvent = {\n  defaultPrevented: boolean;\n  preventDefault(): void;\n};\n\ntype TouchableProps<Route extends BaseRoute> = TouchableRippleProps & {\n  key: string;\n  route: Route;\n  children: React.ReactNode;\n  borderless?: boolean;\n  centered?: boolean;\n  rippleColor?: ColorValue;\n};\n\nexport type Props<Route extends BaseRoute> = {\n  /**\n   * Whether the shifting style is used, the active tab icon shifts up to show the label and the inactive tabs won't have a label.\n   *\n   * By default, this is `false` with theme version 3 and `true` when you have more than 3 tabs.\n   * Pass `shifting={false}` to explicitly disable this animation, or `shifting={true}` to always use this animation.\n   * Note that you need at least 2 tabs be able to run this animation.\n   */\n  shifting?: boolean;\n  /**\n   * Whether to show labels in tabs. When `false`, only icons will be displayed.\n   */\n  labeled?: boolean;\n  /**\n   * Whether tabs should be spread across the entire width.\n   */\n  compact?: boolean;\n  /**\n   * State for the bottom navigation. The state should contain the following properties:\n   *\n   * - `index`: a number representing the index of the active route in the `routes` array\n   * - `routes`: an array containing a list of route objects used for rendering the tabs\n   *\n   * Each route object should contain the following properties:\n   *\n   * - `key`: a unique key to identify the route (required)\n   * - `title`: title of the route to use as the tab label\n   * - `focusedIcon`:  icon to use as the focused tab icon, can be a string, an image source or a react component @renamed Renamed from 'icon' to 'focusedIcon' in v5.x\n   * - `unfocusedIcon`:  icon to use as the unfocused tab icon, can be a string, an image source or a react component @supported Available in v5.x with theme version 3\n   * - `color`: color to use as background color for shifting bottom navigation @deprecatedProperty In v5.x works only with theme version 2.\n   * - `badge`: badge to show on the tab icon, can be `true` to show a dot, `string` or `number` to show text.\n   * - `accessibilityLabel`: accessibility label for the tab button\n   * - `testID`: test id for the tab button\n   *\n   * Example:\n   *\n   * ```js\n   * {\n   *   index: 1,\n   *   routes: [\n   *     { key: 'music', title: 'Favorites', focusedIcon: 'heart', unfocusedIcon: 'heart-outline'},\n   *     { key: 'albums', title: 'Albums', focusedIcon: 'album' },\n   *     { key: 'recents', title: 'Recents', focusedIcon: 'history' },\n   *     { key: 'notifications', title: 'Notifications', focusedIcon: 'bell', unfocusedIcon: 'bell-outline' },\n   *   ]\n   * }\n   * ```\n   *\n   * `BottomNavigation.Bar` is a controlled component, which means the `index` needs to be updated via the `onTabPress` callback.\n   */\n  navigationState: NavigationState<Route>;\n  /**\n   * Callback which returns a React Element to be used as tab icon.\n   */\n  renderIcon?: (props: {\n    route: Route;\n    focused: boolean;\n    color: string;\n  }) => React.ReactNode;\n  /**\n   * Callback which React Element to be used as tab label.\n   */\n  renderLabel?: (props: {\n    route: Route;\n    focused: boolean;\n    color: string;\n  }) => React.ReactNode;\n  /**\n   * Callback which returns a React element to be used as the touchable for the tab item.\n   * Renders a `TouchableRipple` on Android and `Pressable` on iOS.\n   */\n  renderTouchable?: (props: TouchableProps<Route>) => React.ReactNode;\n  /**\n   * Get accessibility label for the tab button. This is read by the screen reader when the user taps the tab.\n   * Uses `route.accessibilityLabel` by default.\n   */\n  getAccessibilityLabel?: (props: { route: Route }) => string | undefined;\n  /**\n   * Get badge for the tab, uses `route.badge` by default.\n   */\n  getBadge?: (props: { route: Route }) => boolean | number | string | undefined;\n  /**\n   * Get color for the tab, uses `route.color` by default.\n   */\n  getColor?: (props: { route: Route }) => string | undefined;\n  /**\n   * Get label text for the tab, uses `route.title` by default. Use `renderLabel` to replace label component.\n   */\n  getLabelText?: (props: { route: Route }) => string | undefined;\n  /**\n   * Get the id to locate this tab button in tests, uses `route.testID` by default.\n   */\n  getTestID?: (props: { route: Route }) => string | undefined;\n  /**\n   * Function to execute on tab press. It receives the route for the pressed tab. Use this to update the navigation state.\n   */\n  onTabPress: (props: { route: Route } & TabPressEvent) => void;\n  /**\n   * Function to execute on tab long press. It receives the route for the pressed tab\n   */\n  onTabLongPress?: (props: { route: Route } & TabPressEvent) => void;\n  /**\n   * Custom color for icon and label in the active tab.\n   */\n  activeColor?: string;\n  /**\n   * Custom color for icon and label in the inactive tab.\n   */\n  inactiveColor?: string;\n  /**\n   * The scene animation Easing.\n   */\n  animationEasing?: EasingFunction | undefined;\n  /**\n   * Whether the bottom navigation bar is hidden when keyboard is shown.\n   * On Android, this works best when [`windowSoftInputMode`](https://developer.android.com/guide/topics/manifest/activity-element#wsoft) is set to `adjustResize`.\n   */\n  keyboardHidesNavigationBar?: boolean;\n  /**\n   * Safe area insets for the tab bar. This can be used to avoid elements like the navigation bar on Android and bottom safe area on iOS.\n   * The bottom insets for iOS is added by default. You can override the behavior with this option.\n   */\n  safeAreaInsets?: {\n    top?: number;\n    right?: number;\n    bottom?: number;\n    left?: number;\n  };\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  activeIndicatorStyle?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n};\n\nconst MIN_RIPPLE_SCALE = 0.001; // Minimum scale is not 0 due to bug with animation\nconst MIN_TAB_WIDTH = 96;\nconst MAX_TAB_WIDTH = 168;\nconst BAR_HEIGHT = 56;\nconst OUTLINE_WIDTH = 64;\n\nconst Touchable = <Route extends BaseRoute>({\n  route: _0,\n  style,\n  children,\n  borderless,\n  centered,\n  rippleColor,\n  ...rest\n}: TouchableProps<Route>) =>\n  TouchableRipple.supported ? (\n    <TouchableRipple\n      {...rest}\n      disabled={rest.disabled || undefined}\n      borderless={borderless}\n      centered={centered}\n      rippleColor={rippleColor}\n      style={style}\n    >\n      {children}\n    </TouchableRipple>\n  ) : (\n    <Pressable style={style} {...rest}>\n      {children}\n    </Pressable>\n  );\n\n/**\n * A navigation bar which can easily be integrated with [React Navigation's Bottom Tabs Navigator](https://reactnavigation.org/docs/bottom-tab-navigator/).\n *\n * ## Usage\n * ### without React Navigation\n * ```js\n * import React from 'react';\n * import { useState } from 'react';\n * import { View } from 'react-native';\n * import { BottomNavigation, Text, Provider } from 'react-native-paper';\n * import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';\n *\n * function HomeScreen() {\n *   return (\n *     <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>\n *       <Text>Home!</Text>\n *     </View>\n *   );\n * }\n *\n * function SettingsScreen() {\n *   return (\n *     <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>\n *       <Text>Settings!</Text>\n *   </View>\n *   );\n * }\n *\n * export default function MyComponent() {\n *   const [index, setIndex] = useState(0);\n *\n *   const routes = [\n *     { key: 'home', title: 'Home', icon: 'home' },\n *     { key: 'settings', title: 'Settings', icon: 'cog' },\n *   ];\n\n *   const renderScene = ({ route }) => {\n *     switch (route.key) {\n *       case 'home':\n *         return <HomeScreen />;\n *       case 'settings':\n *         return <SettingsScreen />;\n *       default:\n *         return null;\n *     }\n *   };\n *\n *   return (\n *     <Provider>\n *       {renderScene({ route: routes[index] })}\n *       <BottomNavigation.Bar\n *         navigationState={{ index, routes }}\n *         onTabPress={({ route }) => {\n *           const newIndex = routes.findIndex((r) => r.key === route.key);\n *           if (newIndex !== -1) {\n *             setIndex(newIndex);\n *           }\n *         }}\n *         renderIcon={({ route, color }) => (\n *           <Icon name={route.icon} size={24} color={color} />\n *         )}\n *         getLabelText={({ route }) => route.title}\n *       />\n *     </Provider>\n *   );\n * }\n * ```\n */\nconst BottomNavigationBar = <Route extends BaseRoute>({\n  navigationState,\n  renderIcon,\n  renderLabel,\n  renderTouchable = ({ key, ...props }: TouchableProps<Route>) => (\n    <Touchable key={key} {...props} />\n  ),\n  getLabelText = ({ route }: { route: Route }) => route.title,\n  getBadge = ({ route }: { route: Route }) => route.badge,\n  getColor = ({ route }: { route: Route }) => route.color,\n  getAccessibilityLabel = ({ route }: { route: Route }) =>\n    route.accessibilityLabel,\n  getTestID = ({ route }: { route: Route }) => route.testID,\n  activeColor,\n  inactiveColor,\n  keyboardHidesNavigationBar = Platform.OS === 'android',\n  style,\n  activeIndicatorStyle,\n  labeled = true,\n  animationEasing,\n  onTabPress,\n  onTabLongPress,\n  shifting: shiftingProp,\n  safeAreaInsets,\n  labelMaxFontSizeMultiplier = 1,\n  compact: compactProp,\n  testID = 'bottom-navigation-bar',\n  theme: themeOverrides,\n}: Props<Route>) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { bottom, left, right } = useSafeAreaInsets();\n  const { scale } = theme.animation;\n  const compact = compactProp ?? !theme.isV3;\n  let shifting =\n    shiftingProp ?? (theme.isV3 ? false : navigationState.routes.length > 3);\n\n  if (shifting && navigationState.routes.length < 2) {\n    shifting = false;\n    console.warn(\n      'BottomNavigation.Bar needs at least 2 tabs to run shifting animation'\n    );\n  }\n\n  /**\n   * Visibility of the navigation bar, visible state is 1 and invisible is 0.\n   */\n  const visibleAnim = useAnimatedValue(1);\n\n  /**\n   * Active state of individual tab items, active state is 1 and inactive state is 0.\n   */\n  const tabsAnims = useAnimatedValueArray(\n    navigationState.routes.map(\n      // focused === 1, unfocused === 0\n      (_, i) => (i === navigationState.index ? 1 : 0)\n    )\n  );\n\n  /**\n   * Index of the currently active tab. Used for setting the background color.\n   * We don't use the color as an animated value directly, because `setValue` seems to be buggy with colors?.\n   */\n  const indexAnim = useAnimatedValue(navigationState.index);\n\n  /**\n   * Animation for the background color ripple, used to determine it's scale and opacity.\n   */\n  const rippleAnim = useAnimatedValue(MIN_RIPPLE_SCALE);\n\n  /**\n   * Layout of the navigation bar. The width is used to determine the size and position of the ripple.\n   */\n  const [layout, onLayout] = useLayout();\n\n  /**\n   * Track whether the keyboard is visible to show and hide the navigation bar.\n   */\n  const [keyboardVisible, setKeyboardVisible] = React.useState(false);\n\n  const handleKeyboardShow = React.useCallback(() => {\n    setKeyboardVisible(true);\n    Animated.timing(visibleAnim, {\n      toValue: 0,\n      duration: 150 * scale,\n      useNativeDriver: true,\n    }).start();\n  }, [scale, visibleAnim]);\n\n  const handleKeyboardHide = React.useCallback(() => {\n    Animated.timing(visibleAnim, {\n      toValue: 1,\n      duration: 100 * scale,\n      useNativeDriver: true,\n    }).start(() => {\n      setKeyboardVisible(false);\n    });\n  }, [scale, visibleAnim]);\n\n  const animateToIndex = React.useCallback(\n    (index: number) => {\n      // Reset the ripple to avoid glitch if it's currently animating\n      rippleAnim.setValue(MIN_RIPPLE_SCALE);\n\n      Animated.parallel([\n        Animated.timing(rippleAnim, {\n          toValue: 1,\n          duration: theme.isV3 || shifting ? 400 * scale : 0,\n          useNativeDriver: true,\n        }),\n        ...navigationState.routes.map((_, i) =>\n          Animated.timing(tabsAnims[i], {\n            toValue: i === index ? 1 : 0,\n            duration: theme.isV3 || shifting ? 150 * scale : 0,\n            useNativeDriver: true,\n            easing: animationEasing,\n          })\n        ),\n      ]).start(() => {\n        // Workaround a bug in native animations where this is reset after first animation\n        tabsAnims.map((tab, i) => tab.setValue(i === index ? 1 : 0));\n\n        // Update the index to change bar's background color and then hide the ripple\n        indexAnim.setValue(index);\n        rippleAnim.setValue(MIN_RIPPLE_SCALE);\n      });\n    },\n    [\n      rippleAnim,\n      theme.isV3,\n      shifting,\n      scale,\n      navigationState.routes,\n      tabsAnims,\n      animationEasing,\n      indexAnim,\n    ]\n  );\n\n  React.useEffect(() => {\n    // Workaround for native animated bug in react-native@^0.57\n    // Context: https://github.com/callstack/react-native-paper/pull/637\n    animateToIndex(navigationState.index);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  useIsKeyboardShown({\n    onShow: handleKeyboardShow,\n    onHide: handleKeyboardHide,\n  });\n\n  React.useEffect(() => {\n    animateToIndex(navigationState.index);\n  }, [navigationState.index, animateToIndex]);\n\n  const eventForIndex = (index: number) => {\n    const event = {\n      route: navigationState.routes[index],\n      defaultPrevented: false,\n      preventDefault: () => {\n        event.defaultPrevented = true;\n      },\n    };\n\n    return event;\n  };\n\n  const { routes } = navigationState;\n  const { colors, dark: isDarkTheme, mode, isV3 } = theme;\n\n  const { backgroundColor: customBackground, elevation = 4 } =\n    (StyleSheet.flatten(style) || {}) as {\n      elevation?: number;\n      backgroundColor?: ColorValue;\n    };\n\n  const approxBackgroundColor = customBackground\n    ? customBackground\n    : isDarkTheme && mode === 'adaptive'\n    ? overlay(elevation, colors?.surface)\n    : colors?.primary;\n\n  const v2BackgroundColorInterpolation = shifting\n    ? indexAnim.interpolate({\n        inputRange: routes.map((_, i) => i),\n        // FIXME: does outputRange support ColorValue or just strings?\n        // @ts-expect-error\n        outputRange: routes.map(\n          (route) => getColor({ route }) || approxBackgroundColor\n        ),\n      })\n    : approxBackgroundColor;\n\n  const backgroundColor = isV3\n    ? customBackground || theme.colors.elevation.level2\n    : shifting\n    ? v2BackgroundColorInterpolation\n    : approxBackgroundColor;\n\n  const isDark =\n    typeof approxBackgroundColor === 'string'\n      ? !color(approxBackgroundColor).isLight()\n      : true;\n\n  const textColor = isDark ? white : black;\n\n  const activeTintColor = getActiveTintColor({\n    activeColor,\n    defaultColor: textColor,\n    theme,\n  });\n\n  const inactiveTintColor = getInactiveTintColor({\n    inactiveColor,\n    defaultColor: textColor,\n    theme,\n  });\n  const touchColor = color(activeTintColor).alpha(0.12).rgb().string();\n\n  const maxTabWidth = routes.length > 3 ? MIN_TAB_WIDTH : MAX_TAB_WIDTH;\n  const maxTabBarWidth = maxTabWidth * routes.length;\n\n  const rippleSize = layout.width / 4;\n\n  const insets = {\n    left: safeAreaInsets?.left ?? left,\n    right: safeAreaInsets?.right ?? right,\n    bottom: safeAreaInsets?.bottom ?? bottom,\n  };\n\n  return (\n    <Surface\n      {...(theme.isV3 && { elevation: 0 })}\n      testID={testID}\n      style={[\n        !theme.isV3 && styles.elevation,\n        styles.bar,\n        keyboardHidesNavigationBar // eslint-disable-next-line react-native/no-inline-styles\n          ? {\n              // When the keyboard is shown, slide down the navigation bar\n              transform: [\n                {\n                  translateY: visibleAnim.interpolate({\n                    inputRange: [0, 1],\n                    outputRange: [layout.height, 0],\n                  }),\n                },\n              ],\n              // Absolutely position the navigation bar so that the content is below it\n              // This is needed to avoid gap at bottom when the navigation bar is hidden\n              position: keyboardVisible ? 'absolute' : undefined,\n            }\n          : null,\n        style,\n      ]}\n      pointerEvents={\n        layout.measured\n          ? keyboardHidesNavigationBar && keyboardVisible\n            ? 'none'\n            : 'auto'\n          : 'none'\n      }\n      onLayout={onLayout}\n    >\n      <Animated.View\n        style={[styles.barContent, { backgroundColor }]}\n        testID={`${testID}-content`}\n      >\n        <View\n          style={[\n            styles.items,\n            {\n              marginBottom: insets.bottom,\n              marginHorizontal: Math.max(insets.left, insets.right),\n            },\n            compact && {\n              maxWidth: maxTabBarWidth,\n            },\n          ]}\n          accessibilityRole={'tablist'}\n          testID={`${testID}-content-wrapper`}\n        >\n          {shifting && !isV3 ? (\n            <Animated.View\n              pointerEvents=\"none\"\n              style={[\n                styles.ripple,\n                {\n                  // Since we have a single ripple, we have to reposition it so that it appears to expand from active tab.\n                  // We need to move it from the top to center of the navigation bar and from the left to the active tab.\n                  top: (BAR_HEIGHT - rippleSize) / 2,\n                  left:\n                    (Math.min(layout.width, maxTabBarWidth) / routes.length) *\n                      (navigationState.index + 0.5) -\n                    rippleSize / 2,\n                  height: rippleSize,\n                  width: rippleSize,\n                  borderRadius: rippleSize / 2,\n                  backgroundColor: getColor({\n                    route: routes[navigationState.index],\n                  }),\n                  transform: [\n                    {\n                      // Scale to twice the size  to ensure it covers the whole navigation bar\n                      scale: rippleAnim.interpolate({\n                        inputRange: [0, 1],\n                        outputRange: [0, 8],\n                      }),\n                    },\n                  ],\n                  opacity: rippleAnim.interpolate({\n                    inputRange: [0, MIN_RIPPLE_SCALE, 0.3, 1],\n                    outputRange: [0, 0, 1, 1],\n                  }),\n                },\n              ]}\n              testID={`${testID}-content-ripple`}\n            />\n          ) : null}\n          {routes.map((route, index) => {\n            const focused = navigationState.index === index;\n            const active = tabsAnims[index];\n\n            // Scale the label up\n            const scale =\n              labeled && shifting\n                ? active.interpolate({\n                    inputRange: [0, 1],\n                    outputRange: [0.5, 1],\n                  })\n                : 1;\n\n            // Move down the icon to account for no-label in shifting and smaller label in non-shifting.\n            const translateY = labeled\n              ? shifting\n                ? active.interpolate({\n                    inputRange: [0, 1],\n                    outputRange: [7, 0],\n                  })\n                : 0\n              : 7;\n\n            // We render the active icon and label on top of inactive ones and cross-fade them on change.\n            // This trick gives the illusion that we are animating between active and inactive colors.\n            // This is to ensure that we can use native driver, as colors cannot be animated with native driver.\n            const activeOpacity = active;\n            const inactiveOpacity = active.interpolate({\n              inputRange: [0, 1],\n              outputRange: [1, 0],\n            });\n\n            const v3ActiveOpacity = focused ? 1 : 0;\n            const v3InactiveOpacity = shifting\n              ? inactiveOpacity\n              : focused\n              ? 0\n              : 1;\n\n            // Scale horizontally the outline pill\n            const outlineScale = focused\n              ? active.interpolate({\n                  inputRange: [0, 1],\n                  outputRange: [0.5, 1],\n                })\n              : 0;\n\n            const badge = getBadge({ route });\n\n            const activeLabelColor = getLabelColor({\n              tintColor: activeTintColor,\n              hasColor: Boolean(activeColor),\n              focused,\n              defaultColor: textColor,\n              theme,\n            });\n\n            const inactiveLabelColor = getLabelColor({\n              tintColor: inactiveTintColor,\n              hasColor: Boolean(inactiveColor),\n              focused,\n              defaultColor: textColor,\n              theme,\n            });\n\n            const badgeStyle = {\n              top: !isV3 ? -2 : typeof badge === 'boolean' ? 4 : 2,\n              right:\n                (badge != null && typeof badge !== 'boolean'\n                  ? String(badge).length * -2\n                  : 0) - (!isV3 ? 2 : 0),\n            };\n\n            const isLegacyOrV3Shifting = !isV3 || (isV3 && shifting && labeled);\n\n            const font = isV3 ? theme.fonts.labelMedium : {};\n\n            return renderTouchable({\n              key: route.key,\n              route,\n              borderless: true,\n              centered: true,\n              rippleColor: isV3 ? 'transparent' : touchColor,\n              onPress: () => onTabPress(eventForIndex(index)),\n              onLongPress: () => onTabLongPress?.(eventForIndex(index)),\n              testID: getTestID({ route }),\n              accessibilityLabel: getAccessibilityLabel({ route }),\n              accessibilityRole: Platform.OS === 'ios' ? 'button' : 'tab',\n              accessibilityState: { selected: focused },\n              style: [styles.item, isV3 && styles.v3Item],\n              children: (\n                <View\n                  pointerEvents=\"none\"\n                  style={\n                    isV3 &&\n                    (labeled\n                      ? styles.v3TouchableContainer\n                      : styles.v3NoLabelContainer)\n                  }\n                >\n                  <Animated.View\n                    style={[\n                      styles.iconContainer,\n                      isV3 && styles.v3IconContainer,\n                      isLegacyOrV3Shifting && {\n                        transform: [{ translateY }],\n                      },\n                    ]}\n                  >\n                    {isV3 && focused && (\n                      <Animated.View\n                        style={[\n                          styles.outline,\n                          {\n                            transform: [\n                              {\n                                scaleX: outlineScale,\n                              },\n                            ],\n                            backgroundColor: theme.colors.secondaryContainer,\n                          },\n                          activeIndicatorStyle,\n                        ]}\n                      />\n                    )}\n                    <Animated.View\n                      style={[\n                        styles.iconWrapper,\n                        isV3 && styles.v3IconWrapper,\n                        {\n                          opacity: isLegacyOrV3Shifting\n                            ? activeOpacity\n                            : v3ActiveOpacity,\n                        },\n                      ]}\n                    >\n                      {renderIcon ? (\n                        renderIcon({\n                          route,\n                          focused: true,\n                          color: activeTintColor,\n                        })\n                      ) : (\n                        <Icon\n                          source={route.focusedIcon as IconSource}\n                          color={activeTintColor}\n                          size={24}\n                        />\n                      )}\n                    </Animated.View>\n                    <Animated.View\n                      style={[\n                        styles.iconWrapper,\n                        isV3 && styles.v3IconWrapper,\n                        {\n                          opacity: isLegacyOrV3Shifting\n                            ? inactiveOpacity\n                            : v3InactiveOpacity,\n                        },\n                      ]}\n                    >\n                      {renderIcon ? (\n                        renderIcon({\n                          route,\n                          focused: false,\n                          color: inactiveTintColor,\n                        })\n                      ) : (\n                        <Icon\n                          source={\n                            theme.isV3 && route.unfocusedIcon !== undefined\n                              ? route.unfocusedIcon\n                              : (route.focusedIcon as IconSource)\n                          }\n                          color={inactiveTintColor}\n                          size={24}\n                        />\n                      )}\n                    </Animated.View>\n                    <View style={[styles.badgeContainer, badgeStyle]}>\n                      {typeof badge === 'boolean' ? (\n                        <Badge visible={badge} size={isV3 ? 6 : 8} />\n                      ) : (\n                        <Badge visible={badge != null} size={16}>\n                          {badge}\n                        </Badge>\n                      )}\n                    </View>\n                  </Animated.View>\n                  {labeled ? (\n                    <Animated.View\n                      style={[\n                        styles.labelContainer,\n                        !isV3 && { transform: [{ scale }] },\n                      ]}\n                    >\n                      <Animated.View\n                        style={[\n                          styles.labelWrapper,\n                          {\n                            opacity: isLegacyOrV3Shifting\n                              ? activeOpacity\n                              : v3ActiveOpacity,\n                          },\n                        ]}\n                      >\n                        {renderLabel ? (\n                          renderLabel({\n                            route,\n                            focused: true,\n                            color: activeLabelColor,\n                          })\n                        ) : (\n                          <Text\n                            maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n                            variant=\"labelMedium\"\n                            style={[\n                              styles.label,\n                              {\n                                color: activeLabelColor,\n                                ...font,\n                              },\n                            ]}\n                          >\n                            {getLabelText({ route })}\n                          </Text>\n                        )}\n                      </Animated.View>\n                      {shifting ? null : (\n                        <Animated.View\n                          style={[\n                            styles.labelWrapper,\n                            {\n                              opacity: isLegacyOrV3Shifting\n                                ? inactiveOpacity\n                                : v3InactiveOpacity,\n                            },\n                          ]}\n                        >\n                          {renderLabel ? (\n                            renderLabel({\n                              route,\n                              focused: false,\n                              color: inactiveLabelColor,\n                            })\n                          ) : (\n                            <Text\n                              maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n                              variant=\"labelMedium\"\n                              selectable={false}\n                              style={[\n                                styles.label,\n                                {\n                                  color: inactiveLabelColor,\n                                  ...font,\n                                },\n                              ]}\n                            >\n                              {getLabelText({ route })}\n                            </Text>\n                          )}\n                        </Animated.View>\n                      )}\n                    </Animated.View>\n                  ) : (\n                    !isV3 && <View style={styles.labelContainer} />\n                  )}\n                </View>\n              ),\n            });\n          })}\n        </View>\n      </Animated.View>\n    </Surface>\n  );\n};\n\nBottomNavigationBar.displayName = 'BottomNavigation.Bar';\n\nexport default BottomNavigationBar;\n\nconst styles = StyleSheet.create({\n  bar: {\n    left: 0,\n    right: 0,\n    bottom: 0,\n  },\n  barContent: {\n    alignItems: 'center',\n    overflow: 'hidden',\n  },\n  items: {\n    flexDirection: 'row',\n    ...(Platform.OS === 'web'\n      ? {\n          width: '100%',\n        }\n      : null),\n  },\n  item: {\n    flex: 1,\n    // Top padding is 6 and bottom padding is 10\n    // The extra 4dp bottom padding is offset by label's height\n    paddingVertical: 6,\n  },\n  v3Item: {\n    paddingVertical: 0,\n  },\n  ripple: {\n    position: 'absolute',\n  },\n  iconContainer: {\n    height: 24,\n    width: 24,\n    marginTop: 2,\n    marginHorizontal: 12,\n    alignSelf: 'center',\n  },\n  v3IconContainer: {\n    height: 32,\n    width: 32,\n    marginBottom: 4,\n    marginTop: 0,\n    justifyContent: 'center',\n  },\n  iconWrapper: {\n    ...StyleSheet.absoluteFillObject,\n    alignItems: 'center',\n  },\n  v3IconWrapper: {\n    top: 4,\n  },\n  labelContainer: {\n    height: 16,\n    paddingBottom: 2,\n  },\n  labelWrapper: {\n    ...StyleSheet.absoluteFillObject,\n  },\n  // eslint-disable-next-line react-native/no-color-literals\n  label: {\n    fontSize: 12,\n    height: BAR_HEIGHT,\n    textAlign: 'center',\n    backgroundColor: 'transparent',\n    ...(Platform.OS === 'web'\n      ? {\n          whiteSpace: 'nowrap',\n          alignSelf: 'center',\n        }\n      : null),\n  },\n  badgeContainer: {\n    position: 'absolute',\n    left: 0,\n  },\n  v3TouchableContainer: {\n    paddingTop: 12,\n    paddingBottom: 16,\n  },\n  v3NoLabelContainer: {\n    height: 80,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  outline: {\n    width: OUTLINE_WIDTH,\n    height: OUTLINE_WIDTH / 2,\n    borderRadius: OUTLINE_WIDTH / 4,\n    alignSelf: 'center',\n  },\n  elevation: {\n    elevation: 4,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,IAAA;AAa9B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,SACEC,kBAAkB,EAClBC,oBAAoB,EACpBC,aAAa;AAEf,SAASC,gBAAgB;AACzB,OAAOC,OAAO;AACd,SAASC,KAAK,EAAEC,KAAK;AAErB,OAAOC,gBAAgB;AACvB,OAAOC,qBAAqB;AAC5B,OAAOC,kBAAkB;AACzB,OAAOC,SAAS;AAChB,OAAOC,KAAK;AACZ,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,eAAe;AAEtB,OAAOC,IAAI;AAoLX,IAAMC,gBAAgB,GAAG,KAAK;AAC9B,IAAMC,aAAa,GAAG,EAAE;AACxB,IAAMC,aAAa,GAAG,GAAG;AACzB,IAAMC,UAAU,GAAG,EAAE;AACrB,IAAMC,aAAa,GAAG,EAAE;AAExB,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAGC,IAAA;EAAA,IACTC,EAAE,GAOaD,IAAA,CAPtBE,KAAK;IACLC,KAAK,GAMiBH,IAAA,CANtBG,KAAK;IACLC,QAAQ,GAKcJ,IAAA,CALtBI,QAAQ;IACRC,UAAU,GAIYL,IAAA,CAJtBK,UAAU;IACVC,QAAQ,GAGcN,IAAA,CAHtBM,QAAQ;IACRC,WAAW,GAEWP,IAAA,CAFtBO,WAAW;IACRC,IAAA,GAAAC,wBAAA,CACmBT,IAAA,EAAAU,SAAA;EAAA,OACtBlB,eAAe,CAACmB,SAAS,GACvBzC,KAAA,CAAA0C,aAAA,CAACpB,eAAe,EAAAqB,QAAA,KACVL,IAAI;IACRM,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAIC,SAAU;IACrCV,UAAU,EAAEA,UAAW;IACvBC,QAAQ,EAAEA,QAAS;IACnBC,WAAW,EAAEA,WAAY;IACzBJ,KAAK,EAAEA;EAAM,IAEZC,QACc,CAAC,GAElBlC,KAAA,CAAA0C,aAAA,CAACtC,SAAS,EAAAuC,QAAA;IAACV,KAAK,EAAEA;EAAM,GAAKK,IAAI,GAC9BJ,QACQ,CACZ;AAAA;AAsEH,IAAMY,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAGC,KAAA,EA4BR;EAAA,IAAAC,KAAA,EAAAC,KAAA,EAAAC,MAAA;EAAA,IA3BlBC,eAAe,GA2BFJ,KAAA,CA3BbI,eAAe;IACfC,UAAU,GA0BGL,KAAA,CA1BbK,UAAU;IACVC,WAAW,GAyBEN,KAAA,CAzBbM,WAAW;IAAAC,qBAAA,GAyBEP,KAAA,CAxBbQ,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAG,UAAAE,KAAA;MAAA,IAAGC,GAAG,GAAmCD,KAAA,CAAtCC,GAAG;QAAKC,KAAA,GAAAnB,wBAAA,CAA8BiB,KAAA,EAAAG,UAAA;MAAA,OACzD3D,KAAA,CAAA0C,aAAA,CAACb,SAAS,EAAAc,QAAA;QAACc,GAAG,EAAEA;MAAI,GAAKC,KAAK,CAAG,CAAC;IAAA,CACnC,GAAAJ,qBAAA;IAAAM,kBAAA,GAsBYb,KAAA,CArBbc,YAAY;IAAZA,YAAY,GAAAD,kBAAA,cAAG,UAAAE,KAAA;MAAA,IAAG9B,KAAA,GAAyB8B,KAAA,CAAzB9B,KAAA;MAAyB,OAAKA,KAAK,CAAC+B,KAAK;IAAA,IAAAH,kBAAA;IAAAI,cAAA,GAqB9CjB,KAAA,CApBbkB,QAAQ;IAARA,QAAQ,GAAAD,cAAA,cAAG,UAAAE,KAAA;MAAA,IAAGlC,KAAA,GAAyBkC,KAAA,CAAzBlC,KAAA;MAAyB,OAAKA,KAAK,CAACmC,KAAK;IAAA,IAAAH,cAAA;IAAAI,cAAA,GAoB1CrB,KAAA,CAnBbsB,QAAQ;IAARA,QAAQ,GAAAD,cAAA,cAAG,UAAAE,KAAA;MAAA,IAAGtC,KAAA,GAAyBsC,KAAA,CAAzBtC,KAAA;MAAyB,OAAKA,KAAK,CAAC1B,KAAK;IAAA,IAAA8D,cAAA;IAAAG,qBAAA,GAmB1CxB,KAAA,CAlBbyB,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,UAAAE,KAAA;MAAA,IAAGzC,KAAA,GAAyByC,KAAA,CAAzBzC,KAAA;MAAyB,OAClDA,KAAK,CAAC0C,kBAAkB;IAAA,IAAAH,qBAAA;IAAAI,eAAA,GAiBb5B,KAAA,CAhBb6B,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAG,UAAAE,KAAA;MAAA,IAAG7C,KAAA,GAAyB6C,KAAA,CAAzB7C,KAAA;MAAyB,OAAKA,KAAK,CAAC8C,MAAM;IAAA,IAAAH,eAAA;IACzDI,WAAW,GAeEhC,KAAA,CAfbgC,WAAW;IACXC,aAAa,GAcAjC,KAAA,CAdbiC,aAAa;IAAAC,qBAAA,GAcAlC,KAAA,CAbbmC,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAG/E,QAAQ,CAACiF,EAAE,KAAK,SAAS,GAAAF,qBAAA;IACtDhD,KAAK,GAYQc,KAAA,CAZbd,KAAK;IACLmD,oBAAoB,GAWPrC,KAAA,CAXbqC,oBAAoB;IAAAC,aAAA,GAWPtC,KAAA,CAVbuC,OAAO;IAAPA,OAAO,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACdE,eAAe,GASFxC,KAAA,CATbwC,eAAe;IACfC,UAAU,GAQGzC,KAAA,CARbyC,UAAU;IACVC,cAAc,GAOD1C,KAAA,CAPb0C,cAAc;IACJC,YAAY,GAMT3C,KAAA,CANb4C,QAAQ;IACRC,cAAc,GAKD7C,KAAA,CALb6C,cAAc;IAAAC,qBAAA,GAKD9C,KAAA,CAJb+C,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;IACrBE,WAAW,GAGPhD,KAAA,CAHbiD,OAAO;IAAAC,YAAA,GAGMlD,KAAA,CAFb+B,MAAM;IAANA,MAAM,GAAAmB,YAAA,cAAG,uBAAuB,GAAAA,YAAA;IACzBC,cAAA,GACMnD,KAAA,CADboD,KAAK;EAEL,IAAMA,KAAK,GAAGxF,gBAAgB,CAACuF,cAAc,CAAC;EAC9C,IAAAE,kBAAA,GAAgC7F,iBAAiB,CAAC,CAAC;IAA3C8F,MAAM,GAAAD,kBAAA,CAANC,MAAM;IAAEC,IAAI,GAAAF,kBAAA,CAAJE,IAAI;IAAEC,KAAA,GAAAH,kBAAA,CAAAG,KAAA;EACtB,IAAQC,KAAA,GAAUL,KAAK,CAACM,SAAS,CAAzBD,KAAA;EACR,IAAMR,OAAO,GAAGD,WAAW,WAAXA,WAAW,GAAI,CAACI,KAAK,CAACO,IAAI;EAC1C,IAAIf,QAAQ,GACVD,YAAY,WAAZA,YAAY,GAAKS,KAAK,CAACO,IAAI,GAAG,KAAK,GAAGvD,eAAe,CAACwD,MAAM,CAACC,MAAM,GAAG,CAAE;EAE1E,IAAIjB,QAAQ,IAAIxC,eAAe,CAACwD,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;IACjDjB,QAAQ,GAAG,KAAK;IAChBkB,OAAO,CAACC,IAAI,CACV,sEACF,CAAC;EACH;EAKA,IAAMC,WAAW,GAAGhG,gBAAgB,CAAC,CAAC,CAAC;EAKvC,IAAMiG,SAAS,GAAGhG,qBAAqB,CACrCmC,eAAe,CAACwD,MAAM,CAACM,GAAG,CAExB,UAACC,CAAC,EAAEC,CAAC;IAAA,OAAMA,CAAC,KAAKhE,eAAe,CAACiE,KAAK,GAAG,CAAC,GAAG,CAC/C;EAAA,EACF,CAAC;EAMD,IAAMC,SAAS,GAAGtG,gBAAgB,CAACoC,eAAe,CAACiE,KAAK,CAAC;EAKzD,IAAME,UAAU,GAAGvG,gBAAgB,CAACS,gBAAgB,CAAC;EAKrD,IAAA+F,UAAA,GAA2BrG,SAAS,CAAC,CAAC;IAAAsG,WAAA,GAAAC,cAAA,CAAAF,UAAA;IAA/BG,MAAM,GAAAF,WAAA;IAAEG,QAAQ,GAAAH,WAAA;EAKvB,IAAAI,eAAA,GAA8C5H,KAAK,CAAC6H,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAL,cAAA,CAAAG,eAAA;IAA5DG,eAAe,GAAAD,gBAAA;IAAEE,kBAAkB,GAAAF,gBAAA;EAE1C,IAAMG,kBAAkB,GAAGjI,KAAK,CAACkI,WAAW,CAAC,YAAM;IACjDF,kBAAkB,CAAC,IAAI,CAAC;IACxB/H,QAAQ,CAACkI,MAAM,CAACpB,WAAW,EAAE;MAC3BqB,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,GAAG,GAAG7B,KAAK;MACrB8B,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC/B,KAAK,EAAEO,WAAW,CAAC,CAAC;EAExB,IAAMyB,kBAAkB,GAAGxI,KAAK,CAACkI,WAAW,CAAC,YAAM;IACjDjI,QAAQ,CAACkI,MAAM,CAACpB,WAAW,EAAE;MAC3BqB,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,GAAG,GAAG7B,KAAK;MACrB8B,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,YAAM;MACbP,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxB,KAAK,EAAEO,WAAW,CAAC,CAAC;EAExB,IAAM0B,cAAc,GAAGzI,KAAK,CAACkI,WAAW,CACrC,UAAAd,KAAa,EAAK;IAEjBE,UAAU,CAACoB,QAAQ,CAAClH,gBAAgB,CAAC;IAErCvB,QAAQ,CAAC0I,QAAQ,EACf1I,QAAQ,CAACkI,MAAM,CAACb,UAAU,EAAE;MAC1Bc,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAElC,KAAK,CAACO,IAAI,IAAIf,QAAQ,GAAG,GAAG,GAAGa,KAAK,GAAG,CAAC;MAClD8B,eAAe,EAAE;IACnB,CAAC,CAAC,EAAAM,MAAA,CAAAC,kBAAA,CACC1F,eAAe,CAACwD,MAAM,CAACM,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;MAAA,OACjClH,QAAQ,CAACkI,MAAM,CAACnB,SAAS,CAACG,CAAC,CAAC,EAAE;QAC5BiB,OAAO,EAAEjB,CAAC,KAAKC,KAAK,GAAG,CAAC,GAAG,CAAC;QAC5BiB,QAAQ,EAAElC,KAAK,CAACO,IAAI,IAAIf,QAAQ,GAAG,GAAG,GAAGa,KAAK,GAAG,CAAC;QAClD8B,eAAe,EAAE,IAAI;QACrBQ,MAAM,EAAEvD;MACV,CAAC,CACH;IAAA,EAAC,EACF,CAAC,CAACgD,KAAK,CAAC,YAAM;MAEbvB,SAAS,CAACC,GAAG,CAAC,UAAC8B,GAAG,EAAE5B,CAAC;QAAA,OAAK4B,GAAG,CAACL,QAAQ,CAACvB,CAAC,KAAKC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAAA,EAAC;MAG5DC,SAAS,CAACqB,QAAQ,CAACtB,KAAK,CAAC;MACzBE,UAAU,CAACoB,QAAQ,CAAClH,gBAAgB,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,EACD,CACE8F,UAAU,EACVnB,KAAK,CAACO,IAAI,EACVf,QAAQ,EACRa,KAAK,EACLrD,eAAe,CAACwD,MAAM,EACtBK,SAAS,EACTzB,eAAe,EACf8B,SAAS,CAEb,CAAC;EAEDrH,KAAK,CAACgJ,SAAS,CAAC,YAAM;IAGpBP,cAAc,CAACtF,eAAe,CAACiE,KAAK,CAAC;EAEvC,CAAC,EAAE,EAAE,CAAC;EAENnG,kBAAkB,CAAC;IACjBgI,MAAM,EAAEhB,kBAAkB;IAC1BiB,MAAM,EAAEV;EACV,CAAC,CAAC;EAEFxI,KAAK,CAACgJ,SAAS,CAAC,YAAM;IACpBP,cAAc,CAACtF,eAAe,CAACiE,KAAK,CAAC;EACvC,CAAC,EAAE,CAACjE,eAAe,CAACiE,KAAK,EAAEqB,cAAc,CAAC,CAAC;EAE3C,IAAMU,aAAa,GAAI,SAAjBA,aAAaA,CAAI/B,KAAa,EAAK;IACvC,IAAMgC,KAAK,GAAG;MACZpH,KAAK,EAAEmB,eAAe,CAACwD,MAAM,CAACS,KAAK,CAAC;MACpCiC,gBAAgB,EAAE,KAAK;MACvBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA,EAAQ;QACpBF,KAAK,CAACC,gBAAgB,GAAG,IAAI;MAC/B;IACF,CAAC;IAED,OAAOD,KAAK;EACd,CAAC;EAED,IAAQzC,MAAA,GAAWxD,eAAe,CAA1BwD,MAAA;EACR,IAAQ4C,MAAM,GAAoCpD,KAAK,CAA/CoD,MAAM;IAAQC,WAAW,GAAiBrD,KAAK,CAAvCsD,IAAI;IAAeC,IAAI,GAAWvD,KAAK,CAApBuD,IAAI;IAAEhD,IAAA,GAASP,KAAK,CAAdO,IAAA;EAEzC,IAAAiD,KAAA,GACGxJ,UAAU,CAACyJ,OAAO,CAAC3H,KAAK,CAAC,IAAI,CAAC,CAG9B;IAJsB4H,gBAAgB,GAAAF,KAAA,CAAjCG,eAAe;IAAAC,eAAA,GAAAJ,KAAA,CAAoBK,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAG,IAAAA,eAAA;EAMvD,IAAME,qBAAqB,GAAGJ,gBAAgB,GAC1CA,gBAAgB,GAChBL,WAAW,IAAIE,IAAI,KAAK,UAAU,GAClC9I,OAAO,CAACoJ,SAAS,EAAET,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEW,OAAO,CAAC,GACnCX,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEY,OAAO;EAEnB,IAAMC,8BAA8B,GAAGzE,QAAQ,GAC3C0B,SAAS,CAACgD,WAAW,CAAC;IACpBC,UAAU,EAAE3D,MAAM,CAACM,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;MAAA,OAAKA,CAAC;IAAA,EAAC;IAGnCoD,WAAW,EAAE5D,MAAM,CAACM,GAAG,CACpB,UAAAjF,KAAK;MAAA,OAAKqC,QAAQ,CAAC;QAAErC,KAAA,EAAAA;MAAM,CAAC,CAAC,IAAIiI,qBACpC;IAAA;EACF,CAAC,CAAC,GACFA,qBAAqB;EAEzB,IAAMH,eAAe,GAAGpD,IAAI,GACxBmD,gBAAgB,IAAI1D,KAAK,CAACoD,MAAM,CAACS,SAAS,CAACQ,MAAM,GACjD7E,QAAQ,GACRyE,8BAA8B,GAC9BH,qBAAqB;EAEzB,IAAMQ,MAAM,GACV,OAAOR,qBAAqB,KAAK,QAAQ,GACrC,CAAC3J,KAAK,CAAC2J,qBAAqB,CAAC,CAACS,OAAO,CAAC,CAAC,GACvC,IAAI;EAEV,IAAMC,SAAS,GAAGF,MAAM,GAAG3J,KAAK,GAAGD,KAAK;EAExC,IAAM+J,eAAe,GAAGpK,kBAAkB,CAAC;IACzCuE,WAAW,EAAXA,WAAW;IACX8F,YAAY,EAAEF,SAAS;IACvBxE,KAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAM2E,iBAAiB,GAAGrK,oBAAoB,CAAC;IAC7CuE,aAAa,EAAbA,aAAa;IACb6F,YAAY,EAAEF,SAAS;IACvBxE,KAAA,EAAAA;EACF,CAAC,CAAC;EACF,IAAM4E,UAAU,GAAGzK,KAAK,CAACsK,eAAe,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAEpE,IAAMC,WAAW,GAAGxE,MAAM,CAACC,MAAM,GAAG,CAAC,GAAGnF,aAAa,GAAGC,aAAa;EACrE,IAAM0J,cAAc,GAAGD,WAAW,GAAGxE,MAAM,CAACC,MAAM;EAElD,IAAMyE,UAAU,GAAG3D,MAAM,CAAC4D,KAAK,GAAG,CAAC;EAEnC,IAAMC,MAAM,GAAG;IACbjF,IAAI,GAAAtD,KAAA,GAAE4C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEU,IAAI,YAAAtD,KAAA,GAAIsD,IAAI;IAClCC,KAAK,GAAAtD,KAAA,GAAE2C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEW,KAAK,YAAAtD,KAAA,GAAIsD,KAAK;IACrCF,MAAM,GAAAnD,MAAA,GAAE0C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAES,MAAM,YAAAnD,MAAA,GAAImD;EACpC,CAAC;EAED,OACErG,KAAA,CAAA0C,aAAA,CAACrB,OAAO,EAAAsB,QAAA,KACDwD,KAAK,CAACO,IAAI,IAAI;IAAEsD,SAAS,EAAE;EAAE,CAAC;IACnClF,MAAM,EAAEA,MAAO;IACf7C,KAAK,EAAE,CACL,CAACkE,KAAK,CAACO,IAAI,IAAI8E,MAAM,CAACxB,SAAS,EAC/BwB,MAAM,CAACC,GAAG,EACVvG,0BAA0B,GACtB;MAEEwG,SAAS,EAAE,CACT;QACEC,UAAU,EAAE5E,WAAW,CAACsD,WAAW,CAAC;UAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC7C,MAAM,CAACkE,MAAM,EAAE,CAAC;QAChC,CAAC;MACH,CAAC,CACF;MAGDC,QAAQ,EAAE9D,eAAe,GAAG,UAAU,GAAGlF;IAC3C,CAAC,GACD,IAAI,EACRZ,KAAK,CACL;IACF6J,aAAa,EACXpE,MAAM,CAACqE,QAAQ,GACX7G,0BAA0B,IAAI6C,eAAe,GAC3C,MAAM,GACN,MAAM,GACR,MACL;IACDJ,QAAQ,EAAEA;EAAS,IAEnB3H,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;IACZ4B,KAAK,EAAE,CAACuJ,MAAM,CAACQ,UAAU,EAAE;MAAElC,eAAA,EAAAA;IAAgB,CAAC,CAAE;IAChDhF,MAAM,EAAG,GAAEA,MAAO;EAAU,GAE5B9E,KAAA,CAAA0C,aAAA,CAACrC,IAAI;IACH4B,KAAK,EAAE,CACLuJ,MAAM,CAACS,KAAK,EACZ;MACEC,YAAY,EAAEX,MAAM,CAAClF,MAAM;MAC3B8F,gBAAgB,EAAEC,IAAI,CAACC,GAAG,CAACd,MAAM,CAACjF,IAAI,EAAEiF,MAAM,CAAChF,KAAK;IACtD,CAAC,EACDP,OAAO,IAAI;MACTsG,QAAQ,EAAElB;IACZ,CAAC,CACD;IACFmB,iBAAiB,EAAE,SAAU;IAC7BzH,MAAM,EAAG,GAAEA,MAAO;EAAkB,GAEnCa,QAAQ,IAAI,CAACe,IAAI,GAChB1G,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;IACZyL,aAAa,EAAC,MAAM;IACpB7J,KAAK,EAAE,CACLuJ,MAAM,CAACgB,MAAM,EACb;MAGEC,GAAG,EAAE,CAAC9K,UAAU,GAAG0J,UAAU,IAAI,CAAC;MAClC/E,IAAI,EACD8F,IAAI,CAACM,GAAG,CAAChF,MAAM,CAAC4D,KAAK,EAAEF,cAAc,CAAC,GAAGzE,MAAM,CAACC,MAAM,IACpDzD,eAAe,CAACiE,KAAK,GAAG,GAAG,CAAC,GAC/BiE,UAAU,GAAG,CAAC;MAChBO,MAAM,EAAEP,UAAU;MAClBC,KAAK,EAAED,UAAU;MACjBsB,YAAY,EAAEtB,UAAU,GAAG,CAAC;MAC5BvB,eAAe,EAAEzF,QAAQ,CAAC;QACxBrC,KAAK,EAAE2E,MAAM,CAACxD,eAAe,CAACiE,KAAK;MACrC,CAAC,CAAC;MACFsE,SAAS,EAAE,CACT;QAEElF,KAAK,EAAEc,UAAU,CAAC+C,WAAW,CAAC;UAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;QACpB,CAAC;MACH,CAAC,CACF;MACDqC,OAAO,EAAEtF,UAAU,CAAC+C,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE9I,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC+I,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;MAC1B,CAAC;IACH,CAAC,CACD;IACFzF,MAAM,EAAG,GAAEA,MAAO;EAAiB,CACpC,CAAC,GACA,IAAI,EACP6B,MAAM,CAACM,GAAG,CAAC,UAACjF,KAAK,EAAEoF,KAAK,EAAK;IAC5B,IAAMyF,OAAO,GAAG1J,eAAe,CAACiE,KAAK,KAAKA,KAAK;IAC/C,IAAM0F,MAAM,GAAG9F,SAAS,CAACI,KAAK,CAAC;IAG/B,IAAMZ,KAAK,GACTlB,OAAO,IAAIK,QAAQ,GACfmH,MAAM,CAACzC,WAAW,CAAC;MACjBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC,CAAC,GACF,CAAC;IAGP,IAAMoB,UAAU,GAAGrG,OAAO,GACtBK,QAAQ,GACNmH,MAAM,CAACzC,WAAW,CAAC;MACjBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,GACF,CAAC,GACH,CAAC;IAKL,IAAMwC,aAAa,GAAGD,MAAM;IAC5B,IAAME,eAAe,GAAGF,MAAM,CAACzC,WAAW,CAAC;MACzCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;IAEF,IAAM0C,eAAe,GAAGJ,OAAO,GAAG,CAAC,GAAG,CAAC;IACvC,IAAMK,iBAAiB,GAAGvH,QAAQ,GAC9BqH,eAAe,GACfH,OAAO,GACP,CAAC,GACD,CAAC;IAGL,IAAMM,YAAY,GAAGN,OAAO,GACxBC,MAAM,CAACzC,WAAW,CAAC;MACjBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC,CAAC,GACF,CAAC;IAEL,IAAMpG,KAAK,GAAGF,QAAQ,CAAC;MAAEjC,KAAA,EAAAA;IAAM,CAAC,CAAC;IAEjC,IAAMoL,gBAAgB,GAAG1M,aAAa,CAAC;MACrC2M,SAAS,EAAEzC,eAAe;MAC1B0C,QAAQ,EAAEC,OAAO,CAACxI,WAAW,CAAC;MAC9B8H,OAAO,EAAPA,OAAO;MACPhC,YAAY,EAAEF,SAAS;MACvBxE,KAAA,EAAAA;IACF,CAAC,CAAC;IAEF,IAAMqH,kBAAkB,GAAG9M,aAAa,CAAC;MACvC2M,SAAS,EAAEvC,iBAAiB;MAC5BwC,QAAQ,EAAEC,OAAO,CAACvI,aAAa,CAAC;MAChC6H,OAAO,EAAPA,OAAO;MACPhC,YAAY,EAAEF,SAAS;MACvBxE,KAAA,EAAAA;IACF,CAAC,CAAC;IAEF,IAAMsH,UAAU,GAAG;MACjBhB,GAAG,EAAE,CAAC/F,IAAI,GAAG,CAAC,CAAC,GAAG,OAAOvC,KAAK,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;MACpDoC,KAAK,EACH,CAACpC,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,GACxCuJ,MAAM,CAACvJ,KAAK,CAAC,CAACyC,MAAM,GAAG,CAAC,CAAC,GACzB,CAAC,KAAK,CAACF,IAAI,GAAG,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED,IAAMiH,oBAAoB,GAAG,CAACjH,IAAI,IAAKA,IAAI,IAAIf,QAAQ,IAAIL,OAAQ;IAEnE,IAAMsI,IAAI,GAAGlH,IAAI,GAAGP,KAAK,CAAC0H,KAAK,CAACC,WAAW,GAAG,CAAC,CAAC;IAEhD,OAAOvK,eAAe,CAAC;MACrBE,GAAG,EAAEzB,KAAK,CAACyB,GAAG;MACdzB,KAAK,EAALA,KAAK;MACLG,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAEqE,IAAI,GAAG,aAAa,GAAGqE,UAAU;MAC9CgD,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQvI,UAAU,CAAC2D,aAAa,CAAC/B,KAAK,CAAC,CAAC;MAAA;MAC/C4G,WAAW,EAAE,SAAbA,WAAWA,CAAA;QAAA,OAAQvI,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG0D,aAAa,CAAC/B,KAAK,CAAC,CAAC;MAAA;MACzDtC,MAAM,EAAEF,SAAS,CAAC;QAAE5C,KAAA,EAAAA;MAAM,CAAC,CAAC;MAC5B0C,kBAAkB,EAAEF,qBAAqB,CAAC;QAAExC,KAAA,EAAAA;MAAM,CAAC,CAAC;MACpDuK,iBAAiB,EAAErM,QAAQ,CAACiF,EAAE,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;MAC3D8I,kBAAkB,EAAE;QAAEC,QAAQ,EAAErB;MAAQ,CAAC;MACzC5K,KAAK,EAAE,CAACuJ,MAAM,CAAC2C,IAAI,EAAEzH,IAAI,IAAI8E,MAAM,CAAC4C,MAAM,CAAC;MAC3ClM,QAAQ,EACNlC,KAAA,CAAA0C,aAAA,CAACrC,IAAI;QACHyL,aAAa,EAAC,MAAM;QACpB7J,KAAK,EACHyE,IAAI,KACHpB,OAAO,GACJkG,MAAM,CAAC6C,oBAAoB,GAC3B7C,MAAM,CAAC8C,kBAAkB;MAC9B,GAEDtO,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;QACZ4B,KAAK,EAAE,CACLuJ,MAAM,CAAC+C,aAAa,EACpB7H,IAAI,IAAI8E,MAAM,CAACgD,eAAe,EAC9Bb,oBAAoB,IAAI;UACtBjC,SAAS,EAAE,CAAC;YAAEC,UAAA,EAAAA;UAAW,CAAC;QAC5B,CAAC;MACD,GAEDjF,IAAI,IAAImG,OAAO,IACd7M,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;QACZ4B,KAAK,EAAE,CACLuJ,MAAM,CAACiD,OAAO,EACd;UACE/C,SAAS,EAAE,CACT;YACEgD,MAAM,EAAEvB;UACV,CAAC,CACF;UACDrD,eAAe,EAAE3D,KAAK,CAACoD,MAAM,CAACoF;QAChC,CAAC,EACDvJ,oBAAoB;MACpB,CACH,CACF,EACDpF,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;QACZ4B,KAAK,EAAE,CACLuJ,MAAM,CAACoD,WAAW,EAClBlI,IAAI,IAAI8E,MAAM,CAACqD,aAAa,EAC5B;UACEjC,OAAO,EAAEe,oBAAoB,GACzBZ,aAAa,GACbE;QACN,CAAC;MACD,GAED7J,UAAU,GACTA,UAAU,CAAC;QACTpB,KAAK,EAALA,KAAK;QACL6K,OAAO,EAAE,IAAI;QACbvM,KAAK,EAAEsK;MACT,CAAC,CAAC,GAEF5K,KAAA,CAAA0C,aAAA,CAACtB,IAAI;QACH0N,MAAM,EAAE9M,KAAK,CAAC+M,WAA0B;QACxCzO,KAAK,EAAEsK,eAAgB;QACvBoE,IAAI,EAAE;MAAG,CACV,CAEU,CAAC,EAChBhP,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;QACZ4B,KAAK,EAAE,CACLuJ,MAAM,CAACoD,WAAW,EAClBlI,IAAI,IAAI8E,MAAM,CAACqD,aAAa,EAC5B;UACEjC,OAAO,EAAEe,oBAAoB,GACzBX,eAAe,GACfE;QACN,CAAC;MACD,GAED9J,UAAU,GACTA,UAAU,CAAC;QACTpB,KAAK,EAALA,KAAK;QACL6K,OAAO,EAAE,KAAK;QACdvM,KAAK,EAAEwK;MACT,CAAC,CAAC,GAEF9K,KAAA,CAAA0C,aAAA,CAACtB,IAAI;QACH0N,MAAM,EACJ3I,KAAK,CAACO,IAAI,IAAI1E,KAAK,CAACiN,aAAa,KAAKpM,SAAS,GAC3Cb,KAAK,CAACiN,aAAa,GAClBjN,KAAK,CAAC+M,WACZ;QACDzO,KAAK,EAAEwK,iBAAkB;QACzBkE,IAAI,EAAE;MAAG,CACV,CAEU,CAAC,EAChBhP,KAAA,CAAA0C,aAAA,CAACrC,IAAI;QAAC4B,KAAK,EAAE,CAACuJ,MAAM,CAAC0D,cAAc,EAAEzB,UAAU;MAAE,GAC9C,OAAOtJ,KAAK,KAAK,SAAS,GACzBnE,KAAA,CAAA0C,aAAA,CAACvB,KAAK;QAACgO,OAAO,EAAEhL,KAAM;QAAC6K,IAAI,EAAEtI,IAAI,GAAG,CAAC,GAAG;MAAE,CAAE,CAAC,GAE7C1G,KAAA,CAAA0C,aAAA,CAACvB,KAAK;QAACgO,OAAO,EAAEhL,KAAK,IAAI,IAAK;QAAC6K,IAAI,EAAE;MAAG,GACrC7K,KACI,CAEL,CACO,CAAC,EACfmB,OAAO,GACNtF,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;QACZ4B,KAAK,EAAE,CACLuJ,MAAM,CAAC4D,cAAc,EACrB,CAAC1I,IAAI,IAAI;UAAEgF,SAAS,EAAE,CAAC;YAAElF,KAAA,EAAAA;UAAM,CAAC;QAAE,CAAC;MACnC,GAEFxG,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;QACZ4B,KAAK,EAAE,CACLuJ,MAAM,CAAC6D,YAAY,EACnB;UACEzC,OAAO,EAAEe,oBAAoB,GACzBZ,aAAa,GACbE;QACN,CAAC;MACD,GAED5J,WAAW,GACVA,WAAW,CAAC;QACVrB,KAAK,EAALA,KAAK;QACL6K,OAAO,EAAE,IAAI;QACbvM,KAAK,EAAE8M;MACT,CAAC,CAAC,GAEFpN,KAAA,CAAA0C,aAAA,CAACnB,IAAI;QACH+N,qBAAqB,EAAExJ,0BAA2B;QAClDyJ,OAAO,EAAC,aAAa;QACrBtN,KAAK,EAAE,CACLuJ,MAAM,CAACgE,KAAK,EAAAC,aAAA;UAEVnP,KAAK,EAAE8M;QAAgB,GACpBQ,IAAA;MAEL,GAED/J,YAAY,CAAC;QAAE7B,KAAA,EAAAA;MAAM,CAAC,CACnB,CAEK,CAAC,EACf2D,QAAQ,GAAG,IAAI,GACd3F,KAAA,CAAA0C,aAAA,CAACzC,QAAQ,CAACI,IAAI;QACZ4B,KAAK,EAAE,CACLuJ,MAAM,CAAC6D,YAAY,EACnB;UACEzC,OAAO,EAAEe,oBAAoB,GACzBX,eAAe,GACfE;QACN,CAAC;MACD,GAED7J,WAAW,GACVA,WAAW,CAAC;QACVrB,KAAK,EAALA,KAAK;QACL6K,OAAO,EAAE,KAAK;QACdvM,KAAK,EAAEkN;MACT,CAAC,CAAC,GAEFxN,KAAA,CAAA0C,aAAA,CAACnB,IAAI;QACH+N,qBAAqB,EAAExJ,0BAA2B;QAClDyJ,OAAO,EAAC,aAAa;QACrBG,UAAU,EAAE,KAAM;QAClBzN,KAAK,EAAE,CACLuJ,MAAM,CAACgE,KAAK,EAAAC,aAAA;UAEVnP,KAAK,EAAEkN;QAAkB,GACtBI,IAAA;MAEL,GAED/J,YAAY,CAAC;QAAE7B,KAAA,EAAAA;MAAM,CAAC,CACnB,CAEK,CAEJ,CAAC,GAEhB,CAAC0E,IAAI,IAAI1G,KAAA,CAAA0C,aAAA,CAACrC,IAAI;QAAC4B,KAAK,EAAEuJ,MAAM,CAAC4D;MAAe,CAAE,CAE5C;IAEV,CAAC,CAAC;EACJ,CAAC,CACG,CACO,CACR,CAAC;AAEd,CAAC;AAEDtM,mBAAmB,CAAC6M,WAAW,GAAG,sBAAsB;AAExD,eAAe7M,mBAAmB;AAElC,IAAM0I,MAAM,GAAGrL,UAAU,CAACyP,MAAM,CAAC;EAC/BnE,GAAG,EAAE;IACHnF,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRF,MAAM,EAAE;EACV,CAAC;EACD2F,UAAU,EAAE;IACV6D,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ,CAAC;EACD7D,KAAK,EAAAwD,aAAA;IACHM,aAAa,EAAE;EAAK,GAChB7P,QAAQ,CAACiF,EAAE,KAAK,KAAK,GACrB;IACEmG,KAAK,EAAE;EACT,CAAC,GACD,IAAI,CACT;EACD6C,IAAI,EAAE;IACJ6B,IAAI,EAAE,CAAC;IAGPC,eAAe,EAAE;EACnB,CAAC;EACD7B,MAAM,EAAE;IACN6B,eAAe,EAAE;EACnB,CAAC;EACDzD,MAAM,EAAE;IACNX,QAAQ,EAAE;EACZ,CAAC;EACD0C,aAAa,EAAE;IACb3C,MAAM,EAAE,EAAE;IACVN,KAAK,EAAE,EAAE;IACT4E,SAAS,EAAE,CAAC;IACZ/D,gBAAgB,EAAE,EAAE;IACpBgE,SAAS,EAAE;EACb,CAAC;EACD3B,eAAe,EAAE;IACf5C,MAAM,EAAE,EAAE;IACVN,KAAK,EAAE,EAAE;IACTY,YAAY,EAAE,CAAC;IACfgE,SAAS,EAAE,CAAC;IACZE,cAAc,EAAE;EAClB,CAAC;EACDxB,WAAW,EAAAa,aAAA,CAAAA,aAAA,KACNtP,UAAU,CAACkQ,kBAAkB;IAChCR,UAAU,EAAE;EAAA,EACb;EACDhB,aAAa,EAAE;IACbpC,GAAG,EAAE;EACP,CAAC;EACD2C,cAAc,EAAE;IACdxD,MAAM,EAAE,EAAE;IACV0E,aAAa,EAAE;EACjB,CAAC;EACDjB,YAAY,EAAAI,aAAA,KACPtP,UAAU,CAACkQ,kBAAA,CACf;EAEDb,KAAK,EAAAC,aAAA;IACHc,QAAQ,EAAE,EAAE;IACZ3E,MAAM,EAAEjK,UAAU;IAClB6O,SAAS,EAAE,QAAQ;IACnB1G,eAAe,EAAE;EAAa,GAC1B5J,QAAQ,CAACiF,EAAE,KAAK,KAAK,GACrB;IACEsL,UAAU,EAAE,QAAQ;IACpBN,SAAS,EAAE;EACb,CAAC,GACD,IAAI,CACT;EACDjB,cAAc,EAAE;IACdrD,QAAQ,EAAE,UAAU;IACpBvF,IAAI,EAAE;EACR,CAAC;EACD+H,oBAAoB,EAAE;IACpBqC,UAAU,EAAE,EAAE;IACdJ,aAAa,EAAE;EACjB,CAAC;EACDhC,kBAAkB,EAAE;IAClB1C,MAAM,EAAE,EAAE;IACVwE,cAAc,EAAE,QAAQ;IACxBP,UAAU,EAAE;EACd,CAAC;EACDpB,OAAO,EAAE;IACPnD,KAAK,EAAE1J,aAAa;IACpBgK,MAAM,EAAEhK,aAAa,GAAG,CAAC;IACzB+K,YAAY,EAAE/K,aAAa,GAAG,CAAC;IAC/BuO,SAAS,EAAE;EACb,CAAC;EACDnG,SAAS,EAAE;IACTA,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}