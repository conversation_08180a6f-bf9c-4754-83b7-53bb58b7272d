{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"state\", \"descriptors\", \"dimensions\", \"insets\", \"style\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { MissingIcon } from '@react-navigation/elements';\nimport { CommonActions, NavigationContext, NavigationRouteContext, useLinkBuilder, useTheme } from '@react-navigation/native';\nimport React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useSafeAreaFrame } from 'react-native-safe-area-context';\nimport BottomTabBarHeightCallbackContext from \"../utils/BottomTabBarHeightCallbackContext\";\nimport useIsKeyboardShown from \"../utils/useIsKeyboardShown\";\nimport BottomTabItem from \"./BottomTabItem\";\nvar DEFAULT_TABBAR_HEIGHT = 49;\nvar COMPACT_TABBAR_HEIGHT = 32;\nvar DEFAULT_MAX_TAB_ITEM_WIDTH = 125;\nvar useNativeDriver = Platform.OS !== 'web';\nvar shouldUseHorizontalLabels = function shouldUseHorizontalLabels(_ref) {\n  var state = _ref.state,\n    descriptors = _ref.descriptors,\n    layout = _ref.layout,\n    dimensions = _ref.dimensions;\n  var tabBarLabelPosition = descriptors[state.routes[state.index].key].options.tabBarLabelPosition;\n  if (tabBarLabelPosition) {\n    switch (tabBarLabelPosition) {\n      case 'beside-icon':\n        return true;\n      case 'below-icon':\n        return false;\n    }\n  }\n  if (layout.width >= 768) {\n    var maxTabWidth = state.routes.reduce(function (acc, route) {\n      var tabBarItemStyle = descriptors[route.key].options.tabBarItemStyle;\n      var flattenedStyle = StyleSheet.flatten(tabBarItemStyle);\n      if (flattenedStyle) {\n        if (typeof flattenedStyle.width === 'number') {\n          return acc + flattenedStyle.width;\n        } else if (typeof flattenedStyle.maxWidth === 'number') {\n          return acc + flattenedStyle.maxWidth;\n        }\n      }\n      return acc + DEFAULT_MAX_TAB_ITEM_WIDTH;\n    }, 0);\n    return maxTabWidth <= layout.width;\n  } else {\n    return dimensions.width > dimensions.height;\n  }\n};\nvar getPaddingBottom = function getPaddingBottom(insets) {\n  return Math.max(insets.bottom - Platform.select({\n    ios: 4,\n    default: 0\n  }), 0);\n};\nexport var getTabBarHeight = function getTabBarHeight(_ref2) {\n  var _StyleSheet$flatten;\n  var state = _ref2.state,\n    descriptors = _ref2.descriptors,\n    dimensions = _ref2.dimensions,\n    insets = _ref2.insets,\n    style = _ref2.style,\n    rest = _objectWithoutProperties(_ref2, _excluded);\n  var customHeight = (_StyleSheet$flatten = StyleSheet.flatten(style)) === null || _StyleSheet$flatten === void 0 ? void 0 : _StyleSheet$flatten.height;\n  if (typeof customHeight === 'number') {\n    return customHeight;\n  }\n  var isLandscape = dimensions.width > dimensions.height;\n  var horizontalLabels = shouldUseHorizontalLabels(_objectSpread({\n    state: state,\n    descriptors: descriptors,\n    dimensions: dimensions\n  }, rest));\n  var paddingBottom = getPaddingBottom(insets);\n  if (Platform.OS === 'ios' && !Platform.isPad && isLandscape && horizontalLabels) {\n    return COMPACT_TABBAR_HEIGHT + paddingBottom;\n  }\n  return DEFAULT_TABBAR_HEIGHT + paddingBottom;\n};\nexport default function BottomTabBar(_ref3) {\n  var state = _ref3.state,\n    navigation = _ref3.navigation,\n    descriptors = _ref3.descriptors,\n    insets = _ref3.insets,\n    style = _ref3.style;\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  var buildLink = useLinkBuilder();\n  var focusedRoute = state.routes[state.index];\n  var focusedDescriptor = descriptors[focusedRoute.key];\n  var focusedOptions = focusedDescriptor.options;\n  var tabBarShowLabel = focusedOptions.tabBarShowLabel,\n    _focusedOptions$tabBa = focusedOptions.tabBarHideOnKeyboard,\n    tabBarHideOnKeyboard = _focusedOptions$tabBa === void 0 ? false : _focusedOptions$tabBa,\n    tabBarVisibilityAnimationConfig = focusedOptions.tabBarVisibilityAnimationConfig,\n    tabBarStyle = focusedOptions.tabBarStyle,\n    tabBarBackground = focusedOptions.tabBarBackground,\n    tabBarActiveTintColor = focusedOptions.tabBarActiveTintColor,\n    tabBarInactiveTintColor = focusedOptions.tabBarInactiveTintColor,\n    tabBarActiveBackgroundColor = focusedOptions.tabBarActiveBackgroundColor,\n    tabBarInactiveBackgroundColor = focusedOptions.tabBarInactiveBackgroundColor;\n  var dimensions = useSafeAreaFrame();\n  var isKeyboardShown = useIsKeyboardShown();\n  var onHeightChange = React.useContext(BottomTabBarHeightCallbackContext);\n  var shouldShowTabBar = !(tabBarHideOnKeyboard && isKeyboardShown);\n  var visibilityAnimationConfigRef = React.useRef(tabBarVisibilityAnimationConfig);\n  React.useEffect(function () {\n    visibilityAnimationConfigRef.current = tabBarVisibilityAnimationConfig;\n  });\n  var _React$useState = React.useState(!shouldShowTabBar),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isTabBarHidden = _React$useState2[0],\n    setIsTabBarHidden = _React$useState2[1];\n  var _React$useState3 = React.useState(function () {\n      return new Animated.Value(shouldShowTabBar ? 1 : 0);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 1),\n    visible = _React$useState4[0];\n  React.useEffect(function () {\n    var visibilityAnimationConfig = visibilityAnimationConfigRef.current;\n    if (shouldShowTabBar) {\n      var _visibilityAnimationC, _visibilityAnimationC2;\n      var animation = (visibilityAnimationConfig === null || visibilityAnimationConfig === void 0 ? void 0 : (_visibilityAnimationC = visibilityAnimationConfig.show) === null || _visibilityAnimationC === void 0 ? void 0 : _visibilityAnimationC.animation) === 'spring' ? Animated.spring : Animated.timing;\n      animation(visible, _objectSpread({\n        toValue: 1,\n        useNativeDriver: useNativeDriver,\n        duration: 250\n      }, visibilityAnimationConfig === null || visibilityAnimationConfig === void 0 ? void 0 : (_visibilityAnimationC2 = visibilityAnimationConfig.show) === null || _visibilityAnimationC2 === void 0 ? void 0 : _visibilityAnimationC2.config)).start(function (_ref4) {\n        var finished = _ref4.finished;\n        if (finished) {\n          setIsTabBarHidden(false);\n        }\n      });\n    } else {\n      var _visibilityAnimationC3, _visibilityAnimationC4;\n      setIsTabBarHidden(true);\n      var _animation = (visibilityAnimationConfig === null || visibilityAnimationConfig === void 0 ? void 0 : (_visibilityAnimationC3 = visibilityAnimationConfig.hide) === null || _visibilityAnimationC3 === void 0 ? void 0 : _visibilityAnimationC3.animation) === 'spring' ? Animated.spring : Animated.timing;\n      _animation(visible, _objectSpread({\n        toValue: 0,\n        useNativeDriver: useNativeDriver,\n        duration: 200\n      }, visibilityAnimationConfig === null || visibilityAnimationConfig === void 0 ? void 0 : (_visibilityAnimationC4 = visibilityAnimationConfig.hide) === null || _visibilityAnimationC4 === void 0 ? void 0 : _visibilityAnimationC4.config)).start();\n    }\n    return function () {\n      return visible.stopAnimation();\n    };\n  }, [visible, shouldShowTabBar]);\n  var _React$useState5 = React.useState({\n      height: 0,\n      width: dimensions.width\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    layout = _React$useState6[0],\n    setLayout = _React$useState6[1];\n  var handleLayout = function handleLayout(e) {\n    var _e$nativeEvent$layout = e.nativeEvent.layout,\n      height = _e$nativeEvent$layout.height,\n      width = _e$nativeEvent$layout.width;\n    onHeightChange === null || onHeightChange === void 0 ? void 0 : onHeightChange(height);\n    setLayout(function (layout) {\n      if (height === layout.height && width === layout.width) {\n        return layout;\n      } else {\n        return {\n          height: height,\n          width: width\n        };\n      }\n    });\n  };\n  var routes = state.routes;\n  var paddingBottom = getPaddingBottom(insets);\n  var tabBarHeight = getTabBarHeight({\n    state: state,\n    descriptors: descriptors,\n    insets: insets,\n    dimensions: dimensions,\n    layout: layout,\n    style: [tabBarStyle, style]\n  });\n  var hasHorizontalLabels = shouldUseHorizontalLabels({\n    state: state,\n    descriptors: descriptors,\n    dimensions: dimensions,\n    layout: layout\n  });\n  var tabBarBackgroundElement = tabBarBackground === null || tabBarBackground === void 0 ? void 0 : tabBarBackground();\n  return React.createElement(Animated.View, {\n    style: [styles.tabBar, {\n      backgroundColor: tabBarBackgroundElement != null ? 'transparent' : colors.card,\n      borderTopColor: colors.border\n    }, {\n      transform: [{\n        translateY: visible.interpolate({\n          inputRange: [0, 1],\n          outputRange: [layout.height + paddingBottom + StyleSheet.hairlineWidth, 0]\n        })\n      }],\n      position: isTabBarHidden ? 'absolute' : null\n    }, {\n      height: tabBarHeight,\n      paddingBottom: paddingBottom,\n      paddingHorizontal: Math.max(insets.left, insets.right)\n    }, tabBarStyle],\n    pointerEvents: isTabBarHidden ? 'none' : 'auto',\n    onLayout: handleLayout\n  }, React.createElement(View, {\n    pointerEvents: \"none\",\n    style: StyleSheet.absoluteFill\n  }, tabBarBackgroundElement), React.createElement(View, {\n    accessibilityRole: \"tablist\",\n    style: styles.content\n  }, routes.map(function (route, index) {\n    var _options$tabBarIcon;\n    var focused = index === state.index;\n    var options = descriptors[route.key].options;\n    var onPress = function onPress() {\n      var event = navigation.emit({\n        type: 'tabPress',\n        target: route.key,\n        canPreventDefault: true\n      });\n      if (!focused && !event.defaultPrevented) {\n        navigation.dispatch(_objectSpread(_objectSpread({}, CommonActions.navigate({\n          name: route.name,\n          merge: true\n        })), {}, {\n          target: state.key\n        }));\n      }\n    };\n    var onLongPress = function onLongPress() {\n      navigation.emit({\n        type: 'tabLongPress',\n        target: route.key\n      });\n    };\n    var label = options.tabBarLabel !== undefined ? options.tabBarLabel : options.title !== undefined ? options.title : route.name;\n    var accessibilityLabel = options.tabBarAccessibilityLabel !== undefined ? options.tabBarAccessibilityLabel : typeof label === 'string' && Platform.OS === 'ios' ? `${label}, tab, ${index + 1} of ${routes.length}` : undefined;\n    return React.createElement(NavigationContext.Provider, {\n      key: route.key,\n      value: descriptors[route.key].navigation\n    }, React.createElement(NavigationRouteContext.Provider, {\n      value: route\n    }, React.createElement(BottomTabItem, {\n      route: route,\n      descriptor: descriptors[route.key],\n      focused: focused,\n      horizontal: hasHorizontalLabels,\n      onPress: onPress,\n      onLongPress: onLongPress,\n      accessibilityLabel: accessibilityLabel,\n      to: buildLink(route.name, route.params),\n      testID: options.tabBarTestID,\n      allowFontScaling: options.tabBarAllowFontScaling,\n      activeTintColor: tabBarActiveTintColor,\n      inactiveTintColor: tabBarInactiveTintColor,\n      activeBackgroundColor: tabBarActiveBackgroundColor,\n      inactiveBackgroundColor: tabBarInactiveBackgroundColor,\n      button: options.tabBarButton,\n      icon: (_options$tabBarIcon = options.tabBarIcon) != null ? _options$tabBarIcon : function (_ref5) {\n        var color = _ref5.color,\n          size = _ref5.size;\n        return React.createElement(MissingIcon, {\n          color: color,\n          size: size\n        });\n      },\n      badge: options.tabBarBadge,\n      badgeStyle: options.tabBarBadgeStyle,\n      label: label,\n      showLabel: tabBarShowLabel,\n      labelStyle: options.tabBarLabelStyle,\n      iconStyle: options.tabBarIconStyle,\n      style: options.tabBarItemStyle\n    })));\n  })));\n}\nvar styles = StyleSheet.create({\n  tabBar: {\n    left: 0,\n    right: 0,\n    bottom: 0,\n    borderTopWidth: StyleSheet.hairlineWidth,\n    elevation: 8\n  },\n  content: {\n    flex: 1,\n    flexDirection: 'row'\n  }\n});", "map": {"version": 3, "names": ["MissingIcon", "CommonActions", "NavigationContext", "NavigationRouteContext", "useLinkBuilder", "useTheme", "React", "Animated", "Platform", "StyleSheet", "View", "useSafeAreaFrame", "BottomTabBarHeightCallbackContext", "useIsKeyboardShown", "BottomTabItem", "DEFAULT_TABBAR_HEIGHT", "COMPACT_TABBAR_HEIGHT", "DEFAULT_MAX_TAB_ITEM_WIDTH", "useNativeDriver", "OS", "shouldUseHorizontalLabels", "_ref", "state", "descriptors", "layout", "dimensions", "tabBarLabelPosition", "routes", "index", "key", "options", "width", "max<PERSON>ab<PERSON><PERSON><PERSON>", "reduce", "acc", "route", "tabBarItemStyle", "flattenedStyle", "flatten", "max<PERSON><PERSON><PERSON>", "height", "getPaddingBottom", "insets", "Math", "max", "bottom", "select", "ios", "default", "getTabBarHeight", "_ref2", "_StyleSheet$flatten", "style", "rest", "_objectWithoutProperties", "_excluded", "customHeight", "isLandscape", "horizontalLabels", "_objectSpread", "paddingBottom", "isPad", "BottomTabBar", "_ref3", "navigation", "_useTheme", "colors", "buildLink", "focusedRoute", "focusedDescriptor", "focusedOptions", "tabBarShowLabel", "_focusedOptions$tabBa", "tabBarHideOnKeyboard", "tabBarVisibilityAnimationConfig", "tabBarStyle", "tabBarBackground", "tabBarActiveTintColor", "tabBarInactiveTintColor", "tabBarActiveBackgroundColor", "tabBarInactiveBackgroundColor", "isKeyboardShown", "onHeightChange", "useContext", "shouldShowTabBar", "visibilityAnimationConfigRef", "useRef", "useEffect", "current", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "isTabBarHidden", "setIsTabBarHidden", "_React$useState3", "Value", "_React$useState4", "visible", "visibilityAnimationConfig", "_visibilityAnimationC", "_visibilityAnimationC2", "animation", "show", "spring", "timing", "toValue", "duration", "config", "start", "_ref4", "finished", "_visibilityAnimationC3", "_visibilityAnimationC4", "hide", "stopAnimation", "_React$useState5", "_React$useState6", "setLayout", "handleLayout", "e", "_e$nativeEvent$layout", "nativeEvent", "tabBarHeight", "hasHorizontalLabels", "tabBarBackgroundElement", "createElement", "styles", "tabBar", "backgroundColor", "card", "borderTopColor", "border", "transform", "translateY", "interpolate", "inputRange", "outputRange", "hairlineWidth", "position", "paddingHorizontal", "left", "right", "pointerEvents", "onLayout", "absoluteFill", "accessibilityRole", "content", "map", "_options$tabBarIcon", "focused", "onPress", "event", "emit", "type", "target", "canPreventDefault", "defaultPrevented", "dispatch", "navigate", "name", "merge", "onLongPress", "label", "tabBarLabel", "undefined", "title", "accessibilityLabel", "tabBarAccessibilityLabel", "length", "Provider", "value", "descriptor", "horizontal", "to", "params", "testID", "tabBarTestID", "allowFontScaling", "tabBarAllowFontScaling", "activeTintColor", "inactiveTintColor", "activeBackgroundColor", "inactiveBackgroundColor", "button", "tabBarButton", "icon", "tabBarIcon", "_ref5", "color", "size", "badge", "tabBarBadge", "badgeStyle", "tabBarBadgeStyle", "showLabel", "labelStyle", "tabBarLabelStyle", "iconStyle", "tabBarIconStyle", "create", "borderTopWidth", "elevation", "flex", "flexDirection"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/bottom-tabs/src/views/BottomTabBar.tsx"], "sourcesContent": ["import { MissingIcon } from '@react-navigation/elements';\nimport {\n  CommonActions,\n  NavigationContext,\n  NavigationRouteContext,\n  ParamListBase,\n  TabNavigationState,\n  useLinkBuilder,\n  useTheme,\n} from '@react-navigation/native';\nimport React from 'react';\nimport {\n  Animated,\n  LayoutChangeEvent,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\nimport { EdgeInsets, useSafeAreaFrame } from 'react-native-safe-area-context';\n\nimport type { BottomTabBarProps, BottomTabDescriptorMap } from '../types';\nimport BottomTabBarHeightCallbackContext from '../utils/BottomTabBarHeightCallbackContext';\nimport useIsKeyboardShown from '../utils/useIsKeyboardShown';\nimport BottomTabItem from './BottomTabItem';\n\ntype Props = BottomTabBarProps & {\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n};\n\nconst DEFAULT_TABBAR_HEIGHT = 49;\nconst COMPACT_TABBAR_HEIGHT = 32;\nconst DEFAULT_MAX_TAB_ITEM_WIDTH = 125;\n\nconst useNativeDriver = Platform.OS !== 'web';\n\ntype Options = {\n  state: TabNavigationState<ParamListBase>;\n  descriptors: BottomTabDescriptorMap;\n  layout: { height: number; width: number };\n  dimensions: { height: number; width: number };\n};\n\nconst shouldUseHorizontalLabels = ({\n  state,\n  descriptors,\n  layout,\n  dimensions,\n}: Options) => {\n  const { tabBarLabelPosition } =\n    descriptors[state.routes[state.index].key].options;\n\n  if (tabBarLabelPosition) {\n    switch (tabBarLabelPosition) {\n      case 'beside-icon':\n        return true;\n      case 'below-icon':\n        return false;\n    }\n  }\n\n  if (layout.width >= 768) {\n    // Screen size matches a tablet\n    const maxTabWidth = state.routes.reduce((acc, route) => {\n      const { tabBarItemStyle } = descriptors[route.key].options;\n      const flattenedStyle = StyleSheet.flatten(tabBarItemStyle);\n\n      if (flattenedStyle) {\n        if (typeof flattenedStyle.width === 'number') {\n          return acc + flattenedStyle.width;\n        } else if (typeof flattenedStyle.maxWidth === 'number') {\n          return acc + flattenedStyle.maxWidth;\n        }\n      }\n\n      return acc + DEFAULT_MAX_TAB_ITEM_WIDTH;\n    }, 0);\n\n    return maxTabWidth <= layout.width;\n  } else {\n    return dimensions.width > dimensions.height;\n  }\n};\n\nconst getPaddingBottom = (insets: EdgeInsets) =>\n  Math.max(insets.bottom - Platform.select({ ios: 4, default: 0 }), 0);\n\nexport const getTabBarHeight = ({\n  state,\n  descriptors,\n  dimensions,\n  insets,\n  style,\n  ...rest\n}: Options & {\n  insets: EdgeInsets;\n  style: Animated.WithAnimatedValue<StyleProp<ViewStyle>> | undefined;\n}) => {\n  // @ts-ignore\n  const customHeight = StyleSheet.flatten(style)?.height;\n\n  if (typeof customHeight === 'number') {\n    return customHeight;\n  }\n\n  const isLandscape = dimensions.width > dimensions.height;\n  const horizontalLabels = shouldUseHorizontalLabels({\n    state,\n    descriptors,\n    dimensions,\n    ...rest,\n  });\n  const paddingBottom = getPaddingBottom(insets);\n\n  if (\n    Platform.OS === 'ios' &&\n    !Platform.isPad &&\n    isLandscape &&\n    horizontalLabels\n  ) {\n    return COMPACT_TABBAR_HEIGHT + paddingBottom;\n  }\n\n  return DEFAULT_TABBAR_HEIGHT + paddingBottom;\n};\n\nexport default function BottomTabBar({\n  state,\n  navigation,\n  descriptors,\n  insets,\n  style,\n}: Props) {\n  const { colors } = useTheme();\n  const buildLink = useLinkBuilder();\n\n  const focusedRoute = state.routes[state.index];\n  const focusedDescriptor = descriptors[focusedRoute.key];\n  const focusedOptions = focusedDescriptor.options;\n\n  const {\n    tabBarShowLabel,\n    tabBarHideOnKeyboard = false,\n    tabBarVisibilityAnimationConfig,\n    tabBarStyle,\n    tabBarBackground,\n    tabBarActiveTintColor,\n    tabBarInactiveTintColor,\n    tabBarActiveBackgroundColor,\n    tabBarInactiveBackgroundColor,\n  } = focusedOptions;\n\n  const dimensions = useSafeAreaFrame();\n  const isKeyboardShown = useIsKeyboardShown();\n\n  const onHeightChange = React.useContext(BottomTabBarHeightCallbackContext);\n\n  const shouldShowTabBar = !(tabBarHideOnKeyboard && isKeyboardShown);\n\n  const visibilityAnimationConfigRef = React.useRef(\n    tabBarVisibilityAnimationConfig\n  );\n\n  React.useEffect(() => {\n    visibilityAnimationConfigRef.current = tabBarVisibilityAnimationConfig;\n  });\n\n  const [isTabBarHidden, setIsTabBarHidden] = React.useState(!shouldShowTabBar);\n\n  const [visible] = React.useState(\n    () => new Animated.Value(shouldShowTabBar ? 1 : 0)\n  );\n\n  React.useEffect(() => {\n    const visibilityAnimationConfig = visibilityAnimationConfigRef.current;\n\n    if (shouldShowTabBar) {\n      const animation =\n        visibilityAnimationConfig?.show?.animation === 'spring'\n          ? Animated.spring\n          : Animated.timing;\n\n      animation(visible, {\n        toValue: 1,\n        useNativeDriver,\n        duration: 250,\n        ...visibilityAnimationConfig?.show?.config,\n      }).start(({ finished }) => {\n        if (finished) {\n          setIsTabBarHidden(false);\n        }\n      });\n    } else {\n      setIsTabBarHidden(true);\n\n      const animation =\n        visibilityAnimationConfig?.hide?.animation === 'spring'\n          ? Animated.spring\n          : Animated.timing;\n\n      animation(visible, {\n        toValue: 0,\n        useNativeDriver,\n        duration: 200,\n        ...visibilityAnimationConfig?.hide?.config,\n      }).start();\n    }\n\n    return () => visible.stopAnimation();\n  }, [visible, shouldShowTabBar]);\n\n  const [layout, setLayout] = React.useState({\n    height: 0,\n    width: dimensions.width,\n  });\n\n  const handleLayout = (e: LayoutChangeEvent) => {\n    const { height, width } = e.nativeEvent.layout;\n\n    onHeightChange?.(height);\n\n    setLayout((layout) => {\n      if (height === layout.height && width === layout.width) {\n        return layout;\n      } else {\n        return {\n          height,\n          width,\n        };\n      }\n    });\n  };\n\n  const { routes } = state;\n\n  const paddingBottom = getPaddingBottom(insets);\n  const tabBarHeight = getTabBarHeight({\n    state,\n    descriptors,\n    insets,\n    dimensions,\n    layout,\n    style: [tabBarStyle, style],\n  });\n\n  const hasHorizontalLabels = shouldUseHorizontalLabels({\n    state,\n    descriptors,\n    dimensions,\n    layout,\n  });\n\n  const tabBarBackgroundElement = tabBarBackground?.();\n\n  return (\n    <Animated.View\n      style={[\n        styles.tabBar,\n        {\n          backgroundColor:\n            tabBarBackgroundElement != null ? 'transparent' : colors.card,\n          borderTopColor: colors.border,\n        },\n        {\n          transform: [\n            {\n              translateY: visible.interpolate({\n                inputRange: [0, 1],\n                outputRange: [\n                  layout.height + paddingBottom + StyleSheet.hairlineWidth,\n                  0,\n                ],\n              }),\n            },\n          ],\n          // Absolutely position the tab bar so that the content is below it\n          // This is needed to avoid gap at bottom when the tab bar is hidden\n          position: isTabBarHidden ? 'absolute' : (null as any),\n        },\n        {\n          height: tabBarHeight,\n          paddingBottom,\n          paddingHorizontal: Math.max(insets.left, insets.right),\n        },\n        tabBarStyle,\n      ]}\n      pointerEvents={isTabBarHidden ? 'none' : 'auto'}\n      onLayout={handleLayout}\n    >\n      <View pointerEvents=\"none\" style={StyleSheet.absoluteFill}>\n        {tabBarBackgroundElement}\n      </View>\n      <View accessibilityRole=\"tablist\" style={styles.content}>\n        {routes.map((route, index) => {\n          const focused = index === state.index;\n          const { options } = descriptors[route.key];\n\n          const onPress = () => {\n            const event = navigation.emit({\n              type: 'tabPress',\n              target: route.key,\n              canPreventDefault: true,\n            });\n\n            if (!focused && !event.defaultPrevented) {\n              navigation.dispatch({\n                ...CommonActions.navigate({ name: route.name, merge: true }),\n                target: state.key,\n              });\n            }\n          };\n\n          const onLongPress = () => {\n            navigation.emit({\n              type: 'tabLongPress',\n              target: route.key,\n            });\n          };\n\n          const label =\n            options.tabBarLabel !== undefined\n              ? options.tabBarLabel\n              : options.title !== undefined\n              ? options.title\n              : route.name;\n\n          const accessibilityLabel =\n            options.tabBarAccessibilityLabel !== undefined\n              ? options.tabBarAccessibilityLabel\n              : typeof label === 'string' && Platform.OS === 'ios'\n              ? `${label}, tab, ${index + 1} of ${routes.length}`\n              : undefined;\n\n          return (\n            <NavigationContext.Provider\n              key={route.key}\n              value={descriptors[route.key].navigation}\n            >\n              <NavigationRouteContext.Provider value={route}>\n                <BottomTabItem\n                  route={route}\n                  descriptor={descriptors[route.key]}\n                  focused={focused}\n                  horizontal={hasHorizontalLabels}\n                  onPress={onPress}\n                  onLongPress={onLongPress}\n                  accessibilityLabel={accessibilityLabel}\n                  to={buildLink(route.name, route.params)}\n                  testID={options.tabBarTestID}\n                  allowFontScaling={options.tabBarAllowFontScaling}\n                  activeTintColor={tabBarActiveTintColor}\n                  inactiveTintColor={tabBarInactiveTintColor}\n                  activeBackgroundColor={tabBarActiveBackgroundColor}\n                  inactiveBackgroundColor={tabBarInactiveBackgroundColor}\n                  button={options.tabBarButton}\n                  icon={\n                    options.tabBarIcon ??\n                    (({ color, size }) => (\n                      <MissingIcon color={color} size={size} />\n                    ))\n                  }\n                  badge={options.tabBarBadge}\n                  badgeStyle={options.tabBarBadgeStyle}\n                  label={label}\n                  showLabel={tabBarShowLabel}\n                  labelStyle={options.tabBarLabelStyle}\n                  iconStyle={options.tabBarIconStyle}\n                  style={options.tabBarItemStyle}\n                />\n              </NavigationRouteContext.Provider>\n            </NavigationContext.Provider>\n          );\n        })}\n      </View>\n    </Animated.View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  tabBar: {\n    left: 0,\n    right: 0,\n    bottom: 0,\n    borderTopWidth: StyleSheet.hairlineWidth,\n    elevation: 8,\n  },\n  content: {\n    flex: 1,\n    flexDirection: 'row',\n  },\n});\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,QAAQ,4BAA4B;AACxD,SACEC,aAAa,EACbC,iBAAiB,EACjBC,sBAAsB,EAGtBC,cAAc,EACdC,QAAQ,QACH,0BAA0B;AACjC,OAAOC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAUzB,SAAqBC,gBAAgB,QAAQ,gCAAgC;AAG7E,OAAOC,iCAAiC;AACxC,OAAOC,kBAAkB;AACzB,OAAOC,aAAa;AAMpB,IAAMC,qBAAqB,GAAG,EAAE;AAChC,IAAMC,qBAAqB,GAAG,EAAE;AAChC,IAAMC,0BAA0B,GAAG,GAAG;AAEtC,IAAMC,eAAe,GAAGV,QAAQ,CAACW,EAAE,KAAK,KAAK;AAS7C,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAGC,IAAA,EAKnB;EAAA,IAJbC,KAAK,GAIGD,IAAA,CAJRC,KAAK;IACLC,WAAW,GAGHF,IAAA,CAHRE,WAAW;IACXC,MAAM,GAEEH,IAAA,CAFRG,MAAM;IACNC,UAAA,GACQJ,IAAA,CADRI,UAAA;EAEA,IAAQC,mBAAA,GACNH,WAAW,CAACD,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,CAD5CJ,mBAAA;EAGR,IAAIA,mBAAmB,EAAE;IACvB,QAAQA,mBAAmB;MACzB,KAAK,aAAa;QAChB,OAAO,IAAI;MACb,KAAK,YAAY;QACf,OAAO,KAAK;IAAC;EAEnB;EAEA,IAAIF,MAAM,CAACO,KAAK,IAAI,GAAG,EAAE;IAEvB,IAAMC,WAAW,GAAGV,KAAK,CAACK,MAAM,CAACM,MAAM,CAAC,UAACC,GAAG,EAAEC,KAAK,EAAK;MACtD,IAAQC,eAAA,GAAoBb,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC,CAACC,OAAO,CAAlDM,eAAA;MACR,IAAMC,cAAc,GAAG5B,UAAU,CAAC6B,OAAO,CAACF,eAAe,CAAC;MAE1D,IAAIC,cAAc,EAAE;QAClB,IAAI,OAAOA,cAAc,CAACN,KAAK,KAAK,QAAQ,EAAE;UAC5C,OAAOG,GAAG,GAAGG,cAAc,CAACN,KAAK;QACnC,CAAC,MAAM,IAAI,OAAOM,cAAc,CAACE,QAAQ,KAAK,QAAQ,EAAE;UACtD,OAAOL,GAAG,GAAGG,cAAc,CAACE,QAAQ;QACtC;MACF;MAEA,OAAOL,GAAG,GAAGjB,0BAA0B;IACzC,CAAC,EAAE,CAAC,CAAC;IAEL,OAAOe,WAAW,IAAIR,MAAM,CAACO,KAAK;EACpC,CAAC,MAAM;IACL,OAAON,UAAU,CAACM,KAAK,GAAGN,UAAU,CAACe,MAAM;EAC7C;AACF,CAAC;AAED,IAAMC,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,MAAkB;EAAA,OAC1CC,IAAI,CAACC,GAAG,CAACF,MAAM,CAACG,MAAM,GAAGrC,QAAQ,CAACsC,MAAM,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAAA;AAEtE,OAAO,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,KAAA,EAUzB;EAAA,IAAAC,mBAAA;EAAA,IATJ7B,KAAK,GASN4B,KAAA,CATC5B,KAAK;IACLC,WAAW,GAQZ2B,KAAA,CARC3B,WAAW;IACXE,UAAU,GAOXyB,KAAA,CAPCzB,UAAU;IACViB,MAAM,GAMPQ,KAAA,CANCR,MAAM;IACNU,KAAK,GAKNF,KAAA,CALCE,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAIJJ,KAAA,EAAAK,SAAA;EAEC,IAAMC,YAAY,IAAAL,mBAAA,GAAG1C,UAAU,CAAC6B,OAAO,CAACc,KAAK,CAAC,cAAAD,mBAAA,uBAAzBA,mBAAA,CAA2BX,MAAM;EAEtD,IAAI,OAAOgB,YAAY,KAAK,QAAQ,EAAE;IACpC,OAAOA,YAAY;EACrB;EAEA,IAAMC,WAAW,GAAGhC,UAAU,CAACM,KAAK,GAAGN,UAAU,CAACe,MAAM;EACxD,IAAMkB,gBAAgB,GAAGtC,yBAAyB,CAAAuC,aAAA;IAChDrC,KAAK,EAALA,KAAK;IACLC,WAAW,EAAXA,WAAW;IACXE,UAAU,EAAVA;EAAU,GACP4B,IAAA,CACJ,CAAC;EACF,IAAMO,aAAa,GAAGnB,gBAAgB,CAACC,MAAM,CAAC;EAE9C,IACElC,QAAQ,CAACW,EAAE,KAAK,KAAK,IACrB,CAACX,QAAQ,CAACqD,KAAK,IACfJ,WAAW,IACXC,gBAAgB,EAChB;IACA,OAAO1C,qBAAqB,GAAG4C,aAAa;EAC9C;EAEA,OAAO7C,qBAAqB,GAAG6C,aAAa;AAC9C,CAAC;AAED,eAAe,SAASE,YAAYA,CAAAC,KAAA,EAM1B;EAAA,IALRzC,KAAK,GAKCyC,KAAA,CALNzC,KAAK;IACL0C,UAAU,GAIJD,KAAA,CAJNC,UAAU;IACVzC,WAAW,GAGLwC,KAAA,CAHNxC,WAAW;IACXmB,MAAM,GAEAqB,KAAA,CAFNrB,MAAM;IACNU,KAAA,GACMW,KAAA,CADNX,KAAA;EAEA,IAAAa,SAAA,GAAmB5D,QAAQ,EAAE;IAArB6D,MAAA,GAAAD,SAAA,CAAAC,MAAA;EACR,IAAMC,SAAS,GAAG/D,cAAc,EAAE;EAElC,IAAMgE,YAAY,GAAG9C,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC;EAC9C,IAAMyC,iBAAiB,GAAG9C,WAAW,CAAC6C,YAAY,CAACvC,GAAG,CAAC;EACvD,IAAMyC,cAAc,GAAGD,iBAAiB,CAACvC,OAAO;EAEhD,IACEyC,eAAe,GASbD,cAAc,CAThBC,eAAe;IAAAC,qBAAA,GASbF,cAAc,CARhBG,oBAAoB;IAApBA,oBAAoB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IAC5BE,+BAA+B,GAO7BJ,cAAc,CAPhBI,+BAA+B;IAC/BC,WAAW,GAMTL,cAAc,CANhBK,WAAW;IACXC,gBAAgB,GAKdN,cAAc,CALhBM,gBAAgB;IAChBC,qBAAqB,GAInBP,cAAc,CAJhBO,qBAAqB;IACrBC,uBAAuB,GAGrBR,cAAc,CAHhBQ,uBAAuB;IACvBC,2BAA2B,GAEzBT,cAAc,CAFhBS,2BAA2B;IAC3BC,6BAAA,GACEV,cAAc,CADhBU,6BAAA;EAGF,IAAMvD,UAAU,GAAGd,gBAAgB,EAAE;EACrC,IAAMsE,eAAe,GAAGpE,kBAAkB,EAAE;EAE5C,IAAMqE,cAAc,GAAG5E,KAAK,CAAC6E,UAAU,CAACvE,iCAAiC,CAAC;EAE1E,IAAMwE,gBAAgB,GAAG,EAAEX,oBAAoB,IAAIQ,eAAe,CAAC;EAEnE,IAAMI,4BAA4B,GAAG/E,KAAK,CAACgF,MAAM,CAC/CZ,+BAA+B,CAChC;EAEDpE,KAAK,CAACiF,SAAS,CAAC,YAAM;IACpBF,4BAA4B,CAACG,OAAO,GAAGd,+BAA+B;EACxE,CAAC,CAAC;EAEF,IAAAe,eAAA,GAA4CnF,KAAK,CAACoF,QAAQ,CAAC,CAACN,gBAAgB,CAAC;IAAAO,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAtEI,cAAc,GAAAF,gBAAA;IAAEG,iBAAiB,GAAAH,gBAAA;EAExC,IAAAI,gBAAA,GAAkBzF,KAAK,CAACoF,QAAQ,CAC9B;MAAA,OAAM,IAAInF,QAAQ,CAACyF,KAAK,CAACZ,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;IAAA,EACnD;IAAAa,gBAAA,GAAAL,cAAA,CAAAG,gBAAA;IAFMG,OAAO,GAAAD,gBAAA;EAId3F,KAAK,CAACiF,SAAS,CAAC,YAAM;IACpB,IAAMY,yBAAyB,GAAGd,4BAA4B,CAACG,OAAO;IAEtE,IAAIJ,gBAAgB,EAAE;MAAA,IAAAgB,qBAAA,EAAAC,sBAAA;MACpB,IAAMC,SAAS,GACb,CAAAH,yBAAyB,aAAzBA,yBAAyB,wBAAAC,qBAAA,GAAzBD,yBAAyB,CAAEI,IAAI,cAAAH,qBAAA,uBAA/BA,qBAAA,CAAiCE,SAAS,MAAK,QAAQ,GACnD/F,QAAQ,CAACiG,MAAM,GACfjG,QAAQ,CAACkG,MAAM;MAErBH,SAAS,CAACJ,OAAO,EAAAvC,aAAA;QACf+C,OAAO,EAAE,CAAC;QACVxF,eAAe,EAAfA,eAAe;QACfyF,QAAQ,EAAE;MAAG,GACVR,yBAAyB,aAAzBA,yBAAyB,wBAAAE,sBAAA,GAAzBF,yBAAyB,CAAEI,IAAI,cAAAF,sBAAA,uBAA/BA,sBAAA,CAAiCO,MAAM,CAC3C,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAA,EAAkB;QAAA,IAAfC,QAAA,GAAUD,KAAA,CAAVC,QAAA;QACV,IAAIA,QAAQ,EAAE;UACZjB,iBAAiB,CAAC,KAAK,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MAAA,IAAAkB,sBAAA,EAAAC,sBAAA;MACLnB,iBAAiB,CAAC,IAAI,CAAC;MAEvB,IAAMQ,UAAS,GACb,CAAAH,yBAAyB,aAAzBA,yBAAyB,wBAAAa,sBAAA,GAAzBb,yBAAyB,CAAEe,IAAI,cAAAF,sBAAA,uBAA/BA,sBAAA,CAAiCV,SAAS,MAAK,QAAQ,GACnD/F,QAAQ,CAACiG,MAAM,GACfjG,QAAQ,CAACkG,MAAM;MAErBH,UAAS,CAACJ,OAAO,EAAAvC,aAAA;QACf+C,OAAO,EAAE,CAAC;QACVxF,eAAe,EAAfA,eAAe;QACfyF,QAAQ,EAAE;MAAG,GACVR,yBAAyB,aAAzBA,yBAAyB,wBAAAc,sBAAA,GAAzBd,yBAAyB,CAAEe,IAAI,cAAAD,sBAAA,uBAA/BA,sBAAA,CAAiCL,MAAM,CAC3C,CAAC,CAACC,KAAK,EAAE;IACZ;IAEA,OAAO;MAAA,OAAMX,OAAO,CAACiB,aAAa,EAAE;IAAA;EACtC,CAAC,EAAE,CAACjB,OAAO,EAAEd,gBAAgB,CAAC,CAAC;EAE/B,IAAAgC,gBAAA,GAA4B9G,KAAK,CAACoF,QAAQ,CAAC;MACzClD,MAAM,EAAE,CAAC;MACTT,KAAK,EAAEN,UAAU,CAACM;IACpB,CAAC,CAAC;IAAAsF,gBAAA,GAAAzB,cAAA,CAAAwB,gBAAA;IAHK5F,MAAM,GAAA6F,gBAAA;IAAEC,SAAS,GAAAD,gBAAA;EAKxB,IAAME,YAAY,GAAI,SAAhBA,YAAYA,CAAIC,CAAoB,EAAK;IAC7C,IAAAC,qBAAA,GAA0BD,CAAC,CAACE,WAAW,CAAClG,MAAM;MAAtCgB,MAAM,GAAAiF,qBAAA,CAANjF,MAAM;MAAET,KAAA,GAAA0F,qBAAA,CAAA1F,KAAA;IAEhBmD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG1C,MAAM,CAAC;IAExB8E,SAAS,CAAE,UAAA9F,MAAM,EAAK;MACpB,IAAIgB,MAAM,KAAKhB,MAAM,CAACgB,MAAM,IAAIT,KAAK,KAAKP,MAAM,CAACO,KAAK,EAAE;QACtD,OAAOP,MAAM;MACf,CAAC,MAAM;QACL,OAAO;UACLgB,MAAM,EAANA,MAAM;UACNT,KAAA,EAAAA;QACF,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAQJ,MAAA,GAAWL,KAAK,CAAhBK,MAAA;EAER,IAAMiC,aAAa,GAAGnB,gBAAgB,CAACC,MAAM,CAAC;EAC9C,IAAMiF,YAAY,GAAG1E,eAAe,CAAC;IACnC3B,KAAK,EAALA,KAAK;IACLC,WAAW,EAAXA,WAAW;IACXmB,MAAM,EAANA,MAAM;IACNjB,UAAU,EAAVA,UAAU;IACVD,MAAM,EAANA,MAAM;IACN4B,KAAK,EAAE,CAACuB,WAAW,EAAEvB,KAAK;EAC5B,CAAC,CAAC;EAEF,IAAMwE,mBAAmB,GAAGxG,yBAAyB,CAAC;IACpDE,KAAK,EAALA,KAAK;IACLC,WAAW,EAAXA,WAAW;IACXE,UAAU,EAAVA,UAAU;IACVD,MAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAMqG,uBAAuB,GAAGjD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,EAAI;EAEpD,OACEtE,KAAA,CAAAwH,aAAA,CAACvH,QAAQ,CAACG,IAAI;IACZ0C,KAAK,EAAE,CACL2E,MAAM,CAACC,MAAM,EACb;MACEC,eAAe,EACbJ,uBAAuB,IAAI,IAAI,GAAG,aAAa,GAAG3D,MAAM,CAACgE,IAAI;MAC/DC,cAAc,EAAEjE,MAAM,CAACkE;IACzB,CAAC,EACD;MACEC,SAAS,EAAE,CACT;QACEC,UAAU,EAAEpC,OAAO,CAACqC,WAAW,CAAC;UAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CACXjH,MAAM,CAACgB,MAAM,GAAGoB,aAAa,GAAGnD,UAAU,CAACiI,aAAa,EACxD,CAAC;QAEL,CAAC;MACH,CAAC,CACF;MAGDC,QAAQ,EAAE9C,cAAc,GAAG,UAAU,GAAI;IAC3C,CAAC,EACD;MACErD,MAAM,EAAEmF,YAAY;MACpB/D,aAAa,EAAbA,aAAa;MACbgF,iBAAiB,EAAEjG,IAAI,CAACC,GAAG,CAACF,MAAM,CAACmG,IAAI,EAAEnG,MAAM,CAACoG,KAAK;IACvD,CAAC,EACDnE,WAAW,CACX;IACFoE,aAAa,EAAElD,cAAc,GAAG,MAAM,GAAG,MAAO;IAChDmD,QAAQ,EAAEzB;EAAa,GAEvBjH,KAAA,CAAAwH,aAAA,CAACpH,IAAI;IAACqI,aAAa,EAAC,MAAM;IAAC3F,KAAK,EAAE3C,UAAU,CAACwI;EAAa,GACvDpB,uBAAuB,CACnB,EACPvH,KAAA,CAAAwH,aAAA,CAACpH,IAAI;IAACwI,iBAAiB,EAAC,SAAS;IAAC9F,KAAK,EAAE2E,MAAM,CAACoB;EAAQ,GACrDxH,MAAM,CAACyH,GAAG,CAAC,UAACjH,KAAK,EAAEP,KAAK,EAAK;IAAA,IAAAyH,mBAAA;IAC5B,IAAMC,OAAO,GAAG1H,KAAK,KAAKN,KAAK,CAACM,KAAK;IACrC,IAAQE,OAAA,GAAYP,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC,CAAlCC,OAAA;IAER,IAAMyH,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpB,IAAMC,KAAK,GAAGxF,UAAU,CAACyF,IAAI,CAAC;QAC5BC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAExH,KAAK,CAACN,GAAG;QACjB+H,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAI,CAACN,OAAO,IAAI,CAACE,KAAK,CAACK,gBAAgB,EAAE;QACvC7F,UAAU,CAAC8F,QAAQ,CAAAnG,aAAA,CAAAA,aAAA,KACd1D,aAAa,CAAC8J,QAAQ,CAAC;UAAEC,IAAI,EAAE7H,KAAK,CAAC6H,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC,CAAC;UAC5DN,MAAM,EAAErI,KAAK,CAACO;QAAA,EACf,CAAC;MACJ;IACF,CAAC;IAED,IAAMqI,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBlG,UAAU,CAACyF,IAAI,CAAC;QACdC,IAAI,EAAE,cAAc;QACpBC,MAAM,EAAExH,KAAK,CAACN;MAChB,CAAC,CAAC;IACJ,CAAC;IAED,IAAMsI,KAAK,GACTrI,OAAO,CAACsI,WAAW,KAAKC,SAAS,GAC7BvI,OAAO,CAACsI,WAAW,GACnBtI,OAAO,CAACwI,KAAK,KAAKD,SAAS,GAC3BvI,OAAO,CAACwI,KAAK,GACbnI,KAAK,CAAC6H,IAAI;IAEhB,IAAMO,kBAAkB,GACtBzI,OAAO,CAAC0I,wBAAwB,KAAKH,SAAS,GAC1CvI,OAAO,CAAC0I,wBAAwB,GAChC,OAAOL,KAAK,KAAK,QAAQ,IAAI3J,QAAQ,CAACW,EAAE,KAAK,KAAK,GACjD,GAAEgJ,KAAM,UAASvI,KAAK,GAAG,CAAE,OAAMD,MAAM,CAAC8I,MAAO,EAAC,GACjDJ,SAAS;IAEf,OACE/J,KAAA,CAAAwH,aAAA,CAAC5H,iBAAiB,CAACwK,QAAQ;MACzB7I,GAAG,EAAEM,KAAK,CAACN,GAAI;MACf8I,KAAK,EAAEpJ,WAAW,CAACY,KAAK,CAACN,GAAG,CAAC,CAACmC;IAAW,GAEzC1D,KAAA,CAAAwH,aAAA,CAAC3H,sBAAsB,CAACuK,QAAQ;MAACC,KAAK,EAAExI;IAAM,GAC5C7B,KAAA,CAAAwH,aAAA,CAAChH,aAAa;MACZqB,KAAK,EAAEA,KAAM;MACbyI,UAAU,EAAErJ,WAAW,CAACY,KAAK,CAACN,GAAG,CAAE;MACnCyH,OAAO,EAAEA,OAAQ;MACjBuB,UAAU,EAAEjD,mBAAoB;MAChC2B,OAAO,EAAEA,OAAQ;MACjBW,WAAW,EAAEA,WAAY;MACzBK,kBAAkB,EAAEA,kBAAmB;MACvCO,EAAE,EAAE3G,SAAS,CAAChC,KAAK,CAAC6H,IAAI,EAAE7H,KAAK,CAAC4I,MAAM,CAAE;MACxCC,MAAM,EAAElJ,OAAO,CAACmJ,YAAa;MAC7BC,gBAAgB,EAAEpJ,OAAO,CAACqJ,sBAAuB;MACjDC,eAAe,EAAEvG,qBAAsB;MACvCwG,iBAAiB,EAAEvG,uBAAwB;MAC3CwG,qBAAqB,EAAEvG,2BAA4B;MACnDwG,uBAAuB,EAAEvG,6BAA8B;MACvDwG,MAAM,EAAE1J,OAAO,CAAC2J,YAAa;MAC7BC,IAAI,GAAArC,mBAAA,GACFvH,OAAO,CAAC6J,UAAU,YAAAtC,mBAAA,GACjB,UAAAuC,KAAA;QAAA,IAAGC,KAAK,GAAQD,KAAA,CAAbC,KAAK;UAAEC,IAAA,GAAMF,KAAA,CAANE,IAAA;QAAM,OACfxL,KAAA,CAAAwH,aAAA,CAAC9H,WAAW;UAAC6L,KAAK,EAAEA,KAAM;UAACC,IAAI,EAAEA;QAAK,EAAG;MAAA,CAE5C;MACDC,KAAK,EAAEjK,OAAO,CAACkK,WAAY;MAC3BC,UAAU,EAAEnK,OAAO,CAACoK,gBAAiB;MACrC/B,KAAK,EAAEA,KAAM;MACbgC,SAAS,EAAE5H,eAAgB;MAC3B6H,UAAU,EAAEtK,OAAO,CAACuK,gBAAiB;MACrCC,SAAS,EAAExK,OAAO,CAACyK,eAAgB;MACnCnJ,KAAK,EAAEtB,OAAO,CAACM;IAAgB,EAC/B,CAC8B,CACP;EAEjC,CAAC,CAAC,CACG,CACO;AAEpB;AAEA,IAAM2F,MAAM,GAAGtH,UAAU,CAAC+L,MAAM,CAAC;EAC/BxE,MAAM,EAAE;IACNa,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRjG,MAAM,EAAE,CAAC;IACT4J,cAAc,EAAEhM,UAAU,CAACiI,aAAa;IACxCgE,SAAS,EAAE;EACb,CAAC;EACDvD,OAAO,EAAE;IACPwD,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}