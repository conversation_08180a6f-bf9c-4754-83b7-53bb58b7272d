{"ast": null, "code": "import _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport dbManager from \"./DatabaseManager\";\nimport { v4 as uuidv4 } from 'uuid';\nvar BaseDataAccess = function () {\n  function BaseDataAccess(tableName) {\n    var idField = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'id';\n    _classCallCheck(this, BaseDataAccess);\n    this.tableName = tableName;\n    this.idField = idField;\n  }\n  return _createClass(BaseDataAccess, [{\n    key: \"getAll\",\n    value: (function () {\n      var _getAll = _asyncToGenerator(function* () {\n        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        var limit = options.limit,\n          offset = options.offset,\n          orderBy = options.orderBy,\n          order = options.order;\n        var query = `SELECT * FROM ${this.tableName} WHERE is_active = 1 AND deleted_at IS NULL`;\n        if (orderBy) {\n          query += ` ORDER BY ${orderBy} ${order || 'ASC'}`;\n        }\n        if (limit) {\n          query += ` LIMIT ${limit}`;\n          if (offset) {\n            query += ` OFFSET ${offset}`;\n          }\n        }\n        var result = yield dbManager.executeQuery(query);\n        var items = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          items.push(result.rows.item(i));\n        }\n        return items;\n      });\n      function getAll() {\n        return _getAll.apply(this, arguments);\n      }\n      return getAll;\n    }())\n  }, {\n    key: \"getById\",\n    value: (function () {\n      var _getById = _asyncToGenerator(function* (id) {\n        var result = yield dbManager.executeQuery(`SELECT * FROM ${this.tableName} WHERE ${this.idField} = ? AND is_active = 1 AND deleted_at IS NULL`, [id]);\n        return result.rows.length > 0 ? result.rows.item(0) : null;\n      });\n      function getById(_x) {\n        return _getById.apply(this, arguments);\n      }\n      return getById;\n    }())\n  }, {\n    key: \"create\",\n    value: (function () {\n      var _create = _asyncToGenerator(function* (data) {\n        var recordData = _objectSpread({}, data);\n        if (!recordData[this.idField]) {\n          recordData[this.idField] = uuidv4();\n        }\n        var now = new Date().toISOString();\n        if (!recordData.created_at) {\n          recordData.created_at = now;\n        }\n        if (!recordData.updated_at) {\n          recordData.updated_at = now;\n        }\n        if (recordData.is_active === undefined) {\n          recordData.is_active = 1;\n        }\n        var columns = Object.keys(recordData).join(', ');\n        var placeholders = Object.keys(recordData).map(function () {\n          return '?';\n        }).join(', ');\n        var values = Object.values(recordData);\n        yield dbManager.executeQuery(`INSERT INTO ${this.tableName} (${columns}) VALUES (${placeholders})`, values);\n        return this.getById(recordData[this.idField]);\n      });\n      function create(_x2) {\n        return _create.apply(this, arguments);\n      }\n      return create;\n    }())\n  }, {\n    key: \"update\",\n    value: (function () {\n      var _update = _asyncToGenerator(function* (id, data) {\n        var recordData = _objectSpread({}, data);\n        if (!recordData.updated_at) {\n          recordData.updated_at = new Date().toISOString();\n        }\n        delete recordData[this.idField];\n        var setClause = Object.keys(recordData).map(function (key) {\n          return `${key} = ?`;\n        }).join(', ');\n        var values = [].concat(_toConsumableArray(Object.values(recordData)), [id]);\n        yield dbManager.executeQuery(`UPDATE ${this.tableName} SET ${setClause} WHERE ${this.idField} = ?`, values);\n        return this.getById(id);\n      });\n      function update(_x3, _x4) {\n        return _update.apply(this, arguments);\n      }\n      return update;\n    }())\n  }, {\n    key: \"delete\",\n    value: (function () {\n      var _delete2 = _asyncToGenerator(function* (id) {\n        var result = yield dbManager.executeQuery(`UPDATE ${this.tableName} SET is_active = 0, deleted_at = datetime('now') WHERE ${this.idField} = ?`, [id]);\n        return result.rowsAffected > 0;\n      });\n      function _delete(_x5) {\n        return _delete2.apply(this, arguments);\n      }\n      return _delete;\n    }())\n  }, {\n    key: \"findBy\",\n    value: (function () {\n      var _findBy = _asyncToGenerator(function* (field, value) {\n        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n        var limit = options.limit,\n          offset = options.offset,\n          orderBy = options.orderBy,\n          order = options.order;\n        var query = `SELECT * FROM ${this.tableName} WHERE ${field} = ? AND is_active = 1 AND deleted_at IS NULL`;\n        if (orderBy) {\n          query += ` ORDER BY ${orderBy} ${order || 'ASC'}`;\n        }\n        if (limit) {\n          query += ` LIMIT ${limit}`;\n          if (offset) {\n            query += ` OFFSET ${offset}`;\n          }\n        }\n        var result = yield dbManager.executeQuery(query, [value]);\n        var items = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          items.push(result.rows.item(i));\n        }\n        return items;\n      });\n      function findBy(_x6, _x7) {\n        return _findBy.apply(this, arguments);\n      }\n      return findBy;\n    }())\n  }, {\n    key: \"search\",\n    value: (function () {\n      var _search = _asyncToGenerator(function* (field, query) {\n        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n        var limit = options.limit,\n          offset = options.offset,\n          orderBy = options.orderBy,\n          order = options.order;\n        var sqlQuery = `SELECT * FROM ${this.tableName} WHERE ${field} LIKE ? AND is_active = 1 AND deleted_at IS NULL`;\n        if (orderBy) {\n          sqlQuery += ` ORDER BY ${orderBy} ${order || 'ASC'}`;\n        }\n        if (limit) {\n          sqlQuery += ` LIMIT ${limit}`;\n          if (offset) {\n            sqlQuery += ` OFFSET ${offset}`;\n          }\n        }\n        var result = yield dbManager.executeQuery(sqlQuery, [`%${query}%`]);\n        var items = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          items.push(result.rows.item(i));\n        }\n        return items;\n      });\n      function search(_x8, _x9) {\n        return _search.apply(this, arguments);\n      }\n      return search;\n    }())\n  }, {\n    key: \"count\",\n    value: (function () {\n      var _count = _asyncToGenerator(function* () {\n        var conditions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        var whereClause = 'is_active = 1 AND deleted_at IS NULL';\n        var params = [];\n        Object.entries(conditions).forEach(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n            key = _ref2[0],\n            value = _ref2[1];\n          whereClause += ` AND ${key} = ?`;\n          params.push(value);\n        });\n        var result = yield dbManager.executeQuery(`SELECT COUNT(*) as count FROM ${this.tableName} WHERE ${whereClause}`, params);\n        return result.rows.item(0).count;\n      });\n      function count() {\n        return _count.apply(this, arguments);\n      }\n      return count;\n    }())\n  }]);\n}();\nvar UserDataAccess = function (_BaseDataAccess) {\n  function UserDataAccess() {\n    _classCallCheck(this, UserDataAccess);\n    return _callSuper(this, UserDataAccess, ['User', 'id']);\n  }\n  _inherits(UserDataAccess, _BaseDataAccess);\n  return _createClass(UserDataAccess, [{\n    key: \"getSettings\",\n    value: (function () {\n      var _getSettings = _asyncToGenerator(function* (userId) {\n        var result = yield dbManager.executeQuery('SELECT setting_key, setting_value FROM UserSetting WHERE user_id = ? AND is_active = 1', [userId]);\n        var settings = {};\n        for (var i = 0; i < result.rows.length; i++) {\n          var setting = result.rows.item(i);\n          settings[setting.setting_key] = setting.setting_value;\n        }\n        return settings;\n      });\n      function getSettings(_x0) {\n        return _getSettings.apply(this, arguments);\n      }\n      return getSettings;\n    }())\n  }, {\n    key: \"saveSetting\",\n    value: (function () {\n      var _saveSetting = _asyncToGenerator(function* (userId, key, value) {\n        var now = new Date().toISOString();\n        var result = yield dbManager.executeQuery('SELECT id FROM UserSetting WHERE user_id = ? AND setting_key = ?', [userId, key]);\n        if (result.rows.length > 0) {\n          yield dbManager.executeQuery('UPDATE UserSetting SET setting_value = ?, updated_at = ? WHERE user_id = ? AND setting_key = ?', [value, now, userId, key]);\n        } else {\n          var settingId = uuidv4();\n          yield dbManager.executeQuery('INSERT INTO UserSetting (id, user_id, setting_key, setting_value, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)', [settingId, userId, key, value, now, now]);\n        }\n      });\n      function saveSetting(_x1, _x10, _x11) {\n        return _saveSetting.apply(this, arguments);\n      }\n      return saveSetting;\n    }())\n  }, {\n    key: \"getCurrentUser\",\n    value: (function () {\n      var _getCurrentUser = _asyncToGenerator(function* () {\n        var result = yield dbManager.executeQuery('SELECT * FROM User WHERE is_active = 1 LIMIT 1');\n        if (result.rows.length === 0) {\n          return null;\n        }\n        var user = result.rows.item(0);\n        user.settings = yield this.getSettings(user.id);\n        return user;\n      });\n      function getCurrentUser() {\n        return _getCurrentUser.apply(this, arguments);\n      }\n      return getCurrentUser;\n    }())\n  }]);\n}(BaseDataAccess);\nvar FoodDataAccess = function (_BaseDataAccess2) {\n  function FoodDataAccess() {\n    _classCallCheck(this, FoodDataAccess);\n    return _callSuper(this, FoodDataAccess, ['Food', 'id']);\n  }\n  _inherits(FoodDataAccess, _BaseDataAccess2);\n  return _createClass(FoodDataAccess, [{\n    key: \"getByBarcode\",\n    value: (function () {\n      var _getByBarcode = _asyncToGenerator(function* (barcode) {\n        var result = yield dbManager.executeQuery('SELECT * FROM Food WHERE barcode = ? AND is_active = 1 AND deleted_at IS NULL', [barcode]);\n        return result.rows.length > 0 ? result.rows.item(0) : null;\n      });\n      function getByBarcode(_x12) {\n        return _getByBarcode.apply(this, arguments);\n      }\n      return getByBarcode;\n    }())\n  }, {\n    key: \"getNutrients\",\n    value: (function () {\n      var _getNutrients = _asyncToGenerator(function* (foodId) {\n        var result = yield dbManager.executeQuery(`SELECT fn.*, n.name, n.unit, n.is_macro\n       FROM FoodNutrient fn\n       JOIN Nutrient n ON fn.nutrient_id = n.id\n       WHERE fn.food_id = ? AND fn.is_active = 1 AND n.is_active = 1`, [foodId]);\n        var nutrients = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          nutrients.push(result.rows.item(i));\n        }\n        return nutrients;\n      });\n      function getNutrients(_x13) {\n        return _getNutrients.apply(this, arguments);\n      }\n      return getNutrients;\n    }())\n  }, {\n    key: \"saveNutrient\",\n    value: (function () {\n      var _saveNutrient = _asyncToGenerator(function* (foodId, nutrientId, amount) {\n        var now = new Date().toISOString();\n        var result = yield dbManager.executeQuery('SELECT id FROM FoodNutrient WHERE food_id = ? AND nutrient_id = ?', [foodId, nutrientId]);\n        if (result.rows.length > 0) {\n          yield dbManager.executeQuery('UPDATE FoodNutrient SET amount = ?, updated_at = ? WHERE food_id = ? AND nutrient_id = ?', [amount, now, foodId, nutrientId]);\n        } else {\n          var id = uuidv4();\n          yield dbManager.executeQuery('INSERT INTO FoodNutrient (id, food_id, nutrient_id, amount, created_at, updated_at, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)', [id, foodId, nutrientId, amount, now, now]);\n        }\n      });\n      function saveNutrient(_x14, _x15, _x16) {\n        return _saveNutrient.apply(this, arguments);\n      }\n      return saveNutrient;\n    }())\n  }, {\n    key: \"getIngredients\",\n    value: (function () {\n      var _getIngredients = _asyncToGenerator(function* (foodId) {\n        var result = yield dbManager.executeQuery(`SELECT fi.*, i.name, i.is_allergen\n       FROM FoodIngredient fi\n       JOIN Ingredient i ON fi.ingredient_id = i.id\n       WHERE fi.food_id = ? AND fi.is_active = 1 AND i.is_active = 1\n       ORDER BY fi.order_num`, [foodId]);\n        var ingredients = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          ingredients.push(result.rows.item(i));\n        }\n        return ingredients;\n      });\n      function getIngredients(_x17) {\n        return _getIngredients.apply(this, arguments);\n      }\n      return getIngredients;\n    }())\n  }, {\n    key: \"getBySource\",\n    value: (function () {\n      var _getBySource = _asyncToGenerator(function* (source) {\n        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;\n        var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n        var result = yield dbManager.executeQuery(`SELECT * FROM Food\n       WHERE source LIKE ? AND is_active = 1 AND deleted_at IS NULL\n       ORDER BY name\n       LIMIT ? OFFSET ?`, [`${source}%`, limit, offset]);\n        var foods = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          var food = result.rows.item(i);\n          food.nutrients = yield this.getNutrients(food.id);\n          food.ingredients = yield this.getIngredients(food.id);\n          foods.push(food);\n        }\n        return foods;\n      });\n      function getBySource(_x18) {\n        return _getBySource.apply(this, arguments);\n      }\n      return getBySource;\n    }())\n  }]);\n}(BaseDataAccess);\nvar ConsumptionDataAccess = function (_BaseDataAccess3) {\n  function ConsumptionDataAccess() {\n    _classCallCheck(this, ConsumptionDataAccess);\n    return _callSuper(this, ConsumptionDataAccess, ['Consumption', 'id']);\n  }\n  _inherits(ConsumptionDataAccess, _BaseDataAccess3);\n  return _createClass(ConsumptionDataAccess, [{\n    key: \"getItems\",\n    value: (function () {\n      var _getItems = _asyncToGenerator(function* (consumptionId) {\n        var result = yield dbManager.executeQuery(`SELECT ci.*, f.name as food_name\n       FROM ConsumptionItem ci\n       JOIN Food f ON ci.food_id = f.id\n       WHERE ci.consumption_id = ? AND ci.is_active = 1 AND f.is_active = 1`, [consumptionId]);\n        var items = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          items.push(result.rows.item(i));\n        }\n        return items;\n      });\n      function getItems(_x19) {\n        return _getItems.apply(this, arguments);\n      }\n      return getItems;\n    }())\n  }, {\n    key: \"addItem\",\n    value: (function () {\n      var _addItem = _asyncToGenerator(function* (consumptionId, foodId, quantity) {\n        var unit = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'g';\n        var now = new Date().toISOString();\n        var id = uuidv4();\n        yield dbManager.executeQuery(`INSERT INTO ConsumptionItem (id, consumption_id, food_id, quantity, unit, created_at, updated_at, is_active)\n       VALUES (?, ?, ?, ?, ?, ?, ?, 1)`, [id, consumptionId, foodId, quantity, unit, now, now]);\n        var result = yield dbManager.executeQuery(`SELECT ci.*, f.name as food_name\n       FROM ConsumptionItem ci\n       JOIN Food f ON ci.food_id = f.id\n       WHERE ci.id = ?`, [id]);\n        return result.rows.length > 0 ? result.rows.item(0) : null;\n      });\n      function addItem(_x20, _x21, _x22) {\n        return _addItem.apply(this, arguments);\n      }\n      return addItem;\n    }())\n  }, {\n    key: \"getByDate\",\n    value: (function () {\n      var _getByDate = _asyncToGenerator(function* (userId, date) {\n        var result = yield dbManager.executeQuery(`SELECT c.*\n       FROM Consumption c\n       WHERE c.user_id = ? AND c.consumption_date = ? AND c.is_active = 1 AND c.deleted_at IS NULL\n       ORDER BY c.meal_type`, [userId, date]);\n        var consumptions = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          var consumption = result.rows.item(i);\n          consumption.items = yield this.getItems(consumption.id);\n          consumptions.push(consumption);\n        }\n        return consumptions;\n      });\n      function getByDate(_x23, _x24) {\n        return _getByDate.apply(this, arguments);\n      }\n      return getByDate;\n    }())\n  }, {\n    key: \"getDailyNutritionSummary\",\n    value: (function () {\n      var _getDailyNutritionSummary = _asyncToGenerator(function* (userId, date) {\n        var result = yield dbManager.executeQuery(`SELECT\n         n.id as nutrient_id,\n         n.name as nutrient_name,\n         n.unit,\n         SUM(fn.amount * ci.quantity / 100) as total_amount,\n         n.daily_value,\n         (SUM(fn.amount * ci.quantity / 100) / n.daily_value * 100) as percent_of_daily_value\n       FROM\n         Consumption c\n       JOIN\n         ConsumptionItem ci ON c.id = ci.consumption_id\n       JOIN\n         Food f ON ci.food_id = f.id\n       JOIN\n         FoodNutrient fn ON f.id = fn.food_id\n       JOIN\n         Nutrient n ON fn.nutrient_id = n.id\n       WHERE\n         c.user_id = ? AND c.consumption_date = ?\n         AND c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1 AND n.is_active = 1\n       GROUP BY\n         n.id`, [userId, date]);\n        var nutrients = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          nutrients.push(result.rows.item(i));\n        }\n        var mealResult = yield dbManager.executeQuery(`SELECT\n         c.meal_type,\n         SUM(fn.amount * ci.quantity / 100) as calories\n       FROM\n         Consumption c\n       JOIN\n         ConsumptionItem ci ON c.id = ci.consumption_id\n       JOIN\n         Food f ON ci.food_id = f.id\n       JOIN\n         FoodNutrient fn ON f.id = fn.food_id\n       WHERE\n         c.user_id = ? AND c.consumption_date = ? AND fn.nutrient_id = 'nutrient-calories'\n         AND c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1\n       GROUP BY\n         c.meal_type`, [userId, date]);\n        var meals = [];\n        for (var _i = 0; _i < mealResult.rows.length; _i++) {\n          meals.push(mealResult.rows.item(_i));\n        }\n        return {\n          date: date,\n          nutrients: nutrients,\n          meals: meals,\n          totalCalories: meals.reduce(function (sum, meal) {\n            return sum + meal.calories;\n          }, 0)\n        };\n      });\n      function getDailyNutritionSummary(_x25, _x26) {\n        return _getDailyNutritionSummary.apply(this, arguments);\n      }\n      return getDailyNutritionSummary;\n    }())\n  }]);\n}(BaseDataAccess);\nvar NutrientDataAccess = function (_BaseDataAccess4) {\n  function NutrientDataAccess() {\n    _classCallCheck(this, NutrientDataAccess);\n    return _callSuper(this, NutrientDataAccess, ['Nutrient', 'id']);\n  }\n  _inherits(NutrientDataAccess, _BaseDataAccess4);\n  return _createClass(NutrientDataAccess, [{\n    key: \"getMacronutrients\",\n    value: (function () {\n      var _getMacronutrients = _asyncToGenerator(function* () {\n        var result = yield dbManager.executeQuery('SELECT * FROM Nutrient WHERE is_macro = 1 AND is_active = 1 AND deleted_at IS NULL');\n        var nutrients = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          nutrients.push(result.rows.item(i));\n        }\n        return nutrients;\n      });\n      function getMacronutrients() {\n        return _getMacronutrients.apply(this, arguments);\n      }\n      return getMacronutrients;\n    }())\n  }]);\n}(BaseDataAccess);\nvar IngredientDataAccess = function (_BaseDataAccess5) {\n  function IngredientDataAccess() {\n    _classCallCheck(this, IngredientDataAccess);\n    return _callSuper(this, IngredientDataAccess, ['Ingredient', 'id']);\n  }\n  _inherits(IngredientDataAccess, _BaseDataAccess5);\n  return _createClass(IngredientDataAccess, [{\n    key: \"getAllergens\",\n    value: (function () {\n      var _getAllergens = _asyncToGenerator(function* () {\n        var result = yield dbManager.executeQuery('SELECT * FROM Ingredient WHERE is_allergen = 1 AND is_active = 1 AND deleted_at IS NULL');\n        var allergens = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          allergens.push(result.rows.item(i));\n        }\n        return allergens;\n      });\n      function getAllergens() {\n        return _getAllergens.apply(this, arguments);\n      }\n      return getAllergens;\n    }())\n  }]);\n}(BaseDataAccess);\nvar MealTypeDataAccess = function (_BaseDataAccess6) {\n  function MealTypeDataAccess() {\n    _classCallCheck(this, MealTypeDataAccess);\n    return _callSuper(this, MealTypeDataAccess, ['MealType', 'id']);\n  }\n  _inherits(MealTypeDataAccess, _BaseDataAccess6);\n  return _createClass(MealTypeDataAccess, [{\n    key: \"getAllOrdered\",\n    value: (function () {\n      var _getAllOrdered = _asyncToGenerator(function* () {\n        var result = yield dbManager.executeQuery('SELECT * FROM MealType WHERE is_active = 1 AND deleted_at IS NULL ORDER BY display_order');\n        var mealTypes = [];\n        for (var i = 0; i < result.rows.length; i++) {\n          mealTypes.push(result.rows.item(i));\n        }\n        return mealTypes;\n      });\n      function getAllOrdered() {\n        return _getAllOrdered.apply(this, arguments);\n      }\n      return getAllOrdered;\n    }())\n  }]);\n}(BaseDataAccess);\nexport var userDataAccess = new UserDataAccess();\nexport var foodDataAccess = new FoodDataAccess();\nexport var consumptionDataAccess = new ConsumptionDataAccess();\nexport var nutrientDataAccess = new NutrientDataAccess();\nexport var ingredientDataAccess = new IngredientDataAccess();\nexport var mealTypeDataAccess = new MealTypeDataAccess();\nexport { BaseDataAccess };", "map": {"version": 3, "names": ["db<PERSON><PERSON><PERSON>", "v4", "uuidv4", "BaseDataAccess", "tableName", "idField", "arguments", "length", "undefined", "_classCallCheck", "_createClass", "key", "value", "_getAll", "_asyncToGenerator", "options", "limit", "offset", "orderBy", "order", "query", "result", "execute<PERSON>uery", "items", "i", "rows", "push", "item", "getAll", "apply", "_getById", "id", "getById", "_x", "_create", "data", "recordData", "_objectSpread", "now", "Date", "toISOString", "created_at", "updated_at", "is_active", "columns", "Object", "keys", "join", "placeholders", "map", "values", "create", "_x2", "_update", "<PERSON><PERSON><PERSON><PERSON>", "concat", "_toConsumableArray", "update", "_x3", "_x4", "_delete2", "rowsAffected", "delete", "_x5", "_findBy", "field", "find<PERSON><PERSON>", "_x6", "_x7", "_search", "sqlQuery", "search", "_x8", "_x9", "_count", "conditions", "<PERSON><PERSON><PERSON><PERSON>", "params", "entries", "for<PERSON>ach", "_ref", "_ref2", "_slicedToArray", "count", "UserDataAccess", "_BaseDataAccess", "_callSuper", "_inherits", "_getSettings", "userId", "settings", "setting", "setting_key", "setting_value", "getSettings", "_x0", "_saveSetting", "settingId", "saveSetting", "_x1", "_x10", "_x11", "_getCurrent<PERSON>ser", "user", "getCurrentUser", "FoodDataAccess", "_BaseDataAccess2", "_getByBarcode", "barcode", "getByBarcode", "_x12", "_getNutrients", "foodId", "nutrients", "getNutrients", "_x13", "_saveNutrient", "nutrientId", "amount", "saveNutrient", "_x14", "_x15", "_x16", "_getIngredients", "ingredients", "getIngredients", "_x17", "_getBySource", "source", "foods", "food", "getBySource", "_x18", "ConsumptionDataAccess", "_BaseDataAccess3", "_getItems", "consumptionId", "getItems", "_x19", "_addItem", "quantity", "unit", "addItem", "_x20", "_x21", "_x22", "_getByDate", "date", "consumptions", "consumption", "getByDate", "_x23", "_x24", "_getDailyNutritionSummary", "mealResult", "meals", "totalCalories", "reduce", "sum", "meal", "calories", "getDailyNutritionSummary", "_x25", "_x26", "NutrientDataAccess", "_BaseDataAccess4", "_getMacronutrients", "getMacronutrients", "IngredientDataAccess", "_BaseDataAccess5", "_get<PERSON><PERSON><PERSON>", "allergens", "getAllergens", "MealTypeDataAccess", "_BaseDataAccess6", "_getAllOrdered", "mealTypes", "getAllO<PERSON>red", "userDataAccess", "foodDataAccess", "consumptionDataAccess", "nutrientDataAccess", "ingredientDataAccess", "mealTypeDataAccess"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/database/DataAccess.js"], "sourcesContent": ["/**\n * Data Access Layer for Znü<PERSON>Zähler\n * Provides a higher-level API for database operations\n */\n\nimport dbManager from './DatabaseManager';\nimport { v4 as uuidv4 } from 'uuid';\n\n/**\n * Base data access class with common CRUD operations\n */\nclass BaseDataAccess {\n  /**\n   * Create a new BaseDataAccess instance\n   * @param {string} tableName - Database table name\n   * @param {string} idField - Primary key field name (default: 'id')\n   */\n  constructor(tableName, idField = 'id') {\n    this.tableName = tableName;\n    this.idField = idField;\n  }\n\n  /**\n   * Get all active records\n   * @param {Object} options - Query options\n   * @param {number} options.limit - Maximum number of records to return\n   * @param {number} options.offset - Number of records to skip\n   * @param {string} options.orderBy - Field to order by\n   * @param {string} options.order - Order direction ('ASC' or 'DESC')\n   * @returns {Promise<Array>} - Array of records\n   */\n  async getAll(options = {}) {\n    const { limit, offset, orderBy, order } = options;\n\n    let query = `SELECT * FROM ${this.tableName} WHERE is_active = 1 AND deleted_at IS NULL`;\n\n    if (orderBy) {\n      query += ` ORDER BY ${orderBy} ${order || 'ASC'}`;\n    }\n\n    if (limit) {\n      query += ` LIMIT ${limit}`;\n\n      if (offset) {\n        query += ` OFFSET ${offset}`;\n      }\n    }\n\n    const result = await dbManager.executeQuery(query);\n\n    const items = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      items.push(result.rows.item(i));\n    }\n\n    return items;\n  }\n\n  /**\n   * Get a record by ID\n   * @param {string} id - Record ID\n   * @returns {Promise<Object|null>} - Record or null if not found\n   */\n  async getById(id) {\n    const result = await dbManager.executeQuery(\n      `SELECT * FROM ${this.tableName} WHERE ${this.idField} = ? AND is_active = 1 AND deleted_at IS NULL`,\n      [id]\n    );\n\n    return result.rows.length > 0 ? result.rows.item(0) : null;\n  }\n\n  /**\n   * Create a new record\n   * @param {Object} data - Record data\n   * @returns {Promise<Object>} - Created record\n   */\n  async create(data) {\n    // Generate ID if not provided\n    const recordData = { ...data };\n    if (!recordData[this.idField]) {\n      recordData[this.idField] = uuidv4();\n    }\n\n    // Add timestamps if not provided\n    const now = new Date().toISOString();\n    if (!recordData.created_at) {\n      recordData.created_at = now;\n    }\n    if (!recordData.updated_at) {\n      recordData.updated_at = now;\n    }\n\n    // Set is_active to 1 if not provided\n    if (recordData.is_active === undefined) {\n      recordData.is_active = 1;\n    }\n\n    const columns = Object.keys(recordData).join(', ');\n    const placeholders = Object.keys(recordData).map(() => '?').join(', ');\n    const values = Object.values(recordData);\n\n    await dbManager.executeQuery(\n      `INSERT INTO ${this.tableName} (${columns}) VALUES (${placeholders})`,\n      values\n    );\n\n    return this.getById(recordData[this.idField]);\n  }\n\n  /**\n   * Update a record\n   * @param {string} id - Record ID\n   * @param {Object} data - Record data\n   * @returns {Promise<Object>} - Updated record\n   */\n  async update(id, data) {\n    // Add updated_at timestamp if not provided\n    const recordData = { ...data };\n    if (!recordData.updated_at) {\n      recordData.updated_at = new Date().toISOString();\n    }\n\n    // Remove id field from data if present\n    delete recordData[this.idField];\n\n    const setClause = Object.keys(recordData).map(key => `${key} = ?`).join(', ');\n    const values = [...Object.values(recordData), id];\n\n    await dbManager.executeQuery(\n      `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.idField} = ?`,\n      values\n    );\n\n    return this.getById(id);\n  }\n\n  /**\n   * Soft delete a record\n   * @param {string} id - Record ID\n   * @returns {Promise<boolean>} - Success flag\n   */\n  async delete(id) {\n    const result = await dbManager.executeQuery(\n      `UPDATE ${this.tableName} SET is_active = 0, deleted_at = datetime('now') WHERE ${this.idField} = ?`,\n      [id]\n    );\n\n    return result.rowsAffected > 0;\n  }\n\n  /**\n   * Find records by field value\n   * @param {string} field - Field name\n   * @param {*} value - Field value\n   * @param {Object} options - Query options\n   * @returns {Promise<Array>} - Array of records\n   */\n  async findBy(field, value, options = {}) {\n    const { limit, offset, orderBy, order } = options;\n\n    let query = `SELECT * FROM ${this.tableName} WHERE ${field} = ? AND is_active = 1 AND deleted_at IS NULL`;\n\n    if (orderBy) {\n      query += ` ORDER BY ${orderBy} ${order || 'ASC'}`;\n    }\n\n    if (limit) {\n      query += ` LIMIT ${limit}`;\n\n      if (offset) {\n        query += ` OFFSET ${offset}`;\n      }\n    }\n\n    const result = await dbManager.executeQuery(query, [value]);\n\n    const items = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      items.push(result.rows.item(i));\n    }\n\n    return items;\n  }\n\n  /**\n   * Search records by field value\n   * @param {string} field - Field name\n   * @param {string} query - Search query\n   * @param {Object} options - Query options\n   * @returns {Promise<Array>} - Array of records\n   */\n  async search(field, query, options = {}) {\n    const { limit, offset, orderBy, order } = options;\n\n    let sqlQuery = `SELECT * FROM ${this.tableName} WHERE ${field} LIKE ? AND is_active = 1 AND deleted_at IS NULL`;\n\n    if (orderBy) {\n      sqlQuery += ` ORDER BY ${orderBy} ${order || 'ASC'}`;\n    }\n\n    if (limit) {\n      sqlQuery += ` LIMIT ${limit}`;\n\n      if (offset) {\n        sqlQuery += ` OFFSET ${offset}`;\n      }\n    }\n\n    const result = await dbManager.executeQuery(sqlQuery, [`%${query}%`]);\n\n    const items = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      items.push(result.rows.item(i));\n    }\n\n    return items;\n  }\n\n  /**\n   * Count records\n   * @param {Object} conditions - Query conditions\n   * @returns {Promise<number>} - Record count\n   */\n  async count(conditions = {}) {\n    let whereClause = 'is_active = 1 AND deleted_at IS NULL';\n    const params = [];\n\n    Object.entries(conditions).forEach(([key, value]) => {\n      whereClause += ` AND ${key} = ?`;\n      params.push(value);\n    });\n\n    const result = await dbManager.executeQuery(\n      `SELECT COUNT(*) as count FROM ${this.tableName} WHERE ${whereClause}`,\n      params\n    );\n\n    return result.rows.item(0).count;\n  }\n}\n\n/**\n * User data access\n */\nclass UserDataAccess extends BaseDataAccess {\n  constructor() {\n    super('User', 'id');\n  }\n\n  /**\n   * Get user settings\n   * @param {string} userId - User ID\n   * @returns {Promise<Object>} - User settings\n   */\n  async getSettings(userId) {\n    const result = await dbManager.executeQuery(\n      'SELECT setting_key, setting_value FROM UserSetting WHERE user_id = ? AND is_active = 1',\n      [userId]\n    );\n\n    const settings = {};\n    for (let i = 0; i < result.rows.length; i++) {\n      const setting = result.rows.item(i);\n      settings[setting.setting_key] = setting.setting_value;\n    }\n\n    return settings;\n  }\n\n  /**\n   * Save user setting\n   * @param {string} userId - User ID\n   * @param {string} key - Setting key\n   * @param {string} value - Setting value\n   * @returns {Promise<void>}\n   */\n  async saveSetting(userId, key, value) {\n    const now = new Date().toISOString();\n\n    // Check if setting exists\n    const result = await dbManager.executeQuery(\n      'SELECT id FROM UserSetting WHERE user_id = ? AND setting_key = ?',\n      [userId, key]\n    );\n\n    if (result.rows.length > 0) {\n      // Update existing setting\n      await dbManager.executeQuery(\n        'UPDATE UserSetting SET setting_value = ?, updated_at = ? WHERE user_id = ? AND setting_key = ?',\n        [value, now, userId, key]\n      );\n    } else {\n      // Create new setting\n      const settingId = uuidv4();\n      await dbManager.executeQuery(\n        'INSERT INTO UserSetting (id, user_id, setting_key, setting_value, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',\n        [settingId, userId, key, value, now, now]\n      );\n    }\n  }\n\n  /**\n   * Get current user\n   * @returns {Promise<Object|null>} - Current user or null\n   */\n  async getCurrentUser() {\n    const result = await dbManager.executeQuery(\n      'SELECT * FROM User WHERE is_active = 1 LIMIT 1'\n    );\n\n    if (result.rows.length === 0) {\n      return null;\n    }\n\n    const user = result.rows.item(0);\n    user.settings = await this.getSettings(user.id);\n\n    return user;\n  }\n}\n\n/**\n * Food data access\n */\nclass FoodDataAccess extends BaseDataAccess {\n  constructor() {\n    super('Food', 'id');\n  }\n\n  /**\n   * Get food by barcode\n   * @param {string} barcode - Barcode\n   * @returns {Promise<Object|null>} - Food or null\n   */\n  async getByBarcode(barcode) {\n    const result = await dbManager.executeQuery(\n      'SELECT * FROM Food WHERE barcode = ? AND is_active = 1 AND deleted_at IS NULL',\n      [barcode]\n    );\n\n    return result.rows.length > 0 ? result.rows.item(0) : null;\n  }\n\n  /**\n   * Get food nutrients\n   * @param {string} foodId - Food ID\n   * @returns {Promise<Array>} - Array of nutrients\n   */\n  async getNutrients(foodId) {\n    const result = await dbManager.executeQuery(\n      `SELECT fn.*, n.name, n.unit, n.is_macro\n       FROM FoodNutrient fn\n       JOIN Nutrient n ON fn.nutrient_id = n.id\n       WHERE fn.food_id = ? AND fn.is_active = 1 AND n.is_active = 1`,\n      [foodId]\n    );\n\n    const nutrients = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      nutrients.push(result.rows.item(i));\n    }\n\n    return nutrients;\n  }\n\n  /**\n   * Save food nutrient\n   * @param {string} foodId - Food ID\n   * @param {string} nutrientId - Nutrient ID\n   * @param {number} amount - Nutrient amount\n   * @returns {Promise<void>}\n   */\n  async saveNutrient(foodId, nutrientId, amount) {\n    const now = new Date().toISOString();\n\n    // Check if nutrient exists for this food\n    const result = await dbManager.executeQuery(\n      'SELECT id FROM FoodNutrient WHERE food_id = ? AND nutrient_id = ?',\n      [foodId, nutrientId]\n    );\n\n    if (result.rows.length > 0) {\n      // Update existing nutrient\n      await dbManager.executeQuery(\n        'UPDATE FoodNutrient SET amount = ?, updated_at = ? WHERE food_id = ? AND nutrient_id = ?',\n        [amount, now, foodId, nutrientId]\n      );\n    } else {\n      // Create new nutrient\n      const id = uuidv4();\n      await dbManager.executeQuery(\n        'INSERT INTO FoodNutrient (id, food_id, nutrient_id, amount, created_at, updated_at, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)',\n        [id, foodId, nutrientId, amount, now, now]\n      );\n    }\n  }\n\n  /**\n   * Get food ingredients\n   * @param {string} foodId - Food ID\n   * @returns {Promise<Array>} - Array of ingredients\n   */\n  async getIngredients(foodId) {\n    const result = await dbManager.executeQuery(\n      `SELECT fi.*, i.name, i.is_allergen\n       FROM FoodIngredient fi\n       JOIN Ingredient i ON fi.ingredient_id = i.id\n       WHERE fi.food_id = ? AND fi.is_active = 1 AND i.is_active = 1\n       ORDER BY fi.order_num`,\n      [foodId]\n    );\n\n    const ingredients = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      ingredients.push(result.rows.item(i));\n    }\n\n    return ingredients;\n  }\n\n  /**\n   * Get foods by source\n   * @param {string} source - Database source (e.g., 'USDA', 'FoodB')\n   * @param {number} limit - Maximum number of results\n   * @param {number} offset - Offset for pagination\n   * @returns {Promise<Array>} - Array of food objects\n   */\n  async getBySource(source, limit = 20, offset = 0) {\n    const result = await dbManager.executeQuery(\n      `SELECT * FROM Food\n       WHERE source LIKE ? AND is_active = 1 AND deleted_at IS NULL\n       ORDER BY name\n       LIMIT ? OFFSET ?`,\n      [`${source}%`, limit, offset]\n    );\n\n    const foods = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      const food = result.rows.item(i);\n      food.nutrients = await this.getNutrients(food.id);\n      food.ingredients = await this.getIngredients(food.id);\n      foods.push(food);\n    }\n\n    return foods;\n  }\n}\n\n/**\n * Consumption data access\n */\nclass ConsumptionDataAccess extends BaseDataAccess {\n  constructor() {\n    super('Consumption', 'id');\n  }\n\n  /**\n   * Get consumption items\n   * @param {string} consumptionId - Consumption ID\n   * @returns {Promise<Array>} - Array of consumption items\n   */\n  async getItems(consumptionId) {\n    const result = await dbManager.executeQuery(\n      `SELECT ci.*, f.name as food_name\n       FROM ConsumptionItem ci\n       JOIN Food f ON ci.food_id = f.id\n       WHERE ci.consumption_id = ? AND ci.is_active = 1 AND f.is_active = 1`,\n      [consumptionId]\n    );\n\n    const items = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      items.push(result.rows.item(i));\n    }\n\n    return items;\n  }\n\n  /**\n   * Add consumption item\n   * @param {string} consumptionId - Consumption ID\n   * @param {string} foodId - Food ID\n   * @param {number} quantity - Quantity\n   * @param {string} unit - Unit\n   * @returns {Promise<Object>} - Created consumption item\n   */\n  async addItem(consumptionId, foodId, quantity, unit = 'g') {\n    const now = new Date().toISOString();\n    const id = uuidv4();\n\n    await dbManager.executeQuery(\n      `INSERT INTO ConsumptionItem (id, consumption_id, food_id, quantity, unit, created_at, updated_at, is_active)\n       VALUES (?, ?, ?, ?, ?, ?, ?, 1)`,\n      [id, consumptionId, foodId, quantity, unit, now, now]\n    );\n\n    const result = await dbManager.executeQuery(\n      `SELECT ci.*, f.name as food_name\n       FROM ConsumptionItem ci\n       JOIN Food f ON ci.food_id = f.id\n       WHERE ci.id = ?`,\n      [id]\n    );\n\n    return result.rows.length > 0 ? result.rows.item(0) : null;\n  }\n\n  /**\n   * Get consumptions by date\n   * @param {string} userId - User ID\n   * @param {string} date - Date (YYYY-MM-DD)\n   * @returns {Promise<Array>} - Array of consumptions\n   */\n  async getByDate(userId, date) {\n    const result = await dbManager.executeQuery(\n      `SELECT c.*\n       FROM Consumption c\n       WHERE c.user_id = ? AND c.consumption_date = ? AND c.is_active = 1 AND c.deleted_at IS NULL\n       ORDER BY c.meal_type`,\n      [userId, date]\n    );\n\n    const consumptions = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      const consumption = result.rows.item(i);\n      consumption.items = await this.getItems(consumption.id);\n      consumptions.push(consumption);\n    }\n\n    return consumptions;\n  }\n\n  /**\n   * Get daily nutrition summary\n   * @param {string} userId - User ID\n   * @param {string} date - Date (YYYY-MM-DD)\n   * @returns {Promise<Object>} - Nutrition summary\n   */\n  async getDailyNutritionSummary(userId, date) {\n    const result = await dbManager.executeQuery(\n      `SELECT\n         n.id as nutrient_id,\n         n.name as nutrient_name,\n         n.unit,\n         SUM(fn.amount * ci.quantity / 100) as total_amount,\n         n.daily_value,\n         (SUM(fn.amount * ci.quantity / 100) / n.daily_value * 100) as percent_of_daily_value\n       FROM\n         Consumption c\n       JOIN\n         ConsumptionItem ci ON c.id = ci.consumption_id\n       JOIN\n         Food f ON ci.food_id = f.id\n       JOIN\n         FoodNutrient fn ON f.id = fn.food_id\n       JOIN\n         Nutrient n ON fn.nutrient_id = n.id\n       WHERE\n         c.user_id = ? AND c.consumption_date = ?\n         AND c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1 AND n.is_active = 1\n       GROUP BY\n         n.id`,\n      [userId, date]\n    );\n\n    const nutrients = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      nutrients.push(result.rows.item(i));\n    }\n\n    // Get meal type summary\n    const mealResult = await dbManager.executeQuery(\n      `SELECT\n         c.meal_type,\n         SUM(fn.amount * ci.quantity / 100) as calories\n       FROM\n         Consumption c\n       JOIN\n         ConsumptionItem ci ON c.id = ci.consumption_id\n       JOIN\n         Food f ON ci.food_id = f.id\n       JOIN\n         FoodNutrient fn ON f.id = fn.food_id\n       WHERE\n         c.user_id = ? AND c.consumption_date = ? AND fn.nutrient_id = 'nutrient-calories'\n         AND c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1\n       GROUP BY\n         c.meal_type`,\n      [userId, date]\n    );\n\n    const meals = [];\n    for (let i = 0; i < mealResult.rows.length; i++) {\n      meals.push(mealResult.rows.item(i));\n    }\n\n    return {\n      date,\n      nutrients,\n      meals,\n      totalCalories: meals.reduce((sum, meal) => sum + meal.calories, 0)\n    };\n  }\n}\n\n/**\n * Nutrient data access\n */\nclass NutrientDataAccess extends BaseDataAccess {\n  constructor() {\n    super('Nutrient', 'id');\n  }\n\n  /**\n   * Get all macronutrients\n   * @returns {Promise<Array>} - Array of macronutrients\n   */\n  async getMacronutrients() {\n    const result = await dbManager.executeQuery(\n      'SELECT * FROM Nutrient WHERE is_macro = 1 AND is_active = 1 AND deleted_at IS NULL'\n    );\n\n    const nutrients = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      nutrients.push(result.rows.item(i));\n    }\n\n    return nutrients;\n  }\n}\n\n/**\n * Ingredient data access\n */\nclass IngredientDataAccess extends BaseDataAccess {\n  constructor() {\n    super('Ingredient', 'id');\n  }\n\n  /**\n   * Get all allergens\n   * @returns {Promise<Array>} - Array of allergens\n   */\n  async getAllergens() {\n    const result = await dbManager.executeQuery(\n      'SELECT * FROM Ingredient WHERE is_allergen = 1 AND is_active = 1 AND deleted_at IS NULL'\n    );\n\n    const allergens = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      allergens.push(result.rows.item(i));\n    }\n\n    return allergens;\n  }\n}\n\n/**\n * MealType data access\n */\nclass MealTypeDataAccess extends BaseDataAccess {\n  constructor() {\n    super('MealType', 'id');\n  }\n\n  /**\n   * Get all meal types ordered by display order\n   * @returns {Promise<Array>} - Array of meal types\n   */\n  async getAllOrdered() {\n    const result = await dbManager.executeQuery(\n      'SELECT * FROM MealType WHERE is_active = 1 AND deleted_at IS NULL ORDER BY display_order'\n    );\n\n    const mealTypes = [];\n    for (let i = 0; i < result.rows.length; i++) {\n      mealTypes.push(result.rows.item(i));\n    }\n\n    return mealTypes;\n  }\n}\n\n// Export data access instances\nexport const userDataAccess = new UserDataAccess();\nexport const foodDataAccess = new FoodDataAccess();\nexport const consumptionDataAccess = new ConsumptionDataAccess();\nexport const nutrientDataAccess = new NutrientDataAccess();\nexport const ingredientDataAccess = new IngredientDataAccess();\nexport const mealTypeDataAccess = new MealTypeDataAccess();\n\n// Export the base class for extension\nexport { BaseDataAccess };\n"], "mappings": ";;;;;;;;;;;;;AAKA,OAAOA,SAAS;AAChB,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,IAK9BC,cAAc;EAMlB,SAAAA,eAAYC,SAAS,EAAkB;IAAA,IAAhBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAAAG,eAAA,OAAAN,cAAA;IACnC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;EAAC,OAAAK,YAAA,CAAAP,cAAA;IAAAQ,GAAA;IAAAC,KAAA;MAAA,IAAAC,OAAA,GAAAC,iBAAA,CAWD,aAA2B;QAAA,IAAdC,OAAO,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;QACvB,IAAQU,KAAK,GAA6BD,OAAO,CAAzCC,KAAK;UAAEC,MAAM,GAAqBF,OAAO,CAAlCE,MAAM;UAAEC,OAAO,GAAYH,OAAO,CAA1BG,OAAO;UAAEC,KAAK,GAAKJ,OAAO,CAAjBI,KAAK;QAErC,IAAIC,KAAK,GAAG,iBAAiB,IAAI,CAAChB,SAAS,6CAA6C;QAExF,IAAIc,OAAO,EAAE;UACXE,KAAK,IAAI,aAAaF,OAAO,IAAIC,KAAK,IAAI,KAAK,EAAE;QACnD;QAEA,IAAIH,KAAK,EAAE;UACTI,KAAK,IAAI,UAAUJ,KAAK,EAAE;UAE1B,IAAIC,MAAM,EAAE;YACVG,KAAK,IAAI,WAAWH,MAAM,EAAE;UAC9B;QACF;QAEA,IAAMI,MAAM,SAASrB,SAAS,CAACsB,YAAY,CAACF,KAAK,CAAC;QAElD,IAAMG,KAAK,GAAG,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CD,KAAK,CAACG,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACjC;QAEA,OAAOD,KAAK;MACd,CAAC;MAAA,SAzBKK,MAAMA,CAAA;QAAA,OAAAf,OAAA,CAAAgB,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAANsB,MAAM;IAAA;EAAA;IAAAjB,GAAA;IAAAC,KAAA;MAAA,IAAAkB,QAAA,GAAAhB,iBAAA,CAgCZ,WAAciB,EAAE,EAAE;QAChB,IAAMV,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,iBAAiB,IAAI,CAAClB,SAAS,UAAU,IAAI,CAACC,OAAO,+CAA+C,EACpG,CAAC0B,EAAE,CACL,CAAC;QAED,OAAOV,MAAM,CAACI,IAAI,CAAClB,MAAM,GAAG,CAAC,GAAGc,MAAM,CAACI,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;MAC5D,CAAC;MAAA,SAPKK,OAAOA,CAAAC,EAAA;QAAA,OAAAH,QAAA,CAAAD,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAP0B,OAAO;IAAA;EAAA;IAAArB,GAAA;IAAAC,KAAA;MAAA,IAAAsB,OAAA,GAAApB,iBAAA,CAcb,WAAaqB,IAAI,EAAE;QAEjB,IAAMC,UAAU,GAAAC,aAAA,KAAQF,IAAI,CAAE;QAC9B,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC/B,OAAO,CAAC,EAAE;UAC7B+B,UAAU,CAAC,IAAI,CAAC/B,OAAO,CAAC,GAAGH,MAAM,CAAC,CAAC;QACrC;QAGA,IAAMoC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpC,IAAI,CAACJ,UAAU,CAACK,UAAU,EAAE;UAC1BL,UAAU,CAACK,UAAU,GAAGH,GAAG;QAC7B;QACA,IAAI,CAACF,UAAU,CAACM,UAAU,EAAE;UAC1BN,UAAU,CAACM,UAAU,GAAGJ,GAAG;QAC7B;QAGA,IAAIF,UAAU,CAACO,SAAS,KAAKnC,SAAS,EAAE;UACtC4B,UAAU,CAACO,SAAS,GAAG,CAAC;QAC1B;QAEA,IAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACV,UAAU,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC;QAClD,IAAMC,YAAY,GAAGH,MAAM,CAACC,IAAI,CAACV,UAAU,CAAC,CAACa,GAAG,CAAC;UAAA,OAAM,GAAG;QAAA,EAAC,CAACF,IAAI,CAAC,IAAI,CAAC;QACtE,IAAMG,MAAM,GAAGL,MAAM,CAACK,MAAM,CAACd,UAAU,CAAC;QAExC,MAAMpC,SAAS,CAACsB,YAAY,CAC1B,eAAe,IAAI,CAAClB,SAAS,KAAKwC,OAAO,aAAaI,YAAY,GAAG,EACrEE,MACF,CAAC;QAED,OAAO,IAAI,CAAClB,OAAO,CAACI,UAAU,CAAC,IAAI,CAAC/B,OAAO,CAAC,CAAC;MAC/C,CAAC;MAAA,SA/BK8C,MAAMA,CAAAC,GAAA;QAAA,OAAAlB,OAAA,CAAAL,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAN6C,MAAM;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA;MAAA,IAAAyC,OAAA,GAAAvC,iBAAA,CAuCZ,WAAaiB,EAAE,EAAEI,IAAI,EAAE;QAErB,IAAMC,UAAU,GAAAC,aAAA,KAAQF,IAAI,CAAE;QAC9B,IAAI,CAACC,UAAU,CAACM,UAAU,EAAE;UAC1BN,UAAU,CAACM,UAAU,GAAG,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAClD;QAGA,OAAOJ,UAAU,CAAC,IAAI,CAAC/B,OAAO,CAAC;QAE/B,IAAMiD,SAAS,GAAGT,MAAM,CAACC,IAAI,CAACV,UAAU,CAAC,CAACa,GAAG,CAAC,UAAAtC,GAAG;UAAA,OAAI,GAAGA,GAAG,MAAM;QAAA,EAAC,CAACoC,IAAI,CAAC,IAAI,CAAC;QAC7E,IAAMG,MAAM,MAAAK,MAAA,CAAAC,kBAAA,CAAOX,MAAM,CAACK,MAAM,CAACd,UAAU,CAAC,IAAEL,EAAE,EAAC;QAEjD,MAAM/B,SAAS,CAACsB,YAAY,CAC1B,UAAU,IAAI,CAAClB,SAAS,QAAQkD,SAAS,UAAU,IAAI,CAACjD,OAAO,MAAM,EACrE6C,MACF,CAAC;QAED,OAAO,IAAI,CAAClB,OAAO,CAACD,EAAE,CAAC;MACzB,CAAC;MAAA,SAnBK0B,MAAMA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAN,OAAA,CAAAxB,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAANmD,MAAM;IAAA;EAAA;IAAA9C,GAAA;IAAAC,KAAA;MAAA,IAAAgD,QAAA,GAAA9C,iBAAA,CA0BZ,WAAaiB,EAAE,EAAE;QACf,IAAMV,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,UAAU,IAAI,CAAClB,SAAS,0DAA0D,IAAI,CAACC,OAAO,MAAM,EACpG,CAAC0B,EAAE,CACL,CAAC;QAED,OAAOV,MAAM,CAACwC,YAAY,GAAG,CAAC;MAChC,CAAC;MAAA,SAPKC,OAAMA,CAAAC,GAAA;QAAA,OAAAH,QAAA,CAAA/B,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAANwD,OAAM;IAAA;EAAA;IAAAnD,GAAA;IAAAC,KAAA;MAAA,IAAAoD,OAAA,GAAAlD,iBAAA,CAgBZ,WAAamD,KAAK,EAAErD,KAAK,EAAgB;QAAA,IAAdG,OAAO,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;QACrC,IAAQU,KAAK,GAA6BD,OAAO,CAAzCC,KAAK;UAAEC,MAAM,GAAqBF,OAAO,CAAlCE,MAAM;UAAEC,OAAO,GAAYH,OAAO,CAA1BG,OAAO;UAAEC,KAAK,GAAKJ,OAAO,CAAjBI,KAAK;QAErC,IAAIC,KAAK,GAAG,iBAAiB,IAAI,CAAChB,SAAS,UAAU6D,KAAK,+CAA+C;QAEzG,IAAI/C,OAAO,EAAE;UACXE,KAAK,IAAI,aAAaF,OAAO,IAAIC,KAAK,IAAI,KAAK,EAAE;QACnD;QAEA,IAAIH,KAAK,EAAE;UACTI,KAAK,IAAI,UAAUJ,KAAK,EAAE;UAE1B,IAAIC,MAAM,EAAE;YACVG,KAAK,IAAI,WAAWH,MAAM,EAAE;UAC9B;QACF;QAEA,IAAMI,MAAM,SAASrB,SAAS,CAACsB,YAAY,CAACF,KAAK,EAAE,CAACR,KAAK,CAAC,CAAC;QAE3D,IAAMW,KAAK,GAAG,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CD,KAAK,CAACG,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACjC;QAEA,OAAOD,KAAK;MACd,CAAC;MAAA,SAzBK2C,MAAMA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,OAAA,CAAAnC,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAN4D,MAAM;IAAA;EAAA;IAAAvD,GAAA;IAAAC,KAAA;MAAA,IAAAyD,OAAA,GAAAvD,iBAAA,CAkCZ,WAAamD,KAAK,EAAE7C,KAAK,EAAgB;QAAA,IAAdL,OAAO,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;QACrC,IAAQU,KAAK,GAA6BD,OAAO,CAAzCC,KAAK;UAAEC,MAAM,GAAqBF,OAAO,CAAlCE,MAAM;UAAEC,OAAO,GAAYH,OAAO,CAA1BG,OAAO;UAAEC,KAAK,GAAKJ,OAAO,CAAjBI,KAAK;QAErC,IAAImD,QAAQ,GAAG,iBAAiB,IAAI,CAAClE,SAAS,UAAU6D,KAAK,kDAAkD;QAE/G,IAAI/C,OAAO,EAAE;UACXoD,QAAQ,IAAI,aAAapD,OAAO,IAAIC,KAAK,IAAI,KAAK,EAAE;QACtD;QAEA,IAAIH,KAAK,EAAE;UACTsD,QAAQ,IAAI,UAAUtD,KAAK,EAAE;UAE7B,IAAIC,MAAM,EAAE;YACVqD,QAAQ,IAAI,WAAWrD,MAAM,EAAE;UACjC;QACF;QAEA,IAAMI,MAAM,SAASrB,SAAS,CAACsB,YAAY,CAACgD,QAAQ,EAAE,CAAC,IAAIlD,KAAK,GAAG,CAAC,CAAC;QAErE,IAAMG,KAAK,GAAG,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CD,KAAK,CAACG,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACjC;QAEA,OAAOD,KAAK;MACd,CAAC;MAAA,SAzBKgD,MAAMA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,OAAA,CAAAxC,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAANiE,MAAM;IAAA;EAAA;IAAA5D,GAAA;IAAAC,KAAA;MAAA,IAAA8D,MAAA,GAAA5D,iBAAA,CAgCZ,aAA6B;QAAA,IAAjB6D,UAAU,GAAArE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;QACzB,IAAIsE,WAAW,GAAG,sCAAsC;QACxD,IAAMC,MAAM,GAAG,EAAE;QAEjBhC,MAAM,CAACiC,OAAO,CAACH,UAAU,CAAC,CAACI,OAAO,CAAC,UAAAC,IAAA,EAAkB;UAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA;YAAhBrE,GAAG,GAAAsE,KAAA;YAAErE,KAAK,GAAAqE,KAAA;UAC7CL,WAAW,IAAI,QAAQjE,GAAG,MAAM;UAChCkE,MAAM,CAACnD,IAAI,CAACd,KAAK,CAAC;QACpB,CAAC,CAAC;QAEF,IAAMS,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,iCAAiC,IAAI,CAAClB,SAAS,UAAUwE,WAAW,EAAE,EACtEC,MACF,CAAC;QAED,OAAOxD,MAAM,CAACI,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAACwD,KAAK;MAClC,CAAC;MAAA,SAfKA,KAAKA,CAAA;QAAA,OAAAT,MAAA,CAAA7C,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAL6E,KAAK;IAAA;EAAA;AAAA;AAAA,IAqBPC,cAAc,aAAAC,eAAA;EAClB,SAAAD,eAAA,EAAc;IAAA3E,eAAA,OAAA2E,cAAA;IAAA,OAAAE,UAAA,OAAAF,cAAA,GACN,MAAM,EAAE,IAAI;EACpB;EAACG,SAAA,CAAAH,cAAA,EAAAC,eAAA;EAAA,OAAA3E,YAAA,CAAA0E,cAAA;IAAAzE,GAAA;IAAAC,KAAA;MAAA,IAAA4E,YAAA,GAAA1E,iBAAA,CAOD,WAAkB2E,MAAM,EAAE;QACxB,IAAMpE,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,wFAAwF,EACxF,CAACmE,MAAM,CACT,CAAC;QAED,IAAMC,QAAQ,GAAG,CAAC,CAAC;QACnB,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3C,IAAMmE,OAAO,GAAGtE,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC;UACnCkE,QAAQ,CAACC,OAAO,CAACC,WAAW,CAAC,GAAGD,OAAO,CAACE,aAAa;QACvD;QAEA,OAAOH,QAAQ;MACjB,CAAC;MAAA,SAbKI,WAAWA,CAAAC,GAAA;QAAA,OAAAP,YAAA,CAAA3D,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAXwF,WAAW;IAAA;EAAA;IAAAnF,GAAA;IAAAC,KAAA;MAAA,IAAAoF,YAAA,GAAAlF,iBAAA,CAsBjB,WAAkB2E,MAAM,EAAE9E,GAAG,EAAEC,KAAK,EAAE;QACpC,IAAM0B,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAGpC,IAAMnB,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,kEAAkE,EAClE,CAACmE,MAAM,EAAE9E,GAAG,CACd,CAAC;QAED,IAAIU,MAAM,CAACI,IAAI,CAAClB,MAAM,GAAG,CAAC,EAAE;UAE1B,MAAMP,SAAS,CAACsB,YAAY,CAC1B,gGAAgG,EAChG,CAACV,KAAK,EAAE0B,GAAG,EAAEmD,MAAM,EAAE9E,GAAG,CAC1B,CAAC;QACH,CAAC,MAAM;UAEL,IAAMsF,SAAS,GAAG/F,MAAM,CAAC,CAAC;UAC1B,MAAMF,SAAS,CAACsB,YAAY,CAC1B,qHAAqH,EACrH,CAAC2E,SAAS,EAAER,MAAM,EAAE9E,GAAG,EAAEC,KAAK,EAAE0B,GAAG,EAAEA,GAAG,CAC1C,CAAC;QACH;MACF,CAAC;MAAA,SAvBK4D,WAAWA,CAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAL,YAAA,CAAAnE,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAX4F,WAAW;IAAA;EAAA;IAAAvF,GAAA;IAAAC,KAAA;MAAA,IAAA0F,eAAA,GAAAxF,iBAAA,CA6BjB,aAAuB;QACrB,IAAMO,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,gDACF,CAAC;QAED,IAAID,MAAM,CAACI,IAAI,CAAClB,MAAM,KAAK,CAAC,EAAE;UAC5B,OAAO,IAAI;QACb;QAEA,IAAMgG,IAAI,GAAGlF,MAAM,CAACI,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC;QAChC4E,IAAI,CAACb,QAAQ,SAAS,IAAI,CAACI,WAAW,CAACS,IAAI,CAACxE,EAAE,CAAC;QAE/C,OAAOwE,IAAI;MACb,CAAC;MAAA,SAbKC,cAAcA,CAAA;QAAA,OAAAF,eAAA,CAAAzE,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAdkG,cAAc;IAAA;EAAA;AAAA,EA7DOrG,cAAc;AAAA,IAgFrCsG,cAAc,aAAAC,gBAAA;EAClB,SAAAD,eAAA,EAAc;IAAAhG,eAAA,OAAAgG,cAAA;IAAA,OAAAnB,UAAA,OAAAmB,cAAA,GACN,MAAM,EAAE,IAAI;EACpB;EAAClB,SAAA,CAAAkB,cAAA,EAAAC,gBAAA;EAAA,OAAAhG,YAAA,CAAA+F,cAAA;IAAA9F,GAAA;IAAAC,KAAA;MAAA,IAAA+F,aAAA,GAAA7F,iBAAA,CAOD,WAAmB8F,OAAO,EAAE;QAC1B,IAAMvF,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,+EAA+E,EAC/E,CAACsF,OAAO,CACV,CAAC;QAED,OAAOvF,MAAM,CAACI,IAAI,CAAClB,MAAM,GAAG,CAAC,GAAGc,MAAM,CAACI,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;MAC5D,CAAC;MAAA,SAPKkF,YAAYA,CAAAC,IAAA;QAAA,OAAAH,aAAA,CAAA9E,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAZuG,YAAY;IAAA;EAAA;IAAAlG,GAAA;IAAAC,KAAA;MAAA,IAAAmG,aAAA,GAAAjG,iBAAA,CAclB,WAAmBkG,MAAM,EAAE;QACzB,IAAM3F,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC;AACN;AACA;AACA,qEAAqE,EAC/D,CAAC0F,MAAM,CACT,CAAC;QAED,IAAMC,SAAS,GAAG,EAAE;QACpB,KAAK,IAAIzF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CyF,SAAS,CAACvF,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACrC;QAEA,OAAOyF,SAAS;MAClB,CAAC;MAAA,SAfKC,YAAYA,CAAAC,IAAA;QAAA,OAAAJ,aAAA,CAAAlF,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAZ4G,YAAY;IAAA;EAAA;IAAAvG,GAAA;IAAAC,KAAA;MAAA,IAAAwG,aAAA,GAAAtG,iBAAA,CAwBlB,WAAmBkG,MAAM,EAAEK,UAAU,EAAEC,MAAM,EAAE;QAC7C,IAAMhF,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAGpC,IAAMnB,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,mEAAmE,EACnE,CAAC0F,MAAM,EAAEK,UAAU,CACrB,CAAC;QAED,IAAIhG,MAAM,CAACI,IAAI,CAAClB,MAAM,GAAG,CAAC,EAAE;UAE1B,MAAMP,SAAS,CAACsB,YAAY,CAC1B,0FAA0F,EAC1F,CAACgG,MAAM,EAAEhF,GAAG,EAAE0E,MAAM,EAAEK,UAAU,CAClC,CAAC;QACH,CAAC,MAAM;UAEL,IAAMtF,EAAE,GAAG7B,MAAM,CAAC,CAAC;UACnB,MAAMF,SAAS,CAACsB,YAAY,CAC1B,6HAA6H,EAC7H,CAACS,EAAE,EAAEiF,MAAM,EAAEK,UAAU,EAAEC,MAAM,EAAEhF,GAAG,EAAEA,GAAG,CAC3C,CAAC;QACH;MACF,CAAC;MAAA,SAvBKiF,YAAYA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAN,aAAA,CAAAvF,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAZiH,YAAY;IAAA;EAAA;IAAA5G,GAAA;IAAAC,KAAA;MAAA,IAAA+G,eAAA,GAAA7G,iBAAA,CA8BlB,WAAqBkG,MAAM,EAAE;QAC3B,IAAM3F,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC;AACN;AACA;AACA;AACA,6BAA6B,EACvB,CAAC0F,MAAM,CACT,CAAC;QAED,IAAMY,WAAW,GAAG,EAAE;QACtB,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CoG,WAAW,CAAClG,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACvC;QAEA,OAAOoG,WAAW;MACpB,CAAC;MAAA,SAhBKC,cAAcA,CAAAC,IAAA;QAAA,OAAAH,eAAA,CAAA9F,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAduH,cAAc;IAAA;EAAA;IAAAlH,GAAA;IAAAC,KAAA;MAAA,IAAAmH,YAAA,GAAAjH,iBAAA,CAyBpB,WAAkBkH,MAAM,EAA0B;QAAA,IAAxBhH,KAAK,GAAAV,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;QAAA,IAAEW,MAAM,GAAAX,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;QAC9C,IAAMe,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC;AACN;AACA;AACA,wBAAwB,EAClB,CAAC,GAAG0G,MAAM,GAAG,EAAEhH,KAAK,EAAEC,MAAM,CAC9B,CAAC;QAED,IAAMgH,KAAK,GAAG,EAAE;QAChB,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3C,IAAM0G,IAAI,GAAG7G,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC;UAChC0G,IAAI,CAACjB,SAAS,SAAS,IAAI,CAACC,YAAY,CAACgB,IAAI,CAACnG,EAAE,CAAC;UACjDmG,IAAI,CAACN,WAAW,SAAS,IAAI,CAACC,cAAc,CAACK,IAAI,CAACnG,EAAE,CAAC;UACrDkG,KAAK,CAACvG,IAAI,CAACwG,IAAI,CAAC;QAClB;QAEA,OAAOD,KAAK;MACd,CAAC;MAAA,SAlBKE,WAAWA,CAAAC,IAAA;QAAA,OAAAL,YAAA,CAAAlG,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAX6H,WAAW;IAAA;EAAA;AAAA,EAvGUhI,cAAc;AAAA,IA+HrCkI,qBAAqB,aAAAC,gBAAA;EACzB,SAAAD,sBAAA,EAAc;IAAA5H,eAAA,OAAA4H,qBAAA;IAAA,OAAA/C,UAAA,OAAA+C,qBAAA,GACN,aAAa,EAAE,IAAI;EAC3B;EAAC9C,SAAA,CAAA8C,qBAAA,EAAAC,gBAAA;EAAA,OAAA5H,YAAA,CAAA2H,qBAAA;IAAA1H,GAAA;IAAAC,KAAA;MAAA,IAAA2H,SAAA,GAAAzH,iBAAA,CAOD,WAAe0H,aAAa,EAAE;QAC5B,IAAMnH,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC;AACN;AACA;AACA,4EAA4E,EACtE,CAACkH,aAAa,CAChB,CAAC;QAED,IAAMjH,KAAK,GAAG,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CD,KAAK,CAACG,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACjC;QAEA,OAAOD,KAAK;MACd,CAAC;MAAA,SAfKkH,QAAQA,CAAAC,IAAA;QAAA,OAAAH,SAAA,CAAA1G,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAARmI,QAAQ;IAAA;EAAA;IAAA9H,GAAA;IAAAC,KAAA;MAAA,IAAA+H,QAAA,GAAA7H,iBAAA,CAyBd,WAAc0H,aAAa,EAAExB,MAAM,EAAE4B,QAAQ,EAAc;QAAA,IAAZC,IAAI,GAAAvI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;QACvD,IAAMgC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpC,IAAMT,EAAE,GAAG7B,MAAM,CAAC,CAAC;QAEnB,MAAMF,SAAS,CAACsB,YAAY,CAC1B;AACN,uCAAuC,EACjC,CAACS,EAAE,EAAEyG,aAAa,EAAExB,MAAM,EAAE4B,QAAQ,EAAEC,IAAI,EAAEvG,GAAG,EAAEA,GAAG,CACtD,CAAC;QAED,IAAMjB,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC;AACN;AACA;AACA,uBAAuB,EACjB,CAACS,EAAE,CACL,CAAC;QAED,OAAOV,MAAM,CAACI,IAAI,CAAClB,MAAM,GAAG,CAAC,GAAGc,MAAM,CAACI,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;MAC5D,CAAC;MAAA,SAnBKmH,OAAOA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAN,QAAA,CAAA9G,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAPwI,OAAO;IAAA;EAAA;IAAAnI,GAAA;IAAAC,KAAA;MAAA,IAAAsI,UAAA,GAAApI,iBAAA,CA2Bb,WAAgB2E,MAAM,EAAE0D,IAAI,EAAE;QAC5B,IAAM9H,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC;AACN;AACA;AACA,4BAA4B,EACtB,CAACmE,MAAM,EAAE0D,IAAI,CACf,CAAC;QAED,IAAMC,YAAY,GAAG,EAAE;QACvB,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3C,IAAM6H,WAAW,GAAGhI,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC;UACvC6H,WAAW,CAAC9H,KAAK,SAAS,IAAI,CAACkH,QAAQ,CAACY,WAAW,CAACtH,EAAE,CAAC;UACvDqH,YAAY,CAAC1H,IAAI,CAAC2H,WAAW,CAAC;QAChC;QAEA,OAAOD,YAAY;MACrB,CAAC;MAAA,SAjBKE,SAASA,CAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAN,UAAA,CAAArH,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAATgJ,SAAS;IAAA;EAAA;IAAA3I,GAAA;IAAAC,KAAA;MAAA,IAAA6I,yBAAA,GAAA3I,iBAAA,CAyBf,WAA+B2E,MAAM,EAAE0D,IAAI,EAAE;QAC3C,IAAM9H,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,EACR,CAACmE,MAAM,EAAE0D,IAAI,CACf,CAAC;QAED,IAAMlC,SAAS,GAAG,EAAE;QACpB,KAAK,IAAIzF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CyF,SAAS,CAACvF,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACrC;QAGA,IAAMkI,UAAU,SAAS1J,SAAS,CAACsB,YAAY,CAC7C;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,EACf,CAACmE,MAAM,EAAE0D,IAAI,CACf,CAAC;QAED,IAAMQ,KAAK,GAAG,EAAE;QAChB,KAAK,IAAInI,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGkI,UAAU,CAACjI,IAAI,CAAClB,MAAM,EAAEiB,EAAC,EAAE,EAAE;UAC/CmI,KAAK,CAACjI,IAAI,CAACgI,UAAU,CAACjI,IAAI,CAACE,IAAI,CAACH,EAAC,CAAC,CAAC;QACrC;QAEA,OAAO;UACL2H,IAAI,EAAJA,IAAI;UACJlC,SAAS,EAATA,SAAS;UACT0C,KAAK,EAALA,KAAK;UACLC,aAAa,EAAED,KAAK,CAACE,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;YAAA,OAAKD,GAAG,GAAGC,IAAI,CAACC,QAAQ;UAAA,GAAE,CAAC;QACnE,CAAC;MACH,CAAC;MAAA,SAhEKC,wBAAwBA,CAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAV,yBAAA,CAAA5H,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAxB2J,wBAAwB;IAAA;EAAA;AAAA,EAvFI9J,cAAc;AAAA,IA6J5CiK,kBAAkB,aAAAC,gBAAA;EACtB,SAAAD,mBAAA,EAAc;IAAA3J,eAAA,OAAA2J,kBAAA;IAAA,OAAA9E,UAAA,OAAA8E,kBAAA,GACN,UAAU,EAAE,IAAI;EACxB;EAAC7E,SAAA,CAAA6E,kBAAA,EAAAC,gBAAA;EAAA,OAAA3J,YAAA,CAAA0J,kBAAA;IAAAzJ,GAAA;IAAAC,KAAA;MAAA,IAAA0J,kBAAA,GAAAxJ,iBAAA,CAMD,aAA0B;QACxB,IAAMO,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,oFACF,CAAC;QAED,IAAM2F,SAAS,GAAG,EAAE;QACpB,KAAK,IAAIzF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CyF,SAAS,CAACvF,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACrC;QAEA,OAAOyF,SAAS;MAClB,CAAC;MAAA,SAXKsD,iBAAiBA,CAAA;QAAA,OAAAD,kBAAA,CAAAzI,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAjBiK,iBAAiB;IAAA;EAAA;AAAA,EATQpK,cAAc;AAAA,IA0BzCqK,oBAAoB,aAAAC,gBAAA;EACxB,SAAAD,qBAAA,EAAc;IAAA/J,eAAA,OAAA+J,oBAAA;IAAA,OAAAlF,UAAA,OAAAkF,oBAAA,GACN,YAAY,EAAE,IAAI;EAC1B;EAACjF,SAAA,CAAAiF,oBAAA,EAAAC,gBAAA;EAAA,OAAA/J,YAAA,CAAA8J,oBAAA;IAAA7J,GAAA;IAAAC,KAAA;MAAA,IAAA8J,aAAA,GAAA5J,iBAAA,CAMD,aAAqB;QACnB,IAAMO,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,yFACF,CAAC;QAED,IAAMqJ,SAAS,GAAG,EAAE;QACpB,KAAK,IAAInJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CmJ,SAAS,CAACjJ,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACrC;QAEA,OAAOmJ,SAAS;MAClB,CAAC;MAAA,SAXKC,YAAYA,CAAA;QAAA,OAAAF,aAAA,CAAA7I,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAZsK,YAAY;IAAA;EAAA;AAAA,EATezK,cAAc;AAAA,IA0B3C0K,kBAAkB,aAAAC,gBAAA;EACtB,SAAAD,mBAAA,EAAc;IAAApK,eAAA,OAAAoK,kBAAA;IAAA,OAAAvF,UAAA,OAAAuF,kBAAA,GACN,UAAU,EAAE,IAAI;EACxB;EAACtF,SAAA,CAAAsF,kBAAA,EAAAC,gBAAA;EAAA,OAAApK,YAAA,CAAAmK,kBAAA;IAAAlK,GAAA;IAAAC,KAAA;MAAA,IAAAmK,cAAA,GAAAjK,iBAAA,CAMD,aAAsB;QACpB,IAAMO,MAAM,SAASrB,SAAS,CAACsB,YAAY,CACzC,0FACF,CAAC;QAED,IAAM0J,SAAS,GAAG,EAAE;QACpB,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAClB,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC3CwJ,SAAS,CAACtJ,IAAI,CAACL,MAAM,CAACI,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;QACrC;QAEA,OAAOwJ,SAAS;MAClB,CAAC;MAAA,SAXKC,aAAaA,CAAA;QAAA,OAAAF,cAAA,CAAAlJ,KAAA,OAAAvB,SAAA;MAAA;MAAA,OAAb2K,aAAa;IAAA;EAAA;AAAA,EATY9K,cAAc;AAwB/C,OAAO,IAAM+K,cAAc,GAAG,IAAI9F,cAAc,CAAC,CAAC;AAClD,OAAO,IAAM+F,cAAc,GAAG,IAAI1E,cAAc,CAAC,CAAC;AAClD,OAAO,IAAM2E,qBAAqB,GAAG,IAAI/C,qBAAqB,CAAC,CAAC;AAChE,OAAO,IAAMgD,kBAAkB,GAAG,IAAIjB,kBAAkB,CAAC,CAAC;AAC1D,OAAO,IAAMkB,oBAAoB,GAAG,IAAId,oBAAoB,CAAC,CAAC;AAC9D,OAAO,IAAMe,kBAAkB,GAAG,IAAIV,kBAAkB,CAAC,CAAC;AAG1D,SAAS1K,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}