{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport AnimatedNode from \"./AnimatedNode\";\nimport AnimatedTransform from \"./AnimatedTransform\";\nimport AnimatedWithChildren from \"./AnimatedWithChildren\";\nimport NativeAnimatedHelper from \"../NativeAnimatedHelper\";\nimport StyleSheet from \"../../../../exports/StyleSheet\";\nvar flattenStyle = StyleSheet.flatten;\nfunction createAnimatedStyle(inputStyle) {\n  var style = flattenStyle(inputStyle);\n  var animatedStyles = {};\n  for (var key in style) {\n    var value = style[key];\n    if (key === 'transform' && Array.isArray(value)) {\n      animatedStyles[key] = new AnimatedTransform(value);\n    } else if (value instanceof AnimatedNode) {\n      animatedStyles[key] = value;\n    } else if (value && !Array.isArray(value) && typeof value === 'object') {\n      animatedStyles[key] = createAnimatedStyle(value);\n    }\n  }\n  return animatedStyles;\n}\nvar AnimatedStyle = function (_AnimatedWithChildren) {\n  function AnimatedStyle(style) {\n    var _this;\n    _classCallCheck(this, AnimatedStyle);\n    _this = _callSuper(this, AnimatedStyle);\n    _this._inputStyle = style;\n    _this._style = createAnimatedStyle(style);\n    return _this;\n  }\n  _inherits(AnimatedStyle, _AnimatedWithChildren);\n  return _createClass(AnimatedStyle, [{\n    key: \"_walkStyleAndGetValues\",\n    value: function _walkStyleAndGetValues(style) {\n      var updatedStyle = {};\n      for (var key in style) {\n        var value = style[key];\n        if (value instanceof AnimatedNode) {\n          if (!value.__isNative) {\n            updatedStyle[key] = value.__getValue();\n          }\n        } else if (value && !Array.isArray(value) && typeof value === 'object') {\n          updatedStyle[key] = this._walkStyleAndGetValues(value);\n        } else {\n          updatedStyle[key] = value;\n        }\n      }\n      return updatedStyle;\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      return [this._inputStyle, this._walkStyleAndGetValues(this._style)];\n    }\n  }, {\n    key: \"_walkStyleAndGetAnimatedValues\",\n    value: function _walkStyleAndGetAnimatedValues(style) {\n      var updatedStyle = {};\n      for (var key in style) {\n        var value = style[key];\n        if (value instanceof AnimatedNode) {\n          updatedStyle[key] = value.__getAnimatedValue();\n        } else if (value && !Array.isArray(value) && typeof value === 'object') {\n          updatedStyle[key] = this._walkStyleAndGetAnimatedValues(value);\n        }\n      }\n      return updatedStyle;\n    }\n  }, {\n    key: \"__getAnimatedValue\",\n    value: function __getAnimatedValue() {\n      return this._walkStyleAndGetAnimatedValues(this._style);\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      for (var key in this._style) {\n        var value = this._style[key];\n        if (value instanceof AnimatedNode) {\n          value.__addChild(this);\n        }\n      }\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      for (var key in this._style) {\n        var value = this._style[key];\n        if (value instanceof AnimatedNode) {\n          value.__removeChild(this);\n        }\n      }\n      _superPropGet(AnimatedStyle, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"__makeNative\",\n    value: function __makeNative() {\n      for (var key in this._style) {\n        var value = this._style[key];\n        if (value instanceof AnimatedNode) {\n          value.__makeNative();\n        }\n      }\n      _superPropGet(AnimatedStyle, \"__makeNative\", this, 3)([]);\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      var styleConfig = {};\n      for (var styleKey in this._style) {\n        if (this._style[styleKey] instanceof AnimatedNode) {\n          var style = this._style[styleKey];\n          style.__makeNative();\n          styleConfig[styleKey] = style.__getNativeTag();\n        }\n      }\n      NativeAnimatedHelper.validateStyles(styleConfig);\n      return {\n        type: 'style',\n        style: styleConfig\n      };\n    }\n  }]);\n}(AnimatedWithChildren);\nexport default AnimatedStyle;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedNode", "AnimatedTransform", "AnimatedWithChildren", "NativeAnimatedHelper", "StyleSheet", "flattenStyle", "flatten", "createAnimatedStyle", "inputStyle", "style", "animatedStyles", "key", "value", "Array", "isArray", "AnimatedStyle", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "_this", "_inputStyle", "_style", "_walkStyleAndGetValues", "updatedStyle", "__isNative", "__getValue", "_walkStyleAndGetAnimatedValues", "__getAnimatedValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__makeNative", "__getNativeConfig", "styleConfig", "styleKey", "__getNativeTag", "validateStyles", "type"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedStyle.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedTransform from './AnimatedTransform';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nimport NativeAnimatedHelper from '../NativeAnimatedHelper';\nimport StyleSheet from '../../../../exports/StyleSheet';\nvar flattenStyle = StyleSheet.flatten;\nfunction createAnimatedStyle(inputStyle) {\n  var style = flattenStyle(inputStyle);\n  var animatedStyles = {};\n  for (var key in style) {\n    var value = style[key];\n    if (key === 'transform' && Array.isArray(value)) {\n      animatedStyles[key] = new AnimatedTransform(value);\n    } else if (value instanceof AnimatedNode) {\n      animatedStyles[key] = value;\n    } else if (value && !Array.isArray(value) && typeof value === 'object') {\n      animatedStyles[key] = createAnimatedStyle(value);\n    }\n  }\n  return animatedStyles;\n}\nclass AnimatedStyle extends AnimatedWithChildren {\n  constructor(style) {\n    super();\n    this._inputStyle = style;\n    this._style = createAnimatedStyle(style);\n  }\n\n  // Recursively get values for nested styles (like iOS's shadowOffset)\n  _walkStyleAndGetValues(style) {\n    var updatedStyle = {};\n    for (var key in style) {\n      var value = style[key];\n      if (value instanceof AnimatedNode) {\n        if (!value.__isNative) {\n          // We cannot use value of natively driven nodes this way as the value we have access from\n          // JS may not be up to date.\n          updatedStyle[key] = value.__getValue();\n        }\n      } else if (value && !Array.isArray(value) && typeof value === 'object') {\n        // Support animating nested values (for example: shadowOffset.height)\n        updatedStyle[key] = this._walkStyleAndGetValues(value);\n      } else {\n        updatedStyle[key] = value;\n      }\n    }\n    return updatedStyle;\n  }\n  __getValue() {\n    return [this._inputStyle, this._walkStyleAndGetValues(this._style)];\n  }\n\n  // Recursively get animated values for nested styles (like iOS's shadowOffset)\n  _walkStyleAndGetAnimatedValues(style) {\n    var updatedStyle = {};\n    for (var key in style) {\n      var value = style[key];\n      if (value instanceof AnimatedNode) {\n        updatedStyle[key] = value.__getAnimatedValue();\n      } else if (value && !Array.isArray(value) && typeof value === 'object') {\n        // Support animating nested values (for example: shadowOffset.height)\n        updatedStyle[key] = this._walkStyleAndGetAnimatedValues(value);\n      }\n    }\n    return updatedStyle;\n  }\n  __getAnimatedValue() {\n    return this._walkStyleAndGetAnimatedValues(this._style);\n  }\n  __attach() {\n    for (var key in this._style) {\n      var value = this._style[key];\n      if (value instanceof AnimatedNode) {\n        value.__addChild(this);\n      }\n    }\n  }\n  __detach() {\n    for (var key in this._style) {\n      var value = this._style[key];\n      if (value instanceof AnimatedNode) {\n        value.__removeChild(this);\n      }\n    }\n    super.__detach();\n  }\n  __makeNative() {\n    for (var key in this._style) {\n      var value = this._style[key];\n      if (value instanceof AnimatedNode) {\n        value.__makeNative();\n      }\n    }\n    super.__makeNative();\n  }\n  __getNativeConfig() {\n    var styleConfig = {};\n    for (var styleKey in this._style) {\n      if (this._style[styleKey] instanceof AnimatedNode) {\n        var style = this._style[styleKey];\n        style.__makeNative();\n        styleConfig[styleKey] = style.__getNativeTag();\n      }\n    }\n    NativeAnimatedHelper.validateStyles(styleConfig);\n    return {\n      type: 'style',\n      style: styleConfig\n    };\n  }\n}\nexport default AnimatedStyle;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,YAAY;AACnB,OAAOC,iBAAiB;AACxB,OAAOC,oBAAoB;AAC3B,OAAOC,oBAAoB;AAC3B,OAAOC,UAAU;AACjB,IAAIC,YAAY,GAAGD,UAAU,CAACE,OAAO;AACrC,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,IAAIC,KAAK,GAAGJ,YAAY,CAACG,UAAU,CAAC;EACpC,IAAIE,cAAc,GAAG,CAAC,CAAC;EACvB,KAAK,IAAIC,GAAG,IAAIF,KAAK,EAAE;IACrB,IAAIG,KAAK,GAAGH,KAAK,CAACE,GAAG,CAAC;IACtB,IAAIA,GAAG,KAAK,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MAC/CF,cAAc,CAACC,GAAG,CAAC,GAAG,IAAIV,iBAAiB,CAACW,KAAK,CAAC;IACpD,CAAC,MAAM,IAAIA,KAAK,YAAYZ,YAAY,EAAE;MACxCU,cAAc,CAACC,GAAG,CAAC,GAAGC,KAAK;IAC7B,CAAC,MAAM,IAAIA,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACtEF,cAAc,CAACC,GAAG,CAAC,GAAGJ,mBAAmB,CAACK,KAAK,CAAC;IAClD;EACF;EACA,OAAOF,cAAc;AACvB;AAAC,IACKK,aAAa,aAAAC,qBAAA;EACjB,SAAAD,cAAYN,KAAK,EAAE;IAAA,IAAAQ,KAAA;IAAAvC,eAAA,OAAAqC,aAAA;IACjBE,KAAA,GAAAjC,UAAA,OAAA+B,aAAA;IACAE,KAAA,CAAKC,WAAW,GAAGT,KAAK;IACxBQ,KAAA,CAAKE,MAAM,GAAGZ,mBAAmB,CAACE,KAAK,CAAC;IAAC,OAAAQ,KAAA;EAC3C;EAAClC,SAAA,CAAAgC,aAAA,EAAAC,qBAAA;EAAA,OAAArC,YAAA,CAAAoC,aAAA;IAAAJ,GAAA;IAAAC,KAAA,EAGD,SAAAQ,sBAAsBA,CAACX,KAAK,EAAE;MAC5B,IAAIY,YAAY,GAAG,CAAC,CAAC;MACrB,KAAK,IAAIV,GAAG,IAAIF,KAAK,EAAE;QACrB,IAAIG,KAAK,GAAGH,KAAK,CAACE,GAAG,CAAC;QACtB,IAAIC,KAAK,YAAYZ,YAAY,EAAE;UACjC,IAAI,CAACY,KAAK,CAACU,UAAU,EAAE;YAGrBD,YAAY,CAACV,GAAG,CAAC,GAAGC,KAAK,CAACW,UAAU,CAAC,CAAC;UACxC;QACF,CAAC,MAAM,IAAIX,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAEtES,YAAY,CAACV,GAAG,CAAC,GAAG,IAAI,CAACS,sBAAsB,CAACR,KAAK,CAAC;QACxD,CAAC,MAAM;UACLS,YAAY,CAACV,GAAG,CAAC,GAAGC,KAAK;QAC3B;MACF;MACA,OAAOS,YAAY;IACrB;EAAC;IAAAV,GAAA;IAAAC,KAAA,EACD,SAAAW,UAAUA,CAAA,EAAG;MACX,OAAO,CAAC,IAAI,CAACL,WAAW,EAAE,IAAI,CAACE,sBAAsB,CAAC,IAAI,CAACD,MAAM,CAAC,CAAC;IACrE;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAGD,SAAAY,8BAA8BA,CAACf,KAAK,EAAE;MACpC,IAAIY,YAAY,GAAG,CAAC,CAAC;MACrB,KAAK,IAAIV,GAAG,IAAIF,KAAK,EAAE;QACrB,IAAIG,KAAK,GAAGH,KAAK,CAACE,GAAG,CAAC;QACtB,IAAIC,KAAK,YAAYZ,YAAY,EAAE;UACjCqB,YAAY,CAACV,GAAG,CAAC,GAAGC,KAAK,CAACa,kBAAkB,CAAC,CAAC;QAChD,CAAC,MAAM,IAAIb,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAEtES,YAAY,CAACV,GAAG,CAAC,GAAG,IAAI,CAACa,8BAA8B,CAACZ,KAAK,CAAC;QAChE;MACF;MACA,OAAOS,YAAY;IACrB;EAAC;IAAAV,GAAA;IAAAC,KAAA,EACD,SAAAa,kBAAkBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACD,8BAA8B,CAAC,IAAI,CAACL,MAAM,CAAC;IACzD;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAc,QAAQA,CAAA,EAAG;MACT,KAAK,IAAIf,GAAG,IAAI,IAAI,CAACQ,MAAM,EAAE;QAC3B,IAAIP,KAAK,GAAG,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYZ,YAAY,EAAE;UACjCY,KAAK,CAACe,UAAU,CAAC,IAAI,CAAC;QACxB;MACF;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EACD,SAAAgB,QAAQA,CAAA,EAAG;MACT,KAAK,IAAIjB,GAAG,IAAI,IAAI,CAACQ,MAAM,EAAE;QAC3B,IAAIP,KAAK,GAAG,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYZ,YAAY,EAAE;UACjCY,KAAK,CAACiB,aAAa,CAAC,IAAI,CAAC;QAC3B;MACF;MACAhC,aAAA,CAAAkB,aAAA;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAkB,YAAYA,CAAA,EAAG;MACb,KAAK,IAAInB,GAAG,IAAI,IAAI,CAACQ,MAAM,EAAE;QAC3B,IAAIP,KAAK,GAAG,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYZ,YAAY,EAAE;UACjCY,KAAK,CAACkB,YAAY,CAAC,CAAC;QACtB;MACF;MACAjC,aAAA,CAAAkB,aAAA;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAmB,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpB,KAAK,IAAIC,QAAQ,IAAI,IAAI,CAACd,MAAM,EAAE;QAChC,IAAI,IAAI,CAACA,MAAM,CAACc,QAAQ,CAAC,YAAYjC,YAAY,EAAE;UACjD,IAAIS,KAAK,GAAG,IAAI,CAACU,MAAM,CAACc,QAAQ,CAAC;UACjCxB,KAAK,CAACqB,YAAY,CAAC,CAAC;UACpBE,WAAW,CAACC,QAAQ,CAAC,GAAGxB,KAAK,CAACyB,cAAc,CAAC,CAAC;QAChD;MACF;MACA/B,oBAAoB,CAACgC,cAAc,CAACH,WAAW,CAAC;MAChD,OAAO;QACLI,IAAI,EAAE,OAAO;QACb3B,KAAK,EAAEuB;MACT,CAAC;IACH;EAAC;AAAA,EAxFyB9B,oBAAoB;AA0FhD,eAAea,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}