{"ast": null, "code": "import { ANDROID_EVT_TYPE, EVENT_TYPE_SET } from \"./constants\";\nexport var createDateTimeSetEvtParams = function createDateTimeSetEvtParams(date, utcOffset) {\n  return [{\n    type: EVENT_TYPE_SET,\n    nativeEvent: {\n      timestamp: date.getTime(),\n      utcOffset: utcOffset\n    }\n  }, date];\n};\nexport var createDismissEvtParams = function createDismissEvtParams(date, utcOffset) {\n  return [{\n    type: ANDROID_EVT_TYPE.dismissed,\n    nativeEvent: {\n      timestamp: date.getTime(),\n      utcOffset: utcOffset\n    }\n  }, date];\n};\nexport var createNeutralEvtParams = function createNeutralEvtParams(date, utcOffset) {\n  return [{\n    type: ANDROID_EVT_TYPE.neutralButtonPressed,\n    nativeEvent: {\n      timestamp: date.getTime(),\n      utcOffset: utcOffset\n    }\n  }, date];\n};", "map": {"version": 3, "names": ["ANDROID_EVT_TYPE", "EVENT_TYPE_SET", "createDateTimeSetEvtParams", "date", "utcOffset", "type", "nativeEvent", "timestamp", "getTime", "createDismissEvtParams", "dismissed", "createNeutralEvtParams", "neutralButtonPressed"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/@react-native-community/datetimepicker/src/eventCreators.js"], "sourcesContent": ["/**\n * @flow strict-local\n */\nimport type {DateTimePickerEvent} from './types';\nimport {ANDROID_EVT_TYPE, EVENT_TYPE_SET} from './constants';\n\nexport const createDateTimeSetEvtParams = (\n  date: Date,\n  utcOffset: number,\n): [DateTimePickerEvent, Date] => {\n  return [\n    {\n      type: EVENT_TYPE_SET,\n      nativeEvent: {\n        timestamp: date.getTime(),\n        utcOffset,\n      },\n    },\n    date,\n  ];\n};\n\nexport const createDismissEvtParams = (\n  date: Date,\n  utcOffset: number,\n): [DateTimePickerEvent, Date] => {\n  return [\n    {\n      type: ANDROID_EVT_TYPE.dismissed,\n      nativeEvent: {\n        timestamp: date.getTime(),\n        utcOffset,\n      },\n    },\n    date,\n  ];\n};\n\nexport const createNeutralEvtParams = (\n  date: Date,\n  utcOffset: number,\n): [DateTimePickerEvent, Date] => {\n  return [\n    {\n      type: ANDROID_EVT_TYPE.neutralButtonPressed,\n      nativeEvent: {\n        timestamp: date.getTime(),\n        utcOffset,\n      },\n    },\n    date,\n  ];\n};\n"], "mappings": "AAIA,SAAQA,gBAAgB,EAAEC,cAAc;AAExC,OAAO,IAAMC,0BAA0B,GAAG,SAA7BA,0BAA0BA,CACrCC,IAAU,EACVC,SAAiB,EACe;EAChC,OAAO,CACL;IACEC,IAAI,EAAEJ,cAAc;IACpBK,WAAW,EAAE;MACXC,SAAS,EAAEJ,IAAI,CAACK,OAAO,CAAC,CAAC;MACzBJ,SAAS,EAATA;IACF;EACF,CAAC,EACDD,IAAI,CACL;AACH,CAAC;AAED,OAAO,IAAMM,sBAAsB,GAAG,SAAzBA,sBAAsBA,CACjCN,IAAU,EACVC,SAAiB,EACe;EAChC,OAAO,CACL;IACEC,IAAI,EAAEL,gBAAgB,CAACU,SAAS;IAChCJ,WAAW,EAAE;MACXC,SAAS,EAAEJ,IAAI,CAACK,OAAO,CAAC,CAAC;MACzBJ,SAAS,EAATA;IACF;EACF,CAAC,EACDD,IAAI,CACL;AACH,CAAC;AAED,OAAO,IAAMQ,sBAAsB,GAAG,SAAzBA,sBAAsBA,CACjCR,IAAU,EACVC,SAAiB,EACe;EAChC,OAAO,CACL;IACEC,IAAI,EAAEL,gBAAgB,CAACY,oBAAoB;IAC3CN,WAAW,EAAE;MACXC,SAAS,EAAEJ,IAAI,CAACK,OAAO,CAAC,CAAC;MACzBJ,SAAS,EAATA;IACF;EACF,CAAC,EACDD,IAAI,CACL;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}