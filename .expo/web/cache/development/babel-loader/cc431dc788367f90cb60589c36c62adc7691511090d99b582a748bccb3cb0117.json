{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport ActivityIndicator from \"react-native-web/dist/exports/ActivityIndicator\";\nimport { Searchbar, Chip, Text, Button, FAB } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport FoodItem from \"./FoodItem\";\nimport BarcodeScanner from \"./BarcodeScanner\";\nimport { searchFoods, getFavorites, getCustomFoods } from \"../services/databaseService\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar FoodList = function FoodList(_ref) {\n  var onSelectFood = _ref.onSelectFood,\n    onAddFood = _ref.onAddFood,\n    onEditFood = _ref.onEditFood;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    foods = _useState2[0],\n    setFoods = _useState2[1];\n  var _useState3 = useState(''),\n    _useState4 = _slicedToArray(_useState3, 2),\n    searchQuery = _useState4[0],\n    setSearchQuery = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    isLoading = _useState6[0],\n    setIsLoading = _useState6[1];\n  var _useState7 = useState('all'),\n    _useState8 = _slicedToArray(_useState7, 2),\n    filter = _useState8[0],\n    setFilter = _useState8[1];\n  var _useState9 = useState(false),\n    _useState0 = _slicedToArray(_useState9, 2),\n    showScanner = _useState0[0],\n    setShowScanner = _useState0[1];\n  useEffect(function () {\n    var loadFoods = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        setIsLoading(true);\n        try {\n          var results = [];\n          if (searchQuery.trim()) {\n            results = yield searchFoods(searchQuery);\n          } else if (filter === 'favorites') {\n            results = yield getFavorites();\n          } else if (filter === 'custom') {\n            results = yield getCustomFoods();\n          } else {\n            results = yield searchFoods('', 50);\n          }\n          setFoods(results);\n        } catch (error) {\n          console.error('Error loading foods:', error);\n        } finally {\n          setIsLoading(false);\n        }\n      });\n      return function loadFoods() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    loadFoods();\n  }, [searchQuery, filter]);\n  var handleSearch = function handleSearch(query) {\n    setSearchQuery(query);\n  };\n  var handleFilterChange = function handleFilterChange(newFilter) {\n    setFilter(newFilter);\n  };\n  var handleBarcodeScan = function handleBarcodeScan(food) {\n    setShowScanner(false);\n    if (onSelectFood) {\n      onSelectFood(food);\n    }\n  };\n  var renderFoodItem = function renderFoodItem(_ref3) {\n    var item = _ref3.item;\n    return _jsx(FoodItem, {\n      food: item,\n      onSelect: onSelectFood,\n      onEdit: onEditFood\n    });\n  };\n  var renderEmptyList = function renderEmptyList() {\n    return _jsxs(View, {\n      style: styles.emptyContainer,\n      children: [_jsx(Text, {\n        style: styles.emptyText,\n        children: \"No foods found\"\n      }), _jsx(Button, {\n        mode: \"contained\",\n        onPress: function onPress() {\n          return onAddFood ? onAddFood() : null;\n        },\n        style: styles.addButton,\n        children: \"Add New Food\"\n      })]\n    });\n  };\n  return _jsxs(View, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: [_jsx(Searchbar, {\n      placeholder: \"Search foods...\",\n      onChangeText: handleSearch,\n      value: searchQuery,\n      style: styles.searchBar\n    }), _jsxs(View, {\n      style: styles.filtersContainer,\n      children: [_jsx(Chip, {\n        selected: filter === 'all',\n        onPress: function onPress() {\n          return handleFilterChange('all');\n        },\n        style: styles.filterChip,\n        children: \"All\"\n      }), _jsx(Chip, {\n        selected: filter === 'favorites',\n        onPress: function onPress() {\n          return handleFilterChange('favorites');\n        },\n        style: styles.filterChip,\n        icon: \"star\",\n        children: \"Favorites\"\n      }), _jsx(Chip, {\n        selected: filter === 'custom',\n        onPress: function onPress() {\n          return handleFilterChange('custom');\n        },\n        style: styles.filterChip,\n        icon: \"pencil\",\n        children: \"Custom\"\n      })]\n    }), isLoading ? _jsx(ActivityIndicator, {\n      size: \"large\",\n      color: theme.colors.primary,\n      style: styles.loader\n    }) : _jsx(FlatList, {\n      data: foods,\n      renderItem: renderFoodItem,\n      keyExtractor: function keyExtractor(item) {\n        return item.id;\n      },\n      contentContainerStyle: styles.listContent,\n      ListEmptyComponent: renderEmptyList\n    }), _jsx(FAB, {\n      icon: \"barcode-scan\",\n      style: [styles.fab, {\n        backgroundColor: theme.colors.primary\n      }],\n      onPress: function onPress() {\n        return setShowScanner(true);\n      }\n    }), showScanner && _jsx(BarcodeScanner, {\n      onScan: handleBarcodeScan,\n      onClose: function onClose() {\n        return setShowScanner(false);\n      }\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  searchBar: {\n    margin: 16,\n    elevation: 2\n  },\n  filtersContainer: {\n    flexDirection: 'row',\n    paddingHorizontal: 16,\n    marginBottom: 8\n  },\n  filterChip: {\n    marginRight: 8\n  },\n  listContent: {\n    paddingBottom: 80\n  },\n  emptyContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20\n  },\n  emptyText: {\n    fontSize: 16,\n    marginBottom: 20\n  },\n  addButton: {\n    marginTop: 10\n  },\n  loader: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0\n  }\n});\nexport default FoodList;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "FlatList", "ActivityIndicator", "Searchbar", "Chip", "Text", "<PERSON><PERSON>", "FAB", "useTheme", "FoodItem", "BarcodeScanner", "searchFoods", "getFavorites", "getCustomFoods", "jsx", "_jsx", "jsxs", "_jsxs", "FoodList", "_ref", "onSelectFood", "onAddFood", "onEditFood", "_useTheme", "theme", "_useState", "_useState2", "_slicedToArray", "foods", "setFoods", "_useState3", "_useState4", "searchQuery", "setSearch<PERSON>uery", "_useState5", "_useState6", "isLoading", "setIsLoading", "_useState7", "_useState8", "filter", "setFilter", "_useState9", "_useState0", "showScanner", "setShowScanner", "loadFoods", "_ref2", "_asyncToGenerator", "results", "trim", "error", "console", "apply", "arguments", "handleSearch", "query", "handleFilterChange", "newFilter", "handleBarcodeScan", "food", "renderFoodItem", "_ref3", "item", "onSelect", "onEdit", "renderEmptyList", "style", "styles", "emptyContainer", "children", "emptyText", "mode", "onPress", "addButton", "container", "backgroundColor", "colors", "background", "placeholder", "onChangeText", "value", "searchBar", "filtersContainer", "selected", "filterChip", "icon", "size", "color", "primary", "loader", "data", "renderItem", "keyExtractor", "id", "contentContainerStyle", "listContent", "ListEmptyComponent", "fab", "onScan", "onClose", "create", "flex", "margin", "elevation", "flexDirection", "paddingHorizontal", "marginBottom", "marginRight", "paddingBottom", "justifyContent", "alignItems", "padding", "fontSize", "marginTop", "position", "right", "bottom"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/components/FoodList.js"], "sourcesContent": ["/**\n * Food List Component for Znü<PERSON>Zähler\n * Displays a list of food items with search and filtering\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, FlatList, ActivityIndicator } from 'react-native';\nimport { Searchbar, Chip, Text, Button, FAB } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport FoodItem from './FoodItem';\nimport BarcodeScanner from './BarcodeScanner';\nimport { searchFoods, getFavorites, getCustomFoods } from '../services/databaseService';\n\n/**\n * Food List Component\n * @param {Object} props - Component props\n * @param {Function} props.onSelectFood - Callback function when a food is selected\n * @param {Function} props.onAddFood - Callback function to add a new food\n * @param {Function} props.onEditFood - Callback function to edit a food\n * @returns {JSX.Element} - Food list component\n */\nconst FoodList = ({ onSelectFood, onAddFood, onEditFood }) => {\n  const { theme } = useTheme();\n  const [foods, setFoods] = useState([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [filter, setFilter] = useState('all'); // 'all', 'favorites', 'custom'\n  const [showScanner, setShowScanner] = useState(false);\n\n  // Load foods based on filter\n  useEffect(() => {\n    const loadFoods = async () => {\n      setIsLoading(true);\n      try {\n        let results = [];\n\n        if (searchQuery.trim()) {\n          // Search foods by name\n          results = await searchFoods(searchQuery);\n        } else if (filter === 'favorites') {\n          // Get favorite foods\n          results = await getFavorites();\n        } else if (filter === 'custom') {\n          // Get custom foods\n          results = await getCustomFoods();\n        } else {\n          // Get all foods (limited to recent ones)\n          results = await searchFoods('', 50);\n        }\n\n        setFoods(results);\n      } catch (error) {\n        console.error('Error loading foods:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadFoods();\n  }, [searchQuery, filter]);\n\n  // Handle search\n  const handleSearch = (query) => {\n    setSearchQuery(query);\n  };\n\n  // Handle filter change\n  const handleFilterChange = (newFilter) => {\n    setFilter(newFilter);\n  };\n\n  // Handle barcode scan\n  const handleBarcodeScan = (food) => {\n    setShowScanner(false);\n    if (onSelectFood) {\n      onSelectFood(food);\n    }\n  };\n\n  // Render food item\n  const renderFoodItem = ({ item }) => (\n    <FoodItem\n      food={item}\n      onSelect={onSelectFood}\n      onEdit={onEditFood}\n    />\n  );\n\n  // Render empty list\n  const renderEmptyList = () => (\n    <View style={styles.emptyContainer}>\n      <Text style={styles.emptyText}>No foods found</Text>\n      <Button\n        mode=\"contained\"\n        onPress={() => onAddFood ? onAddFood() : null}\n        style={styles.addButton}\n      >\n        Add New Food\n      </Button>\n    </View>\n  );\n\n  return (\n    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <Searchbar\n        placeholder=\"Search foods...\"\n        onChangeText={handleSearch}\n        value={searchQuery}\n        style={styles.searchBar}\n      />\n\n      <View style={styles.filtersContainer}>\n        <Chip\n          selected={filter === 'all'}\n          onPress={() => handleFilterChange('all')}\n          style={styles.filterChip}\n        >\n          All\n        </Chip>\n        <Chip\n          selected={filter === 'favorites'}\n          onPress={() => handleFilterChange('favorites')}\n          style={styles.filterChip}\n          icon=\"star\"\n        >\n          Favorites\n        </Chip>\n        <Chip\n          selected={filter === 'custom'}\n          onPress={() => handleFilterChange('custom')}\n          style={styles.filterChip}\n          icon=\"pencil\"\n        >\n          Custom\n        </Chip>\n      </View>\n\n      {isLoading ? (\n        <ActivityIndicator size=\"large\" color={theme.colors.primary} style={styles.loader} />\n      ) : (\n        <FlatList\n          data={foods}\n          renderItem={renderFoodItem}\n          keyExtractor={(item) => item.id}\n          contentContainerStyle={styles.listContent}\n          ListEmptyComponent={renderEmptyList}\n        />\n      )}\n\n      <FAB\n        icon=\"barcode-scan\"\n        style={[styles.fab, { backgroundColor: theme.colors.primary }]}\n        onPress={() => setShowScanner(true)}\n      />\n\n      {showScanner && (\n        <BarcodeScanner\n          onScan={handleBarcodeScan}\n          onClose={() => setShowScanner(false)}\n        />\n      )}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  searchBar: {\n    margin: 16,\n    elevation: 2,\n  },\n  filtersContainer: {\n    flexDirection: 'row',\n    paddingHorizontal: 16,\n    marginBottom: 8,\n  },\n  filterChip: {\n    marginRight: 8,\n  },\n  listContent: {\n    paddingBottom: 80, // Space for FAB\n  },\n  emptyContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n  },\n  emptyText: {\n    fontSize: 16,\n    marginBottom: 20,\n  },\n  addButton: {\n    marginTop: 10,\n  },\n  loader: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0,\n  },\n});\n\nexport default FoodList;\n"], "mappings": ";;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,iBAAA;AAEnD,SAASC,SAAS,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,QAAQ,oBAAoB;AACvE,SAASC,QAAQ;AACjB,OAAOC,QAAQ;AACf,OAAOC,cAAc;AACrB,SAASC,WAAW,EAAEC,YAAY,EAAEC,cAAc;AAAsC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAUxF,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAAgD;EAAA,IAA1CC,YAAY,GAAAD,IAAA,CAAZC,YAAY;IAAEC,SAAS,GAAAF,IAAA,CAATE,SAAS;IAAEC,UAAU,GAAAH,IAAA,CAAVG,UAAU;EACrD,IAAAC,SAAA,GAAkBf,QAAQ,CAAC,CAAC;IAApBgB,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,SAAA,GAA0B5B,QAAQ,CAAC,EAAE,CAAC;IAAA6B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA/BG,KAAK,GAAAF,UAAA;IAAEG,QAAQ,GAAAH,UAAA;EACtB,IAAAI,UAAA,GAAsCjC,QAAQ,CAAC,EAAE,CAAC;IAAAkC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA3CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAClC,IAAAG,UAAA,GAAkCrC,QAAQ,CAAC,KAAK,CAAC;IAAAsC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4BzC,QAAQ,CAAC,KAAK,CAAC;IAAA0C,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAApCE,MAAM,GAAAD,UAAA;IAAEE,SAAS,GAAAF,UAAA;EACxB,IAAAG,UAAA,GAAsC7C,QAAQ,CAAC,KAAK,CAAC;IAAA8C,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAA9CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAGlC7C,SAAS,CAAC,YAAM;IACd,IAAMgD,SAAS;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAC5BX,YAAY,CAAC,IAAI,CAAC;QAClB,IAAI;UACF,IAAIY,OAAO,GAAG,EAAE;UAEhB,IAAIjB,WAAW,CAACkB,IAAI,CAAC,CAAC,EAAE;YAEtBD,OAAO,SAAStC,WAAW,CAACqB,WAAW,CAAC;UAC1C,CAAC,MAAM,IAAIQ,MAAM,KAAK,WAAW,EAAE;YAEjCS,OAAO,SAASrC,YAAY,CAAC,CAAC;UAChC,CAAC,MAAM,IAAI4B,MAAM,KAAK,QAAQ,EAAE;YAE9BS,OAAO,SAASpC,cAAc,CAAC,CAAC;UAClC,CAAC,MAAM;YAELoC,OAAO,SAAStC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;UACrC;UAEAkB,QAAQ,CAACoB,OAAO,CAAC;QACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C,CAAC,SAAS;UACRd,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;MAAA,gBAzBKS,SAASA,CAAA;QAAA,OAAAC,KAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;IAAA,GAyBd;IAEDR,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACd,WAAW,EAAEQ,MAAM,CAAC,CAAC;EAGzB,IAAMe,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAK;IAC9BvB,cAAc,CAACuB,KAAK,CAAC;EACvB,CAAC;EAGD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,SAAS,EAAK;IACxCjB,SAAS,CAACiB,SAAS,CAAC;EACtB,CAAC;EAGD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,IAAI,EAAK;IAClCf,cAAc,CAAC,KAAK,CAAC;IACrB,IAAIzB,YAAY,EAAE;MAChBA,YAAY,CAACwC,IAAI,CAAC;IACpB;EACF,CAAC;EAGD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA;IAAA,IAAMC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IAAA,OAC5BhD,IAAA,CAACN,QAAQ;MACPmD,IAAI,EAAEG,IAAK;MACXC,QAAQ,EAAE5C,YAAa;MACvB6C,MAAM,EAAE3C;IAAW,CACpB,CAAC;EAAA,CACH;EAGD,IAAM4C,eAAe,GAAG,SAAlBA,eAAeA,CAAA;IAAA,OACnBjD,KAAA,CAACjB,IAAI;MAACmE,KAAK,EAAEC,MAAM,CAACC,cAAe;MAAAC,QAAA,GACjCvD,IAAA,CAACV,IAAI;QAAC8D,KAAK,EAAEC,MAAM,CAACG,SAAU;QAAAD,QAAA,EAAC;MAAc,CAAM,CAAC,EACpDvD,IAAA,CAACT,MAAM;QACLkE,IAAI,EAAC,WAAW;QAChBC,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQpD,SAAS,GAAGA,SAAS,CAAC,CAAC,GAAG,IAAI;QAAA,CAAC;QAC9C8C,KAAK,EAAEC,MAAM,CAACM,SAAU;QAAAJ,QAAA,EACzB;MAED,CAAQ,CAAC;IAAA,CACL,CAAC;EAAA,CACR;EAED,OACErD,KAAA,CAACjB,IAAI;IAACmE,KAAK,EAAE,CAACC,MAAM,CAACO,SAAS,EAAE;MAAEC,eAAe,EAAEpD,KAAK,CAACqD,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAR,QAAA,GAC5EvD,IAAA,CAACZ,SAAS;MACR4E,WAAW,EAAC,iBAAiB;MAC7BC,YAAY,EAAEzB,YAAa;MAC3B0B,KAAK,EAAEjD,WAAY;MACnBmC,KAAK,EAAEC,MAAM,CAACc;IAAU,CACzB,CAAC,EAEFjE,KAAA,CAACjB,IAAI;MAACmE,KAAK,EAAEC,MAAM,CAACe,gBAAiB;MAAAb,QAAA,GACnCvD,IAAA,CAACX,IAAI;QACHgF,QAAQ,EAAE5C,MAAM,KAAK,KAAM;QAC3BiC,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQhB,kBAAkB,CAAC,KAAK,CAAC;QAAA,CAAC;QACzCU,KAAK,EAAEC,MAAM,CAACiB,UAAW;QAAAf,QAAA,EAC1B;MAED,CAAM,CAAC,EACPvD,IAAA,CAACX,IAAI;QACHgF,QAAQ,EAAE5C,MAAM,KAAK,WAAY;QACjCiC,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQhB,kBAAkB,CAAC,WAAW,CAAC;QAAA,CAAC;QAC/CU,KAAK,EAAEC,MAAM,CAACiB,UAAW;QACzBC,IAAI,EAAC,MAAM;QAAAhB,QAAA,EACZ;MAED,CAAM,CAAC,EACPvD,IAAA,CAACX,IAAI;QACHgF,QAAQ,EAAE5C,MAAM,KAAK,QAAS;QAC9BiC,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQhB,kBAAkB,CAAC,QAAQ,CAAC;QAAA,CAAC;QAC5CU,KAAK,EAAEC,MAAM,CAACiB,UAAW;QACzBC,IAAI,EAAC,QAAQ;QAAAhB,QAAA,EACd;MAED,CAAM,CAAC;IAAA,CACH,CAAC,EAENlC,SAAS,GACRrB,IAAA,CAACb,iBAAiB;MAACqF,IAAI,EAAC,OAAO;MAACC,KAAK,EAAEhE,KAAK,CAACqD,MAAM,CAACY,OAAQ;MAACtB,KAAK,EAAEC,MAAM,CAACsB;IAAO,CAAE,CAAC,GAErF3E,IAAA,CAACd,QAAQ;MACP0F,IAAI,EAAE/D,KAAM;MACZgE,UAAU,EAAE/B,cAAe;MAC3BgC,YAAY,EAAE,SAAdA,YAAYA,CAAG9B,IAAI;QAAA,OAAKA,IAAI,CAAC+B,EAAE;MAAA,CAAC;MAChCC,qBAAqB,EAAE3B,MAAM,CAAC4B,WAAY;MAC1CC,kBAAkB,EAAE/B;IAAgB,CACrC,CACF,EAEDnD,IAAA,CAACR,GAAG;MACF+E,IAAI,EAAC,cAAc;MACnBnB,KAAK,EAAE,CAACC,MAAM,CAAC8B,GAAG,EAAE;QAAEtB,eAAe,EAAEpD,KAAK,CAACqD,MAAM,CAACY;MAAQ,CAAC,CAAE;MAC/DhB,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQ5B,cAAc,CAAC,IAAI,CAAC;MAAA;IAAC,CACrC,CAAC,EAEDD,WAAW,IACV7B,IAAA,CAACL,cAAc;MACbyF,MAAM,EAAExC,iBAAkB;MAC1ByC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQvD,cAAc,CAAC,KAAK,CAAC;MAAA;IAAC,CACtC,CACF;EAAA,CACG,CAAC;AAEX,CAAC;AAED,IAAMuB,MAAM,GAAGrE,UAAU,CAACsG,MAAM,CAAC;EAC/B1B,SAAS,EAAE;IACT2B,IAAI,EAAE;EACR,CAAC;EACDpB,SAAS,EAAE;IACTqB,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC;EACDrB,gBAAgB,EAAE;IAChBsB,aAAa,EAAE,KAAK;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,YAAY,EAAE;EAChB,CAAC;EACDtB,UAAU,EAAE;IACVuB,WAAW,EAAE;EACf,CAAC;EACDZ,WAAW,EAAE;IACXa,aAAa,EAAE;EACjB,CAAC;EACDxC,cAAc,EAAE;IACdiC,IAAI,EAAE,CAAC;IACPQ,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDzC,SAAS,EAAE;IACT0C,QAAQ,EAAE,EAAE;IACZN,YAAY,EAAE;EAChB,CAAC;EACDjC,SAAS,EAAE;IACTwC,SAAS,EAAE;EACb,CAAC;EACDxB,MAAM,EAAE;IACNY,IAAI,EAAE,CAAC;IACPQ,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDb,GAAG,EAAE;IACHiB,QAAQ,EAAE,UAAU;IACpBZ,MAAM,EAAE,EAAE;IACVa,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAenG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}