{"ast": null, "code": "import Platform from \"react-native-web/dist/exports/Platform\";\nimport NativeModules from \"react-native-web/dist/exports/NativeModules\";\nvar NativeIconAPI = NativeModules.RNVectorIconsManager || NativeModules.RNVectorIconsModule;\nexport default function ensureNativeModuleAvailable() {\n  if (!NativeIconAPI) {\n    if (Platform.OS === 'android') {\n      throw new Error('RNVectorIconsModule not available, did you properly integrate the module? Try running `react-native link react-native-vector-icons` and recompiling.');\n    }\n    throw new Error('RNVectorIconsManager not available, did you add the library to your project and link with libRNVectorIcons.a? Try running `react-native link react-native-vector-icons` and recompiling.');\n  }\n}", "map": {"version": 3, "names": ["NativeIconAPI", "NativeModules", "RNVectorIconsManager", "RNVectorIconsModule", "ensureNativeModuleAvailable", "Platform", "OS", "Error"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/ensure-native-module-available.js"], "sourcesContent": ["import { Platform, NativeModules } from 'react-native';\n\nconst NativeIconAPI =\n  NativeModules.RNVectorIconsManager || NativeModules.RNVectorIconsModule;\n\nexport default function ensureNativeModuleAvailable() {\n  if (!NativeIconAPI) {\n    if (Platform.OS === 'android') {\n      throw new Error(\n        'RNVectorIconsModule not available, did you properly integrate the module? Try running `react-native link react-native-vector-icons` and recompiling.'\n      );\n    }\n    throw new Error(\n      'RNVectorIconsManager not available, did you add the library to your project and link with libRNVectorIcons.a? Try running `react-native link react-native-vector-icons` and recompiling.'\n    );\n  }\n}\n"], "mappings": ";;AAEA,IAAMA,aAAa,GACjBC,aAAa,CAACC,oBAAoB,IAAID,aAAa,CAACE,mBAAmB;AAEzE,eAAe,SAASC,2BAA2BA,CAAA,EAAG;EACpD,IAAI,CAACJ,aAAa,EAAE;IAClB,IAAIK,QAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7B,MAAM,IAAIC,KAAK,CACb,sJACF,CAAC;IACH;IACA,MAAM,IAAIA,KAAK,CACb,0LACF,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}