{"ast": null, "code": "import PanResponder from \"../../vendor/react-native/PanResponder\";\nexport default PanResponder;", "map": {"version": 3, "names": ["PanResponder"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/PanResponder/index.js"], "sourcesContent": ["import PanResponder from '../../vendor/react-native/PanResponder';\nexport default PanResponder;"], "mappings": "AAAA,OAAOA,YAAY;AACnB,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}