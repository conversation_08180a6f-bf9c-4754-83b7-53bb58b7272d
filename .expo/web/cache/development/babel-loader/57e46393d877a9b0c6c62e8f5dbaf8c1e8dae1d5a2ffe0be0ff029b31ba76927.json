{"ast": null, "code": "export default function Group(_) {\n  return null;\n}", "map": {"version": 3, "names": ["Group", "_"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/Group.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/routers';\n\nimport type { RouteGroupConfig } from './types';\n\n/**\n * Empty component used for grouping screen configs.\n */\nexport default function Group<\n  ParamList extends ParamListBase,\n  ScreenOptions extends {}\n>(_: RouteGroupConfig<ParamList, ScreenOptions>) {\n  /* istanbul ignore next */\n  return null;\n}\n"], "mappings": "AAOA,eAAe,SAASA,KAAKA,CAG3BC,CAA6C,EAAE;EAE/C,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}