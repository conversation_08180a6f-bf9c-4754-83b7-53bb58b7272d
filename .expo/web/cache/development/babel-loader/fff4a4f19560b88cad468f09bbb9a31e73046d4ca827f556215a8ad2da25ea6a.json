{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"theme\"],\n  _excluded2 = [\"elevation\", \"style\", \"backgroundColor\", \"testID\", \"children\", \"mode\"],\n  _excluded3 = [\"elevation\", \"children\", \"theme\", \"style\", \"testID\", \"mode\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useInternalTheme } from \"../core/theming\";\nimport overlay, { isAnimatedValue } from \"../styles/overlay\";\nimport shadow from \"../styles/shadow\";\nimport { forwardRef } from \"../utils/forwardRef\";\nimport { splitStyles } from \"../utils/splitStyles\";\nvar MD2Surface = forwardRef(function (_ref, ref) {\n  var style = _ref.style,\n    overrideTheme = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _ref4 = StyleSheet.flatten(style) || {},\n    _ref4$elevation = _ref4.elevation,\n    elevation = _ref4$elevation === void 0 ? 4 : _ref4$elevation;\n  var _useInternalTheme = useInternalTheme(overrideTheme),\n    isDarkTheme = _useInternalTheme.dark,\n    mode = _useInternalTheme.mode,\n    colors = _useInternalTheme.colors;\n  return React.createElement(Animated.View, _extends({\n    ref: ref\n  }, rest, {\n    style: [{\n      backgroundColor: isDarkTheme && mode === 'adaptive' ? overlay(elevation, colors === null || colors === void 0 ? void 0 : colors.surface) : colors === null || colors === void 0 ? void 0 : colors.surface\n    }, elevation ? shadow(elevation) : null, style]\n  }));\n});\nvar outerLayerStyleProperties = ['position', 'alignSelf', 'top', 'right', 'bottom', 'left', 'start', 'end', 'flex', 'flexShrink', 'flexGrow', 'width', 'height', 'transform', 'opacity'];\nvar shadowColor = '#000';\nvar iOSShadowOutputRanges = [{\n  shadowOpacity: 0.15,\n  height: [0, 1, 2, 4, 6, 8],\n  shadowRadius: [0, 3, 6, 8, 10, 12]\n}, {\n  shadowOpacity: 0.3,\n  height: [0, 1, 1, 1, 2, 4],\n  shadowRadius: [0, 1, 2, 3, 3, 4]\n}];\nvar inputRange = [0, 1, 2, 3, 4, 5];\nfunction getStyleForShadowLayer(elevation, layer) {\n  if (isAnimatedValue(elevation)) {\n    return {\n      shadowColor: shadowColor,\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, iOSShadowOutputRanges[layer].shadowOpacity],\n        extrapolate: 'clamp'\n      }),\n      shadowOffset: {\n        width: 0,\n        height: elevation.interpolate({\n          inputRange: inputRange,\n          outputRange: iOSShadowOutputRanges[layer].height\n        })\n      },\n      shadowRadius: elevation.interpolate({\n        inputRange: inputRange,\n        outputRange: iOSShadowOutputRanges[layer].shadowRadius\n      })\n    };\n  }\n  return {\n    shadowColor: shadowColor,\n    shadowOpacity: elevation ? iOSShadowOutputRanges[layer].shadowOpacity : 0,\n    shadowOffset: {\n      width: 0,\n      height: iOSShadowOutputRanges[layer].height[elevation]\n    },\n    shadowRadius: iOSShadowOutputRanges[layer].shadowRadius[elevation]\n  };\n}\nvar SurfaceIOS = forwardRef(function (_ref2, ref) {\n  var elevation = _ref2.elevation,\n    style = _ref2.style,\n    backgroundColor = _ref2.backgroundColor,\n    testID = _ref2.testID,\n    children = _ref2.children,\n    _ref2$mode = _ref2.mode,\n    mode = _ref2$mode === void 0 ? 'elevated' : _ref2$mode,\n    props = _objectWithoutProperties(_ref2, _excluded2);\n  var _React$useMemo = React.useMemo(function () {\n      var flattenedStyles = StyleSheet.flatten(style) || {};\n      var _splitStyles = splitStyles(flattenedStyles, function (style) {\n          return outerLayerStyleProperties.includes(style) || style.startsWith('margin');\n        }, function (style) {\n          return style.startsWith('border') && style.endsWith('Radius');\n        }),\n        _splitStyles2 = _slicedToArray(_splitStyles, 3),\n        filteredStyles = _splitStyles2[0],\n        outerLayerStyles = _splitStyles2[1],\n        borderRadiusStyles = _splitStyles2[2];\n      if (process.env.NODE_ENV !== 'production' && filteredStyles.overflow === 'hidden' && elevation !== 0) {\n        console.warn('When setting overflow to hidden on Surface the shadow will not be displayed correctly. Wrap the content of your component in a separate View with the overflow style.');\n      }\n      var bgColor = flattenedStyles.backgroundColor || backgroundColor;\n      var isElevated = mode === 'elevated';\n      var outerLayerViewStyles = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, isElevated && getStyleForShadowLayer(elevation, 0)), outerLayerStyles), borderRadiusStyles), {}, {\n        backgroundColor: bgColor\n      });\n      var innerLayerViewStyles = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, isElevated && getStyleForShadowLayer(elevation, 1)), filteredStyles), borderRadiusStyles), {}, {\n        flex: flattenedStyles.height ? 1 : undefined,\n        backgroundColor: bgColor\n      });\n      return [outerLayerViewStyles, innerLayerViewStyles];\n    }, [style, elevation, backgroundColor, mode]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    outerLayerViewStyles = _React$useMemo2[0],\n    innerLayerViewStyles = _React$useMemo2[1];\n  return React.createElement(Animated.View, {\n    ref: ref,\n    style: outerLayerViewStyles,\n    testID: `${testID}-outer-layer`\n  }, React.createElement(Animated.View, _extends({}, props, {\n    style: innerLayerViewStyles,\n    testID: testID\n  }), children));\n});\nvar Surface = forwardRef(function (_ref3, ref) {\n  var _ref3$elevation = _ref3.elevation,\n    elevation = _ref3$elevation === void 0 ? 1 : _ref3$elevation,\n    children = _ref3.children,\n    overridenTheme = _ref3.theme,\n    style = _ref3.style,\n    _ref3$testID = _ref3.testID,\n    testID = _ref3$testID === void 0 ? 'surface' : _ref3$testID,\n    _ref3$mode = _ref3.mode,\n    mode = _ref3$mode === void 0 ? 'elevated' : _ref3$mode,\n    props = _objectWithoutProperties(_ref3, _excluded3);\n  var theme = useInternalTheme(overridenTheme);\n  if (!theme.isV3) return React.createElement(MD2Surface, _extends({}, props, {\n    theme: theme,\n    style: style,\n    ref: ref\n  }), children);\n  var colors = theme.colors;\n  var inputRange = [0, 1, 2, 3, 4, 5];\n  var backgroundColor = function (_colors$elevation2) {\n    if (isAnimatedValue(elevation)) {\n      return elevation.interpolate({\n        inputRange: inputRange,\n        outputRange: inputRange.map(function (elevation) {\n          var _colors$elevation;\n          return (_colors$elevation = colors.elevation) === null || _colors$elevation === void 0 ? void 0 : _colors$elevation[`level${elevation}`];\n        })\n      });\n    }\n    return (_colors$elevation2 = colors.elevation) === null || _colors$elevation2 === void 0 ? void 0 : _colors$elevation2[`level${elevation}`];\n  }();\n  var isElevated = mode === 'elevated';\n  if (Platform.OS === 'web') {\n    var _props$pointerEvents = props.pointerEvents,\n      pointerEvents = _props$pointerEvents === void 0 ? 'auto' : _props$pointerEvents;\n    return React.createElement(Animated.View, _extends({}, props, {\n      pointerEvents: pointerEvents,\n      ref: ref,\n      testID: testID,\n      style: [{\n        backgroundColor: backgroundColor\n      }, elevation && isElevated ? shadow(elevation, theme.isV3) : null, style]\n    }), children);\n  }\n  if (Platform.OS === 'android') {\n    var elevationLevel = [0, 3, 6, 9, 12, 15];\n    var getElevationAndroid = function getElevationAndroid() {\n      if (isAnimatedValue(elevation)) {\n        return elevation.interpolate({\n          inputRange: inputRange,\n          outputRange: elevationLevel\n        });\n      }\n      return elevationLevel[elevation];\n    };\n    var _ref5 = StyleSheet.flatten(style) || {},\n      margin = _ref5.margin,\n      padding = _ref5.padding,\n      transform = _ref5.transform,\n      borderRadius = _ref5.borderRadius;\n    var outerLayerStyles = {\n      margin: margin,\n      padding: padding,\n      transform: transform,\n      borderRadius: borderRadius\n    };\n    var sharedStyle = [{\n      backgroundColor: backgroundColor\n    }, style];\n    return React.createElement(Animated.View, _extends({}, props, {\n      testID: testID,\n      ref: ref,\n      style: [{\n        backgroundColor: backgroundColor,\n        transform: transform\n      }, outerLayerStyles, sharedStyle, isElevated && {\n        elevation: getElevationAndroid()\n      }]\n    }), children);\n  }\n  return React.createElement(SurfaceIOS, _extends({}, props, {\n    ref: ref,\n    elevation: elevation,\n    backgroundColor: backgroundColor,\n    style: style,\n    testID: testID,\n    mode: mode\n  }), children);\n});\nexport default Surface;", "map": {"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "useInternalTheme", "overlay", "isAnimatedValue", "shadow", "forwardRef", "splitStyles", "MD2Surface", "_ref", "ref", "style", "overrideTheme", "theme", "rest", "_objectWithoutProperties", "_excluded", "_ref4", "flatten", "_ref4$elevation", "elevation", "_useInternalTheme", "isDarkTheme", "dark", "mode", "colors", "createElement", "View", "_extends", "backgroundColor", "surface", "outerLayerStyleProperties", "shadowColor", "iOSShadowOutputRanges", "shadowOpacity", "height", "shadowRadius", "inputRange", "getStyleForShadowLayer", "layer", "interpolate", "outputRange", "extrapolate", "shadowOffset", "width", "SurfaceIOS", "_ref2", "testID", "children", "_ref2$mode", "props", "_excluded2", "_React$useMemo", "useMemo", "flattenedStyles", "_splitStyles", "includes", "startsWith", "endsWith", "_splitStyles2", "_slicedToArray", "filteredStyles", "outerLayerStyles", "borderRadiusStyles", "process", "env", "NODE_ENV", "overflow", "console", "warn", "bgColor", "isElevated", "outerLayerViewStyles", "_objectSpread", "innerLayerViewStyles", "flex", "undefined", "_React$useMemo2", "Surface", "_ref3", "_ref3$elevation", "overridenTheme", "_ref3$testID", "_ref3$mode", "_excluded3", "isV3", "_colors$elevation2", "map", "_colors$elevation", "OS", "_props$pointerEvents", "pointerEvents", "elevationLevel", "getElevationAndroid", "_ref5", "margin", "padding", "transform", "borderRadius", "sharedStyle"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Surface.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  ShadowStyleIOS,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../core/theming';\nimport overlay, { isAnimatedValue } from '../styles/overlay';\nimport shadow from '../styles/shadow';\nimport type { ThemeProp, MD3Elevation } from '../types';\nimport { forwardRef } from '../utils/forwardRef';\nimport { splitStyles } from '../utils/splitStyles';\n\ntype Elevation = 0 | 1 | 2 | 3 | 4 | 5 | Animated.Value;\n\nexport type Props = Omit<React.ComponentPropsWithRef<typeof View>, 'style'> & {\n  /**\n   * Content of the `Surface`.\n   */\n  children: React.ReactNode;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Changes shadows and background on iOS and Android.\n   * Used to create UI hierarchy between components.\n   *\n   * Note: If `mode` is set to `flat`, Surface doesn't have a shadow.\n   *\n   * Note: In version 2 the `elevation` prop was accepted via `style` prop i.e. `style={{ elevation: 4 }}`.\n   * It's no longer supported with theme version 3 and you should use `elevation` property instead.\n   */\n  elevation?: Elevation;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Mode of the Surface.\n   * - `elevated` - Surface with a shadow and background color corresponding to set `elevation` value.\n   * - `flat` - Surface without a shadow, with the background color corresponding to set `elevation` value.\n   */\n  mode?: 'flat' | 'elevated';\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  ref?: React.RefObject<View>;\n};\n\nconst MD2Surface = forwardRef<View, Props>(\n  ({ style, theme: overrideTheme, ...rest }: Omit<Props, 'elevation'>, ref) => {\n    const { elevation = 4 } = (StyleSheet.flatten(style) || {}) as ViewStyle;\n    const { dark: isDarkTheme, mode, colors } = useInternalTheme(overrideTheme);\n\n    return (\n      <Animated.View\n        ref={ref}\n        {...rest}\n        style={[\n          {\n            backgroundColor:\n              isDarkTheme && mode === 'adaptive'\n                ? overlay(elevation, colors?.surface)\n                : colors?.surface,\n          },\n          elevation ? shadow(elevation) : null,\n          style,\n        ]}\n      />\n    );\n  }\n);\n\nconst outerLayerStyleProperties: (keyof ViewStyle)[] = [\n  'position',\n  'alignSelf',\n  'top',\n  'right',\n  'bottom',\n  'left',\n  'start',\n  'end',\n  'flex',\n  'flexShrink',\n  'flexGrow',\n  'width',\n  'height',\n  'transform',\n  'opacity',\n];\n\nconst shadowColor = '#000';\nconst iOSShadowOutputRanges = [\n  {\n    shadowOpacity: 0.15,\n    height: [0, 1, 2, 4, 6, 8],\n    shadowRadius: [0, 3, 6, 8, 10, 12],\n  },\n  {\n    shadowOpacity: 0.3,\n    height: [0, 1, 1, 1, 2, 4],\n    shadowRadius: [0, 1, 2, 3, 3, 4],\n  },\n];\nconst inputRange = [0, 1, 2, 3, 4, 5];\nfunction getStyleForShadowLayer(\n  elevation: Elevation,\n  layer: 0 | 1\n): Animated.WithAnimatedValue<ShadowStyleIOS> {\n  if (isAnimatedValue(elevation)) {\n    return {\n      shadowColor,\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, iOSShadowOutputRanges[layer].shadowOpacity],\n        extrapolate: 'clamp',\n      }),\n      shadowOffset: {\n        width: 0,\n        height: elevation.interpolate({\n          inputRange,\n          outputRange: iOSShadowOutputRanges[layer].height,\n        }),\n      },\n      shadowRadius: elevation.interpolate({\n        inputRange,\n        outputRange: iOSShadowOutputRanges[layer].shadowRadius,\n      }),\n    };\n  }\n\n  return {\n    shadowColor,\n    shadowOpacity: elevation ? iOSShadowOutputRanges[layer].shadowOpacity : 0,\n    shadowOffset: {\n      width: 0,\n      height: iOSShadowOutputRanges[layer].height[elevation],\n    },\n    shadowRadius: iOSShadowOutputRanges[layer].shadowRadius[elevation],\n  };\n}\n\nconst SurfaceIOS = forwardRef<\n  View,\n  Omit<Props, 'elevation'> & {\n    elevation: Elevation;\n    backgroundColor?: string | Animated.AnimatedInterpolation<string | number>;\n  }\n>(\n  (\n    {\n      elevation,\n      style,\n      backgroundColor,\n      testID,\n      children,\n      mode = 'elevated',\n      ...props\n    },\n    ref\n  ) => {\n    const [outerLayerViewStyles, innerLayerViewStyles] = React.useMemo(() => {\n      const flattenedStyles = (StyleSheet.flatten(style) || {}) as ViewStyle;\n\n      const [filteredStyles, outerLayerStyles, borderRadiusStyles] =\n        splitStyles(\n          flattenedStyles,\n          (style) =>\n            outerLayerStyleProperties.includes(style) ||\n            style.startsWith('margin'),\n          (style) => style.startsWith('border') && style.endsWith('Radius')\n        );\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        filteredStyles.overflow === 'hidden' &&\n        elevation !== 0\n      ) {\n        console.warn(\n          'When setting overflow to hidden on Surface the shadow will not be displayed correctly. Wrap the content of your component in a separate View with the overflow style.'\n        );\n      }\n\n      const bgColor = flattenedStyles.backgroundColor || backgroundColor;\n\n      const isElevated = mode === 'elevated';\n\n      const outerLayerViewStyles = {\n        ...(isElevated && getStyleForShadowLayer(elevation, 0)),\n        ...outerLayerStyles,\n        ...borderRadiusStyles,\n        backgroundColor: bgColor,\n      };\n\n      const innerLayerViewStyles = {\n        ...(isElevated && getStyleForShadowLayer(elevation, 1)),\n        ...filteredStyles,\n        ...borderRadiusStyles,\n        flex: flattenedStyles.height ? 1 : undefined,\n        backgroundColor: bgColor,\n      };\n\n      return [outerLayerViewStyles, innerLayerViewStyles];\n    }, [style, elevation, backgroundColor, mode]);\n\n    return (\n      <Animated.View\n        ref={ref}\n        style={outerLayerViewStyles}\n        testID={`${testID}-outer-layer`}\n      >\n        <Animated.View {...props} style={innerLayerViewStyles} testID={testID}>\n          {children}\n        </Animated.View>\n      </Animated.View>\n    );\n  }\n);\n\n/**\n * Surface is a basic container that can give depth to an element with elevation shadow.\n * On dark theme with `adaptive` mode, surface is constructed by also placing a semi-transparent white overlay over a component surface.\n * See [Dark Theme](https://callstack.github.io/react-native-paper/docs/guides/theming#dark-theme) for more information.\n * Overlay and shadow can be applied by specifying the `elevation` property both on Android and iOS.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Surface, Text } from 'react-native-paper';\n * import { StyleSheet } from 'react-native';\n *\n * const MyComponent = () => (\n *   <Surface style={styles.surface} elevation={4}>\n *      <Text>Surface</Text>\n *   </Surface>\n * );\n *\n * export default MyComponent;\n *\n * const styles = StyleSheet.create({\n *   surface: {\n *     padding: 8,\n *     height: 80,\n *     width: 80,\n *     alignItems: 'center',\n *     justifyContent: 'center',\n *   },\n * });\n * ```\n */\nconst Surface = forwardRef<View, Props>(\n  (\n    {\n      elevation = 1,\n      children,\n      theme: overridenTheme,\n      style,\n      testID = 'surface',\n      mode = 'elevated',\n      ...props\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(overridenTheme);\n\n    if (!theme.isV3)\n      return (\n        <MD2Surface {...props} theme={theme} style={style} ref={ref}>\n          {children}\n        </MD2Surface>\n      );\n\n    const { colors } = theme;\n\n    const inputRange = [0, 1, 2, 3, 4, 5];\n\n    const backgroundColor = (() => {\n      if (isAnimatedValue(elevation)) {\n        return elevation.interpolate({\n          inputRange,\n          outputRange: inputRange.map((elevation) => {\n            return colors.elevation?.[`level${elevation as MD3Elevation}`];\n          }),\n        });\n      }\n\n      return colors.elevation?.[`level${elevation}`];\n    })();\n\n    const isElevated = mode === 'elevated';\n\n    if (Platform.OS === 'web') {\n      const { pointerEvents = 'auto' } = props;\n      return (\n        <Animated.View\n          {...props}\n          pointerEvents={pointerEvents}\n          ref={ref}\n          testID={testID}\n          style={[\n            { backgroundColor },\n            elevation && isElevated ? shadow(elevation, theme.isV3) : null,\n            style,\n          ]}\n        >\n          {children}\n        </Animated.View>\n      );\n    }\n\n    if (Platform.OS === 'android') {\n      const elevationLevel = [0, 3, 6, 9, 12, 15];\n\n      const getElevationAndroid = () => {\n        if (isAnimatedValue(elevation)) {\n          return elevation.interpolate({\n            inputRange,\n            outputRange: elevationLevel,\n          });\n        }\n\n        return elevationLevel[elevation];\n      };\n\n      const { margin, padding, transform, borderRadius } = (StyleSheet.flatten(\n        style\n      ) || {}) as ViewStyle;\n\n      const outerLayerStyles = { margin, padding, transform, borderRadius };\n      const sharedStyle = [{ backgroundColor }, style];\n\n      return (\n        <Animated.View\n          {...props}\n          testID={testID}\n          ref={ref}\n          style={[\n            {\n              backgroundColor,\n              transform,\n            },\n            outerLayerStyles,\n            sharedStyle,\n            isElevated && {\n              elevation: getElevationAndroid(),\n            },\n          ]}\n        >\n          {children}\n        </Animated.View>\n      );\n    }\n\n    return (\n      <SurfaceIOS\n        {...props}\n        ref={ref}\n        elevation={elevation}\n        backgroundColor={backgroundColor}\n        style={style}\n        testID={testID}\n        mode={mode}\n      >\n        {children}\n      </SurfaceIOS>\n    );\n  }\n);\n\nexport default Surface;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAW9B,SAASC,gBAAgB;AACzB,OAAOC,OAAO,IAAIC,eAAe;AACjC,OAAOC,MAAM;AAEb,SAASC,UAAU;AACnB,SAASC,WAAW;AAuCpB,IAAMC,UAAU,GAAGF,UAAU,CAC3B,UAAAG,IAAA,EAAqEC,GAAG,EAAK;EAAA,IAA1EC,KAAK,GAA2DF,IAAA,CAAhEE,KAAK;IAASC,aAAa,GAAqCH,IAAA,CAAzDI,KAAK;IAAoBC,IAAA,GAAAC,wBAAA,CAAgCN,IAAA,EAAAO,SAAA;EACjE,IAAAC,KAAA,GAA2BhB,UAAU,CAACiB,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAe;IAAAQ,eAAA,GAAAF,KAAA,CAAhEG,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAG,IAAAA,eAAA;EACpB,IAAAE,iBAAA,GAA4CnB,gBAAgB,CAACU,aAAa,CAAC;IAA7DU,WAAW,GAAAD,iBAAA,CAAjBE,IAAI;IAAeC,IAAI,GAAAH,iBAAA,CAAJG,IAAI;IAAEC,MAAA,GAAAJ,iBAAA,CAAAI,MAAA;EAEjC,OACE3B,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA;IACZlB,GAAG,EAAEA;EAAI,GACLI,IAAI;IACRH,KAAK,EAAE,CACL;MACEkB,eAAe,EACbP,WAAW,IAAIE,IAAI,KAAK,UAAU,GAC9BrB,OAAO,CAACiB,SAAS,EAAEK,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,OAAO,CAAC,GACnCL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK;IAChB,CAAC,EACDV,SAAS,GAAGf,MAAM,CAACe,SAAS,CAAC,GAAG,IAAI,EACpCT,KAAK;EACL,EACH,CAAC;AAEN,CACF,CAAC;AAED,IAAMoB,yBAA8C,GAAG,CACrD,UAAU,EACV,WAAW,EACX,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,YAAY,EACZ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,CACV;AAED,IAAMC,WAAW,GAAG,MAAM;AAC1B,IAAMC,qBAAqB,GAAG,CAC5B;EACEC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;AACnC,CAAC,EACD;EACEF,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACjC,CAAC,CACF;AACD,IAAMC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,SAASC,sBAAsBA,CAC7BlB,SAAoB,EACpBmB,KAAY,EACgC;EAC5C,IAAInC,eAAe,CAACgB,SAAS,CAAC,EAAE;IAC9B,OAAO;MACLY,WAAW,EAAXA,WAAW;MACXE,aAAa,EAAEd,SAAS,CAACoB,WAAW,CAAC;QACnCH,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBI,WAAW,EAAE,CAAC,CAAC,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,CAAC;QAC5DQ,WAAW,EAAE;MACf,CAAC,CAAC;MACFC,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRT,MAAM,EAAEf,SAAS,CAACoB,WAAW,CAAC;UAC5BH,UAAU,EAAVA,UAAU;UACVI,WAAW,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACJ;QAC5C,CAAC;MACH,CAAC;MACDC,YAAY,EAAEhB,SAAS,CAACoB,WAAW,CAAC;QAClCH,UAAU,EAAVA,UAAU;QACVI,WAAW,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACH;MAC5C,CAAC;IACH,CAAC;EACH;EAEA,OAAO;IACLJ,WAAW,EAAXA,WAAW;IACXE,aAAa,EAAEd,SAAS,GAAGa,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,GAAG,CAAC;IACzES,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRT,MAAM,EAAEF,qBAAqB,CAACM,KAAK,CAAC,CAACJ,MAAM,CAACf,SAAS;IACvD,CAAC;IACDgB,YAAY,EAAEH,qBAAqB,CAACM,KAAK,CAAC,CAACH,YAAY,CAAChB,SAAS;EACnE,CAAC;AACH;AAEA,IAAMyB,UAAU,GAAGvC,UAAU,CAO3B,UAAAwC,KAAA,EAUEpC,GAAG,EACA;EAAA,IATDU,SAAS,GAOV0B,KAAA,CAPC1B,SAAS;IACTT,KAAK,GAMNmC,KAAA,CANCnC,KAAK;IACLkB,eAAe,GAKhBiB,KAAA,CALCjB,eAAe;IACfkB,MAAM,GAIPD,KAAA,CAJCC,MAAM;IACNC,QAAQ,GAGTF,KAAA,CAHCE,QAAQ;IAAAC,UAAA,GAGTH,KAAA,CAFCtB,IAAI;IAAJA,IAAI,GAAAyB,UAAA,cAAG,UAAU,GAAAA,UAAA;IACdC,KAAA,GAAAnC,wBAAA,CACJ+B,KAAA,EAAAK,UAAA;EAGD,IAAAC,cAAA,GAAqDtD,KAAK,CAACuD,OAAO,CAAC,YAAM;MACvE,IAAMC,eAAe,GAAIrD,UAAU,CAACiB,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAe;MAEtE,IAAA4C,YAAA,GACEhD,WAAW,CACT+C,eAAe,EACd,UAAA3C,KAAK;UAAA,OACJoB,yBAAyB,CAACyB,QAAQ,CAAC7C,KAAK,CAAC,IACzCA,KAAK,CAAC8C,UAAU,CAAC,QAAQ,CAAC;QAAA,GAC3B,UAAA9C,KAAK;UAAA,OAAKA,KAAK,CAAC8C,UAAU,CAAC,QAAQ,CAAC,IAAI9C,KAAK,CAAC+C,QAAQ,CAAC,QAAQ,CAClE;QAAA,EAAC;QAAAC,aAAA,GAAAC,cAAA,CAAAL,YAAA;QAPIM,cAAc,GAAAF,aAAA;QAAEG,gBAAgB,GAAAH,aAAA;QAAEI,kBAAkB,GAAAJ,aAAA;MAS3D,IACEK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCL,cAAc,CAACM,QAAQ,KAAK,QAAQ,IACpC/C,SAAS,KAAK,CAAC,EACf;QACAgD,OAAO,CAACC,IAAI,CACV,uKACF,CAAC;MACH;MAEA,IAAMC,OAAO,GAAGhB,eAAe,CAACzB,eAAe,IAAIA,eAAe;MAElE,IAAM0C,UAAU,GAAG/C,IAAI,KAAK,UAAU;MAEtC,IAAMgD,oBAAoB,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACpBF,UAAU,IAAIjC,sBAAsB,CAAClB,SAAS,EAAE,CAAC,CAAC,GACnD0C,gBAAgB,GAChBC,kBAAkB;QACrBlC,eAAe,EAAEyC;MAAA,EAClB;MAED,IAAMI,oBAAoB,GAAAD,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACpBF,UAAU,IAAIjC,sBAAsB,CAAClB,SAAS,EAAE,CAAC,CAAC,GACnDyC,cAAc,GACdE,kBAAkB;QACrBY,IAAI,EAAErB,eAAe,CAACnB,MAAM,GAAG,CAAC,GAAGyC,SAAS;QAC5C/C,eAAe,EAAEyC;MAAA,EAClB;MAED,OAAO,CAACE,oBAAoB,EAAEE,oBAAoB,CAAC;IACrD,CAAC,EAAE,CAAC/D,KAAK,EAAES,SAAS,EAAES,eAAe,EAAEL,IAAI,CAAC,CAAC;IAAAqD,eAAA,GAAAjB,cAAA,CAAAR,cAAA;IA1CtCoB,oBAAoB,GAAAK,eAAA;IAAEH,oBAAoB,GAAAG,eAAA;EA4CjD,OACE/E,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI;IACZjB,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAE6D,oBAAqB;IAC5BzB,MAAM,EAAG,GAAEA,MAAO;EAAc,GAEhCjD,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA,KAAKsB,KAAK;IAAEvC,KAAK,EAAE+D,oBAAqB;IAAC3B,MAAM,EAAEA;EAAO,IACnEC,QACY,CACF,CAAC;AAEpB,CACF,CAAC;AAiCD,IAAM8B,OAAO,GAAGxE,UAAU,CACxB,UAAAyE,KAAA,EAUErE,GAAG,EACA;EAAA,IAAAsE,eAAA,GAFKD,KAAA,CAPN3D,SAAS;IAATA,SAAS,GAAA4D,eAAA,cAAG,CAAC,GAAAA,eAAA;IACbhC,QAAQ,GAMF+B,KAAA,CANN/B,QAAQ;IACDiC,cAAc,GAKfF,KAAA,CALNlE,KAAK;IACLF,KAAK,GAICoE,KAAA,CAJNpE,KAAK;IAAAuE,YAAA,GAICH,KAAA,CAHNhC,MAAM;IAANA,MAAM,GAAAmC,YAAA,cAAG,SAAS,GAAAA,YAAA;IAAAC,UAAA,GAGZJ,KAAA,CAFNvD,IAAI;IAAJA,IAAI,GAAA2D,UAAA,cAAG,UAAU,GAAAA,UAAA;IACdjC,KAAA,GAAAnC,wBAAA,CACGgE,KAAA,EAAAK,UAAA;EAGR,IAAMvE,KAAK,GAAGX,gBAAgB,CAAC+E,cAAc,CAAC;EAE9C,IAAI,CAACpE,KAAK,CAACwE,IAAI,EACb,OACEvF,KAAA,CAAA4B,aAAA,CAAClB,UAAU,EAAAoB,QAAA,KAAKsB,KAAK;IAAErC,KAAK,EAAEA,KAAM;IAACF,KAAK,EAAEA,KAAM;IAACD,GAAG,EAAEA;EAAI,IACzDsC,QACS,CAAC;EAGjB,IAAQvB,MAAA,GAAWZ,KAAK,CAAhBY,MAAA;EAER,IAAMY,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAErC,IAAMR,eAAe,GAAI,UAAAyD,kBAAA,EAAM;IAC7B,IAAIlF,eAAe,CAACgB,SAAS,CAAC,EAAE;MAC9B,OAAOA,SAAS,CAACoB,WAAW,CAAC;QAC3BH,UAAU,EAAVA,UAAU;QACVI,WAAW,EAAEJ,UAAU,CAACkD,GAAG,CAAE,UAAAnE,SAAS,EAAK;UAAA,IAAAoE,iBAAA;UACzC,QAAAA,iBAAA,GAAO/D,MAAM,CAACL,SAAS,cAAAoE,iBAAA,uBAAhBA,iBAAA,CAAoB,QAAOpE,SAA0B,EAAC,CAAC;QAChE,CAAC;MACH,CAAC,CAAC;IACJ;IAEA,QAAAkE,kBAAA,GAAO7D,MAAM,CAACL,SAAS,cAAAkE,kBAAA,uBAAhBA,kBAAA,CAAoB,QAAOlE,SAAU,EAAC,CAAC;EAChD,CAAC,CAAE,CAAC;EAEJ,IAAMmD,UAAU,GAAG/C,IAAI,KAAK,UAAU;EAEtC,IAAIxB,QAAQ,CAACyF,EAAE,KAAK,KAAK,EAAE;IACzB,IAAAC,oBAAA,GAAmCxC,KAAK,CAAhCyC,aAAa;MAAbA,aAAa,GAAAD,oBAAA,cAAG,SAAAA,oBAAA;IACxB,OACE5F,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA,KACRsB,KAAK;MACTyC,aAAa,EAAEA,aAAc;MAC7BjF,GAAG,EAAEA,GAAI;MACTqC,MAAM,EAAEA,MAAO;MACfpC,KAAK,EAAE,CACL;QAAEkB,eAAA,EAAAA;MAAgB,CAAC,EACnBT,SAAS,IAAImD,UAAU,GAAGlE,MAAM,CAACe,SAAS,EAAEP,KAAK,CAACwE,IAAI,CAAC,GAAG,IAAI,EAC9D1E,KAAK;IACL,IAEDqC,QACY,CAAC;EAEpB;EAEA,IAAIhD,QAAQ,CAACyF,EAAE,KAAK,SAAS,EAAE;IAC7B,IAAMG,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IAE3C,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;MAChC,IAAIzF,eAAe,CAACgB,SAAS,CAAC,EAAE;QAC9B,OAAOA,SAAS,CAACoB,WAAW,CAAC;UAC3BH,UAAU,EAAVA,UAAU;UACVI,WAAW,EAAEmD;QACf,CAAC,CAAC;MACJ;MAEA,OAAOA,cAAc,CAACxE,SAAS,CAAC;IAClC,CAAC;IAED,IAAA0E,KAAA,GAAsD7F,UAAU,CAACiB,OAAO,CACtEP,KACF,CAAC,IAAI,CAAC,CAAe;MAFboF,MAAM,GAAAD,KAAA,CAANC,MAAM;MAAEC,OAAO,GAAAF,KAAA,CAAPE,OAAO;MAAEC,SAAS,GAAAH,KAAA,CAATG,SAAS;MAAEC,YAAA,GAAAJ,KAAA,CAAAI,YAAA;IAIpC,IAAMpC,gBAAgB,GAAG;MAAEiC,MAAM,EAANA,MAAM;MAAEC,OAAO,EAAPA,OAAO;MAAEC,SAAS,EAATA,SAAS;MAAEC,YAAA,EAAAA;IAAa,CAAC;IACrE,IAAMC,WAAW,GAAG,CAAC;MAAEtE,eAAA,EAAAA;IAAgB,CAAC,EAAElB,KAAK,CAAC;IAEhD,OACEb,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA,KACRsB,KAAK;MACTH,MAAM,EAAEA,MAAO;MACfrC,GAAG,EAAEA,GAAI;MACTC,KAAK,EAAE,CACL;QACEkB,eAAe,EAAfA,eAAe;QACfoE,SAAA,EAAAA;MACF,CAAC,EACDnC,gBAAgB,EAChBqC,WAAW,EACX5B,UAAU,IAAI;QACZnD,SAAS,EAAEyE,mBAAmB,CAAC;MACjC,CAAC;IACD,IAED7C,QACY,CAAC;EAEpB;EAEA,OACElD,KAAA,CAAA4B,aAAA,CAACmB,UAAU,EAAAjB,QAAA,KACLsB,KAAK;IACTxC,GAAG,EAAEA,GAAI;IACTU,SAAS,EAAEA,SAAU;IACrBS,eAAe,EAAEA,eAAgB;IACjClB,KAAK,EAAEA,KAAM;IACboC,MAAM,EAAEA,MAAO;IACfvB,IAAI,EAAEA;EAAK,IAEVwB,QACS,CAAC;AAEjB,CACF,CAAC;AAED,eAAe8B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}