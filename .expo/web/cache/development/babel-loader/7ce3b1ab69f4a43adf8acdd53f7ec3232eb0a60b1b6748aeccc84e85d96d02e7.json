{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport FAB from \"./FAB\";\nimport { getFABGroupColors } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Card from \"../Card/Card\";\nimport Text from \"../Typography/Text\";\nvar AnimatedPressable = Animated.createAnimatedComponent(Pressable);\nvar FABGroup = function FABGroup(_ref) {\n  var actions = _ref.actions,\n    icon = _ref.icon,\n    open = _ref.open,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    toggleStackOnLongPress = _ref.toggleStackOnLongPress,\n    accessibilityLabel = _ref.accessibilityLabel,\n    themeOverrides = _ref.theme,\n    style = _ref.style,\n    fabStyle = _ref.fabStyle,\n    visible = _ref.visible,\n    label = _ref.label,\n    testID = _ref.testID,\n    onStateChange = _ref.onStateChange,\n    colorProp = _ref.color,\n    _ref$delayLongPress = _ref.delayLongPress,\n    delayLongPress = _ref$delayLongPress === void 0 ? 200 : _ref$delayLongPress,\n    _ref$variant = _ref.variant,\n    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,\n    _ref$enableLongPressW = _ref.enableLongPressWhenStackOpened,\n    enableLongPressWhenStackOpened = _ref$enableLongPressW === void 0 ? false : _ref$enableLongPressW,\n    customBackdropColor = _ref.backdropColor,\n    rippleColor = _ref.rippleColor;\n  var theme = useInternalTheme(themeOverrides);\n  var _useSafeAreaInsets = useSafeAreaInsets(),\n    top = _useSafeAreaInsets.top,\n    bottom = _useSafeAreaInsets.bottom,\n    right = _useSafeAreaInsets.right,\n    left = _useSafeAreaInsets.left;\n  var _React$useRef = React.useRef(new Animated.Value(0)),\n    backdrop = _React$useRef.current;\n  var animations = React.useRef(actions.map(function () {\n    return new Animated.Value(open ? 1 : 0);\n  }));\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isClosingAnimationFinished = _React$useState2[0],\n    setIsClosingAnimationFinished = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevActions = _React$useState4[0],\n    setPrevActions = _React$useState4[1];\n  var scale = theme.animation.scale;\n  var isV3 = theme.isV3;\n  React.useEffect(function () {\n    if (open) {\n      setIsClosingAnimationFinished(false);\n      Animated.parallel([Animated.timing(backdrop, {\n        toValue: 1,\n        duration: 250 * scale,\n        useNativeDriver: true\n      }), Animated.stagger(isV3 ? 15 : 50 * scale, animations.current.map(function (animation) {\n        return Animated.timing(animation, {\n          toValue: 1,\n          duration: 150 * scale,\n          useNativeDriver: true\n        });\n      }).reverse())]).start();\n    } else {\n      Animated.parallel([Animated.timing(backdrop, {\n        toValue: 0,\n        duration: 200 * scale,\n        useNativeDriver: true\n      })].concat(_toConsumableArray(animations.current.map(function (animation) {\n        return Animated.timing(animation, {\n          toValue: 0,\n          duration: 150 * scale,\n          useNativeDriver: true\n        });\n      })))).start(function (_ref2) {\n        var finished = _ref2.finished;\n        if (finished) {\n          setIsClosingAnimationFinished(true);\n        }\n      });\n    }\n  }, [open, actions, backdrop, scale, isV3]);\n  var close = function close() {\n    return onStateChange({\n      open: false\n    });\n  };\n  var toggle = function toggle() {\n    return onStateChange({\n      open: !open\n    });\n  };\n  var handlePress = function handlePress(e) {\n    onPress === null || onPress === void 0 ? void 0 : onPress(e);\n    if (!toggleStackOnLongPress || open) {\n      toggle();\n    }\n  };\n  var handleLongPress = function handleLongPress(e) {\n    if (!open || enableLongPressWhenStackOpened) {\n      onLongPress === null || onLongPress === void 0 ? void 0 : onLongPress(e);\n      if (toggleStackOnLongPress) {\n        toggle();\n      }\n    }\n  };\n  var _getFABGroupColors = getFABGroupColors({\n      theme: theme,\n      customBackdropColor: customBackdropColor\n    }),\n    labelColor = _getFABGroupColors.labelColor,\n    backdropColor = _getFABGroupColors.backdropColor,\n    stackedFABBackgroundColor = _getFABGroupColors.stackedFABBackgroundColor;\n  var backdropOpacity = open ? backdrop.interpolate({\n    inputRange: [0, 0.5, 1],\n    outputRange: [0, 1, 1]\n  }) : backdrop;\n  var opacities = animations.current;\n  var scales = opacities.map(function (opacity) {\n    return open ? opacity.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0.5, 1]\n    }) : 1;\n  });\n  var translations = opacities.map(function (opacity) {\n    return open ? opacity.interpolate({\n      inputRange: [0, 1],\n      outputRange: [24, -8]\n    }) : -8;\n  });\n  var labelTranslations = opacities.map(function (opacity) {\n    return open ? opacity.interpolate({\n      inputRange: [0, 1],\n      outputRange: [8, -8]\n    }) : -8;\n  });\n  var containerPaddings = {\n    paddingBottom: bottom,\n    paddingRight: right,\n    paddingLeft: left,\n    paddingTop: top\n  };\n  var actionsContainerVisibility = {\n    display: isClosingAnimationFinished ? 'none' : 'flex'\n  };\n  if (actions.length !== (prevActions === null || prevActions === void 0 ? void 0 : prevActions.length)) {\n    animations.current = actions.map(function (_, i) {\n      return animations.current[i] || new Animated.Value(open ? 1 : 0);\n    });\n    setPrevActions(actions);\n  }\n  return React.createElement(View, {\n    pointerEvents: \"box-none\",\n    style: [styles.container, containerPaddings, style]\n  }, React.createElement(AnimatedPressable, {\n    accessibilityRole: \"button\",\n    onPress: close,\n    pointerEvents: open ? 'auto' : 'none',\n    style: [styles.backdrop, {\n      opacity: backdropOpacity,\n      backgroundColor: backdropColor\n    }]\n  }), React.createElement(View, {\n    pointerEvents: \"box-none\",\n    style: styles.safeArea\n  }, React.createElement(View, {\n    pointerEvents: open ? 'box-none' : 'none',\n    style: actionsContainerVisibility\n  }, actions.map(function (it, i) {\n    var _it$labelTextColor;\n    var labelTextStyle = _objectSpread({\n      color: (_it$labelTextColor = it.labelTextColor) != null ? _it$labelTextColor : labelColor\n    }, isV3 ? theme.fonts.titleMedium : {});\n    var marginHorizontal = typeof it.size === 'undefined' || it.size === 'small' ? 24 : 16;\n    var accessibilityLabel = typeof it.accessibilityLabel !== 'undefined' ? it.accessibilityLabel : it.label;\n    var size = typeof it.size !== 'undefined' ? it.size : 'small';\n    var handleActionPress = function handleActionPress(e) {\n      it.onPress(e);\n      close();\n    };\n    return React.createElement(View, {\n      key: i,\n      style: [styles.item, {\n        marginHorizontal: marginHorizontal\n      }, it.wrapperStyle],\n      pointerEvents: open ? 'box-none' : 'none',\n      accessibilityRole: \"button\",\n      importantForAccessibility: open ? 'yes' : 'no-hide-descendants',\n      accessibilityElementsHidden: !open,\n      accessible: open,\n      accessibilityLabel: accessibilityLabel\n    }, it.label && React.createElement(View, null, React.createElement(Card, {\n      mode: isV3 ? 'contained' : 'elevated',\n      onPress: handleActionPress,\n      accessibilityHint: it.accessibilityHint,\n      importantForAccessibility: \"no-hide-descendants\",\n      accessibilityElementsHidden: true,\n      style: [styles.containerStyle, {\n        transform: [isV3 ? {\n          translateY: labelTranslations[i]\n        } : {\n          scale: scales[i]\n        }],\n        opacity: opacities[i]\n      }, isV3 && styles.v3ContainerStyle, it.containerStyle]\n    }, React.createElement(Text, {\n      variant: \"titleMedium\",\n      importantForAccessibility: \"no-hide-descendants\",\n      accessibilityElementsHidden: true,\n      style: [labelTextStyle, it.labelStyle],\n      maxFontSizeMultiplier: it.labelMaxFontSizeMultiplier\n    }, it.label))), React.createElement(FAB, {\n      size: size,\n      icon: it.icon,\n      color: it.color,\n      style: [{\n        transform: [{\n          scale: scales[i]\n        }],\n        opacity: opacities[i],\n        backgroundColor: stackedFABBackgroundColor\n      }, isV3 && {\n        transform: [{\n          translateY: translations[i]\n        }]\n      }, it.style],\n      accessibilityElementsHidden: true,\n      theme: theme,\n      onPress: handleActionPress,\n      importantForAccessibility: \"no-hide-descendants\",\n      testID: it.testID,\n      visible: open,\n      rippleColor: it.rippleColor\n    }));\n  })), React.createElement(FAB, {\n    onPress: handlePress,\n    onLongPress: handleLongPress,\n    delayLongPress: delayLongPress,\n    icon: icon,\n    color: colorProp,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"button\",\n    accessibilityState: {\n      expanded: open\n    },\n    style: [styles.fab, fabStyle],\n    theme: theme,\n    visible: visible,\n    label: label,\n    testID: testID,\n    variant: variant,\n    rippleColor: rippleColor\n  })));\n};\nFABGroup.displayName = 'FAB.Group';\nexport default FABGroup;\nexport { FABGroup };\nvar styles = StyleSheet.create({\n  safeArea: {\n    alignItems: 'flex-end'\n  },\n  container: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    justifyContent: 'flex-end'\n  }),\n  fab: {\n    marginHorizontal: 16,\n    marginBottom: 16,\n    marginTop: 0\n  },\n  backdrop: _objectSpread({}, StyleSheet.absoluteFillObject),\n  containerStyle: {\n    borderRadius: 5,\n    paddingHorizontal: 12,\n    paddingVertical: 6,\n    marginVertical: 8,\n    marginHorizontal: 16,\n    elevation: 2\n  },\n  item: {\n    marginBottom: 16,\n    flexDirection: 'row',\n    justifyContent: 'flex-end',\n    alignItems: 'center'\n  },\n  v3ContainerStyle: {\n    backgroundColor: 'transparent',\n    elevation: 0\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "Pressable", "StyleSheet", "View", "useSafeAreaInsets", "FAB", "getFABGroupColors", "useInternalTheme", "Card", "Text", "AnimatedPressable", "createAnimatedComponent", "FABGroup", "_ref", "actions", "icon", "open", "onPress", "onLongPress", "toggleStackOnLongPress", "accessibilityLabel", "themeOverrides", "theme", "style", "fabStyle", "visible", "label", "testID", "onStateChange", "colorProp", "color", "_ref$delayLongPress", "delayLongPress", "_ref$variant", "variant", "_ref$enableLongPressW", "enableLongPressWhenStackOpened", "customBackdropColor", "backdropColor", "rippleColor", "_useSafeAreaInsets", "top", "bottom", "right", "left", "_React$useRef", "useRef", "Value", "backdrop", "current", "animations", "map", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "isClosingAnimationFinished", "setIsClosingAnimationFinished", "_React$useState3", "_React$useState4", "prevActions", "setPrevActions", "scale", "animation", "isV3", "useEffect", "parallel", "timing", "toValue", "duration", "useNativeDriver", "stagger", "reverse", "start", "concat", "_toConsumableArray", "_ref2", "finished", "close", "toggle", "handlePress", "e", "handleLongPress", "_getFABGroupColors", "labelColor", "stackedFABBackgroundColor", "backdropOpacity", "interpolate", "inputRange", "outputRange", "opacities", "scales", "opacity", "translations", "labelTranslations", "containerPaddings", "paddingBottom", "paddingRight", "paddingLeft", "paddingTop", "actionsContainerVisibility", "display", "length", "_", "i", "createElement", "pointerEvents", "styles", "container", "accessibilityRole", "backgroundColor", "safeArea", "it", "_it$labelTextColor", "labelTextStyle", "_objectSpread", "labelTextColor", "fonts", "titleMedium", "marginHorizontal", "size", "handleActionPress", "key", "item", "wrapperStyle", "importantForAccessibility", "accessibilityElementsHidden", "accessible", "mode", "accessibilityHint", "containerStyle", "transform", "translateY", "v3ContainerStyle", "labelStyle", "maxFontSizeMultiplier", "labelMaxFontSizeMultiplier", "accessibilityState", "expanded", "fab", "displayName", "create", "alignItems", "absoluteFillObject", "justifyContent", "marginBottom", "marginTop", "borderRadius", "paddingHorizontal", "paddingVertical", "marginVertical", "elevation", "flexDirection"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/FAB/FABGroup.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  ColorValue,\n  GestureResponderEvent,\n  Pressable,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\n\nimport FAB from './FAB';\nimport { getFABGroupColors } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport Card from '../Card/Card';\nimport type { IconSource } from '../Icon';\nimport Text from '../Typography/Text';\n\nconst AnimatedPressable = Animated.createAnimatedComponent(Pressable);\n\nexport type Props = {\n  /**\n   * Action items to display in the form of a speed dial.\n   * An action item should contain the following properties:\n   * - `icon`: icon to display (required)\n   * - `label`: optional label text\n   * - `color`: custom icon color of the action item\n   * - `labelTextColor`: custom label text color of the action item\n   * - `accessibilityLabel`: accessibility label for the action, uses label by default if specified\n   * - `accessibilityHint`: accessibility hint for the action\n   * - `style`: pass additional styles for the fab item, for example, `backgroundColor`\n   * - `containerStyle`: pass additional styles for the fab item label container, for example, `backgroundColor` @supported Available in 5.x\n   * - `wrapperStyle`: pass additional styles for the wrapper of the action item.\n   * - `labelStyle`: pass additional styles for the fab item label, for example, `fontSize`\n   * - `labelMaxFontSizeMultiplier`: specifies the largest possible scale a title font can reach.\n   * - `onPress`: callback that is called when `FAB` is pressed (required)\n   * - `onLongPress`: callback that is called when `FAB` is long pressed\n   * - `toggleStackOnLongPress`: callback that is called when `FAB` is long pressed\n   * - `size`: size of action item. Defaults to `small`. @supported Available in v5.x\n   * - `testID`: testID to be used on tests\n   * - `rippleColor`: color of the ripple effect.\n   */\n  actions: Array<{\n    icon: IconSource;\n    label?: string;\n    color?: string;\n    labelTextColor?: string;\n    accessibilityLabel?: string;\n    accessibilityHint?: string;\n    style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n    containerStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n    wrapperStyle?: StyleProp<ViewStyle>;\n    labelStyle?: StyleProp<TextStyle>;\n    labelMaxFontSizeMultiplier?: number;\n    onPress: (e: GestureResponderEvent) => void;\n    size?: 'small' | 'medium';\n    testID?: string;\n    rippleColor?: ColorValue;\n  }>;\n  /**\n   * Icon to display for the `FAB`.\n   * You can toggle it based on whether the speed dial is open to display a different icon.\n   */\n  icon: IconSource;\n  /**\n   * Accessibility label for the FAB. This is read by the screen reader when the user taps the FAB.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Custom color for the `FAB`.\n   */\n  color?: string;\n  /**\n   * Custom backdrop color for opened speed dial background.\n   */\n  backdropColor?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Function to execute on pressing the `FAB`.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long pressing the `FAB`.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Makes actions stack appear on long press instead of on press.\n   */\n  toggleStackOnLongPress?: boolean;\n  /**\n   * Changes the delay for long press reaction.\n   */\n  delayLongPress?: number;\n  /**\n   * Allows for onLongPress when stack is opened.\n   */\n  enableLongPressWhenStackOpened?: boolean;\n  /**\n   * Whether the speed dial is open.\n   */\n  open: boolean;\n  /**\n   * Callback which is called on opening and closing the speed dial.\n   * The open state needs to be updated when it's called, otherwise the change is dropped.\n   */\n  onStateChange: (state: { open: boolean }) => void;\n  /**\n   * Whether `FAB` is currently visible.\n   */\n  visible: boolean;\n  /**\n   * Style for the group. You can use it to pass additional styles if you need.\n   * For example, you can set an additional padding if you have a tab bar at the bottom.\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Style for the FAB. It allows to pass the FAB button styles, such as backgroundColor.\n   */\n  fabStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Color mappings variant for combinations of container and icon colors.\n   */\n  variant?: 'primary' | 'secondary' | 'tertiary' | 'surface';\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Optional label for `FAB`.\n   */\n  label?: string;\n  /**\n   * Pass down testID from Group props to FAB.\n   */\n  testID?: string;\n};\n\n/**\n * A component to display a stack of FABs with related actions in a speed dial.\n * To render the group above other components, you'll need to wrap it with the [`Portal`](../Portal) component.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { FAB, Portal, PaperProvider } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [state, setState] = React.useState({ open: false });\n *\n *   const onStateChange = ({ open }) => setState({ open });\n *\n *   const { open } = state;\n *\n *   return (\n *     <PaperProvider>\n *       <Portal>\n *         <FAB.Group\n *           open={open}\n *           visible\n *           icon={open ? 'calendar-today' : 'plus'}\n *           actions={[\n *             { icon: 'plus', onPress: () => console.log('Pressed add') },\n *             {\n *               icon: 'star',\n *               label: 'Star',\n *               onPress: () => console.log('Pressed star'),\n *             },\n *             {\n *               icon: 'email',\n *               label: 'Email',\n *               onPress: () => console.log('Pressed email'),\n *             },\n *             {\n *               icon: 'bell',\n *               label: 'Remind',\n *               onPress: () => console.log('Pressed notifications'),\n *             },\n *           ]}\n *           onStateChange={onStateChange}\n *           onPress={() => {\n *             if (open) {\n *               // do something if the speed dial is open\n *             }\n *           }}\n *         />\n *       </Portal>\n *     </PaperProvider>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst FABGroup = ({\n  actions,\n  icon,\n  open,\n  onPress,\n  onLongPress,\n  toggleStackOnLongPress,\n  accessibilityLabel,\n  theme: themeOverrides,\n  style,\n  fabStyle,\n  visible,\n  label,\n  testID,\n  onStateChange,\n  color: colorProp,\n  delayLongPress = 200,\n  variant = 'primary',\n  enableLongPressWhenStackOpened = false,\n  backdropColor: customBackdropColor,\n  rippleColor,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { top, bottom, right, left } = useSafeAreaInsets();\n\n  const { current: backdrop } = React.useRef<Animated.Value>(\n    new Animated.Value(0)\n  );\n  const animations = React.useRef<Animated.Value[]>(\n    actions.map(() => new Animated.Value(open ? 1 : 0))\n  );\n\n  const [isClosingAnimationFinished, setIsClosingAnimationFinished] =\n    React.useState(false);\n\n  const [prevActions, setPrevActions] = React.useState<\n    | {\n        icon: IconSource;\n        label?: string;\n        color?: string;\n        accessibilityLabel?: string;\n        style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n        onPress: (e: GestureResponderEvent) => void;\n        testID?: string;\n      }[]\n    | null\n  >(null);\n\n  const { scale } = theme.animation;\n  const { isV3 } = theme;\n\n  React.useEffect(() => {\n    if (open) {\n      setIsClosingAnimationFinished(false);\n      Animated.parallel([\n        Animated.timing(backdrop, {\n          toValue: 1,\n          duration: 250 * scale,\n          useNativeDriver: true,\n        }),\n        Animated.stagger(\n          isV3 ? 15 : 50 * scale,\n          animations.current\n            .map((animation) =>\n              Animated.timing(animation, {\n                toValue: 1,\n                duration: 150 * scale,\n                useNativeDriver: true,\n              })\n            )\n            .reverse()\n        ),\n      ]).start();\n    } else {\n      Animated.parallel([\n        Animated.timing(backdrop, {\n          toValue: 0,\n          duration: 200 * scale,\n          useNativeDriver: true,\n        }),\n        ...animations.current.map((animation) =>\n          Animated.timing(animation, {\n            toValue: 0,\n            duration: 150 * scale,\n            useNativeDriver: true,\n          })\n        ),\n      ]).start(({ finished }) => {\n        if (finished) {\n          setIsClosingAnimationFinished(true);\n        }\n      });\n    }\n  }, [open, actions, backdrop, scale, isV3]);\n\n  const close = () => onStateChange({ open: false });\n  const toggle = () => onStateChange({ open: !open });\n\n  const handlePress = (e: GestureResponderEvent) => {\n    onPress?.(e);\n    if (!toggleStackOnLongPress || open) {\n      toggle();\n    }\n  };\n\n  const handleLongPress = (e: GestureResponderEvent) => {\n    if (!open || enableLongPressWhenStackOpened) {\n      onLongPress?.(e);\n      if (toggleStackOnLongPress) {\n        toggle();\n      }\n    }\n  };\n\n  const { labelColor, backdropColor, stackedFABBackgroundColor } =\n    getFABGroupColors({ theme, customBackdropColor });\n\n  const backdropOpacity = open\n    ? backdrop.interpolate({\n        inputRange: [0, 0.5, 1],\n        outputRange: [0, 1, 1],\n      })\n    : backdrop;\n\n  const opacities = animations.current;\n  const scales = opacities.map((opacity) =>\n    open\n      ? opacity.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0.5, 1],\n        })\n      : 1\n  );\n\n  const translations = opacities.map((opacity) =>\n    open\n      ? opacity.interpolate({\n          inputRange: [0, 1],\n          outputRange: [24, -8],\n        })\n      : -8\n  );\n  const labelTranslations = opacities.map((opacity) =>\n    open\n      ? opacity.interpolate({\n          inputRange: [0, 1],\n          outputRange: [8, -8],\n        })\n      : -8\n  );\n\n  const containerPaddings = {\n    paddingBottom: bottom,\n    paddingRight: right,\n    paddingLeft: left,\n    paddingTop: top,\n  };\n\n  const actionsContainerVisibility: ViewStyle = {\n    display: isClosingAnimationFinished ? 'none' : 'flex',\n  };\n\n  if (actions.length !== prevActions?.length) {\n    animations.current = actions.map(\n      (_, i) => animations.current[i] || new Animated.Value(open ? 1 : 0)\n    );\n    setPrevActions(actions);\n  }\n\n  return (\n    <View\n      pointerEvents=\"box-none\"\n      style={[styles.container, containerPaddings, style]}\n    >\n      <AnimatedPressable\n        accessibilityRole=\"button\"\n        onPress={close}\n        pointerEvents={open ? 'auto' : 'none'}\n        style={[\n          styles.backdrop,\n          {\n            opacity: backdropOpacity,\n            backgroundColor: backdropColor,\n          },\n        ]}\n      />\n      <View pointerEvents=\"box-none\" style={styles.safeArea}>\n        <View\n          pointerEvents={open ? 'box-none' : 'none'}\n          style={actionsContainerVisibility}\n        >\n          {actions.map((it, i) => {\n            const labelTextStyle = {\n              color: it.labelTextColor ?? labelColor,\n              ...(isV3 ? theme.fonts.titleMedium : {}),\n            };\n            const marginHorizontal =\n              typeof it.size === 'undefined' || it.size === 'small' ? 24 : 16;\n            const accessibilityLabel =\n              typeof it.accessibilityLabel !== 'undefined'\n                ? it.accessibilityLabel\n                : it.label;\n            const size = typeof it.size !== 'undefined' ? it.size : 'small';\n\n            const handleActionPress = (e: GestureResponderEvent) => {\n              it.onPress(e);\n              close();\n            };\n\n            return (\n              <View\n                key={i} // eslint-disable-line react/no-array-index-key\n                style={[\n                  styles.item,\n                  {\n                    marginHorizontal,\n                  },\n                  it.wrapperStyle,\n                ]}\n                pointerEvents={open ? 'box-none' : 'none'}\n                accessibilityRole=\"button\"\n                importantForAccessibility={open ? 'yes' : 'no-hide-descendants'}\n                accessibilityElementsHidden={!open}\n                accessible={open}\n                accessibilityLabel={accessibilityLabel}\n              >\n                {it.label && (\n                  <View>\n                    <Card\n                      mode={isV3 ? 'contained' : 'elevated'}\n                      onPress={handleActionPress}\n                      accessibilityHint={it.accessibilityHint}\n                      importantForAccessibility=\"no-hide-descendants\"\n                      accessibilityElementsHidden={true}\n                      style={[\n                        styles.containerStyle,\n                        {\n                          transform: [\n                            isV3\n                              ? { translateY: labelTranslations[i] }\n                              : { scale: scales[i] },\n                          ],\n                          opacity: opacities[i],\n                        },\n                        isV3 && styles.v3ContainerStyle,\n                        it.containerStyle,\n                      ]}\n                    >\n                      <Text\n                        variant=\"titleMedium\"\n                        importantForAccessibility=\"no-hide-descendants\"\n                        accessibilityElementsHidden={true}\n                        style={[labelTextStyle, it.labelStyle]}\n                        maxFontSizeMultiplier={it.labelMaxFontSizeMultiplier}\n                      >\n                        {it.label}\n                      </Text>\n                    </Card>\n                  </View>\n                )}\n                <FAB\n                  size={size}\n                  icon={it.icon}\n                  color={it.color}\n                  style={[\n                    {\n                      transform: [{ scale: scales[i] }],\n                      opacity: opacities[i],\n                      backgroundColor: stackedFABBackgroundColor,\n                    },\n                    isV3 && { transform: [{ translateY: translations[i] }] },\n                    it.style,\n                  ]}\n                  accessibilityElementsHidden={true}\n                  theme={theme}\n                  onPress={handleActionPress}\n                  importantForAccessibility=\"no-hide-descendants\"\n                  testID={it.testID}\n                  visible={open}\n                  rippleColor={it.rippleColor}\n                />\n              </View>\n            );\n          })}\n        </View>\n        <FAB\n          onPress={handlePress}\n          onLongPress={handleLongPress}\n          delayLongPress={delayLongPress}\n          icon={icon}\n          color={colorProp}\n          accessibilityLabel={accessibilityLabel}\n          accessibilityRole=\"button\"\n          accessibilityState={{ expanded: open }}\n          style={[styles.fab, fabStyle]}\n          theme={theme}\n          visible={visible}\n          label={label}\n          testID={testID}\n          variant={variant}\n          rippleColor={rippleColor}\n        />\n      </View>\n    </View>\n  );\n};\n\nFABGroup.displayName = 'FAB.Group';\n\nexport default FABGroup;\n\n// @component-docs ignore-next-line\nexport { FABGroup };\n\nconst styles = StyleSheet.create({\n  safeArea: {\n    alignItems: 'flex-end',\n  },\n  container: {\n    ...StyleSheet.absoluteFillObject,\n    justifyContent: 'flex-end',\n  },\n  fab: {\n    marginHorizontal: 16,\n    marginBottom: 16,\n    marginTop: 0,\n  },\n  backdrop: {\n    ...StyleSheet.absoluteFillObject,\n  },\n  containerStyle: {\n    borderRadius: 5,\n    paddingHorizontal: 12,\n    paddingVertical: 6,\n    marginVertical: 8,\n    marginHorizontal: 16,\n    elevation: 2,\n  },\n  item: {\n    marginBottom: 16,\n    flexDirection: 'row',\n    justifyContent: 'flex-end',\n    alignItems: 'center',\n  },\n  // eslint-disable-next-line react-native/no-color-literals\n  v3ContainerStyle: {\n    backgroundColor: 'transparent',\n    elevation: 0,\n  },\n});\n"], "mappings": ";;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAa9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,OAAOC,GAAG;AACV,SAASC,iBAAiB;AAC1B,SAASC,gBAAgB;AAEzB,OAAOC,IAAI;AAEX,OAAOC,IAAI;AAEX,IAAMC,iBAAiB,GAAGV,QAAQ,CAACW,uBAAuB,CAACV,SAAS,CAAC;AAoLrE,IAAMW,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,IAAA,EAqBJ;EAAA,IApBXC,OAAO,GAoBDD,IAAA,CApBNC,OAAO;IACPC,IAAI,GAmBEF,IAAA,CAnBNE,IAAI;IACJC,IAAI,GAkBEH,IAAA,CAlBNG,IAAI;IACJC,OAAO,GAiBDJ,IAAA,CAjBNI,OAAO;IACPC,WAAW,GAgBLL,IAAA,CAhBNK,WAAW;IACXC,sBAAsB,GAehBN,IAAA,CAfNM,sBAAsB;IACtBC,kBAAkB,GAcZP,IAAA,CAdNO,kBAAkB;IACXC,cAAc,GAafR,IAAA,CAbNS,KAAK;IACLC,KAAK,GAYCV,IAAA,CAZNU,KAAK;IACLC,QAAQ,GAWFX,IAAA,CAXNW,QAAQ;IACRC,OAAO,GAUDZ,IAAA,CAVNY,OAAO;IACPC,KAAK,GASCb,IAAA,CATNa,KAAK;IACLC,MAAM,GAQAd,IAAA,CARNc,MAAM;IACNC,aAAa,GAOPf,IAAA,CAPNe,aAAa;IACNC,SAAS,GAMVhB,IAAA,CANNiB,KAAK;IAAAC,mBAAA,GAMClB,IAAA,CALNmB,cAAc;IAAdA,cAAc,GAAAD,mBAAA,cAAG,GAAG,GAAAA,mBAAA;IAAAE,YAAA,GAKdpB,IAAA,CAJNqB,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,SAAS,GAAAA,YAAA;IAAAE,qBAAA,GAIbtB,IAAA,CAHNuB,8BAA8B;IAA9BA,8BAA8B,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IACvBE,mBAAmB,GAE5BxB,IAAA,CAFNyB,aAAa;IACbC,WAAA,GACM1B,IAAA,CADN0B,WAAA;EAEA,IAAMjB,KAAK,GAAGf,gBAAgB,CAACc,cAAc,CAAC;EAC9C,IAAAmB,kBAAA,GAAqCpC,iBAAiB,CAAC,CAAC;IAAhDqC,GAAG,GAAAD,kBAAA,CAAHC,GAAG;IAAEC,MAAM,GAAAF,kBAAA,CAANE,MAAM;IAAEC,KAAK,GAAAH,kBAAA,CAALG,KAAK;IAAEC,IAAA,GAAAJ,kBAAA,CAAAI,IAAA;EAE5B,IAAAC,aAAA,GAA8B9C,KAAK,CAAC+C,MAAM,CACxC,IAAI9C,QAAQ,CAAC+C,KAAK,CAAC,CAAC,CACtB,CAAC;IAFgBC,QAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAMC,UAAU,GAAGnD,KAAK,CAAC+C,MAAM,CAC7BhC,OAAO,CAACqC,GAAG,CAAC;IAAA,OAAM,IAAInD,QAAQ,CAAC+C,KAAK,CAAC/B,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EAAA,EACpD,CAAC;EAED,IAAAoC,eAAA,GACErD,KAAK,CAACsD,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IADhBI,0BAA0B,GAAAF,gBAAA;IAAEG,6BAA6B,GAAAH,gBAAA;EAGhE,IAAAI,gBAAA,GAAsC3D,KAAK,CAACsD,QAAQ,CAWlD,IAAI,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAXAE,WAAW,GAAAD,gBAAA;IAAEE,cAAc,GAAAF,gBAAA;EAalC,IAAQG,KAAA,GAAUxC,KAAK,CAACyC,SAAS,CAAzBD,KAAA;EACR,IAAQE,IAAA,GAAS1C,KAAK,CAAd0C,IAAA;EAERjE,KAAK,CAACkE,SAAS,CAAC,YAAM;IACpB,IAAIjD,IAAI,EAAE;MACRyC,6BAA6B,CAAC,KAAK,CAAC;MACpCzD,QAAQ,CAACkE,QAAQ,CAAC,CAChBlE,QAAQ,CAACmE,MAAM,CAACnB,QAAQ,EAAE;QACxBoB,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGP,KAAK;QACrBQ,eAAe,EAAE;MACnB,CAAC,CAAC,EACFtE,QAAQ,CAACuE,OAAO,CACdP,IAAI,GAAG,EAAE,GAAG,EAAE,GAAGF,KAAK,EACtBZ,UAAU,CAACD,OAAO,CACfE,GAAG,CAAE,UAAAY,SAAS;QAAA,OACb/D,QAAQ,CAACmE,MAAM,CAACJ,SAAS,EAAE;UACzBK,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,GAAG,GAAGP,KAAK;UACrBQ,eAAe,EAAE;QACnB,CAAC,CACH;MAAA,EAAC,CACAE,OAAO,CAAC,CACb,CAAC,CACF,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACLzE,QAAQ,CAACkE,QAAQ,EACflE,QAAQ,CAACmE,MAAM,CAACnB,QAAQ,EAAE;QACxBoB,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGP,KAAK;QACrBQ,eAAe,EAAE;MACnB,CAAC,CAAC,EAAAI,MAAA,CAAAC,kBAAA,CACCzB,UAAU,CAACD,OAAO,CAACE,GAAG,CAAE,UAAAY,SAAS;QAAA,OAClC/D,QAAQ,CAACmE,MAAM,CAACJ,SAAS,EAAE;UACzBK,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,GAAG,GAAGP,KAAK;UACrBQ,eAAe,EAAE;QACnB,CAAC,CACH;MAAA,EAAC,EACF,CAAC,CAACG,KAAK,CAAC,UAAAG,KAAA,EAAkB;QAAA,IAAfC,QAAA,GAAUD,KAAA,CAAVC,QAAA;QACV,IAAIA,QAAQ,EAAE;UACZpB,6BAA6B,CAAC,IAAI,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACzC,IAAI,EAAEF,OAAO,EAAEkC,QAAQ,EAAEc,KAAK,EAAEE,IAAI,CAAC,CAAC;EAE1C,IAAMc,KAAK,GAAG,SAARA,KAAKA,CAAA;IAAA,OAASlD,aAAa,CAAC;MAAEZ,IAAI,EAAE;IAAM,CAAC,CAAC;EAAA;EAClD,IAAM+D,MAAM,GAAG,SAATA,MAAMA,CAAA;IAAA,OAASnD,aAAa,CAAC;MAAEZ,IAAI,EAAE,CAACA;IAAK,CAAC,CAAC;EAAA;EAEnD,IAAMgE,WAAW,GAAI,SAAfA,WAAWA,CAAIC,CAAwB,EAAK;IAChDhE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGgE,CAAC,CAAC;IACZ,IAAI,CAAC9D,sBAAsB,IAAIH,IAAI,EAAE;MACnC+D,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EAED,IAAMG,eAAe,GAAI,SAAnBA,eAAeA,CAAID,CAAwB,EAAK;IACpD,IAAI,CAACjE,IAAI,IAAIoB,8BAA8B,EAAE;MAC3ClB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG+D,CAAC,CAAC;MAChB,IAAI9D,sBAAsB,EAAE;QAC1B4D,MAAM,CAAC,CAAC;MACV;IACF;EACF,CAAC;EAED,IAAAI,kBAAA,GACE7E,iBAAiB,CAAC;MAAEgB,KAAK,EAALA,KAAK;MAAEe,mBAAA,EAAAA;IAAoB,CAAC,CAAC;IAD3C+C,UAAU,GAAAD,kBAAA,CAAVC,UAAU;IAAE9C,aAAa,GAAA6C,kBAAA,CAAb7C,aAAa;IAAE+C,yBAAA,GAAAF,kBAAA,CAAAE,yBAAA;EAGnC,IAAMC,eAAe,GAAGtE,IAAI,GACxBgC,QAAQ,CAACuC,WAAW,CAAC;IACnBC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IACvBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC,GACFzC,QAAQ;EAEZ,IAAM0C,SAAS,GAAGxC,UAAU,CAACD,OAAO;EACpC,IAAM0C,MAAM,GAAGD,SAAS,CAACvC,GAAG,CAAE,UAAAyC,OAAO;IAAA,OACnC5E,IAAI,GACA4E,OAAO,CAACL,WAAW,CAAC;MAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC,CAAC,GACF,CACN;EAAA,EAAC;EAED,IAAMI,YAAY,GAAGH,SAAS,CAACvC,GAAG,CAAE,UAAAyC,OAAO;IAAA,OACzC5E,IAAI,GACA4E,OAAO,CAACL,WAAW,CAAC;MAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACtB,CAAC,CAAC,GACF,CAAC,CACP;EAAA,EAAC;EACD,IAAMK,iBAAiB,GAAGJ,SAAS,CAACvC,GAAG,CAAE,UAAAyC,OAAO;IAAA,OAC9C5E,IAAI,GACA4E,OAAO,CAACL,WAAW,CAAC;MAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,CAAC,CAAC,GACF,CAAC,CACP;EAAA,EAAC;EAED,IAAMM,iBAAiB,GAAG;IACxBC,aAAa,EAAEtD,MAAM;IACrBuD,YAAY,EAAEtD,KAAK;IACnBuD,WAAW,EAAEtD,IAAI;IACjBuD,UAAU,EAAE1D;EACd,CAAC;EAED,IAAM2D,0BAAqC,GAAG;IAC5CC,OAAO,EAAE7C,0BAA0B,GAAG,MAAM,GAAG;EACjD,CAAC;EAED,IAAI1C,OAAO,CAACwF,MAAM,MAAK1C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0C,MAAM,GAAE;IAC1CpD,UAAU,CAACD,OAAO,GAAGnC,OAAO,CAACqC,GAAG,CAC9B,UAACoD,CAAC,EAAEC,CAAC;MAAA,OAAKtD,UAAU,CAACD,OAAO,CAACuD,CAAC,CAAC,IAAI,IAAIxG,QAAQ,CAAC+C,KAAK,CAAC/B,IAAI,GAAG,CAAC,GAAG,CAAC,CACpE;IAAA,EAAC;IACD6C,cAAc,CAAC/C,OAAO,CAAC;EACzB;EAEA,OACEf,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IACHuG,aAAa,EAAC,UAAU;IACxBnF,KAAK,EAAE,CAACoF,MAAM,CAACC,SAAS,EAAEb,iBAAiB,EAAExE,KAAK;EAAE,GAEpDxB,KAAA,CAAA0G,aAAA,CAAC/F,iBAAiB;IAChBmG,iBAAiB,EAAC,QAAQ;IAC1B5F,OAAO,EAAE6D,KAAM;IACf4B,aAAa,EAAE1F,IAAI,GAAG,MAAM,GAAG,MAAO;IACtCO,KAAK,EAAE,CACLoF,MAAM,CAAC3D,QAAQ,EACf;MACE4C,OAAO,EAAEN,eAAe;MACxBwB,eAAe,EAAExE;IACnB,CAAC;EACD,CACH,CAAC,EACFvC,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IAACuG,aAAa,EAAC,UAAU;IAACnF,KAAK,EAAEoF,MAAM,CAACI;EAAS,GACpDhH,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IACHuG,aAAa,EAAE1F,IAAI,GAAG,UAAU,GAAG,MAAO;IAC1CO,KAAK,EAAE6E;EAA2B,GAEjCtF,OAAO,CAACqC,GAAG,CAAC,UAAC6D,EAAE,EAAER,CAAC,EAAK;IAAA,IAAAS,kBAAA;IACtB,IAAMC,cAAc,GAAAC,aAAA;MAClBrF,KAAK,GAAAmF,kBAAA,GAAED,EAAE,CAACI,cAAc,YAAAH,kBAAA,GAAI7B;IAAU,GAClCpB,IAAI,GAAG1C,KAAK,CAAC+F,KAAK,CAACC,WAAW,GAAG,CAAC,CAAC,CACxC;IACD,IAAMC,gBAAgB,GACpB,OAAOP,EAAE,CAACQ,IAAI,KAAK,WAAW,IAAIR,EAAE,CAACQ,IAAI,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE;IACjE,IAAMpG,kBAAkB,GACtB,OAAO4F,EAAE,CAAC5F,kBAAkB,KAAK,WAAW,GACxC4F,EAAE,CAAC5F,kBAAkB,GACrB4F,EAAE,CAACtF,KAAK;IACd,IAAM8F,IAAI,GAAG,OAAOR,EAAE,CAACQ,IAAI,KAAK,WAAW,GAAGR,EAAE,CAACQ,IAAI,GAAG,OAAO;IAE/D,IAAMC,iBAAiB,GAAI,SAArBA,iBAAiBA,CAAIxC,CAAwB,EAAK;MACtD+B,EAAE,CAAC/F,OAAO,CAACgE,CAAC,CAAC;MACbH,KAAK,CAAC,CAAC;IACT,CAAC;IAED,OACE/E,KAAA,CAAA0G,aAAA,CAACtG,IAAI;MACHuH,GAAG,EAAElB,CAAE;MACPjF,KAAK,EAAE,CACLoF,MAAM,CAACgB,IAAI,EACX;QACEJ,gBAAA,EAAAA;MACF,CAAC,EACDP,EAAE,CAACY,YAAY,CACf;MACFlB,aAAa,EAAE1F,IAAI,GAAG,UAAU,GAAG,MAAO;MAC1C6F,iBAAiB,EAAC,QAAQ;MAC1BgB,yBAAyB,EAAE7G,IAAI,GAAG,KAAK,GAAG,qBAAsB;MAChE8G,2BAA2B,EAAE,CAAC9G,IAAK;MACnC+G,UAAU,EAAE/G,IAAK;MACjBI,kBAAkB,EAAEA;IAAmB,GAEtC4F,EAAE,CAACtF,KAAK,IACP3B,KAAA,CAAA0G,aAAA,CAACtG,IAAI,QACHJ,KAAA,CAAA0G,aAAA,CAACjG,IAAI;MACHwH,IAAI,EAAEhE,IAAI,GAAG,WAAW,GAAG,UAAW;MACtC/C,OAAO,EAAEwG,iBAAkB;MAC3BQ,iBAAiB,EAAEjB,EAAE,CAACiB,iBAAkB;MACxCJ,yBAAyB,EAAC,qBAAqB;MAC/CC,2BAA2B,EAAE,IAAK;MAClCvG,KAAK,EAAE,CACLoF,MAAM,CAACuB,cAAc,EACrB;QACEC,SAAS,EAAE,CACTnE,IAAI,GACA;UAAEoE,UAAU,EAAEtC,iBAAiB,CAACU,CAAC;QAAE,CAAC,GACpC;UAAE1C,KAAK,EAAE6B,MAAM,CAACa,CAAC;QAAE,CAAC,CACzB;QACDZ,OAAO,EAAEF,SAAS,CAACc,CAAC;MACtB,CAAC,EACDxC,IAAI,IAAI2C,MAAM,CAAC0B,gBAAgB,EAC/BrB,EAAE,CAACkB,cAAc;IACjB,GAEFnI,KAAA,CAAA0G,aAAA,CAAChG,IAAI;MACHyB,OAAO,EAAC,aAAa;MACrB2F,yBAAyB,EAAC,qBAAqB;MAC/CC,2BAA2B,EAAE,IAAK;MAClCvG,KAAK,EAAE,CAAC2F,cAAc,EAAEF,EAAE,CAACsB,UAAU,CAAE;MACvCC,qBAAqB,EAAEvB,EAAE,CAACwB;IAA2B,GAEpDxB,EAAE,CAACtF,KACA,CACF,CACF,CACP,EACD3B,KAAA,CAAA0G,aAAA,CAACpG,GAAG;MACFmH,IAAI,EAAEA,IAAK;MACXzG,IAAI,EAAEiG,EAAE,CAACjG,IAAK;MACde,KAAK,EAAEkF,EAAE,CAAClF,KAAM;MAChBP,KAAK,EAAE,CACL;QACE4G,SAAS,EAAE,CAAC;UAAErE,KAAK,EAAE6B,MAAM,CAACa,CAAC;QAAE,CAAC,CAAC;QACjCZ,OAAO,EAAEF,SAAS,CAACc,CAAC,CAAC;QACrBM,eAAe,EAAEzB;MACnB,CAAC,EACDrB,IAAI,IAAI;QAAEmE,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAEvC,YAAY,CAACW,CAAC;QAAE,CAAC;MAAE,CAAC,EACxDQ,EAAE,CAACzF,KAAK,CACR;MACFuG,2BAA2B,EAAE,IAAK;MAClCxG,KAAK,EAAEA,KAAM;MACbL,OAAO,EAAEwG,iBAAkB;MAC3BI,yBAAyB,EAAC,qBAAqB;MAC/ClG,MAAM,EAAEqF,EAAE,CAACrF,MAAO;MAClBF,OAAO,EAAET,IAAK;MACduB,WAAW,EAAEyE,EAAE,CAACzE;IAAY,CAC7B,CACG,CAAC;EAEX,CAAC,CACG,CAAC,EACPxC,KAAA,CAAA0G,aAAA,CAACpG,GAAG;IACFY,OAAO,EAAE+D,WAAY;IACrB9D,WAAW,EAAEgE,eAAgB;IAC7BlD,cAAc,EAAEA,cAAe;IAC/BjB,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAED,SAAU;IACjBT,kBAAkB,EAAEA,kBAAmB;IACvCyF,iBAAiB,EAAC,QAAQ;IAC1B4B,kBAAkB,EAAE;MAAEC,QAAQ,EAAE1H;IAAK,CAAE;IACvCO,KAAK,EAAE,CAACoF,MAAM,CAACgC,GAAG,EAAEnH,QAAQ,CAAE;IAC9BF,KAAK,EAAEA,KAAM;IACbG,OAAO,EAAEA,OAAQ;IACjBC,KAAK,EAAEA,KAAM;IACbC,MAAM,EAAEA,MAAO;IACfO,OAAO,EAAEA,OAAQ;IACjBK,WAAW,EAAEA;EAAY,CAC1B,CACG,CACF,CAAC;AAEX,CAAC;AAED3B,QAAQ,CAACgI,WAAW,GAAG,WAAW;AAElC,eAAehI,QAAQ;AAGvB,SAASA,QAAQ;AAEjB,IAAM+F,MAAM,GAAGzG,UAAU,CAAC2I,MAAM,CAAC;EAC/B9B,QAAQ,EAAE;IACR+B,UAAU,EAAE;EACd,CAAC;EACDlC,SAAS,EAAAO,aAAA,CAAAA,aAAA,KACJjH,UAAU,CAAC6I,kBAAkB;IAChCC,cAAc,EAAE;EAAA,EACjB;EACDL,GAAG,EAAE;IACHpB,gBAAgB,EAAE,EAAE;IACpB0B,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDlG,QAAQ,EAAAmE,aAAA,KACHjH,UAAU,CAAC6I,kBAAA,CACf;EACDb,cAAc,EAAE;IACdiB,YAAY,EAAE,CAAC;IACfC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,CAAC;IACjB/B,gBAAgB,EAAE,EAAE;IACpBgC,SAAS,EAAE;EACb,CAAC;EACD5B,IAAI,EAAE;IACJsB,YAAY,EAAE,EAAE;IAChBO,aAAa,EAAE,KAAK;IACpBR,cAAc,EAAE,UAAU;IAC1BF,UAAU,EAAE;EACd,CAAC;EAEDT,gBAAgB,EAAE;IAChBvB,eAAe,EAAE,aAAa;IAC9ByC,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}