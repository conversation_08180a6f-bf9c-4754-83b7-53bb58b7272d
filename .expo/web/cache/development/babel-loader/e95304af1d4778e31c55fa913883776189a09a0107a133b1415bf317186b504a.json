{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { initialWindowMetrics, SafeAreaFrameContext, SafeAreaInsetsContext, SafeAreaProvider } from 'react-native-safe-area-context';\nvar _Dimensions$get = Dimensions.get('window'),\n  _Dimensions$get$width = _Dimensions$get.width,\n  width = _Dimensions$get$width === void 0 ? 0 : _Dimensions$get$width,\n  _Dimensions$get$heigh = _Dimensions$get.height,\n  height = _Dimensions$get$heigh === void 0 ? 0 : _Dimensions$get$heigh;\nvar initialMetrics = Platform.OS === 'web' || initialWindowMetrics == null ? {\n  frame: {\n    x: 0,\n    y: 0,\n    width: width,\n    height: height\n  },\n  insets: {\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0\n  }\n} : initialWindowMetrics;\nexport default function SafeAreaProviderCompat(_ref) {\n  var children = _ref.children,\n    style = _ref.style;\n  var insets = React.useContext(SafeAreaInsetsContext);\n  if (insets) {\n    return React.createElement(View, {\n      style: [styles.container, style]\n    }, children);\n  }\n  if (Platform.OS === 'web') {\n    children = React.createElement(SafeAreaFrameProvider, {\n      initialMetrics: initialMetrics\n    }, children);\n  }\n  return React.createElement(SafeAreaProvider, {\n    initialMetrics: initialMetrics,\n    style: style\n  }, children);\n}\nvar SafeAreaFrameProvider = function SafeAreaFrameProvider(_ref2) {\n  var initialMetrics = _ref2.initialMetrics,\n    children = _ref2.children;\n  var element = React.useRef(null);\n  var _React$useState = React.useState(initialMetrics.frame),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    frame = _React$useState2[0],\n    setFrame = _React$useState2[1];\n  React.useEffect(function () {\n    if (element.current == null) {\n      return;\n    }\n    var rect = element.current.getBoundingClientRect();\n    setFrame({\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    });\n    var timeout;\n    var observer = new ResizeObserver(function (entries) {\n      var entry = entries[0];\n      if (entry) {\n        var _entry$contentRect = entry.contentRect,\n          x = _entry$contentRect.x,\n          y = _entry$contentRect.y,\n          _width = _entry$contentRect.width,\n          _height = _entry$contentRect.height;\n        clearTimeout(timeout);\n        timeout = setTimeout(function () {\n          setFrame({\n            x: x,\n            y: y,\n            width: _width,\n            height: _height\n          });\n        }, 100);\n      }\n    });\n    observer.observe(element.current);\n    return function () {\n      observer.disconnect();\n      clearTimeout(timeout);\n    };\n  }, []);\n  return React.createElement(SafeAreaFrameContext.Provider, {\n    value: frame\n  }, React.createElement(\"div\", {\n    ref: element,\n    style: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n      pointerEvents: 'none',\n      visibility: 'hidden'\n    })\n  }), children);\n};\nSafeAreaProviderCompat.initialMetrics = initialMetrics;\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["React", "Dimensions", "Platform", "StyleSheet", "View", "initialWindowMetrics", "SafeAreaFrameContext", "SafeAreaInsetsContext", "SafeAreaProvider", "_Dimensions$get", "get", "_Dimensions$get$width", "width", "_Dimensions$get$heigh", "height", "initialMetrics", "OS", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "_ref", "children", "style", "useContext", "createElement", "styles", "container", "SafeAreaFrameProvider", "_ref2", "element", "useRef", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "set<PERSON>rame", "useEffect", "current", "rect", "getBoundingClientRect", "timeout", "observer", "ResizeObserver", "entries", "entry", "_entry$contentRect", "contentRect", "clearTimeout", "setTimeout", "observe", "disconnect", "Provider", "value", "ref", "_objectSpread", "absoluteFillObject", "pointerEvents", "visibility", "create", "flex"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/elements/src/SafeAreaProviderCompat.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Dimensions,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\nimport {\n  initialWindowMetrics,\n  type Metrics,\n  SafeAreaFrameContext,\n  SafeAreaInsetsContext,\n  SafeAreaProvider,\n} from 'react-native-safe-area-context';\n\ntype Props = {\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n};\n\nconst { width = 0, height = 0 } = Dimensions.get('window');\n\n// To support SSR on web, we need to have empty insets for initial values\n// Otherwise there can be mismatch between SSR and client output\n// We also need to specify empty values to support tests environments\nconst initialMetrics =\n  Platform.OS === 'web' || initialWindowMetrics == null\n    ? {\n        frame: { x: 0, y: 0, width, height },\n        insets: { top: 0, left: 0, right: 0, bottom: 0 },\n      }\n    : initialWindowMetrics;\n\nexport default function SafeAreaProviderCompat({ children, style }: Props) {\n  const insets = React.useContext(SafeAreaInsetsContext);\n\n  if (insets) {\n    // If we already have insets, don't wrap the stack in another safe area provider\n    // This avoids an issue with updates at the cost of potentially incorrect values\n    // https://github.com/react-navigation/react-navigation/issues/174\n    return <View style={[styles.container, style]}>{children}</View>;\n  }\n\n  if (Platform.OS === 'web') {\n    children = (\n      <SafeAreaFrameProvider initialMetrics={initialMetrics}>\n        {children}\n      </SafeAreaFrameProvider>\n    );\n  }\n\n  return (\n    <SafeAreaProvider initialMetrics={initialMetrics} style={style}>\n      {children}\n    </SafeAreaProvider>\n  );\n}\n\n// FIXME: On the Web, the safe area frame value doesn't update on resize\n// So we workaround this by measuring the frame on resize\nconst SafeAreaFrameProvider = ({\n  initialMetrics,\n  children,\n}: {\n  initialMetrics: Metrics;\n  children: React.ReactNode;\n}) => {\n  const element = React.useRef<HTMLDivElement>(null);\n  const [frame, setFrame] = React.useState(initialMetrics.frame);\n\n  React.useEffect(() => {\n    if (element.current == null) {\n      return;\n    }\n\n    const rect = element.current.getBoundingClientRect();\n\n    setFrame({\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height,\n    });\n\n    let timeout: NodeJS.Timeout;\n\n    const observer = new ResizeObserver((entries) => {\n      const entry = entries[0];\n\n      if (entry) {\n        const { x, y, width, height } = entry.contentRect;\n\n        // Debounce the frame updates to avoid too many updates in a short time\n        clearTimeout(timeout);\n        timeout = setTimeout(() => {\n          setFrame({ x, y, width, height });\n        }, 100);\n      }\n    });\n\n    observer.observe(element.current);\n\n    return () => {\n      observer.disconnect();\n      clearTimeout(timeout);\n    };\n  }, []);\n\n  return (\n    <SafeAreaFrameContext.Provider value={frame}>\n      <div\n        ref={element}\n        style={{\n          ...StyleSheet.absoluteFillObject,\n          pointerEvents: 'none',\n          visibility: 'hidden',\n        }}\n      />\n      {children}\n    </SafeAreaFrameContext.Provider>\n  );\n};\n\nSafeAreaProviderCompat.initialMetrics = initialMetrics;\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAS9B,SACEC,oBAAoB,EAEpBC,oBAAoB,EACpBC,qBAAqB,EACrBC,gBAAgB,QACX,gCAAgC;AAOvC,IAAAC,eAAA,GAAkCR,UAAU,CAACS,GAAG,CAAC,QAAQ,CAAC;EAAAC,qBAAA,GAAAF,eAAA,CAAlDG,KAAK;EAALA,KAAK,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;EAAAE,qBAAA,GAAAJ,eAAA,CAAEK,MAAM;EAANA,MAAM,GAAAD,qBAAA,cAAG,IAAAA,qBAAA;AAK5B,IAAME,cAAc,GAClBb,QAAQ,CAACc,EAAE,KAAK,KAAK,IAAIX,oBAAoB,IAAI,IAAI,GACjD;EACEY,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEP,KAAK,EAALA,KAAK;IAAEE,MAAA,EAAAA;EAAO,CAAC;EACpCM,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDnB,oBAAoB;AAE1B,eAAe,SAASoB,sBAAsBA,CAAAC,IAAA,EAA6B;EAAA,IAA1BC,QAAQ,GAAgBD,IAAA,CAAxBC,QAAQ;IAAEC,KAAA,GAAcF,IAAA,CAAdE,KAAA;EACzD,IAAMR,MAAM,GAAGpB,KAAK,CAAC6B,UAAU,CAACtB,qBAAqB,CAAC;EAEtD,IAAIa,MAAM,EAAE;IAIV,OAAOpB,KAAA,CAAA8B,aAAA,CAAC1B,IAAI;MAACwB,KAAK,EAAE,CAACG,MAAM,CAACC,SAAS,EAAEJ,KAAK;IAAE,GAAED,QAAQ,CAAQ;EAClE;EAEA,IAAIzB,QAAQ,CAACc,EAAE,KAAK,KAAK,EAAE;IACzBW,QAAQ,GACN3B,KAAA,CAAA8B,aAAA,CAACG,qBAAqB;MAAClB,cAAc,EAAEA;IAAe,GACnDY,QAAQ,CAEZ;EACH;EAEA,OACE3B,KAAA,CAAA8B,aAAA,CAACtB,gBAAgB;IAACO,cAAc,EAAEA,cAAe;IAACa,KAAK,EAAEA;EAAM,GAC5DD,QAAQ,CACQ;AAEvB;AAIA,IAAMM,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAGC,KAAA,EAMxB;EAAA,IALJnB,cAAc,GAKfmB,KAAA,CALCnB,cAAc;IACdY,QAAA,GAIDO,KAAA,CAJCP,QAAA;EAKA,IAAMQ,OAAO,GAAGnC,KAAK,CAACoC,MAAM,CAAiB,IAAI,CAAC;EAClD,IAAAC,eAAA,GAA0BrC,KAAK,CAACsC,QAAQ,CAACvB,cAAc,CAACE,KAAK,CAAC;IAAAsB,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAvDpB,KAAK,GAAAsB,gBAAA;IAAEE,QAAQ,GAAAF,gBAAA;EAEtBvC,KAAK,CAAC0C,SAAS,CAAC,YAAM;IACpB,IAAIP,OAAO,CAACQ,OAAO,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,IAAMC,IAAI,GAAGT,OAAO,CAACQ,OAAO,CAACE,qBAAqB,EAAE;IAEpDJ,QAAQ,CAAC;MACPvB,CAAC,EAAE0B,IAAI,CAAC1B,CAAC;MACTC,CAAC,EAAEyB,IAAI,CAACzB,CAAC;MACTP,KAAK,EAAEgC,IAAI,CAAChC,KAAK;MACjBE,MAAM,EAAE8B,IAAI,CAAC9B;IACf,CAAC,CAAC;IAEF,IAAIgC,OAAuB;IAE3B,IAAMC,QAAQ,GAAG,IAAIC,cAAc,CAAE,UAAAC,OAAO,EAAK;MAC/C,IAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC;MAExB,IAAIC,KAAK,EAAE;QACT,IAAAC,kBAAA,GAAgCD,KAAK,CAACE,WAAW;UAAzClC,CAAC,GAAAiC,kBAAA,CAADjC,CAAC;UAAEC,CAAC,GAAAgC,kBAAA,CAADhC,CAAC;UAAEP,MAAK,GAAAuC,kBAAA,CAALvC,KAAK;UAAEE,OAAA,GAAAqC,kBAAA,CAAArC,MAAA;QAGrBuC,YAAY,CAACP,OAAO,CAAC;QACrBA,OAAO,GAAGQ,UAAU,CAAC,YAAM;UACzBb,QAAQ,CAAC;YAAEvB,CAAC,EAADA,CAAC;YAAEC,CAAC,EAADA,CAAC;YAAEP,KAAK,EAALA,MAAK;YAAEE,MAAA,EAAAA;UAAO,CAAC,CAAC;QACnC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;IAEFiC,QAAQ,CAACQ,OAAO,CAACpB,OAAO,CAACQ,OAAO,CAAC;IAEjC,OAAO,YAAM;MACXI,QAAQ,CAACS,UAAU,EAAE;MACrBH,YAAY,CAACP,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OACE9C,KAAA,CAAA8B,aAAA,CAACxB,oBAAoB,CAACmD,QAAQ;IAACC,KAAK,EAAEzC;EAAM,GAC1CjB,KAAA,CAAA8B,aAAA;IACE6B,GAAG,EAAExB,OAAQ;IACbP,KAAK,EAAAgC,aAAA,CAAAA,aAAA,KACAzD,UAAU,CAAC0D,kBAAkB;MAChCC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE;IAAA;EACZ,EACF,EACDpC,QAAQ,CACqB;AAEpC,CAAC;AAEDF,sBAAsB,CAACV,cAAc,GAAGA,cAAc;AAEtD,IAAMgB,MAAM,GAAG5B,UAAU,CAAC6D,MAAM,CAAC;EAC/BhC,SAAS,EAAE;IACTiC,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}