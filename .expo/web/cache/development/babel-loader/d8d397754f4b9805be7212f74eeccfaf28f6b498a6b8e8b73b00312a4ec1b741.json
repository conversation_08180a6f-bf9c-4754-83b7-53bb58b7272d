{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Card, Text, TextInput, Button, Chip, SegmentedButtons, IconButton, ActivityIndicator, Divider } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { saveConsumption, addConsumptionItem, getMealTypes, getCurrentUser, getAllNutrients } from \"../services/databaseService\";\nimport DateTimePicker from '@react-native-community/datetimepicker';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ConsumptionEntry = function ConsumptionEntry(_ref) {\n  var _ref$consumption = _ref.consumption,\n    consumption = _ref$consumption === void 0 ? null : _ref$consumption,\n    onSave = _ref.onSave,\n    onCancel = _ref.onCancel,\n    onSelectFood = _ref.onSelectFood,\n    _ref$selectedFoods = _ref.selectedFoods,\n    selectedFoods = _ref$selectedFoods === void 0 ? [] : _ref$selectedFoods,\n    onRemoveFood = _ref.onRemoveFood,\n    onQuantityChange = _ref.onQuantityChange,\n    date = _ref.date;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    mealTypes = _useState2[0],\n    setMealTypes = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    nutrients = _useState4[0],\n    setNutrients = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    userId = _useState6[0],\n    setUserId = _useState6[1];\n  var _useState7 = useState(false),\n    _useState8 = _slicedToArray(_useState7, 2),\n    isLoading = _useState8[0],\n    setIsLoading = _useState8[1];\n  var _useState9 = useState(false),\n    _useState0 = _slicedToArray(_useState9, 2),\n    showDatePicker = _useState0[0],\n    setShowDatePicker = _useState0[1];\n  var _useState1 = useState({\n      id: (consumption == null ? void 0 : consumption.id) || null,\n      user_id: (consumption == null ? void 0 : consumption.user_id) || null,\n      consumption_date: (consumption == null ? void 0 : consumption.consumption_date) || date || new Date().toISOString().split('T')[0],\n      meal_type: (consumption == null ? void 0 : consumption.meal_type) || 'snack',\n      notes: (consumption == null ? void 0 : consumption.notes) || '',\n      items: (consumption == null ? void 0 : consumption.items) || []\n    }),\n    _useState10 = _slicedToArray(_useState1, 2),\n    consumptionData = _useState10[0],\n    setConsumptionData = _useState10[1];\n  useEffect(function () {\n    var loadData = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        try {\n          setIsLoading(true);\n          var types = yield getMealTypes();\n          setMealTypes(types);\n          var nutrientList = yield getAllNutrients();\n          setNutrients(nutrientList);\n          if (!consumptionData.user_id) {\n            var user = yield getCurrentUser();\n            if (user) {\n              setUserId(user.id);\n              setConsumptionData(function (prev) {\n                return _objectSpread(_objectSpread({}, prev), {}, {\n                  user_id: user.id\n                });\n              });\n            }\n          }\n        } catch (error) {\n          console.error('Error loading data:', error);\n          Alert.alert('Error', 'Failed to load data');\n        } finally {\n          setIsLoading(false);\n        }\n      });\n      return function loadData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    loadData();\n  }, []);\n  useEffect(function () {\n    if (selectedFoods.length > 0) {\n      var newItems = selectedFoods.map(function (food) {\n        return {\n          id: `temp-${food.id}-${Date.now()}`,\n          consumption_id: consumptionData.id,\n          food_id: food.id,\n          food_name: food.name,\n          food: food,\n          quantity: food.quantity || 100,\n          unit: food.unit || 'g'\n        };\n      });\n      setConsumptionData(function (prev) {\n        return _objectSpread(_objectSpread({}, prev), {}, {\n          items: newItems\n        });\n      });\n    }\n  }, [selectedFoods]);\n  var handleMealTypeChange = function handleMealTypeChange(value) {\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        meal_type: value\n      });\n    });\n  };\n  var handleNotesChange = function handleNotesChange(text) {\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        notes: text\n      });\n    });\n  };\n  var handleDateChange = function handleDateChange(event, selectedDate) {\n    setShowDatePicker(false);\n    if (selectedDate) {\n      var formattedDate = selectedDate.toISOString().split('T')[0];\n      setConsumptionData(function (prev) {\n        return _objectSpread(_objectSpread({}, prev), {}, {\n          consumption_date: formattedDate\n        });\n      });\n    }\n  };\n  var handleQuantityChange = function handleQuantityChange(itemId, quantity) {\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        items: prev.items.map(function (item) {\n          return item.id === itemId ? _objectSpread(_objectSpread({}, item), {}, {\n            quantity: parseFloat(quantity) || 0\n          }) : item;\n        })\n      });\n    });\n    if (onQuantityChange) {\n      var item = consumptionData.items.find(function (item) {\n        return item.id === itemId;\n      });\n      if (item) {\n        onQuantityChange(item.food_id, parseFloat(quantity) || 0);\n      }\n    }\n  };\n  var handleRemoveItem = function handleRemoveItem(itemId) {\n    var item = consumptionData.items.find(function (item) {\n      return item.id === itemId;\n    });\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        items: prev.items.filter(function (item) {\n          return item.id !== itemId;\n        })\n      });\n    });\n    if (onRemoveFood && item) {\n      onRemoveFood(item.food_id);\n    }\n  };\n  var handleSave = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      try {\n        if (!consumptionData.user_id) {\n          Alert.alert('Error', 'User ID is required');\n          return;\n        }\n        if (consumptionData.items.length === 0) {\n          Alert.alert('Error', 'At least one food item is required');\n          return;\n        }\n        setIsLoading(true);\n        var savedConsumption = yield saveConsumption(consumptionData);\n        for (var item of consumptionData.items) {\n          if (item.id && !item.id.startsWith('temp-')) continue;\n          yield addConsumptionItem(savedConsumption.id, item.food_id, item.quantity, item.unit);\n        }\n        if (onSave) {\n          onSave(savedConsumption);\n        }\n      } catch (error) {\n        console.error('Error saving consumption:', error);\n        Alert.alert('Error', 'Failed to save consumption');\n      } finally {\n        setIsLoading(false);\n      }\n    });\n    return function handleSave() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var calculateNutrition = function calculateNutrition() {\n    var totals = {\n      calories: 0,\n      protein: 0,\n      carbs: 0,\n      fat: 0\n    };\n    consumptionData.items.forEach(function (item) {\n      var food = item.food;\n      if (!food || !food.nutrients) return;\n      var scale = item.quantity / 100;\n      food.nutrients.forEach(function (nutrient) {\n        var name = nutrient.name.toLowerCase();\n        if (name === 'calories' || name === 'energy') {\n          totals.calories += nutrient.amount * scale;\n        } else if (name === 'protein') {\n          totals.protein += nutrient.amount * scale;\n        } else if (name === 'carbohydrates' || name === 'carbs') {\n          totals.carbs += nutrient.amount * scale;\n        } else if (name === 'fat' || name === 'total fat') {\n          totals.fat += nutrient.amount * scale;\n        }\n      });\n    });\n    return {\n      calories: Math.round(totals.calories),\n      protein: Math.round(totals.protein),\n      carbs: Math.round(totals.carbs),\n      fat: Math.round(totals.fat)\n    };\n  };\n  var nutrition = calculateNutrition();\n  var formatDate = function formatDate(dateString) {\n    var options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n  var mealTypeButtons = mealTypes.length > 0 ? mealTypes.map(function (type) {\n    return {\n      value: type.name.toLowerCase(),\n      label: type.name\n    };\n  }) : [{\n    value: 'breakfast',\n    label: 'Breakfast'\n  }, {\n    value: 'lunch',\n    label: 'Lunch'\n  }, {\n    value: 'dinner',\n    label: 'Dinner'\n  }, {\n    value: 'snack',\n    label: 'Snack'\n  }];\n  if (isLoading) {\n    return _jsxs(View, {\n      style: [styles.loadingContainer, {\n        backgroundColor: theme.colors.background\n      }],\n      children: [_jsx(ActivityIndicator, {\n        size: \"large\",\n        color: theme.colors.primary\n      }), _jsx(Text, {\n        style: {\n          marginTop: 16\n        },\n        children: \"Loading...\"\n      })]\n    });\n  }\n  return _jsx(ScrollView, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: _jsxs(Card, {\n      style: styles.card,\n      children: [_jsxs(Card.Content, {\n        children: [_jsx(Button, {\n          mode: \"outlined\",\n          icon: \"calendar\",\n          onPress: function onPress() {\n            return setShowDatePicker(true);\n          },\n          style: styles.dateButton,\n          children: formatDate(consumptionData.consumption_date)\n        }), showDatePicker && _jsx(DateTimePicker, {\n          value: new Date(consumptionData.consumption_date),\n          mode: \"date\",\n          display: \"default\",\n          onChange: handleDateChange\n        }), _jsx(Text, {\n          style: styles.label,\n          children: \"Meal Type\"\n        }), _jsx(SegmentedButtons, {\n          value: consumptionData.meal_type,\n          onValueChange: handleMealTypeChange,\n          buttons: mealTypeButtons,\n          style: styles.mealTypeButtons\n        }), _jsx(Text, {\n          style: styles.label,\n          children: \"Notes\"\n        }), _jsx(TextInput, {\n          mode: \"outlined\",\n          value: consumptionData.notes,\n          onChangeText: handleNotesChange,\n          placeholder: \"Add notes about this meal...\",\n          multiline: true,\n          style: styles.notesInput\n        }), _jsxs(View, {\n          style: styles.nutritionSummary,\n          children: [_jsx(Text, {\n            style: styles.summaryTitle,\n            children: \"Nutrition Summary\"\n          }), _jsxs(View, {\n            style: styles.macrosContainer,\n            children: [_jsxs(Chip, {\n              icon: \"fire\",\n              style: styles.macroChip,\n              children: [nutrition.calories, \" kcal\"]\n            }), _jsxs(Chip, {\n              icon: \"protein\",\n              style: styles.macroChip,\n              children: [nutrition.protein, \"g\"]\n            }), _jsxs(Chip, {\n              icon: \"grain\",\n              style: styles.macroChip,\n              children: [nutrition.carbs, \"g\"]\n            }), _jsxs(Chip, {\n              icon: \"oil\",\n              style: styles.macroChip,\n              children: [nutrition.fat, \"g\"]\n            })]\n          })]\n        }), _jsx(Divider, {\n          style: styles.divider\n        }), _jsxs(View, {\n          style: styles.foodsHeader,\n          children: [_jsx(Text, {\n            style: styles.label,\n            children: \"Food Items\"\n          }), _jsx(Button, {\n            mode: \"text\",\n            icon: \"plus\",\n            onPress: onSelectFood,\n            compact: true,\n            children: \"Add\"\n          })]\n        }), consumptionData.items.length === 0 ? _jsxs(View, {\n          style: styles.emptyContainer,\n          children: [_jsx(Text, {\n            style: styles.emptyText,\n            children: \"No items added yet\"\n          }), _jsx(Button, {\n            mode: \"contained\",\n            icon: \"plus\",\n            onPress: onSelectFood,\n            style: styles.addButton,\n            children: \"Add Food\"\n          })]\n        }) : consumptionData.items.map(function (item) {\n          var _item$food$nutrients$, _item$food$nutrients$2, _item$food$nutrients$3, _item$food$nutrients$4;\n          return _jsxs(View, {\n            style: styles.itemContainer,\n            children: [_jsxs(View, {\n              style: styles.itemHeader,\n              children: [_jsx(Text, {\n                style: styles.itemName,\n                children: item.food_name\n              }), _jsx(IconButton, {\n                icon: \"delete\",\n                size: 20,\n                onPress: function onPress() {\n                  return handleRemoveItem(item.id);\n                }\n              })]\n            }), _jsxs(View, {\n              style: styles.itemDetails,\n              children: [_jsx(TextInput, {\n                mode: \"outlined\",\n                label: \"Quantity\",\n                value: item.quantity.toString(),\n                onChangeText: function onChangeText(text) {\n                  return handleQuantityChange(item.id, text);\n                },\n                keyboardType: \"numeric\",\n                style: styles.quantityInput\n              }), _jsx(Text, {\n                style: styles.unitText,\n                children: item.unit\n              })]\n            }), item.food && item.food.nutrients && _jsxs(View, {\n              style: styles.itemNutrition,\n              children: [_jsxs(Text, {\n                style: styles.nutritionText,\n                children: [Math.round((((_item$food$nutrients$ = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'calories' || n.name.toLowerCase() === 'energy';\n                })) == null ? void 0 : _item$food$nutrients$.amount) || 0) * (item.quantity / 100)), \" kcal\"]\n              }), _jsxs(Text, {\n                style: styles.nutritionText,\n                children: [\"P: \", Math.round((((_item$food$nutrients$2 = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'protein';\n                })) == null ? void 0 : _item$food$nutrients$2.amount) || 0) * (item.quantity / 100)), \"g\"]\n              }), _jsxs(Text, {\n                style: styles.nutritionText,\n                children: [\"C: \", Math.round((((_item$food$nutrients$3 = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'carbohydrates' || n.name.toLowerCase() === 'carbs';\n                })) == null ? void 0 : _item$food$nutrients$3.amount) || 0) * (item.quantity / 100)), \"g\"]\n              }), _jsxs(Text, {\n                style: styles.nutritionText,\n                children: [\"F: \", Math.round((((_item$food$nutrients$4 = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'fat' || n.name.toLowerCase() === 'total fat';\n                })) == null ? void 0 : _item$food$nutrients$4.amount) || 0) * (item.quantity / 100)), \"g\"]\n              })]\n            })]\n          }, item.id);\n        })]\n      }), _jsxs(Card.Actions, {\n        style: styles.actions,\n        children: [_jsx(Button, {\n          onPress: onCancel,\n          disabled: isLoading,\n          children: \"Cancel\"\n        }), _jsx(Button, {\n          mode: \"contained\",\n          onPress: handleSave,\n          loading: isLoading,\n          disabled: isLoading || consumptionData.items.length === 0,\n          children: \"Save\"\n        })]\n      })]\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  card: {\n    margin: 16,\n    elevation: 2\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginTop: 16,\n    marginBottom: 8\n  },\n  mealTypeButtons: {\n    marginBottom: 16\n  },\n  notesInput: {\n    marginBottom: 16\n  },\n  emptyText: {\n    fontStyle: 'italic',\n    marginBottom: 16\n  },\n  itemContainer: {\n    marginBottom: 16,\n    padding: 12,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)'\n  },\n  itemHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center'\n  },\n  itemName: {\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  itemDetails: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginTop: 8\n  },\n  quantityInput: {\n    width: 100\n  },\n  unitText: {\n    marginLeft: 8,\n    fontSize: 16\n  },\n  addButton: {\n    marginVertical: 16\n  },\n  calorieContainer: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    marginTop: 16,\n    padding: 8,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)'\n  },\n  calorieLabel: {\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  actions: {\n    justifyContent: 'space-between',\n    paddingHorizontal: 16,\n    paddingBottom: 8\n  }\n});\nexport default ConsumptionEntry;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "ScrollView", "<PERSON><PERSON>", "Card", "Text", "TextInput", "<PERSON><PERSON>", "Chip", "SegmentedButtons", "IconButton", "ActivityIndicator", "Divider", "useTheme", "saveConsumption", "addConsumptionItem", "getMealTypes", "getCurrentUser", "getAllNutrients", "DateTimePicker", "jsx", "_jsx", "jsxs", "_jsxs", "ConsumptionEntry", "_ref", "_ref$consumption", "consumption", "onSave", "onCancel", "onSelectFood", "_ref$selectedFoods", "selectedFoods", "onRemoveFood", "onQuantityChange", "date", "_useTheme", "theme", "_useState", "_useState2", "_slicedToArray", "mealTypes", "setMealTypes", "_useState3", "_useState4", "nutrients", "setNutrients", "_useState5", "_useState6", "userId", "setUserId", "_useState7", "_useState8", "isLoading", "setIsLoading", "_useState9", "_useState0", "showDatePicker", "setShowDatePicker", "_useState1", "id", "user_id", "consumption_date", "Date", "toISOString", "split", "meal_type", "notes", "items", "_useState10", "consumptionData", "setConsumptionData", "loadData", "_ref2", "_asyncToGenerator", "types", "nutrientList", "user", "prev", "_objectSpread", "error", "console", "alert", "apply", "arguments", "length", "newItems", "map", "food", "now", "consumption_id", "food_id", "food_name", "name", "quantity", "unit", "handleMealTypeChange", "value", "handleNotesChange", "text", "handleDateChange", "event", "selectedDate", "formattedDate", "handleQuantityChange", "itemId", "item", "parseFloat", "find", "handleRemoveItem", "filter", "handleSave", "_ref3", "savedConsumption", "startsWith", "calculateNutrition", "totals", "calories", "protein", "carbs", "fat", "for<PERSON>ach", "scale", "nutrient", "toLowerCase", "amount", "Math", "round", "nutrition", "formatDate", "dateString", "options", "weekday", "year", "month", "day", "toLocaleDateString", "undefined", "mealTypeButtons", "type", "label", "style", "styles", "loadingContainer", "backgroundColor", "colors", "background", "children", "size", "color", "primary", "marginTop", "container", "card", "Content", "mode", "icon", "onPress", "dateButton", "display", "onChange", "onValueChange", "buttons", "onChangeText", "placeholder", "multiline", "notesInput", "nutritionSummary", "summaryTitle", "macrosContainer", "macroChip", "divider", "foodsHeader", "compact", "emptyContainer", "emptyText", "addButton", "_item$food$nutrients$", "_item$food$nutrients$2", "_item$food$nutrients$3", "_item$food$nutrients$4", "itemContainer", "itemHeader", "itemName", "itemDetails", "toString", "keyboardType", "quantityInput", "unitText", "itemNutrition", "nutritionText", "n", "Actions", "actions", "disabled", "loading", "create", "flex", "margin", "elevation", "fontSize", "fontWeight", "marginBottom", "fontStyle", "padding", "borderRadius", "flexDirection", "justifyContent", "alignItems", "width", "marginLeft", "marginVertical", "calorieContainer", "calorieLabel", "paddingHorizontal", "paddingBottom"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/src/components/ConsumptionEntry.js"], "sourcesContent": ["/**\n * Consumption Entry Component for Znü<PERSON>Zähler\n * Allows users to log food consumption\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, ScrollView, Alert } from 'react-native';\nimport { Card, Text, TextInput, Button, Chip, SegmentedButtons, IconButton, ActivityIndicator, Divider } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { saveConsumption, addConsumptionItem, getMealTypes, getCurrentUser, getAllNutrients } from '../services/databaseService';\nimport DateTimePicker from '@react-native-community/datetimepicker';\n\n/**\n * Consumption Entry Component\n * @param {Object} props - Component props\n * @param {Object} props.consumption - Existing consumption data (optional)\n * @param {Function} props.onSave - Callback function when consumption is saved\n * @param {Function} props.onCancel - Callback function to cancel entry\n * @param {Function} props.onSelectFood - Callback function to select a food\n * @param {Array} props.selectedFoods - Array of selected foods\n * @param {Function} props.onRemoveFood - Callback function to remove a food\n * @param {Function} props.onQuantityChange - Callback function to change quantity\n * @param {string} props.date - Date in YYYY-MM-DD format\n * @returns {JSX.Element} - Consumption entry component\n */\nconst ConsumptionEntry = ({\n  consumption = null,\n  onSave,\n  onCancel,\n  onSelectFood,\n  selectedFoods = [],\n  onRemoveFood,\n  onQuantityChange,\n  date\n}) => {\n  const { theme } = useTheme();\n  const [mealTypes, setMealTypes] = useState([]);\n  const [nutrients, setNutrients] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showDatePicker, setShowDatePicker] = useState(false);\n\n  // Initialize consumption data\n  const [consumptionData, setConsumptionData] = useState({\n    id: consumption?.id || null,\n    user_id: consumption?.user_id || null,\n    consumption_date: consumption?.consumption_date || date || new Date().toISOString().split('T')[0],\n    meal_type: consumption?.meal_type || 'snack',\n    notes: consumption?.notes || '',\n    items: consumption?.items || []\n  });\n\n  // Load meal types, nutrients, and user\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n\n        // Load meal types\n        const types = await getMealTypes();\n        setMealTypes(types);\n\n        // Load nutrients for calculations\n        const nutrientList = await getAllNutrients();\n        setNutrients(nutrientList);\n\n        // Load current user if not provided\n        if (!consumptionData.user_id) {\n          const user = await getCurrentUser();\n          if (user) {\n            setUserId(user.id);\n            setConsumptionData(prev => ({\n              ...prev,\n              user_id: user.id\n            }));\n          }\n        }\n      } catch (error) {\n        console.error('Error loading data:', error);\n        Alert.alert('Error', 'Failed to load data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Update items when selectedFoods changes\n  useEffect(() => {\n    if (selectedFoods.length > 0) {\n      const newItems = selectedFoods.map(food => ({\n        id: `temp-${food.id}-${Date.now()}`, // Temporary ID until saved\n        consumption_id: consumptionData.id,\n        food_id: food.id,\n        food_name: food.name,\n        food: food,\n        quantity: food.quantity || 100,\n        unit: food.unit || 'g'\n      }));\n\n      setConsumptionData(prev => ({\n        ...prev,\n        items: newItems\n      }));\n    }\n  }, [selectedFoods]);\n\n  // Handle meal type change\n  const handleMealTypeChange = (value) => {\n    setConsumptionData(prev => ({\n      ...prev,\n      meal_type: value\n    }));\n  };\n\n  // Handle notes change\n  const handleNotesChange = (text) => {\n    setConsumptionData(prev => ({\n      ...prev,\n      notes: text\n    }));\n  };\n\n  // Handle date change\n  const handleDateChange = (event, selectedDate) => {\n    setShowDatePicker(false);\n\n    if (selectedDate) {\n      const formattedDate = selectedDate.toISOString().split('T')[0];\n      setConsumptionData(prev => ({\n        ...prev,\n        consumption_date: formattedDate\n      }));\n    }\n  };\n\n  // Handle quantity change for an item\n  const handleQuantityChange = (itemId, quantity) => {\n    setConsumptionData(prev => ({\n      ...prev,\n      items: prev.items.map(item =>\n        item.id === itemId ? { ...item, quantity: parseFloat(quantity) || 0 } : item\n      )\n    }));\n\n    // Call parent callback if provided\n    if (onQuantityChange) {\n      const item = consumptionData.items.find(item => item.id === itemId);\n      if (item) {\n        onQuantityChange(item.food_id, parseFloat(quantity) || 0);\n      }\n    }\n  };\n\n  // Handle remove item\n  const handleRemoveItem = (itemId) => {\n    const item = consumptionData.items.find(item => item.id === itemId);\n\n    setConsumptionData(prev => ({\n      ...prev,\n      items: prev.items.filter(item => item.id !== itemId)\n    }));\n\n    // Call parent callback if provided\n    if (onRemoveFood && item) {\n      onRemoveFood(item.food_id);\n    }\n  };\n\n  // Handle save\n  const handleSave = async () => {\n    try {\n      if (!consumptionData.user_id) {\n        Alert.alert('Error', 'User ID is required');\n        return;\n      }\n\n      if (consumptionData.items.length === 0) {\n        Alert.alert('Error', 'At least one food item is required');\n        return;\n      }\n\n      setIsLoading(true);\n\n      // Save consumption\n      const savedConsumption = await saveConsumption(consumptionData);\n\n      // Save items\n      for (const item of consumptionData.items) {\n        // Skip items that already have a valid ID (not temporary)\n        if (item.id && !item.id.startsWith('temp-')) continue;\n\n        await addConsumptionItem(\n          savedConsumption.id,\n          item.food_id,\n          item.quantity,\n          item.unit\n        );\n      }\n\n      // Call onSave callback\n      if (onSave) {\n        onSave(savedConsumption);\n      }\n    } catch (error) {\n      console.error('Error saving consumption:', error);\n      Alert.alert('Error', 'Failed to save consumption');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Calculate nutrition totals\n  const calculateNutrition = () => {\n    const totals = {\n      calories: 0,\n      protein: 0,\n      carbs: 0,\n      fat: 0\n    };\n\n    consumptionData.items.forEach(item => {\n      const food = item.food;\n      if (!food || !food.nutrients) return;\n\n      const scale = item.quantity / 100; // Assuming nutrients are per 100g\n\n      food.nutrients.forEach(nutrient => {\n        const name = nutrient.name.toLowerCase();\n        if (name === 'calories' || name === 'energy') {\n          totals.calories += (nutrient.amount * scale);\n        } else if (name === 'protein') {\n          totals.protein += (nutrient.amount * scale);\n        } else if (name === 'carbohydrates' || name === 'carbs') {\n          totals.carbs += (nutrient.amount * scale);\n        } else if (name === 'fat' || name === 'total fat') {\n          totals.fat += (nutrient.amount * scale);\n        }\n      });\n    });\n\n    return {\n      calories: Math.round(totals.calories),\n      protein: Math.round(totals.protein),\n      carbs: Math.round(totals.carbs),\n      fat: Math.round(totals.fat)\n    };\n  };\n\n  const nutrition = calculateNutrition();\n\n  // Format date for display\n  const formatDate = (dateString) => {\n    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n\n  // Generate meal type buttons\n  const mealTypeButtons = mealTypes.length > 0\n    ? mealTypes.map(type => ({\n        value: type.name.toLowerCase(),\n        label: type.name\n      }))\n    : [\n        { value: 'breakfast', label: 'Breakfast' },\n        { value: 'lunch', label: 'Lunch' },\n        { value: 'dinner', label: 'Dinner' },\n        { value: 'snack', label: 'Snack' }\n      ];\n\n  if (isLoading) {\n    return (\n      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>\n        <ActivityIndicator size=\"large\" color={theme.colors.primary} />\n        <Text style={{ marginTop: 16 }}>Loading...</Text>\n      </View>\n    );\n  }\n\n  return (\n    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <Card style={styles.card}>\n        <Card.Content>\n          <Button\n            mode=\"outlined\"\n            icon=\"calendar\"\n            onPress={() => setShowDatePicker(true)}\n            style={styles.dateButton}\n          >\n            {formatDate(consumptionData.consumption_date)}\n          </Button>\n\n          {showDatePicker && (\n            <DateTimePicker\n              value={new Date(consumptionData.consumption_date)}\n              mode=\"date\"\n              display=\"default\"\n              onChange={handleDateChange}\n            />\n          )}\n\n          <Text style={styles.label}>Meal Type</Text>\n          <SegmentedButtons\n            value={consumptionData.meal_type}\n            onValueChange={handleMealTypeChange}\n            buttons={mealTypeButtons}\n            style={styles.mealTypeButtons}\n          />\n\n          <Text style={styles.label}>Notes</Text>\n          <TextInput\n            mode=\"outlined\"\n            value={consumptionData.notes}\n            onChangeText={handleNotesChange}\n            placeholder=\"Add notes about this meal...\"\n            multiline\n            style={styles.notesInput}\n          />\n\n          <View style={styles.nutritionSummary}>\n            <Text style={styles.summaryTitle}>Nutrition Summary</Text>\n            <View style={styles.macrosContainer}>\n              <Chip icon=\"fire\" style={styles.macroChip}>\n                {nutrition.calories} kcal\n              </Chip>\n              <Chip icon=\"protein\" style={styles.macroChip}>\n                {nutrition.protein}g\n              </Chip>\n              <Chip icon=\"grain\" style={styles.macroChip}>\n                {nutrition.carbs}g\n              </Chip>\n              <Chip icon=\"oil\" style={styles.macroChip}>\n                {nutrition.fat}g\n              </Chip>\n            </View>\n          </View>\n\n          <Divider style={styles.divider} />\n\n          <View style={styles.foodsHeader}>\n            <Text style={styles.label}>Food Items</Text>\n            <Button\n              mode=\"text\"\n              icon=\"plus\"\n              onPress={onSelectFood}\n              compact\n            >\n              Add\n            </Button>\n          </View>\n\n          {consumptionData.items.length === 0 ? (\n            <View style={styles.emptyContainer}>\n              <Text style={styles.emptyText}>No items added yet</Text>\n              <Button\n                mode=\"contained\"\n                icon=\"plus\"\n                onPress={onSelectFood}\n                style={styles.addButton}\n              >\n                Add Food\n              </Button>\n            </View>\n          ) : (\n            consumptionData.items.map((item) => (\n              <View key={item.id} style={styles.itemContainer}>\n                <View style={styles.itemHeader}>\n                  <Text style={styles.itemName}>{item.food_name}</Text>\n                  <IconButton\n                    icon=\"delete\"\n                    size={20}\n                    onPress={() => handleRemoveItem(item.id)}\n                  />\n                </View>\n                <View style={styles.itemDetails}>\n                  <TextInput\n                    mode=\"outlined\"\n                    label=\"Quantity\"\n                    value={item.quantity.toString()}\n                    onChangeText={(text) => handleQuantityChange(item.id, text)}\n                    keyboardType=\"numeric\"\n                    style={styles.quantityInput}\n                  />\n                  <Text style={styles.unitText}>{item.unit}</Text>\n                </View>\n                {item.food && item.food.nutrients && (\n                  <View style={styles.itemNutrition}>\n                    <Text style={styles.nutritionText}>\n                      {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'calories' ||\n                        n.name.toLowerCase() === 'energy'\n                      )?.amount || 0) * (item.quantity / 100))} kcal\n                    </Text>\n                    <Text style={styles.nutritionText}>\n                      P: {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'protein'\n                      )?.amount || 0) * (item.quantity / 100))}g\n                    </Text>\n                    <Text style={styles.nutritionText}>\n                      C: {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'carbohydrates' ||\n                        n.name.toLowerCase() === 'carbs'\n                      )?.amount || 0) * (item.quantity / 100))}g\n                    </Text>\n                    <Text style={styles.nutritionText}>\n                      F: {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'fat' ||\n                        n.name.toLowerCase() === 'total fat'\n                      )?.amount || 0) * (item.quantity / 100))}g\n                    </Text>\n                  </View>\n                )}\n              </View>\n            ))\n          )}\n        </Card.Content>\n\n        <Card.Actions style={styles.actions}>\n          <Button\n            onPress={onCancel}\n            disabled={isLoading}\n          >\n            Cancel\n          </Button>\n          <Button\n            mode=\"contained\"\n            onPress={handleSave}\n            loading={isLoading}\n            disabled={isLoading || consumptionData.items.length === 0}\n          >\n            Save\n          </Button>\n        </Card.Actions>\n      </Card>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  card: {\n    margin: 16,\n    elevation: 2,\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginTop: 16,\n    marginBottom: 8,\n  },\n  mealTypeButtons: {\n    marginBottom: 16,\n  },\n  notesInput: {\n    marginBottom: 16,\n  },\n  emptyText: {\n    fontStyle: 'italic',\n    marginBottom: 16,\n  },\n  itemContainer: {\n    marginBottom: 16,\n    padding: 12,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)',\n  },\n  itemHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n  },\n  itemName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n  },\n  itemDetails: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginTop: 8,\n  },\n  quantityInput: {\n    width: 100,\n  },\n  unitText: {\n    marginLeft: 8,\n    fontSize: 16,\n  },\n  addButton: {\n    marginVertical: 16,\n  },\n  calorieContainer: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    marginTop: 16,\n    padding: 8,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)',\n  },\n  calorieLabel: {\n    fontSize: 16,\n    fontWeight: 'bold',\n  },\n  actions: {\n    justifyContent: 'space-between',\n    paddingHorizontal: 16,\n    paddingBottom: 8,\n  },\n});\n\nexport default ConsumptionEntry;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAEnD,SAASC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,OAAO,QAAQ,oBAAoB;AAClI,SAASC,QAAQ;AACjB,SAASC,eAAe,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe;AAC3F,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAepE,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,IAAA,EAShB;EAAA,IAAAC,gBAAA,GAAAD,IAAA,CARJE,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,IAAI,GAAAA,gBAAA;IAClBE,MAAM,GAAAH,IAAA,CAANG,MAAM;IACNC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IACRC,YAAY,GAAAL,IAAA,CAAZK,YAAY;IAAAC,kBAAA,GAAAN,IAAA,CACZO,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,EAAE,GAAAA,kBAAA;IAClBE,YAAY,GAAAR,IAAA,CAAZQ,YAAY;IACZC,gBAAgB,GAAAT,IAAA,CAAhBS,gBAAgB;IAChBC,IAAI,GAAAV,IAAA,CAAJU,IAAI;EAEJ,IAAAC,SAAA,GAAkBvB,QAAQ,CAAC,CAAC;IAApBwB,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,SAAA,GAAkCxC,QAAQ,CAAC,EAAE,CAAC;IAAAyC,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAvCG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAkC7C,QAAQ,CAAC,EAAE,CAAC;IAAA8C,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAvCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4BjD,QAAQ,CAAC,IAAI,CAAC;IAAAkD,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAnCE,MAAM,GAAAD,UAAA;IAAEE,SAAS,GAAAF,UAAA;EACxB,IAAAG,UAAA,GAAkCrD,QAAQ,CAAC,KAAK,CAAC;IAAAsD,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4CzD,QAAQ,CAAC,KAAK,CAAC;IAAA0D,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAApDE,cAAc,GAAAD,UAAA;IAAEE,iBAAiB,GAAAF,UAAA;EAGxC,IAAAG,UAAA,GAA8C7D,QAAQ,CAAC;MACrD8D,EAAE,EAAE,CAAAjC,WAAW,oBAAXA,WAAW,CAAEiC,EAAE,KAAI,IAAI;MAC3BC,OAAO,EAAE,CAAAlC,WAAW,oBAAXA,WAAW,CAAEkC,OAAO,KAAI,IAAI;MACrCC,gBAAgB,EAAE,CAAAnC,WAAW,oBAAXA,WAAW,CAAEmC,gBAAgB,KAAI3B,IAAI,IAAI,IAAI4B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjGC,SAAS,EAAE,CAAAvC,WAAW,oBAAXA,WAAW,CAAEuC,SAAS,KAAI,OAAO;MAC5CC,KAAK,EAAE,CAAAxC,WAAW,oBAAXA,WAAW,CAAEwC,KAAK,KAAI,EAAE;MAC/BC,KAAK,EAAE,CAAAzC,WAAW,oBAAXA,WAAW,CAAEyC,KAAK,KAAI;IAC/B,CAAC,CAAC;IAAAC,WAAA,GAAA7B,cAAA,CAAAmB,UAAA;IAPKW,eAAe,GAAAD,WAAA;IAAEE,kBAAkB,GAAAF,WAAA;EAU1CtE,SAAS,CAAC,YAAM;IACd,IAAMyE,QAAQ;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAC3B,IAAI;UACFpB,YAAY,CAAC,IAAI,CAAC;UAGlB,IAAMqB,KAAK,SAAS3D,YAAY,CAAC,CAAC;UAClC0B,YAAY,CAACiC,KAAK,CAAC;UAGnB,IAAMC,YAAY,SAAS1D,eAAe,CAAC,CAAC;UAC5C4B,YAAY,CAAC8B,YAAY,CAAC;UAG1B,IAAI,CAACN,eAAe,CAACT,OAAO,EAAE;YAC5B,IAAMgB,IAAI,SAAS5D,cAAc,CAAC,CAAC;YACnC,IAAI4D,IAAI,EAAE;cACR3B,SAAS,CAAC2B,IAAI,CAACjB,EAAE,CAAC;cAClBW,kBAAkB,CAAC,UAAAO,IAAI;gBAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;kBACPjB,OAAO,EAAEgB,IAAI,CAACjB;gBAAE;cAAA,CAChB,CAAC;YACL;UACF;QACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC3C7E,KAAK,CAAC+E,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC;QAC7C,CAAC,SAAS;UACR5B,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;MAAA,gBA7BKkB,QAAQA,CAAA;QAAA,OAAAC,KAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;IAAA,GA6Bb;IAEDZ,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAGNzE,SAAS,CAAC,YAAM;IACd,IAAIiC,aAAa,CAACqD,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAMC,QAAQ,GAAGtD,aAAa,CAACuD,GAAG,CAAC,UAAAC,IAAI;QAAA,OAAK;UAC1C5B,EAAE,EAAE,QAAQ4B,IAAI,CAAC5B,EAAE,IAAIG,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE;UACnCC,cAAc,EAAEpB,eAAe,CAACV,EAAE;UAClC+B,OAAO,EAAEH,IAAI,CAAC5B,EAAE;UAChBgC,SAAS,EAAEJ,IAAI,CAACK,IAAI;UACpBL,IAAI,EAAEA,IAAI;UACVM,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,GAAG;UAC9BC,IAAI,EAAEP,IAAI,CAACO,IAAI,IAAI;QACrB,CAAC;MAAA,CAAC,CAAC;MAEHxB,kBAAkB,CAAC,UAAAO,IAAI;QAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;UACPV,KAAK,EAAEkB;QAAQ;MAAA,CACf,CAAC;IACL;EACF,CAAC,EAAE,CAACtD,aAAa,CAAC,CAAC;EAGnB,IAAMgE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,KAAK,EAAK;IACtC1B,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPZ,SAAS,EAAE+B;MAAK;IAAA,CAChB,CAAC;EACL,CAAC;EAGD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,IAAI,EAAK;IAClC5B,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPX,KAAK,EAAEgC;MAAI;IAAA,CACX,CAAC;EACL,CAAC;EAGD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,KAAK,EAAEC,YAAY,EAAK;IAChD5C,iBAAiB,CAAC,KAAK,CAAC;IAExB,IAAI4C,YAAY,EAAE;MAChB,IAAMC,aAAa,GAAGD,YAAY,CAACtC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC9DM,kBAAkB,CAAC,UAAAO,IAAI;QAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;UACPhB,gBAAgB,EAAEyC;QAAa;MAAA,CAC/B,CAAC;IACL;EACF,CAAC;EAGD,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,MAAM,EAAEX,QAAQ,EAAK;IACjDvB,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPV,KAAK,EAAEU,IAAI,CAACV,KAAK,CAACmB,GAAG,CAAC,UAAAmB,IAAI;UAAA,OACxBA,IAAI,CAAC9C,EAAE,KAAK6C,MAAM,GAAA1B,aAAA,CAAAA,aAAA,KAAQ2B,IAAI;YAAEZ,QAAQ,EAAEa,UAAU,CAACb,QAAQ,CAAC,IAAI;UAAC,KAAKY,IAAI;QAAA,CAC9E;MAAC;IAAA,CACD,CAAC;IAGH,IAAIxE,gBAAgB,EAAE;MACpB,IAAMwE,IAAI,GAAGpC,eAAe,CAACF,KAAK,CAACwC,IAAI,CAAC,UAAAF,IAAI;QAAA,OAAIA,IAAI,CAAC9C,EAAE,KAAK6C,MAAM;MAAA,EAAC;MACnE,IAAIC,IAAI,EAAE;QACRxE,gBAAgB,CAACwE,IAAI,CAACf,OAAO,EAAEgB,UAAU,CAACb,QAAQ,CAAC,IAAI,CAAC,CAAC;MAC3D;IACF;EACF,CAAC;EAGD,IAAMe,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIJ,MAAM,EAAK;IACnC,IAAMC,IAAI,GAAGpC,eAAe,CAACF,KAAK,CAACwC,IAAI,CAAC,UAAAF,IAAI;MAAA,OAAIA,IAAI,CAAC9C,EAAE,KAAK6C,MAAM;IAAA,EAAC;IAEnElC,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPV,KAAK,EAAEU,IAAI,CAACV,KAAK,CAAC0C,MAAM,CAAC,UAAAJ,IAAI;UAAA,OAAIA,IAAI,CAAC9C,EAAE,KAAK6C,MAAM;QAAA;MAAC;IAAA,CACpD,CAAC;IAGH,IAAIxE,YAAY,IAAIyE,IAAI,EAAE;MACxBzE,YAAY,CAACyE,IAAI,CAACf,OAAO,CAAC;IAC5B;EACF,CAAC;EAGD,IAAMoB,UAAU;IAAA,IAAAC,KAAA,GAAAtC,iBAAA,CAAG,aAAY;MAC7B,IAAI;QACF,IAAI,CAACJ,eAAe,CAACT,OAAO,EAAE;UAC5B1D,KAAK,CAAC+E,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC;UAC3C;QACF;QAEA,IAAIZ,eAAe,CAACF,KAAK,CAACiB,MAAM,KAAK,CAAC,EAAE;UACtClF,KAAK,CAAC+E,KAAK,CAAC,OAAO,EAAE,oCAAoC,CAAC;UAC1D;QACF;QAEA5B,YAAY,CAAC,IAAI,CAAC;QAGlB,IAAM2D,gBAAgB,SAASnG,eAAe,CAACwD,eAAe,CAAC;QAG/D,KAAK,IAAMoC,IAAI,IAAIpC,eAAe,CAACF,KAAK,EAAE;UAExC,IAAIsC,IAAI,CAAC9C,EAAE,IAAI,CAAC8C,IAAI,CAAC9C,EAAE,CAACsD,UAAU,CAAC,OAAO,CAAC,EAAE;UAE7C,MAAMnG,kBAAkB,CACtBkG,gBAAgB,CAACrD,EAAE,EACnB8C,IAAI,CAACf,OAAO,EACZe,IAAI,CAACZ,QAAQ,EACbY,IAAI,CAACX,IACP,CAAC;QACH;QAGA,IAAInE,MAAM,EAAE;UACVA,MAAM,CAACqF,gBAAgB,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD7E,KAAK,CAAC+E,KAAK,CAAC,OAAO,EAAE,4BAA4B,CAAC;MACpD,CAAC,SAAS;QACR5B,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBAxCKyD,UAAUA,CAAA;MAAA,OAAAC,KAAA,CAAA7B,KAAA,OAAAC,SAAA;IAAA;EAAA,GAwCf;EAGD,IAAM+B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;IAC/B,IAAMC,MAAM,GAAG;MACbC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;IACP,CAAC;IAEDlD,eAAe,CAACF,KAAK,CAACqD,OAAO,CAAC,UAAAf,IAAI,EAAI;MACpC,IAAMlB,IAAI,GAAGkB,IAAI,CAAClB,IAAI;MACtB,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC3C,SAAS,EAAE;MAE9B,IAAM6E,KAAK,GAAGhB,IAAI,CAACZ,QAAQ,GAAG,GAAG;MAEjCN,IAAI,CAAC3C,SAAS,CAAC4E,OAAO,CAAC,UAAAE,QAAQ,EAAI;QACjC,IAAM9B,IAAI,GAAG8B,QAAQ,CAAC9B,IAAI,CAAC+B,WAAW,CAAC,CAAC;QACxC,IAAI/B,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,QAAQ,EAAE;UAC5CuB,MAAM,CAACC,QAAQ,IAAKM,QAAQ,CAACE,MAAM,GAAGH,KAAM;QAC9C,CAAC,MAAM,IAAI7B,IAAI,KAAK,SAAS,EAAE;UAC7BuB,MAAM,CAACE,OAAO,IAAKK,QAAQ,CAACE,MAAM,GAAGH,KAAM;QAC7C,CAAC,MAAM,IAAI7B,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,OAAO,EAAE;UACvDuB,MAAM,CAACG,KAAK,IAAKI,QAAQ,CAACE,MAAM,GAAGH,KAAM;QAC3C,CAAC,MAAM,IAAI7B,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,WAAW,EAAE;UACjDuB,MAAM,CAACI,GAAG,IAAKG,QAAQ,CAACE,MAAM,GAAGH,KAAM;QACzC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACLL,QAAQ,EAAES,IAAI,CAACC,KAAK,CAACX,MAAM,CAACC,QAAQ,CAAC;MACrCC,OAAO,EAAEQ,IAAI,CAACC,KAAK,CAACX,MAAM,CAACE,OAAO,CAAC;MACnCC,KAAK,EAAEO,IAAI,CAACC,KAAK,CAACX,MAAM,CAACG,KAAK,CAAC;MAC/BC,GAAG,EAAEM,IAAI,CAACC,KAAK,CAACX,MAAM,CAACI,GAAG;IAC5B,CAAC;EACH,CAAC;EAED,IAAMQ,SAAS,GAAGb,kBAAkB,CAAC,CAAC;EAGtC,IAAMc,UAAU,GAAG,SAAbA,UAAUA,CAAIC,UAAU,EAAK;IACjC,IAAMC,OAAO,GAAG;MAAEC,OAAO,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAU,CAAC;IACnF,OAAO,IAAIxE,IAAI,CAACmE,UAAU,CAAC,CAACM,kBAAkB,CAACC,SAAS,EAAEN,OAAO,CAAC;EACpE,CAAC;EAGD,IAAMO,eAAe,GAAGjG,SAAS,CAAC4C,MAAM,GAAG,CAAC,GACxC5C,SAAS,CAAC8C,GAAG,CAAC,UAAAoD,IAAI;IAAA,OAAK;MACrB1C,KAAK,EAAE0C,IAAI,CAAC9C,IAAI,CAAC+B,WAAW,CAAC,CAAC;MAC9BgB,KAAK,EAAED,IAAI,CAAC9C;IACd,CAAC;EAAA,CAAC,CAAC,GACH,CACE;IAAEI,KAAK,EAAE,WAAW;IAAE2C,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAE3C,KAAK,EAAE,OAAO;IAAE2C,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAE3C,KAAK,EAAE,QAAQ;IAAE2C,KAAK,EAAE;EAAS,CAAC,EACpC;IAAE3C,KAAK,EAAE,OAAO;IAAE2C,KAAK,EAAE;EAAQ,CAAC,CACnC;EAEL,IAAIvF,SAAS,EAAE;IACb,OACE9B,KAAA,CAACtB,IAAI;MAAC4I,KAAK,EAAE,CAACC,MAAM,CAACC,gBAAgB,EAAE;QAAEC,eAAe,EAAE3G,KAAK,CAAC4G,MAAM,CAACC;MAAW,CAAC,CAAE;MAAAC,QAAA,GACnF9H,IAAA,CAACV,iBAAiB;QAACyI,IAAI,EAAC,OAAO;QAACC,KAAK,EAAEhH,KAAK,CAAC4G,MAAM,CAACK;MAAQ,CAAE,CAAC,EAC/DjI,IAAA,CAAChB,IAAI;QAACwI,KAAK,EAAE;UAAEU,SAAS,EAAE;QAAG,CAAE;QAAAJ,QAAA,EAAC;MAAU,CAAM,CAAC;IAAA,CAC7C,CAAC;EAEX;EAEA,OACE9H,IAAA,CAACnB,UAAU;IAAC2I,KAAK,EAAE,CAACC,MAAM,CAACU,SAAS,EAAE;MAAER,eAAe,EAAE3G,KAAK,CAAC4G,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,EAClF5H,KAAA,CAACnB,IAAI;MAACyI,KAAK,EAAEC,MAAM,CAACW,IAAK;MAAAN,QAAA,GACvB5H,KAAA,CAACnB,IAAI,CAACsJ,OAAO;QAAAP,QAAA,GACX9H,IAAA,CAACd,MAAM;UACLoJ,IAAI,EAAC,UAAU;UACfC,IAAI,EAAC,UAAU;UACfC,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQnG,iBAAiB,CAAC,IAAI,CAAC;UAAA,CAAC;UACvCmF,KAAK,EAAEC,MAAM,CAACgB,UAAW;UAAAX,QAAA,EAExBlB,UAAU,CAAC3D,eAAe,CAACR,gBAAgB;QAAC,CACvC,CAAC,EAERL,cAAc,IACbpC,IAAA,CAACF,cAAc;UACb8E,KAAK,EAAE,IAAIlC,IAAI,CAACO,eAAe,CAACR,gBAAgB,CAAE;UAClD6F,IAAI,EAAC,MAAM;UACXI,OAAO,EAAC,SAAS;UACjBC,QAAQ,EAAE5D;QAAiB,CAC5B,CACF,EAED/E,IAAA,CAAChB,IAAI;UAACwI,KAAK,EAAEC,MAAM,CAACF,KAAM;UAAAO,QAAA,EAAC;QAAS,CAAM,CAAC,EAC3C9H,IAAA,CAACZ,gBAAgB;UACfwF,KAAK,EAAE3B,eAAe,CAACJ,SAAU;UACjC+F,aAAa,EAAEjE,oBAAqB;UACpCkE,OAAO,EAAExB,eAAgB;UACzBG,KAAK,EAAEC,MAAM,CAACJ;QAAgB,CAC/B,CAAC,EAEFrH,IAAA,CAAChB,IAAI;UAACwI,KAAK,EAAEC,MAAM,CAACF,KAAM;UAAAO,QAAA,EAAC;QAAK,CAAM,CAAC,EACvC9H,IAAA,CAACf,SAAS;UACRqJ,IAAI,EAAC,UAAU;UACf1D,KAAK,EAAE3B,eAAe,CAACH,KAAM;UAC7BgG,YAAY,EAAEjE,iBAAkB;UAChCkE,WAAW,EAAC,8BAA8B;UAC1CC,SAAS;UACTxB,KAAK,EAAEC,MAAM,CAACwB;QAAW,CAC1B,CAAC,EAEF/I,KAAA,CAACtB,IAAI;UAAC4I,KAAK,EAAEC,MAAM,CAACyB,gBAAiB;UAAApB,QAAA,GACnC9H,IAAA,CAAChB,IAAI;YAACwI,KAAK,EAAEC,MAAM,CAAC0B,YAAa;YAAArB,QAAA,EAAC;UAAiB,CAAM,CAAC,EAC1D5H,KAAA,CAACtB,IAAI;YAAC4I,KAAK,EAAEC,MAAM,CAAC2B,eAAgB;YAAAtB,QAAA,GAClC5H,KAAA,CAACf,IAAI;cAACoJ,IAAI,EAAC,MAAM;cAACf,KAAK,EAAEC,MAAM,CAAC4B,SAAU;cAAAvB,QAAA,GACvCnB,SAAS,CAACX,QAAQ,EAAC,OACtB;YAAA,CAAM,CAAC,EACP9F,KAAA,CAACf,IAAI;cAACoJ,IAAI,EAAC,SAAS;cAACf,KAAK,EAAEC,MAAM,CAAC4B,SAAU;cAAAvB,QAAA,GAC1CnB,SAAS,CAACV,OAAO,EAAC,GACrB;YAAA,CAAM,CAAC,EACP/F,KAAA,CAACf,IAAI;cAACoJ,IAAI,EAAC,OAAO;cAACf,KAAK,EAAEC,MAAM,CAAC4B,SAAU;cAAAvB,QAAA,GACxCnB,SAAS,CAACT,KAAK,EAAC,GACnB;YAAA,CAAM,CAAC,EACPhG,KAAA,CAACf,IAAI;cAACoJ,IAAI,EAAC,KAAK;cAACf,KAAK,EAAEC,MAAM,CAAC4B,SAAU;cAAAvB,QAAA,GACtCnB,SAAS,CAACR,GAAG,EAAC,GACjB;YAAA,CAAM,CAAC;UAAA,CACH,CAAC;QAAA,CACH,CAAC,EAEPnG,IAAA,CAACT,OAAO;UAACiI,KAAK,EAAEC,MAAM,CAAC6B;QAAQ,CAAE,CAAC,EAElCpJ,KAAA,CAACtB,IAAI;UAAC4I,KAAK,EAAEC,MAAM,CAAC8B,WAAY;UAAAzB,QAAA,GAC9B9H,IAAA,CAAChB,IAAI;YAACwI,KAAK,EAAEC,MAAM,CAACF,KAAM;YAAAO,QAAA,EAAC;UAAU,CAAM,CAAC,EAC5C9H,IAAA,CAACd,MAAM;YACLoJ,IAAI,EAAC,MAAM;YACXC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAE/H,YAAa;YACtB+I,OAAO;YAAA1B,QAAA,EACR;UAED,CAAQ,CAAC;QAAA,CACL,CAAC,EAEN7E,eAAe,CAACF,KAAK,CAACiB,MAAM,KAAK,CAAC,GACjC9D,KAAA,CAACtB,IAAI;UAAC4I,KAAK,EAAEC,MAAM,CAACgC,cAAe;UAAA3B,QAAA,GACjC9H,IAAA,CAAChB,IAAI;YAACwI,KAAK,EAAEC,MAAM,CAACiC,SAAU;YAAA5B,QAAA,EAAC;UAAkB,CAAM,CAAC,EACxD9H,IAAA,CAACd,MAAM;YACLoJ,IAAI,EAAC,WAAW;YAChBC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAE/H,YAAa;YACtB+G,KAAK,EAAEC,MAAM,CAACkC,SAAU;YAAA7B,QAAA,EACzB;UAED,CAAQ,CAAC;QAAA,CACL,CAAC,GAEP7E,eAAe,CAACF,KAAK,CAACmB,GAAG,CAAC,UAACmB,IAAI;UAAA,IAAAuE,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAAA,OAC7B7J,KAAA,CAACtB,IAAI;YAAe4I,KAAK,EAAEC,MAAM,CAACuC,aAAc;YAAAlC,QAAA,GAC9C5H,KAAA,CAACtB,IAAI;cAAC4I,KAAK,EAAEC,MAAM,CAACwC,UAAW;cAAAnC,QAAA,GAC7B9H,IAAA,CAAChB,IAAI;gBAACwI,KAAK,EAAEC,MAAM,CAACyC,QAAS;gBAAApC,QAAA,EAAEzC,IAAI,CAACd;cAAS,CAAO,CAAC,EACrDvE,IAAA,CAACX,UAAU;gBACTkJ,IAAI,EAAC,QAAQ;gBACbR,IAAI,EAAE,EAAG;gBACTS,OAAO,EAAE,SAATA,OAAOA,CAAA;kBAAA,OAAQhD,gBAAgB,CAACH,IAAI,CAAC9C,EAAE,CAAC;gBAAA;cAAC,CAC1C,CAAC;YAAA,CACE,CAAC,EACPrC,KAAA,CAACtB,IAAI;cAAC4I,KAAK,EAAEC,MAAM,CAAC0C,WAAY;cAAArC,QAAA,GAC9B9H,IAAA,CAACf,SAAS;gBACRqJ,IAAI,EAAC,UAAU;gBACff,KAAK,EAAC,UAAU;gBAChB3C,KAAK,EAAES,IAAI,CAACZ,QAAQ,CAAC2F,QAAQ,CAAC,CAAE;gBAChCtB,YAAY,EAAE,SAAdA,YAAYA,CAAGhE,IAAI;kBAAA,OAAKK,oBAAoB,CAACE,IAAI,CAAC9C,EAAE,EAAEuC,IAAI,CAAC;gBAAA,CAAC;gBAC5DuF,YAAY,EAAC,SAAS;gBACtB7C,KAAK,EAAEC,MAAM,CAAC6C;cAAc,CAC7B,CAAC,EACFtK,IAAA,CAAChB,IAAI;gBAACwI,KAAK,EAAEC,MAAM,CAAC8C,QAAS;gBAAAzC,QAAA,EAAEzC,IAAI,CAACX;cAAI,CAAO,CAAC;YAAA,CAC5C,CAAC,EACNW,IAAI,CAAClB,IAAI,IAAIkB,IAAI,CAAClB,IAAI,CAAC3C,SAAS,IAC/BtB,KAAA,CAACtB,IAAI;cAAC4I,KAAK,EAAEC,MAAM,CAAC+C,aAAc;cAAA1C,QAAA,GAChC5H,KAAA,CAAClB,IAAI;gBAACwI,KAAK,EAAEC,MAAM,CAACgD,aAAc;gBAAA3C,QAAA,GAC/BrB,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAkD,qBAAA,GAAAvE,IAAI,CAAClB,IAAI,CAAC3C,SAAS,CAAC+D,IAAI,CAAC,UAAAmF,CAAC;kBAAA,OACrCA,CAAC,CAAClG,IAAI,CAAC+B,WAAW,CAAC,CAAC,KAAK,UAAU,IACnCmE,CAAC,CAAClG,IAAI,CAAC+B,WAAW,CAAC,CAAC,KAAK,QAAQ;gBAAA,CACnC,CAAC,qBAHYqD,qBAAA,CAGVpD,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAACZ,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,OAC3C;cAAA,CAAM,CAAC,EACPvE,KAAA,CAAClB,IAAI;gBAACwI,KAAK,EAAEC,MAAM,CAACgD,aAAc;gBAAA3C,QAAA,GAAC,KAC9B,EAACrB,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAmD,sBAAA,GAAAxE,IAAI,CAAClB,IAAI,CAAC3C,SAAS,CAAC+D,IAAI,CAAC,UAAAmF,CAAC;kBAAA,OACxCA,CAAC,CAAClG,IAAI,CAAC+B,WAAW,CAAC,CAAC,KAAK,SAAS;gBAAA,CACpC,CAAC,qBAFesD,sBAAA,CAEbrD,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAACZ,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,GAC3C;cAAA,CAAM,CAAC,EACPvE,KAAA,CAAClB,IAAI;gBAACwI,KAAK,EAAEC,MAAM,CAACgD,aAAc;gBAAA3C,QAAA,GAAC,KAC9B,EAACrB,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAoD,sBAAA,GAAAzE,IAAI,CAAClB,IAAI,CAAC3C,SAAS,CAAC+D,IAAI,CAAC,UAAAmF,CAAC;kBAAA,OACxCA,CAAC,CAAClG,IAAI,CAAC+B,WAAW,CAAC,CAAC,KAAK,eAAe,IACxCmE,CAAC,CAAClG,IAAI,CAAC+B,WAAW,CAAC,CAAC,KAAK,OAAO;gBAAA,CAClC,CAAC,qBAHeuD,sBAAA,CAGbtD,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAACZ,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,GAC3C;cAAA,CAAM,CAAC,EACPvE,KAAA,CAAClB,IAAI;gBAACwI,KAAK,EAAEC,MAAM,CAACgD,aAAc;gBAAA3C,QAAA,GAAC,KAC9B,EAACrB,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAqD,sBAAA,GAAA1E,IAAI,CAAClB,IAAI,CAAC3C,SAAS,CAAC+D,IAAI,CAAC,UAAAmF,CAAC;kBAAA,OACxCA,CAAC,CAAClG,IAAI,CAAC+B,WAAW,CAAC,CAAC,KAAK,KAAK,IAC9BmE,CAAC,CAAClG,IAAI,CAAC+B,WAAW,CAAC,CAAC,KAAK,WAAW;gBAAA,CACtC,CAAC,qBAHewD,sBAAA,CAGbvD,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAACZ,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,GAC3C;cAAA,CAAM,CAAC;YAAA,CACH,CACP;UAAA,GA9CQY,IAAI,CAAC9C,EA+CV,CAAC;QAAA,CACR,CACF;MAAA,CACW,CAAC,EAEfrC,KAAA,CAACnB,IAAI,CAAC4L,OAAO;QAACnD,KAAK,EAAEC,MAAM,CAACmD,OAAQ;QAAA9C,QAAA,GAClC9H,IAAA,CAACd,MAAM;UACLsJ,OAAO,EAAEhI,QAAS;UAClBqK,QAAQ,EAAE7I,SAAU;UAAA8F,QAAA,EACrB;QAED,CAAQ,CAAC,EACT9H,IAAA,CAACd,MAAM;UACLoJ,IAAI,EAAC,WAAW;UAChBE,OAAO,EAAE9C,UAAW;UACpBoF,OAAO,EAAE9I,SAAU;UACnB6I,QAAQ,EAAE7I,SAAS,IAAIiB,eAAe,CAACF,KAAK,CAACiB,MAAM,KAAK,CAAE;UAAA8D,QAAA,EAC3D;QAED,CAAQ,CAAC;MAAA,CACG,CAAC;IAAA,CACX;EAAC,CACG,CAAC;AAEjB,CAAC;AAED,IAAML,MAAM,GAAG9I,UAAU,CAACoM,MAAM,CAAC;EAC/B5C,SAAS,EAAE;IACT6C,IAAI,EAAE;EACR,CAAC;EACD5C,IAAI,EAAE;IACJ6C,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC;EACD3D,KAAK,EAAE;IACL4D,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBlD,SAAS,EAAE,EAAE;IACbmD,YAAY,EAAE;EAChB,CAAC;EACDhE,eAAe,EAAE;IACfgE,YAAY,EAAE;EAChB,CAAC;EACDpC,UAAU,EAAE;IACVoC,YAAY,EAAE;EAChB,CAAC;EACD3B,SAAS,EAAE;IACT4B,SAAS,EAAE,QAAQ;IACnBD,YAAY,EAAE;EAChB,CAAC;EACDrB,aAAa,EAAE;IACbqB,YAAY,EAAE,EAAE;IAChBE,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,CAAC;IACf7D,eAAe,EAAE;EACnB,CAAC;EACDsC,UAAU,EAAE;IACVwB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACDzB,QAAQ,EAAE;IACRiB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDjB,WAAW,EAAE;IACXsB,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE,QAAQ;IACpBzD,SAAS,EAAE;EACb,CAAC;EACDoC,aAAa,EAAE;IACbsB,KAAK,EAAE;EACT,CAAC;EACDrB,QAAQ,EAAE;IACRsB,UAAU,EAAE,CAAC;IACbV,QAAQ,EAAE;EACZ,CAAC;EACDxB,SAAS,EAAE;IACTmC,cAAc,EAAE;EAClB,CAAC;EACDC,gBAAgB,EAAE;IAChBN,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,eAAe;IAC/BxD,SAAS,EAAE,EAAE;IACbqD,OAAO,EAAE,CAAC;IACVC,YAAY,EAAE,CAAC;IACf7D,eAAe,EAAE;EACnB,CAAC;EACDqE,YAAY,EAAE;IACZb,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDR,OAAO,EAAE;IACPc,cAAc,EAAE,eAAe;IAC/BO,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAEF,eAAe/L,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}