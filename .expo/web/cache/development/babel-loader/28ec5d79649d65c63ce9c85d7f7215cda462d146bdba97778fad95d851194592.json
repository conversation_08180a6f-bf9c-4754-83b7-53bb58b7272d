{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"iconColor\", \"containerColor\", \"rippleColor\", \"size\", \"accessibilityLabel\", \"disabled\", \"onPress\", \"selected\", \"animated\", \"mode\", \"style\", \"theme\", \"testID\", \"loading\", \"contentStyle\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { getIconButtonColor } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nimport ActivityIndicator from \"../ActivityIndicator\";\nimport CrossFadeIcon from \"../CrossFadeIcon\";\nimport Icon from \"../Icon\";\nimport Surface from \"../Surface\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nvar PADDING = 8;\nvar IconButton = forwardRef(function (_ref, ref) {\n  var icon = _ref.icon,\n    customIconColor = _ref.iconColor,\n    customContainerColor = _ref.containerColor,\n    customRippleColor = _ref.rippleColor,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 24 : _ref$size,\n    accessibilityLabel = _ref.accessibilityLabel,\n    disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    _ref$selected = _ref.selected,\n    selected = _ref$selected === void 0 ? false : _ref$selected,\n    _ref$animated = _ref.animated,\n    animated = _ref$animated === void 0 ? false : _ref$animated,\n    mode = _ref.mode,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'icon-button' : _ref$testID,\n    _ref$loading = _ref.loading,\n    loading = _ref$loading === void 0 ? false : _ref$loading,\n    contentStyle = _ref.contentStyle,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3;\n  var IconComponent = animated ? CrossFadeIcon : Icon;\n  var _getIconButtonColor = getIconButtonColor({\n      theme: theme,\n      disabled: disabled,\n      selected: selected,\n      mode: mode,\n      customIconColor: customIconColor,\n      customContainerColor: customContainerColor,\n      customRippleColor: customRippleColor\n    }),\n    iconColor = _getIconButtonColor.iconColor,\n    rippleColor = _getIconButtonColor.rippleColor,\n    backgroundColor = _getIconButtonColor.backgroundColor,\n    borderColor = _getIconButtonColor.borderColor;\n  var buttonSize = isV3 ? size + 2 * PADDING : size * 1.5;\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$borderWidth = _ref2.borderWidth,\n    borderWidth = _ref2$borderWidth === void 0 ? isV3 && mode === 'outlined' && !selected ? 1 : 0 : _ref2$borderWidth,\n    _ref2$borderRadius = _ref2.borderRadius,\n    borderRadius = _ref2$borderRadius === void 0 ? buttonSize / 2 : _ref2$borderRadius;\n  var borderStyles = {\n    borderWidth: borderWidth,\n    borderRadius: borderRadius,\n    borderColor: borderColor\n  };\n  return React.createElement(Surface, _extends({\n    ref: ref,\n    testID: `${testID}-container`,\n    style: [{\n      backgroundColor: backgroundColor,\n      width: buttonSize,\n      height: buttonSize\n    }, styles.container, borderStyles, !isV3 && disabled && styles.disabled, style]\n  }, isV3 && {\n    elevation: 0\n  }), React.createElement(TouchableRipple, _extends({\n    borderless: true,\n    centered: true,\n    onPress: onPress,\n    rippleColor: rippleColor,\n    accessibilityLabel: accessibilityLabel,\n    style: [styles.touchable, contentStyle],\n    accessibilityTraits: disabled ? ['button', 'disabled'] : 'button',\n    accessibilityComponentType: \"button\",\n    accessibilityRole: \"button\",\n    accessibilityState: {\n      disabled: disabled\n    },\n    disabled: disabled,\n    hitSlop: TouchableRipple.supported ? {\n      top: 10,\n      left: 10,\n      bottom: 10,\n      right: 10\n    } : {\n      top: 6,\n      left: 6,\n      bottom: 6,\n      right: 6\n    },\n    testID: testID\n  }, rest), loading ? React.createElement(ActivityIndicator, {\n    size: size,\n    color: iconColor\n  }) : React.createElement(IconComponent, {\n    color: iconColor,\n    source: icon,\n    size: size\n  })));\n});\nvar styles = StyleSheet.create({\n  container: {\n    overflow: 'hidden',\n    margin: 6,\n    elevation: 0\n  },\n  touchable: {\n    flexGrow: 1,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  disabled: {\n    opacity: 0.32\n  }\n});\nexport default IconButton;", "map": {"version": 3, "names": ["React", "StyleSheet", "getIconButtonColor", "useInternalTheme", "forwardRef", "ActivityIndicator", "CrossFadeIcon", "Icon", "Surface", "TouchableRipple", "PADDING", "IconButton", "_ref", "ref", "icon", "customIconColor", "iconColor", "customContainerColor", "containerColor", "customRippleColor", "rippleColor", "_ref$size", "size", "accessibilityLabel", "disabled", "onPress", "_ref$selected", "selected", "_ref$animated", "animated", "mode", "style", "themeOverrides", "theme", "_ref$testID", "testID", "_ref$loading", "loading", "contentStyle", "rest", "_objectWithoutProperties", "_excluded", "isV3", "IconComponent", "_getIconButtonColor", "backgroundColor", "borderColor", "buttonSize", "_ref2", "flatten", "_ref2$borderWidth", "borderWidth", "_ref2$borderRadius", "borderRadius", "borderStyles", "createElement", "_extends", "width", "height", "styles", "container", "elevation", "borderless", "centered", "touchable", "accessibilityTraits", "accessibilityComponentType", "accessibilityRole", "accessibilityState", "hitSlop", "supported", "top", "left", "bottom", "right", "color", "source", "create", "overflow", "margin", "flexGrow", "justifyContent", "alignItems", "opacity"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/IconButton/IconButton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  GestureResponderEvent,\n  StyleProp,\n  StyleSheet,\n  ViewStyle,\n  View,\n  Animated,\n  ColorValue,\n} from 'react-native';\n\nimport { getIconButtonColor } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $RemoveChildren, ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\nimport ActivityIndicator from '../ActivityIndicator';\nimport CrossFadeIcon from '../CrossFadeIcon';\nimport Icon, { IconSource } from '../Icon';\nimport Surface from '../Surface';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\n\nconst PADDING = 8;\n\ntype IconButtonMode = 'outlined' | 'contained' | 'contained-tonal';\n\nexport type Props = Omit<$RemoveChildren<typeof TouchableRipple>, 'style'> & {\n  /**\n   * Icon to display.\n   */\n  icon: IconSource;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Mode of the icon button. By default there is no specified mode - only pressable icon will be rendered.\n   */\n  mode?: IconButtonMode;\n  /**\n   * @renamed Renamed from 'color' to 'iconColor' in v5.x\n   * Color of the icon.\n   */\n  iconColor?: string;\n  /**\n   * Background color of the icon container.\n   */\n  containerColor?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Whether icon button is selected. A selected button receives alternative combination of icon and container colors.\n   */\n  selected?: boolean;\n  /**\n   * Size of the icon.\n   */\n  size?: number;\n  /**\n   * Whether the button is disabled. A disabled button is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Whether an icon change is animated.\n   */\n  animated?: boolean;\n  /**\n   * Accessibility label for the button. This is read by the screen reader when the user taps the button.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Style of button's inner content.\n   * Use this prop to apply custom height and width or to set a custom padding`.\n   */\n  contentStyle?: StyleProp<ViewStyle>;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  ref?: React.RefObject<View>;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Whether to show a loading indicator.\n   */\n  loading?: boolean;\n};\n\n/**\n * An icon button is a button which displays only an icon without a label.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { IconButton, MD3Colors } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <IconButton\n *     icon=\"camera\"\n *     iconColor={MD3Colors.error50}\n *     size={20}\n *     onPress={() => console.log('Pressed')}\n *   />\n * );\n *\n * export default MyComponent;\n * ```\n *\n * @extends TouchableRipple props https://callstack.github.io/react-native-paper/docs/components/TouchableRipple\n */\nconst IconButton = forwardRef<View, Props>(\n  (\n    {\n      icon,\n      iconColor: customIconColor,\n      containerColor: customContainerColor,\n      rippleColor: customRippleColor,\n      size = 24,\n      accessibilityLabel,\n      disabled,\n      onPress,\n      selected = false,\n      animated = false,\n      mode,\n      style,\n      theme: themeOverrides,\n      testID = 'icon-button',\n      loading = false,\n      contentStyle,\n      ...rest\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(themeOverrides);\n    const { isV3 } = theme;\n\n    const IconComponent = animated ? CrossFadeIcon : Icon;\n\n    const { iconColor, rippleColor, backgroundColor, borderColor } =\n      getIconButtonColor({\n        theme,\n        disabled,\n        selected,\n        mode,\n        customIconColor,\n        customContainerColor,\n        customRippleColor,\n      });\n\n    const buttonSize = isV3 ? size + 2 * PADDING : size * 1.5;\n\n    const {\n      borderWidth = isV3 && mode === 'outlined' && !selected ? 1 : 0,\n      borderRadius = buttonSize / 2,\n    } = (StyleSheet.flatten(style) || {}) as ViewStyle;\n\n    const borderStyles = {\n      borderWidth,\n      borderRadius,\n      borderColor,\n    };\n\n    return (\n      <Surface\n        ref={ref}\n        testID={`${testID}-container`}\n        style={[\n          {\n            backgroundColor,\n            width: buttonSize,\n            height: buttonSize,\n          },\n          styles.container,\n          borderStyles,\n          !isV3 && disabled && styles.disabled,\n          style,\n        ]}\n        {...(isV3 && { elevation: 0 })}\n      >\n        <TouchableRipple\n          borderless\n          centered\n          onPress={onPress}\n          rippleColor={rippleColor}\n          accessibilityLabel={accessibilityLabel}\n          style={[styles.touchable, contentStyle]}\n          // @ts-expect-error We keep old a11y props for backwards compat with old RN versions\n          accessibilityTraits={disabled ? ['button', 'disabled'] : 'button'}\n          accessibilityComponentType=\"button\"\n          accessibilityRole=\"button\"\n          accessibilityState={{ disabled }}\n          disabled={disabled}\n          hitSlop={\n            TouchableRipple.supported\n              ? { top: 10, left: 10, bottom: 10, right: 10 }\n              : { top: 6, left: 6, bottom: 6, right: 6 }\n          }\n          testID={testID}\n          {...rest}\n        >\n          {loading ? (\n            <ActivityIndicator size={size} color={iconColor} />\n          ) : (\n            <IconComponent color={iconColor} source={icon} size={size} />\n          )}\n        </TouchableRipple>\n      </Surface>\n    );\n  }\n);\n\nconst styles = StyleSheet.create({\n  container: {\n    overflow: 'hidden',\n    margin: 6,\n    elevation: 0,\n  },\n  touchable: {\n    flexGrow: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  disabled: {\n    opacity: 0.32,\n  },\n});\n\nexport default IconButton;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAW9B,SAASC,kBAAkB;AAC3B,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AACnB,OAAOC,iBAAiB;AACxB,OAAOC,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,eAAe;AAEtB,IAAMC,OAAO,GAAG,CAAC;AA+FjB,IAAMC,UAAU,GAAGP,UAAU,CAC3B,UAAAQ,IAAA,EAoBEC,GAAG,EACA;EAAA,IAnBDC,IAAI,GAiBEF,IAAA,CAjBNE,IAAI;IACOC,eAAe,GAgBpBH,IAAA,CAhBNI,SAAS;IACOC,oBAAoB,GAe9BL,IAAA,CAfNM,cAAc;IACDC,iBAAiB,GAcxBP,IAAA,CAdNQ,WAAW;IAAAC,SAAA,GAcLT,IAAA,CAbNU,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,EAAE,GAAAA,SAAA;IACTE,kBAAkB,GAYZX,IAAA,CAZNW,kBAAkB;IAClBC,QAAQ,GAWFZ,IAAA,CAXNY,QAAQ;IACRC,OAAO,GAUDb,IAAA,CAVNa,OAAO;IAAAC,aAAA,GAUDd,IAAA,CATNe,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,aAAA,GASVhB,IAAA,CARNiB,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAChBE,IAAI,GAOElB,IAAA,CAPNkB,IAAI;IACJC,KAAK,GAMCnB,IAAA,CANNmB,KAAK;IACEC,cAAc,GAKfpB,IAAA,CALNqB,KAAK;IAAAC,WAAA,GAKCtB,IAAA,CAJNuB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,aAAa,GAAAA,WAAA;IAAAE,YAAA,GAIhBxB,IAAA,CAHNyB,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IACfE,YAAY,GAEN1B,IAAA,CAFN0B,YAAY;IACTC,IAAA,GAAAC,wBAAA,CACG5B,IAAA,EAAA6B,SAAA;EAGR,IAAMR,KAAK,GAAG9B,gBAAgB,CAAC6B,cAAc,CAAC;EAC9C,IAAQU,IAAA,GAAST,KAAK,CAAdS,IAAA;EAER,IAAMC,aAAa,GAAGd,QAAQ,GAAGvB,aAAa,GAAGC,IAAI;EAErD,IAAAqC,mBAAA,GACE1C,kBAAkB,CAAC;MACjB+B,KAAK,EAALA,KAAK;MACLT,QAAQ,EAARA,QAAQ;MACRG,QAAQ,EAARA,QAAQ;MACRG,IAAI,EAAJA,IAAI;MACJf,eAAe,EAAfA,eAAe;MACfE,oBAAoB,EAApBA,oBAAoB;MACpBE,iBAAA,EAAAA;IACF,CAAC,CAAC;IATIH,SAAS,GAAA4B,mBAAA,CAAT5B,SAAS;IAAEI,WAAW,GAAAwB,mBAAA,CAAXxB,WAAW;IAAEyB,eAAe,GAAAD,mBAAA,CAAfC,eAAe;IAAEC,WAAA,GAAAF,mBAAA,CAAAE,WAAA;EAWjD,IAAMC,UAAU,GAAGL,IAAI,GAAGpB,IAAI,GAAG,CAAC,GAAGZ,OAAO,GAAGY,IAAI,GAAG,GAAG;EAEzD,IAAA0B,KAAA,GAGK/C,UAAU,CAACgD,OAAO,CAAClB,KAAK,CAAC,IAAI,CAAC,CAAe;IAAAmB,iBAAA,GAAAF,KAAA,CAFhDG,WAAW;IAAXA,WAAW,GAAAD,iBAAA,cAAGR,IAAI,IAAIZ,IAAI,KAAK,UAAU,IAAI,CAACH,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAAuB,iBAAA;IAAAE,kBAAA,GAAAJ,KAAA,CAC9DK,YAAY;IAAZA,YAAY,GAAAD,kBAAA,cAAGL,UAAU,GAAG,IAAAK,kBAAA;EAG9B,IAAME,YAAY,GAAG;IACnBH,WAAW,EAAXA,WAAW;IACXE,YAAY,EAAZA,YAAY;IACZP,WAAA,EAAAA;EACF,CAAC;EAED,OACE9C,KAAA,CAAAuD,aAAA,CAAC/C,OAAO,EAAAgD,QAAA;IACN3C,GAAG,EAAEA,GAAI;IACTsB,MAAM,EAAG,GAAEA,MAAO,YAAY;IAC9BJ,KAAK,EAAE,CACL;MACEc,eAAe,EAAfA,eAAe;MACfY,KAAK,EAAEV,UAAU;MACjBW,MAAM,EAAEX;IACV,CAAC,EACDY,MAAM,CAACC,SAAS,EAChBN,YAAY,EACZ,CAACZ,IAAI,IAAIlB,QAAQ,IAAImC,MAAM,CAACnC,QAAQ,EACpCO,KAAK;EACL,GACGW,IAAI,IAAI;IAAEmB,SAAS,EAAE;EAAE,CAAC,GAE7B7D,KAAA,CAAAuD,aAAA,CAAC9C,eAAe,EAAA+C,QAAA;IACdM,UAAU;IACVC,QAAQ;IACRtC,OAAO,EAAEA,OAAQ;IACjBL,WAAW,EAAEA,WAAY;IACzBG,kBAAkB,EAAEA,kBAAmB;IACvCQ,KAAK,EAAE,CAAC4B,MAAM,CAACK,SAAS,EAAE1B,YAAY;IAEtC2B,mBAAmB,EAAEzC,QAAQ,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,QAAS;IAClE0C,0BAA0B,EAAC,QAAQ;IACnCC,iBAAiB,EAAC,QAAQ;IAC1BC,kBAAkB,EAAE;MAAE5C,QAAA,EAAAA;IAAS,CAAE;IACjCA,QAAQ,EAAEA,QAAS;IACnB6C,OAAO,EACL5D,eAAe,CAAC6D,SAAS,GACrB;MAAEC,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,GAC5C;MAAEH,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAC5C;IACDvC,MAAM,EAAEA;EAAO,GACXI,IAAI,GAEPF,OAAO,GACNrC,KAAA,CAAAuD,aAAA,CAAClD,iBAAiB;IAACiB,IAAI,EAAEA,IAAK;IAACqD,KAAK,EAAE3D;EAAU,CAAE,CAAC,GAEnDhB,KAAA,CAAAuD,aAAA,CAACZ,aAAa;IAACgC,KAAK,EAAE3D,SAAU;IAAC4D,MAAM,EAAE9D,IAAK;IAACQ,IAAI,EAAEA;EAAK,CAAE,CAE/C,CACV,CAAC;AAEd,CACF,CAAC;AAED,IAAMqC,MAAM,GAAG1D,UAAU,CAAC4E,MAAM,CAAC;EAC/BjB,SAAS,EAAE;IACTkB,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,CAAC;IACTlB,SAAS,EAAE;EACb,CAAC;EACDG,SAAS,EAAE;IACTgB,QAAQ,EAAE,CAAC;IACXC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACD1D,QAAQ,EAAE;IACR2D,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAexE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}