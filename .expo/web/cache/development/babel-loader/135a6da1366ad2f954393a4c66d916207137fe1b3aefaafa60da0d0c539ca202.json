{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"theme\", \"style\", \"children\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar CardActions = function CardActions(_ref) {\n  var theme = _ref.theme,\n    style = _ref.style,\n    children = _ref.children,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useInternalTheme = useInternalTheme(theme),\n    isV3 = _useInternalTheme.isV3;\n  var justifyContent = isV3 ? 'flex-end' : 'flex-start';\n  var containerStyle = [styles.container, {\n    justifyContent: justifyContent\n  }, style];\n  return React.createElement(View, _extends({}, rest, {\n    style: containerStyle\n  }), React.Children.map(children, function (child, index) {\n    var _child$props$mode;\n    if (!React.isValidElement(child)) {\n      return child;\n    }\n    var compact = !isV3 && child.props.compact !== false;\n    var mode = (_child$props$mode = child.props.mode) != null ? _child$props$mode : isV3 ? index === 0 ? 'outlined' : 'contained' : undefined;\n    var childStyle = [isV3 && styles.button, child.props.style];\n    return React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n      compact: compact,\n      mode: mode,\n      style: childStyle\n    }));\n  }));\n};\nCardActions.displayName = 'Card.Actions';\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 8\n  },\n  button: {\n    marginLeft: 8\n  }\n});\nexport default CardActions;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "CardActions", "_ref", "theme", "style", "children", "rest", "_objectWithoutProperties", "_excluded", "_useInternalTheme", "isV3", "justifyContent", "containerStyle", "styles", "container", "createElement", "_extends", "Children", "map", "child", "index", "_child$props$mode", "isValidElement", "compact", "props", "mode", "undefined", "childStyle", "button", "cloneElement", "_objectSpread", "displayName", "create", "flexDirection", "alignItems", "padding", "marginLeft"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Card/CardActions.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { useInternalTheme } from '../../core/theming';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Items inside the `CardActions`.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n  theme?: ThemeProp;\n};\n\n/**\n * A component to show a list of actions inside a Card.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Card, Button } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Card>\n *     <Card.Actions>\n *       <Button>Cancel</Button>\n *       <Button>Ok</Button>\n *     </Card.Actions>\n *   </Card>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst CardActions = ({ theme, style, children, ...rest }: Props) => {\n  const { isV3 } = useInternalTheme(theme);\n\n  const justifyContent = (\n    isV3 ? 'flex-end' : 'flex-start'\n  ) as ViewStyle['justifyContent'];\n  const containerStyle = [styles.container, { justifyContent }, style];\n\n  return (\n    <View {...rest} style={containerStyle}>\n      {React.Children.map(children, (child, index) => {\n        if (!React.isValidElement(child)) {\n          return child;\n        }\n\n        const compact = !isV3 && child.props.compact !== false;\n        const mode =\n          child.props.mode ??\n          (isV3 ? (index === 0 ? 'outlined' : 'contained') : undefined);\n        const childStyle = [isV3 && styles.button, child.props.style];\n\n        return React.cloneElement(child, {\n          ...child.props,\n          compact,\n          mode,\n          style: childStyle,\n        });\n      })}\n    </View>\n  );\n};\n\nCardActions.displayName = 'Card.Actions';\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 8,\n  },\n  button: {\n    marginLeft: 8,\n  },\n});\n\nexport default CardActions;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAK9B,SAASC,gBAAgB;AA+BzB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAGC,IAAA,EAAgD;EAAA,IAA7CC,KAAK,GAAmCD,IAAA,CAAxCC,KAAK;IAAEC,KAAK,GAA4BF,IAAA,CAAjCE,KAAK;IAAEC,QAAQ,GAAkBH,IAAA,CAA1BG,QAAQ;IAAKC,IAAA,GAAAC,wBAAA,CAAaL,IAAA,EAAAM,SAAA;EAC7D,IAAAC,iBAAA,GAAiBT,gBAAgB,CAACG,KAAK,CAAC;IAAhCO,IAAA,GAAAD,iBAAA,CAAAC,IAAA;EAER,IAAMC,cAAc,GAClBD,IAAI,GAAG,UAAU,GAAG,YACU;EAChC,IAAME,cAAc,GAAG,CAACC,MAAM,CAACC,SAAS,EAAE;IAAEH,cAAA,EAAAA;EAAe,CAAC,EAAEP,KAAK,CAAC;EAEpE,OACEP,KAAA,CAAAkB,aAAA,CAAChB,IAAI,EAAAiB,QAAA,KAAKV,IAAI;IAAEF,KAAK,EAAEQ;EAAe,IACnCf,KAAK,CAACoB,QAAQ,CAACC,GAAG,CAACb,QAAQ,EAAE,UAACc,KAAK,EAAEC,KAAK,EAAK;IAAA,IAAAC,iBAAA;IAC9C,IAAI,CAACxB,KAAK,CAACyB,cAAc,CAACH,KAAK,CAAC,EAAE;MAChC,OAAOA,KAAK;IACd;IAEA,IAAMI,OAAO,GAAG,CAACb,IAAI,IAAIS,KAAK,CAACK,KAAK,CAACD,OAAO,KAAK,KAAK;IACtD,IAAME,IAAI,IAAAJ,iBAAA,GACRF,KAAK,CAACK,KAAK,CAACC,IAAI,YAAAJ,iBAAA,GACfX,IAAI,GAAIU,KAAK,KAAK,CAAC,GAAG,UAAU,GAAG,WAAW,GAAIM,SAAU;IAC/D,IAAMC,UAAU,GAAG,CAACjB,IAAI,IAAIG,MAAM,CAACe,MAAM,EAAET,KAAK,CAACK,KAAK,CAACpB,KAAK,CAAC;IAE7D,OAAOP,KAAK,CAACgC,YAAY,CAACV,KAAK,EAAAW,aAAA,CAAAA,aAAA,KAC1BX,KAAK,CAACK,KAAK;MACdD,OAAO,EAAPA,OAAO;MACPE,IAAI,EAAJA,IAAI;MACJrB,KAAK,EAAEuB;IAAA,EACR,CAAC;EACJ,CAAC,CACG,CAAC;AAEX,CAAC;AAED1B,WAAW,CAAC8B,WAAW,GAAG,cAAc;AAExC,IAAMlB,MAAM,GAAGf,UAAU,CAACkC,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTmB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDP,MAAM,EAAE;IACNQ,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAenC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}