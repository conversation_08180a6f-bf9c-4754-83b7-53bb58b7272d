{"ast": null, "code": "'use strict';\n\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nvar Info = _createClass(function Info() {\n  _classCallCheck(this, Info);\n  this.any_blank_count = 0;\n  this.any_blank_ms = 0;\n  this.any_blank_speed_sum = 0;\n  this.mostly_blank_count = 0;\n  this.mostly_blank_ms = 0;\n  this.pixels_blank = 0;\n  this.pixels_sampled = 0;\n  this.pixels_scrolled = 0;\n  this.total_time_spent = 0;\n  this.sample_count = 0;\n});\nvar DEBUG = false;\nvar _listeners = [];\nvar _minSampleCount = 10;\nvar _sampleRate = DEBUG ? 1 : null;\nvar FillRateHelper = function () {\n  function FillRateHelper(getFrameMetrics) {\n    _classCallCheck(this, FillRateHelper);\n    this._anyBlankStartTime = null;\n    this._enabled = false;\n    this._info = new Info();\n    this._mostlyBlankStartTime = null;\n    this._samplesStartTime = null;\n    this._getFrameMetrics = getFrameMetrics;\n    this._enabled = (_sampleRate || 0) > Math.random();\n    this._resetData();\n  }\n  return _createClass(FillRateHelper, [{\n    key: \"activate\",\n    value: function activate() {\n      if (this._enabled && this._samplesStartTime == null) {\n        DEBUG && console.debug('FillRateHelper: activate');\n        this._samplesStartTime = global.performance.now();\n      }\n    }\n  }, {\n    key: \"deactivateAndFlush\",\n    value: function deactivateAndFlush() {\n      if (!this._enabled) {\n        return;\n      }\n      var start = this._samplesStartTime;\n      if (start == null) {\n        DEBUG && console.debug('FillRateHelper: bail on deactivate with no start time');\n        return;\n      }\n      if (this._info.sample_count < _minSampleCount) {\n        this._resetData();\n        return;\n      }\n      var total_time_spent = global.performance.now() - start;\n      var info = _objectSpread(_objectSpread({}, this._info), {}, {\n        total_time_spent: total_time_spent\n      });\n      if (DEBUG) {\n        var derived = {\n          avg_blankness: this._info.pixels_blank / this._info.pixels_sampled,\n          avg_speed: this._info.pixels_scrolled / (total_time_spent / 1000),\n          avg_speed_when_any_blank: this._info.any_blank_speed_sum / this._info.any_blank_count,\n          any_blank_per_min: this._info.any_blank_count / (total_time_spent / 1000 / 60),\n          any_blank_time_frac: this._info.any_blank_ms / total_time_spent,\n          mostly_blank_per_min: this._info.mostly_blank_count / (total_time_spent / 1000 / 60),\n          mostly_blank_time_frac: this._info.mostly_blank_ms / total_time_spent\n        };\n        for (var key in derived) {\n          derived[key] = Math.round(1000 * derived[key]) / 1000;\n        }\n        console.debug('FillRateHelper deactivateAndFlush: ', {\n          derived: derived,\n          info: info\n        });\n      }\n      _listeners.forEach(function (listener) {\n        return listener(info);\n      });\n      this._resetData();\n    }\n  }, {\n    key: \"computeBlankness\",\n    value: function computeBlankness(props, cellsAroundViewport, scrollMetrics) {\n      if (!this._enabled || props.getItemCount(props.data) === 0 || cellsAroundViewport.last < cellsAroundViewport.first || this._samplesStartTime == null) {\n        return 0;\n      }\n      var dOffset = scrollMetrics.dOffset,\n        offset = scrollMetrics.offset,\n        velocity = scrollMetrics.velocity,\n        visibleLength = scrollMetrics.visibleLength;\n      this._info.sample_count++;\n      this._info.pixels_sampled += Math.round(visibleLength);\n      this._info.pixels_scrolled += Math.round(Math.abs(dOffset));\n      var scrollSpeed = Math.round(Math.abs(velocity) * 1000);\n      var now = global.performance.now();\n      if (this._anyBlankStartTime != null) {\n        this._info.any_blank_ms += now - this._anyBlankStartTime;\n      }\n      this._anyBlankStartTime = null;\n      if (this._mostlyBlankStartTime != null) {\n        this._info.mostly_blank_ms += now - this._mostlyBlankStartTime;\n      }\n      this._mostlyBlankStartTime = null;\n      var blankTop = 0;\n      var first = cellsAroundViewport.first;\n      var firstFrame = this._getFrameMetrics(first, props);\n      while (first <= cellsAroundViewport.last && (!firstFrame || !firstFrame.inLayout)) {\n        firstFrame = this._getFrameMetrics(first, props);\n        first++;\n      }\n      if (firstFrame && first > 0) {\n        blankTop = Math.min(visibleLength, Math.max(0, firstFrame.offset - offset));\n      }\n      var blankBottom = 0;\n      var last = cellsAroundViewport.last;\n      var lastFrame = this._getFrameMetrics(last, props);\n      while (last >= cellsAroundViewport.first && (!lastFrame || !lastFrame.inLayout)) {\n        lastFrame = this._getFrameMetrics(last, props);\n        last--;\n      }\n      if (lastFrame && last < props.getItemCount(props.data) - 1) {\n        var bottomEdge = lastFrame.offset + lastFrame.length;\n        blankBottom = Math.min(visibleLength, Math.max(0, offset + visibleLength - bottomEdge));\n      }\n      var pixels_blank = Math.round(blankTop + blankBottom);\n      var blankness = pixels_blank / visibleLength;\n      if (blankness > 0) {\n        this._anyBlankStartTime = now;\n        this._info.any_blank_speed_sum += scrollSpeed;\n        this._info.any_blank_count++;\n        this._info.pixels_blank += pixels_blank;\n        if (blankness > 0.5) {\n          this._mostlyBlankStartTime = now;\n          this._info.mostly_blank_count++;\n        }\n      } else if (scrollSpeed < 0.01 || Math.abs(dOffset) < 1) {\n        this.deactivateAndFlush();\n      }\n      return blankness;\n    }\n  }, {\n    key: \"enabled\",\n    value: function enabled() {\n      return this._enabled;\n    }\n  }, {\n    key: \"_resetData\",\n    value: function _resetData() {\n      this._anyBlankStartTime = null;\n      this._info = new Info();\n      this._mostlyBlankStartTime = null;\n      this._samplesStartTime = null;\n    }\n  }], [{\n    key: \"addListener\",\n    value: function addListener(callback) {\n      if (_sampleRate === null) {\n        console.warn('Call `FillRateHelper.setSampleRate` before `addListener`.');\n      }\n      _listeners.push(callback);\n      return {\n        remove: function remove() {\n          _listeners = _listeners.filter(function (listener) {\n            return callback !== listener;\n          });\n        }\n      };\n    }\n  }, {\n    key: \"setSampleRate\",\n    value: function setSampleRate(sampleRate) {\n      _sampleRate = sampleRate;\n    }\n  }, {\n    key: \"setMinSampleCount\",\n    value: function setMinSampleCount(minSampleCount) {\n      _minSampleCount = minSampleCount;\n    }\n  }]);\n}();\nexport default FillRateHelper;", "map": {"version": 3, "names": ["_createClass", "_classCallCheck", "_objectSpread", "Info", "any_blank_count", "any_blank_ms", "any_blank_speed_sum", "mostly_blank_count", "mostly_blank_ms", "pixels_blank", "pixels_sampled", "pixels_scrolled", "total_time_spent", "sample_count", "DEBUG", "_listeners", "_minSampleCount", "_sampleRate", "FillRateHelper", "getFrameMetrics", "_anyBlankStartTime", "_enabled", "_info", "_mostlyBlankStartTime", "_samplesStartTime", "_getFrameMetrics", "Math", "random", "_resetData", "key", "value", "activate", "console", "debug", "global", "performance", "now", "deactivateAndFlush", "start", "info", "derived", "avg_blankness", "avg_speed", "avg_speed_when_any_blank", "any_blank_per_min", "any_blank_time_frac", "mostly_blank_per_min", "mostly_blank_time_frac", "round", "for<PERSON>ach", "listener", "computeBlankness", "props", "cellsAroundViewport", "scrollMetrics", "getItemCount", "data", "last", "first", "dOffset", "offset", "velocity", "<PERSON><PERSON><PERSON><PERSON>", "abs", "scrollSpeed", "blankTop", "firstFrame", "inLayout", "min", "max", "blankBottom", "<PERSON><PERSON><PERSON><PERSON>", "bottomEdge", "length", "blankness", "enabled", "addListener", "callback", "warn", "push", "remove", "filter", "setSampleRate", "sampleRate", "setMinSampleCount", "minSampleCount"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/FillRateHelper/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nclass Info {\n  constructor() {\n    this.any_blank_count = 0;\n    this.any_blank_ms = 0;\n    this.any_blank_speed_sum = 0;\n    this.mostly_blank_count = 0;\n    this.mostly_blank_ms = 0;\n    this.pixels_blank = 0;\n    this.pixels_sampled = 0;\n    this.pixels_scrolled = 0;\n    this.total_time_spent = 0;\n    this.sample_count = 0;\n  }\n}\nvar DEBUG = false;\nvar _listeners = [];\nvar _minSampleCount = 10;\nvar _sampleRate = DEBUG ? 1 : null;\n\n/**\n * A helper class for detecting when the maximem fill rate of `VirtualizedList` is exceeded.\n * By default the sampling rate is set to zero and this will do nothing. If you want to collect\n * samples (e.g. to log them), make sure to call `FillRateHelper.setSampleRate(0.0-1.0)`.\n *\n * Listeners and sample rate are global for all `VirtualizedList`s - typical usage will combine with\n * `SceneTracker.getActiveScene` to determine the context of the events.\n */\nclass FillRateHelper {\n  static addListener(callback) {\n    if (_sampleRate === null) {\n      console.warn('Call `FillRateHelper.setSampleRate` before `addListener`.');\n    }\n    _listeners.push(callback);\n    return {\n      remove: () => {\n        _listeners = _listeners.filter(listener => callback !== listener);\n      }\n    };\n  }\n  static setSampleRate(sampleRate) {\n    _sampleRate = sampleRate;\n  }\n  static setMinSampleCount(minSampleCount) {\n    _minSampleCount = minSampleCount;\n  }\n  constructor(getFrameMetrics) {\n    this._anyBlankStartTime = null;\n    this._enabled = false;\n    this._info = new Info();\n    this._mostlyBlankStartTime = null;\n    this._samplesStartTime = null;\n    this._getFrameMetrics = getFrameMetrics;\n    this._enabled = (_sampleRate || 0) > Math.random();\n    this._resetData();\n  }\n  activate() {\n    if (this._enabled && this._samplesStartTime == null) {\n      DEBUG && console.debug('FillRateHelper: activate');\n      this._samplesStartTime = global.performance.now();\n    }\n  }\n  deactivateAndFlush() {\n    if (!this._enabled) {\n      return;\n    }\n    var start = this._samplesStartTime; // const for flow\n    if (start == null) {\n      DEBUG && console.debug('FillRateHelper: bail on deactivate with no start time');\n      return;\n    }\n    if (this._info.sample_count < _minSampleCount) {\n      // Don't bother with under-sampled events.\n      this._resetData();\n      return;\n    }\n    var total_time_spent = global.performance.now() - start;\n    var info = _objectSpread(_objectSpread({}, this._info), {}, {\n      total_time_spent\n    });\n    if (DEBUG) {\n      var derived = {\n        avg_blankness: this._info.pixels_blank / this._info.pixels_sampled,\n        avg_speed: this._info.pixels_scrolled / (total_time_spent / 1000),\n        avg_speed_when_any_blank: this._info.any_blank_speed_sum / this._info.any_blank_count,\n        any_blank_per_min: this._info.any_blank_count / (total_time_spent / 1000 / 60),\n        any_blank_time_frac: this._info.any_blank_ms / total_time_spent,\n        mostly_blank_per_min: this._info.mostly_blank_count / (total_time_spent / 1000 / 60),\n        mostly_blank_time_frac: this._info.mostly_blank_ms / total_time_spent\n      };\n      for (var key in derived) {\n        // $FlowFixMe[prop-missing]\n        derived[key] = Math.round(1000 * derived[key]) / 1000;\n      }\n      console.debug('FillRateHelper deactivateAndFlush: ', {\n        derived,\n        info\n      });\n    }\n    _listeners.forEach(listener => listener(info));\n    this._resetData();\n  }\n  computeBlankness(props, cellsAroundViewport, scrollMetrics) {\n    if (!this._enabled || props.getItemCount(props.data) === 0 || cellsAroundViewport.last < cellsAroundViewport.first || this._samplesStartTime == null) {\n      return 0;\n    }\n    var dOffset = scrollMetrics.dOffset,\n      offset = scrollMetrics.offset,\n      velocity = scrollMetrics.velocity,\n      visibleLength = scrollMetrics.visibleLength;\n\n    // Denominator metrics that we track for all events - most of the time there is no blankness and\n    // we want to capture that.\n    this._info.sample_count++;\n    this._info.pixels_sampled += Math.round(visibleLength);\n    this._info.pixels_scrolled += Math.round(Math.abs(dOffset));\n    var scrollSpeed = Math.round(Math.abs(velocity) * 1000); // px / sec\n\n    // Whether blank now or not, record the elapsed time blank if we were blank last time.\n    var now = global.performance.now();\n    if (this._anyBlankStartTime != null) {\n      this._info.any_blank_ms += now - this._anyBlankStartTime;\n    }\n    this._anyBlankStartTime = null;\n    if (this._mostlyBlankStartTime != null) {\n      this._info.mostly_blank_ms += now - this._mostlyBlankStartTime;\n    }\n    this._mostlyBlankStartTime = null;\n    var blankTop = 0;\n    var first = cellsAroundViewport.first;\n    var firstFrame = this._getFrameMetrics(first, props);\n    while (first <= cellsAroundViewport.last && (!firstFrame || !firstFrame.inLayout)) {\n      firstFrame = this._getFrameMetrics(first, props);\n      first++;\n    }\n    // Only count blankTop if we aren't rendering the first item, otherwise we will count the header\n    // as blank.\n    if (firstFrame && first > 0) {\n      blankTop = Math.min(visibleLength, Math.max(0, firstFrame.offset - offset));\n    }\n    var blankBottom = 0;\n    var last = cellsAroundViewport.last;\n    var lastFrame = this._getFrameMetrics(last, props);\n    while (last >= cellsAroundViewport.first && (!lastFrame || !lastFrame.inLayout)) {\n      lastFrame = this._getFrameMetrics(last, props);\n      last--;\n    }\n    // Only count blankBottom if we aren't rendering the last item, otherwise we will count the\n    // footer as blank.\n    if (lastFrame && last < props.getItemCount(props.data) - 1) {\n      var bottomEdge = lastFrame.offset + lastFrame.length;\n      blankBottom = Math.min(visibleLength, Math.max(0, offset + visibleLength - bottomEdge));\n    }\n    var pixels_blank = Math.round(blankTop + blankBottom);\n    var blankness = pixels_blank / visibleLength;\n    if (blankness > 0) {\n      this._anyBlankStartTime = now;\n      this._info.any_blank_speed_sum += scrollSpeed;\n      this._info.any_blank_count++;\n      this._info.pixels_blank += pixels_blank;\n      if (blankness > 0.5) {\n        this._mostlyBlankStartTime = now;\n        this._info.mostly_blank_count++;\n      }\n    } else if (scrollSpeed < 0.01 || Math.abs(dOffset) < 1) {\n      this.deactivateAndFlush();\n    }\n    return blankness;\n  }\n  enabled() {\n    return this._enabled;\n  }\n  _resetData() {\n    this._anyBlankStartTime = null;\n    this._info = new Info();\n    this._mostlyBlankStartTime = null;\n    this._samplesStartTime = null;\n  }\n}\nexport default FillRateHelper;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,YAAA;AAAA,OAAAC,eAAA;AAEb,OAAOC,aAAa,MAAM,sCAAsC;AAAC,IAC3DC,IAAI,GAAAH,YAAA,CACR,SAAAG,KAAA,EAAc;EAAAF,eAAA,OAAAE,IAAA;EACZ,IAAI,CAACC,eAAe,GAAG,CAAC;EACxB,IAAI,CAACC,YAAY,GAAG,CAAC;EACrB,IAAI,CAACC,mBAAmB,GAAG,CAAC;EAC5B,IAAI,CAACC,kBAAkB,GAAG,CAAC;EAC3B,IAAI,CAACC,eAAe,GAAG,CAAC;EACxB,IAAI,CAACC,YAAY,GAAG,CAAC;EACrB,IAAI,CAACC,cAAc,GAAG,CAAC;EACvB,IAAI,CAACC,eAAe,GAAG,CAAC;EACxB,IAAI,CAACC,gBAAgB,GAAG,CAAC;EACzB,IAAI,CAACC,YAAY,GAAG,CAAC;AACvB,CAAC;AAEH,IAAIC,KAAK,GAAG,KAAK;AACjB,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,eAAe,GAAG,EAAE;AACxB,IAAIC,WAAW,GAAGH,KAAK,GAAG,CAAC,GAAG,IAAI;AAAC,IAU7BI,cAAc;EAkBlB,SAAAA,eAAYC,eAAe,EAAE;IAAAlB,eAAA,OAAAiB,cAAA;IAC3B,IAAI,CAACE,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,KAAK,GAAG,IAAInB,IAAI,CAAC,CAAC;IACvB,IAAI,CAACoB,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,gBAAgB,GAAGN,eAAe;IACvC,IAAI,CAACE,QAAQ,GAAG,CAACJ,WAAW,IAAI,CAAC,IAAIS,IAAI,CAACC,MAAM,CAAC,CAAC;IAClD,IAAI,CAACC,UAAU,CAAC,CAAC;EACnB;EAAC,OAAA5B,YAAA,CAAAkB,cAAA;IAAAW,GAAA;IAAAC,KAAA,EACD,SAAAC,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACV,QAAQ,IAAI,IAAI,CAACG,iBAAiB,IAAI,IAAI,EAAE;QACnDV,KAAK,IAAIkB,OAAO,CAACC,KAAK,CAAC,0BAA0B,CAAC;QAClD,IAAI,CAACT,iBAAiB,GAAGU,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC;MACnD;IACF;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAAO,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAC,IAAI,CAAChB,QAAQ,EAAE;QAClB;MACF;MACA,IAAIiB,KAAK,GAAG,IAAI,CAACd,iBAAiB;MAClC,IAAIc,KAAK,IAAI,IAAI,EAAE;QACjBxB,KAAK,IAAIkB,OAAO,CAACC,KAAK,CAAC,uDAAuD,CAAC;QAC/E;MACF;MACA,IAAI,IAAI,CAACX,KAAK,CAACT,YAAY,GAAGG,eAAe,EAAE;QAE7C,IAAI,CAACY,UAAU,CAAC,CAAC;QACjB;MACF;MACA,IAAIhB,gBAAgB,GAAGsB,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGE,KAAK;MACvD,IAAIC,IAAI,GAAGrC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACoB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1DV,gBAAgB,EAAhBA;MACF,CAAC,CAAC;MACF,IAAIE,KAAK,EAAE;QACT,IAAI0B,OAAO,GAAG;UACZC,aAAa,EAAE,IAAI,CAACnB,KAAK,CAACb,YAAY,GAAG,IAAI,CAACa,KAAK,CAACZ,cAAc;UAClEgC,SAAS,EAAE,IAAI,CAACpB,KAAK,CAACX,eAAe,IAAIC,gBAAgB,GAAG,IAAI,CAAC;UACjE+B,wBAAwB,EAAE,IAAI,CAACrB,KAAK,CAAChB,mBAAmB,GAAG,IAAI,CAACgB,KAAK,CAAClB,eAAe;UACrFwC,iBAAiB,EAAE,IAAI,CAACtB,KAAK,CAAClB,eAAe,IAAIQ,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;UAC9EiC,mBAAmB,EAAE,IAAI,CAACvB,KAAK,CAACjB,YAAY,GAAGO,gBAAgB;UAC/DkC,oBAAoB,EAAE,IAAI,CAACxB,KAAK,CAACf,kBAAkB,IAAIK,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;UACpFmC,sBAAsB,EAAE,IAAI,CAACzB,KAAK,CAACd,eAAe,GAAGI;QACvD,CAAC;QACD,KAAK,IAAIiB,GAAG,IAAIW,OAAO,EAAE;UAEvBA,OAAO,CAACX,GAAG,CAAC,GAAGH,IAAI,CAACsB,KAAK,CAAC,IAAI,GAAGR,OAAO,CAACX,GAAG,CAAC,CAAC,GAAG,IAAI;QACvD;QACAG,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAE;UACnDO,OAAO,EAAPA,OAAO;UACPD,IAAI,EAAJA;QACF,CAAC,CAAC;MACJ;MACAxB,UAAU,CAACkC,OAAO,CAAC,UAAAC,QAAQ;QAAA,OAAIA,QAAQ,CAACX,IAAI,CAAC;MAAA,EAAC;MAC9C,IAAI,CAACX,UAAU,CAAC,CAAC;IACnB;EAAC;IAAAC,GAAA;IAAAC,KAAA,EACD,SAAAqB,gBAAgBA,CAACC,KAAK,EAAEC,mBAAmB,EAAEC,aAAa,EAAE;MAC1D,IAAI,CAAC,IAAI,CAACjC,QAAQ,IAAI+B,KAAK,CAACG,YAAY,CAACH,KAAK,CAACI,IAAI,CAAC,KAAK,CAAC,IAAIH,mBAAmB,CAACI,IAAI,GAAGJ,mBAAmB,CAACK,KAAK,IAAI,IAAI,CAAClC,iBAAiB,IAAI,IAAI,EAAE;QACpJ,OAAO,CAAC;MACV;MACA,IAAImC,OAAO,GAAGL,aAAa,CAACK,OAAO;QACjCC,MAAM,GAAGN,aAAa,CAACM,MAAM;QAC7BC,QAAQ,GAAGP,aAAa,CAACO,QAAQ;QACjCC,aAAa,GAAGR,aAAa,CAACQ,aAAa;MAI7C,IAAI,CAACxC,KAAK,CAACT,YAAY,EAAE;MACzB,IAAI,CAACS,KAAK,CAACZ,cAAc,IAAIgB,IAAI,CAACsB,KAAK,CAACc,aAAa,CAAC;MACtD,IAAI,CAACxC,KAAK,CAACX,eAAe,IAAIe,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACqC,GAAG,CAACJ,OAAO,CAAC,CAAC;MAC3D,IAAIK,WAAW,GAAGtC,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACqC,GAAG,CAACF,QAAQ,CAAC,GAAG,IAAI,CAAC;MAGvD,IAAIzB,GAAG,GAAGF,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC;MAClC,IAAI,IAAI,CAAChB,kBAAkB,IAAI,IAAI,EAAE;QACnC,IAAI,CAACE,KAAK,CAACjB,YAAY,IAAI+B,GAAG,GAAG,IAAI,CAAChB,kBAAkB;MAC1D;MACA,IAAI,CAACA,kBAAkB,GAAG,IAAI;MAC9B,IAAI,IAAI,CAACG,qBAAqB,IAAI,IAAI,EAAE;QACtC,IAAI,CAACD,KAAK,CAACd,eAAe,IAAI4B,GAAG,GAAG,IAAI,CAACb,qBAAqB;MAChE;MACA,IAAI,CAACA,qBAAqB,GAAG,IAAI;MACjC,IAAI0C,QAAQ,GAAG,CAAC;MAChB,IAAIP,KAAK,GAAGL,mBAAmB,CAACK,KAAK;MACrC,IAAIQ,UAAU,GAAG,IAAI,CAACzC,gBAAgB,CAACiC,KAAK,EAAEN,KAAK,CAAC;MACpD,OAAOM,KAAK,IAAIL,mBAAmB,CAACI,IAAI,KAAK,CAACS,UAAU,IAAI,CAACA,UAAU,CAACC,QAAQ,CAAC,EAAE;QACjFD,UAAU,GAAG,IAAI,CAACzC,gBAAgB,CAACiC,KAAK,EAAEN,KAAK,CAAC;QAChDM,KAAK,EAAE;MACT;MAGA,IAAIQ,UAAU,IAAIR,KAAK,GAAG,CAAC,EAAE;QAC3BO,QAAQ,GAAGvC,IAAI,CAAC0C,GAAG,CAACN,aAAa,EAAEpC,IAAI,CAAC2C,GAAG,CAAC,CAAC,EAAEH,UAAU,CAACN,MAAM,GAAGA,MAAM,CAAC,CAAC;MAC7E;MACA,IAAIU,WAAW,GAAG,CAAC;MACnB,IAAIb,IAAI,GAAGJ,mBAAmB,CAACI,IAAI;MACnC,IAAIc,SAAS,GAAG,IAAI,CAAC9C,gBAAgB,CAACgC,IAAI,EAAEL,KAAK,CAAC;MAClD,OAAOK,IAAI,IAAIJ,mBAAmB,CAACK,KAAK,KAAK,CAACa,SAAS,IAAI,CAACA,SAAS,CAACJ,QAAQ,CAAC,EAAE;QAC/EI,SAAS,GAAG,IAAI,CAAC9C,gBAAgB,CAACgC,IAAI,EAAEL,KAAK,CAAC;QAC9CK,IAAI,EAAE;MACR;MAGA,IAAIc,SAAS,IAAId,IAAI,GAAGL,KAAK,CAACG,YAAY,CAACH,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE;QAC1D,IAAIgB,UAAU,GAAGD,SAAS,CAACX,MAAM,GAAGW,SAAS,CAACE,MAAM;QACpDH,WAAW,GAAG5C,IAAI,CAAC0C,GAAG,CAACN,aAAa,EAAEpC,IAAI,CAAC2C,GAAG,CAAC,CAAC,EAAET,MAAM,GAAGE,aAAa,GAAGU,UAAU,CAAC,CAAC;MACzF;MACA,IAAI/D,YAAY,GAAGiB,IAAI,CAACsB,KAAK,CAACiB,QAAQ,GAAGK,WAAW,CAAC;MACrD,IAAII,SAAS,GAAGjE,YAAY,GAAGqD,aAAa;MAC5C,IAAIY,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACtD,kBAAkB,GAAGgB,GAAG;QAC7B,IAAI,CAACd,KAAK,CAAChB,mBAAmB,IAAI0D,WAAW;QAC7C,IAAI,CAAC1C,KAAK,CAAClB,eAAe,EAAE;QAC5B,IAAI,CAACkB,KAAK,CAACb,YAAY,IAAIA,YAAY;QACvC,IAAIiE,SAAS,GAAG,GAAG,EAAE;UACnB,IAAI,CAACnD,qBAAqB,GAAGa,GAAG;UAChC,IAAI,CAACd,KAAK,CAACf,kBAAkB,EAAE;QACjC;MACF,CAAC,MAAM,IAAIyD,WAAW,GAAG,IAAI,IAAItC,IAAI,CAACqC,GAAG,CAACJ,OAAO,CAAC,GAAG,CAAC,EAAE;QACtD,IAAI,CAACtB,kBAAkB,CAAC,CAAC;MAC3B;MACA,OAAOqC,SAAS;IAClB;EAAC;IAAA7C,GAAA;IAAAC,KAAA,EACD,SAAA6C,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACtD,QAAQ;IACtB;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EACD,SAAAF,UAAUA,CAAA,EAAG;MACX,IAAI,CAACR,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACE,KAAK,GAAG,IAAInB,IAAI,CAAC,CAAC;MACvB,IAAI,CAACoB,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC/B;EAAC;IAAAK,GAAA;IAAAC,KAAA,EApJD,SAAO8C,WAAWA,CAACC,QAAQ,EAAE;MAC3B,IAAI5D,WAAW,KAAK,IAAI,EAAE;QACxBe,OAAO,CAAC8C,IAAI,CAAC,2DAA2D,CAAC;MAC3E;MACA/D,UAAU,CAACgE,IAAI,CAACF,QAAQ,CAAC;MACzB,OAAO;QACLG,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;UACZjE,UAAU,GAAGA,UAAU,CAACkE,MAAM,CAAC,UAAA/B,QAAQ;YAAA,OAAI2B,QAAQ,KAAK3B,QAAQ;UAAA,EAAC;QACnE;MACF,CAAC;IACH;EAAC;IAAArB,GAAA;IAAAC,KAAA,EACD,SAAOoD,aAAaA,CAACC,UAAU,EAAE;MAC/BlE,WAAW,GAAGkE,UAAU;IAC1B;EAAC;IAAAtD,GAAA;IAAAC,KAAA,EACD,SAAOsD,iBAAiBA,CAACC,cAAc,EAAE;MACvCrE,eAAe,GAAGqE,cAAc;IAClC;EAAC;AAAA;AAsIH,eAAenE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}