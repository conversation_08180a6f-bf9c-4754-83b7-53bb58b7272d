{"ast": null, "code": "import * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Icon from \"../Icon\";\nvar ICON_SIZE = 24;\nvar ListIcon = function ListIcon(_ref) {\n  var icon = _ref.icon,\n    iconColor = _ref.color,\n    style = _ref.style,\n    themeOverrides = _ref.theme;\n  var theme = useInternalTheme(themeOverrides);\n  return React.createElement(View, {\n    style: [theme.isV3 ? styles.itemV3 : styles.item, style],\n    pointerEvents: \"box-none\"\n  }, React.createElement(Icon, {\n    source: icon,\n    size: ICON_SIZE,\n    color: iconColor,\n    theme: theme\n  }));\n};\nvar styles = StyleSheet.create({\n  item: {\n    margin: 8,\n    height: 40,\n    width: 40,\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  itemV3: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  }\n});\nListIcon.displayName = 'List.Icon';\nexport default ListIcon;", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "useInternalTheme", "Icon", "ICON_SIZE", "ListIcon", "_ref", "icon", "iconColor", "color", "style", "themeOverrides", "theme", "createElement", "isV3", "styles", "itemV3", "item", "pointerEvents", "source", "size", "create", "margin", "height", "width", "alignItems", "justifyContent", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/List/ListIcon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { View, ViewStyle, StyleSheet, StyleProp } from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport Icon, { IconSource } from '../Icon';\n\nexport type Props = {\n  /**\n   * Icon to show.\n   */\n  icon: IconSource;\n  /**\n   * Color for the icon.\n   */\n  color?: string;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\nconst ICON_SIZE = 24;\n\n/**\n * A component to show an icon in a list item.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { List, MD3Colors } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <>\n *     <List.Icon color={MD3Colors.tertiary70} icon=\"folder\" />\n *     <List.Icon color={MD3Colors.tertiary70} icon=\"equal\" />\n *     <List.Icon color={MD3Colors.tertiary70} icon=\"calendar\" />\n *   </>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst ListIcon = ({\n  icon,\n  color: iconColor,\n  style,\n  theme: themeOverrides,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  return (\n    <View\n      style={[theme.isV3 ? styles.itemV3 : styles.item, style]}\n      pointerEvents=\"box-none\"\n    >\n      <Icon source={icon} size={ICON_SIZE} color={iconColor} theme={theme} />\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  item: {\n    margin: 8,\n    height: 40,\n    width: 40,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  itemV3: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n});\n\nListIcon.displayName = 'List.Icon';\n\nexport default ListIcon;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAG9B,SAASC,gBAAgB;AAEzB,OAAOC,IAAI;AAkBX,IAAMC,SAAS,GAAG,EAAE;AAqBpB,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,IAAA,EAKJ;EAAA,IAJXC,IAAI,GAIED,IAAA,CAJNC,IAAI;IACGC,SAAS,GAGVF,IAAA,CAHNG,KAAK;IACLC,KAAK,GAECJ,IAAA,CAFNI,KAAK;IACEC,cAAA,GACDL,IAAA,CADNM,KAAK;EAEL,IAAMA,KAAK,GAAGV,gBAAgB,CAACS,cAAc,CAAC;EAE9C,OACEZ,KAAA,CAAAc,aAAA,CAACb,IAAI;IACHU,KAAK,EAAE,CAACE,KAAK,CAACE,IAAI,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACE,IAAI,EAAEP,KAAK,CAAE;IACzDQ,aAAa,EAAC;EAAU,GAExBnB,KAAA,CAAAc,aAAA,CAACV,IAAI;IAACgB,MAAM,EAAEZ,IAAK;IAACa,IAAI,EAAEhB,SAAU;IAACK,KAAK,EAAED,SAAU;IAACI,KAAK,EAAEA;EAAM,CAAE,CAClE,CAAC;AAEX,CAAC;AAED,IAAMG,MAAM,GAAGd,UAAU,CAACoB,MAAM,CAAC;EAC/BJ,IAAI,EAAE;IACJK,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDV,MAAM,EAAE;IACNS,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEFrB,QAAQ,CAACsB,WAAW,GAAG,WAAW;AAElC,eAAetB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}