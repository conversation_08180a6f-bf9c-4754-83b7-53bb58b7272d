{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport React from 'react';\nimport Text from \"react-native-web/dist/exports/Text\";\nimport View from \"react-native-web/dist/exports/View\";\nvar ExpoBarCodeScannerView = function (_React$Component) {\n  function ExpoBarCodeScannerView() {\n    _classCallCheck(this, ExpoBarCodeScannerView);\n    return _callSuper(this, ExpoBarCodeScannerView, arguments);\n  }\n  _inherits(ExpoBarCodeScannerView, _React$Component);\n  return _createClass(ExpoBarCodeScannerView, [{\n    key: \"render\",\n    value: function render() {\n      return React.createElement(View, null, React.createElement(Text, null, \"BarCodeScanner Component not supported on the web\"));\n    }\n  }]);\n}(React.Component);\nexport { ExpoBarCodeScannerView as default };", "map": {"version": 3, "names": ["React", "Text", "View", "ExpoBarCodeScannerView", "_React$Component", "_classCallCheck", "_callSuper", "arguments", "_inherits", "_createClass", "key", "value", "render", "createElement", "Component", "default"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-barcode-scanner/src/ExpoBarCodeScannerView.web.tsx"], "sourcesContent": ["import React from 'react';\nimport { Text, View } from 'react-native';\n\nexport default class ExpoBarCodeScannerView extends React.Component<object> {\n  render() {\n    return (\n      <View>\n        <Text>BarCodeScanner Component not supported on the web</Text>\n      </View>\n    );\n  }\n}\n"], "mappings": ";;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,IAGLC,sBAAuB,aAAAC,gBAAA;EAAA,SAAAD,uBAAA;IAAAE,eAAA,OAAAF,sBAAA;IAAA,OAAAG,UAAA,OAAAH,sBAAA,EAAAI,SAAA;EAAA;EAAAC,SAAA,CAAAL,sBAAA,EAAAC,gBAAA;EAAA,OAAAK,YAAA,CAAAN,sBAAA;IAAAO,GAAA;IAAAC,KAAA,EAC1C,SAAAC,MAAMA,CAAA;MACJ,OACEZ,KAAA,CAAAa,aAAA,CAACX,IAAI,QACHF,KAAA,CAAAa,aAAA,CAACZ,IAAI,4DAAyD,CACzD;IAEX;EAAC;AAAA,EAPiDD,KAAK,CAACc,SAAiB;AAAA,SAAtDX,sBAAuB,IAAAY,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}