{"ast": null, "code": "export default function isSelectionValid() {\n  var selection = window.getSelection();\n  var string = selection.toString();\n  var anchorNode = selection.anchorNode;\n  var focusNode = selection.focusNode;\n  var isTextNode = anchorNode && anchorNode.nodeType === window.Node.TEXT_NODE || focusNode && focusNode.nodeType === window.Node.TEXT_NODE;\n  return string.length >= 1 && string !== '\\n' && isTextNode;\n}", "map": {"version": 3, "names": ["isSelectionValid", "selection", "window", "getSelection", "string", "toString", "anchorNode", "focusNode", "isTextNode", "nodeType", "Node", "TEXT_NODE", "length"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/modules/isSelectionValid/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nexport default function isSelectionValid() {\n  var selection = window.getSelection();\n  var string = selection.toString();\n  var anchorNode = selection.anchorNode;\n  var focusNode = selection.focusNode;\n  var isTextNode = anchorNode && anchorNode.nodeType === window.Node.TEXT_NODE || focusNode && focusNode.nodeType === window.Node.TEXT_NODE;\n  return string.length >= 1 && string !== '\\n' && isTextNode;\n}"], "mappings": "AASA,eAAe,SAASA,gBAAgBA,CAAA,EAAG;EACzC,IAAIC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;EACrC,IAAIC,MAAM,GAAGH,SAAS,CAACI,QAAQ,CAAC,CAAC;EACjC,IAAIC,UAAU,GAAGL,SAAS,CAACK,UAAU;EACrC,IAAIC,SAAS,GAAGN,SAAS,CAACM,SAAS;EACnC,IAAIC,UAAU,GAAGF,UAAU,IAAIA,UAAU,CAACG,QAAQ,KAAKP,MAAM,CAACQ,IAAI,CAACC,SAAS,IAAIJ,SAAS,IAAIA,SAAS,CAACE,QAAQ,KAAKP,MAAM,CAACQ,IAAI,CAACC,SAAS;EACzI,OAAOP,MAAM,CAACQ,MAAM,IAAI,CAAC,IAAIR,MAAM,KAAK,IAAI,IAAII,UAAU;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}