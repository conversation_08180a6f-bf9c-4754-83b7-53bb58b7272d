{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport invariant from 'invariant';\nimport NativeEventEmitter from \"react-native-web/dist/exports/NativeEventEmitter\";\nimport NativeModules from \"react-native-web/dist/exports/NativeModules\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nvar nativeEmitterSubscriptionKey = '@@nativeEmitterSubscription@@';\nexport var EventEmitter = function () {\n  function EventEmitter(nativeModule) {\n    _classCallCheck(this, EventEmitter);\n    this._listenerCount = 0;\n    if (nativeModule.__expo_module_name__ && NativeModules.EXReactNativeEventEmitter) {\n      nativeModule.addListener = function () {\n        var _NativeModules$EXReac;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        return (_NativeModules$EXReac = NativeModules.EXReactNativeEventEmitter).addProxiedListener.apply(_NativeModules$EXReac, [nativeModule.__expo_module_name__].concat(args));\n      };\n      nativeModule.removeListeners = function () {\n        var _NativeModules$EXReac2;\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        return (_NativeModules$EXReac2 = NativeModules.EXReactNativeEventEmitter).removeProxiedListeners.apply(_NativeModules$EXReac2, [nativeModule.__expo_module_name__].concat(args));\n      };\n    }\n    this._nativeModule = nativeModule;\n    this._eventEmitter = new NativeEventEmitter(nativeModule);\n  }\n  return _createClass(EventEmitter, [{\n    key: \"addListener\",\n    value: function addListener(eventName, listener) {\n      var _this = this;\n      if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.startObserving) {\n        this._nativeModule.startObserving();\n      }\n      this._listenerCount++;\n      var nativeEmitterSubscription = this._eventEmitter.addListener(eventName, listener);\n      var subscription = _defineProperty(_defineProperty({}, nativeEmitterSubscriptionKey, nativeEmitterSubscription), \"remove\", function remove() {\n        _this.removeSubscription(subscription);\n      });\n      return subscription;\n    }\n  }, {\n    key: \"removeAllListeners\",\n    value: function removeAllListeners(eventName) {\n      var removedListenerCount = this._eventEmitter.listenerCount ? this._eventEmitter.listenerCount(eventName) : this._eventEmitter.listeners(eventName).length;\n      this._eventEmitter.removeAllListeners(eventName);\n      this._listenerCount -= removedListenerCount;\n      invariant(this._listenerCount >= 0, `EventEmitter must have a non-negative number of listeners`);\n      if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n        this._nativeModule.stopObserving();\n      }\n    }\n  }, {\n    key: \"removeSubscription\",\n    value: function removeSubscription(subscription) {\n      var nativeEmitterSubscription = subscription[nativeEmitterSubscriptionKey];\n      if (!nativeEmitterSubscription) {\n        return;\n      }\n      if ('remove' in nativeEmitterSubscription) {\n        nativeEmitterSubscription.remove();\n      } else if ('removeSubscription' in this._eventEmitter) {\n        this._eventEmitter.removeSubscription(nativeEmitterSubscription);\n      }\n      this._listenerCount--;\n      delete subscription[nativeEmitterSubscriptionKey];\n      subscription.remove = function () {};\n      if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n        this._nativeModule.stopObserving();\n      }\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(eventName) {\n      var _this$_eventEmitter;\n      for (var _len3 = arguments.length, params = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n        params[_key3 - 1] = arguments[_key3];\n      }\n      (_this$_eventEmitter = this._eventEmitter).emit.apply(_this$_eventEmitter, [eventName].concat(params));\n    }\n  }]);\n}();", "map": {"version": 3, "names": ["invariant", "NativeEventEmitter", "NativeModules", "Platform", "nativeEmitterSubscriptionKey", "EventEmitter", "nativeModule", "_classCallCheck", "_listenerCount", "__expo_module_name__", "EXReactNativeEventEmitter", "addListener", "_NativeModules$EXReac", "_len", "arguments", "length", "args", "Array", "_key", "addProxiedListener", "apply", "concat", "removeListeners", "_NativeModules$EXReac2", "_len2", "_key2", "removeProxiedListeners", "_nativeModule", "_eventEmitter", "_createClass", "key", "value", "eventName", "listener", "_this", "OS", "startObserving", "nativeEmitterSubscription", "subscription", "_defineProperty", "remove", "removeSubscription", "removeAllListeners", "removedListenerCount", "listenerCount", "listeners", "stopObserving", "emit", "_this$_eventEmitter", "_len3", "params", "_key3"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-modules-core/src/EventEmitter.ts"], "sourcesContent": ["import invariant from 'invariant';\nimport { NativeEventEmitter, NativeModules, Platform } from 'react-native';\n\nconst nativeEmitterSubscriptionKey = '@@nativeEmitterSubscription@@';\n\ntype NativeModule = {\n  __expo_module_name__?: string;\n  startObserving?: () => void;\n  stopObserving?: () => void;\n  addListener: (eventName: string) => void;\n  removeListeners: (count: number) => void;\n};\n\n// @needsAudit\nexport type Subscription = {\n  /**\n   * A method to unsubscribe the listener.\n   */\n  remove: () => void;\n};\n\nexport class EventEmitter {\n  _listenerCount = 0;\n  _nativeModule: NativeModule;\n  _eventEmitter: NativeEventEmitter;\n\n  constructor(nativeModule: NativeModule) {\n    // Expo modules installed through the JSI don't have `addListener` and `removeListeners` set,\n    // so if someone wants to use them with `EventEmitter`, make sure to provide these functions\n    // as they are required by `NativeEventEmitter`. This is only temporary — in the future\n    // JSI modules will have event emitter built in.\n    if (nativeModule.__expo_module_name__ && NativeModules.EXReactNativeEventEmitter) {\n      nativeModule.addListener = (...args) =>\n        NativeModules.EXReactNativeEventEmitter.addProxiedListener(\n          nativeModule.__expo_module_name__,\n          ...args\n        );\n      nativeModule.removeListeners = (...args) =>\n        NativeModules.EXReactNativeEventEmitter.removeProxiedListeners(\n          nativeModule.__expo_module_name__,\n          ...args\n        );\n    }\n    this._nativeModule = nativeModule;\n    this._eventEmitter = new NativeEventEmitter(nativeModule as any);\n  }\n\n  addListener<T>(eventName: string, listener: (event: T) => void): Subscription {\n    if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.startObserving) {\n      this._nativeModule.startObserving();\n    }\n\n    this._listenerCount++;\n    const nativeEmitterSubscription = this._eventEmitter.addListener(eventName, listener);\n    const subscription = {\n      [nativeEmitterSubscriptionKey]: nativeEmitterSubscription,\n      remove: () => {\n        this.removeSubscription(subscription);\n      },\n    };\n    return subscription;\n  }\n\n  removeAllListeners(eventName: string): void {\n    // @ts-ignore: the EventEmitter interface has been changed in react-native@0.64.0\n    const removedListenerCount = this._eventEmitter.listenerCount\n      ? // @ts-ignore: this is available since 0.64\n        this._eventEmitter.listenerCount(eventName)\n      : // @ts-ignore: this is available in older versions\n        this._eventEmitter.listeners(eventName).length;\n    this._eventEmitter.removeAllListeners(eventName);\n    this._listenerCount -= removedListenerCount;\n    invariant(\n      this._listenerCount >= 0,\n      `EventEmitter must have a non-negative number of listeners`\n    );\n\n    if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n      this._nativeModule.stopObserving();\n    }\n  }\n\n  removeSubscription(subscription: Subscription): void {\n    const nativeEmitterSubscription = subscription[nativeEmitterSubscriptionKey];\n    if (!nativeEmitterSubscription) {\n      return;\n    }\n\n    if ('remove' in nativeEmitterSubscription) {\n      // `react-native-web@0.17.1` doesn't support `removeSubscription`\n      nativeEmitterSubscription.remove();\n    } else if ('removeSubscription' in this._eventEmitter) {\n      this._eventEmitter.removeSubscription(nativeEmitterSubscription!);\n    }\n    this._listenerCount--;\n\n    // Ensure that the emitter's internal state remains correct even if `removeSubscription` is\n    // called again with the same subscription\n    delete subscription[nativeEmitterSubscriptionKey];\n\n    // Release closed-over references to the emitter\n    subscription.remove = () => {};\n\n    if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n      this._nativeModule.stopObserving();\n    }\n  }\n\n  emit(eventName: string, ...params: any[]): void {\n    this._eventEmitter.emit(eventName, ...params);\n  }\n}\n"], "mappings": ";;;AAAA,OAAOA,SAAS,MAAM,WAAW;AAAC,OAAAC,kBAAA;AAAA,OAAAC,aAAA;AAAA,OAAAC,QAAA;AAGlC,IAAMC,4BAA4B,GAAG,+BAA+B;AAkBpE,WAAaC,YAAY;EAKvB,SAAAA,aAAYC,YAA0B;IAAAC,eAAA,OAAAF,YAAA;IAAA,KAJtCG,cAAc,GAAG,CAAC;IAShB,IAAIF,YAAY,CAACG,oBAAoB,IAAIP,aAAa,CAACQ,yBAAyB,EAAE;MAChFJ,YAAY,CAACK,WAAW,GAAG;QAAA,IAAAC,qBAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAIC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;QAAA;QAAA,OACjC,CAAAN,qBAAA,GAAAV,aAAa,CAACQ,yBAAyB,EAACS,kBAAkB,CAAAC,KAAA,CAAAR,qBAAA,GACxDN,YAAY,CAACG,oBAAoB,EAAAY,MAAA,CAC9BL,IAAI,EACR;MAAA;MACHV,YAAY,CAACgB,eAAe,GAAG;QAAA,IAAAC,sBAAA;QAAA,SAAAC,KAAA,GAAAV,SAAA,CAAAC,MAAA,EAAIC,IAAI,OAAAC,KAAA,CAAAO,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJT,IAAI,CAAAS,KAAA,IAAAX,SAAA,CAAAW,KAAA;QAAA;QAAA,OACrC,CAAAF,sBAAA,GAAArB,aAAa,CAACQ,yBAAyB,EAACgB,sBAAsB,CAAAN,KAAA,CAAAG,sBAAA,GAC5DjB,YAAY,CAACG,oBAAoB,EAAAY,MAAA,CAC9BL,IAAI,EACR;MAAA;;IAEL,IAAI,CAACW,aAAa,GAAGrB,YAAY;IACjC,IAAI,CAACsB,aAAa,GAAG,IAAI3B,kBAAkB,CAACK,YAAmB,CAAC;EAClE;EAAC,OAAAuB,YAAA,CAAAxB,YAAA;IAAAyB,GAAA;IAAAC,KAAA,EAED,SAAApB,WAAWA,CAAIqB,SAAiB,EAAEC,QAA4B;MAAA,IAAAC,KAAA;MAC5D,IAAI,CAAC,IAAI,CAAC1B,cAAc,IAAIL,QAAQ,CAACgC,EAAE,KAAK,KAAK,IAAI,IAAI,CAACR,aAAa,CAACS,cAAc,EAAE;QACtF,IAAI,CAACT,aAAa,CAACS,cAAc,EAAE;;MAGrC,IAAI,CAAC5B,cAAc,EAAE;MACrB,IAAM6B,yBAAyB,GAAG,IAAI,CAACT,aAAa,CAACjB,WAAW,CAACqB,SAAS,EAAEC,QAAQ,CAAC;MACrF,IAAMK,YAAY,GAAAC,eAAA,CAAAA,eAAA,KACfnC,4BAA4B,EAAGiC,yBAAyB,aACjD,SAARG,MAAMA,CAAA,EAAO;QACXN,KAAI,CAACO,kBAAkB,CAACH,YAAY,CAAC;MACvC,CAAC,CACF;MACD,OAAOA,YAAY;IACrB;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAED,SAAAW,kBAAkBA,CAACV,SAAiB;MAElC,IAAMW,oBAAoB,GAAG,IAAI,CAACf,aAAa,CAACgB,aAAa,GAEzD,IAAI,CAAChB,aAAa,CAACgB,aAAa,CAACZ,SAAS,CAAC,GAE3C,IAAI,CAACJ,aAAa,CAACiB,SAAS,CAACb,SAAS,CAAC,CAACjB,MAAM;MAClD,IAAI,CAACa,aAAa,CAACc,kBAAkB,CAACV,SAAS,CAAC;MAChD,IAAI,CAACxB,cAAc,IAAImC,oBAAoB;MAC3C3C,SAAS,CACP,IAAI,CAACQ,cAAc,IAAI,CAAC,EACxB,2DAA2D,CAC5D;MAED,IAAI,CAAC,IAAI,CAACA,cAAc,IAAIL,QAAQ,CAACgC,EAAE,KAAK,KAAK,IAAI,IAAI,CAACR,aAAa,CAACmB,aAAa,EAAE;QACrF,IAAI,CAACnB,aAAa,CAACmB,aAAa,EAAE;;IAEtC;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAED,SAAAU,kBAAkBA,CAACH,YAA0B;MAC3C,IAAMD,yBAAyB,GAAGC,YAAY,CAAClC,4BAA4B,CAAC;MAC5E,IAAI,CAACiC,yBAAyB,EAAE;QAC9B;;MAGF,IAAI,QAAQ,IAAIA,yBAAyB,EAAE;QAEzCA,yBAAyB,CAACG,MAAM,EAAE;OACnC,MAAM,IAAI,oBAAoB,IAAI,IAAI,CAACZ,aAAa,EAAE;QACrD,IAAI,CAACA,aAAa,CAACa,kBAAkB,CAACJ,yBAA0B,CAAC;;MAEnE,IAAI,CAAC7B,cAAc,EAAE;MAIrB,OAAO8B,YAAY,CAAClC,4BAA4B,CAAC;MAGjDkC,YAAY,CAACE,MAAM,GAAG,YAAK,CAAE,CAAC;MAE9B,IAAI,CAAC,IAAI,CAAChC,cAAc,IAAIL,QAAQ,CAACgC,EAAE,KAAK,KAAK,IAAI,IAAI,CAACR,aAAa,CAACmB,aAAa,EAAE;QACrF,IAAI,CAACnB,aAAa,CAACmB,aAAa,EAAE;;IAEtC;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAED,SAAAgB,IAAIA,CAACf,SAAiB,EAAkB;MAAA,IAAAgB,mBAAA;MAAA,SAAAC,KAAA,GAAAnC,SAAA,CAAAC,MAAA,EAAbmC,MAAa,OAAAjC,KAAA,CAAAgC,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;QAAbD,MAAa,CAAAC,KAAA,QAAArC,SAAA,CAAAqC,KAAA;MAAA;MACtC,CAAAH,mBAAA,OAAI,CAACpB,aAAa,EAACmB,IAAI,CAAA3B,KAAA,CAAA4B,mBAAA,GAAChB,SAAS,EAAAX,MAAA,CAAK6B,MAAM,EAAC;IAC/C;EAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}