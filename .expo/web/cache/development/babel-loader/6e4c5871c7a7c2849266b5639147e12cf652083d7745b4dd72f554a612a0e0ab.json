{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"name\", \"color\", \"size\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport Text from \"react-native-web/dist/exports/Text\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar MaterialCommunityIcon = function MaterialCommunityIcon(_ref) {\n  var name = _ref.name,\n    color = _ref.color,\n    size = _ref.size,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return _jsx(Text, _objectSpread(_objectSpread({\n    style: {\n      color: color,\n      fontSize: size,\n      fontWeight: 'bold',\n      textAlign: 'center',\n      width: size,\n      height: size\n    }\n  }, rest), {}, {\n    children: name.charAt(0).toUpperCase()\n  }));\n};\nexport default MaterialCommunityIcon;", "map": {"version": 3, "names": ["React", "Text", "jsx", "_jsx", "MaterialCommunityIcon", "_ref", "name", "color", "size", "rest", "_objectWithoutProperties", "_excluded", "_objectSpread", "style", "fontSize", "fontWeight", "textAlign", "width", "height", "children", "char<PERSON>t", "toUpperCase"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/utils/MaterialCommunityIcon.js"], "sourcesContent": ["/**\n * Custom Material Community Icon for React Native Paper\n * This is a workaround for the missing @react-native-vector-icons/material-design-icons package\n */\n\nimport React from 'react';\nimport { Text } from 'react-native';\n\n/**\n * Custom Material Community Icon Component\n * @param {Object} props - Component props\n * @returns {JSX.Element} - Icon component\n */\nconst MaterialCommunityIcon = ({ name, color, size, ...rest }) => {\n  // This is a placeholder icon\n  return (\n    <Text\n      style={{\n        color,\n        fontSize: size,\n        fontWeight: 'bold',\n        textAlign: 'center',\n        width: size,\n        height: size,\n      }}\n      {...rest}\n    >\n      {name.charAt(0).toUpperCase()}\n    </Text>\n  );\n};\n\nexport default MaterialCommunityIcon;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA;AAQ1B,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAAC,IAAA,EAAuC;EAAA,IAAjCC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAEC,IAAI,GAAAH,IAAA,CAAJG,IAAI;IAAKC,IAAI,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAEzD,OACER,IAAA,CAACF,IAAI,EAAAW,aAAA,CAAAA,aAAA;IACHC,KAAK,EAAE;MACLN,KAAK,EAALA,KAAK;MACLO,QAAQ,EAAEN,IAAI;MACdO,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAET,IAAI;MACXU,MAAM,EAAEV;IACV;EAAE,GACEC,IAAI;IAAAU,QAAA,EAEPb,IAAI,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;EAAC,EACzB,CAAC;AAEX,CAAC;AAED,eAAejB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}