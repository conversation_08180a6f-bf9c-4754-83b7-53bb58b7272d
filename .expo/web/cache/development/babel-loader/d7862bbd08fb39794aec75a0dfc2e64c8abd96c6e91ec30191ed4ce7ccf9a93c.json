{"ast": null, "code": "var rng = require(\"./lib/rng\");\nvar bytesToUuid = require(\"./lib/bytesToUuid\");\nfunction v4(options, buf, offset) {\n  var i = buf && offset || 0;\n  if (typeof options == 'string') {\n    buf = options === 'binary' ? new Array(16) : null;\n    options = null;\n  }\n  options = options || {};\n  var rnds = options.random || (options.rng || rng)();\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80;\n  if (buf) {\n    for (var ii = 0; ii < 16; ++ii) {\n      buf[i + ii] = rnds[ii];\n    }\n  }\n  return buf || bytesToUuid(rnds);\n}\nmodule.exports = v4;", "map": {"version": 3, "names": ["rng", "require", "bytesToUuid", "v4", "options", "buf", "offset", "i", "Array", "rnds", "random", "ii", "module", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-constants/node_modules/uuid/v4.js"], "sourcesContent": ["var rng = require('./lib/rng');\nvar bytesToUuid = require('./lib/bytesToUuid');\n\nfunction v4(options, buf, offset) {\n  var i = buf && offset || 0;\n\n  if (typeof(options) == 'string') {\n    buf = options === 'binary' ? new Array(16) : null;\n    options = null;\n  }\n  options = options || {};\n\n  var rnds = options.random || (options.rng || rng)();\n\n  // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n  rnds[6] = (rnds[6] & 0x0f) | 0x40;\n  rnds[8] = (rnds[8] & 0x3f) | 0x80;\n\n  // Copy bytes to buffer, if provided\n  if (buf) {\n    for (var ii = 0; ii < 16; ++ii) {\n      buf[i + ii] = rnds[ii];\n    }\n  }\n\n  return buf || bytesToUuid(rnds);\n}\n\nmodule.exports = v4;\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,OAAO,YAAY,CAAC;AAC9B,IAAIC,WAAW,GAAGD,OAAO,oBAAoB,CAAC;AAE9C,SAASE,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAChC,IAAIC,CAAC,GAAGF,GAAG,IAAIC,MAAM,IAAI,CAAC;EAE1B,IAAI,OAAOF,OAAQ,IAAI,QAAQ,EAAE;IAC/BC,GAAG,GAAGD,OAAO,KAAK,QAAQ,GAAG,IAAII,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI;IACjDJ,OAAO,GAAG,IAAI;EAChB;EACAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIK,IAAI,GAAGL,OAAO,CAACM,MAAM,IAAI,CAACN,OAAO,CAACJ,GAAG,IAAIA,GAAG,EAAE,CAAC;EAGnDS,IAAI,CAAC,CAAC,CAAC,GAAIA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EACjCA,IAAI,CAAC,CAAC,CAAC,GAAIA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EAGjC,IAAIJ,GAAG,EAAE;IACP,KAAK,IAAIM,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,EAAE,EAAE,EAAEA,EAAE,EAAE;MAC9BN,GAAG,CAACE,CAAC,GAAGI,EAAE,CAAC,GAAGF,IAAI,CAACE,EAAE,CAAC;IACxB;EACF;EAEA,OAAON,GAAG,IAAIH,WAAW,CAACO,IAAI,CAAC;AACjC;AAEAG,MAAM,CAACC,OAAO,GAAGV,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}