{"ast": null, "code": "export var initialWindowMetrics = null;\nexport var initialWindowSafeAreaInsets = null;", "map": {"version": 3, "names": ["initialWindowMetrics", "initialWindowSafeAreaInsets"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-safe-area-context/src/InitialWindow.ts"], "sourcesContent": ["import type { EdgeInsets, Metrics } from './SafeArea.types';\n\nexport const initialWindowMetrics: Metrics | null = null;\n\n/**\n * @deprecated\n */\nexport const initialWindowSafeAreaInsets: EdgeInsets | null = null;\n"], "mappings": "AAEA,OAAO,IAAMA,oBAAoC,GAAG,IAAI;AAKxD,OAAO,IAAMC,2BAA8C,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}