{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"children\", \"style\", \"imageStyle\", \"imageRef\"];\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport Image from \"../Image\";\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nvar emptyObject = {};\nvar ImageBackground = forwardRef(function (props, forwardedRef) {\n  var children = props.children,\n    _props$style = props.style,\n    style = _props$style === void 0 ? emptyObject : _props$style,\n    imageStyle = props.imageStyle,\n    imageRef = props.imageRef,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var _StyleSheet$flatten = StyleSheet.flatten(style),\n    height = _StyleSheet$flatten.height,\n    width = _StyleSheet$flatten.width;\n  return React.createElement(View, {\n    ref: forwardedRef,\n    style: style\n  }, React.createElement(Image, _extends({}, rest, {\n    ref: imageRef,\n    style: [{\n      width: width,\n      height: height,\n      zIndex: -1\n    }, StyleSheet.absoluteFill, imageStyle]\n  })), children);\n});\nImageBackground.displayName = 'ImageBackground';\nexport default ImageBackground;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "forwardRef", "Image", "StyleSheet", "View", "emptyObject", "ImageBackground", "props", "forwardedRef", "children", "_props$style", "style", "imageStyle", "imageRef", "rest", "_StyleSheet$flatten", "flatten", "height", "width", "createElement", "ref", "zIndex", "absoluteFill", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/ImageBackground/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"children\", \"style\", \"imageStyle\", \"imageRef\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport Image from '../Image';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\nvar emptyObject = {};\n\n/**\n * Very simple drop-in replacement for <Image> which supports nesting views.\n */\nvar ImageBackground = /*#__PURE__*/forwardRef((props, forwardedRef) => {\n  var children = props.children,\n    _props$style = props.style,\n    style = _props$style === void 0 ? emptyObject : _props$style,\n    imageStyle = props.imageStyle,\n    imageRef = props.imageRef,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var _StyleSheet$flatten = StyleSheet.flatten(style),\n    height = _StyleSheet$flatten.height,\n    width = _StyleSheet$flatten.width;\n  return /*#__PURE__*/React.createElement(View, {\n    ref: forwardedRef,\n    style: style\n  }, /*#__PURE__*/React.createElement(Image, _extends({}, rest, {\n    ref: imageRef,\n    style: [{\n      // Temporary Workaround:\n      // Current (imperfect yet) implementation of <Image> overwrites width and height styles\n      // (which is not quite correct), and these styles conflict with explicitly set styles\n      // of <ImageBackground> and with our internal layout model here.\n      // So, we have to proxy/reapply these styles explicitly for actual <Image> component.\n      // This workaround should be removed after implementing proper support of\n      // intrinsic content size of the <Image>.\n      width,\n      height,\n      zIndex: -1\n    }, StyleSheet.absoluteFill, imageStyle]\n  })), children);\n});\nImageBackground.displayName = 'ImageBackground';\nexport default ImageBackground;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,CAAC;AAU/D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,KAAK;AACZ,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,IAAIC,WAAW,GAAG,CAAC,CAAC;AAKpB,IAAIC,eAAe,GAAgBL,UAAU,CAAC,UAACM,KAAK,EAAEC,YAAY,EAAK;EACrE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,YAAY,GAAGH,KAAK,CAACI,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGL,WAAW,GAAGK,YAAY;IAC5DE,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,IAAI,GAAGhB,6BAA6B,CAACS,KAAK,EAAER,SAAS,CAAC;EACxD,IAAIgB,mBAAmB,GAAGZ,UAAU,CAACa,OAAO,CAACL,KAAK,CAAC;IACjDM,MAAM,GAAGF,mBAAmB,CAACE,MAAM;IACnCC,KAAK,GAAGH,mBAAmB,CAACG,KAAK;EACnC,OAAoBlB,KAAK,CAACmB,aAAa,CAACf,IAAI,EAAE;IAC5CgB,GAAG,EAAEZ,YAAY;IACjBG,KAAK,EAAEA;EACT,CAAC,EAAeX,KAAK,CAACmB,aAAa,CAACjB,KAAK,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEiB,IAAI,EAAE;IAC5DM,GAAG,EAAEP,QAAQ;IACbF,KAAK,EAAE,CAAC;MAQNO,KAAK,EAALA,KAAK;MACLD,MAAM,EAANA,MAAM;MACNI,MAAM,EAAE,CAAC;IACX,CAAC,EAAElB,UAAU,CAACmB,YAAY,EAAEV,UAAU;EACxC,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAAC;AAChB,CAAC,CAAC;AACFH,eAAe,CAACiB,WAAW,GAAG,iBAAiB;AAC/C,eAAejB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}