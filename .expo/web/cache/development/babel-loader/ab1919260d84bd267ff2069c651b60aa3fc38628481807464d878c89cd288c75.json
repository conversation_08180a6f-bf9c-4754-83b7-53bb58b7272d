{"ast": null, "code": "import { useCallback } from 'react';\nexport default function useMergeRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  return useCallback(function (current) {\n    for (var _i = 0, _refs = refs; _i < _refs.length; _i++) {\n      var ref = _refs[_i];\n      if (ref != null) {\n        if (typeof ref === 'function') {\n          ref(current);\n        } else {\n          ref.current = current;\n        }\n      }\n    }\n  }, [].concat(refs));\n}", "map": {"version": 3, "names": ["useCallback", "useMergeRefs", "_len", "arguments", "length", "refs", "Array", "_key", "current", "_i", "_refs", "ref", "concat"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/Utilities/useMergeRefs.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport { useCallback } from 'react';\n/**\n * Constructs a new ref that forwards new values to each of the given refs. The\n * given refs will always be invoked in the order that they are supplied.\n *\n * WARNING: A known problem of merging refs using this approach is that if any\n * of the given refs change, the returned callback ref will also be changed. If\n * the returned callback ref is supplied as a `ref` to a React element, this may\n * lead to problems with the given refs being invoked more times than desired.\n */\nexport default function useMergeRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  return useCallback(current => {\n    for (var _i = 0, _refs = refs; _i < _refs.length; _i++) {\n      var ref = _refs[_i];\n      if (ref != null) {\n        if (typeof ref === 'function') {\n          ref(current);\n        } else {\n          ref.current = current;\n        }\n      }\n    }\n  }, [...refs] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n}"], "mappings": "AAUA,SAASA,WAAW,QAAQ,OAAO;AAUnC,eAAe,SAASC,YAAYA,CAAA,EAAG;EACrC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,OAAOP,WAAW,CAAC,UAAAQ,OAAO,EAAI;IAC5B,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,KAAK,GAAGL,IAAI,EAAEI,EAAE,GAAGC,KAAK,CAACN,MAAM,EAAEK,EAAE,EAAE,EAAE;MACtD,IAAIE,GAAG,GAAGD,KAAK,CAACD,EAAE,CAAC;MACnB,IAAIE,GAAG,IAAI,IAAI,EAAE;QACf,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;UAC7BA,GAAG,CAACH,OAAO,CAAC;QACd,CAAC,MAAM;UACLG,GAAG,CAACH,OAAO,GAAGA,OAAO;QACvB;MACF;IACF;EACF,CAAC,KAAAI,MAAA,CAAMP,IAAI,CACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}