{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nvar _excluded = [\"style\", \"index\", \"children\", \"visibility\"];\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport View from \"react-native-web/dist/exports/View\";\nvar BottomNavigationRouteScreen = function (_React$Component) {\n  function BottomNavigationRouteScreen() {\n    _classCallCheck(this, BottomNavigationRouteScreen);\n    return _callSuper(this, BottomNavigationRouteScreen, arguments);\n  }\n  _inherits(BottomNavigationRouteScreen, _React$Component);\n  return _createClass(BottomNavigationRouteScreen, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        style = _this$props.style,\n        index = _this$props.index,\n        children = _this$props.children,\n        visibility = _this$props.visibility,\n        rest = _objectWithoutProperties(_this$props, _excluded);\n      var display = Platform.OS === 'web' ? visibility === 0 ? 'none' : 'flex' : undefined;\n      return React.createElement(View, _extends({\n        testID: `RouteScreen: ${index}`,\n        style: [style, {\n          display: display\n        }]\n      }, rest), children);\n    }\n  }]);\n}(React.Component);\nexport default Animated.createAnimatedComponent(BottomNavigationRouteScreen);", "map": {"version": 3, "names": ["React", "Animated", "Platform", "View", "BottomNavigationRouteScreen", "_React$Component", "_classCallCheck", "_callSuper", "arguments", "_inherits", "_createClass", "key", "value", "render", "_this$props", "props", "style", "index", "children", "visibility", "rest", "_objectWithoutProperties", "_excluded", "display", "OS", "undefined", "createElement", "_extends", "testID", "Component", "createAnimatedComponent"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/BottomNavigation/BottomNavigationRouteScreen.tsx"], "sourcesContent": ["import React from 'react';\nimport { Animated, Platform, View, ViewProps } from 'react-native';\n\ninterface Props extends ViewProps {\n  visibility?: 0 | 1 | Animated.AnimatedInterpolation<number>;\n  index: number;\n}\n\nclass BottomNavigationRouteScreen extends React.Component<Props> {\n  render(): JSX.Element {\n    const { style, index, children, visibility, ...rest } = this.props;\n\n    // On Web, the unfocused tab screens can still be clicked since they are transparent, but still there\n    // Hiding them with `display: none` makes sure that they won't receive clicks\n    // We only set it on Web since on native, react-native-pager-view's breaks due to layout changing\n    const display =\n      Platform.OS === 'web' ? (visibility === 0 ? 'none' : 'flex') : undefined;\n\n    return (\n      <View\n        testID={`RouteScreen: ${index}`}\n        style={[style, { display }]}\n        {...rest}\n      >\n        {children}\n      </View>\n    );\n  }\n}\n\nexport default Animated.createAnimatedComponent(BottomNavigationRouteScreen);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,IAAA;AAAA,IAQnBC,2BAA2B,aAAAC,gBAAA;EAAA,SAAAD,4BAAA;IAAAE,eAAA,OAAAF,2BAAA;IAAA,OAAAG,UAAA,OAAAH,2BAAA,EAAAI,SAAA;EAAA;EAAAC,SAAA,CAAAL,2BAAA,EAAAC,gBAAA;EAAA,OAAAK,YAAA,CAAAN,2BAAA;IAAAO,GAAA;IAAAC,KAAA,EAC/B,SAAAC,MAAMA,CAAA,EAAgB;MACpB,IAAAC,WAAA,GAAwD,IAAI,CAACC,KAAK;QAA1DC,KAAK,GAAAF,WAAA,CAALE,KAAK;QAAEC,KAAK,GAAAH,WAAA,CAALG,KAAK;QAAEC,QAAQ,GAAAJ,WAAA,CAARI,QAAQ;QAAEC,UAAU,GAAAL,WAAA,CAAVK,UAAU;QAAKC,IAAA,GAAAC,wBAAA,CAAAP,WAAA,EAAAQ,SAAA;MAK/C,IAAMC,OAAO,GACXrB,QAAQ,CAACsB,EAAE,KAAK,KAAK,GAAIL,UAAU,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,GAAIM,SAAS;MAE1E,OACEzB,KAAA,CAAA0B,aAAA,CAACvB,IAAI,EAAAwB,QAAA;QACHC,MAAM,EAAG,gBAAeX,KAAM,EAAE;QAChCD,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEO,OAAA,EAAAA;QAAQ,CAAC;MAAE,GACxBH,IAAI,GAEPF,QACG,CAAC;IAEX;EAAA;AAAA,EAnBwClB,KAAK,CAAC6B,SAAS;AAsBzD,eAAe5B,QAAQ,CAAC6B,uBAAuB,CAAC1B,2BAA2B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}