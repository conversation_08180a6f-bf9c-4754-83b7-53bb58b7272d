{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"animating\", \"color\", \"hidesWhenStopped\", \"size\", \"style\", \"theme\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Easing from \"react-native-web/dist/exports/Easing\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../core/theming\";\nvar DURATION = 2400;\nvar ActivityIndicator = function ActivityIndicator(_ref) {\n  var _theme$colors;\n  var _ref$animating = _ref.animating,\n    animating = _ref$animating === void 0 ? true : _ref$animating,\n    indicatorColor = _ref.color,\n    _ref$hidesWhenStopped = _ref.hidesWhenStopped,\n    hidesWhenStopped = _ref$hidesWhenStopped === void 0 ? true : _ref$hidesWhenStopped,\n    _ref$size = _ref.size,\n    indicatorSize = _ref$size === void 0 ? 'small' : _ref$size,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(0)),\n    timer = _React$useRef.current;\n  var _React$useRef2 = React.useRef(new Animated.Value(!animating && hidesWhenStopped ? 0 : 1)),\n    fade = _React$useRef2.current;\n  var rotation = React.useRef(undefined);\n  var scale = theme.animation.scale;\n  var startRotation = React.useCallback(function () {\n    Animated.timing(fade, {\n      duration: 200 * scale,\n      toValue: 1,\n      isInteraction: false,\n      useNativeDriver: true\n    }).start();\n    if (rotation.current) {\n      timer.setValue(0);\n      Animated.loop(rotation.current).start();\n    }\n  }, [scale, fade, timer]);\n  var stopRotation = function stopRotation() {\n    if (rotation.current) {\n      rotation.current.stop();\n    }\n  };\n  React.useEffect(function () {\n    if (rotation.current === undefined) {\n      rotation.current = Animated.timing(timer, {\n        duration: DURATION,\n        easing: Easing.linear,\n        useNativeDriver: Platform.OS !== 'web',\n        toValue: 1,\n        isInteraction: false\n      });\n    }\n    if (animating) {\n      startRotation();\n    } else if (hidesWhenStopped) {\n      Animated.timing(fade, {\n        duration: 200 * scale,\n        toValue: 0,\n        useNativeDriver: true,\n        isInteraction: false\n      }).start(stopRotation);\n    } else {\n      stopRotation();\n    }\n  }, [animating, fade, hidesWhenStopped, startRotation, scale, timer]);\n  var color = indicatorColor || ((_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.primary);\n  var size = typeof indicatorSize === 'string' ? indicatorSize === 'small' ? 24 : 48 : indicatorSize ? indicatorSize : 24;\n  var frames = 60 * DURATION / 1000;\n  var easing = Easing.bezier(0.4, 0.0, 0.7, 1.0);\n  var containerStyle = {\n    width: size,\n    height: size / 2,\n    overflow: 'hidden'\n  };\n  return React.createElement(View, _extends({\n    style: [styles.container, style]\n  }, rest, {\n    accessible: true,\n    accessibilityRole: \"progressbar\",\n    accessibilityState: {\n      busy: animating\n    }\n  }), React.createElement(Animated.View, {\n    style: [{\n      width: size,\n      height: size,\n      opacity: fade\n    }],\n    collapsable: false\n  }, [0, 1].map(function (index) {\n    var inputRange = Array.from(new Array(frames), function (_, frameIndex) {\n      return frameIndex / (frames - 1);\n    });\n    var outputRange = Array.from(new Array(frames), function (_, frameIndex) {\n      var progress = 2 * frameIndex / (frames - 1);\n      var rotation = index ? +(360 - 15) : -(180 - 15);\n      if (progress > 1.0) {\n        progress = 2.0 - progress;\n      }\n      var direction = index ? -1 : +1;\n      return `${direction * (180 - 30) * easing(progress) + rotation}deg`;\n    });\n    var layerStyle = {\n      width: size,\n      height: size,\n      transform: [{\n        rotate: timer.interpolate({\n          inputRange: [0, 1],\n          outputRange: [`${0 + 30 + 15}deg`, `${2 * 360 + 30 + 15}deg`]\n        })\n      }]\n    };\n    var viewportStyle = {\n      width: size,\n      height: size,\n      transform: [{\n        translateY: index ? -size / 2 : 0\n      }, {\n        rotate: timer.interpolate({\n          inputRange: inputRange,\n          outputRange: outputRange\n        })\n      }]\n    };\n    var offsetStyle = index ? {\n      top: size / 2\n    } : null;\n    var lineStyle = {\n      width: size,\n      height: size,\n      borderColor: color,\n      borderWidth: size / 10,\n      borderRadius: size / 2\n    };\n    return React.createElement(Animated.View, {\n      key: index,\n      style: [styles.layer]\n    }, React.createElement(Animated.View, {\n      style: layerStyle\n    }, React.createElement(Animated.View, {\n      style: [containerStyle, offsetStyle],\n      collapsable: false\n    }, React.createElement(Animated.View, {\n      style: viewportStyle\n    }, React.createElement(Animated.View, {\n      style: containerStyle,\n      collapsable: false\n    }, React.createElement(Animated.View, {\n      style: lineStyle\n    }))))));\n  })));\n};\nvar styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  layer: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    justifyContent: 'center',\n    alignItems: 'center'\n  })\n});\nexport default ActivityIndicator;", "map": {"version": 3, "names": ["React", "Animated", "Easing", "Platform", "StyleSheet", "View", "useInternalTheme", "DURATION", "ActivityIndicator", "_ref", "_theme$colors", "_ref$animating", "animating", "indicatorColor", "color", "_ref$hidesWhenStopped", "hidesWhenStopped", "_ref$size", "size", "indicatorSize", "style", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "_React$useRef", "useRef", "Value", "timer", "current", "_React$useRef2", "fade", "rotation", "undefined", "scale", "animation", "startRotation", "useCallback", "timing", "duration", "toValue", "isInteraction", "useNativeDriver", "start", "setValue", "loop", "stopRotation", "stop", "useEffect", "easing", "linear", "OS", "colors", "primary", "frames", "bezier", "containerStyle", "width", "height", "overflow", "createElement", "_extends", "styles", "container", "accessible", "accessibilityRole", "accessibilityState", "busy", "opacity", "collapsable", "map", "index", "inputRange", "Array", "from", "_", "frameIndex", "outputRange", "progress", "direction", "layerStyle", "transform", "rotate", "interpolate", "viewportStyle", "translateY", "offsetStyle", "top", "lineStyle", "borderColor", "borderWidth", "borderRadius", "key", "layer", "create", "justifyContent", "alignItems", "_objectSpread", "absoluteFillObject"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/ActivityIndicator.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Easing,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../core/theming';\nimport type { ThemeProp } from '../types';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Whether to show the indicator or hide it.\n   */\n  animating?: boolean;\n  /**\n   * The color of the spinner.\n   */\n  color?: string;\n  /**\n   * Size of the indicator.\n   */\n  size?: 'small' | 'large' | number;\n  /**\n   * Whether the indicator should hide when not animating.\n   */\n  hidesWhenStopped?: boolean;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\nconst DURATION = 2400;\n\n/**\n * Activity indicator is used to present progress of some activity in the app.\n * It can be used as a drop-in replacement for the ActivityIndicator shipped with React Native.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { ActivityIndicator, MD2Colors } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <ActivityIndicator animating={true} color={MD2Colors.red800} />\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst ActivityIndicator = ({\n  animating = true,\n  color: indicatorColor,\n  hidesWhenStopped = true,\n  size: indicatorSize = 'small',\n  style,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { current: timer } = React.useRef<Animated.Value>(\n    new Animated.Value(0)\n  );\n  const { current: fade } = React.useRef<Animated.Value>(\n    new Animated.Value(!animating && hidesWhenStopped ? 0 : 1)\n  );\n\n  const rotation = React.useRef<Animated.CompositeAnimation | undefined>(\n    undefined\n  );\n\n  const {\n    animation: { scale },\n  } = theme;\n\n  const startRotation = React.useCallback(() => {\n    // Show indicator\n    Animated.timing(fade, {\n      duration: 200 * scale,\n      toValue: 1,\n      isInteraction: false,\n      useNativeDriver: true,\n    }).start();\n\n    // Circular animation in loop\n    if (rotation.current) {\n      timer.setValue(0);\n      // $FlowFixMe\n      Animated.loop(rotation.current).start();\n    }\n  }, [scale, fade, timer]);\n\n  const stopRotation = () => {\n    if (rotation.current) {\n      rotation.current.stop();\n    }\n  };\n\n  React.useEffect(() => {\n    if (rotation.current === undefined) {\n      // Circular animation in loop\n      rotation.current = Animated.timing(timer, {\n        duration: DURATION,\n        easing: Easing.linear,\n        // Animated.loop does not work if useNativeDriver is true on web\n        useNativeDriver: Platform.OS !== 'web',\n        toValue: 1,\n        isInteraction: false,\n      });\n    }\n\n    if (animating) {\n      startRotation();\n    } else if (hidesWhenStopped) {\n      // Hide indicator first and then stop rotation\n      Animated.timing(fade, {\n        duration: 200 * scale,\n        toValue: 0,\n        useNativeDriver: true,\n        isInteraction: false,\n      }).start(stopRotation);\n    } else {\n      stopRotation();\n    }\n  }, [animating, fade, hidesWhenStopped, startRotation, scale, timer]);\n\n  const color = indicatorColor || theme.colors?.primary;\n  const size =\n    typeof indicatorSize === 'string'\n      ? indicatorSize === 'small'\n        ? 24\n        : 48\n      : indicatorSize\n      ? indicatorSize\n      : 24;\n\n  const frames = (60 * DURATION) / 1000;\n  const easing = Easing.bezier(0.4, 0.0, 0.7, 1.0);\n  const containerStyle = {\n    width: size,\n    height: size / 2,\n    overflow: 'hidden' as const,\n  };\n\n  return (\n    <View\n      style={[styles.container, style]}\n      {...rest}\n      accessible\n      accessibilityRole=\"progressbar\"\n      accessibilityState={{ busy: animating }}\n    >\n      <Animated.View\n        style={[{ width: size, height: size, opacity: fade }]}\n        collapsable={false}\n      >\n        {[0, 1].map((index) => {\n          // Thanks to https://github.com/n4kz/react-native-indicators for the great work\n          const inputRange = Array.from(\n            new Array(frames),\n            (_, frameIndex) => frameIndex / (frames - 1)\n          );\n          const outputRange = Array.from(new Array(frames), (_, frameIndex) => {\n            let progress = (2 * frameIndex) / (frames - 1);\n            const rotation = index ? +(360 - 15) : -(180 - 15);\n\n            if (progress > 1.0) {\n              progress = 2.0 - progress;\n            }\n\n            const direction = index ? -1 : +1;\n\n            return `${direction * (180 - 30) * easing(progress) + rotation}deg`;\n          });\n\n          const layerStyle = {\n            width: size,\n            height: size,\n            transform: [\n              {\n                rotate: timer.interpolate({\n                  inputRange: [0, 1],\n                  outputRange: [`${0 + 30 + 15}deg`, `${2 * 360 + 30 + 15}deg`],\n                }),\n              },\n            ],\n          };\n\n          const viewportStyle = {\n            width: size,\n            height: size,\n            transform: [\n              {\n                translateY: index ? -size / 2 : 0,\n              },\n              {\n                rotate: timer.interpolate({ inputRange, outputRange }),\n              },\n            ],\n          };\n\n          const offsetStyle = index ? { top: size / 2 } : null;\n\n          const lineStyle = {\n            width: size,\n            height: size,\n            borderColor: color,\n            borderWidth: size / 10,\n            borderRadius: size / 2,\n          };\n\n          return (\n            <Animated.View key={index} style={[styles.layer]}>\n              <Animated.View style={layerStyle}>\n                <Animated.View\n                  style={[containerStyle, offsetStyle]}\n                  collapsable={false}\n                >\n                  <Animated.View style={viewportStyle}>\n                    <Animated.View style={containerStyle} collapsable={false}>\n                      <Animated.View style={lineStyle} />\n                    </Animated.View>\n                  </Animated.View>\n                </Animated.View>\n              </Animated.View>\n            </Animated.View>\n          );\n        })}\n      </Animated.View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n\n  layer: {\n    ...StyleSheet.absoluteFillObject,\n\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n});\n\nexport default ActivityIndicator;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAW9B,SAASC,gBAAgB;AA2BzB,IAAMC,QAAQ,GAAG,IAAI;AAkBrB,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAGC,IAAA,EAQb;EAAA,IAAAC,aAAA;EAAA,IAAAC,cAAA,GAALF,IAAA,CAPNG,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,IAAI,GAAAA,cAAA;IACTE,cAAc,GAMfJ,IAAA,CANNK,KAAK;IAAAC,qBAAA,GAMCN,IAAA,CALNO,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,SAAA,GAKjBR,IAAA,CAJNS,IAAI;IAAEC,aAAa,GAAAF,SAAA,cAAG,OAAO,GAAAA,SAAA;IAC7BG,KAAK,GAGCX,IAAA,CAHNW,KAAK;IACEC,cAAc,GAEfZ,IAAA,CAFNa,KAAK;IACFC,IAAA,GAAAC,wBAAA,CACGf,IAAA,EAAAgB,SAAA;EACN,IAAMH,KAAK,GAAGhB,gBAAgB,CAACe,cAAc,CAAC;EAC9C,IAAAK,aAAA,GAA2B1B,KAAK,CAAC2B,MAAM,CACrC,IAAI1B,QAAQ,CAAC2B,KAAK,CAAC,CAAC,CACtB,CAAC;IAFgBC,KAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAAC,cAAA,GAA0B/B,KAAK,CAAC2B,MAAM,CACpC,IAAI1B,QAAQ,CAAC2B,KAAK,CAAC,CAAChB,SAAS,IAAII,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAC3D,CAAC;IAFgBgB,IAAA,GAAAD,cAAA,CAATD,OAAO;EAIf,IAAMG,QAAQ,GAAGjC,KAAK,CAAC2B,MAAM,CAC3BO,SACF,CAAC;EAED,IACeC,KAAA,GACXb,KAAK,CADPc,SAAS,CAAID,KAAA;EAGf,IAAME,aAAa,GAAGrC,KAAK,CAACsC,WAAW,CAAC,YAAM;IAE5CrC,QAAQ,CAACsC,MAAM,CAACP,IAAI,EAAE;MACpBQ,QAAQ,EAAE,GAAG,GAAGL,KAAK;MACrBM,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IAGV,IAAIX,QAAQ,CAACH,OAAO,EAAE;MACpBD,KAAK,CAACgB,QAAQ,CAAC,CAAC,CAAC;MAEjB5C,QAAQ,CAAC6C,IAAI,CAACb,QAAQ,CAACH,OAAO,CAAC,CAACc,KAAK,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,CAACT,KAAK,EAAEH,IAAI,EAAEH,KAAK,CAAC,CAAC;EAExB,IAAMkB,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAId,QAAQ,CAACH,OAAO,EAAE;MACpBG,QAAQ,CAACH,OAAO,CAACkB,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EAEDhD,KAAK,CAACiD,SAAS,CAAC,YAAM;IACpB,IAAIhB,QAAQ,CAACH,OAAO,KAAKI,SAAS,EAAE;MAElCD,QAAQ,CAACH,OAAO,GAAG7B,QAAQ,CAACsC,MAAM,CAACV,KAAK,EAAE;QACxCW,QAAQ,EAAEjC,QAAQ;QAClB2C,MAAM,EAAEhD,MAAM,CAACiD,MAAM;QAErBR,eAAe,EAAExC,QAAQ,CAACiD,EAAE,KAAK,KAAK;QACtCX,OAAO,EAAE,CAAC;QACVC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;IAEA,IAAI9B,SAAS,EAAE;MACbyB,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM,IAAIrB,gBAAgB,EAAE;MAE3Bf,QAAQ,CAACsC,MAAM,CAACP,IAAI,EAAE;QACpBQ,QAAQ,EAAE,GAAG,GAAGL,KAAK;QACrBM,OAAO,EAAE,CAAC;QACVE,eAAe,EAAE,IAAI;QACrBD,aAAa,EAAE;MACjB,CAAC,CAAC,CAACE,KAAK,CAACG,YAAY,CAAC;IACxB,CAAC,MAAM;MACLA,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACnC,SAAS,EAAEoB,IAAI,EAAEhB,gBAAgB,EAAEqB,aAAa,EAAEF,KAAK,EAAEN,KAAK,CAAC,CAAC;EAEpE,IAAMf,KAAK,GAAGD,cAAc,MAAAH,aAAA,GAAIY,KAAK,CAAC+B,MAAM,cAAA3C,aAAA,uBAAZA,aAAA,CAAc4C,OAAO;EACrD,IAAMpC,IAAI,GACR,OAAOC,aAAa,KAAK,QAAQ,GAC7BA,aAAa,KAAK,OAAO,GACvB,EAAE,GACF,EAAE,GACJA,aAAa,GACbA,aAAa,GACb,EAAE;EAER,IAAMoC,MAAM,GAAI,EAAE,GAAGhD,QAAQ,GAAI,IAAI;EACrC,IAAM2C,MAAM,GAAGhD,MAAM,CAACsD,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChD,IAAMC,cAAc,GAAG;IACrBC,KAAK,EAAExC,IAAI;IACXyC,MAAM,EAAEzC,IAAI,GAAG,CAAC;IAChB0C,QAAQ,EAAE;EACZ,CAAC;EAED,OACE5D,KAAA,CAAA6D,aAAA,CAACxD,IAAI,EAAAyD,QAAA;IACH1C,KAAK,EAAE,CAAC2C,MAAM,CAACC,SAAS,EAAE5C,KAAK;EAAE,GAC7BG,IAAI;IACR0C,UAAU;IACVC,iBAAiB,EAAC,aAAa;IAC/BC,kBAAkB,EAAE;MAAEC,IAAI,EAAExD;IAAU;EAAE,IAExCZ,KAAA,CAAA6D,aAAA,CAAC5D,QAAQ,CAACI,IAAI;IACZe,KAAK,EAAE,CAAC;MAAEsC,KAAK,EAAExC,IAAI;MAAEyC,MAAM,EAAEzC,IAAI;MAAEmD,OAAO,EAAErC;IAAK,CAAC,CAAE;IACtDsC,WAAW,EAAE;EAAM,GAElB,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAE,UAAAC,KAAK,EAAK;IAErB,IAAMC,UAAU,GAAGC,KAAK,CAACC,IAAI,CAC3B,IAAID,KAAK,CAACnB,MAAM,CAAC,EACjB,UAACqB,CAAC,EAAEC,UAAU;MAAA,OAAKA,UAAU,IAAItB,MAAM,GAAG,CAAC,CAC7C;IAAA,EAAC;IACD,IAAMuB,WAAW,GAAGJ,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAACnB,MAAM,CAAC,EAAE,UAACqB,CAAC,EAAEC,UAAU,EAAK;MACnE,IAAIE,QAAQ,GAAI,CAAC,GAAGF,UAAU,IAAKtB,MAAM,GAAG,CAAC,CAAC;MAC9C,IAAMtB,QAAQ,GAAGuC,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;MAElD,IAAIO,QAAQ,GAAG,GAAG,EAAE;QAClBA,QAAQ,GAAG,GAAG,GAAGA,QAAQ;MAC3B;MAEA,IAAMC,SAAS,GAAGR,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAEjC,OAAQ,GAAEQ,SAAS,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG9B,MAAM,CAAC6B,QAAQ,CAAC,GAAG9C,QAAS,KAAI;IACrE,CAAC,CAAC;IAEF,IAAMgD,UAAU,GAAG;MACjBvB,KAAK,EAAExC,IAAI;MACXyC,MAAM,EAAEzC,IAAI;MACZgE,SAAS,EAAE,CACT;QACEC,MAAM,EAAEtD,KAAK,CAACuD,WAAW,CAAC;UACxBX,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBK,WAAW,EAAE,CAAE,GAAE,CAAC,GAAG,EAAE,GAAG,EAAG,KAAI,EAAG,GAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAG,KAAI;QAC9D,CAAC;MACH,CAAC;IAEL,CAAC;IAED,IAAMO,aAAa,GAAG;MACpB3B,KAAK,EAAExC,IAAI;MACXyC,MAAM,EAAEzC,IAAI;MACZgE,SAAS,EAAE,CACT;QACEI,UAAU,EAAEd,KAAK,GAAG,CAACtD,IAAI,GAAG,CAAC,GAAG;MAClC,CAAC,EACD;QACEiE,MAAM,EAAEtD,KAAK,CAACuD,WAAW,CAAC;UAAEX,UAAU,EAAVA,UAAU;UAAEK,WAAA,EAAAA;QAAY,CAAC;MACvD,CAAC;IAEL,CAAC;IAED,IAAMS,WAAW,GAAGf,KAAK,GAAG;MAAEgB,GAAG,EAAEtE,IAAI,GAAG;IAAE,CAAC,GAAG,IAAI;IAEpD,IAAMuE,SAAS,GAAG;MAChB/B,KAAK,EAAExC,IAAI;MACXyC,MAAM,EAAEzC,IAAI;MACZwE,WAAW,EAAE5E,KAAK;MAClB6E,WAAW,EAAEzE,IAAI,GAAG,EAAE;MACtB0E,YAAY,EAAE1E,IAAI,GAAG;IACvB,CAAC;IAED,OACElB,KAAA,CAAA6D,aAAA,CAAC5D,QAAQ,CAACI,IAAI;MAACwF,GAAG,EAAErB,KAAM;MAACpD,KAAK,EAAE,CAAC2C,MAAM,CAAC+B,KAAK;IAAE,GAC/C9F,KAAA,CAAA6D,aAAA,CAAC5D,QAAQ,CAACI,IAAI;MAACe,KAAK,EAAE6D;IAAW,GAC/BjF,KAAA,CAAA6D,aAAA,CAAC5D,QAAQ,CAACI,IAAI;MACZe,KAAK,EAAE,CAACqC,cAAc,EAAE8B,WAAW,CAAE;MACrCjB,WAAW,EAAE;IAAM,GAEnBtE,KAAA,CAAA6D,aAAA,CAAC5D,QAAQ,CAACI,IAAI;MAACe,KAAK,EAAEiE;IAAc,GAClCrF,KAAA,CAAA6D,aAAA,CAAC5D,QAAQ,CAACI,IAAI;MAACe,KAAK,EAAEqC,cAAe;MAACa,WAAW,EAAE;IAAM,GACvDtE,KAAA,CAAA6D,aAAA,CAAC5D,QAAQ,CAACI,IAAI;MAACe,KAAK,EAAEqE;IAAU,CAAE,CACrB,CACF,CACF,CACF,CACF,CAAC;EAEpB,CAAC,CACY,CACX,CAAC;AAEX,CAAC;AAED,IAAM1B,MAAM,GAAG3D,UAAU,CAAC2F,MAAM,CAAC;EAC/B/B,SAAS,EAAE;IACTgC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EAEDH,KAAK,EAAAI,aAAA,CAAAA,aAAA,KACA9F,UAAU,CAAC+F,kBAAkB;IAEhCH,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EAAA;AAEhB,CAAC,CAAC;AAEF,eAAezF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}