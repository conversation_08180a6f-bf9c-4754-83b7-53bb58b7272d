{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Card, Text, TextInput, Button, Chip, SegmentedButtons, IconButton, ActivityIndicator, Divider, Dialog, Portal } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { saveConsumption, addConsumptionItem, getMealTypes, getCurrentUser, getAllNutrients } from \"../services/databaseService\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ConsumptionEntry = function ConsumptionEntry(_ref) {\n  var _ref$consumption = _ref.consumption,\n    consumption = _ref$consumption === void 0 ? null : _ref$consumption,\n    onSave = _ref.onSave,\n    onCancel = _ref.onCancel,\n    onSelectFood = _ref.onSelectFood,\n    _ref$selectedFoods = _ref.selectedFoods,\n    selectedFoods = _ref$selectedFoods === void 0 ? [] : _ref$selectedFoods,\n    onRemoveFood = _ref.onRemoveFood,\n    onQuantityChange = _ref.onQuantityChange,\n    date = _ref.date;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    mealTypes = _useState2[0],\n    setMealTypes = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    nutrients = _useState4[0],\n    setNutrients = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    userId = _useState6[0],\n    setUserId = _useState6[1];\n  var _useState7 = useState(false),\n    _useState8 = _slicedToArray(_useState7, 2),\n    isLoading = _useState8[0],\n    setIsLoading = _useState8[1];\n  var _useState9 = useState(false),\n    _useState0 = _slicedToArray(_useState9, 2),\n    showDatePicker = _useState0[0],\n    setShowDatePicker = _useState0[1];\n  var _useState1 = useState(new Date()),\n    _useState10 = _slicedToArray(_useState1, 2),\n    selectedDate = _useState10[0],\n    setSelectedDate = _useState10[1];\n  var _useState11 = useState({\n      id: (consumption == null ? void 0 : consumption.id) || null,\n      user_id: (consumption == null ? void 0 : consumption.user_id) || null,\n      consumption_date: (consumption == null ? void 0 : consumption.consumption_date) || date || new Date().toISOString().split('T')[0],\n      meal_type: (consumption == null ? void 0 : consumption.meal_type) || 'snack',\n      notes: (consumption == null ? void 0 : consumption.notes) || '',\n      items: (consumption == null ? void 0 : consumption.items) || []\n    }),\n    _useState12 = _slicedToArray(_useState11, 2),\n    consumptionData = _useState12[0],\n    setConsumptionData = _useState12[1];\n  useEffect(function () {\n    if (consumptionData.consumption_date) {\n      setSelectedDate(new Date(consumptionData.consumption_date));\n    }\n  }, [consumptionData.consumption_date]);\n  useEffect(function () {\n    var loadData = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        try {\n          setIsLoading(true);\n          var types = yield getMealTypes();\n          setMealTypes(types);\n          var nutrientList = yield getAllNutrients();\n          setNutrients(nutrientList);\n          if (!consumptionData.user_id) {\n            var user = yield getCurrentUser();\n            if (user) {\n              setUserId(user.id);\n              setConsumptionData(function (prev) {\n                return _objectSpread(_objectSpread({}, prev), {}, {\n                  user_id: user.id\n                });\n              });\n            }\n          }\n        } catch (error) {\n          console.error('Error loading data:', error);\n          Alert.alert('Error', 'Failed to load data');\n        } finally {\n          setIsLoading(false);\n        }\n      });\n      return function loadData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    loadData();\n  }, []);\n  useEffect(function () {\n    if (selectedFoods.length > 0) {\n      var newItems = selectedFoods.map(function (food) {\n        return {\n          id: `temp-${food.id}-${Date.now()}`,\n          consumption_id: consumptionData.id,\n          food_id: food.id,\n          food_name: food.name,\n          food: food,\n          quantity: food.quantity || 100,\n          unit: food.unit || 'g'\n        };\n      });\n      setConsumptionData(function (prev) {\n        return _objectSpread(_objectSpread({}, prev), {}, {\n          items: newItems\n        });\n      });\n    }\n  }, [selectedFoods]);\n  var handleMealTypeChange = function handleMealTypeChange(value) {\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        meal_type: value\n      });\n    });\n  };\n  var handleNotesChange = function handleNotesChange(text) {\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        notes: text\n      });\n    });\n  };\n  var handleDateChange = function handleDateChange(newDate) {\n    setSelectedDate(newDate);\n    var formattedDate = newDate.toISOString().split('T')[0];\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        consumption_date: formattedDate\n      });\n    });\n    setShowDatePicker(false);\n  };\n  var renderDatePickerDialog = function renderDatePickerDialog() {\n    var currentYear = new Date().getFullYear();\n    var years = [currentYear - 1, currentYear, currentYear + 1];\n    var months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n    var getDaysInMonth = function getDaysInMonth(year, month) {\n      return new Date(year, month + 1, 0).getDate();\n    };\n    var daysInMonth = getDaysInMonth(selectedDate.getFullYear(), selectedDate.getMonth());\n    var days = Array.from({\n      length: daysInMonth\n    }, function (_, i) {\n      return i + 1;\n    });\n    return _jsx(Portal, {\n      children: _jsxs(Dialog, {\n        visible: showDatePicker,\n        onDismiss: function onDismiss() {\n          return setShowDatePicker(false);\n        },\n        children: [_jsx(Dialog.Title, {\n          children: \"Select Date\"\n        }), _jsx(Dialog.Content, {\n          children: _jsxs(View, {\n            style: styles.datePickerContainer,\n            children: [_jsxs(View, {\n              style: styles.datePickerColumn,\n              children: [_jsx(Text, {\n                style: styles.datePickerLabel,\n                children: \"Year\"\n              }), _jsx(ScrollView, {\n                style: styles.datePickerScroll,\n                children: years.map(function (year) {\n                  return _jsx(Button, {\n                    mode: selectedDate.getFullYear() === year ? 'contained' : 'outlined',\n                    onPress: function onPress() {\n                      var newDate = new Date(selectedDate);\n                      newDate.setFullYear(year);\n                      setSelectedDate(newDate);\n                    },\n                    style: styles.datePickerButton,\n                    children: year\n                  }, year);\n                })\n              })]\n            }), _jsxs(View, {\n              style: styles.datePickerColumn,\n              children: [_jsx(Text, {\n                style: styles.datePickerLabel,\n                children: \"Month\"\n              }), _jsx(ScrollView, {\n                style: styles.datePickerScroll,\n                children: months.map(function (month, index) {\n                  return _jsx(Button, {\n                    mode: selectedDate.getMonth() === index ? 'contained' : 'outlined',\n                    onPress: function onPress() {\n                      var newDate = new Date(selectedDate);\n                      newDate.setMonth(index);\n                      var daysInNewMonth = getDaysInMonth(newDate.getFullYear(), index);\n                      if (newDate.getDate() > daysInNewMonth) {\n                        newDate.setDate(daysInNewMonth);\n                      }\n                      setSelectedDate(newDate);\n                    },\n                    style: styles.datePickerButton,\n                    children: month\n                  }, month);\n                })\n              })]\n            }), _jsxs(View, {\n              style: styles.datePickerColumn,\n              children: [_jsx(Text, {\n                style: styles.datePickerLabel,\n                children: \"Day\"\n              }), _jsx(ScrollView, {\n                style: styles.datePickerScroll,\n                children: days.map(function (day) {\n                  return _jsx(Button, {\n                    mode: selectedDate.getDate() === day ? 'contained' : 'outlined',\n                    onPress: function onPress() {\n                      var newDate = new Date(selectedDate);\n                      newDate.setDate(day);\n                      setSelectedDate(newDate);\n                    },\n                    style: styles.datePickerButton,\n                    children: day\n                  }, day);\n                })\n              })]\n            })]\n          })\n        }), _jsxs(Dialog.Actions, {\n          children: [_jsx(Button, {\n            onPress: function onPress() {\n              return setShowDatePicker(false);\n            },\n            children: \"Cancel\"\n          }), _jsx(Button, {\n            onPress: function onPress() {\n              return handleDateChange(selectedDate);\n            },\n            children: \"Confirm\"\n          })]\n        })]\n      })\n    });\n  };\n  var handleQuantityChange = function handleQuantityChange(itemId, quantity) {\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        items: prev.items.map(function (item) {\n          return item.id === itemId ? _objectSpread(_objectSpread({}, item), {}, {\n            quantity: parseFloat(quantity) || 0\n          }) : item;\n        })\n      });\n    });\n    if (onQuantityChange) {\n      var item = consumptionData.items.find(function (item) {\n        return item.id === itemId;\n      });\n      if (item) {\n        onQuantityChange(item.food_id, parseFloat(quantity) || 0);\n      }\n    }\n  };\n  var handleRemoveItem = function handleRemoveItem(itemId) {\n    var item = consumptionData.items.find(function (item) {\n      return item.id === itemId;\n    });\n    setConsumptionData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        items: prev.items.filter(function (item) {\n          return item.id !== itemId;\n        })\n      });\n    });\n    if (onRemoveFood && item) {\n      onRemoveFood(item.food_id);\n    }\n  };\n  var handleSave = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      try {\n        if (!consumptionData.user_id) {\n          Alert.alert('Error', 'User ID is required');\n          return;\n        }\n        if (consumptionData.items.length === 0) {\n          Alert.alert('Error', 'At least one food item is required');\n          return;\n        }\n        setIsLoading(true);\n        var savedConsumption = yield saveConsumption(consumptionData);\n        for (var item of consumptionData.items) {\n          if (item.id && !item.id.startsWith('temp-')) continue;\n          yield addConsumptionItem(savedConsumption.id, item.food_id, item.quantity, item.unit);\n        }\n        if (onSave) {\n          onSave(savedConsumption);\n        }\n      } catch (error) {\n        console.error('Error saving consumption:', error);\n        Alert.alert('Error', 'Failed to save consumption');\n      } finally {\n        setIsLoading(false);\n      }\n    });\n    return function handleSave() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var calculateNutrition = function calculateNutrition() {\n    var totals = {\n      calories: 0,\n      protein: 0,\n      carbs: 0,\n      fat: 0\n    };\n    consumptionData.items.forEach(function (item) {\n      var food = item.food;\n      if (!food || !food.nutrients) return;\n      var scale = item.quantity / 100;\n      food.nutrients.forEach(function (nutrient) {\n        var name = nutrient.name.toLowerCase();\n        if (name === 'calories' || name === 'energy') {\n          totals.calories += nutrient.amount * scale;\n        } else if (name === 'protein') {\n          totals.protein += nutrient.amount * scale;\n        } else if (name === 'carbohydrates' || name === 'carbs') {\n          totals.carbs += nutrient.amount * scale;\n        } else if (name === 'fat' || name === 'total fat') {\n          totals.fat += nutrient.amount * scale;\n        }\n      });\n    });\n    return {\n      calories: Math.round(totals.calories),\n      protein: Math.round(totals.protein),\n      carbs: Math.round(totals.carbs),\n      fat: Math.round(totals.fat)\n    };\n  };\n  var nutrition = calculateNutrition();\n  var formatDate = function formatDate(dateString) {\n    var options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n  var mealTypeButtons = mealTypes.length > 0 ? mealTypes.map(function (type) {\n    return {\n      value: type.name.toLowerCase(),\n      label: type.name\n    };\n  }) : [{\n    value: 'breakfast',\n    label: 'Breakfast'\n  }, {\n    value: 'lunch',\n    label: 'Lunch'\n  }, {\n    value: 'dinner',\n    label: 'Dinner'\n  }, {\n    value: 'snack',\n    label: 'Snack'\n  }];\n  if (isLoading) {\n    return _jsxs(View, {\n      style: [styles.loadingContainer, {\n        backgroundColor: theme.colors.background\n      }],\n      children: [_jsx(ActivityIndicator, {\n        size: \"large\",\n        color: theme.colors.primary\n      }), _jsx(Text, {\n        style: {\n          marginTop: 16\n        },\n        children: \"Loading...\"\n      })]\n    });\n  }\n  return _jsx(ScrollView, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: _jsxs(Card, {\n      style: styles.card,\n      children: [_jsxs(Card.Content, {\n        children: [_jsx(Button, {\n          mode: \"outlined\",\n          icon: \"calendar\",\n          onPress: function onPress() {\n            return setShowDatePicker(true);\n          },\n          style: styles.dateButton,\n          children: formatDate(consumptionData.consumption_date)\n        }), renderDatePickerDialog(), _jsx(Text, {\n          style: styles.label,\n          children: \"Meal Type\"\n        }), _jsx(SegmentedButtons, {\n          value: consumptionData.meal_type,\n          onValueChange: handleMealTypeChange,\n          buttons: mealTypeButtons,\n          style: styles.mealTypeButtons\n        }), _jsx(Text, {\n          style: styles.label,\n          children: \"Notes\"\n        }), _jsx(TextInput, {\n          mode: \"outlined\",\n          value: consumptionData.notes,\n          onChangeText: handleNotesChange,\n          placeholder: \"Add notes about this meal...\",\n          multiline: true,\n          style: styles.notesInput\n        }), _jsxs(View, {\n          style: styles.nutritionSummary,\n          children: [_jsx(Text, {\n            style: styles.summaryTitle,\n            children: \"Nutrition Summary\"\n          }), _jsxs(View, {\n            style: styles.macrosContainer,\n            children: [_jsxs(Chip, {\n              icon: \"fire\",\n              style: styles.macroChip,\n              children: [nutrition.calories, \" kcal\"]\n            }), _jsxs(Chip, {\n              icon: \"protein\",\n              style: styles.macroChip,\n              children: [nutrition.protein, \"g\"]\n            }), _jsxs(Chip, {\n              icon: \"grain\",\n              style: styles.macroChip,\n              children: [nutrition.carbs, \"g\"]\n            }), _jsxs(Chip, {\n              icon: \"oil\",\n              style: styles.macroChip,\n              children: [nutrition.fat, \"g\"]\n            })]\n          })]\n        }), _jsx(Divider, {\n          style: styles.divider\n        }), _jsxs(View, {\n          style: styles.foodsHeader,\n          children: [_jsx(Text, {\n            style: styles.label,\n            children: \"Food Items\"\n          }), _jsx(Button, {\n            mode: \"text\",\n            icon: \"plus\",\n            onPress: onSelectFood,\n            compact: true,\n            children: \"Add\"\n          })]\n        }), consumptionData.items.length === 0 ? _jsxs(View, {\n          style: styles.emptyContainer,\n          children: [_jsx(Text, {\n            style: styles.emptyText,\n            children: \"No items added yet\"\n          }), _jsx(Button, {\n            mode: \"contained\",\n            icon: \"plus\",\n            onPress: onSelectFood,\n            style: styles.addButton,\n            children: \"Add Food\"\n          })]\n        }) : consumptionData.items.map(function (item) {\n          var _item$food$nutrients$, _item$food$nutrients$2, _item$food$nutrients$3, _item$food$nutrients$4;\n          return _jsxs(View, {\n            style: styles.itemContainer,\n            children: [_jsxs(View, {\n              style: styles.itemHeader,\n              children: [_jsx(Text, {\n                style: styles.itemName,\n                children: item.food_name\n              }), _jsx(IconButton, {\n                icon: \"delete\",\n                size: 20,\n                onPress: function onPress() {\n                  return handleRemoveItem(item.id);\n                }\n              })]\n            }), _jsxs(View, {\n              style: styles.itemDetails,\n              children: [_jsx(TextInput, {\n                mode: \"outlined\",\n                label: \"Quantity\",\n                value: item.quantity.toString(),\n                onChangeText: function onChangeText(text) {\n                  return handleQuantityChange(item.id, text);\n                },\n                keyboardType: \"numeric\",\n                style: styles.quantityInput\n              }), _jsx(Text, {\n                style: styles.unitText,\n                children: item.unit\n              })]\n            }), item.food && item.food.nutrients && _jsxs(View, {\n              style: styles.itemNutrition,\n              children: [_jsxs(Text, {\n                style: styles.nutritionText,\n                children: [Math.round((((_item$food$nutrients$ = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'calories' || n.name.toLowerCase() === 'energy';\n                })) == null ? void 0 : _item$food$nutrients$.amount) || 0) * (item.quantity / 100)), \" kcal\"]\n              }), _jsxs(Text, {\n                style: styles.nutritionText,\n                children: [\"P: \", Math.round((((_item$food$nutrients$2 = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'protein';\n                })) == null ? void 0 : _item$food$nutrients$2.amount) || 0) * (item.quantity / 100)), \"g\"]\n              }), _jsxs(Text, {\n                style: styles.nutritionText,\n                children: [\"C: \", Math.round((((_item$food$nutrients$3 = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'carbohydrates' || n.name.toLowerCase() === 'carbs';\n                })) == null ? void 0 : _item$food$nutrients$3.amount) || 0) * (item.quantity / 100)), \"g\"]\n              }), _jsxs(Text, {\n                style: styles.nutritionText,\n                children: [\"F: \", Math.round((((_item$food$nutrients$4 = item.food.nutrients.find(function (n) {\n                  return n.name.toLowerCase() === 'fat' || n.name.toLowerCase() === 'total fat';\n                })) == null ? void 0 : _item$food$nutrients$4.amount) || 0) * (item.quantity / 100)), \"g\"]\n              })]\n            })]\n          }, item.id);\n        })]\n      }), _jsxs(Card.Actions, {\n        style: styles.actions,\n        children: [_jsx(Button, {\n          onPress: onCancel,\n          disabled: isLoading,\n          children: \"Cancel\"\n        }), _jsx(Button, {\n          mode: \"contained\",\n          onPress: handleSave,\n          loading: isLoading,\n          disabled: isLoading || consumptionData.items.length === 0,\n          children: \"Save\"\n        })]\n      })]\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  card: {\n    margin: 16,\n    elevation: 2\n  },\n  dateButton: {\n    marginBottom: 16,\n    alignSelf: 'center'\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginTop: 16,\n    marginBottom: 8\n  },\n  mealTypeButtons: {\n    marginBottom: 16\n  },\n  notesInput: {\n    marginBottom: 16\n  },\n  nutritionSummary: {\n    marginTop: 16,\n    padding: 16,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)'\n  },\n  summaryTitle: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginBottom: 12,\n    textAlign: 'center'\n  },\n  macrosContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    justifyContent: 'center'\n  },\n  macroChip: {\n    margin: 4\n  },\n  divider: {\n    marginVertical: 16\n  },\n  foodsHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center'\n  },\n  emptyContainer: {\n    alignItems: 'center',\n    padding: 24,\n    marginTop: 8\n  },\n  emptyText: {\n    fontStyle: 'italic',\n    marginBottom: 16\n  },\n  itemContainer: {\n    marginBottom: 16,\n    padding: 12,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)'\n  },\n  itemHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center'\n  },\n  itemName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    flex: 1\n  },\n  itemDetails: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginTop: 8\n  },\n  quantityInput: {\n    width: 100\n  },\n  unitText: {\n    marginLeft: 8,\n    fontSize: 16\n  },\n  itemNutrition: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 8,\n    paddingTop: 8,\n    borderTopWidth: StyleSheet.hairlineWidth,\n    borderTopColor: 'rgba(0, 0, 0, 0.1)'\n  },\n  nutritionText: {\n    fontSize: 12\n  },\n  addButton: {\n    marginVertical: 16\n  },\n  actions: {\n    justifyContent: 'space-between',\n    paddingHorizontal: 16,\n    paddingBottom: 8\n  },\n  datePickerContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between'\n  },\n  datePickerColumn: {\n    flex: 1,\n    marginHorizontal: 4\n  },\n  datePickerLabel: {\n    textAlign: 'center',\n    fontWeight: 'bold',\n    marginBottom: 8\n  },\n  datePickerScroll: {\n    height: 200\n  },\n  datePickerButton: {\n    marginVertical: 4\n  }\n});\nexport default ConsumptionEntry;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "ScrollView", "<PERSON><PERSON>", "Card", "Text", "TextInput", "<PERSON><PERSON>", "Chip", "SegmentedButtons", "IconButton", "ActivityIndicator", "Divider", "Dialog", "Portal", "useTheme", "saveConsumption", "addConsumptionItem", "getMealTypes", "getCurrentUser", "getAllNutrients", "jsx", "_jsx", "jsxs", "_jsxs", "ConsumptionEntry", "_ref", "_ref$consumption", "consumption", "onSave", "onCancel", "onSelectFood", "_ref$selectedFoods", "selectedFoods", "onRemoveFood", "onQuantityChange", "date", "_useTheme", "theme", "_useState", "_useState2", "_slicedToArray", "mealTypes", "setMealTypes", "_useState3", "_useState4", "nutrients", "setNutrients", "_useState5", "_useState6", "userId", "setUserId", "_useState7", "_useState8", "isLoading", "setIsLoading", "_useState9", "_useState0", "showDatePicker", "setShowDatePicker", "_useState1", "Date", "_useState10", "selectedDate", "setSelectedDate", "_useState11", "id", "user_id", "consumption_date", "toISOString", "split", "meal_type", "notes", "items", "_useState12", "consumptionData", "setConsumptionData", "loadData", "_ref2", "_asyncToGenerator", "types", "nutrientList", "user", "prev", "_objectSpread", "error", "console", "alert", "apply", "arguments", "length", "newItems", "map", "food", "now", "consumption_id", "food_id", "food_name", "name", "quantity", "unit", "handleMealTypeChange", "value", "handleNotesChange", "text", "handleDateChange", "newDate", "formattedDate", "renderDatePickerDialog", "currentYear", "getFullYear", "years", "months", "getDaysInMonth", "year", "month", "getDate", "daysInMonth", "getMonth", "days", "Array", "from", "_", "i", "children", "visible", "on<PERSON><PERSON><PERSON>", "Title", "Content", "style", "styles", "datePickerContainer", "datePickerColumn", "datePickerLabel", "datePickerScroll", "mode", "onPress", "setFullYear", "datePickerButton", "index", "setMonth", "daysInNewMonth", "setDate", "day", "Actions", "handleQuantityChange", "itemId", "item", "parseFloat", "find", "handleRemoveItem", "filter", "handleSave", "_ref3", "savedConsumption", "startsWith", "calculateNutrition", "totals", "calories", "protein", "carbs", "fat", "for<PERSON>ach", "scale", "nutrient", "toLowerCase", "amount", "Math", "round", "nutrition", "formatDate", "dateString", "options", "weekday", "toLocaleDateString", "undefined", "mealTypeButtons", "type", "label", "loadingContainer", "backgroundColor", "colors", "background", "size", "color", "primary", "marginTop", "container", "card", "icon", "dateButton", "onValueChange", "buttons", "onChangeText", "placeholder", "multiline", "notesInput", "nutritionSummary", "summaryTitle", "macrosContainer", "macroChip", "divider", "foodsHeader", "compact", "emptyContainer", "emptyText", "addButton", "_item$food$nutrients$", "_item$food$nutrients$2", "_item$food$nutrients$3", "_item$food$nutrients$4", "itemContainer", "itemHeader", "itemName", "itemDetails", "toString", "keyboardType", "quantityInput", "unitText", "itemNutrition", "nutritionText", "n", "actions", "disabled", "loading", "create", "flex", "justifyContent", "alignItems", "margin", "elevation", "marginBottom", "alignSelf", "fontSize", "fontWeight", "padding", "borderRadius", "textAlign", "flexDirection", "flexWrap", "marginVertical", "fontStyle", "width", "marginLeft", "paddingTop", "borderTopWidth", "hairlineWidth", "borderTopColor", "paddingHorizontal", "paddingBottom", "marginHorizontal", "height"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/src/components/ConsumptionEntry.js"], "sourcesContent": ["/**\n * Consumption Entry Component for Znü<PERSON>Zähler\n * Allows users to log food consumption\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, ScrollView, Alert } from 'react-native';\nimport { Card, Text, TextInput, Button, Chip, SegmentedButtons, IconButton, ActivityIndicator, Divider, Dialog, Portal } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { saveConsumption, addConsumptionItem, getMealTypes, getCurrentUser, getAllNutrients } from '../services/databaseService';\n\n/**\n * Consumption Entry Component\n * @param {Object} props - Component props\n * @param {Object} props.consumption - Existing consumption data (optional)\n * @param {Function} props.onSave - Callback function when consumption is saved\n * @param {Function} props.onCancel - Callback function to cancel entry\n * @param {Function} props.onSelectFood - Callback function to select a food\n * @param {Array} props.selectedFoods - Array of selected foods\n * @param {Function} props.onRemoveFood - Callback function to remove a food\n * @param {Function} props.onQuantityChange - Callback function to change quantity\n * @param {string} props.date - Date in YYYY-MM-DD format\n * @returns {JSX.Element} - Consumption entry component\n */\nconst ConsumptionEntry = ({\n  consumption = null,\n  onSave,\n  onCancel,\n  onSelectFood,\n  selectedFoods = [],\n  onRemoveFood,\n  onQuantityChange,\n  date\n}) => {\n  const { theme } = useTheme();\n  const [mealTypes, setMealTypes] = useState([]);\n  const [nutrients, setNutrients] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showDatePicker, setShowDatePicker] = useState(false);\n  const [selectedDate, setSelectedDate] = useState(new Date());\n\n  // Initialize consumption data\n  const [consumptionData, setConsumptionData] = useState({\n    id: consumption?.id || null,\n    user_id: consumption?.user_id || null,\n    consumption_date: consumption?.consumption_date || date || new Date().toISOString().split('T')[0],\n    meal_type: consumption?.meal_type || 'snack',\n    notes: consumption?.notes || '',\n    items: consumption?.items || []\n  });\n\n  // Initialize selected date from consumption date\n  useEffect(() => {\n    if (consumptionData.consumption_date) {\n      setSelectedDate(new Date(consumptionData.consumption_date));\n    }\n  }, [consumptionData.consumption_date]);\n\n  // Load meal types, nutrients, and user\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n\n        // Load meal types\n        const types = await getMealTypes();\n        setMealTypes(types);\n\n        // Load nutrients for calculations\n        const nutrientList = await getAllNutrients();\n        setNutrients(nutrientList);\n\n        // Load current user if not provided\n        if (!consumptionData.user_id) {\n          const user = await getCurrentUser();\n          if (user) {\n            setUserId(user.id);\n            setConsumptionData(prev => ({\n              ...prev,\n              user_id: user.id\n            }));\n          }\n        }\n      } catch (error) {\n        console.error('Error loading data:', error);\n        Alert.alert('Error', 'Failed to load data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Update items when selectedFoods changes\n  useEffect(() => {\n    if (selectedFoods.length > 0) {\n      const newItems = selectedFoods.map(food => ({\n        id: `temp-${food.id}-${Date.now()}`, // Temporary ID until saved\n        consumption_id: consumptionData.id,\n        food_id: food.id,\n        food_name: food.name,\n        food: food,\n        quantity: food.quantity || 100,\n        unit: food.unit || 'g'\n      }));\n\n      setConsumptionData(prev => ({\n        ...prev,\n        items: newItems\n      }));\n    }\n  }, [selectedFoods]);\n\n  // Handle meal type change\n  const handleMealTypeChange = (value) => {\n    setConsumptionData(prev => ({\n      ...prev,\n      meal_type: value\n    }));\n  };\n\n  // Handle notes change\n  const handleNotesChange = (text) => {\n    setConsumptionData(prev => ({\n      ...prev,\n      notes: text\n    }));\n  };\n\n  // Handle date change\n  const handleDateChange = (newDate) => {\n    setSelectedDate(newDate);\n    const formattedDate = newDate.toISOString().split('T')[0];\n    setConsumptionData(prev => ({\n      ...prev,\n      consumption_date: formattedDate\n    }));\n    setShowDatePicker(false);\n  };\n\n  // Create date picker dialog\n  const renderDatePickerDialog = () => {\n    // Generate years (current year - 1 to current year + 1)\n    const currentYear = new Date().getFullYear();\n    const years = [currentYear - 1, currentYear, currentYear + 1];\n\n    // Generate months\n    const months = [\n      'January', 'February', 'March', 'April', 'May', 'June',\n      'July', 'August', 'September', 'October', 'November', 'December'\n    ];\n\n    // Generate days based on selected month and year\n    const getDaysInMonth = (year, month) => {\n      return new Date(year, month + 1, 0).getDate();\n    };\n\n    const daysInMonth = getDaysInMonth(\n      selectedDate.getFullYear(),\n      selectedDate.getMonth()\n    );\n\n    const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);\n\n    return (\n      <Portal>\n        <Dialog visible={showDatePicker} onDismiss={() => setShowDatePicker(false)}>\n          <Dialog.Title>Select Date</Dialog.Title>\n          <Dialog.Content>\n            <View style={styles.datePickerContainer}>\n              {/* Year Picker */}\n              <View style={styles.datePickerColumn}>\n                <Text style={styles.datePickerLabel}>Year</Text>\n                <ScrollView style={styles.datePickerScroll}>\n                  {years.map(year => (\n                    <Button\n                      key={year}\n                      mode={selectedDate.getFullYear() === year ? 'contained' : 'outlined'}\n                      onPress={() => {\n                        const newDate = new Date(selectedDate);\n                        newDate.setFullYear(year);\n                        setSelectedDate(newDate);\n                      }}\n                      style={styles.datePickerButton}\n                    >\n                      {year}\n                    </Button>\n                  ))}\n                </ScrollView>\n              </View>\n\n              {/* Month Picker */}\n              <View style={styles.datePickerColumn}>\n                <Text style={styles.datePickerLabel}>Month</Text>\n                <ScrollView style={styles.datePickerScroll}>\n                  {months.map((month, index) => (\n                    <Button\n                      key={month}\n                      mode={selectedDate.getMonth() === index ? 'contained' : 'outlined'}\n                      onPress={() => {\n                        const newDate = new Date(selectedDate);\n                        newDate.setMonth(index);\n\n                        // Adjust day if necessary (e.g., if we're switching from a month with 31 days to one with 30)\n                        const daysInNewMonth = getDaysInMonth(newDate.getFullYear(), index);\n                        if (newDate.getDate() > daysInNewMonth) {\n                          newDate.setDate(daysInNewMonth);\n                        }\n\n                        setSelectedDate(newDate);\n                      }}\n                      style={styles.datePickerButton}\n                    >\n                      {month}\n                    </Button>\n                  ))}\n                </ScrollView>\n              </View>\n\n              {/* Day Picker */}\n              <View style={styles.datePickerColumn}>\n                <Text style={styles.datePickerLabel}>Day</Text>\n                <ScrollView style={styles.datePickerScroll}>\n                  {days.map(day => (\n                    <Button\n                      key={day}\n                      mode={selectedDate.getDate() === day ? 'contained' : 'outlined'}\n                      onPress={() => {\n                        const newDate = new Date(selectedDate);\n                        newDate.setDate(day);\n                        setSelectedDate(newDate);\n                      }}\n                      style={styles.datePickerButton}\n                    >\n                      {day}\n                    </Button>\n                  ))}\n                </ScrollView>\n              </View>\n            </View>\n          </Dialog.Content>\n          <Dialog.Actions>\n            <Button onPress={() => setShowDatePicker(false)}>Cancel</Button>\n            <Button onPress={() => handleDateChange(selectedDate)}>Confirm</Button>\n          </Dialog.Actions>\n        </Dialog>\n      </Portal>\n    );\n  };\n\n  // Handle quantity change for an item\n  const handleQuantityChange = (itemId, quantity) => {\n    setConsumptionData(prev => ({\n      ...prev,\n      items: prev.items.map(item =>\n        item.id === itemId ? { ...item, quantity: parseFloat(quantity) || 0 } : item\n      )\n    }));\n\n    // Call parent callback if provided\n    if (onQuantityChange) {\n      const item = consumptionData.items.find(item => item.id === itemId);\n      if (item) {\n        onQuantityChange(item.food_id, parseFloat(quantity) || 0);\n      }\n    }\n  };\n\n  // Handle remove item\n  const handleRemoveItem = (itemId) => {\n    const item = consumptionData.items.find(item => item.id === itemId);\n\n    setConsumptionData(prev => ({\n      ...prev,\n      items: prev.items.filter(item => item.id !== itemId)\n    }));\n\n    // Call parent callback if provided\n    if (onRemoveFood && item) {\n      onRemoveFood(item.food_id);\n    }\n  };\n\n  // Handle save\n  const handleSave = async () => {\n    try {\n      if (!consumptionData.user_id) {\n        Alert.alert('Error', 'User ID is required');\n        return;\n      }\n\n      if (consumptionData.items.length === 0) {\n        Alert.alert('Error', 'At least one food item is required');\n        return;\n      }\n\n      setIsLoading(true);\n\n      // Save consumption\n      const savedConsumption = await saveConsumption(consumptionData);\n\n      // Save items\n      for (const item of consumptionData.items) {\n        // Skip items that already have a valid ID (not temporary)\n        if (item.id && !item.id.startsWith('temp-')) continue;\n\n        await addConsumptionItem(\n          savedConsumption.id,\n          item.food_id,\n          item.quantity,\n          item.unit\n        );\n      }\n\n      // Call onSave callback\n      if (onSave) {\n        onSave(savedConsumption);\n      }\n    } catch (error) {\n      console.error('Error saving consumption:', error);\n      Alert.alert('Error', 'Failed to save consumption');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Calculate nutrition totals\n  const calculateNutrition = () => {\n    const totals = {\n      calories: 0,\n      protein: 0,\n      carbs: 0,\n      fat: 0\n    };\n\n    consumptionData.items.forEach(item => {\n      const food = item.food;\n      if (!food || !food.nutrients) return;\n\n      const scale = item.quantity / 100; // Assuming nutrients are per 100g\n\n      food.nutrients.forEach(nutrient => {\n        const name = nutrient.name.toLowerCase();\n        if (name === 'calories' || name === 'energy') {\n          totals.calories += (nutrient.amount * scale);\n        } else if (name === 'protein') {\n          totals.protein += (nutrient.amount * scale);\n        } else if (name === 'carbohydrates' || name === 'carbs') {\n          totals.carbs += (nutrient.amount * scale);\n        } else if (name === 'fat' || name === 'total fat') {\n          totals.fat += (nutrient.amount * scale);\n        }\n      });\n    });\n\n    return {\n      calories: Math.round(totals.calories),\n      protein: Math.round(totals.protein),\n      carbs: Math.round(totals.carbs),\n      fat: Math.round(totals.fat)\n    };\n  };\n\n  const nutrition = calculateNutrition();\n\n  // Format date for display\n  const formatDate = (dateString) => {\n    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n\n  // Generate meal type buttons\n  const mealTypeButtons = mealTypes.length > 0\n    ? mealTypes.map(type => ({\n        value: type.name.toLowerCase(),\n        label: type.name\n      }))\n    : [\n        { value: 'breakfast', label: 'Breakfast' },\n        { value: 'lunch', label: 'Lunch' },\n        { value: 'dinner', label: 'Dinner' },\n        { value: 'snack', label: 'Snack' }\n      ];\n\n  if (isLoading) {\n    return (\n      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>\n        <ActivityIndicator size=\"large\" color={theme.colors.primary} />\n        <Text style={{ marginTop: 16 }}>Loading...</Text>\n      </View>\n    );\n  }\n\n  return (\n    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <Card style={styles.card}>\n        <Card.Content>\n          <Button\n            mode=\"outlined\"\n            icon=\"calendar\"\n            onPress={() => setShowDatePicker(true)}\n            style={styles.dateButton}\n          >\n            {formatDate(consumptionData.consumption_date)}\n          </Button>\n\n          {renderDatePickerDialog()}\n\n          <Text style={styles.label}>Meal Type</Text>\n          <SegmentedButtons\n            value={consumptionData.meal_type}\n            onValueChange={handleMealTypeChange}\n            buttons={mealTypeButtons}\n            style={styles.mealTypeButtons}\n          />\n\n          <Text style={styles.label}>Notes</Text>\n          <TextInput\n            mode=\"outlined\"\n            value={consumptionData.notes}\n            onChangeText={handleNotesChange}\n            placeholder=\"Add notes about this meal...\"\n            multiline\n            style={styles.notesInput}\n          />\n\n          <View style={styles.nutritionSummary}>\n            <Text style={styles.summaryTitle}>Nutrition Summary</Text>\n            <View style={styles.macrosContainer}>\n              <Chip icon=\"fire\" style={styles.macroChip}>\n                {nutrition.calories} kcal\n              </Chip>\n              <Chip icon=\"protein\" style={styles.macroChip}>\n                {nutrition.protein}g\n              </Chip>\n              <Chip icon=\"grain\" style={styles.macroChip}>\n                {nutrition.carbs}g\n              </Chip>\n              <Chip icon=\"oil\" style={styles.macroChip}>\n                {nutrition.fat}g\n              </Chip>\n            </View>\n          </View>\n\n          <Divider style={styles.divider} />\n\n          <View style={styles.foodsHeader}>\n            <Text style={styles.label}>Food Items</Text>\n            <Button\n              mode=\"text\"\n              icon=\"plus\"\n              onPress={onSelectFood}\n              compact\n            >\n              Add\n            </Button>\n          </View>\n\n          {consumptionData.items.length === 0 ? (\n            <View style={styles.emptyContainer}>\n              <Text style={styles.emptyText}>No items added yet</Text>\n              <Button\n                mode=\"contained\"\n                icon=\"plus\"\n                onPress={onSelectFood}\n                style={styles.addButton}\n              >\n                Add Food\n              </Button>\n            </View>\n          ) : (\n            consumptionData.items.map((item) => (\n              <View key={item.id} style={styles.itemContainer}>\n                <View style={styles.itemHeader}>\n                  <Text style={styles.itemName}>{item.food_name}</Text>\n                  <IconButton\n                    icon=\"delete\"\n                    size={20}\n                    onPress={() => handleRemoveItem(item.id)}\n                  />\n                </View>\n                <View style={styles.itemDetails}>\n                  <TextInput\n                    mode=\"outlined\"\n                    label=\"Quantity\"\n                    value={item.quantity.toString()}\n                    onChangeText={(text) => handleQuantityChange(item.id, text)}\n                    keyboardType=\"numeric\"\n                    style={styles.quantityInput}\n                  />\n                  <Text style={styles.unitText}>{item.unit}</Text>\n                </View>\n                {item.food && item.food.nutrients && (\n                  <View style={styles.itemNutrition}>\n                    <Text style={styles.nutritionText}>\n                      {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'calories' ||\n                        n.name.toLowerCase() === 'energy'\n                      )?.amount || 0) * (item.quantity / 100))} kcal\n                    </Text>\n                    <Text style={styles.nutritionText}>\n                      P: {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'protein'\n                      )?.amount || 0) * (item.quantity / 100))}g\n                    </Text>\n                    <Text style={styles.nutritionText}>\n                      C: {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'carbohydrates' ||\n                        n.name.toLowerCase() === 'carbs'\n                      )?.amount || 0) * (item.quantity / 100))}g\n                    </Text>\n                    <Text style={styles.nutritionText}>\n                      F: {Math.round((item.food.nutrients.find(n =>\n                        n.name.toLowerCase() === 'fat' ||\n                        n.name.toLowerCase() === 'total fat'\n                      )?.amount || 0) * (item.quantity / 100))}g\n                    </Text>\n                  </View>\n                )}\n              </View>\n            ))\n          )}\n        </Card.Content>\n\n        <Card.Actions style={styles.actions}>\n          <Button\n            onPress={onCancel}\n            disabled={isLoading}\n          >\n            Cancel\n          </Button>\n          <Button\n            mode=\"contained\"\n            onPress={handleSave}\n            loading={isLoading}\n            disabled={isLoading || consumptionData.items.length === 0}\n          >\n            Save\n          </Button>\n        </Card.Actions>\n      </Card>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  card: {\n    margin: 16,\n    elevation: 2,\n  },\n  dateButton: {\n    marginBottom: 16,\n    alignSelf: 'center',\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginTop: 16,\n    marginBottom: 8,\n  },\n  mealTypeButtons: {\n    marginBottom: 16,\n  },\n  notesInput: {\n    marginBottom: 16,\n  },\n  nutritionSummary: {\n    marginTop: 16,\n    padding: 16,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)',\n  },\n  summaryTitle: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginBottom: 12,\n    textAlign: 'center',\n  },\n  macrosContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    justifyContent: 'center',\n  },\n  macroChip: {\n    margin: 4,\n  },\n  divider: {\n    marginVertical: 16,\n  },\n  foodsHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n  },\n  emptyContainer: {\n    alignItems: 'center',\n    padding: 24,\n    marginTop: 8,\n  },\n  emptyText: {\n    fontStyle: 'italic',\n    marginBottom: 16,\n  },\n  itemContainer: {\n    marginBottom: 16,\n    padding: 12,\n    borderRadius: 8,\n    backgroundColor: 'rgba(0, 0, 0, 0.05)',\n  },\n  itemHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n  },\n  itemName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    flex: 1,\n  },\n  itemDetails: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginTop: 8,\n  },\n  quantityInput: {\n    width: 100,\n  },\n  unitText: {\n    marginLeft: 8,\n    fontSize: 16,\n  },\n  itemNutrition: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 8,\n    paddingTop: 8,\n    borderTopWidth: StyleSheet.hairlineWidth,\n    borderTopColor: 'rgba(0, 0, 0, 0.1)',\n  },\n  nutritionText: {\n    fontSize: 12,\n  },\n  addButton: {\n    marginVertical: 16,\n  },\n  actions: {\n    justifyContent: 'space-between',\n    paddingHorizontal: 16,\n    paddingBottom: 8,\n  },\n  datePickerContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n  datePickerColumn: {\n    flex: 1,\n    marginHorizontal: 4,\n  },\n  datePickerLabel: {\n    textAlign: 'center',\n    fontWeight: 'bold',\n    marginBottom: 8,\n  },\n  datePickerScroll: {\n    height: 200,\n  },\n  datePickerButton: {\n    marginVertical: 4,\n  },\n});\n\nexport default ConsumptionEntry;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAEnD,SAASC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,QAAQ,oBAAoB;AAClJ,SAASC,QAAQ;AACjB,SAASC,eAAe,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe;AAAsC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAejI,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,IAAA,EAShB;EAAA,IAAAC,gBAAA,GAAAD,IAAA,CARJE,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,IAAI,GAAAA,gBAAA;IAClBE,MAAM,GAAAH,IAAA,CAANG,MAAM;IACNC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IACRC,YAAY,GAAAL,IAAA,CAAZK,YAAY;IAAAC,kBAAA,GAAAN,IAAA,CACZO,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,EAAE,GAAAA,kBAAA;IAClBE,YAAY,GAAAR,IAAA,CAAZQ,YAAY;IACZC,gBAAgB,GAAAT,IAAA,CAAhBS,gBAAgB;IAChBC,IAAI,GAAAV,IAAA,CAAJU,IAAI;EAEJ,IAAAC,SAAA,GAAkBtB,QAAQ,CAAC,CAAC;IAApBuB,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,SAAA,GAAkCzC,QAAQ,CAAC,EAAE,CAAC;IAAA0C,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAvCG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAkC9C,QAAQ,CAAC,EAAE,CAAC;IAAA+C,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAvCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4BlD,QAAQ,CAAC,IAAI,CAAC;IAAAmD,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAnCE,MAAM,GAAAD,UAAA;IAAEE,SAAS,GAAAF,UAAA;EACxB,IAAAG,UAAA,GAAkCtD,QAAQ,CAAC,KAAK,CAAC;IAAAuD,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4C1D,QAAQ,CAAC,KAAK,CAAC;IAAA2D,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAApDE,cAAc,GAAAD,UAAA;IAAEE,iBAAiB,GAAAF,UAAA;EACxC,IAAAG,UAAA,GAAwC9D,QAAQ,CAAC,IAAI+D,IAAI,CAAC,CAAC,CAAC;IAAAC,WAAA,GAAArB,cAAA,CAAAmB,UAAA;IAArDG,YAAY,GAAAD,WAAA;IAAEE,eAAe,GAAAF,WAAA;EAGpC,IAAAG,WAAA,GAA8CnE,QAAQ,CAAC;MACrDoE,EAAE,EAAE,CAAAtC,WAAW,oBAAXA,WAAW,CAAEsC,EAAE,KAAI,IAAI;MAC3BC,OAAO,EAAE,CAAAvC,WAAW,oBAAXA,WAAW,CAAEuC,OAAO,KAAI,IAAI;MACrCC,gBAAgB,EAAE,CAAAxC,WAAW,oBAAXA,WAAW,CAAEwC,gBAAgB,KAAIhC,IAAI,IAAI,IAAIyB,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjGC,SAAS,EAAE,CAAA3C,WAAW,oBAAXA,WAAW,CAAE2C,SAAS,KAAI,OAAO;MAC5CC,KAAK,EAAE,CAAA5C,WAAW,oBAAXA,WAAW,CAAE4C,KAAK,KAAI,EAAE;MAC/BC,KAAK,EAAE,CAAA7C,WAAW,oBAAXA,WAAW,CAAE6C,KAAK,KAAI;IAC/B,CAAC,CAAC;IAAAC,WAAA,GAAAjC,cAAA,CAAAwB,WAAA;IAPKU,eAAe,GAAAD,WAAA;IAAEE,kBAAkB,GAAAF,WAAA;EAU1C3E,SAAS,CAAC,YAAM;IACd,IAAI4E,eAAe,CAACP,gBAAgB,EAAE;MACpCJ,eAAe,CAAC,IAAIH,IAAI,CAACc,eAAe,CAACP,gBAAgB,CAAC,CAAC;IAC7D;EACF,CAAC,EAAE,CAACO,eAAe,CAACP,gBAAgB,CAAC,CAAC;EAGtCrE,SAAS,CAAC,YAAM;IACd,IAAM8E,QAAQ;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAC3B,IAAI;UACFxB,YAAY,CAAC,IAAI,CAAC;UAGlB,IAAMyB,KAAK,SAAS9D,YAAY,CAAC,CAAC;UAClCyB,YAAY,CAACqC,KAAK,CAAC;UAGnB,IAAMC,YAAY,SAAS7D,eAAe,CAAC,CAAC;UAC5C2B,YAAY,CAACkC,YAAY,CAAC;UAG1B,IAAI,CAACN,eAAe,CAACR,OAAO,EAAE;YAC5B,IAAMe,IAAI,SAAS/D,cAAc,CAAC,CAAC;YACnC,IAAI+D,IAAI,EAAE;cACR/B,SAAS,CAAC+B,IAAI,CAAChB,EAAE,CAAC;cAClBU,kBAAkB,CAAC,UAAAO,IAAI;gBAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;kBACPhB,OAAO,EAAEe,IAAI,CAAChB;gBAAE;cAAA,CAChB,CAAC;YACL;UACF;QACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC3ClF,KAAK,CAACoF,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC;QAC7C,CAAC,SAAS;UACRhC,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;MAAA,gBA7BKsB,QAAQA,CAAA;QAAA,OAAAC,KAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;IAAA,GA6Bb;IAEDZ,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAGN9E,SAAS,CAAC,YAAM;IACd,IAAIkC,aAAa,CAACyD,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAMC,QAAQ,GAAG1D,aAAa,CAAC2D,GAAG,CAAC,UAAAC,IAAI;QAAA,OAAK;UAC1C3B,EAAE,EAAE,QAAQ2B,IAAI,CAAC3B,EAAE,IAAIL,IAAI,CAACiC,GAAG,CAAC,CAAC,EAAE;UACnCC,cAAc,EAAEpB,eAAe,CAACT,EAAE;UAClC8B,OAAO,EAAEH,IAAI,CAAC3B,EAAE;UAChB+B,SAAS,EAAEJ,IAAI,CAACK,IAAI;UACpBL,IAAI,EAAEA,IAAI;UACVM,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,GAAG;UAC9BC,IAAI,EAAEP,IAAI,CAACO,IAAI,IAAI;QACrB,CAAC;MAAA,CAAC,CAAC;MAEHxB,kBAAkB,CAAC,UAAAO,IAAI;QAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;UACPV,KAAK,EAAEkB;QAAQ;MAAA,CACf,CAAC;IACL;EACF,CAAC,EAAE,CAAC1D,aAAa,CAAC,CAAC;EAGnB,IAAMoE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,KAAK,EAAK;IACtC1B,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPZ,SAAS,EAAE+B;MAAK;IAAA,CAChB,CAAC;EACL,CAAC;EAGD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,IAAI,EAAK;IAClC5B,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPX,KAAK,EAAEgC;MAAI;IAAA,CACX,CAAC;EACL,CAAC;EAGD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,OAAO,EAAK;IACpC1C,eAAe,CAAC0C,OAAO,CAAC;IACxB,IAAMC,aAAa,GAAGD,OAAO,CAACrC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzDM,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPf,gBAAgB,EAAEuC;MAAa;IAAA,CAC/B,CAAC;IACHhD,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAGD,IAAMiD,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;IAEnC,IAAMC,WAAW,GAAG,IAAIhD,IAAI,CAAC,CAAC,CAACiD,WAAW,CAAC,CAAC;IAC5C,IAAMC,KAAK,GAAG,CAACF,WAAW,GAAG,CAAC,EAAEA,WAAW,EAAEA,WAAW,GAAG,CAAC,CAAC;IAG7D,IAAMG,MAAM,GAAG,CACb,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAGD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAI,EAAEC,KAAK,EAAK;MACtC,OAAO,IAAItD,IAAI,CAACqD,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,IAAMC,WAAW,GAAGJ,cAAc,CAChClD,YAAY,CAAC+C,WAAW,CAAC,CAAC,EAC1B/C,YAAY,CAACuD,QAAQ,CAAC,CACxB,CAAC;IAED,IAAMC,IAAI,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAE/B,MAAM,EAAE2B;IAAY,CAAC,EAAE,UAACK,CAAC,EAAEC,CAAC;MAAA,OAAKA,CAAC,GAAG,CAAC;IAAA,EAAC;IAEjE,OACErG,IAAA,CAACR,MAAM;MAAA8G,QAAA,EACLpG,KAAA,CAACX,MAAM;QAACgH,OAAO,EAAEnE,cAAe;QAACoE,SAAS,EAAE,SAAXA,SAASA,CAAA;UAAA,OAAQnE,iBAAiB,CAAC,KAAK,CAAC;QAAA,CAAC;QAAAiE,QAAA,GACzEtG,IAAA,CAACT,MAAM,CAACkH,KAAK;UAAAH,QAAA,EAAC;QAAW,CAAc,CAAC,EACxCtG,IAAA,CAACT,MAAM,CAACmH,OAAO;UAAAJ,QAAA,EACbpG,KAAA,CAACvB,IAAI;YAACgI,KAAK,EAAEC,MAAM,CAACC,mBAAoB;YAAAP,QAAA,GAEtCpG,KAAA,CAACvB,IAAI;cAACgI,KAAK,EAAEC,MAAM,CAACE,gBAAiB;cAAAR,QAAA,GACnCtG,IAAA,CAACjB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAACG,eAAgB;gBAAAT,QAAA,EAAC;cAAI,CAAM,CAAC,EAChDtG,IAAA,CAACpB,UAAU;gBAAC+H,KAAK,EAAEC,MAAM,CAACI,gBAAiB;gBAAAV,QAAA,EACxCb,KAAK,CAACnB,GAAG,CAAC,UAAAsB,IAAI;kBAAA,OACb5F,IAAA,CAACf,MAAM;oBAELgI,IAAI,EAAExE,YAAY,CAAC+C,WAAW,CAAC,CAAC,KAAKI,IAAI,GAAG,WAAW,GAAG,UAAW;oBACrEsB,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;sBACb,IAAM9B,OAAO,GAAG,IAAI7C,IAAI,CAACE,YAAY,CAAC;sBACtC2C,OAAO,CAAC+B,WAAW,CAACvB,IAAI,CAAC;sBACzBlD,eAAe,CAAC0C,OAAO,CAAC;oBAC1B,CAAE;oBACFuB,KAAK,EAAEC,MAAM,CAACQ,gBAAiB;oBAAAd,QAAA,EAE9BV;kBAAI,GATAA,IAUC,CAAC;gBAAA,CACV;cAAC,CACQ,CAAC;YAAA,CACT,CAAC,EAGP1F,KAAA,CAACvB,IAAI;cAACgI,KAAK,EAAEC,MAAM,CAACE,gBAAiB;cAAAR,QAAA,GACnCtG,IAAA,CAACjB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAACG,eAAgB;gBAAAT,QAAA,EAAC;cAAK,CAAM,CAAC,EACjDtG,IAAA,CAACpB,UAAU;gBAAC+H,KAAK,EAAEC,MAAM,CAACI,gBAAiB;gBAAAV,QAAA,EACxCZ,MAAM,CAACpB,GAAG,CAAC,UAACuB,KAAK,EAAEwB,KAAK;kBAAA,OACvBrH,IAAA,CAACf,MAAM;oBAELgI,IAAI,EAAExE,YAAY,CAACuD,QAAQ,CAAC,CAAC,KAAKqB,KAAK,GAAG,WAAW,GAAG,UAAW;oBACnEH,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;sBACb,IAAM9B,OAAO,GAAG,IAAI7C,IAAI,CAACE,YAAY,CAAC;sBACtC2C,OAAO,CAACkC,QAAQ,CAACD,KAAK,CAAC;sBAGvB,IAAME,cAAc,GAAG5B,cAAc,CAACP,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE6B,KAAK,CAAC;sBACnE,IAAIjC,OAAO,CAACU,OAAO,CAAC,CAAC,GAAGyB,cAAc,EAAE;wBACtCnC,OAAO,CAACoC,OAAO,CAACD,cAAc,CAAC;sBACjC;sBAEA7E,eAAe,CAAC0C,OAAO,CAAC;oBAC1B,CAAE;oBACFuB,KAAK,EAAEC,MAAM,CAACQ,gBAAiB;oBAAAd,QAAA,EAE9BT;kBAAK,GAhBDA,KAiBC,CAAC;gBAAA,CACV;cAAC,CACQ,CAAC;YAAA,CACT,CAAC,EAGP3F,KAAA,CAACvB,IAAI;cAACgI,KAAK,EAAEC,MAAM,CAACE,gBAAiB;cAAAR,QAAA,GACnCtG,IAAA,CAACjB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAACG,eAAgB;gBAAAT,QAAA,EAAC;cAAG,CAAM,CAAC,EAC/CtG,IAAA,CAACpB,UAAU;gBAAC+H,KAAK,EAAEC,MAAM,CAACI,gBAAiB;gBAAAV,QAAA,EACxCL,IAAI,CAAC3B,GAAG,CAAC,UAAAmD,GAAG;kBAAA,OACXzH,IAAA,CAACf,MAAM;oBAELgI,IAAI,EAAExE,YAAY,CAACqD,OAAO,CAAC,CAAC,KAAK2B,GAAG,GAAG,WAAW,GAAG,UAAW;oBAChEP,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;sBACb,IAAM9B,OAAO,GAAG,IAAI7C,IAAI,CAACE,YAAY,CAAC;sBACtC2C,OAAO,CAACoC,OAAO,CAACC,GAAG,CAAC;sBACpB/E,eAAe,CAAC0C,OAAO,CAAC;oBAC1B,CAAE;oBACFuB,KAAK,EAAEC,MAAM,CAACQ,gBAAiB;oBAAAd,QAAA,EAE9BmB;kBAAG,GATCA,GAUC,CAAC;gBAAA,CACV;cAAC,CACQ,CAAC;YAAA,CACT,CAAC;UAAA,CACH;QAAC,CACO,CAAC,EACjBvH,KAAA,CAACX,MAAM,CAACmI,OAAO;UAAApB,QAAA,GACbtG,IAAA,CAACf,MAAM;YAACiI,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ7E,iBAAiB,CAAC,KAAK,CAAC;YAAA,CAAC;YAAAiE,QAAA,EAAC;UAAM,CAAQ,CAAC,EAChEtG,IAAA,CAACf,MAAM;YAACiI,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ/B,gBAAgB,CAAC1C,YAAY,CAAC;YAAA,CAAC;YAAA6D,QAAA,EAAC;UAAO,CAAQ,CAAC;QAAA,CACzD,CAAC;MAAA,CACX;IAAC,CACH,CAAC;EAEb,CAAC;EAGD,IAAMqB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,MAAM,EAAE/C,QAAQ,EAAK;IACjDvB,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPV,KAAK,EAAEU,IAAI,CAACV,KAAK,CAACmB,GAAG,CAAC,UAAAuD,IAAI;UAAA,OACxBA,IAAI,CAACjF,EAAE,KAAKgF,MAAM,GAAA9D,aAAA,CAAAA,aAAA,KAAQ+D,IAAI;YAAEhD,QAAQ,EAAEiD,UAAU,CAACjD,QAAQ,CAAC,IAAI;UAAC,KAAKgD,IAAI;QAAA,CAC9E;MAAC;IAAA,CACD,CAAC;IAGH,IAAIhH,gBAAgB,EAAE;MACpB,IAAMgH,IAAI,GAAGxE,eAAe,CAACF,KAAK,CAAC4E,IAAI,CAAC,UAAAF,IAAI;QAAA,OAAIA,IAAI,CAACjF,EAAE,KAAKgF,MAAM;MAAA,EAAC;MACnE,IAAIC,IAAI,EAAE;QACRhH,gBAAgB,CAACgH,IAAI,CAACnD,OAAO,EAAEoD,UAAU,CAACjD,QAAQ,CAAC,IAAI,CAAC,CAAC;MAC3D;IACF;EACF,CAAC;EAGD,IAAMmD,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIJ,MAAM,EAAK;IACnC,IAAMC,IAAI,GAAGxE,eAAe,CAACF,KAAK,CAAC4E,IAAI,CAAC,UAAAF,IAAI;MAAA,OAAIA,IAAI,CAACjF,EAAE,KAAKgF,MAAM;IAAA,EAAC;IAEnEtE,kBAAkB,CAAC,UAAAO,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAClBD,IAAI;QACPV,KAAK,EAAEU,IAAI,CAACV,KAAK,CAAC8E,MAAM,CAAC,UAAAJ,IAAI;UAAA,OAAIA,IAAI,CAACjF,EAAE,KAAKgF,MAAM;QAAA;MAAC;IAAA,CACpD,CAAC;IAGH,IAAIhH,YAAY,IAAIiH,IAAI,EAAE;MACxBjH,YAAY,CAACiH,IAAI,CAACnD,OAAO,CAAC;IAC5B;EACF,CAAC;EAGD,IAAMwD,UAAU;IAAA,IAAAC,KAAA,GAAA1E,iBAAA,CAAG,aAAY;MAC7B,IAAI;QACF,IAAI,CAACJ,eAAe,CAACR,OAAO,EAAE;UAC5BhE,KAAK,CAACoF,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC;UAC3C;QACF;QAEA,IAAIZ,eAAe,CAACF,KAAK,CAACiB,MAAM,KAAK,CAAC,EAAE;UACtCvF,KAAK,CAACoF,KAAK,CAAC,OAAO,EAAE,oCAAoC,CAAC;UAC1D;QACF;QAEAhC,YAAY,CAAC,IAAI,CAAC;QAGlB,IAAMmG,gBAAgB,SAAS1I,eAAe,CAAC2D,eAAe,CAAC;QAG/D,KAAK,IAAMwE,IAAI,IAAIxE,eAAe,CAACF,KAAK,EAAE;UAExC,IAAI0E,IAAI,CAACjF,EAAE,IAAI,CAACiF,IAAI,CAACjF,EAAE,CAACyF,UAAU,CAAC,OAAO,CAAC,EAAE;UAE7C,MAAM1I,kBAAkB,CACtByI,gBAAgB,CAACxF,EAAE,EACnBiF,IAAI,CAACnD,OAAO,EACZmD,IAAI,CAAChD,QAAQ,EACbgD,IAAI,CAAC/C,IACP,CAAC;QACH;QAGA,IAAIvE,MAAM,EAAE;UACVA,MAAM,CAAC6H,gBAAgB,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOrE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDlF,KAAK,CAACoF,KAAK,CAAC,OAAO,EAAE,4BAA4B,CAAC;MACpD,CAAC,SAAS;QACRhC,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBAxCKiG,UAAUA,CAAA;MAAA,OAAAC,KAAA,CAAAjE,KAAA,OAAAC,SAAA;IAAA;EAAA,GAwCf;EAGD,IAAMmE,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;IAC/B,IAAMC,MAAM,GAAG;MACbC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;IACP,CAAC;IAEDtF,eAAe,CAACF,KAAK,CAACyF,OAAO,CAAC,UAAAf,IAAI,EAAI;MACpC,IAAMtD,IAAI,GAAGsD,IAAI,CAACtD,IAAI;MACtB,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC/C,SAAS,EAAE;MAE9B,IAAMqH,KAAK,GAAGhB,IAAI,CAAChD,QAAQ,GAAG,GAAG;MAEjCN,IAAI,CAAC/C,SAAS,CAACoH,OAAO,CAAC,UAAAE,QAAQ,EAAI;QACjC,IAAMlE,IAAI,GAAGkE,QAAQ,CAAClE,IAAI,CAACmE,WAAW,CAAC,CAAC;QACxC,IAAInE,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,QAAQ,EAAE;UAC5C2D,MAAM,CAACC,QAAQ,IAAKM,QAAQ,CAACE,MAAM,GAAGH,KAAM;QAC9C,CAAC,MAAM,IAAIjE,IAAI,KAAK,SAAS,EAAE;UAC7B2D,MAAM,CAACE,OAAO,IAAKK,QAAQ,CAACE,MAAM,GAAGH,KAAM;QAC7C,CAAC,MAAM,IAAIjE,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,OAAO,EAAE;UACvD2D,MAAM,CAACG,KAAK,IAAKI,QAAQ,CAACE,MAAM,GAAGH,KAAM;QAC3C,CAAC,MAAM,IAAIjE,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,WAAW,EAAE;UACjD2D,MAAM,CAACI,GAAG,IAAKG,QAAQ,CAACE,MAAM,GAAGH,KAAM;QACzC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACLL,QAAQ,EAAES,IAAI,CAACC,KAAK,CAACX,MAAM,CAACC,QAAQ,CAAC;MACrCC,OAAO,EAAEQ,IAAI,CAACC,KAAK,CAACX,MAAM,CAACE,OAAO,CAAC;MACnCC,KAAK,EAAEO,IAAI,CAACC,KAAK,CAACX,MAAM,CAACG,KAAK,CAAC;MAC/BC,GAAG,EAAEM,IAAI,CAACC,KAAK,CAACX,MAAM,CAACI,GAAG;IAC5B,CAAC;EACH,CAAC;EAED,IAAMQ,SAAS,GAAGb,kBAAkB,CAAC,CAAC;EAGtC,IAAMc,UAAU,GAAG,SAAbA,UAAUA,CAAIC,UAAU,EAAK;IACjC,IAAMC,OAAO,GAAG;MAAEC,OAAO,EAAE,MAAM;MAAE3D,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,MAAM;MAAE4B,GAAG,EAAE;IAAU,CAAC;IACnF,OAAO,IAAIlF,IAAI,CAAC8G,UAAU,CAAC,CAACG,kBAAkB,CAACC,SAAS,EAAEH,OAAO,CAAC;EACpE,CAAC;EAGD,IAAMI,eAAe,GAAGtI,SAAS,CAACgD,MAAM,GAAG,CAAC,GACxChD,SAAS,CAACkD,GAAG,CAAC,UAAAqF,IAAI;IAAA,OAAK;MACrB3E,KAAK,EAAE2E,IAAI,CAAC/E,IAAI,CAACmE,WAAW,CAAC,CAAC;MAC9Ba,KAAK,EAAED,IAAI,CAAC/E;IACd,CAAC;EAAA,CAAC,CAAC,GACH,CACE;IAAEI,KAAK,EAAE,WAAW;IAAE4E,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAE5E,KAAK,EAAE,OAAO;IAAE4E,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAE5E,KAAK,EAAE,QAAQ;IAAE4E,KAAK,EAAE;EAAS,CAAC,EACpC;IAAE5E,KAAK,EAAE,OAAO;IAAE4E,KAAK,EAAE;EAAQ,CAAC,CACnC;EAEL,IAAI5H,SAAS,EAAE;IACb,OACE9B,KAAA,CAACvB,IAAI;MAACgI,KAAK,EAAE,CAACC,MAAM,CAACiD,gBAAgB,EAAE;QAAEC,eAAe,EAAE9I,KAAK,CAAC+I,MAAM,CAACC;MAAW,CAAC,CAAE;MAAA1D,QAAA,GACnFtG,IAAA,CAACX,iBAAiB;QAAC4K,IAAI,EAAC,OAAO;QAACC,KAAK,EAAElJ,KAAK,CAAC+I,MAAM,CAACI;MAAQ,CAAE,CAAC,EAC/DnK,IAAA,CAACjB,IAAI;QAAC4H,KAAK,EAAE;UAAEyD,SAAS,EAAE;QAAG,CAAE;QAAA9D,QAAA,EAAC;MAAU,CAAM,CAAC;IAAA,CAC7C,CAAC;EAEX;EAEA,OACEtG,IAAA,CAACpB,UAAU;IAAC+H,KAAK,EAAE,CAACC,MAAM,CAACyD,SAAS,EAAE;MAAEP,eAAe,EAAE9I,KAAK,CAAC+I,MAAM,CAACC;IAAW,CAAC,CAAE;IAAA1D,QAAA,EAClFpG,KAAA,CAACpB,IAAI;MAAC6H,KAAK,EAAEC,MAAM,CAAC0D,IAAK;MAAAhE,QAAA,GACvBpG,KAAA,CAACpB,IAAI,CAAC4H,OAAO;QAAAJ,QAAA,GACXtG,IAAA,CAACf,MAAM;UACLgI,IAAI,EAAC,UAAU;UACfsD,IAAI,EAAC,UAAU;UACfrD,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQ7E,iBAAiB,CAAC,IAAI,CAAC;UAAA,CAAC;UACvCsE,KAAK,EAAEC,MAAM,CAAC4D,UAAW;UAAAlE,QAAA,EAExB8C,UAAU,CAAC/F,eAAe,CAACP,gBAAgB;QAAC,CACvC,CAAC,EAERwC,sBAAsB,CAAC,CAAC,EAEzBtF,IAAA,CAACjB,IAAI;UAAC4H,KAAK,EAAEC,MAAM,CAACgD,KAAM;UAAAtD,QAAA,EAAC;QAAS,CAAM,CAAC,EAC3CtG,IAAA,CAACb,gBAAgB;UACf6F,KAAK,EAAE3B,eAAe,CAACJ,SAAU;UACjCwH,aAAa,EAAE1F,oBAAqB;UACpC2F,OAAO,EAAEhB,eAAgB;UACzB/C,KAAK,EAAEC,MAAM,CAAC8C;QAAgB,CAC/B,CAAC,EAEF1J,IAAA,CAACjB,IAAI;UAAC4H,KAAK,EAAEC,MAAM,CAACgD,KAAM;UAAAtD,QAAA,EAAC;QAAK,CAAM,CAAC,EACvCtG,IAAA,CAAChB,SAAS;UACRiI,IAAI,EAAC,UAAU;UACfjC,KAAK,EAAE3B,eAAe,CAACH,KAAM;UAC7ByH,YAAY,EAAE1F,iBAAkB;UAChC2F,WAAW,EAAC,8BAA8B;UAC1CC,SAAS;UACTlE,KAAK,EAAEC,MAAM,CAACkE;QAAW,CAC1B,CAAC,EAEF5K,KAAA,CAACvB,IAAI;UAACgI,KAAK,EAAEC,MAAM,CAACmE,gBAAiB;UAAAzE,QAAA,GACnCtG,IAAA,CAACjB,IAAI;YAAC4H,KAAK,EAAEC,MAAM,CAACoE,YAAa;YAAA1E,QAAA,EAAC;UAAiB,CAAM,CAAC,EAC1DpG,KAAA,CAACvB,IAAI;YAACgI,KAAK,EAAEC,MAAM,CAACqE,eAAgB;YAAA3E,QAAA,GAClCpG,KAAA,CAAChB,IAAI;cAACqL,IAAI,EAAC,MAAM;cAAC5D,KAAK,EAAEC,MAAM,CAACsE,SAAU;cAAA5E,QAAA,GACvC6C,SAAS,CAACX,QAAQ,EAAC,OACtB;YAAA,CAAM,CAAC,EACPtI,KAAA,CAAChB,IAAI;cAACqL,IAAI,EAAC,SAAS;cAAC5D,KAAK,EAAEC,MAAM,CAACsE,SAAU;cAAA5E,QAAA,GAC1C6C,SAAS,CAACV,OAAO,EAAC,GACrB;YAAA,CAAM,CAAC,EACPvI,KAAA,CAAChB,IAAI;cAACqL,IAAI,EAAC,OAAO;cAAC5D,KAAK,EAAEC,MAAM,CAACsE,SAAU;cAAA5E,QAAA,GACxC6C,SAAS,CAACT,KAAK,EAAC,GACnB;YAAA,CAAM,CAAC,EACPxI,KAAA,CAAChB,IAAI;cAACqL,IAAI,EAAC,KAAK;cAAC5D,KAAK,EAAEC,MAAM,CAACsE,SAAU;cAAA5E,QAAA,GACtC6C,SAAS,CAACR,GAAG,EAAC,GACjB;YAAA,CAAM,CAAC;UAAA,CACH,CAAC;QAAA,CACH,CAAC,EAEP3I,IAAA,CAACV,OAAO;UAACqH,KAAK,EAAEC,MAAM,CAACuE;QAAQ,CAAE,CAAC,EAElCjL,KAAA,CAACvB,IAAI;UAACgI,KAAK,EAAEC,MAAM,CAACwE,WAAY;UAAA9E,QAAA,GAC9BtG,IAAA,CAACjB,IAAI;YAAC4H,KAAK,EAAEC,MAAM,CAACgD,KAAM;YAAAtD,QAAA,EAAC;UAAU,CAAM,CAAC,EAC5CtG,IAAA,CAACf,MAAM;YACLgI,IAAI,EAAC,MAAM;YACXsD,IAAI,EAAC,MAAM;YACXrD,OAAO,EAAEzG,YAAa;YACtB4K,OAAO;YAAA/E,QAAA,EACR;UAED,CAAQ,CAAC;QAAA,CACL,CAAC,EAENjD,eAAe,CAACF,KAAK,CAACiB,MAAM,KAAK,CAAC,GACjClE,KAAA,CAACvB,IAAI;UAACgI,KAAK,EAAEC,MAAM,CAAC0E,cAAe;UAAAhF,QAAA,GACjCtG,IAAA,CAACjB,IAAI;YAAC4H,KAAK,EAAEC,MAAM,CAAC2E,SAAU;YAAAjF,QAAA,EAAC;UAAkB,CAAM,CAAC,EACxDtG,IAAA,CAACf,MAAM;YACLgI,IAAI,EAAC,WAAW;YAChBsD,IAAI,EAAC,MAAM;YACXrD,OAAO,EAAEzG,YAAa;YACtBkG,KAAK,EAAEC,MAAM,CAAC4E,SAAU;YAAAlF,QAAA,EACzB;UAED,CAAQ,CAAC;QAAA,CACL,CAAC,GAEPjD,eAAe,CAACF,KAAK,CAACmB,GAAG,CAAC,UAACuD,IAAI;UAAA,IAAA4D,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAAA,OAC7B1L,KAAA,CAACvB,IAAI;YAAegI,KAAK,EAAEC,MAAM,CAACiF,aAAc;YAAAvF,QAAA,GAC9CpG,KAAA,CAACvB,IAAI;cAACgI,KAAK,EAAEC,MAAM,CAACkF,UAAW;cAAAxF,QAAA,GAC7BtG,IAAA,CAACjB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAACmF,QAAS;gBAAAzF,QAAA,EAAEuB,IAAI,CAAClD;cAAS,CAAO,CAAC,EACrD3E,IAAA,CAACZ,UAAU;gBACTmL,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAE,EAAG;gBACT/C,OAAO,EAAE,SAATA,OAAOA,CAAA;kBAAA,OAAQc,gBAAgB,CAACH,IAAI,CAACjF,EAAE,CAAC;gBAAA;cAAC,CAC1C,CAAC;YAAA,CACE,CAAC,EACP1C,KAAA,CAACvB,IAAI;cAACgI,KAAK,EAAEC,MAAM,CAACoF,WAAY;cAAA1F,QAAA,GAC9BtG,IAAA,CAAChB,SAAS;gBACRiI,IAAI,EAAC,UAAU;gBACf2C,KAAK,EAAC,UAAU;gBAChB5E,KAAK,EAAE6C,IAAI,CAAChD,QAAQ,CAACoH,QAAQ,CAAC,CAAE;gBAChCtB,YAAY,EAAE,SAAdA,YAAYA,CAAGzF,IAAI;kBAAA,OAAKyC,oBAAoB,CAACE,IAAI,CAACjF,EAAE,EAAEsC,IAAI,CAAC;gBAAA,CAAC;gBAC5DgH,YAAY,EAAC,SAAS;gBACtBvF,KAAK,EAAEC,MAAM,CAACuF;cAAc,CAC7B,CAAC,EACFnM,IAAA,CAACjB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAACwF,QAAS;gBAAA9F,QAAA,EAAEuB,IAAI,CAAC/C;cAAI,CAAO,CAAC;YAAA,CAC5C,CAAC,EACN+C,IAAI,CAACtD,IAAI,IAAIsD,IAAI,CAACtD,IAAI,CAAC/C,SAAS,IAC/BtB,KAAA,CAACvB,IAAI;cAACgI,KAAK,EAAEC,MAAM,CAACyF,aAAc;cAAA/F,QAAA,GAChCpG,KAAA,CAACnB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAAC0F,aAAc;gBAAAhG,QAAA,GAC/B2C,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAuC,qBAAA,GAAA5D,IAAI,CAACtD,IAAI,CAAC/C,SAAS,CAACuG,IAAI,CAAC,UAAAwE,CAAC;kBAAA,OACrCA,CAAC,CAAC3H,IAAI,CAACmE,WAAW,CAAC,CAAC,KAAK,UAAU,IACnCwD,CAAC,CAAC3H,IAAI,CAACmE,WAAW,CAAC,CAAC,KAAK,QAAQ;gBAAA,CACnC,CAAC,qBAHY0C,qBAAA,CAGVzC,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAAChD,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,OAC3C;cAAA,CAAM,CAAC,EACP3E,KAAA,CAACnB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAAC0F,aAAc;gBAAAhG,QAAA,GAAC,KAC9B,EAAC2C,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAwC,sBAAA,GAAA7D,IAAI,CAACtD,IAAI,CAAC/C,SAAS,CAACuG,IAAI,CAAC,UAAAwE,CAAC;kBAAA,OACxCA,CAAC,CAAC3H,IAAI,CAACmE,WAAW,CAAC,CAAC,KAAK,SAAS;gBAAA,CACpC,CAAC,qBAFe2C,sBAAA,CAEb1C,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAAChD,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,GAC3C;cAAA,CAAM,CAAC,EACP3E,KAAA,CAACnB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAAC0F,aAAc;gBAAAhG,QAAA,GAAC,KAC9B,EAAC2C,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAyC,sBAAA,GAAA9D,IAAI,CAACtD,IAAI,CAAC/C,SAAS,CAACuG,IAAI,CAAC,UAAAwE,CAAC;kBAAA,OACxCA,CAAC,CAAC3H,IAAI,CAACmE,WAAW,CAAC,CAAC,KAAK,eAAe,IACxCwD,CAAC,CAAC3H,IAAI,CAACmE,WAAW,CAAC,CAAC,KAAK,OAAO;gBAAA,CAClC,CAAC,qBAHe4C,sBAAA,CAGb3C,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAAChD,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,GAC3C;cAAA,CAAM,CAAC,EACP3E,KAAA,CAACnB,IAAI;gBAAC4H,KAAK,EAAEC,MAAM,CAAC0F,aAAc;gBAAAhG,QAAA,GAAC,KAC9B,EAAC2C,IAAI,CAACC,KAAK,CAAC,CAAC,EAAA0C,sBAAA,GAAA/D,IAAI,CAACtD,IAAI,CAAC/C,SAAS,CAACuG,IAAI,CAAC,UAAAwE,CAAC;kBAAA,OACxCA,CAAC,CAAC3H,IAAI,CAACmE,WAAW,CAAC,CAAC,KAAK,KAAK,IAC9BwD,CAAC,CAAC3H,IAAI,CAACmE,WAAW,CAAC,CAAC,KAAK,WAAW;gBAAA,CACtC,CAAC,qBAHe6C,sBAAA,CAGb5C,MAAM,KAAI,CAAC,KAAKnB,IAAI,CAAChD,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAC,GAC3C;cAAA,CAAM,CAAC;YAAA,CACH,CACP;UAAA,GA9CQgD,IAAI,CAACjF,EA+CV,CAAC;QAAA,CACR,CACF;MAAA,CACW,CAAC,EAEf1C,KAAA,CAACpB,IAAI,CAAC4I,OAAO;QAACf,KAAK,EAAEC,MAAM,CAAC4F,OAAQ;QAAAlG,QAAA,GAClCtG,IAAA,CAACf,MAAM;UACLiI,OAAO,EAAE1G,QAAS;UAClBiM,QAAQ,EAAEzK,SAAU;UAAAsE,QAAA,EACrB;QAED,CAAQ,CAAC,EACTtG,IAAA,CAACf,MAAM;UACLgI,IAAI,EAAC,WAAW;UAChBC,OAAO,EAAEgB,UAAW;UACpBwE,OAAO,EAAE1K,SAAU;UACnByK,QAAQ,EAAEzK,SAAS,IAAIqB,eAAe,CAACF,KAAK,CAACiB,MAAM,KAAK,CAAE;UAAAkC,QAAA,EAC3D;QAED,CAAQ,CAAC;MAAA,CACG,CAAC;IAAA,CACX;EAAC,CACG,CAAC;AAEjB,CAAC;AAED,IAAMM,MAAM,GAAGlI,UAAU,CAACiO,MAAM,CAAC;EAC/BtC,SAAS,EAAE;IACTuC,IAAI,EAAE;EACR,CAAC;EACD/C,gBAAgB,EAAE;IAChB+C,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDxC,IAAI,EAAE;IACJyC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC;EACDxC,UAAU,EAAE;IACVyC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDtD,KAAK,EAAE;IACLuD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBhD,SAAS,EAAE,EAAE;IACb6C,YAAY,EAAE;EAChB,CAAC;EACDvD,eAAe,EAAE;IACfuD,YAAY,EAAE;EAChB,CAAC;EACDnC,UAAU,EAAE;IACVmC,YAAY,EAAE;EAChB,CAAC;EACDlC,gBAAgB,EAAE;IAChBX,SAAS,EAAE,EAAE;IACbiD,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,CAAC;IACfxD,eAAe,EAAE;EACnB,CAAC;EACDkB,YAAY,EAAE;IACZmC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBH,YAAY,EAAE,EAAE;IAChBM,SAAS,EAAE;EACb,CAAC;EACDtC,eAAe,EAAE;IACfuC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,MAAM;IAChBZ,cAAc,EAAE;EAClB,CAAC;EACD3B,SAAS,EAAE;IACT6B,MAAM,EAAE;EACV,CAAC;EACD5B,OAAO,EAAE;IACPuC,cAAc,EAAE;EAClB,CAAC;EACDtC,WAAW,EAAE;IACXoC,aAAa,EAAE,KAAK;IACpBX,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACDxB,cAAc,EAAE;IACdwB,UAAU,EAAE,QAAQ;IACpBO,OAAO,EAAE,EAAE;IACXjD,SAAS,EAAE;EACb,CAAC;EACDmB,SAAS,EAAE;IACToC,SAAS,EAAE,QAAQ;IACnBV,YAAY,EAAE;EAChB,CAAC;EACDpB,aAAa,EAAE;IACboB,YAAY,EAAE,EAAE;IAChBI,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,CAAC;IACfxD,eAAe,EAAE;EACnB,CAAC;EACDgC,UAAU,EAAE;IACV0B,aAAa,EAAE,KAAK;IACpBX,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACDf,QAAQ,EAAE;IACRoB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBR,IAAI,EAAE;EACR,CAAC;EACDZ,WAAW,EAAE;IACXwB,aAAa,EAAE,KAAK;IACpBV,UAAU,EAAE,QAAQ;IACpB1C,SAAS,EAAE;EACb,CAAC;EACD+B,aAAa,EAAE;IACbyB,KAAK,EAAE;EACT,CAAC;EACDxB,QAAQ,EAAE;IACRyB,UAAU,EAAE,CAAC;IACbV,QAAQ,EAAE;EACZ,CAAC;EACDd,aAAa,EAAE;IACbmB,aAAa,EAAE,KAAK;IACpBX,cAAc,EAAE,eAAe;IAC/BzC,SAAS,EAAE,CAAC;IACZ0D,UAAU,EAAE,CAAC;IACbC,cAAc,EAAErP,UAAU,CAACsP,aAAa;IACxCC,cAAc,EAAE;EAClB,CAAC;EACD3B,aAAa,EAAE;IACba,QAAQ,EAAE;EACZ,CAAC;EACD3B,SAAS,EAAE;IACTkC,cAAc,EAAE;EAClB,CAAC;EACDlB,OAAO,EAAE;IACPK,cAAc,EAAE,eAAe;IAC/BqB,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE;EACjB,CAAC;EACDtH,mBAAmB,EAAE;IACnB2G,aAAa,EAAE,KAAK;IACpBX,cAAc,EAAE;EAClB,CAAC;EACD/F,gBAAgB,EAAE;IAChB8F,IAAI,EAAE,CAAC;IACPwB,gBAAgB,EAAE;EACpB,CAAC;EACDrH,eAAe,EAAE;IACfwG,SAAS,EAAE,QAAQ;IACnBH,UAAU,EAAE,MAAM;IAClBH,YAAY,EAAE;EAChB,CAAC;EACDjG,gBAAgB,EAAE;IAChBqH,MAAM,EAAE;EACV,CAAC;EACDjH,gBAAgB,EAAE;IAChBsG,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAevN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}