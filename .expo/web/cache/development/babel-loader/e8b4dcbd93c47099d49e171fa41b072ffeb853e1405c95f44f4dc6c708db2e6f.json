{"ast": null, "code": "'use client';\n\nimport { useLocaleContext } from \"../../modules/useLocale\";\nexport default useLocaleContext;", "map": {"version": 3, "names": ["useLocaleContext"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/useLocaleContext/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport { useLocaleContext } from '../../modules/useLocale';\nexport default useLocaleContext;"], "mappings": "AASA,YAAY;;AAEZ,SAASA,gBAAgB;AACzB,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}