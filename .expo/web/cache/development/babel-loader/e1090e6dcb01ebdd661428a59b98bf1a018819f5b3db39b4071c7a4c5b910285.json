{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"disabled\", \"compact\", \"mode\", \"dark\", \"loading\", \"icon\", \"buttonColor\", \"textColor\", \"rippleColor\", \"children\", \"accessibilityLabel\", \"accessibilityHint\", \"accessibilityRole\", \"onPress\", \"onPressIn\", \"onPressOut\", \"onLongPress\", \"delayLongPress\", \"style\", \"theme\", \"uppercase\", \"contentStyle\", \"labelStyle\", \"testID\", \"accessible\", \"background\", \"maxFontSizeMultiplier\", \"touchableRef\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { getButtonColors, getButtonTouchableRippleStyle } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nimport hasTouchHandler from \"../../utils/hasTouchHandler\";\nimport { splitStyles } from \"../../utils/splitStyles\";\nimport ActivityIndicator from \"../ActivityIndicator\";\nimport Icon from \"../Icon\";\nimport Surface from \"../Surface\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar Button = function Button(_ref, ref) {\n  var _borderRadiusStyles$b;\n  var _StyleSheet$flatten;\n  var disabled = _ref.disabled,\n    compact = _ref.compact,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'text' : _ref$mode,\n    dark = _ref.dark,\n    loading = _ref.loading,\n    icon = _ref.icon,\n    customButtonColor = _ref.buttonColor,\n    customTextColor = _ref.textColor,\n    customRippleColor = _ref.rippleColor,\n    children = _ref.children,\n    accessibilityLabel = _ref.accessibilityLabel,\n    accessibilityHint = _ref.accessibilityHint,\n    _ref$accessibilityRol = _ref.accessibilityRole,\n    accessibilityRole = _ref$accessibilityRol === void 0 ? 'button' : _ref$accessibilityRol,\n    onPress = _ref.onPress,\n    onPressIn = _ref.onPressIn,\n    onPressOut = _ref.onPressOut,\n    onLongPress = _ref.onLongPress,\n    delayLongPress = _ref.delayLongPress,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    uppercaseProp = _ref.uppercase,\n    contentStyle = _ref.contentStyle,\n    labelStyle = _ref.labelStyle,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'button' : _ref$testID,\n    accessible = _ref.accessible,\n    background = _ref.background,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    touchableRef = _ref.touchableRef,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isMode = React.useCallback(function (modeToCompare) {\n    return mode === modeToCompare;\n  }, [mode]);\n  var roundness = theme.roundness,\n    isV3 = theme.isV3,\n    animation = theme.animation;\n  var uppercase = uppercaseProp != null ? uppercaseProp : !theme.isV3;\n  var hasPassedTouchHandler = hasTouchHandler({\n    onPress: onPress,\n    onPressIn: onPressIn,\n    onPressOut: onPressOut,\n    onLongPress: onLongPress\n  });\n  var isElevationEntitled = !disabled && (isV3 ? isMode('elevated') : isMode('contained'));\n  var initialElevation = isV3 ? 1 : 2;\n  var activeElevation = isV3 ? 2 : 8;\n  var _React$useRef = React.useRef(new Animated.Value(isElevationEntitled ? initialElevation : 0)),\n    elevation = _React$useRef.current;\n  React.useEffect(function () {\n    Animated.timing(elevation, {\n      toValue: isElevationEntitled ? initialElevation : 0,\n      duration: 0,\n      useNativeDriver: true\n    });\n  }, [isElevationEntitled, elevation, initialElevation]);\n  var handlePressIn = function handlePressIn(e) {\n    onPressIn === null || onPressIn === void 0 ? void 0 : onPressIn(e);\n    if (isV3 ? isMode('elevated') : isMode('contained')) {\n      var scale = animation.scale;\n      Animated.timing(elevation, {\n        toValue: activeElevation,\n        duration: 200 * scale,\n        useNativeDriver: Platform.OS === 'web' || Platform.constants.reactNativeVersion.minor <= 72\n      }).start();\n    }\n  };\n  var handlePressOut = function handlePressOut(e) {\n    onPressOut === null || onPressOut === void 0 ? void 0 : onPressOut(e);\n    if (isV3 ? isMode('elevated') : isMode('contained')) {\n      var scale = animation.scale;\n      Animated.timing(elevation, {\n        toValue: initialElevation,\n        duration: 150 * scale,\n        useNativeDriver: Platform.OS === 'web' || Platform.constants.reactNativeVersion.minor <= 72\n      }).start();\n    }\n  };\n  var flattenedStyles = StyleSheet.flatten(style) || {};\n  var _splitStyles = splitStyles(flattenedStyles, function (style) {\n      return style.startsWith('border') && style.endsWith('Radius');\n    }),\n    _splitStyles2 = _slicedToArray(_splitStyles, 2),\n    borderRadiusStyles = _splitStyles2[1];\n  var borderRadius = (isV3 ? 5 : 1) * roundness;\n  var iconSize = isV3 ? 18 : 16;\n  var _getButtonColors = getButtonColors({\n      customButtonColor: customButtonColor,\n      customTextColor: customTextColor,\n      theme: theme,\n      mode: mode,\n      disabled: disabled,\n      dark: dark\n    }),\n    backgroundColor = _getButtonColors.backgroundColor,\n    borderColor = _getButtonColors.borderColor,\n    textColor = _getButtonColors.textColor,\n    borderWidth = _getButtonColors.borderWidth;\n  var rippleColor = customRippleColor || color(textColor).alpha(0.12).rgb().string();\n  var touchableStyle = _objectSpread(_objectSpread({}, borderRadiusStyles), {}, {\n    borderRadius: (_borderRadiusStyles$b = borderRadiusStyles.borderRadius) != null ? _borderRadiusStyles$b : borderRadius\n  });\n  var buttonStyle = _objectSpread({\n    backgroundColor: backgroundColor,\n    borderColor: borderColor,\n    borderWidth: borderWidth\n  }, touchableStyle);\n  var _ref2 = StyleSheet.flatten(labelStyle) || {},\n    customLabelColor = _ref2.color,\n    customLabelSize = _ref2.fontSize;\n  var font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n  var textStyle = _objectSpread({\n    color: textColor\n  }, font);\n  var iconStyle = ((_StyleSheet$flatten = StyleSheet.flatten(contentStyle)) === null || _StyleSheet$flatten === void 0 ? void 0 : _StyleSheet$flatten.flexDirection) === 'row-reverse' ? [styles.iconReverse, isV3 && styles[`md3IconReverse${compact ? 'Compact' : ''}`], isV3 && isMode('text') && styles[`md3IconReverseTextMode${compact ? 'Compact' : ''}`]] : [styles.icon, isV3 && styles[`md3Icon${compact ? 'Compact' : ''}`], isV3 && isMode('text') && styles[`md3IconTextMode${compact ? 'Compact' : ''}`]];\n  return React.createElement(Surface, _extends({}, rest, {\n    ref: ref,\n    testID: `${testID}-container`,\n    style: [styles.button, compact && styles.compact, buttonStyle, style, !isV3 && !disabled && {\n      elevation: elevation\n    }]\n  }, isV3 && {\n    elevation: elevation\n  }), React.createElement(TouchableRipple, {\n    borderless: true,\n    background: background,\n    onPress: onPress,\n    onLongPress: onLongPress,\n    onPressIn: hasPassedTouchHandler ? handlePressIn : undefined,\n    onPressOut: hasPassedTouchHandler ? handlePressOut : undefined,\n    delayLongPress: delayLongPress,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityHint: accessibilityHint,\n    accessibilityRole: accessibilityRole,\n    accessibilityState: {\n      disabled: disabled\n    },\n    accessible: accessible,\n    disabled: disabled,\n    rippleColor: rippleColor,\n    style: getButtonTouchableRippleStyle(touchableStyle, borderWidth),\n    testID: testID,\n    theme: theme,\n    ref: touchableRef\n  }, React.createElement(View, {\n    style: [styles.content, contentStyle]\n  }, icon && loading !== true ? React.createElement(View, {\n    style: iconStyle,\n    testID: `${testID}-icon-container`\n  }, React.createElement(Icon, {\n    source: icon,\n    size: customLabelSize != null ? customLabelSize : iconSize,\n    color: typeof customLabelColor === 'string' ? customLabelColor : textColor\n  })) : null, loading ? React.createElement(ActivityIndicator, {\n    size: customLabelSize != null ? customLabelSize : iconSize,\n    color: typeof customLabelColor === 'string' ? customLabelColor : textColor,\n    style: iconStyle\n  }) : null, React.createElement(Text, {\n    variant: \"labelLarge\",\n    selectable: false,\n    numberOfLines: 1,\n    testID: `${testID}-text`,\n    style: [styles.label, !isV3 && styles.md2Label, isV3 && (isMode('text') ? icon || loading ? styles.md3LabelTextAddons : styles.md3LabelText : styles.md3Label), compact && styles.compactLabel, uppercase && styles.uppercaseLabel, textStyle, labelStyle],\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, children))));\n};\nvar styles = StyleSheet.create({\n  button: {\n    minWidth: 64,\n    borderStyle: 'solid'\n  },\n  compact: {\n    minWidth: 'auto'\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  icon: {\n    marginLeft: 12,\n    marginRight: -4\n  },\n  iconReverse: {\n    marginRight: 12,\n    marginLeft: -4\n  },\n  md3Icon: {\n    marginLeft: 16,\n    marginRight: -16\n  },\n  md3IconCompact: {\n    marginLeft: 8,\n    marginRight: 0\n  },\n  md3IconReverse: {\n    marginLeft: -16,\n    marginRight: 16\n  },\n  md3IconReverseCompact: {\n    marginLeft: 0,\n    marginRight: 8\n  },\n  md3IconTextMode: {\n    marginLeft: 12,\n    marginRight: -8\n  },\n  md3IconTextModeCompact: {\n    marginLeft: 6,\n    marginRight: 0\n  },\n  md3IconReverseTextMode: {\n    marginLeft: -8,\n    marginRight: 12\n  },\n  md3IconReverseTextModeCompact: {\n    marginLeft: 0,\n    marginRight: 6\n  },\n  label: {\n    textAlign: 'center',\n    marginVertical: 9,\n    marginHorizontal: 16\n  },\n  md2Label: {\n    letterSpacing: 1\n  },\n  compactLabel: {\n    marginHorizontal: 8\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase'\n  },\n  md3Label: {\n    marginVertical: 10,\n    marginHorizontal: 24\n  },\n  md3LabelText: {\n    marginHorizontal: 12\n  },\n  md3LabelTextAddons: {\n    marginHorizontal: 16\n  }\n});\nexport default forwardRef(Button);", "map": {"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "View", "color", "getButtonColors", "getButtonTouchableRippleStyle", "useInternalTheme", "forwardRef", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "splitStyles", "ActivityIndicator", "Icon", "Surface", "TouchableRipple", "Text", "<PERSON><PERSON>", "_ref", "ref", "_borderRadiusStyles$b", "_StyleSheet$flatten", "disabled", "compact", "_ref$mode", "mode", "dark", "loading", "icon", "customButtonColor", "buttonColor", "customTextColor", "textColor", "customRippleColor", "rippleColor", "children", "accessibilityLabel", "accessibilityHint", "_ref$accessibilityRol", "accessibilityRole", "onPress", "onPressIn", "onPressOut", "onLongPress", "delayLongPress", "style", "themeOverrides", "theme", "uppercaseProp", "uppercase", "contentStyle", "labelStyle", "_ref$testID", "testID", "accessible", "background", "maxFontSizeMultiplier", "touchableRef", "rest", "_objectWithoutProperties", "_excluded", "isMode", "useCallback", "modeToCompare", "roundness", "isV3", "animation", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isElevationEntitled", "initialElevation", "activeElevation", "_React$useRef", "useRef", "Value", "elevation", "current", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "handlePressIn", "e", "scale", "OS", "constants", "reactNativeVersion", "minor", "start", "handlePressOut", "flattenedStyles", "flatten", "_splitStyles", "startsWith", "endsWith", "_splitStyles2", "_slicedToArray", "borderRadiusStyles", "borderRadius", "iconSize", "_getButtonColors", "backgroundColor", "borderColor", "borderWidth", "alpha", "rgb", "string", "touchableStyle", "_objectSpread", "buttonStyle", "_ref2", "customLabelColor", "customLabelSize", "fontSize", "font", "fonts", "labelLarge", "medium", "textStyle", "iconStyle", "flexDirection", "styles", "iconReverse", "createElement", "_extends", "button", "borderless", "undefined", "accessibilityState", "content", "source", "size", "variant", "selectable", "numberOfLines", "label", "md2Label", "md3LabelTextAddons", "md3LabelText", "md3Label", "compactLabel", "uppercase<PERSON>abel", "create", "min<PERSON><PERSON><PERSON>", "borderStyle", "alignItems", "justifyContent", "marginLeft", "marginRight", "md3Icon", "md3IconCompact", "md3IconReverse", "md3IconReverseCompact", "md3IconTextMode", "md3IconTextModeCompact", "md3IconReverseTextMode", "md3IconReverseTextModeCompact", "textAlign", "marginVertical", "marginHorizontal", "letterSpacing", "textTransform"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Button/Button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  AccessibilityRole,\n  Animated,\n  ColorValue,\n  GestureResponderEvent,\n  Platform,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\n\nimport {\n  ButtonMode,\n  getButtonColors,\n  getButtonTouchableRippleStyle,\n} from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $Omit, ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\nimport hasTouchHandler from '../../utils/hasTouchHandler';\nimport { splitStyles } from '../../utils/splitStyles';\nimport ActivityIndicator from '../ActivityIndicator';\nimport Icon, { IconSource } from '../Icon';\nimport Surface from '../Surface';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = $Omit<React.ComponentProps<typeof Surface>, 'mode'> & {\n  /**\n   * Mode of the button. You can change the mode to adjust the styling to give it desired emphasis.\n   * - `text` - flat button without background or outline, used for the lowest priority actions, especially when presenting multiple options.\n   * - `outlined` - button with an outline without background, typically used for important, but not primary action – represents medium emphasis.\n   * - `contained` - button with a background color, used for important action, have the most visual impact and high emphasis.\n   * - `elevated` - button with a background color and elevation, used when absolutely necessary e.g. button requires visual separation from a patterned background. @supported Available in v5.x with theme version 3\n   * - `contained-tonal` - button with a secondary background color, an alternative middle ground between contained and outlined buttons. @supported Available in v5.x with theme version 3\n   */\n  mode?: 'text' | 'outlined' | 'contained' | 'elevated' | 'contained-tonal';\n  /**\n   * Whether the color is a dark color. A dark button will render light text and vice-versa. Only applicable for:\n   *  * `contained` mode for theme version 2\n   *  * `contained`, `contained-tonal` and `elevated` modes for theme version 3.\n   */\n  dark?: boolean;\n  /**\n   * Use a compact look, useful for `text` buttons in a row.\n   */\n  compact?: boolean;\n  /**\n   * @deprecated Deprecated in v5.x - use `buttonColor` or `textColor` instead.\n   * Custom text color for flat button, or background color for contained button.\n   */\n  color?: string;\n  /**\n   * Custom button's background color.\n   */\n  buttonColor?: string;\n  /**\n   * Custom button's text color.\n   */\n  textColor?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Whether to show a loading indicator.\n   */\n  loading?: boolean;\n  /**\n   * Icon to display for the `Button`.\n   */\n  icon?: IconSource;\n  /**\n   * Whether the button is disabled. A disabled button is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Label text of the button.\n   */\n  children: React.ReactNode;\n  /**\n   * Make the label text uppercased. Note that this won't work if you pass React elements as children.\n   */\n  uppercase?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the button. This is read by the screen reader when the user taps the button.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Accessibility hint for the button. This is read by the screen reader when the user taps the button.\n   */\n  accessibilityHint?: string;\n  /**\n   * Accessibility role for the button. The \"button\" role is set by default.\n   */\n  accessibilityRole?: AccessibilityRole;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute as soon as the touchable element is pressed and invoked even before onPress.\n   */\n  onPressIn?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute as soon as the touch is released even before onPress.\n   */\n  onPressOut?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * The number of milliseconds a user must touch the element before executing `onLongPress`.\n   */\n  delayLongPress?: number;\n  /**\n   * Style of button's inner content.\n   * Use this prop to apply custom height and width, to set a custom padding or to set the icon on the right with `flexDirection: 'row-reverse'`.\n   */\n  contentStyle?: StyleProp<ViewStyle>;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Style for the button text.\n   */\n  labelStyle?: StyleProp<TextStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Reference for the touchable\n   */\n  touchableRef?: React.RefObject<View>;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n/**\n * A button is component that the user can press to trigger an action.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Button } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Button icon=\"camera\" mode=\"contained\" onPress={() => console.log('Pressed')}>\n *     Press me\n *   </Button>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Button = (\n  {\n    disabled,\n    compact,\n    mode = 'text',\n    dark,\n    loading,\n    icon,\n    buttonColor: customButtonColor,\n    textColor: customTextColor,\n    rippleColor: customRippleColor,\n    children,\n    accessibilityLabel,\n    accessibilityHint,\n    accessibilityRole = 'button',\n    onPress,\n    onPressIn,\n    onPressOut,\n    onLongPress,\n    delayLongPress,\n    style,\n    theme: themeOverrides,\n    uppercase: uppercaseProp,\n    contentStyle,\n    labelStyle,\n    testID = 'button',\n    accessible,\n    background,\n    maxFontSizeMultiplier,\n    touchableRef,\n    ...rest\n  }: Props,\n  ref: React.ForwardedRef<View>\n) => {\n  const theme = useInternalTheme(themeOverrides);\n  const isMode = React.useCallback(\n    (modeToCompare: ButtonMode) => {\n      return mode === modeToCompare;\n    },\n    [mode]\n  );\n  const { roundness, isV3, animation } = theme;\n  const uppercase = uppercaseProp ?? !theme.isV3;\n\n  const hasPassedTouchHandler = hasTouchHandler({\n    onPress,\n    onPressIn,\n    onPressOut,\n    onLongPress,\n  });\n\n  const isElevationEntitled =\n    !disabled && (isV3 ? isMode('elevated') : isMode('contained'));\n  const initialElevation = isV3 ? 1 : 2;\n  const activeElevation = isV3 ? 2 : 8;\n\n  const { current: elevation } = React.useRef<Animated.Value>(\n    new Animated.Value(isElevationEntitled ? initialElevation : 0)\n  );\n\n  React.useEffect(() => {\n    // Workaround not to call setValue on Animated.Value, because it breaks styles.\n    // https://github.com/callstack/react-native-paper/issues/4559\n    Animated.timing(elevation, {\n      toValue: isElevationEntitled ? initialElevation : 0,\n      duration: 0,\n      useNativeDriver: true,\n    });\n  }, [isElevationEntitled, elevation, initialElevation]);\n\n  const handlePressIn = (e: GestureResponderEvent) => {\n    onPressIn?.(e);\n    if (isV3 ? isMode('elevated') : isMode('contained')) {\n      const { scale } = animation;\n      Animated.timing(elevation, {\n        toValue: activeElevation,\n        duration: 200 * scale,\n        useNativeDriver:\n          Platform.OS === 'web' ||\n          Platform.constants.reactNativeVersion.minor <= 72,\n      }).start();\n    }\n  };\n\n  const handlePressOut = (e: GestureResponderEvent) => {\n    onPressOut?.(e);\n    if (isV3 ? isMode('elevated') : isMode('contained')) {\n      const { scale } = animation;\n      Animated.timing(elevation, {\n        toValue: initialElevation,\n        duration: 150 * scale,\n        useNativeDriver:\n          Platform.OS === 'web' ||\n          Platform.constants.reactNativeVersion.minor <= 72,\n      }).start();\n    }\n  };\n\n  const flattenedStyles = (StyleSheet.flatten(style) || {}) as ViewStyle;\n  const [, borderRadiusStyles] = splitStyles(\n    flattenedStyles,\n    (style) => style.startsWith('border') && style.endsWith('Radius')\n  );\n\n  const borderRadius = (isV3 ? 5 : 1) * roundness;\n  const iconSize = isV3 ? 18 : 16;\n\n  const { backgroundColor, borderColor, textColor, borderWidth } =\n    getButtonColors({\n      customButtonColor,\n      customTextColor,\n      theme,\n      mode,\n      disabled,\n      dark,\n    });\n\n  const rippleColor =\n    customRippleColor || color(textColor).alpha(0.12).rgb().string();\n\n  const touchableStyle = {\n    ...borderRadiusStyles,\n    borderRadius: borderRadiusStyles.borderRadius ?? borderRadius,\n  };\n\n  const buttonStyle = {\n    backgroundColor,\n    borderColor,\n    borderWidth,\n    ...touchableStyle,\n  };\n\n  const { color: customLabelColor, fontSize: customLabelSize } =\n    StyleSheet.flatten(labelStyle) || {};\n\n  const font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n\n  const textStyle = {\n    color: textColor,\n    ...font,\n  };\n\n  const iconStyle =\n    StyleSheet.flatten(contentStyle)?.flexDirection === 'row-reverse'\n      ? [\n          styles.iconReverse,\n          isV3 && styles[`md3IconReverse${compact ? 'Compact' : ''}`],\n          isV3 &&\n            isMode('text') &&\n            styles[`md3IconReverseTextMode${compact ? 'Compact' : ''}`],\n        ]\n      : [\n          styles.icon,\n          isV3 && styles[`md3Icon${compact ? 'Compact' : ''}`],\n          isV3 &&\n            isMode('text') &&\n            styles[`md3IconTextMode${compact ? 'Compact' : ''}`],\n        ];\n\n  return (\n    <Surface\n      {...rest}\n      ref={ref}\n      testID={`${testID}-container`}\n      style={\n        [\n          styles.button,\n          compact && styles.compact,\n          buttonStyle,\n          style,\n          !isV3 && !disabled && { elevation },\n        ] as Animated.WithAnimatedValue<StyleProp<ViewStyle>>\n      }\n      {...(isV3 && { elevation: elevation })}\n    >\n      <TouchableRipple\n        borderless\n        background={background}\n        onPress={onPress}\n        onLongPress={onLongPress}\n        onPressIn={hasPassedTouchHandler ? handlePressIn : undefined}\n        onPressOut={hasPassedTouchHandler ? handlePressOut : undefined}\n        delayLongPress={delayLongPress}\n        accessibilityLabel={accessibilityLabel}\n        accessibilityHint={accessibilityHint}\n        accessibilityRole={accessibilityRole}\n        accessibilityState={{ disabled }}\n        accessible={accessible}\n        disabled={disabled}\n        rippleColor={rippleColor}\n        style={getButtonTouchableRippleStyle(touchableStyle, borderWidth)}\n        testID={testID}\n        theme={theme}\n        ref={touchableRef}\n      >\n        <View style={[styles.content, contentStyle]}>\n          {icon && loading !== true ? (\n            <View style={iconStyle} testID={`${testID}-icon-container`}>\n              <Icon\n                source={icon}\n                size={customLabelSize ?? iconSize}\n                color={\n                  typeof customLabelColor === 'string'\n                    ? customLabelColor\n                    : textColor\n                }\n              />\n            </View>\n          ) : null}\n          {loading ? (\n            <ActivityIndicator\n              size={customLabelSize ?? iconSize}\n              color={\n                typeof customLabelColor === 'string'\n                  ? customLabelColor\n                  : textColor\n              }\n              style={iconStyle}\n            />\n          ) : null}\n          <Text\n            variant=\"labelLarge\"\n            selectable={false}\n            numberOfLines={1}\n            testID={`${testID}-text`}\n            style={[\n              styles.label,\n              !isV3 && styles.md2Label,\n              isV3 &&\n                (isMode('text')\n                  ? icon || loading\n                    ? styles.md3LabelTextAddons\n                    : styles.md3LabelText\n                  : styles.md3Label),\n              compact && styles.compactLabel,\n              uppercase && styles.uppercaseLabel,\n              textStyle,\n              labelStyle,\n            ]}\n            maxFontSizeMultiplier={maxFontSizeMultiplier}\n          >\n            {children}\n          </Text>\n        </View>\n      </TouchableRipple>\n    </Surface>\n  );\n};\n\nconst styles = StyleSheet.create({\n  button: {\n    minWidth: 64,\n    borderStyle: 'solid',\n  },\n  compact: {\n    minWidth: 'auto',\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  icon: {\n    marginLeft: 12,\n    marginRight: -4,\n  },\n  iconReverse: {\n    marginRight: 12,\n    marginLeft: -4,\n  },\n  /* eslint-disable react-native/no-unused-styles */\n  md3Icon: {\n    marginLeft: 16,\n    marginRight: -16,\n  },\n  md3IconCompact: {\n    marginLeft: 8,\n    marginRight: 0,\n  },\n  md3IconReverse: {\n    marginLeft: -16,\n    marginRight: 16,\n  },\n  md3IconReverseCompact: {\n    marginLeft: 0,\n    marginRight: 8,\n  },\n  md3IconTextMode: {\n    marginLeft: 12,\n    marginRight: -8,\n  },\n  md3IconTextModeCompact: {\n    marginLeft: 6,\n    marginRight: 0,\n  },\n  md3IconReverseTextMode: {\n    marginLeft: -8,\n    marginRight: 12,\n  },\n  md3IconReverseTextModeCompact: {\n    marginLeft: 0,\n    marginRight: 6,\n  },\n  /* eslint-enable react-native/no-unused-styles */\n  label: {\n    textAlign: 'center',\n    marginVertical: 9,\n    marginHorizontal: 16,\n  },\n  md2Label: {\n    letterSpacing: 1,\n  },\n  compactLabel: {\n    marginHorizontal: 8,\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase',\n  },\n  md3Label: {\n    marginVertical: 10,\n    marginHorizontal: 24,\n  },\n  md3LabelText: {\n    marginHorizontal: 12,\n  },\n  md3LabelTextAddons: {\n    marginHorizontal: 16,\n  },\n});\n\nexport default forwardRef(Button);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAe9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAEEC,eAAe,EACfC,6BAA6B;AAE/B,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AACnB,OAAOC,eAAe;AACtB,SAASC,WAAW;AACpB,OAAOC,iBAAiB;AACxB,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,eAAe;AACtB,OAAOC,IAAI;AA6IX,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAGC,IAAA,EAgCbC,GAA6B,EAC1B;EAAA,IAAAC,qBAAA;EAAA,IAAAC,mBAAA;EAAA,IA/BDC,QAAQ,GA6BFJ,IAAA,CA7BNI,QAAQ;IACRC,OAAO,GA4BDL,IAAA,CA5BNK,OAAO;IAAAC,SAAA,GA4BDN,IAAA,CA3BNO,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,MAAM,GAAAA,SAAA;IACbE,IAAI,GA0BER,IAAA,CA1BNQ,IAAI;IACJC,OAAO,GAyBDT,IAAA,CAzBNS,OAAO;IACPC,IAAI,GAwBEV,IAAA,CAxBNU,IAAI;IACSC,iBAAiB,GAuBxBX,IAAA,CAvBNY,WAAW;IACAC,eAAe,GAsBpBb,IAAA,CAtBNc,SAAS;IACIC,iBAAiB,GAqBxBf,IAAA,CArBNgB,WAAW;IACXC,QAAQ,GAoBFjB,IAAA,CApBNiB,QAAQ;IACRC,kBAAkB,GAmBZlB,IAAA,CAnBNkB,kBAAkB;IAClBC,iBAAiB,GAkBXnB,IAAA,CAlBNmB,iBAAiB;IAAAC,qBAAA,GAkBXpB,IAAA,CAjBNqB,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,cAAG,QAAQ,GAAAA,qBAAA;IAC5BE,OAAO,GAgBDtB,IAAA,CAhBNsB,OAAO;IACPC,SAAS,GAeHvB,IAAA,CAfNuB,SAAS;IACTC,UAAU,GAcJxB,IAAA,CAdNwB,UAAU;IACVC,WAAW,GAaLzB,IAAA,CAbNyB,WAAW;IACXC,cAAc,GAYR1B,IAAA,CAZN0B,cAAc;IACdC,KAAK,GAWC3B,IAAA,CAXN2B,KAAK;IACEC,cAAc,GAUf5B,IAAA,CAVN6B,KAAK;IACMC,aAAa,GASlB9B,IAAA,CATN+B,SAAS;IACTC,YAAY,GAQNhC,IAAA,CARNgC,YAAY;IACZC,UAAU,GAOJjC,IAAA,CAPNiC,UAAU;IAAAC,WAAA,GAOJlC,IAAA,CANNmC,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,QAAQ,GAAAA,WAAA;IACjBE,UAAU,GAKJpC,IAAA,CALNoC,UAAU;IACVC,UAAU,GAIJrC,IAAA,CAJNqC,UAAU;IACVC,qBAAqB,GAGftC,IAAA,CAHNsC,qBAAqB;IACrBC,YAAY,GAENvC,IAAA,CAFNuC,YAAY;IACTC,IAAA,GAAAC,wBAAA,CACGzC,IAAA,EAAA0C,SAAA;EAGR,IAAMb,KAAK,GAAGvC,gBAAgB,CAACsC,cAAc,CAAC;EAC9C,IAAMe,MAAM,GAAG7D,KAAK,CAAC8D,WAAW,CAC7B,UAAAC,aAAyB,EAAK;IAC7B,OAAOtC,IAAI,KAAKsC,aAAa;EAC/B,CAAC,EACD,CAACtC,IAAI,CACP,CAAC;EACD,IAAQuC,SAAS,GAAsBjB,KAAK,CAApCiB,SAAS;IAAEC,IAAI,GAAgBlB,KAAK,CAAzBkB,IAAI;IAAEC,SAAA,GAAcnB,KAAK,CAAnBmB,SAAA;EACzB,IAAMjB,SAAS,GAAGD,aAAa,WAAbA,aAAa,GAAI,CAACD,KAAK,CAACkB,IAAI;EAE9C,IAAME,qBAAqB,GAAGzD,eAAe,CAAC;IAC5C8B,OAAO,EAAPA,OAAO;IACPC,SAAS,EAATA,SAAS;IACTC,UAAU,EAAVA,UAAU;IACVC,WAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAMyB,mBAAmB,GACvB,CAAC9C,QAAQ,KAAK2C,IAAI,GAAGJ,MAAM,CAAC,UAAU,CAAC,GAAGA,MAAM,CAAC,WAAW,CAAC,CAAC;EAChE,IAAMQ,gBAAgB,GAAGJ,IAAI,GAAG,CAAC,GAAG,CAAC;EACrC,IAAMK,eAAe,GAAGL,IAAI,GAAG,CAAC,GAAG,CAAC;EAEpC,IAAAM,aAAA,GAA+BvE,KAAK,CAACwE,MAAM,CACzC,IAAIvE,QAAQ,CAACwE,KAAK,CAACL,mBAAmB,GAAGC,gBAAgB,GAAG,CAAC,CAC/D,CAAC;IAFgBK,SAAA,GAAAH,aAAA,CAATI,OAAO;EAIf3E,KAAK,CAAC4E,SAAS,CAAC,YAAM;IAGpB3E,QAAQ,CAAC4E,MAAM,CAACH,SAAS,EAAE;MACzBI,OAAO,EAAEV,mBAAmB,GAAGC,gBAAgB,GAAG,CAAC;MACnDU,QAAQ,EAAE,CAAC;MACXC,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,mBAAmB,EAAEM,SAAS,EAAEL,gBAAgB,CAAC,CAAC;EAEtD,IAAMY,aAAa,GAAI,SAAjBA,aAAaA,CAAIC,CAAwB,EAAK;IAClDzC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGyC,CAAC,CAAC;IACd,IAAIjB,IAAI,GAAGJ,MAAM,CAAC,UAAU,CAAC,GAAGA,MAAM,CAAC,WAAW,CAAC,EAAE;MACnD,IAAQsB,KAAA,GAAUjB,SAAS,CAAnBiB,KAAA;MACRlF,QAAQ,CAAC4E,MAAM,CAACH,SAAS,EAAE;QACzBI,OAAO,EAAER,eAAe;QACxBS,QAAQ,EAAE,GAAG,GAAGI,KAAK;QACrBH,eAAe,EACb9E,QAAQ,CAACkF,EAAE,KAAK,KAAK,IACrBlF,QAAQ,CAACmF,SAAS,CAACC,kBAAkB,CAACC,KAAK,IAAI;MACnD,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC;EAED,IAAMC,cAAc,GAAI,SAAlBA,cAAcA,CAAIP,CAAwB,EAAK;IACnDxC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAGwC,CAAC,CAAC;IACf,IAAIjB,IAAI,GAAGJ,MAAM,CAAC,UAAU,CAAC,GAAGA,MAAM,CAAC,WAAW,CAAC,EAAE;MACnD,IAAQsB,KAAA,GAAUjB,SAAS,CAAnBiB,KAAA;MACRlF,QAAQ,CAAC4E,MAAM,CAACH,SAAS,EAAE;QACzBI,OAAO,EAAET,gBAAgB;QACzBU,QAAQ,EAAE,GAAG,GAAGI,KAAK;QACrBH,eAAe,EACb9E,QAAQ,CAACkF,EAAE,KAAK,KAAK,IACrBlF,QAAQ,CAACmF,SAAS,CAACC,kBAAkB,CAACC,KAAK,IAAI;MACnD,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC;EAED,IAAME,eAAe,GAAIvF,UAAU,CAACwF,OAAO,CAAC9C,KAAK,CAAC,IAAI,CAAC,CAAe;EACtE,IAAA+C,YAAA,GAA+BjF,WAAW,CACxC+E,eAAe,EACd,UAAA7C,KAAK;MAAA,OAAKA,KAAK,CAACgD,UAAU,CAAC,QAAQ,CAAC,IAAIhD,KAAK,CAACiD,QAAQ,CAAC,QAAQ,CAClE;IAAA,EAAC;IAAAC,aAAA,GAAAC,cAAA,CAAAJ,YAAA;IAHQK,kBAAkB,GAAAF,aAAA;EAK3B,IAAMG,YAAY,GAAG,CAACjC,IAAI,GAAG,CAAC,GAAG,CAAC,IAAID,SAAS;EAC/C,IAAMmC,QAAQ,GAAGlC,IAAI,GAAG,EAAE,GAAG,EAAE;EAE/B,IAAAmC,gBAAA,GACE9F,eAAe,CAAC;MACduB,iBAAiB,EAAjBA,iBAAiB;MACjBE,eAAe,EAAfA,eAAe;MACfgB,KAAK,EAALA,KAAK;MACLtB,IAAI,EAAJA,IAAI;MACJH,QAAQ,EAARA,QAAQ;MACRI,IAAA,EAAAA;IACF,CAAC,CAAC;IARI2E,eAAe,GAAAD,gBAAA,CAAfC,eAAe;IAAEC,WAAW,GAAAF,gBAAA,CAAXE,WAAW;IAAEtE,SAAS,GAAAoE,gBAAA,CAATpE,SAAS;IAAEuE,WAAA,GAAAH,gBAAA,CAAAG,WAAA;EAUjD,IAAMrE,WAAW,GACfD,iBAAiB,IAAI5B,KAAK,CAAC2B,SAAS,CAAC,CAACwE,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAElE,IAAMC,cAAc,GAAAC,aAAA,CAAAA,aAAA,KACfX,kBAAkB;IACrBC,YAAY,GAAA9E,qBAAA,GAAE6E,kBAAkB,CAACC,YAAY,YAAA9E,qBAAA,GAAI8E;EAAA,EAClD;EAED,IAAMW,WAAW,GAAAD,aAAA;IACfP,eAAe,EAAfA,eAAe;IACfC,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EAAW,GACRI,cAAA,CACJ;EAED,IAAAG,KAAA,GACE3G,UAAU,CAACwF,OAAO,CAACxC,UAAU,CAAC,IAAI,CAAC,CAAC;IADvB4D,gBAAgB,GAAAD,KAAA,CAAvBzG,KAAK;IAA8B2G,eAAA,GAAAF,KAAA,CAAVG,QAAQ;EAGzC,IAAMC,IAAI,GAAGjD,IAAI,GAAGlB,KAAK,CAACoE,KAAK,CAACC,UAAU,GAAGrE,KAAK,CAACoE,KAAK,CAACE,MAAM;EAE/D,IAAMC,SAAS,GAAAV,aAAA;IACbvG,KAAK,EAAE2B;EAAS,GACbkF,IAAA,CACJ;EAED,IAAMK,SAAS,GACb,EAAAlG,mBAAA,GAAAlB,UAAU,CAACwF,OAAO,CAACzC,YAAY,CAAC,cAAA7B,mBAAA,uBAAhCA,mBAAA,CAAkCmG,aAAa,MAAK,aAAa,GAC7D,CACEC,MAAM,CAACC,WAAW,EAClBzD,IAAI,IAAIwD,MAAM,CAAE,iBAAgBlG,OAAO,GAAG,SAAS,GAAG,EAAG,EAAC,CAAC,EAC3D0C,IAAI,IACFJ,MAAM,CAAC,MAAM,CAAC,IACd4D,MAAM,CAAE,yBAAwBlG,OAAO,GAAG,SAAS,GAAG,EAAG,EAAC,CAAC,CAC9D,GACD,CACEkG,MAAM,CAAC7F,IAAI,EACXqC,IAAI,IAAIwD,MAAM,CAAE,UAASlG,OAAO,GAAG,SAAS,GAAG,EAAG,EAAC,CAAC,EACpD0C,IAAI,IACFJ,MAAM,CAAC,MAAM,CAAC,IACd4D,MAAM,CAAE,kBAAiBlG,OAAO,GAAG,SAAS,GAAG,EAAG,EAAC,CAAC,CACvD;EAEP,OACEvB,KAAA,CAAA2H,aAAA,CAAC7G,OAAO,EAAA8G,QAAA,KACFlE,IAAI;IACRvC,GAAG,EAAEA,GAAI;IACTkC,MAAM,EAAG,GAAEA,MAAO,YAAY;IAC9BR,KAAK,EACH,CACE4E,MAAM,CAACI,MAAM,EACbtG,OAAO,IAAIkG,MAAM,CAAClG,OAAO,EACzBsF,WAAW,EACXhE,KAAK,EACL,CAACoB,IAAI,IAAI,CAAC3C,QAAQ,IAAI;MAAEoD,SAAA,EAAAA;IAAU,CAAC;EAEtC,GACIT,IAAI,IAAI;IAAES,SAAS,EAAEA;EAAU,CAAC,GAErC1E,KAAA,CAAA2H,aAAA,CAAC5G,eAAe;IACd+G,UAAU;IACVvE,UAAU,EAAEA,UAAW;IACvBf,OAAO,EAAEA,OAAQ;IACjBG,WAAW,EAAEA,WAAY;IACzBF,SAAS,EAAE0B,qBAAqB,GAAGc,aAAa,GAAG8C,SAAU;IAC7DrF,UAAU,EAAEyB,qBAAqB,GAAGsB,cAAc,GAAGsC,SAAU;IAC/DnF,cAAc,EAAEA,cAAe;IAC/BR,kBAAkB,EAAEA,kBAAmB;IACvCC,iBAAiB,EAAEA,iBAAkB;IACrCE,iBAAiB,EAAEA,iBAAkB;IACrCyF,kBAAkB,EAAE;MAAE1G,QAAA,EAAAA;IAAS,CAAE;IACjCgC,UAAU,EAAEA,UAAW;IACvBhC,QAAQ,EAAEA,QAAS;IACnBY,WAAW,EAAEA,WAAY;IACzBW,KAAK,EAAEtC,6BAA6B,CAACoG,cAAc,EAAEJ,WAAW,CAAE;IAClElD,MAAM,EAAEA,MAAO;IACfN,KAAK,EAAEA,KAAM;IACb5B,GAAG,EAAEsC;EAAa,GAElBzD,KAAA,CAAA2H,aAAA,CAACvH,IAAI;IAACyC,KAAK,EAAE,CAAC4E,MAAM,CAACQ,OAAO,EAAE/E,YAAY;EAAE,GACzCtB,IAAI,IAAID,OAAO,KAAK,IAAI,GACvB3B,KAAA,CAAA2H,aAAA,CAACvH,IAAI;IAACyC,KAAK,EAAE0E,SAAU;IAAClE,MAAM,EAAG,GAAEA,MAAO;EAAiB,GACzDrD,KAAA,CAAA2H,aAAA,CAAC9G,IAAI;IACHqH,MAAM,EAAEtG,IAAK;IACbuG,IAAI,EAAEnB,eAAe,WAAfA,eAAe,GAAIb,QAAS;IAClC9F,KAAK,EACH,OAAO0G,gBAAgB,KAAK,QAAQ,GAChCA,gBAAgB,GAChB/E;EACL,CACF,CACG,CAAC,GACL,IAAI,EACPL,OAAO,GACN3B,KAAA,CAAA2H,aAAA,CAAC/G,iBAAiB;IAChBuH,IAAI,EAAEnB,eAAe,WAAfA,eAAe,GAAIb,QAAS;IAClC9F,KAAK,EACH,OAAO0G,gBAAgB,KAAK,QAAQ,GAChCA,gBAAgB,GAChB/E,SACL;IACDa,KAAK,EAAE0E;EAAU,CAClB,CAAC,GACA,IAAI,EACRvH,KAAA,CAAA2H,aAAA,CAAC3G,IAAI;IACHoH,OAAO,EAAC,YAAY;IACpBC,UAAU,EAAE,KAAM;IAClBC,aAAa,EAAE,CAAE;IACjBjF,MAAM,EAAG,GAAEA,MAAO,OAAO;IACzBR,KAAK,EAAE,CACL4E,MAAM,CAACc,KAAK,EACZ,CAACtE,IAAI,IAAIwD,MAAM,CAACe,QAAQ,EACxBvE,IAAI,KACDJ,MAAM,CAAC,MAAM,CAAC,GACXjC,IAAI,IAAID,OAAO,GACb8F,MAAM,CAACgB,kBAAkB,GACzBhB,MAAM,CAACiB,YAAY,GACrBjB,MAAM,CAACkB,QAAQ,CAAC,EACtBpH,OAAO,IAAIkG,MAAM,CAACmB,YAAY,EAC9B3F,SAAS,IAAIwE,MAAM,CAACoB,cAAc,EAClCvB,SAAS,EACTnE,UAAU,CACV;IACFK,qBAAqB,EAAEA;EAAsB,GAE5CrB,QACG,CACF,CACS,CACV,CAAC;AAEd,CAAC;AAED,IAAMsF,MAAM,GAAGtH,UAAU,CAAC2I,MAAM,CAAC;EAC/BjB,MAAM,EAAE;IACNkB,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC;EACDzH,OAAO,EAAE;IACPwH,QAAQ,EAAE;EACZ,CAAC;EACDd,OAAO,EAAE;IACPT,aAAa,EAAE,KAAK;IACpByB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDtH,IAAI,EAAE;IACJuH,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,CAAC;EAChB,CAAC;EACD1B,WAAW,EAAE;IACX0B,WAAW,EAAE,EAAE;IACfD,UAAU,EAAE,CAAC;EACf,CAAC;EAEDE,OAAO,EAAE;IACPF,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,CAAC;EAChB,CAAC;EACDE,cAAc,EAAE;IACdH,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE;EACf,CAAC;EACDG,cAAc,EAAE;IACdJ,UAAU,EAAE,CAAC,EAAE;IACfC,WAAW,EAAE;EACf,CAAC;EACDI,qBAAqB,EAAE;IACrBL,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE;EACf,CAAC;EACDK,eAAe,EAAE;IACfN,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,CAAC;EAChB,CAAC;EACDM,sBAAsB,EAAE;IACtBP,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE;EACf,CAAC;EACDO,sBAAsB,EAAE;IACtBR,UAAU,EAAE,CAAC,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDQ,6BAA6B,EAAE;IAC7BT,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE;EACf,CAAC;EAEDb,KAAK,EAAE;IACLsB,SAAS,EAAE,QAAQ;IACnBC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE;EACpB,CAAC;EACDvB,QAAQ,EAAE;IACRwB,aAAa,EAAE;EACjB,CAAC;EACDpB,YAAY,EAAE;IACZmB,gBAAgB,EAAE;EACpB,CAAC;EACDlB,cAAc,EAAE;IACdoB,aAAa,EAAE;EACjB,CAAC;EACDtB,QAAQ,EAAE;IACRmB,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EACDrB,YAAY,EAAE;IACZqB,gBAAgB,EAAE;EACpB,CAAC;EACDtB,kBAAkB,EAAE;IAClBsB,gBAAgB,EAAE;EACpB;AACF,CAAC,CAAC;AAEF,eAAetJ,UAAU,CAACQ,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}