{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nvar _BarCodeScanner;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport { PermissionStatus, createPermissionHook, UnavailabilityError } from 'expo-modules-core';\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport ExpoBarCodeScannerModule from \"./ExpoBarCodeScannerModule\";\nimport ExpoBarCodeScannerView from \"./ExpoBarCodeScannerView\";\nvar BarCodeType = ExpoBarCodeScannerModule.BarCodeType,\n  Type = ExpoBarCodeScannerModule.Type;\nvar EVENT_THROTTLE_MS = 500;\nexport var BarCodeScanner = function (_React$Component) {\n  function BarCodeScanner() {\n    var _this;\n    _classCallCheck(this, BarCodeScanner);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, BarCodeScanner, [].concat(args));\n    _this.lastEvents = {};\n    _this.lastEventsTimes = {};\n    _this.onObjectDetected = function (callback) {\n      return function (_ref) {\n        var nativeEvent = _ref.nativeEvent;\n        var type = nativeEvent.type;\n        if (_this.lastEvents[type] && _this.lastEventsTimes[type] && JSON.stringify(nativeEvent) === _this.lastEvents[type] && Date.now() - _this.lastEventsTimes[type] < EVENT_THROTTLE_MS) {\n          return;\n        }\n        if (callback) {\n          callback(nativeEvent);\n          _this.lastEventsTimes[type] = new Date();\n          _this.lastEvents[type] = JSON.stringify(nativeEvent);\n        }\n      };\n    };\n    return _this;\n  }\n  _inherits(BarCodeScanner, _React$Component);\n  return _createClass(BarCodeScanner, [{\n    key: \"render\",\n    value: function render() {\n      var nativeProps = this.convertNativeProps(this.props);\n      var onBarCodeScanned = this.props.onBarCodeScanned;\n      return React.createElement(ExpoBarCodeScannerView, _objectSpread(_objectSpread({}, nativeProps), {}, {\n        onBarCodeScanned: this.onObjectDetected(onBarCodeScanned)\n      }));\n    }\n  }, {\n    key: \"convertNativeProps\",\n    value: function convertNativeProps(props) {\n      var nativeProps = {};\n      for (var _ref2 of Object.entries(props)) {\n        var _ref3 = _slicedToArray(_ref2, 2);\n        var key = _ref3[0];\n        var value = _ref3[1];\n        if (typeof value === 'string' && BarCodeScanner.ConversionTables[key]) {\n          nativeProps[key] = BarCodeScanner.ConversionTables[key][value];\n        } else {\n          nativeProps[key] = value;\n        }\n      }\n      return nativeProps;\n    }\n  }], [{\n    key: \"getPermissionsAsync\",\n    value: function () {\n      var _getPermissionsAsync = _asyncToGenerator(function* () {\n        return ExpoBarCodeScannerModule.getPermissionsAsync();\n      });\n      function getPermissionsAsync() {\n        return _getPermissionsAsync.apply(this, arguments);\n      }\n      return getPermissionsAsync;\n    }()\n  }, {\n    key: \"requestPermissionsAsync\",\n    value: function () {\n      var _requestPermissionsAsync = _asyncToGenerator(function* () {\n        return ExpoBarCodeScannerModule.requestPermissionsAsync();\n      });\n      function requestPermissionsAsync() {\n        return _requestPermissionsAsync.apply(this, arguments);\n      }\n      return requestPermissionsAsync;\n    }()\n  }, {\n    key: \"scanFromURLAsync\",\n    value: function () {\n      var _scanFromURLAsync = _asyncToGenerator(function* (url) {\n        var barCodeTypes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Object.values(BarCodeType);\n        if (!ExpoBarCodeScannerModule.scanFromURLAsync) {\n          throw new UnavailabilityError('expo-barcode-scanner', 'scanFromURLAsync');\n        }\n        if (Array.isArray(barCodeTypes) && !barCodeTypes.length) {\n          throw new Error('No barCodeTypes specified; provide at least one barCodeType for scanner');\n        }\n        if (Platform.OS === 'ios') {\n          if (Array.isArray(barCodeTypes) && !barCodeTypes.includes(BarCodeType.qr)) {\n            throw new Error('Only QR type is supported by scanFromURLAsync() on iOS');\n          }\n          return yield ExpoBarCodeScannerModule.scanFromURLAsync(url, [BarCodeType.qr]);\n        }\n        return yield ExpoBarCodeScannerModule.scanFromURLAsync(url, barCodeTypes);\n      });\n      function scanFromURLAsync(_x) {\n        return _scanFromURLAsync.apply(this, arguments);\n      }\n      return scanFromURLAsync;\n    }()\n  }]);\n}(React.Component);\n_BarCodeScanner = BarCodeScanner;\nBarCodeScanner.Constants = {\n  BarCodeType: BarCodeType,\n  Type: Type\n};\nBarCodeScanner.ConversionTables = {\n  type: Type\n};\nBarCodeScanner.defaultProps = {\n  type: Type.back,\n  barCodeTypes: Object.values(BarCodeType)\n};\nBarCodeScanner.usePermissions = createPermissionHook({\n  getMethod: _BarCodeScanner.getPermissionsAsync,\n  requestMethod: _BarCodeScanner.requestPermissionsAsync\n});\nexport { PermissionStatus };\nvar Constants = BarCodeScanner.Constants,\n  getPermissionsAsync = BarCodeScanner.getPermissionsAsync,\n  requestPermissionsAsync = BarCodeScanner.requestPermissionsAsync,\n  scanFromURLAsync = BarCodeScanner.scanFromURLAsync;\nexport { Constants, getPermissionsAsync, requestPermissionsAsync, scanFromURLAsync };", "map": {"version": 3, "names": ["PermissionStatus", "createPermissionHook", "UnavailabilityError", "React", "Platform", "ExpoBarCodeScannerModule", "ExpoBarCodeScannerView", "BarCodeType", "Type", "EVENT_THROTTLE_MS", "BarCodeScanner", "_React$Component", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper", "concat", "lastEvents", "lastEventsTimes", "onObjectDetected", "callback", "_ref", "nativeEvent", "type", "JSON", "stringify", "Date", "now", "_inherits", "_createClass", "key", "value", "render", "nativeProps", "convertNativeProps", "props", "onBarCodeScanned", "createElement", "_objectSpread", "_ref2", "Object", "entries", "_ref3", "_slicedToArray", "ConversionTables", "_getPermissionsAsync", "_asyncToGenerator", "getPermissionsAsync", "apply", "_requestPermissionsAsync", "requestPermissionsAsync", "_scanFromURLAsync", "url", "barCodeTypes", "undefined", "values", "scanFromURLAsync", "isArray", "Error", "OS", "includes", "qr", "_x", "Component", "Constants", "defaultProps", "back", "usePermissions", "getMethod", "requestMethod"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-barcode-scanner/src/BarCodeScanner.tsx"], "sourcesContent": ["import {\n  PermissionResponse,\n  PermissionStatus,\n  PermissionHookOptions,\n  createPermissionHook,\n  UnavailabilityError,\n} from 'expo-modules-core';\nimport * as React from 'react';\nimport { Platform, ViewProps } from 'react-native';\n\nimport ExpoBarCodeScannerModule from './ExpoBarCodeScannerModule';\nimport ExpoBarCodeScannerView from './ExpoBarCodeScannerView';\n\nconst { BarCodeType, Type } = ExpoBarCodeScannerModule;\n\nconst EVENT_THROTTLE_MS = 500;\n\n// @needsAudit\n/**\n * Those coordinates are represented in the coordinate space of the barcode source (e.g. when you\n * are using the barcode scanner view, these values are adjusted to the dimensions of the view).\n */\nexport type BarCodePoint = {\n  /**\n   * The `x` coordinate value.\n   */\n  x: number;\n  /**\n   * The `y` coordinate value.\n   */\n  y: number;\n};\n\n// @needsAudit\nexport type BarCodeSize = {\n  /**\n   * The height value.\n   */\n  height: number;\n  /**\n   * The width value.\n   */\n  width: number;\n};\n\n// @needsAudit\nexport type BarCodeBounds = {\n  /**\n   * The origin point of the bounding box.\n   */\n  origin: BarCodePoint;\n  /**\n   * The size of the bounding box.\n   */\n  size: BarCodeSize;\n};\n\n// @needsAudit\nexport type BarCodeScannerResult = {\n  /**\n   * The barcode type.\n   */\n  type: string;\n  /**\n   * The information encoded in the bar code.\n   */\n  data: string;\n  /**\n   * The [BarCodeBounds](#barcodebounds) object.\n   * `bounds` in some case will be representing an empty rectangle.\n   * Moreover, `bounds` doesn't have to bound the whole barcode.\n   * For some types, they will represent the area used by the scanner.\n   */\n  bounds: BarCodeBounds;\n  /**\n   * Corner points of the bounding box.\n   * `cornerPoints` is not always available and may be empty. On iOS, for `code39` and `pdf417`\n   * you don't get this value.\n   */\n  cornerPoints: BarCodePoint[];\n};\n\n// @docsMissing\nexport type BarCodeEvent = BarCodeScannerResult & {\n  target?: number;\n};\n\n// @docsMissing\nexport type BarCodeEventCallbackArguments = {\n  nativeEvent: BarCodeEvent;\n};\n\n// @docsMissing\nexport type BarCodeScannedCallback = (params: BarCodeEvent) => void;\n\n// @needsAudit\nexport type BarCodeScannerProps = ViewProps & {\n  /**\n   * Camera facing. Use one of `BarCodeScanner.Constants.Type`. Use either `Type.front` or `Type.back`.\n   * Same as `Camera.Constants.Type`.\n   * @default Type.back\n   */\n  type?: 'front' | 'back' | number;\n  /**\n   * An array of bar code types. Usage: `BarCodeScanner.Constants.BarCodeType.<codeType>` where\n   * `codeType` is one of these [listed above](#supported-formats). Defaults to all supported bar\n   * code types. It is recommended to provide only the bar code formats you expect to scan to\n   * minimize battery usage.\n   *\n   * For example: `barCodeTypes={[BarCodeScanner.Constants.BarCodeType.qr]}`.\n   */\n  barCodeTypes?: string[];\n  /**\n   * A callback that is invoked when a bar code has been successfully scanned. The callback is\n   * provided with an [BarCodeScannerResult](#barcodescannerresult).\n   * > __Note:__ Passing `undefined` to the `onBarCodeScanned` prop will result in no scanning. This\n   * > can be used to effectively \"pause\" the scanner so that it doesn't continually scan even after\n   * > data has been retrieved.\n   */\n  onBarCodeScanned?: BarCodeScannedCallback;\n};\n\nexport class BarCodeScanner extends React.Component<BarCodeScannerProps> {\n  lastEvents: { [key: string]: any } = {};\n  lastEventsTimes: { [key: string]: any } = {};\n\n  static Constants = {\n    BarCodeType,\n    Type,\n  };\n\n  static ConversionTables = {\n    type: Type,\n  };\n\n  static defaultProps = {\n    type: Type.back,\n    barCodeTypes: Object.values(BarCodeType),\n  };\n\n  // @needsAudit\n  /**\n   * Checks user's permissions for accessing the camera.\n   * @return Return a promise that fulfills to an object of type [`PermissionResponse`](#permissionresponse).\n   */\n  static async getPermissionsAsync(): Promise<PermissionResponse> {\n    return ExpoBarCodeScannerModule.getPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Asks the user to grant permissions for accessing the camera.\n   *\n   * On iOS this will require apps to specify the `NSCameraUsageDescription` entry in the `Info.plist`.\n   * @return Return a promise that fulfills to an object of type [`PermissionResponse`](#permissionresponse).\n   */\n  static async requestPermissionsAsync(): Promise<PermissionResponse> {\n    return ExpoBarCodeScannerModule.requestPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Check or request permissions for the barcode scanner.\n   * This uses both `requestPermissionAsync` and `getPermissionsAsync` to interact with the permissions.\n   *\n   * @example\n   * ```ts\n   * const [permissionResponse, requestPermission] = BarCodeScanner.usePermissions();\n   * ```\n   */\n  static usePermissions = createPermissionHook({\n    getMethod: BarCodeScanner.getPermissionsAsync,\n    requestMethod: BarCodeScanner.requestPermissionsAsync,\n  });\n\n  // @needsAudit\n  /**\n   * Scan bar codes from the image given by the URL.\n   * @param url URL to get the image from.\n   * @param barCodeTypes An array of bar code types. Defaults to all supported bar code types on\n   * the platform.\n   * > __Note:__ Only QR codes are supported on iOS.\n   * @return A possibly empty array of objects of the `BarCodeScannerResult` shape, where the type\n   * refers to the bar code type that was scanned and the data is the information encoded in the bar\n   * code.\n   */\n  static async scanFromURLAsync(\n    url: string,\n    barCodeTypes: string[] = Object.values(BarCodeType)\n  ): Promise<BarCodeScannerResult[]> {\n    if (!ExpoBarCodeScannerModule.scanFromURLAsync) {\n      throw new UnavailabilityError('expo-barcode-scanner', 'scanFromURLAsync');\n    }\n    if (Array.isArray(barCodeTypes) && !barCodeTypes.length) {\n      throw new Error('No barCodeTypes specified; provide at least one barCodeType for scanner');\n    }\n\n    if (Platform.OS === 'ios') {\n      if (Array.isArray(barCodeTypes) && !barCodeTypes.includes(BarCodeType.qr)) {\n        // Only QR type is supported on iOS, fail if one tries to use other types\n        throw new Error('Only QR type is supported by scanFromURLAsync() on iOS');\n      }\n      // on iOS use only supported QR type\n      return await ExpoBarCodeScannerModule.scanFromURLAsync(url, [BarCodeType.qr]);\n    }\n\n    // On other platforms, if barCodeTypes is not provided, use all available types\n    return await ExpoBarCodeScannerModule.scanFromURLAsync(url, barCodeTypes);\n  }\n\n  render() {\n    const nativeProps = this.convertNativeProps(this.props);\n    const { onBarCodeScanned } = this.props;\n    return (\n      <ExpoBarCodeScannerView\n        {...nativeProps}\n        onBarCodeScanned={this.onObjectDetected(onBarCodeScanned)}\n      />\n    );\n  }\n\n  /**\n   * @hidden\n   */\n  onObjectDetected =\n    (callback?: BarCodeScannedCallback) =>\n    ({ nativeEvent }: BarCodeEventCallbackArguments) => {\n      const { type } = nativeEvent;\n      if (\n        this.lastEvents[type] &&\n        this.lastEventsTimes[type] &&\n        JSON.stringify(nativeEvent) === this.lastEvents[type] &&\n        Date.now() - this.lastEventsTimes[type] < EVENT_THROTTLE_MS\n      ) {\n        return;\n      }\n\n      if (callback) {\n        callback(nativeEvent);\n        this.lastEventsTimes[type] = new Date();\n        this.lastEvents[type] = JSON.stringify(nativeEvent);\n      }\n    };\n\n  /**\n   * @hidden\n   */\n  convertNativeProps(props: BarCodeScannerProps) {\n    const nativeProps: BarCodeScannerProps = {};\n\n    for (const [key, value] of Object.entries(props)) {\n      if (typeof value === 'string' && BarCodeScanner.ConversionTables[key]) {\n        nativeProps[key] = BarCodeScanner.ConversionTables[key][value];\n      } else {\n        nativeProps[key] = value;\n      }\n    }\n\n    return nativeProps;\n  }\n}\n\nexport { PermissionResponse, PermissionStatus, PermissionHookOptions };\nexport const { Constants, getPermissionsAsync, requestPermissionsAsync, scanFromURLAsync } =\n  BarCodeScanner;\n"], "mappings": ";;;;;;;;;;;;;AAAA,SAEEA,gBAAgB,EAEhBC,oBAAoB,EACpBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAC,OAAAC,QAAA;AAG/B,OAAOC,wBAAwB;AAC/B,OAAOC,sBAAsB;AAE7B,IAAQC,WAAW,GAAWF,wBAAwB,CAA9CE,WAAW;EAAEC,IAAI,GAAKH,wBAAwB,CAAjCG,IAAI;AAEzB,IAAMC,iBAAiB,GAAG,GAAG;AA2G7B,WAAaC,cAAe,aAAAC,gBAAA;EAAA,SAAAD,eAAA;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,cAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAQ,UAAA,OAAAV,cAAA,KAAAW,MAAA,CAAAJ,IAAA;IAAAL,KAAA,CAC1BU,UAAU,GAA2B,EAAE;IAAAV,KAAA,CACvCW,eAAe,GAA2B,EAAE;IAAAX,KAAA,CAoG5CY,gBAAgB,GACd,UAACC,QAAiC;MAAA,OAClC,UAAAC,IAAA,EAAmD;QAAA,IAAhDC,WAAW,GAAAD,IAAA,CAAXC,WAAW;QACZ,IAAQC,IAAI,GAAKD,WAAW,CAApBC,IAAI;QACZ,IACEhB,KAAA,CAAKU,UAAU,CAACM,IAAI,CAAC,IACrBhB,KAAA,CAAKW,eAAe,CAACK,IAAI,CAAC,IAC1BC,IAAI,CAACC,SAAS,CAACH,WAAW,CAAC,KAAKf,KAAA,CAAKU,UAAU,CAACM,IAAI,CAAC,IACrDG,IAAI,CAACC,GAAG,EAAE,GAAGpB,KAAA,CAAKW,eAAe,CAACK,IAAI,CAAC,GAAGnB,iBAAiB,EAC3D;UACA;;QAGF,IAAIgB,QAAQ,EAAE;UACZA,QAAQ,CAACE,WAAW,CAAC;UACrBf,KAAA,CAAKW,eAAe,CAACK,IAAI,CAAC,GAAG,IAAIG,IAAI,EAAE;UACvCnB,KAAA,CAAKU,UAAU,CAACM,IAAI,CAAC,GAAGC,IAAI,CAACC,SAAS,CAACH,WAAW,CAAC;;MAEvD,CAAC;IAAA;IAAA,OAAAf,KAAA;EAAA;EAAAqB,SAAA,CAAAvB,cAAA,EAAAC,gBAAA;EAAA,OAAAuB,YAAA,CAAAxB,cAAA;IAAAyB,GAAA;IAAAC,KAAA,EAhCH,SAAAC,MAAMA,CAAA;MACJ,IAAMC,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACC,KAAK,CAAC;MACvD,IAAQC,gBAAgB,GAAK,IAAI,CAACD,KAAK,CAA/BC,gBAAgB;MACxB,OACEtC,KAAA,CAAAuC,aAAA,CAACpC,sBAAsB,EAAAqC,aAAA,CAAAA,aAAA,KACjBL,WAAW;QACfG,gBAAgB,EAAE,IAAI,CAACjB,gBAAgB,CAACiB,gBAAgB;MAAC,GACzD;IAEN;EAAC;IAAAN,GAAA;IAAAC,KAAA,EA4BD,SAAAG,kBAAkBA,CAACC,KAA0B;MAC3C,IAAMF,WAAW,GAAwB,EAAE;MAE3C,SAAAM,KAAA,IAA2BC,MAAM,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;QAAA,IAAAO,KAAA,GAAAC,cAAA,CAAAJ,KAAA;QAAA,IAAtCT,GAAG,GAAAY,KAAA;QAAA,IAAEX,KAAK,GAAAW,KAAA;QACpB,IAAI,OAAOX,KAAK,KAAK,QAAQ,IAAI1B,cAAc,CAACuC,gBAAgB,CAACd,GAAG,CAAC,EAAE;UACrEG,WAAW,CAACH,GAAG,CAAC,GAAGzB,cAAc,CAACuC,gBAAgB,CAACd,GAAG,CAAC,CAACC,KAAK,CAAC;SAC/D,MAAM;UACLE,WAAW,CAACH,GAAG,CAAC,GAAGC,KAAK;;;MAI5B,OAAOE,WAAW;IACpB;EAAC;IAAAH,GAAA;IAAAC,KAAA;MAAA,IAAAc,oBAAA,GAAAC,iBAAA,CAlHD,aAAgC;QAC9B,OAAO9C,wBAAwB,CAAC+C,mBAAmB,EAAE;MACvD,CAAC;MAAA,SAFYA,mBAAmBA,CAAA;QAAA,OAAAF,oBAAA,CAAAG,KAAA,OAAAtC,SAAA;MAAA;MAAA,OAAnBqC,mBAAmB;IAAA;EAAA;IAAAjB,GAAA;IAAAC,KAAA;MAAA,IAAAkB,wBAAA,GAAAH,iBAAA,CAWhC,aAAoC;QAClC,OAAO9C,wBAAwB,CAACkD,uBAAuB,EAAE;MAC3D,CAAC;MAAA,SAFYA,uBAAuBA,CAAA;QAAA,OAAAD,wBAAA,CAAAD,KAAA,OAAAtC,SAAA;MAAA;MAAA,OAAvBwC,uBAAuB;IAAA;EAAA;IAAApB,GAAA;IAAAC,KAAA;MAAA,IAAAoB,iBAAA,GAAAL,iBAAA,CA8BpC,WACEM,GAAW,EACwC;QAAA,IAAnDC,YAAA,GAAA3C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA4C,SAAA,GAAA5C,SAAA,MAAyB8B,MAAM,CAACe,MAAM,CAACrD,WAAW,CAAC;QAEnD,IAAI,CAACF,wBAAwB,CAACwD,gBAAgB,EAAE;UAC9C,MAAM,IAAI3D,mBAAmB,CAAC,sBAAsB,EAAE,kBAAkB,CAAC;;QAE3E,IAAIgB,KAAK,CAAC4C,OAAO,CAACJ,YAAY,CAAC,IAAI,CAACA,YAAY,CAAC1C,MAAM,EAAE;UACvD,MAAM,IAAI+C,KAAK,CAAC,yEAAyE,CAAC;;QAG5F,IAAI3D,QAAQ,CAAC4D,EAAE,KAAK,KAAK,EAAE;UACzB,IAAI9C,KAAK,CAAC4C,OAAO,CAACJ,YAAY,CAAC,IAAI,CAACA,YAAY,CAACO,QAAQ,CAAC1D,WAAW,CAAC2D,EAAE,CAAC,EAAE;YAEzE,MAAM,IAAIH,KAAK,CAAC,wDAAwD,CAAC;;UAG3E,aAAa1D,wBAAwB,CAACwD,gBAAgB,CAACJ,GAAG,EAAE,CAAClD,WAAW,CAAC2D,EAAE,CAAC,CAAC;;QAI/E,aAAa7D,wBAAwB,CAACwD,gBAAgB,CAACJ,GAAG,EAAEC,YAAY,CAAC;MAC3E,CAAC;MAAA,SAtBYG,gBAAgBA,CAAAM,EAAA;QAAA,OAAAX,iBAAA,CAAAH,KAAA,OAAAtC,SAAA;MAAA;MAAA,OAAhB8C,gBAAgB;IAAA;EAAA;AAAA,EAhEK1D,KAAK,CAACiE,SAA8B;kBAA3D1D,cAAe;AAAfA,cAAe,CAInB2D,SAAS,GAAG;EACjB9D,WAAW,EAAXA,WAAW;EACXC,IAAI,EAAJA;CACD;AAPUE,cAAe,CASnBuC,gBAAgB,GAAG;EACxBrB,IAAI,EAAEpB;CACP;AAXUE,cAAe,CAanB4D,YAAY,GAAG;EACpB1C,IAAI,EAAEpB,IAAI,CAAC+D,IAAI;EACfb,YAAY,EAAEb,MAAM,CAACe,MAAM,CAACrD,WAAW;CACxC;AAhBUG,cAAe,CAgDnB8D,cAAc,GAAGvE,oBAAoB,CAAC;EAC3CwE,SAAS,EAAE/D,eAAc,CAAC0C,mBAAmB;EAC7CsB,aAAa,EAAEhE,eAAc,CAAC6C;CAC/B,CAAC;AAyFJ,SAA6BvD,gBAAgB;AACtC,IAAQqE,SAAS,GACtB3D,cAAc,CADD2D,SAAS;EAAEjB,mBAAmB,GAC3C1C,cAAc,CADU0C,mBAAmB;EAAEG,uBAAuB,GACpE7C,cAAc,CAD+B6C,uBAAuB;EAAEM,gBAAgB,GACtFnD,cAAc,CADwDmD,gBAAgB;AACvE,SAAAQ,SAAA,EAAAjB,mBAAA,EAAAG,uBAAA,EAAAM,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}