{"ast": null, "code": "import color from 'color';\nexport function getTextColor(_ref) {\n  var _theme$colors;\n  var theme = _ref.theme,\n    disabled = _ref.disabled,\n    type = _ref.type;\n  var colors = theme.colors,\n    dark = theme.dark;\n  if (type === 'error') {\n    return colors === null || colors === void 0 ? void 0 : colors.error;\n  }\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    } else {\n      return theme.colors.onSurfaceVariant;\n    }\n  }\n  return color(theme === null || theme === void 0 || (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.text).alpha(dark ? 0.7 : 0.54).rgb().string();\n}", "map": {"version": 3, "names": ["color", "getTextColor", "_ref", "_theme$colors", "theme", "disabled", "type", "colors", "dark", "error", "isV3", "onSurfaceDisabled", "onSurfaceVariant", "text", "alpha", "rgb", "string"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/HelperText/utils.ts"], "sourcesContent": ["import color from 'color';\n\nimport type { InternalTheme } from '../../types';\n\ntype BaseProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n  type?: 'error' | 'info';\n};\n\nexport function getTextColor({ theme, disabled, type }: BaseProps) {\n  const { colors, dark } = theme;\n\n  if (type === 'error') {\n    return colors?.error;\n  }\n\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    } else {\n      return theme.colors.onSurfaceVariant;\n    }\n  }\n\n  return color(theme?.colors?.text)\n    .alpha(dark ? 0.7 : 0.54)\n    .rgb()\n    .string();\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAUzB,OAAO,SAASC,YAAYA,CAAAC,IAAA,EAAuC;EAAA,IAAAC,aAAA;EAAA,IAApCC,KAAK,GAA6BF,IAAA,CAAlCE,KAAK;IAAEC,QAAQ,GAAmBH,IAAA,CAA3BG,QAAQ;IAAEC,IAAA,GAAiBJ,IAAA,CAAjBI,IAAA;EAC9C,IAAQC,MAAM,GAAWH,KAAK,CAAtBG,MAAM;IAAEC,IAAA,GAASJ,KAAK,CAAdI,IAAA;EAEhB,IAAIF,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK;EACtB;EAEA,IAAIL,KAAK,CAACM,IAAI,EAAE;IACd,IAAIL,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACG,MAAM,CAACI,iBAAiB;IACvC,CAAC,MAAM;MACL,OAAOP,KAAK,CAACG,MAAM,CAACK,gBAAgB;IACtC;EACF;EAEA,OAAOZ,KAAK,CAACI,KAAK,aAALA,KAAK,gBAAAD,aAAA,GAALC,KAAK,CAAEG,MAAM,cAAAJ,aAAA,uBAAbA,aAAA,CAAeU,IAAI,CAAC,CAC9BC,KAAK,CAACN,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CACxBO,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}