{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nimport useNavigation from \"./useNavigation\";\nexport default function useNavigationState(selector) {\n  var navigation = useNavigation();\n  var _React$useState = React.useState(function () {\n      return selector(navigation.getState());\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    setResult = _React$useState2[1];\n  var selectorRef = React.useRef(selector);\n  React.useEffect(function () {\n    selectorRef.current = selector;\n  });\n  React.useEffect(function () {\n    var unsubscribe = navigation.addListener('state', function (e) {\n      setResult(selectorRef.current(e.data.state));\n    });\n    return unsubscribe;\n  }, [navigation]);\n  return selector(navigation.getState());\n}", "map": {"version": 3, "names": ["React", "useNavigation", "useNavigationState", "selector", "navigation", "_React$useState", "useState", "getState", "_React$useState2", "_slicedToArray", "setResult", "selectorRef", "useRef", "useEffect", "current", "unsubscribe", "addListener", "e", "data", "state"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/useNavigationState.tsx"], "sourcesContent": ["import type { NavigationState, ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport type { NavigationProp } from './types';\nimport useNavigation from './useNavigation';\n\ntype Selector<ParamList extends ParamListBase, T> = (\n  state: NavigationState<ParamList>\n) => T;\n\n/**\n * Hook to get a value from the current navigation state using a selector.\n *\n * @param selector Selector function to get a value from the state.\n */\nexport default function useNavigationState<ParamList extends ParamListBase, T>(\n  selector: Selector<ParamList, T>\n): T {\n  const navigation = useNavigation<NavigationProp<ParamList>>();\n\n  // We don't care about the state value, we run the selector again at the end\n  // The state is only to make sure that there's a re-render when we have a new value\n  const [, setResult] = React.useState(() => selector(navigation.getState()));\n\n  // We store the selector in a ref to avoid re-subscribing listeners every render\n  const selectorRef = React.useRef(selector);\n\n  React.useEffect(() => {\n    selectorRef.current = selector;\n  });\n\n  React.useEffect(() => {\n    const unsubscribe = navigation.addListener('state', (e) => {\n      setResult(selectorRef.current(e.data.state));\n    });\n\n    return unsubscribe;\n  }, [navigation]);\n\n  return selector(navigation.getState());\n}\n"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,OAAOC,aAAa;AAWpB,eAAe,SAASC,kBAAkBA,CACxCC,QAAgC,EAC7B;EACH,IAAMC,UAAU,GAAGH,aAAa,EAA6B;EAI7D,IAAAI,eAAA,GAAsBL,KAAK,CAACM,QAAQ,CAAC;MAAA,OAAMH,QAAQ,CAACC,UAAU,CAACG,QAAQ,EAAE,CAAC;IAAA,EAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;IAAlEK,SAAS,GAAAF,gBAAA;EAGlB,IAAMG,WAAW,GAAGX,KAAK,CAACY,MAAM,CAACT,QAAQ,CAAC;EAE1CH,KAAK,CAACa,SAAS,CAAC,YAAM;IACpBF,WAAW,CAACG,OAAO,GAAGX,QAAQ;EAChC,CAAC,CAAC;EAEFH,KAAK,CAACa,SAAS,CAAC,YAAM;IACpB,IAAME,WAAW,GAAGX,UAAU,CAACY,WAAW,CAAC,OAAO,EAAG,UAAAC,CAAC,EAAK;MACzDP,SAAS,CAACC,WAAW,CAACG,OAAO,CAACG,CAAC,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,OAAOJ,WAAW;EACpB,CAAC,EAAE,CAACX,UAAU,CAAC,CAAC;EAEhB,OAAOD,QAAQ,CAACC,UAAU,CAACG,QAAQ,EAAE,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}