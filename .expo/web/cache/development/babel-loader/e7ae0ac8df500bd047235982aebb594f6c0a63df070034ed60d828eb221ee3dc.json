{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"label\", \"background\", \"accessibilityLabel\", \"accessibilityState\", \"animated\", \"color\", \"rippleColor\", \"disabled\", \"onPress\", \"onLongPress\", \"delayLongPress\", \"theme\", \"style\", \"visible\", \"uppercase\", \"loading\", \"testID\", \"size\", \"customSize\", \"mode\", \"variant\", \"labelMaxFontSizeMultiplier\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { getExtendedFabStyle, getFABColors, getFabStyle } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nimport ActivityIndicator from \"../ActivityIndicator\";\nimport CrossFadeIcon from \"../CrossFadeIcon\";\nimport Icon from \"../Icon\";\nimport Surface from \"../Surface\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar FAB = forwardRef(function (_ref, ref) {\n  var icon = _ref.icon,\n    label = _ref.label,\n    background = _ref.background,\n    _ref$accessibilityLab = _ref.accessibilityLabel,\n    accessibilityLabel = _ref$accessibilityLab === void 0 ? label : _ref$accessibilityLab,\n    accessibilityState = _ref.accessibilityState,\n    _ref$animated = _ref.animated,\n    animated = _ref$animated === void 0 ? true : _ref$animated,\n    customColor = _ref.color,\n    customRippleColor = _ref.rippleColor,\n    disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    delayLongPress = _ref.delayLongPress,\n    themeOverrides = _ref.theme,\n    style = _ref.style,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible,\n    uppercaseProp = _ref.uppercase,\n    loading = _ref.loading,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'fab' : _ref$testID,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 'medium' : _ref$size,\n    customSize = _ref.customSize,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'elevated' : _ref$mode,\n    _ref$variant = _ref.variant,\n    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,\n    labelMaxFontSizeMultiplier = _ref.labelMaxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var uppercase = uppercaseProp != null ? uppercaseProp : !theme.isV3;\n  var _React$useRef = React.useRef(new Animated.Value(visible ? 1 : 0)),\n    visibility = _React$useRef.current;\n  var isV3 = theme.isV3,\n    animation = theme.animation;\n  var scale = animation.scale;\n  React.useEffect(function () {\n    if (visible) {\n      Animated.timing(visibility, {\n        toValue: 1,\n        duration: 200 * scale,\n        useNativeDriver: true\n      }).start();\n    } else {\n      Animated.timing(visibility, {\n        toValue: 0,\n        duration: 150 * scale,\n        useNativeDriver: true\n      }).start();\n    }\n  }, [visible, scale, visibility]);\n  var IconComponent = animated ? CrossFadeIcon : Icon;\n  var fabStyle = getFabStyle({\n    customSize: customSize,\n    size: size,\n    theme: theme\n  });\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$borderRadius = _ref2.borderRadius,\n    borderRadius = _ref2$borderRadius === void 0 ? fabStyle.borderRadius : _ref2$borderRadius,\n    customBackgroundColor = _ref2.backgroundColor;\n  var _getFABColors = getFABColors({\n      theme: theme,\n      variant: variant,\n      disabled: disabled,\n      customColor: customColor,\n      customBackgroundColor: customBackgroundColor,\n      customRippleColor: customRippleColor\n    }),\n    backgroundColor = _getFABColors.backgroundColor,\n    foregroundColor = _getFABColors.foregroundColor,\n    rippleColor = _getFABColors.rippleColor;\n  var isLargeSize = size === 'large';\n  var isFlatMode = mode === 'flat';\n  var iconSize = isLargeSize ? 36 : 24;\n  var loadingIndicatorSize = isLargeSize ? 24 : 18;\n  var font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n  var extendedStyle = getExtendedFabStyle({\n    customSize: customSize,\n    theme: theme\n  });\n  var textStyle = _objectSpread({\n    color: foregroundColor\n  }, font);\n  var md3Elevation = isFlatMode || disabled ? 0 : 3;\n  var newAccessibilityState = _objectSpread(_objectSpread({}, accessibilityState), {}, {\n    disabled: disabled\n  });\n  return React.createElement(Surface, _extends({\n    ref: ref\n  }, rest, {\n    style: [{\n      borderRadius: borderRadius,\n      backgroundColor: backgroundColor,\n      opacity: visibility,\n      transform: [{\n        scale: visibility\n      }]\n    }, !isV3 && styles.elevated, !isV3 && disabled && styles.disabled, style],\n    pointerEvents: visible ? 'auto' : 'none',\n    testID: `${testID}-container`\n  }, isV3 && {\n    elevation: md3Elevation\n  }), React.createElement(TouchableRipple, _extends({\n    borderless: true,\n    background: background,\n    onPress: onPress,\n    onLongPress: onLongPress,\n    delayLongPress: delayLongPress,\n    rippleColor: rippleColor,\n    disabled: disabled,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"button\",\n    accessibilityState: newAccessibilityState,\n    testID: testID,\n    style: {\n      borderRadius: borderRadius\n    }\n  }, rest), React.createElement(View, {\n    style: [styles.content, label ? extendedStyle : fabStyle],\n    testID: `${testID}-content`,\n    pointerEvents: \"none\"\n  }, icon && loading !== true ? React.createElement(IconComponent, {\n    source: icon,\n    size: customSize ? customSize / 2 : iconSize,\n    color: foregroundColor\n  }) : null, loading ? React.createElement(ActivityIndicator, {\n    size: customSize ? customSize / 2 : loadingIndicatorSize,\n    color: foregroundColor\n  }) : null, label ? React.createElement(Text, {\n    variant: \"labelLarge\",\n    selectable: false,\n    testID: `${testID}-text`,\n    style: [styles.label, uppercase && styles.uppercaseLabel, textStyle],\n    maxFontSizeMultiplier: labelMaxFontSizeMultiplier\n  }, label) : null)));\n});\nvar styles = StyleSheet.create({\n  elevated: {\n    elevation: 6\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  label: {\n    marginHorizontal: 8\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase'\n  },\n  disabled: {\n    elevation: 0\n  }\n});\nexport default FAB;\nexport { FAB };", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "View", "getExtendedFabStyle", "getFABColors", "getFabStyle", "useInternalTheme", "forwardRef", "ActivityIndicator", "CrossFadeIcon", "Icon", "Surface", "TouchableRipple", "Text", "FAB", "_ref", "ref", "icon", "label", "background", "_ref$accessibilityLab", "accessibilityLabel", "accessibilityState", "_ref$animated", "animated", "customColor", "color", "customRippleColor", "rippleColor", "disabled", "onPress", "onLongPress", "delayLongPress", "themeOverrides", "theme", "style", "_ref$visible", "visible", "uppercaseProp", "uppercase", "loading", "_ref$testID", "testID", "_ref$size", "size", "customSize", "_ref$mode", "mode", "_ref$variant", "variant", "labelMaxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "isV3", "_React$useRef", "useRef", "Value", "visibility", "current", "animation", "scale", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "IconComponent", "fabStyle", "_ref2", "flatten", "_ref2$borderRadius", "borderRadius", "customBackgroundColor", "backgroundColor", "_getFABColors", "foregroundColor", "isLargeSize", "isFlatMode", "iconSize", "loadingIndicatorSize", "font", "fonts", "labelLarge", "medium", "extendedStyle", "textStyle", "_objectSpread", "md3Elevation", "newAccessibilityState", "createElement", "_extends", "opacity", "transform", "styles", "elevated", "pointerEvents", "elevation", "borderless", "accessibilityRole", "content", "source", "selectable", "uppercase<PERSON>abel", "maxFontSizeMultiplier", "create", "flexDirection", "alignItems", "justifyContent", "marginHorizontal", "textTransform"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/FAB/FAB.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  AccessibilityState,\n  Animated,\n  ColorValue,\n  GestureResponderEvent,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { getExtendedFabStyle, getFABColors, getFabStyle } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $Omit, $RemoveChildren, ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\nimport ActivityIndicator from '../ActivityIndicator';\nimport CrossFadeIcon from '../CrossFadeIcon';\nimport Icon, { IconSource } from '../Icon';\nimport Surface from '../Surface';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\ntype FABSize = 'small' | 'medium' | 'large';\n\ntype FABMode = 'flat' | 'elevated';\n\ntype IconOrLabel =\n  | {\n      icon: IconSource;\n      label?: string;\n    }\n  | {\n      icon?: IconSource;\n      label: string;\n    };\n\nexport type Props = $Omit<$RemoveChildren<typeof Surface>, 'mode'> & {\n  // For `icon` and `label` props their types are duplicated due to the generation of documentation.\n  // Appropriate type for them is `IconOrLabel` contains the both union and intersection types.\n  /**\n   * Icon to display for the `FAB`. It's optional only if `label` is defined.\n   */\n  icon?: IconSource;\n  /**\n   * Optional label for extended `FAB`. It's optional only if `icon` is defined.\n   */\n  label?: string;\n  /**\n   * Make the label text uppercased.\n   */\n  uppercase?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the FAB. This is read by the screen reader when the user taps the FAB.\n   * Uses `label` by default if specified.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Accessibility state for the FAB. This is read by the screen reader when the user taps the FAB.\n   */\n  accessibilityState?: AccessibilityState;\n  /**\n   * Whether an icon change is animated.\n   */\n  animated?: boolean;\n  /**\n   *  @deprecated Deprecated in v.5x - use prop size=\"small\".\n   *\n   *  Whether FAB is mini-sized, used to create visual continuity with other elements. This has no effect if `label` is specified.\n   */\n  small?: boolean;\n  /**\n   * Custom color for the icon and label of the `FAB`.\n   */\n  color?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Whether `FAB` is disabled. A disabled button is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Whether `FAB` is currently visible.\n   */\n  visible?: boolean;\n  /**\n   * Whether to show a loading indicator.\n   */\n  loading?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * The number of milliseconds a user must touch the element before executing `onLongPress`.\n   */\n  delayLongPress?: number;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Size of the `FAB`.\n   * - `small` - FAB with small height (40).\n   * - `medium` - FAB with default medium height (56).\n   * - `large` - FAB with large height (96).\n   */\n  size?: FABSize;\n  /**\n   * Custom size for the `FAB`. This prop takes precedence over size prop\n   */\n  customSize?: number;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Mode of the `FAB`. You can change the mode to adjust the the shadow:\n   * - `flat` - button without a shadow.\n   * - `elevated` - button with a shadow.\n   */\n  mode?: FABMode;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Color mappings variant for combinations of container and icon colors.\n   */\n  variant?: 'primary' | 'secondary' | 'tertiary' | 'surface';\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  ref?: React.RefObject<View>;\n} & IconOrLabel;\n\n/**\n * A floating action button represents the primary action on a screen. It appears in front of all screen content.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { StyleSheet } from 'react-native';\n * import { FAB } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <FAB\n *     icon=\"plus\"\n *     style={styles.fab}\n *     onPress={() => console.log('Pressed')}\n *   />\n * );\n *\n * const styles = StyleSheet.create({\n *   fab: {\n *     position: 'absolute',\n *     margin: 16,\n *     right: 0,\n *     bottom: 0,\n *   },\n * })\n *\n * export default MyComponent;\n * ```\n */\nconst FAB = forwardRef<View, Props>(\n  (\n    {\n      icon,\n      label,\n      background,\n      accessibilityLabel = label,\n      accessibilityState,\n      animated = true,\n      color: customColor,\n      rippleColor: customRippleColor,\n      disabled,\n      onPress,\n      onLongPress,\n      delayLongPress,\n      theme: themeOverrides,\n      style,\n      visible = true,\n      uppercase: uppercaseProp,\n      loading,\n      testID = 'fab',\n      size = 'medium',\n      customSize,\n      mode = 'elevated',\n      variant = 'primary',\n      labelMaxFontSizeMultiplier,\n      ...rest\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(themeOverrides);\n    const uppercase = uppercaseProp ?? !theme.isV3;\n    const { current: visibility } = React.useRef<Animated.Value>(\n      new Animated.Value(visible ? 1 : 0)\n    );\n    const { isV3, animation } = theme;\n    const { scale } = animation;\n\n    React.useEffect(() => {\n      if (visible) {\n        Animated.timing(visibility, {\n          toValue: 1,\n          duration: 200 * scale,\n          useNativeDriver: true,\n        }).start();\n      } else {\n        Animated.timing(visibility, {\n          toValue: 0,\n          duration: 150 * scale,\n          useNativeDriver: true,\n        }).start();\n      }\n    }, [visible, scale, visibility]);\n\n    const IconComponent = animated ? CrossFadeIcon : Icon;\n\n    const fabStyle = getFabStyle({ customSize, size, theme });\n\n    const {\n      borderRadius = fabStyle.borderRadius,\n      backgroundColor: customBackgroundColor,\n    } = (StyleSheet.flatten(style) || {}) as ViewStyle;\n\n    const { backgroundColor, foregroundColor, rippleColor } = getFABColors({\n      theme,\n      variant,\n      disabled,\n      customColor,\n      customBackgroundColor,\n      customRippleColor,\n    });\n\n    const isLargeSize = size === 'large';\n    const isFlatMode = mode === 'flat';\n    const iconSize = isLargeSize ? 36 : 24;\n    const loadingIndicatorSize = isLargeSize ? 24 : 18;\n    const font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n\n    const extendedStyle = getExtendedFabStyle({ customSize, theme });\n    const textStyle = {\n      color: foregroundColor,\n      ...font,\n    };\n\n    const md3Elevation = isFlatMode || disabled ? 0 : 3;\n\n    const newAccessibilityState = { ...accessibilityState, disabled };\n\n    return (\n      <Surface\n        ref={ref}\n        {...rest}\n        style={[\n          {\n            borderRadius,\n            backgroundColor,\n            opacity: visibility,\n            transform: [\n              {\n                scale: visibility,\n              },\n            ],\n          },\n          !isV3 && styles.elevated,\n          !isV3 && disabled && styles.disabled,\n          style,\n        ]}\n        pointerEvents={visible ? 'auto' : 'none'}\n        testID={`${testID}-container`}\n        {...(isV3 && { elevation: md3Elevation })}\n      >\n        <TouchableRipple\n          borderless\n          background={background}\n          onPress={onPress}\n          onLongPress={onLongPress}\n          delayLongPress={delayLongPress}\n          rippleColor={rippleColor}\n          disabled={disabled}\n          accessibilityLabel={accessibilityLabel}\n          accessibilityRole=\"button\"\n          accessibilityState={newAccessibilityState}\n          testID={testID}\n          style={{ borderRadius }}\n          {...rest}\n        >\n          <View\n            style={[styles.content, label ? extendedStyle : fabStyle]}\n            testID={`${testID}-content`}\n            pointerEvents=\"none\"\n          >\n            {icon && loading !== true ? (\n              <IconComponent\n                source={icon}\n                size={customSize ? customSize / 2 : iconSize}\n                color={foregroundColor}\n              />\n            ) : null}\n            {loading ? (\n              <ActivityIndicator\n                size={customSize ? customSize / 2 : loadingIndicatorSize}\n                color={foregroundColor}\n              />\n            ) : null}\n            {label ? (\n              <Text\n                variant=\"labelLarge\"\n                selectable={false}\n                testID={`${testID}-text`}\n                style={[\n                  styles.label,\n                  uppercase && styles.uppercaseLabel,\n                  textStyle,\n                ]}\n                maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n              >\n                {label}\n              </Text>\n            ) : null}\n          </View>\n        </TouchableRipple>\n      </Surface>\n    );\n  }\n);\n\nconst styles = StyleSheet.create({\n  elevated: {\n    elevation: 6,\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  label: {\n    marginHorizontal: 8,\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase',\n  },\n  disabled: {\n    elevation: 0,\n  },\n});\n\nexport default FAB;\n\n// @component-docs ignore-next-line\nexport { FAB };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAa9B,SAASC,mBAAmB,EAAEC,YAAY,EAAEC,WAAW;AACvD,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AACnB,OAAOC,iBAAiB;AACxB,OAAOC,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,eAAe;AACtB,OAAOC,IAAI;AA+JX,IAAMC,GAAG,GAAGP,UAAU,CACpB,UAAAQ,IAAA,EA2BEC,GAAG,EACA;EAAA,IA1BDC,IAAI,GAwBEF,IAAA,CAxBNE,IAAI;IACJC,KAAK,GAuBCH,IAAA,CAvBNG,KAAK;IACLC,UAAU,GAsBJJ,IAAA,CAtBNI,UAAU;IAAAC,qBAAA,GAsBJL,IAAA,CArBNM,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGF,KAAK,GAAAE,qBAAA;IAC1BE,kBAAkB,GAoBZP,IAAA,CApBNO,kBAAkB;IAAAC,aAAA,GAoBZR,IAAA,CAnBNS,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACRE,WAAW,GAkBZV,IAAA,CAlBNW,KAAK;IACQC,iBAAiB,GAiBxBZ,IAAA,CAjBNa,WAAW;IACXC,QAAQ,GAgBFd,IAAA,CAhBNc,QAAQ;IACRC,OAAO,GAeDf,IAAA,CAfNe,OAAO;IACPC,WAAW,GAcLhB,IAAA,CAdNgB,WAAW;IACXC,cAAc,GAaRjB,IAAA,CAbNiB,cAAc;IACPC,cAAc,GAYflB,IAAA,CAZNmB,KAAK;IACLC,KAAK,GAWCpB,IAAA,CAXNoB,KAAK;IAAAC,YAAA,GAWCrB,IAAA,CAVNsB,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IACHE,aAAa,GASlBvB,IAAA,CATNwB,SAAS;IACTC,OAAO,GAQDzB,IAAA,CARNyB,OAAO;IAAAC,WAAA,GAQD1B,IAAA,CAPN2B,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,KAAK,GAAAA,WAAA;IAAAE,SAAA,GAOR5B,IAAA,CANN6B,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,QAAQ,GAAAA,SAAA;IACfE,UAAU,GAKJ9B,IAAA,CALN8B,UAAU;IAAAC,SAAA,GAKJ/B,IAAA,CAJNgC,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,UAAU,GAAAA,SAAA;IAAAE,YAAA,GAIXjC,IAAA,CAHNkC,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,SAAS,GAAAA,YAAA;IACnBE,0BAA0B,GAEpBnC,IAAA,CAFNmC,0BAA0B;IACvBC,IAAA,GAAAC,wBAAA,CACGrC,IAAA,EAAAsC,SAAA;EAGR,IAAMnB,KAAK,GAAG5B,gBAAgB,CAAC2B,cAAc,CAAC;EAC9C,IAAMM,SAAS,GAAGD,aAAa,WAAbA,aAAa,GAAI,CAACJ,KAAK,CAACoB,IAAI;EAC9C,IAAAC,aAAA,GAAgCxD,KAAK,CAACyD,MAAM,CAC1C,IAAIxD,QAAQ,CAACyD,KAAK,CAACpB,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;IAFgBqB,UAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAQL,IAAI,GAAgBpB,KAAK,CAAzBoB,IAAI;IAAEM,SAAA,GAAc1B,KAAK,CAAnB0B,SAAA;EACd,IAAQC,KAAA,GAAUD,SAAS,CAAnBC,KAAA;EAER9D,KAAK,CAAC+D,SAAS,CAAC,YAAM;IACpB,IAAIzB,OAAO,EAAE;MACXrC,QAAQ,CAAC+D,MAAM,CAACL,UAAU,EAAE;QAC1BM,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGJ,KAAK;QACrBK,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACLnE,QAAQ,CAAC+D,MAAM,CAACL,UAAU,EAAE;QAC1BM,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGJ,KAAK;QACrBK,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAC9B,OAAO,EAAEwB,KAAK,EAAEH,UAAU,CAAC,CAAC;EAEhC,IAAMU,aAAa,GAAG5C,QAAQ,GAAGf,aAAa,GAAGC,IAAI;EAErD,IAAM2D,QAAQ,GAAGhE,WAAW,CAAC;IAAEwC,UAAU,EAAVA,UAAU;IAAED,IAAI,EAAJA,IAAI;IAAEV,KAAA,EAAAA;EAAM,CAAC,CAAC;EAEzD,IAAAoC,KAAA,GAGKrE,UAAU,CAACsE,OAAO,CAACpC,KAAK,CAAC,IAAI,CAAC,CAAe;IAAAqC,kBAAA,GAAAF,KAAA,CAFhDG,YAAY;IAAZA,YAAY,GAAAD,kBAAA,cAAGH,QAAQ,CAACI,YAAY,GAAAD,kBAAA;IACnBE,qBAAA,GAAAJ,KAAA,CAAjBK,eAAe;EAGjB,IAAAC,aAAA,GAA0DxE,YAAY,CAAC;MACrE8B,KAAK,EAALA,KAAK;MACLe,OAAO,EAAPA,OAAO;MACPpB,QAAQ,EAARA,QAAQ;MACRJ,WAAW,EAAXA,WAAW;MACXiD,qBAAqB,EAArBA,qBAAqB;MACrB/C,iBAAA,EAAAA;IACF,CAAC,CAAC;IAPMgD,eAAe,GAAAC,aAAA,CAAfD,eAAe;IAAEE,eAAe,GAAAD,aAAA,CAAfC,eAAe;IAAEjD,WAAA,GAAAgD,aAAA,CAAAhD,WAAA;EAS1C,IAAMkD,WAAW,GAAGlC,IAAI,KAAK,OAAO;EACpC,IAAMmC,UAAU,GAAGhC,IAAI,KAAK,MAAM;EAClC,IAAMiC,QAAQ,GAAGF,WAAW,GAAG,EAAE,GAAG,EAAE;EACtC,IAAMG,oBAAoB,GAAGH,WAAW,GAAG,EAAE,GAAG,EAAE;EAClD,IAAMI,IAAI,GAAG5B,IAAI,GAAGpB,KAAK,CAACiD,KAAK,CAACC,UAAU,GAAGlD,KAAK,CAACiD,KAAK,CAACE,MAAM;EAE/D,IAAMC,aAAa,GAAGnF,mBAAmB,CAAC;IAAE0C,UAAU,EAAVA,UAAU;IAAEX,KAAA,EAAAA;EAAM,CAAC,CAAC;EAChE,IAAMqD,SAAS,GAAAC,aAAA;IACb9D,KAAK,EAAEmD;EAAe,GACnBK,IAAA,CACJ;EAED,IAAMO,YAAY,GAAGV,UAAU,IAAIlD,QAAQ,GAAG,CAAC,GAAG,CAAC;EAEnD,IAAM6D,qBAAqB,GAAAF,aAAA,CAAAA,aAAA,KAAQlE,kBAAkB;IAAEO,QAAA,EAAAA;EAAA,EAAU;EAEjE,OACE9B,KAAA,CAAA4F,aAAA,CAAChF,OAAO,EAAAiF,QAAA;IACN5E,GAAG,EAAEA;EAAI,GACLmC,IAAI;IACRhB,KAAK,EAAE,CACL;MACEsC,YAAY,EAAZA,YAAY;MACZE,eAAe,EAAfA,eAAe;MACfkB,OAAO,EAAEnC,UAAU;MACnBoC,SAAS,EAAE,CACT;QACEjC,KAAK,EAAEH;MACT,CAAC;IAEL,CAAC,EACD,CAACJ,IAAI,IAAIyC,MAAM,CAACC,QAAQ,EACxB,CAAC1C,IAAI,IAAIzB,QAAQ,IAAIkE,MAAM,CAAClE,QAAQ,EACpCM,KAAK,CACL;IACF8D,aAAa,EAAE5D,OAAO,GAAG,MAAM,GAAG,MAAO;IACzCK,MAAM,EAAG,GAAEA,MAAO;EAAY,GACzBY,IAAI,IAAI;IAAE4C,SAAS,EAAET;EAAa,CAAC,GAExC1F,KAAA,CAAA4F,aAAA,CAAC/E,eAAe,EAAAgF,QAAA;IACdO,UAAU;IACVhF,UAAU,EAAEA,UAAW;IACvBW,OAAO,EAAEA,OAAQ;IACjBC,WAAW,EAAEA,WAAY;IACzBC,cAAc,EAAEA,cAAe;IAC/BJ,WAAW,EAAEA,WAAY;IACzBC,QAAQ,EAAEA,QAAS;IACnBR,kBAAkB,EAAEA,kBAAmB;IACvC+E,iBAAiB,EAAC,QAAQ;IAC1B9E,kBAAkB,EAAEoE,qBAAsB;IAC1ChD,MAAM,EAAEA,MAAO;IACfP,KAAK,EAAE;MAAEsC,YAAA,EAAAA;IAAa;EAAE,GACpBtB,IAAI,GAERpD,KAAA,CAAA4F,aAAA,CAACzF,IAAI;IACHiC,KAAK,EAAE,CAAC4D,MAAM,CAACM,OAAO,EAAEnF,KAAK,GAAGoE,aAAa,GAAGjB,QAAQ,CAAE;IAC1D3B,MAAM,EAAG,GAAEA,MAAO,UAAU;IAC5BuD,aAAa,EAAC;EAAM,GAEnBhF,IAAI,IAAIuB,OAAO,KAAK,IAAI,GACvBzC,KAAA,CAAA4F,aAAA,CAACvB,aAAa;IACZkC,MAAM,EAAErF,IAAK;IACb2B,IAAI,EAAEC,UAAU,GAAGA,UAAU,GAAG,CAAC,GAAGmC,QAAS;IAC7CtD,KAAK,EAAEmD;EAAgB,CACxB,CAAC,GACA,IAAI,EACPrC,OAAO,GACNzC,KAAA,CAAA4F,aAAA,CAACnF,iBAAiB;IAChBoC,IAAI,EAAEC,UAAU,GAAGA,UAAU,GAAG,CAAC,GAAGoC,oBAAqB;IACzDvD,KAAK,EAAEmD;EAAgB,CACxB,CAAC,GACA,IAAI,EACP3D,KAAK,GACJnB,KAAA,CAAA4F,aAAA,CAAC9E,IAAI;IACHoC,OAAO,EAAC,YAAY;IACpBsD,UAAU,EAAE,KAAM;IAClB7D,MAAM,EAAG,GAAEA,MAAO,OAAO;IACzBP,KAAK,EAAE,CACL4D,MAAM,CAAC7E,KAAK,EACZqB,SAAS,IAAIwD,MAAM,CAACS,cAAc,EAClCjB,SAAS,CACT;IACFkB,qBAAqB,EAAEvD;EAA2B,GAEjDhC,KACG,CAAC,GACL,IACA,CACS,CACV,CAAC;AAEd,CACF,CAAC;AAED,IAAM6E,MAAM,GAAG9F,UAAU,CAACyG,MAAM,CAAC;EAC/BV,QAAQ,EAAE;IACRE,SAAS,EAAE;EACb,CAAC;EACDG,OAAO,EAAE;IACPM,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACD3F,KAAK,EAAE;IACL4F,gBAAgB,EAAE;EACpB,CAAC;EACDN,cAAc,EAAE;IACdO,aAAa,EAAE;EACjB,CAAC;EACDlF,QAAQ,EAAE;IACRqE,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAepF,GAAG;AAGlB,SAASA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}