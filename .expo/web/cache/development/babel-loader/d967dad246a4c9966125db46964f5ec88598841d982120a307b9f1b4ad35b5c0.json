{"ast": null, "code": "import NativeModules from \"react-native-web/dist/exports/NativeModules\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { registerWebGlobals } from \"./web\";\nexport function ensureNativeModulesAreInstalled() {\n  if (globalThis.expo) {\n    return;\n  }\n  try {\n    if (Platform.OS === 'web') {\n      registerWebGlobals();\n    } else {\n      var _NativeModules$ExpoMo;\n      (_NativeModules$ExpoMo = NativeModules.ExpoModulesCore) == null ? void 0 : _NativeModules$ExpoMo.installModules();\n    }\n  } catch (error) {\n    console.error(`Unable to install Expo modules: ${error}`);\n  }\n}", "map": {"version": 3, "names": ["registerWebGlobals", "ensureNativeModulesAreInstalled", "globalThis", "expo", "Platform", "OS", "_NativeModules$ExpoMo", "NativeModules", "ExpoModulesCore", "installModules", "error", "console"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts"], "sourcesContent": ["import { NativeModules, Platform } from 'react-native';\n\nimport { registerWebGlobals } from './web';\n\n/**\n * Ensures that the native modules are installed in the current runtime.\n * Otherwise, it synchronously calls a native function that installs them.\n */\nexport function ensureNativeModulesAreInstalled(): void {\n  if (globalThis.expo) {\n    return;\n  }\n  try {\n    if (Platform.OS === 'web') {\n      // Requiring web folder sets up the `globalThis.expo` object.\n      registerWebGlobals();\n    } else {\n      // TODO: ExpoModulesCore shouldn't be optional here,\n      // but to keep backwards compatibility let's just ignore it in SDK 50.\n      // In most cases the modules were already installed from the native side.\n      NativeModules.ExpoModulesCore?.installModules();\n    }\n  } catch (error) {\n    console.error(`Unable to install Expo modules: ${error}`);\n  }\n}\n"], "mappings": ";;AAEA,SAASA,kBAAkB;AAM3B,OAAO,SAASC,+BAA+BA,CAAA,EAAS;EACtD,IAAIC,UAAU,CAACC,IAAI,EAAE;IACnB;EACF;EACA,IAAI;IACF,IAAIC,QAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MAEzBL,kBAAkB,CAAC,CAAC;IACtB,CAAC,MAAM;MAAA,IAAAM,qBAAA;MAIL,CAAAA,qBAAA,GAAAC,aAAa,CAACC,eAAe,qBAA7BF,qBAAA,CAA+BG,cAAc,CAAC,CAAC;IACjD;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmCA,KAAK,EAAE,CAAC;EAC3D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}