{"ast": null, "code": "var rng = require(\"./lib/rng\");\nvar bytesToUuid = require(\"./lib/bytesToUuid\");\nvar _nodeId;\nvar _clockseq;\nvar _lastMSecs = 0;\nvar _lastNSecs = 0;\nfunction v1(options, buf, offset) {\n  var i = buf && offset || 0;\n  var b = buf || [];\n  options = options || {};\n  var node = options.node || _nodeId;\n  var clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq;\n  if (node == null || clockseq == null) {\n    var seedBytes = rng();\n    if (node == null) {\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n    if (clockseq == null) {\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  }\n  var msecs = options.msecs !== undefined ? options.msecs : new Date().getTime();\n  var nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1;\n  var dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000;\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  }\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  }\n  if (nsecs >= 10000) {\n    throw new Error('uuid.v1(): Can\\'t create more than 10M uuids/sec');\n  }\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq;\n  msecs += 12219292800000;\n  var tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff;\n  var tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff;\n  b[i++] = tmh >>> 24 & 0xf | 0x10;\n  b[i++] = tmh >>> 16 & 0xff;\n  b[i++] = clockseq >>> 8 | 0x80;\n  b[i++] = clockseq & 0xff;\n  for (var n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n  return buf ? buf : bytesToUuid(b);\n}\nmodule.exports = v1;", "map": {"version": 3, "names": ["rng", "require", "bytesToUuid", "_nodeId", "_clockseq", "_lastMSecs", "_lastNSecs", "v1", "options", "buf", "offset", "i", "b", "node", "clockseq", "undefined", "seedBytes", "msecs", "Date", "getTime", "nsecs", "dt", "Error", "tl", "tmh", "n", "module", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-constants/node_modules/uuid/v1.js"], "sourcesContent": ["var rng = require('./lib/rng');\nvar bytesToUuid = require('./lib/bytesToUuid');\n\n// **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nvar _nodeId;\nvar _clockseq;\n\n// Previous uuid creation time\nvar _lastMSecs = 0;\nvar _lastNSecs = 0;\n\n// See https://github.com/uuidjs/uuid for API details\nfunction v1(options, buf, offset) {\n  var i = buf && offset || 0;\n  var b = buf || [];\n\n  options = options || {};\n  var node = options.node || _nodeId;\n  var clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq;\n\n  // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n  if (node == null || clockseq == null) {\n    var seedBytes = rng();\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [\n        seedBytes[0] | 0x01,\n        seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]\n      ];\n    }\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  }\n\n  // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n  var msecs = options.msecs !== undefined ? options.msecs : new Date().getTime();\n\n  // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n  var nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1;\n\n  // Time since last uuid creation (in msecs)\n  var dt = (msecs - _lastMSecs) + (nsecs - _lastNSecs)/10000;\n\n  // Per 4.2.1.2, Bump clockseq on clock regression\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  }\n\n  // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  }\n\n  // Per 4.2.1.2 Throw error if too many uuids are requested\n  if (nsecs >= 10000) {\n    throw new Error('uuid.v1(): Can\\'t create more than 10M uuids/sec');\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq;\n\n  // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n  msecs += 12219292800000;\n\n  // `time_low`\n  var tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff;\n\n  // `time_mid`\n  var tmh = (msecs / 0x100000000 * 10000) & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff;\n\n  // `time_high_and_version`\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n  b[i++] = tmh >>> 16 & 0xff;\n\n  // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n  b[i++] = clockseq >>> 8 | 0x80;\n\n  // `clock_seq_low`\n  b[i++] = clockseq & 0xff;\n\n  // `node`\n  for (var n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf ? buf : bytesToUuid(b);\n}\n\nmodule.exports = v1;\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,OAAO,YAAY,CAAC;AAC9B,IAAIC,WAAW,GAAGD,OAAO,oBAAoB,CAAC;AAO9C,IAAIE,OAAO;AACX,IAAIC,SAAS;AAGb,IAAIC,UAAU,GAAG,CAAC;AAClB,IAAIC,UAAU,GAAG,CAAC;AAGlB,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAChC,IAAIC,CAAC,GAAGF,GAAG,IAAIC,MAAM,IAAI,CAAC;EAC1B,IAAIE,CAAC,GAAGH,GAAG,IAAI,EAAE;EAEjBD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIK,IAAI,GAAGL,OAAO,CAACK,IAAI,IAAIV,OAAO;EAClC,IAAIW,QAAQ,GAAGN,OAAO,CAACM,QAAQ,KAAKC,SAAS,GAAGP,OAAO,CAACM,QAAQ,GAAGV,SAAS;EAK5E,IAAIS,IAAI,IAAI,IAAI,IAAIC,QAAQ,IAAI,IAAI,EAAE;IACpC,IAAIE,SAAS,GAAGhB,GAAG,CAAC,CAAC;IACrB,IAAIa,IAAI,IAAI,IAAI,EAAE;MAEhBA,IAAI,GAAGV,OAAO,GAAG,CACfa,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,EACnBA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CACrE;IACH;IACA,IAAIF,QAAQ,IAAI,IAAI,EAAE;MAEpBA,QAAQ,GAAGV,SAAS,GAAG,CAACY,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,IAAI,MAAM;IACpE;EACF;EAMA,IAAIC,KAAK,GAAGT,OAAO,CAACS,KAAK,KAAKF,SAAS,GAAGP,OAAO,CAACS,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAI9E,IAAIC,KAAK,GAAGZ,OAAO,CAACY,KAAK,KAAKL,SAAS,GAAGP,OAAO,CAACY,KAAK,GAAGd,UAAU,GAAG,CAAC;EAGxE,IAAIe,EAAE,GAAIJ,KAAK,GAAGZ,UAAU,GAAI,CAACe,KAAK,GAAGd,UAAU,IAAE,KAAK;EAG1D,IAAIe,EAAE,GAAG,CAAC,IAAIb,OAAO,CAACM,QAAQ,KAAKC,SAAS,EAAE;IAC5CD,QAAQ,GAAGA,QAAQ,GAAG,CAAC,GAAG,MAAM;EAClC;EAIA,IAAI,CAACO,EAAE,GAAG,CAAC,IAAIJ,KAAK,GAAGZ,UAAU,KAAKG,OAAO,CAACY,KAAK,KAAKL,SAAS,EAAE;IACjEK,KAAK,GAAG,CAAC;EACX;EAGA,IAAIA,KAAK,IAAI,KAAK,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,kDAAkD,CAAC;EACrE;EAEAjB,UAAU,GAAGY,KAAK;EAClBX,UAAU,GAAGc,KAAK;EAClBhB,SAAS,GAAGU,QAAQ;EAGpBG,KAAK,IAAI,cAAc;EAGvB,IAAIM,EAAE,GAAG,CAAC,CAACN,KAAK,GAAG,SAAS,IAAI,KAAK,GAAGG,KAAK,IAAI,WAAW;EAC5DR,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGY,EAAE,KAAK,EAAE,GAAG,IAAI;EACzBX,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGY,EAAE,KAAK,EAAE,GAAG,IAAI;EACzBX,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGY,EAAE,KAAK,CAAC,GAAG,IAAI;EACxBX,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGY,EAAE,GAAG,IAAI;EAGlB,IAAIC,GAAG,GAAIP,KAAK,GAAG,WAAW,GAAG,KAAK,GAAI,SAAS;EACnDL,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGa,GAAG,KAAK,CAAC,GAAG,IAAI;EACzBZ,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGa,GAAG,GAAG,IAAI;EAGnBZ,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGa,GAAG,KAAK,EAAE,GAAG,GAAG,GAAG,IAAI;EAChCZ,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGa,GAAG,KAAK,EAAE,GAAG,IAAI;EAG1BZ,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGG,QAAQ,KAAK,CAAC,GAAG,IAAI;EAG9BF,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGG,QAAQ,GAAG,IAAI;EAGxB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC1Bb,CAAC,CAACD,CAAC,GAAGc,CAAC,CAAC,GAAGZ,IAAI,CAACY,CAAC,CAAC;EACpB;EAEA,OAAOhB,GAAG,GAAGA,GAAG,GAAGP,WAAW,CAACU,CAAC,CAAC;AACnC;AAEAc,MAAM,CAACC,OAAO,GAAGpB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}