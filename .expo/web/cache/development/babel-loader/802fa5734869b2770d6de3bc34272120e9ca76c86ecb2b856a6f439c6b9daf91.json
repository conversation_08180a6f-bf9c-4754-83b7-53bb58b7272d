{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nvar Alert = function () {\n  function Alert() {\n    _classCallCheck(this, Alert);\n  }\n  return _createClass(Alert, null, [{\n    key: \"alert\",\n    value: function alert() {}\n  }]);\n}();\nexport default Alert;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "_classCallCheck", "_createClass", "key", "value", "alert"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Alert/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nclass Alert {\n  static alert() {}\n}\nexport default Alert;"], "mappings": ";;IASMA,KAAK;EAAA,SAAAA,MAAA;IAAAC,eAAA,OAAAD,KAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,KAAA;IAAAG,GAAA;IAAAC,KAAA,EACT,SAAOC,KAAKA,CAAA,EAAG,CAAC;EAAC;AAAA;AAEnB,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}