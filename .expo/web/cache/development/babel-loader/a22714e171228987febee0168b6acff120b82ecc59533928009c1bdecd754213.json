{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport * as FileSystem from 'expo-file-system';\nimport { getCurrentUser, updateUserSettings } from \"./databaseService\";\nimport dbManager from \"../database/DatabaseManager\";\nvar SYNC_TIMESTAMP_KEY = 'last_sync_timestamp';\nvar SYNC_INTERVAL = 24 * 60 * 60 * 1000;\nexport var isSyncNeeded = function () {\n  var _ref = _asyncToGenerator(function* () {\n    try {\n      var lastSyncStr = yield AsyncStorage.getItem(SYNC_TIMESTAMP_KEY);\n      if (!lastSyncStr) {\n        return true;\n      }\n      var lastSync = new Date(lastSyncStr);\n      var now = new Date();\n      return now.getTime() - lastSync.getTime() > SYNC_INTERVAL;\n    } catch (error) {\n      console.error('Error checking sync status:', error);\n      return false;\n    }\n  });\n  return function isSyncNeeded() {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport var syncData = function () {\n  var _ref2 = _asyncToGenerator(function* () {\n    try {\n      var user = yield getCurrentUser();\n      if (!user) {\n        throw new Error('No user found');\n      }\n      var backupPath = yield backupDatabaseBeforeSync();\n      var now = new Date().toISOString();\n      yield AsyncStorage.setItem(SYNC_TIMESTAMP_KEY, now);\n      yield updateUserSettings(user.id, {\n        last_sync: now\n      });\n      return {\n        success: true,\n        timestamp: now,\n        message: 'Data synchronized successfully',\n        backupPath: backupPath\n      };\n    } catch (error) {\n      console.error('Error syncing data:', error);\n      throw error;\n    }\n  });\n  return function syncData() {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport var backupDatabaseBeforeSync = function () {\n  var _ref3 = _asyncToGenerator(function* () {\n    try {\n      return yield dbManager.backupDatabase();\n    } catch (error) {\n      console.error('Error backing up database before sync:', error);\n      throw error;\n    }\n  });\n  return function backupDatabaseBeforeSync() {\n    return _ref3.apply(this, arguments);\n  };\n}();\nexport var restoreFromBackup = function () {\n  var _ref4 = _asyncToGenerator(function* (backupPath) {\n    try {\n      dbManager.close();\n      var dbDir = FileSystem.documentDirectory + 'SQLite/';\n      var dbPath = dbDir + 'znunizaehler.db';\n      yield FileSystem.copyAsync({\n        from: backupPath,\n        to: dbPath\n      });\n      yield dbManager.init();\n      return true;\n    } catch (error) {\n      console.error('Error restoring from backup:', error);\n      throw error;\n    }\n  });\n  return function restoreFromBackup(_x) {\n    return _ref4.apply(this, arguments);\n  };\n}();\nexport var getAvailableBackups = function () {\n  var _ref5 = _asyncToGenerator(function* () {\n    try {\n      var backupDir = FileSystem.documentDirectory;\n      var files = yield FileSystem.readDirectoryAsync(backupDir);\n      var backupFiles = files.filter(function (file) {\n        return file.startsWith('backup_') && file.endsWith('.db');\n      });\n      var backups = yield Promise.all(backupFiles.map(function () {\n        var _ref6 = _asyncToGenerator(function* (file) {\n          var fileInfo = yield FileSystem.getInfoAsync(backupDir + file);\n          var timestampStr = file.replace('backup_', '').replace('.db', '');\n          var timestamp = new Date(timestampStr.replace(/_/g, ':').replace(/T/g, ' '));\n          return {\n            filename: file,\n            path: fileInfo.uri,\n            size: fileInfo.size,\n            timestamp: timestamp\n          };\n        });\n        return function (_x2) {\n          return _ref6.apply(this, arguments);\n        };\n      }()));\n      return backups.sort(function (a, b) {\n        return b.timestamp - a.timestamp;\n      });\n    } catch (error) {\n      console.error('Error getting available backups:', error);\n      throw error;\n    }\n  });\n  return function getAvailableBackups() {\n    return _ref5.apply(this, arguments);\n  };\n}();\nexport var deleteBackup = function () {\n  var _ref7 = _asyncToGenerator(function* (backupPath) {\n    try {\n      yield FileSystem.deleteAsync(backupPath);\n      return true;\n    } catch (error) {\n      console.error('Error deleting backup:', error);\n      throw error;\n    }\n  });\n  return function deleteBackup(_x3) {\n    return _ref7.apply(this, arguments);\n  };\n}();\nexport default {\n  isSyncNeeded: isSyncNeeded,\n  syncData: syncData,\n  backupDatabaseBeforeSync: backupDatabaseBeforeSync,\n  restoreFromBackup: restoreFromBackup,\n  getAvailableBackups: getAvailableBackups,\n  deleteBackup: deleteBackup\n};", "map": {"version": 3, "names": ["AsyncStorage", "FileSystem", "getCurrentUser", "updateUserSettings", "db<PERSON><PERSON><PERSON>", "SYNC_TIMESTAMP_KEY", "SYNC_INTERVAL", "isSyncNeeded", "_ref", "_asyncToGenerator", "lastSyncStr", "getItem", "lastSync", "Date", "now", "getTime", "error", "console", "apply", "arguments", "syncData", "_ref2", "user", "Error", "<PERSON><PERSON><PERSON>", "backupDatabaseBeforeSync", "toISOString", "setItem", "id", "last_sync", "success", "timestamp", "message", "_ref3", "backupDatabase", "restoreFromBackup", "_ref4", "close", "dbDir", "documentDirectory", "db<PERSON><PERSON>", "copyAsync", "from", "to", "init", "_x", "getAvailableBackups", "_ref5", "backupDir", "files", "readDirectoryAsync", "backupFiles", "filter", "file", "startsWith", "endsWith", "backups", "Promise", "all", "map", "_ref6", "fileInfo", "getInfoAsync", "timestampStr", "replace", "filename", "path", "uri", "size", "_x2", "sort", "a", "b", "deleteBackup", "_ref7", "deleteAsync", "_x3"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/services/syncService.js"], "sourcesContent": ["/**\n * Sync Service for ZnüniZähler\n * Provides functionality for data synchronization\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport * as FileSystem from 'expo-file-system';\nimport { getCurrentUser, updateUserSettings } from './databaseService';\nimport dbManager from '../database/DatabaseManager';\n\n// Constants\nconst SYNC_TIMESTAMP_KEY = 'last_sync_timestamp';\nconst SYNC_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n\n/**\n * Check if sync is needed\n * @returns {Promise<boolean>} - Whether sync is needed\n */\nexport const isSyncNeeded = async () => {\n  try {\n    // Get last sync timestamp\n    const lastSyncStr = await AsyncStorage.getItem(SYNC_TIMESTAMP_KEY);\n    \n    if (!lastSyncStr) {\n      return true; // No previous sync, sync needed\n    }\n    \n    const lastSync = new Date(lastSyncStr);\n    const now = new Date();\n    \n    // Check if sync interval has passed\n    return now.getTime() - lastSync.getTime() > SYNC_INTERVAL;\n  } catch (error) {\n    console.error('Error checking sync status:', error);\n    return false; // Default to no sync needed on error\n  }\n};\n\n/**\n * Sync data with cloud\n * @returns {Promise<Object>} - Sync result\n */\nexport const syncData = async () => {\n  try {\n    // Get current user\n    const user = await getCurrentUser();\n    \n    if (!user) {\n      throw new Error('No user found');\n    }\n    \n    // Backup database before sync\n    const backupPath = await backupDatabaseBeforeSync();\n    \n    // Perform sync operations\n    // This is a placeholder for actual sync implementation\n    // In a real app, you would:\n    // 1. Upload local changes to the server\n    // 2. Download remote changes from the server\n    // 3. Merge changes and resolve conflicts\n    \n    // For now, we'll just update the last sync timestamp\n    const now = new Date().toISOString();\n    await AsyncStorage.setItem(SYNC_TIMESTAMP_KEY, now);\n    \n    // Update user's last sync timestamp\n    await updateUserSettings(user.id, { last_sync: now });\n    \n    return {\n      success: true,\n      timestamp: now,\n      message: 'Data synchronized successfully',\n      backupPath\n    };\n  } catch (error) {\n    console.error('Error syncing data:', error);\n    throw error;\n  }\n};\n\n/**\n * Backup database before sync\n * @returns {Promise<string>} - Backup file path\n */\nexport const backupDatabaseBeforeSync = async () => {\n  try {\n    return await dbManager.backupDatabase();\n  } catch (error) {\n    console.error('Error backing up database before sync:', error);\n    throw error;\n  }\n};\n\n/**\n * Restore database from backup\n * @param {string} backupPath - Path to backup file\n * @returns {Promise<boolean>} - Success flag\n */\nexport const restoreFromBackup = async (backupPath) => {\n  try {\n    // Close current database connection\n    dbManager.close();\n    \n    // Get database file path\n    const dbDir = FileSystem.documentDirectory + 'SQLite/';\n    const dbPath = dbDir + 'znunizaehler.db';\n    \n    // Copy backup to database file\n    await FileSystem.copyAsync({\n      from: backupPath,\n      to: dbPath\n    });\n    \n    // Reinitialize database\n    await dbManager.init();\n    \n    return true;\n  } catch (error) {\n    console.error('Error restoring from backup:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all available backups\n * @returns {Promise<Array<Object>>} - Array of backup objects\n */\nexport const getAvailableBackups = async () => {\n  try {\n    const backupDir = FileSystem.documentDirectory;\n    const files = await FileSystem.readDirectoryAsync(backupDir);\n    \n    // Filter backup files\n    const backupFiles = files.filter(file => file.startsWith('backup_') && file.endsWith('.db'));\n    \n    // Get file info\n    const backups = await Promise.all(\n      backupFiles.map(async (file) => {\n        const fileInfo = await FileSystem.getInfoAsync(backupDir + file);\n        \n        // Parse timestamp from filename\n        const timestampStr = file.replace('backup_', '').replace('.db', '');\n        const timestamp = new Date(timestampStr.replace(/_/g, ':').replace(/T/g, ' '));\n        \n        return {\n          filename: file,\n          path: fileInfo.uri,\n          size: fileInfo.size,\n          timestamp\n        };\n      })\n    );\n    \n    // Sort by timestamp (newest first)\n    return backups.sort((a, b) => b.timestamp - a.timestamp);\n  } catch (error) {\n    console.error('Error getting available backups:', error);\n    throw error;\n  }\n};\n\n/**\n * Delete backup file\n * @param {string} backupPath - Path to backup file\n * @returns {Promise<boolean>} - Success flag\n */\nexport const deleteBackup = async (backupPath) => {\n  try {\n    await FileSystem.deleteAsync(backupPath);\n    return true;\n  } catch (error) {\n    console.error('Error deleting backup:', error);\n    throw error;\n  }\n};\n\nexport default {\n  isSyncNeeded,\n  syncData,\n  backupDatabaseBeforeSync,\n  restoreFromBackup,\n  getAvailableBackups,\n  deleteBackup\n};\n"], "mappings": ";AAKA,OAAOA,YAAY,MAAM,2CAA2C;AACpE,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,SAASC,cAAc,EAAEC,kBAAkB;AAC3C,OAAOC,SAAS;AAGhB,IAAMC,kBAAkB,GAAG,qBAAqB;AAChD,IAAMC,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAMzC,OAAO,IAAMC,YAAY;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;IACtC,IAAI;MAEF,IAAMC,WAAW,SAASV,YAAY,CAACW,OAAO,CAACN,kBAAkB,CAAC;MAElE,IAAI,CAACK,WAAW,EAAE;QAChB,OAAO,IAAI;MACb;MAEA,IAAME,QAAQ,GAAG,IAAIC,IAAI,CAACH,WAAW,CAAC;MACtC,IAAMI,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MAGtB,OAAOC,GAAG,CAACC,OAAO,CAAC,CAAC,GAAGH,QAAQ,CAACG,OAAO,CAAC,CAAC,GAAGT,aAAa;IAC3D,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,KAAK;IACd;EACF,CAAC;EAAA,gBAlBYT,YAAYA,CAAA;IAAA,OAAAC,IAAA,CAAAU,KAAA,OAAAC,SAAA;EAAA;AAAA,GAkBxB;AAMD,OAAO,IAAMC,QAAQ;EAAA,IAAAC,KAAA,GAAAZ,iBAAA,CAAG,aAAY;IAClC,IAAI;MAEF,IAAMa,IAAI,SAASpB,cAAc,CAAC,CAAC;MAEnC,IAAI,CAACoB,IAAI,EAAE;QACT,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;MAClC;MAGA,IAAMC,UAAU,SAASC,wBAAwB,CAAC,CAAC;MAUnD,IAAMX,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC;MACpC,MAAM1B,YAAY,CAAC2B,OAAO,CAACtB,kBAAkB,EAAES,GAAG,CAAC;MAGnD,MAAMX,kBAAkB,CAACmB,IAAI,CAACM,EAAE,EAAE;QAAEC,SAAS,EAAEf;MAAI,CAAC,CAAC;MAErD,OAAO;QACLgB,OAAO,EAAE,IAAI;QACbC,SAAS,EAAEjB,GAAG;QACdkB,OAAO,EAAE,gCAAgC;QACzCR,UAAU,EAAVA;MACF,CAAC;IACH,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBApCYI,QAAQA,CAAA;IAAA,OAAAC,KAAA,CAAAH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAoCpB;AAMD,OAAO,IAAMM,wBAAwB;EAAA,IAAAQ,KAAA,GAAAxB,iBAAA,CAAG,aAAY;IAClD,IAAI;MACF,aAAaL,SAAS,CAAC8B,cAAc,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYS,wBAAwBA,CAAA;IAAA,OAAAQ,KAAA,CAAAf,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOpC;AAOD,OAAO,IAAMgB,iBAAiB;EAAA,IAAAC,KAAA,GAAA3B,iBAAA,CAAG,WAAOe,UAAU,EAAK;IACrD,IAAI;MAEFpB,SAAS,CAACiC,KAAK,CAAC,CAAC;MAGjB,IAAMC,KAAK,GAAGrC,UAAU,CAACsC,iBAAiB,GAAG,SAAS;MACtD,IAAMC,MAAM,GAAGF,KAAK,GAAG,iBAAiB;MAGxC,MAAMrC,UAAU,CAACwC,SAAS,CAAC;QACzBC,IAAI,EAAElB,UAAU;QAChBmB,EAAE,EAAEH;MACN,CAAC,CAAC;MAGF,MAAMpC,SAAS,CAACwC,IAAI,CAAC,CAAC;MAEtB,OAAO,IAAI;IACb,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAvBYmB,iBAAiBA,CAAAU,EAAA;IAAA,OAAAT,KAAA,CAAAlB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAuB7B;AAMD,OAAO,IAAM2B,mBAAmB;EAAA,IAAAC,KAAA,GAAAtC,iBAAA,CAAG,aAAY;IAC7C,IAAI;MACF,IAAMuC,SAAS,GAAG/C,UAAU,CAACsC,iBAAiB;MAC9C,IAAMU,KAAK,SAAShD,UAAU,CAACiD,kBAAkB,CAACF,SAAS,CAAC;MAG5D,IAAMG,WAAW,GAAGF,KAAK,CAACG,MAAM,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,UAAU,CAAC,SAAS,CAAC,IAAID,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC;MAAA,EAAC;MAG5F,IAAMC,OAAO,SAASC,OAAO,CAACC,GAAG,CAC/BP,WAAW,CAACQ,GAAG;QAAA,IAAAC,KAAA,GAAAnD,iBAAA,CAAC,WAAO4C,IAAI,EAAK;UAC9B,IAAMQ,QAAQ,SAAS5D,UAAU,CAAC6D,YAAY,CAACd,SAAS,GAAGK,IAAI,CAAC;UAGhE,IAAMU,YAAY,GAAGV,IAAI,CAACW,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;UACnE,IAAMjC,SAAS,GAAG,IAAIlB,IAAI,CAACkD,YAAY,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;UAE9E,OAAO;YACLC,QAAQ,EAAEZ,IAAI;YACda,IAAI,EAAEL,QAAQ,CAACM,GAAG;YAClBC,IAAI,EAAEP,QAAQ,CAACO,IAAI;YACnBrC,SAAS,EAATA;UACF,CAAC;QACH,CAAC;QAAA,iBAAAsC,GAAA;UAAA,OAAAT,KAAA,CAAA1C,KAAA,OAAAC,SAAA;QAAA;MAAA,IACH,CAAC;MAGD,OAAOqC,OAAO,CAACc,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAACzC,SAAS,GAAGwC,CAAC,CAACxC,SAAS;MAAA,EAAC;IAC1D,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAhCY8B,mBAAmBA,CAAA;IAAA,OAAAC,KAAA,CAAA7B,KAAA,OAAAC,SAAA;EAAA;AAAA,GAgC/B;AAOD,OAAO,IAAMsD,YAAY;EAAA,IAAAC,KAAA,GAAAjE,iBAAA,CAAG,WAAOe,UAAU,EAAK;IAChD,IAAI;MACF,MAAMvB,UAAU,CAAC0E,WAAW,CAACnD,UAAU,CAAC;MACxC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBARYyD,YAAYA,CAAAG,GAAA;IAAA,OAAAF,KAAA,CAAAxD,KAAA,OAAAC,SAAA;EAAA;AAAA,GAQxB;AAED,eAAe;EACbZ,YAAY,EAAZA,YAAY;EACZa,QAAQ,EAARA,QAAQ;EACRK,wBAAwB,EAAxBA,wBAAwB;EACxBU,iBAAiB,EAAjBA,iBAAiB;EACjBW,mBAAmB,EAAnBA,mBAAmB;EACnB2B,YAAY,EAAZA;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}