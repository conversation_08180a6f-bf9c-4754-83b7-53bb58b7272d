{"ast": null, "code": "import LayoutAnimation from \"../../vendor/react-native/LayoutAnimation\";\nexport default LayoutAnimation;", "map": {"version": 3, "names": ["LayoutAnimation"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/LayoutAnimation/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport LayoutAnimation from '../../vendor/react-native/LayoutAnimation';\nexport default LayoutAnimation;"], "mappings": "AAUA,OAAOA,eAAe;AACtB,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}