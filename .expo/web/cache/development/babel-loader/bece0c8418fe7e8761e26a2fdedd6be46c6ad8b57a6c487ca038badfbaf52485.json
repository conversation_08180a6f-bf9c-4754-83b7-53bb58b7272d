{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"title\", \"theme\", \"style\", \"showDivider\", \"titleMaxFontSizeMultiplier\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { useInternalTheme } from \"../../core/theming\";\nimport { MD3Colors } from \"../../styles/themes/v3/tokens\";\nimport Divider from \"../Divider\";\nimport Text from \"../Typography/Text\";\nvar DrawerSection = function DrawerSection(_ref) {\n  var children = _ref.children,\n    title = _ref.title,\n    themeOverrides = _ref.theme,\n    style = _ref.style,\n    _ref$showDivider = _ref.showDivider,\n    showDivider = _ref$showDivider === void 0 ? true : _ref$showDivider,\n    titleMaxFontSizeMultiplier = _ref.titleMaxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3;\n  var titleColor = isV3 ? theme.colors.onSurfaceVariant : color(theme.colors.text).alpha(0.54).rgb().string();\n  var titleMargin = isV3 ? 28 : 16;\n  var font = isV3 ? theme.fonts.titleSmall : theme.fonts.medium;\n  return React.createElement(View, _extends({\n    style: [styles.container, style]\n  }, rest), title && React.createElement(View, {\n    style: [styles.titleContainer, isV3 && styles.v3TitleContainer]\n  }, title && React.createElement(Text, {\n    variant: \"titleSmall\",\n    numberOfLines: 1,\n    style: [_objectSpread({\n      color: titleColor,\n      marginLeft: titleMargin\n    }, font)],\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }, title)), children, showDivider && React.createElement(Divider, _extends({}, isV3 && {\n    horizontalInset: true,\n    bold: true\n  }, {\n    style: [styles.divider, isV3 && styles.v3Divider],\n    theme: theme\n  })));\n};\nDrawerSection.displayName = 'Drawer.Section';\nvar styles = StyleSheet.create({\n  container: {\n    marginBottom: 4\n  },\n  titleContainer: {\n    height: 40,\n    justifyContent: 'center'\n  },\n  v3TitleContainer: {\n    height: 56\n  },\n  divider: {\n    marginTop: 4\n  },\n  v3Divider: {\n    backgroundColor: MD3Colors.neutralVariant50\n  }\n});\nexport default DrawerSection;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "color", "useInternalTheme", "MD3Colors", "Divider", "Text", "DrawerSection", "_ref", "children", "title", "themeOverrides", "theme", "style", "_ref$showDivider", "showDivider", "titleMaxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "isV3", "titleColor", "colors", "onSurfaceVariant", "text", "alpha", "rgb", "string", "<PERSON><PERSON><PERSON><PERSON>", "font", "fonts", "titleSmall", "medium", "createElement", "_extends", "styles", "container", "<PERSON><PERSON><PERSON><PERSON>", "v3TitleContainer", "variant", "numberOfLines", "_objectSpread", "marginLeft", "maxFontSizeMultiplier", "horizontalInset", "bold", "divider", "v3Divider", "displayName", "create", "marginBottom", "height", "justifyContent", "marginTop", "backgroundColor", "neutralVariant50"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Drawer/DrawerSection.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport color from 'color';\n\nimport { useInternalTheme } from '../../core/theming';\nimport { MD3Colors } from '../../styles/themes/v3/tokens';\nimport type { ThemeProp } from '../../types';\nimport Divider from '../Divider';\nimport Text from '../Typography/Text';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Title to show as the header for the section.\n   */\n  title?: string;\n  /**\n   * Content of the `Drawer.Section`.\n   */\n  children: React.ReactNode;\n  /**\n   * Whether to show `Divider` at the end of the section. True by default.\n   */\n  showDivider?: boolean;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to group content inside a navigation drawer.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Drawer } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [active, setActive] = React.useState('');\n *\n *   return (\n *     <Drawer.Section title=\"Some title\">\n *       <Drawer.Item\n *         label=\"First Item\"\n *         active={active === 'first'}\n *         onPress={() => setActive('first')}\n *       />\n *       <Drawer.Item\n *         label=\"Second Item\"\n *         active={active === 'second'}\n *         onPress={() => setActive('second')}\n *       />\n *     </Drawer.Section>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst DrawerSection = ({\n  children,\n  title,\n  theme: themeOverrides,\n  style,\n  showDivider = true,\n  titleMaxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { isV3 } = theme;\n  const titleColor = isV3\n    ? theme.colors.onSurfaceVariant\n    : color(theme.colors.text).alpha(0.54).rgb().string();\n  const titleMargin = isV3 ? 28 : 16;\n  const font = isV3 ? theme.fonts.titleSmall : theme.fonts.medium;\n\n  return (\n    <View style={[styles.container, style]} {...rest}>\n      {title && (\n        <View style={[styles.titleContainer, isV3 && styles.v3TitleContainer]}>\n          {title && (\n            <Text\n              variant=\"titleSmall\"\n              numberOfLines={1}\n              style={[\n                {\n                  color: titleColor,\n                  marginLeft: titleMargin,\n                  ...font,\n                },\n              ]}\n              maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n            >\n              {title}\n            </Text>\n          )}\n        </View>\n      )}\n      {children}\n      {showDivider && (\n        <Divider\n          {...(isV3 && { horizontalInset: true, bold: true })}\n          style={[styles.divider, isV3 && styles.v3Divider]}\n          theme={theme}\n        />\n      )}\n    </View>\n  );\n};\n\nDrawerSection.displayName = 'Drawer.Section';\n\nconst styles = StyleSheet.create({\n  container: {\n    marginBottom: 4,\n  },\n  titleContainer: {\n    height: 40,\n    justifyContent: 'center',\n  },\n  v3TitleContainer: {\n    height: 56,\n  },\n  divider: {\n    marginTop: 4,\n  },\n  v3Divider: {\n    backgroundColor: MD3Colors.neutralVariant50,\n  },\n});\n\nexport default DrawerSection;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,gBAAgB;AACzB,SAASC,SAAS;AAElB,OAAOC,OAAO;AACd,OAAOC,IAAI;AAwDX,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAA,EAQT;EAAA,IAPXC,QAAQ,GAOFD,IAAA,CAPNC,QAAQ;IACRC,KAAK,GAMCF,IAAA,CANNE,KAAK;IACEC,cAAc,GAKfH,IAAA,CALNI,KAAK;IACLC,KAAK,GAICL,IAAA,CAJNK,KAAK;IAAAC,gBAAA,GAICN,IAAA,CAHNO,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,IAAI,GAAAA,gBAAA;IAClBE,0BAA0B,GAEpBR,IAAA,CAFNQ,0BAA0B;IACvBC,IAAA,GAAAC,wBAAA,CACGV,IAAA,EAAAW,SAAA;EACN,IAAMP,KAAK,GAAGT,gBAAgB,CAACQ,cAAc,CAAC;EAC9C,IAAQS,IAAA,GAASR,KAAK,CAAdQ,IAAA;EACR,IAAMC,UAAU,GAAGD,IAAI,GACnBR,KAAK,CAACU,MAAM,CAACC,gBAAgB,GAC7BrB,KAAK,CAACU,KAAK,CAACU,MAAM,CAACE,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACvD,IAAMC,WAAW,GAAGR,IAAI,GAAG,EAAE,GAAG,EAAE;EAClC,IAAMS,IAAI,GAAGT,IAAI,GAAGR,KAAK,CAACkB,KAAK,CAACC,UAAU,GAAGnB,KAAK,CAACkB,KAAK,CAACE,MAAM;EAE/D,OACEjC,KAAA,CAAAkC,aAAA,CAAChC,IAAI,EAAAiC,QAAA;IAACrB,KAAK,EAAE,CAACsB,MAAM,CAACC,SAAS,EAAEvB,KAAK;EAAE,GAAKI,IAAI,GAC7CP,KAAK,IACJX,KAAA,CAAAkC,aAAA,CAAChC,IAAI;IAACY,KAAK,EAAE,CAACsB,MAAM,CAACE,cAAc,EAAEjB,IAAI,IAAIe,MAAM,CAACG,gBAAgB;EAAE,GACnE5B,KAAK,IACJX,KAAA,CAAAkC,aAAA,CAAC3B,IAAI;IACHiC,OAAO,EAAC,YAAY;IACpBC,aAAa,EAAE,CAAE;IACjB3B,KAAK,EAAE,CAAA4B,aAAA;MAEHvC,KAAK,EAAEmB,UAAU;MACjBqB,UAAU,EAAEd;IAAW,GACpBC,IAAA,EAEL;IACFc,qBAAqB,EAAE3B;EAA2B,GAEjDN,KACG,CAEJ,CACP,EACAD,QAAQ,EACRM,WAAW,IACVhB,KAAA,CAAAkC,aAAA,CAAC5B,OAAO,EAAA6B,QAAA,KACDd,IAAI,IAAI;IAAEwB,eAAe,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAK,CAAC;IAClDhC,KAAK,EAAE,CAACsB,MAAM,CAACW,OAAO,EAAE1B,IAAI,IAAIe,MAAM,CAACY,SAAS,CAAE;IAClDnC,KAAK,EAAEA;EAAM,EACd,CAEC,CAAC;AAEX,CAAC;AAEDL,aAAa,CAACyC,WAAW,GAAG,gBAAgB;AAE5C,IAAMb,MAAM,GAAGnC,UAAU,CAACiD,MAAM,CAAC;EAC/Bb,SAAS,EAAE;IACTc,YAAY,EAAE;EAChB,CAAC;EACDb,cAAc,EAAE;IACdc,MAAM,EAAE,EAAE;IACVC,cAAc,EAAE;EAClB,CAAC;EACDd,gBAAgB,EAAE;IAChBa,MAAM,EAAE;EACV,CAAC;EACDL,OAAO,EAAE;IACPO,SAAS,EAAE;EACb,CAAC;EACDN,SAAS,EAAE;IACTO,eAAe,EAAElD,SAAS,CAACmD;EAC7B;AACF,CAAC,CAAC;AAEF,eAAehD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}