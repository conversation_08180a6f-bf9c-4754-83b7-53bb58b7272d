{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport ConsumptionEntry from \"../components/ConsumptionEntry\";\nimport FoodList from \"../components/FoodList\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar AddConsumptionScreen = function AddConsumptionScreen(_ref) {\n  var route = _ref.route,\n    navigation = _ref.navigation;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _ref2 = route.params || {},\n    consumption = _ref2.consumption,\n    date = _ref2.date;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    showFoodList = _useState2[0],\n    setShowFoodList = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedFood = _useState4[0],\n    setSelectedFood = _useState4[1];\n  var handleSaveConsumption = function handleSaveConsumption(savedConsumption) {\n    navigation.goBack();\n  };\n  var handleCancel = function handleCancel() {\n    navigation.goBack();\n  };\n  var handleSelectFood = function handleSelectFood(food) {\n    setSelectedFood(food);\n    setShowFoodList(false);\n  };\n  return _jsx(View, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: showFoodList ? _jsx(FoodList, {\n      onSelectFood: handleSelectFood\n    }) : _jsx(ConsumptionEntry, {\n      consumption: consumption,\n      onSave: handleSaveConsumption,\n      onCancel: handleCancel,\n      onSelectFood: function onSelectFood() {\n        return setShowFoodList(true);\n      },\n      selectedFood: selectedFood\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  }\n});\nexport default AddConsumptionScreen;", "map": {"version": 3, "names": ["React", "useState", "StyleSheet", "View", "useTheme", "ConsumptionEntry", "FoodList", "jsx", "_jsx", "AddConsumptionScreen", "_ref", "route", "navigation", "_useTheme", "theme", "_ref2", "params", "consumption", "date", "_useState", "_useState2", "_slicedToArray", "showFoodList", "setShowFoodList", "_useState3", "_useState4", "selected<PERSON>ood", "setSelectedFood", "handleSaveConsumption", "savedConsumption", "goBack", "handleCancel", "handleSelectFood", "food", "style", "styles", "container", "backgroundColor", "colors", "background", "children", "onSelectFood", "onSave", "onCancel", "create", "flex"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/src/screens/AddConsumptionScreen.js"], "sourcesContent": ["/**\n * Add Consumption Screen for ZnüniZähler\n * Allows users to add or edit food consumption\n */\n\nimport React, { useState } from 'react';\nimport { StyleSheet, View } from 'react-native';\nimport { useTheme } from '../theme/ThemeProvider';\nimport ConsumptionEntry from '../components/ConsumptionEntry';\nimport FoodList from '../components/FoodList';\n\n/**\n * Add Consumption Screen Component\n * @param {Object} props - Component props\n * @param {Object} props.route - Route object\n * @param {Object} props.navigation - Navigation object\n * @returns {JSX.Element} - Add consumption screen component\n */\nconst AddConsumptionScreen = ({ route, navigation }) => {\n  const { theme } = useTheme();\n  const { consumption, date } = route.params || {};\n  const [showFoodList, setShowFoodList] = useState(false);\n  const [selectedFood, setSelectedFood] = useState(null);\n  \n  // Handle save consumption\n  const handleSaveConsumption = (savedConsumption) => {\n    navigation.goBack();\n  };\n  \n  // Handle cancel\n  const handleCancel = () => {\n    navigation.goBack();\n  };\n  \n  // Handle select food\n  const handleSelectFood = (food) => {\n    setSelectedFood(food);\n    setShowFoodList(false);\n  };\n  \n  return (\n    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      {showFoodList ? (\n        <FoodList\n          onSelectFood={handleSelectFood}\n        />\n      ) : (\n        <ConsumptionEntry\n          consumption={consumption}\n          onSave={handleSaveConsumption}\n          onCancel={handleCancel}\n          onSelectFood={() => setShowFoodList(true)}\n          selectedFood={selectedFood}\n        />\n      )}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n\nexport default AddConsumptionScreen;\n"], "mappings": ";AAKA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAExC,SAASC,QAAQ;AACjB,OAAOC,gBAAgB;AACvB,OAAOC,QAAQ;AAA+B,SAAAC,GAAA,IAAAC,IAAA;AAS9C,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,IAAA,EAA8B;EAAA,IAAxBC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;EAC/C,IAAAC,SAAA,GAAkBT,QAAQ,CAAC,CAAC;IAApBU,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,KAAA,GAA8BJ,KAAK,CAACK,MAAM,IAAI,CAAC,CAAC;IAAxCC,WAAW,GAAAF,KAAA,CAAXE,WAAW;IAAEC,IAAI,GAAAH,KAAA,CAAJG,IAAI;EACzB,IAAAC,SAAA,GAAwClB,QAAQ,CAAC,KAAK,CAAC;IAAAmB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAhDG,YAAY,GAAAF,UAAA;IAAEG,eAAe,GAAAH,UAAA;EACpC,IAAAI,UAAA,GAAwCvB,QAAQ,CAAC,IAAI,CAAC;IAAAwB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA/CE,YAAY,GAAAD,UAAA;IAAEE,eAAe,GAAAF,UAAA;EAGpC,IAAMG,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,gBAAgB,EAAK;IAClDjB,UAAU,CAACkB,MAAM,CAAC,CAAC;EACrB,CAAC;EAGD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzBnB,UAAU,CAACkB,MAAM,CAAC,CAAC;EACrB,CAAC;EAGD,IAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;IACjCN,eAAe,CAACM,IAAI,CAAC;IACrBV,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,OACEf,IAAA,CAACL,IAAI;IAAC+B,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAEvB,KAAK,CAACwB,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,EAC3ElB,YAAY,GACXd,IAAA,CAACF,QAAQ;MACPmC,YAAY,EAAET;IAAiB,CAChC,CAAC,GAEFxB,IAAA,CAACH,gBAAgB;MACfY,WAAW,EAAEA,WAAY;MACzByB,MAAM,EAAEd,qBAAsB;MAC9Be,QAAQ,EAAEZ,YAAa;MACvBU,YAAY,EAAE,SAAdA,YAAYA,CAAA;QAAA,OAAQlB,eAAe,CAAC,IAAI,CAAC;MAAA,CAAC;MAC1CG,YAAY,EAAEA;IAAa,CAC5B;EACF,CACG,CAAC;AAEX,CAAC;AAED,IAAMS,MAAM,GAAGjC,UAAU,CAAC0C,MAAM,CAAC;EAC/BR,SAAS,EAAE;IACTS,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,eAAepC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}