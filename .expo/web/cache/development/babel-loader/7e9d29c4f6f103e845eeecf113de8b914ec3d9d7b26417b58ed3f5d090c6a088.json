{"ast": null, "code": "import _createClass from \"@babel/runtime/helpers/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport Platform from \"../Platform\";\nimport { CodedError } from \"./CodedError\";\nexport var UnavailabilityError = function (_CodedError) {\n  function UnavailabilityError(moduleName, propertyName) {\n    _classCallCheck(this, UnavailabilityError);\n    return _callSuper(this, UnavailabilityError, ['ERR_UNAVAILABLE', `The method or property ${moduleName}.${propertyName} is not available on ${Platform.OS}, are you sure you've linked all the native dependencies properly?`]);\n  }\n  _inherits(UnavailabilityError, _CodedError);\n  return _createClass(UnavailabilityError);\n}(CodedError);", "map": {"version": 3, "names": ["Platform", "CodedError", "UnavailabilityError", "_CodedError", "moduleName", "propertyName", "_classCallCheck", "_callSuper", "OS", "_inherits", "_createClass"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-modules-core/src/errors/UnavailabilityError.ts"], "sourcesContent": ["import Platform from '../Platform';\nimport { CodedError } from './CodedError';\n\n/**\n * A class for errors to be thrown when a property is accessed which is\n * unavailable, unsupported, or not currently implemented on the running\n * platform.\n */\nexport class UnavailabilityError extends CodedError {\n  constructor(moduleName: string, propertyName: string) {\n    super(\n      'ERR_UNAVAILABLE',\n      `The method or property ${moduleName}.${propertyName} is not available on ${Platform.OS}, are you sure you've linked all the native dependencies properly?`\n    );\n  }\n}\n"], "mappings": ";;;;;;;AAAA,OAAOA,QAAQ;AACf,SAASC,UAAU;AAOnB,WAAaC,mBAAoB,aAAAC,WAAA;EAC/B,SAAAD,oBAAYE,UAAkB,EAAEC,YAAoB;IAAAC,eAAA,OAAAJ,mBAAA;IAAA,OAAAK,UAAA,OAAAL,mBAAA,GAEhD,iBAAiB,EACjB,0BAA0BE,UAAU,IAAIC,YAAY,wBAAwBL,QAAQ,CAACQ,EAAE,oEAAoE;EAE/J;EAACC,SAAA,CAAAP,mBAAA,EAAAC,WAAA;EAAA,OAAAO,YAAA,CAAAR,mBAAA;AAAA,EANsCD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}