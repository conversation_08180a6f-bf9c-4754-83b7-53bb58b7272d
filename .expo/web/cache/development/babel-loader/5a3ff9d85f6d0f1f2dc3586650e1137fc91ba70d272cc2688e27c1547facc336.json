{"ast": null, "code": "import BackHandler from \"react-native-web/dist/exports/BackHandler\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nexport var isSearchBarAvailableForCurrentPlatform = ['ios', 'android'].includes(Platform.OS);\nexport function executeNativeBackPress() {\n  BackHandler.exitApp();\n  return true;\n}\nexport var isNewBackTitleImplementation = true;", "map": {"version": 3, "names": ["isSearchBarAvailableForCurrentPlatform", "includes", "Platform", "OS", "executeNativeBackPress", "<PERSON><PERSON><PERSON><PERSON>", "exitApp", "isNewBackTitleImplementation"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-screens/lib/module/utils.ts"], "sourcesContent": ["import { BackHandler, Platform } from 'react-native';\n\nexport const isSearchBarAvailableForCurrentPlatform = [\n  'ios',\n  'android',\n].includes(Platform.OS);\n\nexport function executeNativeBackPress() {\n  // This function invokes the native back press event\n  BackHandler.exitApp();\n  return true;\n}\n\n// Because of a bug introduced in https://github.com/software-mansion/react-native-screens/pull/1646\n// react-native-screens v3.21 changed how header's backTitle handles whitespace strings in https://github.com/software-mansion/react-native-screens/pull/1726\n// To allow for backwards compatibility in @react-navigation/native-stack we need a way to check if this version or newer is used.\n// See https://github.com/react-navigation/react-navigation/pull/11423 for more context.\nexport const isNewBackTitleImplementation = true;\n"], "mappings": ";;AAEA,OAAO,IAAMA,sCAAsC,GAAG,CACpD,KAAK,EACL,SAAS,CACV,CAACC,QAAQ,CAACC,QAAQ,CAACC,EAAE,CAAC;AAEvB,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EAEvCC,WAAW,CAACC,OAAO,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;AAMA,OAAO,IAAMC,4BAA4B,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}