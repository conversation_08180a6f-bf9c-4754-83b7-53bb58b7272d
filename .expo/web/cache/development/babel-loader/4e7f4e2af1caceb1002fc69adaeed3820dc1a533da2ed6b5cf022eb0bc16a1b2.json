{"ast": null, "code": "\"use strict\";\n\nvar _asyncToGenerator = require(\"@babel/runtime/helpers/asyncToGenerator\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dynamicLoader = void 0;\nvar loadFontAsync = function () {\n  var _ref = _asyncToGenerator(function* (_fontFamily, _fontSource) {\n    return undefined;\n  });\n  return function loadFontAsync(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nvar isLoaded = function isLoaded(_fontFamily) {\n  return true;\n};\nvar dynamicLoader = exports.dynamicLoader = {\n  isLoaded: isLoaded,\n  loadFontAsync: loadFontAsync\n};", "map": {"version": 3, "names": ["loadFontAsync", "_ref", "_asyncToGenerator", "_fontFamily", "_fontSource", "undefined", "_x", "_x2", "apply", "arguments", "isLoaded", "dynamic<PERSON>oader", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-vector-icons/common/src/dynamicLoading/dynamic-font-loading.web.ts"], "sourcesContent": ["/**\n * dynamic font loading isn't supported on web\n * */\nimport type { DynamicLoader, FontSource } from './types';\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst loadFontAsync = async (_fontFamily: string, _fontSource: FontSource): Promise<void> => undefined;\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst isLoaded = (_fontFamily: string) => true;\n\nexport const dynamicLoader: DynamicLoader = {\n  isLoaded,\n  loadFontAsync,\n};\n"], "mappings": ";;;;;;;AAMA,IAAMA,aAAa;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,WAAmB,EAAEC,WAAuB;IAAA,OAAoBC,SAAS;EAAA;EAAA,gBAAhGL,aAAaA,CAAAM,EAAA,EAAAC,GAAA;IAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;EAAA;AAAA,GAAmF;AAGtG,IAAMC,QAAQ,GAAI,SAAZA,QAAQA,CAAIP,WAAmB;EAAA,OAAK,IAAI;AAAA;AAEvC,IAAMQ,aAA4B,GAAAC,OAAA,CAAAD,aAAA,GAAG;EAC1CD,QAAQ,EAARA,QAAQ;EACRV,aAAA,EAAAA;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}