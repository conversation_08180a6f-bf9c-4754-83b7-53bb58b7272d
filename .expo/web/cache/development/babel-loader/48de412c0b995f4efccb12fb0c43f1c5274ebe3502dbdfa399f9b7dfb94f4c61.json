{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { createTheming } from '@callstack/react-theme-provider';\nimport color from 'color';\nimport { MD2DarkTheme, MD2LightTheme, MD3DarkTheme, MD3LightTheme } from \"../styles/themes\";\nexport var DefaultTheme = MD3LightTheme;\nvar _createTheming = createTheming(MD3LightTheme),\n  ThemeProvider = _createTheming.ThemeProvider,\n  withTheme = _createTheming.withTheme,\n  useAppTheme = _createTheming.useTheme;\nexport { ThemeProvider, withTheme, useAppTheme };\nexport function useTheme(overrides) {\n  return useAppTheme(overrides);\n}\nexport var useInternalTheme = function useInternalTheme(themeOverrides) {\n  return useAppTheme(themeOverrides);\n};\nexport var withInternalTheme = function withInternalTheme(WrappedComponent) {\n  return withTheme(WrappedComponent);\n};\nexport var defaultThemesByVersion = {\n  2: {\n    light: MD2LightTheme,\n    dark: MD2DarkTheme\n  },\n  3: {\n    light: MD3LightTheme,\n    dark: MD3DarkTheme\n  }\n};\nexport var getTheme = function getTheme() {\n  var isDark = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var isV3 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var themeVersion = isV3 ? 3 : 2;\n  var scheme = isDark ? 'dark' : 'light';\n  return defaultThemesByVersion[themeVersion][scheme];\n};\nexport function adaptNavigationTheme(themes) {\n  var reactNavigationLight = themes.reactNavigationLight,\n    reactNavigationDark = themes.reactNavigationDark,\n    materialLight = themes.materialLight,\n    materialDark = themes.materialDark;\n  var MD3Themes = {\n    light: materialLight || MD3LightTheme,\n    dark: materialDark || MD3DarkTheme\n  };\n  var result = {};\n  if (reactNavigationLight) {\n    result.LightTheme = getAdaptedTheme(reactNavigationLight, MD3Themes.light);\n  }\n  if (reactNavigationDark) {\n    result.DarkTheme = getAdaptedTheme(reactNavigationDark, MD3Themes.dark);\n  }\n  return result;\n}\nvar getAdaptedTheme = function getAdaptedTheme(theme, materialTheme) {\n  var base = _objectSpread(_objectSpread({}, theme), {}, {\n    colors: _objectSpread(_objectSpread({}, theme.colors), {}, {\n      primary: materialTheme.colors.primary,\n      background: materialTheme.colors.background,\n      card: materialTheme.colors.elevation.level2,\n      text: materialTheme.colors.onSurface,\n      border: materialTheme.colors.outline,\n      notification: materialTheme.colors.error\n    })\n  });\n  if ('fonts' in theme) {\n    return _objectSpread(_objectSpread({}, base), {}, {\n      fonts: {\n        regular: {\n          fontFamily: materialTheme.fonts.bodyMedium.fontFamily,\n          fontWeight: materialTheme.fonts.bodyMedium.fontWeight,\n          letterSpacing: materialTheme.fonts.bodyMedium.letterSpacing\n        },\n        medium: {\n          fontFamily: materialTheme.fonts.titleMedium.fontFamily,\n          fontWeight: materialTheme.fonts.titleMedium.fontWeight,\n          letterSpacing: materialTheme.fonts.titleMedium.letterSpacing\n        },\n        bold: {\n          fontFamily: materialTheme.fonts.headlineSmall.fontFamily,\n          fontWeight: materialTheme.fonts.headlineSmall.fontWeight,\n          letterSpacing: materialTheme.fonts.headlineSmall.letterSpacing\n        },\n        heavy: {\n          fontFamily: materialTheme.fonts.headlineLarge.fontFamily,\n          fontWeight: materialTheme.fonts.headlineLarge.fontWeight,\n          letterSpacing: materialTheme.fonts.headlineLarge.letterSpacing\n        }\n      }\n    });\n  }\n  return base;\n};\nexport var getDynamicThemeElevations = function getDynamicThemeElevations(scheme) {\n  var elevationValues = ['transparent', 0.05, 0.08, 0.11, 0.12, 0.14];\n  return elevationValues.reduce(function (elevations, elevationValue, index) {\n    return _objectSpread(_objectSpread({}, elevations), {}, _defineProperty({}, `level${index}`, index === 0 ? elevationValue : color(scheme.surface).mix(color(scheme.primary), elevationValue).rgb().string()));\n  }, {});\n};", "map": {"version": 3, "names": ["createTheming", "color", "MD2DarkTheme", "MD2LightTheme", "MD3DarkTheme", "MD3LightTheme", "DefaultTheme", "_createTheming", "ThemeProvider", "withTheme", "useAppTheme", "useTheme", "overrides", "useInternalTheme", "themeOverrides", "withInternalTheme", "WrappedComponent", "defaultThemesByVersion", "light", "dark", "getTheme", "isDark", "arguments", "length", "undefined", "isV3", "themeVersion", "scheme", "adaptNavigationTheme", "themes", "reactNavigationLight", "reactNavigationDark", "materialLight", "materialDark", "MD3Themes", "result", "LightTheme", "getAdaptedTheme", "DarkTheme", "theme", "materialTheme", "base", "_objectSpread", "colors", "primary", "background", "card", "elevation", "level2", "text", "onSurface", "border", "outline", "notification", "error", "fonts", "regular", "fontFamily", "bodyMedium", "fontWeight", "letterSpacing", "medium", "titleMedium", "bold", "headlineSmall", "heavy", "headlineLarge", "getDynamicThemeElevations", "elevationValues", "reduce", "elevations", "elevationValue", "index", "_defineProperty", "surface", "mix", "rgb", "string"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/core/theming.tsx"], "sourcesContent": ["import type { ComponentType } from 'react';\n\nimport { $DeepPartial, createTheming } from '@callstack/react-theme-provider';\nimport color from 'color';\n\nimport {\n  MD2DarkTheme,\n  MD2LightTheme,\n  MD3DarkTheme,\n  MD3LightTheme,\n} from '../styles/themes';\nimport type {\n  InternalTheme,\n  MD3Theme,\n  MD3AndroidColors,\n  NavigationTheme,\n} from '../types';\n\nexport const DefaultTheme = MD3LightTheme;\n\nexport const {\n  ThemeProvider,\n  withTheme,\n  useTheme: useAppTheme,\n} = createTheming<unknown>(MD3LightTheme);\n\nexport function useTheme<T = MD3Theme>(overrides?: $DeepPartial<T>) {\n  return useAppTheme<T>(overrides);\n}\n\nexport const useInternalTheme = (\n  themeOverrides: $DeepPartial<InternalTheme> | undefined\n) => useAppTheme<InternalTheme>(themeOverrides);\n\nexport const withInternalTheme = <Props extends { theme: InternalTheme }, C>(\n  WrappedComponent: ComponentType<Props & { theme: InternalTheme }> & C\n) => withTheme<Props, C>(WrappedComponent);\n\nexport const defaultThemesByVersion = {\n  2: {\n    light: MD2LightTheme,\n    dark: MD2DarkTheme,\n  },\n  3: {\n    light: MD3LightTheme,\n    dark: MD3DarkTheme,\n  },\n};\n\nexport const getTheme = <\n  Scheme extends boolean = false,\n  IsVersion3 extends boolean = true\n>(\n  isDark: Scheme = false as Scheme,\n  isV3: IsVersion3 = true as IsVersion3\n): (typeof defaultThemesByVersion)[IsVersion3 extends true\n  ? 3\n  : 2][Scheme extends true ? 'dark' : 'light'] => {\n  const themeVersion = isV3 ? 3 : 2;\n  const scheme = isDark ? 'dark' : 'light';\n\n  return defaultThemesByVersion[themeVersion][scheme];\n};\n\n// eslint-disable-next-line no-redeclare\nexport function adaptNavigationTheme<T extends NavigationTheme>(themes: {\n  reactNavigationLight: T;\n  materialLight?: MD3Theme;\n}): {\n  LightTheme: NavigationTheme;\n};\n// eslint-disable-next-line no-redeclare\nexport function adaptNavigationTheme<T extends NavigationTheme>(themes: {\n  reactNavigationDark: T;\n  materialDark?: MD3Theme;\n}): {\n  DarkTheme: NavigationTheme;\n};\n// eslint-disable-next-line no-redeclare\nexport function adaptNavigationTheme<\n  TLight extends NavigationTheme,\n  TDark extends NavigationTheme\n>(themes: {\n  reactNavigationLight: TLight;\n  reactNavigationDark: TDark;\n  materialLight?: MD3Theme;\n  materialDark?: MD3Theme;\n}): { LightTheme: TLight; DarkTheme: TDark };\n// eslint-disable-next-line no-redeclare\nexport function adaptNavigationTheme(themes: any) {\n  const {\n    reactNavigationLight,\n    reactNavigationDark,\n    materialLight,\n    materialDark,\n  } = themes;\n\n  const MD3Themes = {\n    light: materialLight || MD3LightTheme,\n    dark: materialDark || MD3DarkTheme,\n  };\n\n  const result: { LightTheme?: any; DarkTheme?: any } = {};\n\n  if (reactNavigationLight) {\n    result.LightTheme = getAdaptedTheme(reactNavigationLight, MD3Themes.light);\n  }\n\n  if (reactNavigationDark) {\n    result.DarkTheme = getAdaptedTheme(reactNavigationDark, MD3Themes.dark);\n  }\n\n  return result;\n}\n\nconst getAdaptedTheme = <T extends NavigationTheme>(\n  theme: T,\n  materialTheme: MD3Theme\n): T => {\n  const base = {\n    ...theme,\n    colors: {\n      ...theme.colors,\n      primary: materialTheme.colors.primary,\n      background: materialTheme.colors.background,\n      card: materialTheme.colors.elevation.level2,\n      text: materialTheme.colors.onSurface,\n      border: materialTheme.colors.outline,\n      notification: materialTheme.colors.error,\n    },\n  };\n\n  if ('fonts' in theme) {\n    return {\n      ...base,\n      fonts: {\n        regular: {\n          fontFamily: materialTheme.fonts.bodyMedium.fontFamily,\n          fontWeight: materialTheme.fonts.bodyMedium.fontWeight,\n          letterSpacing: materialTheme.fonts.bodyMedium.letterSpacing,\n        },\n        medium: {\n          fontFamily: materialTheme.fonts.titleMedium.fontFamily,\n          fontWeight: materialTheme.fonts.titleMedium.fontWeight,\n          letterSpacing: materialTheme.fonts.titleMedium.letterSpacing,\n        },\n        bold: {\n          fontFamily: materialTheme.fonts.headlineSmall.fontFamily,\n          fontWeight: materialTheme.fonts.headlineSmall.fontWeight,\n          letterSpacing: materialTheme.fonts.headlineSmall.letterSpacing,\n        },\n        heavy: {\n          fontFamily: materialTheme.fonts.headlineLarge.fontFamily,\n          fontWeight: materialTheme.fonts.headlineLarge.fontWeight,\n          letterSpacing: materialTheme.fonts.headlineLarge.letterSpacing,\n        },\n      },\n    };\n  }\n\n  return base;\n};\n\nexport const getDynamicThemeElevations = (scheme: MD3AndroidColors) => {\n  const elevationValues = ['transparent', 0.05, 0.08, 0.11, 0.12, 0.14];\n  return elevationValues.reduce((elevations, elevationValue, index) => {\n    return {\n      ...elevations,\n      [`level${index}`]:\n        index === 0\n          ? elevationValue\n          : color(scheme.surface)\n              .mix(color(scheme.primary), elevationValue as number)\n              .rgb()\n              .string(),\n    };\n  }, {});\n};\n"], "mappings": ";;;AAEA,SAAuBA,aAAa,QAAQ,iCAAiC;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAEzB,SACEC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,aAAa;AASf,OAAO,IAAMC,YAAY,GAAGD,aAAa;AAElC,IAAAE,cAAA,GAIHP,aAAa,CAAUK,aAAa,CAAC;EAHvCG,aAAa,GAAAD,cAAA,CAAbC,aAAa;EACbC,SAAS,GAAAF,cAAA,CAATE,SAAS;EACCC,WAAA,GAAAH,cAAA,CAAVI,QAAQ;AAC+B,SAAAH,aAAA,EAAAC,SAAA,EAAAC,WAAA;AAEzC,OAAO,SAASC,QAAQA,CAAeC,SAA2B,EAAE;EAClE,OAAOF,WAAW,CAAIE,SAAS,CAAC;AAClC;AAEA,OAAO,IAAMC,gBAAgB,GAC3B,SADWA,gBAAgBA,CAC3BC,cAAuD;EAAA,OACpDJ,WAAW,CAAgBI,cAAc,CAAC;AAAA;AAE/C,OAAO,IAAMC,iBAAiB,GAC5B,SADWA,iBAAiBA,CAC5BC,gBAAqE;EAAA,OAClEP,SAAS,CAAWO,gBAAgB,CAAC;AAAA;AAE1C,OAAO,IAAMC,sBAAsB,GAAG;EACpC,CAAC,EAAE;IACDC,KAAK,EAAEf,aAAa;IACpBgB,IAAI,EAAEjB;EACR,CAAC;EACD,CAAC,EAAE;IACDgB,KAAK,EAAEb,aAAa;IACpBc,IAAI,EAAEf;EACR;AACF,CAAC;AAED,OAAO,IAAMgB,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAQ6B;EAAA,IAJhDC,MAAc,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IACtBG,IAAgB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAIvB,IAAMI,YAAY,GAAGD,IAAI,GAAG,CAAC,GAAG,CAAC;EACjC,IAAME,MAAM,GAAGN,MAAM,GAAG,MAAM,GAAG,OAAO;EAExC,OAAOJ,sBAAsB,CAACS,YAAY,CAAC,CAACC,MAAM,CAAC;AACrD,CAAC;AA2BD,OAAO,SAASC,oBAAoBA,CAACC,MAAW,EAAE;EAChD,IACEC,oBAAoB,GAIlBD,MAAM,CAJRC,oBAAoB;IACpBC,mBAAmB,GAGjBF,MAAM,CAHRE,mBAAmB;IACnBC,aAAa,GAEXH,MAAM,CAFRG,aAAa;IACbC,YAAA,GACEJ,MAAM,CADRI,YAAA;EAGF,IAAMC,SAAS,GAAG;IAChBhB,KAAK,EAAEc,aAAa,IAAI3B,aAAa;IACrCc,IAAI,EAAEc,YAAY,IAAI7B;EACxB,CAAC;EAED,IAAM+B,MAA6C,GAAG,CAAC,CAAC;EAExD,IAAIL,oBAAoB,EAAE;IACxBK,MAAM,CAACC,UAAU,GAAGC,eAAe,CAACP,oBAAoB,EAAEI,SAAS,CAAChB,KAAK,CAAC;EAC5E;EAEA,IAAIa,mBAAmB,EAAE;IACvBI,MAAM,CAACG,SAAS,GAAGD,eAAe,CAACN,mBAAmB,EAAEG,SAAS,CAACf,IAAI,CAAC;EACzE;EAEA,OAAOgB,MAAM;AACf;AAEA,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CACnBE,KAAQ,EACRC,aAAuB,EACjB;EACN,IAAMC,IAAI,GAAAC,aAAA,CAAAA,aAAA,KACLH,KAAK;IACRI,MAAM,EAAAD,aAAA,CAAAA,aAAA,KACDH,KAAK,CAACI,MAAM;MACfC,OAAO,EAAEJ,aAAa,CAACG,MAAM,CAACC,OAAO;MACrCC,UAAU,EAAEL,aAAa,CAACG,MAAM,CAACE,UAAU;MAC3CC,IAAI,EAAEN,aAAa,CAACG,MAAM,CAACI,SAAS,CAACC,MAAM;MAC3CC,IAAI,EAAET,aAAa,CAACG,MAAM,CAACO,SAAS;MACpCC,MAAM,EAAEX,aAAa,CAACG,MAAM,CAACS,OAAO;MACpCC,YAAY,EAAEb,aAAa,CAACG,MAAM,CAACW;IAAA;EACrC,EACD;EAED,IAAI,OAAO,IAAIf,KAAK,EAAE;IACpB,OAAAG,aAAA,CAAAA,aAAA,KACKD,IAAI;MACPc,KAAK,EAAE;QACLC,OAAO,EAAE;UACPC,UAAU,EAAEjB,aAAa,CAACe,KAAK,CAACG,UAAU,CAACD,UAAU;UACrDE,UAAU,EAAEnB,aAAa,CAACe,KAAK,CAACG,UAAU,CAACC,UAAU;UACrDC,aAAa,EAAEpB,aAAa,CAACe,KAAK,CAACG,UAAU,CAACE;QAChD,CAAC;QACDC,MAAM,EAAE;UACNJ,UAAU,EAAEjB,aAAa,CAACe,KAAK,CAACO,WAAW,CAACL,UAAU;UACtDE,UAAU,EAAEnB,aAAa,CAACe,KAAK,CAACO,WAAW,CAACH,UAAU;UACtDC,aAAa,EAAEpB,aAAa,CAACe,KAAK,CAACO,WAAW,CAACF;QACjD,CAAC;QACDG,IAAI,EAAE;UACJN,UAAU,EAAEjB,aAAa,CAACe,KAAK,CAACS,aAAa,CAACP,UAAU;UACxDE,UAAU,EAAEnB,aAAa,CAACe,KAAK,CAACS,aAAa,CAACL,UAAU;UACxDC,aAAa,EAAEpB,aAAa,CAACe,KAAK,CAACS,aAAa,CAACJ;QACnD,CAAC;QACDK,KAAK,EAAE;UACLR,UAAU,EAAEjB,aAAa,CAACe,KAAK,CAACW,aAAa,CAACT,UAAU;UACxDE,UAAU,EAAEnB,aAAa,CAACe,KAAK,CAACW,aAAa,CAACP,UAAU;UACxDC,aAAa,EAAEpB,aAAa,CAACe,KAAK,CAACW,aAAa,CAACN;QACnD;MACF;IAAA;EAEJ;EAEA,OAAOnB,IAAI;AACb,CAAC;AAED,OAAO,IAAM0B,yBAAyB,GAAI,SAA7BA,yBAAyBA,CAAIxC,MAAwB,EAAK;EACrE,IAAMyC,eAAe,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrE,OAAOA,eAAe,CAACC,MAAM,CAAC,UAACC,UAAU,EAAEC,cAAc,EAAEC,KAAK,EAAK;IACnE,OAAA9B,aAAA,CAAAA,aAAA,KACK4B,UAAU,OAAAG,eAAA,KACX,QAAOD,KAAM,EAAC,EACdA,KAAK,KAAK,CAAC,GACPD,cAAc,GACdtE,KAAK,CAAC0B,MAAM,CAAC+C,OAAO,CAAC,CAClBC,GAAG,CAAC1E,KAAK,CAAC0B,MAAM,CAACiB,OAAO,CAAC,EAAE2B,cAAwB,CAAC,CACpDK,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC;EAEpB,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}