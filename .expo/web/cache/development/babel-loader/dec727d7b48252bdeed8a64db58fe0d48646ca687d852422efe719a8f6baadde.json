{"ast": null, "code": "export function requireNativeModule(moduleName) {\n  var nativeModule = requireOptionalNativeModule(moduleName);\n  if (nativeModule != null) {\n    return nativeModule;\n  }\n  if (typeof window === 'undefined') {\n    return {};\n  }\n  throw new Error(`Cannot find native module '${moduleName}'`);\n}\nexport function requireOptionalNativeModule(moduleName) {\n  var _globalThis$expo;\n  if (typeof globalThis.ExpoDomWebView === 'object' && (globalThis == null ? void 0 : (_globalThis$expo = globalThis.expo) == null ? void 0 : _globalThis$expo.modules) != null) {\n    var _globalThis$expo$modu, _globalThis$expo2, _globalThis$expo2$mod;\n    return (_globalThis$expo$modu = (_globalThis$expo2 = globalThis.expo) == null ? void 0 : (_globalThis$expo2$mod = _globalThis$expo2.modules) == null ? void 0 : _globalThis$expo2$mod[moduleName]) != null ? _globalThis$expo$modu : null;\n  }\n  return null;\n}", "map": {"version": 3, "names": ["requireNativeModule", "moduleName", "nativeModule", "requireOptionalNativeModule", "window", "Error", "_globalThis$expo", "globalThis", "ExpoDomWebView", "expo", "modules", "_globalThis$expo$modu", "_globalThis$expo2", "_globalThis$expo2$mod"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/expo-modules-core/src/requireNativeModule.web.ts"], "sourcesContent": ["export function requireNativeModule<ModuleType = any>(moduleName: string): ModuleType {\n  const nativeModule = requireOptionalNativeModule<ModuleType>(moduleName);\n  if (nativeModule != null) {\n    return nativeModule;\n  }\n  if (typeof window === 'undefined') {\n    // For SSR, we expect not to have native modules available, but to avoid crashing from SSR resolutions, we return an empty object.\n    return {} as ModuleType;\n  }\n  throw new Error(`Cannot find native module '${moduleName}'`);\n}\n\nexport function requireOptionalNativeModule<ModuleType = any>(\n  moduleName: string\n): ModuleType | null {\n  if (typeof globalThis.ExpoDomWebView === 'object' && globalThis?.expo?.modules != null) {\n    return globalThis.expo?.modules?.[moduleName] ?? null;\n  }\n  return null;\n}\n"], "mappings": "AAAA,OAAO,SAASA,mBAAmBA,CAAmBC,UAAkB,EAAc;EACpF,IAAMC,YAAY,GAAGC,2BAA2B,CAAaF,UAAU,CAAC;EACxE,IAAIC,YAAY,IAAI,IAAI,EAAE;IACxB,OAAOA,YAAY;EACrB;EACA,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;IAEjC,OAAO,CAAC,CAAC;EACX;EACA,MAAM,IAAIC,KAAK,CAAC,8BAA8BJ,UAAU,GAAG,CAAC;AAC9D;AAEA,OAAO,SAASE,2BAA2BA,CACzCF,UAAkB,EACC;EAAA,IAAAK,gBAAA;EACnB,IAAI,OAAOC,UAAU,CAACC,cAAc,KAAK,QAAQ,IAAI,CAAAD,UAAU,qBAAAD,gBAAA,GAAVC,UAAU,CAAEE,IAAI,qBAAhBH,gBAAA,CAAkBI,OAAO,KAAI,IAAI,EAAE;IAAA,IAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;IACtF,QAAAF,qBAAA,IAAAC,iBAAA,GAAOL,UAAU,CAACE,IAAI,sBAAAI,qBAAA,GAAfD,iBAAA,CAAiBF,OAAO,qBAAxBG,qBAAA,CAA2BZ,UAAU,CAAC,YAAAU,qBAAA,GAAI,IAAI;EACvD;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}