{"ast": null, "code": "export default function Screen(_) {\n  return null;\n}", "map": {"version": 3, "names": ["Screen", "_"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/Screen.tsx"], "sourcesContent": ["import type { NavigationState, ParamListBase } from '@react-navigation/routers';\n\nimport type { EventMapBase, RouteConfig } from './types';\n\n/**\n * Empty component used for specifying route configuration.\n */\nexport default function Screen<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList,\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase\n>(_: RouteConfig<ParamList, RouteName, State, ScreenOptions, EventMap>) {\n  /* istanbul ignore next */\n  return null;\n}\n"], "mappings": "AAOA,eAAe,SAASA,MAAMA,CAM5BC,CAAoE,EAAE;EAEtE,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}