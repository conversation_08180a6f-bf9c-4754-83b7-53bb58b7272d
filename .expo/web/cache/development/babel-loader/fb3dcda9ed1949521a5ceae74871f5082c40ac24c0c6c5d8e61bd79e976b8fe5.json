{"ast": null, "code": "import UIManager from \"../../exports/UIManager\";\nvar TextInputState = {\n  _currentlyFocusedNode: null,\n  currentlyFocusedField: function currentlyFocusedField() {\n    if (document.activeElement !== this._currentlyFocusedNode) {\n      this._currentlyFocusedNode = null;\n    }\n    return this._currentlyFocusedNode;\n  },\n  focusTextInput: function focusTextInput(textFieldNode) {\n    if (textFieldNode !== null) {\n      this._currentlyFocusedNode = textFieldNode;\n      if (document.activeElement !== textFieldNode) {\n        UIManager.focus(textFieldNode);\n      }\n    }\n  },\n  blurTextInput: function blurTextInput(textFieldNode) {\n    if (textFieldNode !== null) {\n      this._currentlyFocusedNode = null;\n      if (document.activeElement === textFieldNode) {\n        UIManager.blur(textFieldNode);\n      }\n    }\n  }\n};\nexport default TextInputState;", "map": {"version": 3, "names": ["UIManager", "TextInputState", "_currentlyFocusedNode", "currentlyFocusedField", "document", "activeElement", "focusTextInput", "textFieldNode", "focus", "blurTextInput", "blur"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/modules/TextInputState/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport UIManager from '../../exports/UIManager';\n\n/**\n * This class is responsible for coordinating the \"focused\"\n * state for TextInputs. All calls relating to the keyboard\n * should be funneled through here\n */\nvar TextInputState = {\n  /**\n   * Internal state\n   */\n  _currentlyFocusedNode: null,\n  /**\n   * Returns the ID of the currently focused text field, if one exists\n   * If no text field is focused it returns null\n   */\n  currentlyFocusedField() {\n    if (document.activeElement !== this._currentlyFocusedNode) {\n      this._currentlyFocusedNode = null;\n    }\n    return this._currentlyFocusedNode;\n  },\n  /**\n   * @param {Object} TextInputID id of the text field to focus\n   * Focuses the specified text field\n   * noop if the text field was already focused\n   */\n  focusTextInput(textFieldNode) {\n    if (textFieldNode !== null) {\n      this._currentlyFocusedNode = textFieldNode;\n      if (document.activeElement !== textFieldNode) {\n        UIManager.focus(textFieldNode);\n      }\n    }\n  },\n  /**\n   * @param {Object} textFieldNode id of the text field to focus\n   * Unfocuses the specified text field\n   * noop if it wasn't focused\n   */\n  blurTextInput(textFieldNode) {\n    if (textFieldNode !== null) {\n      this._currentlyFocusedNode = null;\n      if (document.activeElement === textFieldNode) {\n        UIManager.blur(textFieldNode);\n      }\n    }\n  }\n};\nexport default TextInputState;"], "mappings": "AAUA,OAAOA,SAAS;AAOhB,IAAIC,cAAc,GAAG;EAInBC,qBAAqB,EAAE,IAAI;EAK3BC,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;IACtB,IAAIC,QAAQ,CAACC,aAAa,KAAK,IAAI,CAACH,qBAAqB,EAAE;MACzD,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACnC;IACA,OAAO,IAAI,CAACA,qBAAqB;EACnC,CAAC;EAMDI,cAAc,WAAdA,cAAcA,CAACC,aAAa,EAAE;IAC5B,IAAIA,aAAa,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACL,qBAAqB,GAAGK,aAAa;MAC1C,IAAIH,QAAQ,CAACC,aAAa,KAAKE,aAAa,EAAE;QAC5CP,SAAS,CAACQ,KAAK,CAACD,aAAa,CAAC;MAChC;IACF;EACF,CAAC;EAMDE,aAAa,WAAbA,aAAaA,CAACF,aAAa,EAAE;IAC3B,IAAIA,aAAa,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACL,qBAAqB,GAAG,IAAI;MACjC,IAAIE,QAAQ,CAACC,aAAa,KAAKE,aAAa,EAAE;QAC5CP,SAAS,CAACU,IAAI,CAACH,aAAa,CAAC;MAC/B;IACF;EACF;AACF,CAAC;AACD,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}