{"ast": null, "code": "import * as React from 'react';\nimport NavigationContainerRefContext from \"./NavigationContainerRefContext\";\nimport NavigationContext from \"./NavigationContext\";\nexport default function useNavigation() {\n  var root = React.useContext(NavigationContainerRefContext);\n  var navigation = React.useContext(NavigationContext);\n  if (navigation === undefined && root === undefined) {\n    throw new Error(\"Couldn't find a navigation object. Is your component inside NavigationContainer?\");\n  }\n  return navigation != null ? navigation : root;\n}", "map": {"version": 3, "names": ["React", "NavigationContainerRefContext", "NavigationContext", "useNavigation", "root", "useContext", "navigation", "undefined", "Error"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/useNavigation.tsx"], "sourcesContent": ["import type { NavigationState } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationContainerRefContext from './NavigationContainerRefContext';\nimport NavigationContext from './NavigationContext';\nimport type { NavigationProp } from './types';\n\n/**\n * Hook to access the navigation prop of the parent screen anywhere.\n *\n * @returns Navigation prop of the parent screen.\n */\nexport default function useNavigation<\n  T = Omit<NavigationProp<ReactNavigation.RootParamList>, 'getState'> & {\n    getState(): NavigationState | undefined;\n  }\n>(): T {\n  const root = React.useContext(NavigationContainerRefContext);\n  const navigation = React.useContext(NavigationContext);\n\n  if (navigation === undefined && root === undefined) {\n    throw new Error(\n      \"Couldn't find a navigation object. Is your component inside NavigationContainer?\"\n    );\n  }\n\n  // FIXME: Figure out a better way to do this\n  return (navigation ?? root) as unknown as T;\n}\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,6BAA6B;AACpC,OAAOC,iBAAiB;AAQxB,eAAe,SAASC,aAAaA,CAAA,EAI9B;EACL,IAAMC,IAAI,GAAGJ,KAAK,CAACK,UAAU,CAACJ,6BAA6B,CAAC;EAC5D,IAAMK,UAAU,GAAGN,KAAK,CAACK,UAAU,CAACH,iBAAiB,CAAC;EAEtD,IAAII,UAAU,KAAKC,SAAS,IAAIH,IAAI,KAAKG,SAAS,EAAE;IAClD,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;EACH;EAGA,OAAQF,UAAU,WAAVA,UAAU,GAAIF,IAAI;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}