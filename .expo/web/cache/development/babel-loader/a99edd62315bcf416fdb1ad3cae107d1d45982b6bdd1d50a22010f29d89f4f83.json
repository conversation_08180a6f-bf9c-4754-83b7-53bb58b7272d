{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport Easing from \"react-native-web/dist/exports/Easing\";\nimport findNodeHandle from \"react-native-web/dist/exports/findNodeHandle\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Keyboard from \"react-native-web/dist/exports/Keyboard\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport MenuItem from \"./MenuItem\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { ElevationLevels } from \"../../types\";\nimport { addEventListener } from \"../../utils/addEventListener\";\nimport { BackHandler } from \"../../utils/BackHandler/BackHandler\";\nimport Portal from \"../Portal/Portal\";\nimport Surface from \"../Surface\";\nvar SCREEN_INDENT = 8;\nvar ANIMATION_DURATION = 250;\nvar EASING = Easing.bezier(0.4, 0, 0.2, 1);\nvar WINDOW_LAYOUT = Dimensions.get('window');\nvar DEFAULT_ELEVATION = 2;\nexport var ELEVATION_LEVELS_MAP = Object.values(ElevationLevels);\nvar DEFAULT_MODE = 'elevated';\nvar focusFirstDOMNode = function focusFirstDOMNode(el) {\n  if (el && Platform.OS === 'web') {\n    var node = findNodeHandle(el);\n    var focusableNode = node.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n    focusableNode === null || focusableNode === void 0 ? void 0 : focusableNode.focus();\n  }\n};\nvar isCoordinate = function isCoordinate(anchor) {\n  return !React.isValidElement(anchor) && typeof (anchor === null || anchor === void 0 ? void 0 : anchor.x) === 'number' && typeof (anchor === null || anchor === void 0 ? void 0 : anchor.y) === 'number';\n};\nvar isBrowser = function isBrowser() {\n  return Platform.OS === 'web' && 'document' in global;\n};\nvar Menu = function Menu(_ref) {\n  var visible = _ref.visible,\n    statusBarHeight = _ref.statusBarHeight,\n    _ref$overlayAccessibi = _ref.overlayAccessibilityLabel,\n    overlayAccessibilityLabel = _ref$overlayAccessibi === void 0 ? 'Close menu' : _ref$overlayAccessibi,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'menu' : _ref$testID,\n    anchor = _ref.anchor,\n    onDismiss = _ref.onDismiss,\n    anchorPosition = _ref.anchorPosition,\n    contentStyle = _ref.contentStyle,\n    style = _ref.style,\n    _ref$elevation = _ref.elevation,\n    elevation = _ref$elevation === void 0 ? DEFAULT_ELEVATION : _ref$elevation,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? DEFAULT_MODE : _ref$mode,\n    children = _ref.children,\n    themeOverrides = _ref.theme,\n    keyboardShouldPersistTaps = _ref.keyboardShouldPersistTaps;\n  var theme = useInternalTheme(themeOverrides);\n  var insets = useSafeAreaInsets();\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    rendered = _React$useState2[0],\n    setRendered = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    left = _React$useState4[0],\n    setLeft = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    top = _React$useState6[0],\n    setTop = _React$useState6[1];\n  var _React$useState7 = React.useState({\n      width: 0,\n      height: 0\n    }),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    menuLayout = _React$useState8[0],\n    setMenuLayout = _React$useState8[1];\n  var _React$useState9 = React.useState({\n      width: 0,\n      height: 0\n    }),\n    _React$useState0 = _slicedToArray(_React$useState9, 2),\n    anchorLayout = _React$useState0[0],\n    setAnchorLayout = _React$useState0[1];\n  var _React$useState1 = React.useState({\n      width: WINDOW_LAYOUT.width,\n      height: WINDOW_LAYOUT.height\n    }),\n    _React$useState10 = _slicedToArray(_React$useState1, 2),\n    windowLayout = _React$useState10[0],\n    setWindowLayout = _React$useState10[1];\n  var opacityAnimationRef = React.useRef(new Animated.Value(0));\n  var scaleAnimationRef = React.useRef(new Animated.ValueXY({\n    x: 0,\n    y: 0\n  }));\n  var keyboardHeightRef = React.useRef(0);\n  var prevVisible = React.useRef(null);\n  var anchorRef = React.useRef(null);\n  var menuRef = React.useRef(null);\n  var prevRendered = React.useRef(false);\n  var keyboardDidShow = React.useCallback(function (e) {\n    var keyboardHeight = e.endCoordinates.height;\n    keyboardHeightRef.current = keyboardHeight;\n  }, []);\n  var keyboardDidHide = React.useCallback(function () {\n    keyboardHeightRef.current = 0;\n  }, []);\n  var keyboardDidShowListenerRef = React.useRef();\n  var keyboardDidHideListenerRef = React.useRef();\n  var backHandlerSubscriptionRef = React.useRef();\n  var dimensionsSubscriptionRef = React.useRef();\n  var handleDismiss = React.useCallback(function () {\n    if (visible) {\n      onDismiss === null || onDismiss === void 0 ? void 0 : onDismiss();\n    }\n  }, [onDismiss, visible]);\n  var handleKeypress = React.useCallback(function (e) {\n    if (e.key === 'Escape') {\n      onDismiss === null || onDismiss === void 0 ? void 0 : onDismiss();\n    }\n  }, [onDismiss]);\n  var removeListeners = React.useCallback(function () {\n    var _backHandlerSubscript, _dimensionsSubscripti;\n    (_backHandlerSubscript = backHandlerSubscriptionRef.current) === null || _backHandlerSubscript === void 0 ? void 0 : _backHandlerSubscript.remove();\n    (_dimensionsSubscripti = dimensionsSubscriptionRef.current) === null || _dimensionsSubscripti === void 0 ? void 0 : _dimensionsSubscripti.remove();\n    isBrowser() && document.removeEventListener('keyup', handleKeypress);\n  }, [handleKeypress]);\n  var attachListeners = React.useCallback(function () {\n    backHandlerSubscriptionRef.current = addEventListener(BackHandler, 'hardwareBackPress', handleDismiss);\n    dimensionsSubscriptionRef.current = addEventListener(Dimensions, 'change', handleDismiss);\n    Platform.OS === 'web' && document.addEventListener('keyup', handleKeypress);\n  }, [handleDismiss, handleKeypress]);\n  var measureMenuLayout = function measureMenuLayout() {\n    return new Promise(function (resolve) {\n      if (menuRef.current) {\n        menuRef.current.measureInWindow(function (x, y, width, height) {\n          resolve({\n            x: x,\n            y: y,\n            width: width,\n            height: height\n          });\n        });\n      }\n    });\n  };\n  var measureAnchorLayout = React.useCallback(function () {\n    return new Promise(function (resolve) {\n      if (isCoordinate(anchor)) {\n        resolve({\n          x: anchor.x,\n          y: anchor.y,\n          width: 0,\n          height: 0\n        });\n        return;\n      }\n      if (anchorRef.current) {\n        anchorRef.current.measureInWindow(function (x, y, width, height) {\n          resolve({\n            x: x,\n            y: y,\n            width: width,\n            height: height\n          });\n        });\n      }\n    });\n  }, [anchor]);\n  var show = React.useCallback(_asyncToGenerator(function* () {\n    var windowLayoutResult = Dimensions.get('window');\n    var _yield$Promise$all = yield Promise.all([measureMenuLayout(), measureAnchorLayout()]),\n      _yield$Promise$all2 = _slicedToArray(_yield$Promise$all, 2),\n      menuLayoutResult = _yield$Promise$all2[0],\n      anchorLayoutResult = _yield$Promise$all2[1];\n    if (!windowLayoutResult.width || !windowLayoutResult.height || !menuLayoutResult.width || !menuLayoutResult.height || !anchorLayoutResult.width && !isCoordinate(anchor) || !anchorLayoutResult.height && !isCoordinate(anchor)) {\n      requestAnimationFrame(show);\n      return;\n    }\n    setLeft(anchorLayoutResult.x);\n    setTop(anchorLayoutResult.y);\n    setAnchorLayout({\n      height: anchorLayoutResult.height,\n      width: anchorLayoutResult.width\n    });\n    setMenuLayout({\n      height: menuLayoutResult.height,\n      width: menuLayoutResult.width\n    });\n    setWindowLayout({\n      height: windowLayoutResult.height - keyboardHeightRef.current,\n      width: windowLayoutResult.width\n    });\n    attachListeners();\n    var animation = theme.animation;\n    Animated.parallel([Animated.timing(scaleAnimationRef.current, {\n      toValue: {\n        x: menuLayoutResult.width,\n        y: menuLayoutResult.height\n      },\n      duration: ANIMATION_DURATION * animation.scale,\n      easing: EASING,\n      useNativeDriver: true\n    }), Animated.timing(opacityAnimationRef.current, {\n      toValue: 1,\n      duration: ANIMATION_DURATION * animation.scale,\n      easing: EASING,\n      useNativeDriver: true\n    })]).start(function (_ref2) {\n      var finished = _ref2.finished;\n      if (finished) {\n        focusFirstDOMNode(menuRef.current);\n        prevRendered.current = true;\n      }\n    });\n  }), [anchor, attachListeners, measureAnchorLayout, theme]);\n  var hide = React.useCallback(function () {\n    removeListeners();\n    var animation = theme.animation;\n    Animated.timing(opacityAnimationRef.current, {\n      toValue: 0,\n      duration: ANIMATION_DURATION * animation.scale,\n      easing: EASING,\n      useNativeDriver: true\n    }).start(function (_ref3) {\n      var finished = _ref3.finished;\n      if (finished) {\n        setMenuLayout({\n          width: 0,\n          height: 0\n        });\n        setRendered(false);\n        prevRendered.current = false;\n        focusFirstDOMNode(anchorRef.current);\n      }\n    });\n  }, [removeListeners, theme]);\n  var updateVisibility = React.useCallback(function () {\n    var _ref5 = _asyncToGenerator(function* (display) {\n      yield Promise.resolve().then(function () {\n        if (display && !prevRendered.current) {\n          show();\n          return;\n        }\n        if (!display && prevRendered.current) {\n          hide();\n        }\n        return;\n      });\n    });\n    return function (_x) {\n      return _ref5.apply(this, arguments);\n    };\n  }(), [hide, show]);\n  React.useEffect(function () {\n    var opacityAnimation = opacityAnimationRef.current;\n    var scaleAnimation = scaleAnimationRef.current;\n    keyboardDidShowListenerRef.current = Keyboard.addListener('keyboardDidShow', keyboardDidShow);\n    keyboardDidHideListenerRef.current = Keyboard.addListener('keyboardDidHide', keyboardDidHide);\n    return function () {\n      var _keyboardDidShowListe, _keyboardDidHideListe;\n      removeListeners();\n      (_keyboardDidShowListe = keyboardDidShowListenerRef.current) === null || _keyboardDidShowListe === void 0 ? void 0 : _keyboardDidShowListe.remove();\n      (_keyboardDidHideListe = keyboardDidHideListenerRef.current) === null || _keyboardDidHideListe === void 0 ? void 0 : _keyboardDidHideListe.remove();\n      scaleAnimation.removeAllListeners();\n      opacityAnimation === null || opacityAnimation === void 0 ? void 0 : opacityAnimation.removeAllListeners();\n    };\n  }, [removeListeners, keyboardDidHide, keyboardDidShow]);\n  React.useEffect(function () {\n    if (prevVisible.current !== visible) {\n      prevVisible.current = visible;\n      if (visible !== rendered) {\n        setRendered(visible);\n      }\n    }\n  }, [visible, rendered]);\n  React.useEffect(function () {\n    updateVisibility(rendered);\n  }, [rendered, updateVisibility]);\n  var additionalVerticalValue = Platform.select({\n    android: statusBarHeight != null ? statusBarHeight : insets.top,\n    default: 0\n  });\n  var positionTransforms = [];\n  var leftTransformation = left;\n  var topTransformation = !isCoordinate(anchorRef.current) && anchorPosition === 'bottom' ? top + anchorLayout.height : top;\n  if (left <= windowLayout.width - menuLayout.width - SCREEN_INDENT) {\n    positionTransforms.push({\n      translateX: scaleAnimationRef.current.x.interpolate({\n        inputRange: [0, menuLayout.width],\n        outputRange: [-(menuLayout.width / 2), 0]\n      })\n    });\n    if (leftTransformation < SCREEN_INDENT) {\n      leftTransformation = SCREEN_INDENT;\n    }\n  } else {\n    positionTransforms.push({\n      translateX: scaleAnimationRef.current.x.interpolate({\n        inputRange: [0, menuLayout.width],\n        outputRange: [menuLayout.width / 2, 0]\n      })\n    });\n    leftTransformation += anchorLayout.width - menuLayout.width;\n    var right = leftTransformation + menuLayout.width;\n    if (right > windowLayout.width - SCREEN_INDENT) {\n      leftTransformation = windowLayout.width - SCREEN_INDENT - menuLayout.width;\n    }\n  }\n  var scrollableMenuHeight = 0;\n  if (topTransformation >= windowLayout.height - menuLayout.height - SCREEN_INDENT - additionalVerticalValue && topTransformation <= windowLayout.height - topTransformation) {\n    scrollableMenuHeight = windowLayout.height - topTransformation - SCREEN_INDENT - additionalVerticalValue;\n  } else if (topTransformation >= windowLayout.height - menuLayout.height - SCREEN_INDENT - additionalVerticalValue && topTransformation >= windowLayout.height - top && topTransformation <= menuLayout.height - anchorLayout.height + SCREEN_INDENT - additionalVerticalValue) {\n    scrollableMenuHeight = topTransformation + anchorLayout.height - SCREEN_INDENT + additionalVerticalValue;\n  }\n  scrollableMenuHeight = scrollableMenuHeight > windowLayout.height - 2 * SCREEN_INDENT ? windowLayout.height - 2 * SCREEN_INDENT : scrollableMenuHeight;\n  if (topTransformation <= windowLayout.height - menuLayout.height - SCREEN_INDENT - additionalVerticalValue || topTransformation >= windowLayout.height - menuLayout.height - SCREEN_INDENT - additionalVerticalValue && topTransformation <= windowLayout.height - topTransformation) {\n    positionTransforms.push({\n      translateY: scaleAnimationRef.current.y.interpolate({\n        inputRange: [0, menuLayout.height],\n        outputRange: [-((scrollableMenuHeight || menuLayout.height) / 2), 0]\n      })\n    });\n    if (topTransformation < SCREEN_INDENT) {\n      topTransformation = SCREEN_INDENT;\n    }\n  } else {\n    positionTransforms.push({\n      translateY: scaleAnimationRef.current.y.interpolate({\n        inputRange: [0, menuLayout.height],\n        outputRange: [(scrollableMenuHeight || menuLayout.height) / 2, 0]\n      })\n    });\n    topTransformation += anchorLayout.height - (scrollableMenuHeight || menuLayout.height);\n    var bottom = topTransformation + (scrollableMenuHeight || menuLayout.height) + additionalVerticalValue;\n    if (bottom > windowLayout.height - SCREEN_INDENT) {\n      topTransformation = scrollableMenuHeight === windowLayout.height - 2 * SCREEN_INDENT ? -SCREEN_INDENT * 2 : windowLayout.height - menuLayout.height - SCREEN_INDENT - additionalVerticalValue;\n    }\n  }\n  var shadowMenuContainerStyle = _objectSpread(_objectSpread({\n    opacity: opacityAnimationRef.current,\n    transform: [{\n      scaleX: scaleAnimationRef.current.x.interpolate({\n        inputRange: [0, menuLayout.width],\n        outputRange: [0, 1]\n      })\n    }, {\n      scaleY: scaleAnimationRef.current.y.interpolate({\n        inputRange: [0, menuLayout.height],\n        outputRange: [0, 1]\n      })\n    }],\n    borderRadius: theme.roundness\n  }, !theme.isV3 && {\n    elevation: 8\n  }), scrollableMenuHeight ? {\n    height: scrollableMenuHeight\n  } : {});\n  var positionStyle = _objectSpread({\n    top: isCoordinate(anchor) ? topTransformation : topTransformation + additionalVerticalValue\n  }, I18nManager.getConstants().isRTL ? {\n    right: leftTransformation\n  } : {\n    left: leftTransformation\n  });\n  var pointerEvents = visible ? 'box-none' : 'none';\n  return React.createElement(View, {\n    ref: function ref(_ref6) {\n      return anchorRef.current = _ref6;\n    },\n    collapsable: false\n  }, isCoordinate(anchor) ? null : anchor, rendered ? React.createElement(Portal, null, React.createElement(Pressable, {\n    accessibilityLabel: overlayAccessibilityLabel,\n    accessibilityRole: \"button\",\n    onPress: onDismiss,\n    style: styles.pressableOverlay\n  }), React.createElement(View, {\n    ref: function ref(_ref7) {\n      return menuRef.current = _ref7;\n    },\n    collapsable: false,\n    accessibilityViewIsModal: visible,\n    style: [styles.wrapper, positionStyle, style],\n    pointerEvents: pointerEvents,\n    onAccessibilityEscape: onDismiss,\n    testID: `${testID}-view`\n  }, React.createElement(Animated.View, {\n    pointerEvents: pointerEvents,\n    style: {\n      transform: positionTransforms\n    }\n  }, React.createElement(Surface, _extends({\n    mode: mode,\n    pointerEvents: pointerEvents,\n    style: [styles.shadowMenuContainer, shadowMenuContainerStyle, theme.isV3 && {\n      backgroundColor: theme.colors.elevation[ELEVATION_LEVELS_MAP[elevation]]\n    }, contentStyle]\n  }, theme.isV3 && {\n    elevation: elevation\n  }, {\n    testID: `${testID}-surface`,\n    theme: theme\n  }), scrollableMenuHeight && React.createElement(ScrollView, {\n    keyboardShouldPersistTaps: keyboardShouldPersistTaps\n  }, children) || React.createElement(React.Fragment, null, children))))) : null);\n};\nMenu.Item = MenuItem;\nvar styles = StyleSheet.create({\n  wrapper: {\n    position: 'absolute'\n  },\n  shadowMenuContainer: {\n    opacity: 0,\n    paddingVertical: 8\n  },\n  pressableOverlay: _objectSpread(_objectSpread(_objectSpread({}, Platform.select({\n    web: {\n      cursor: 'auto'\n    }\n  })), StyleSheet.absoluteFillObject), {}, {\n    width: '100%'\n  })\n});\nexport default Menu;", "map": {"version": 3, "names": ["React", "Animated", "Dimensions", "Easing", "findNodeHandle", "I18nManager", "Keyboard", "Platform", "ScrollView", "StyleSheet", "View", "Pressable", "useSafeAreaInsets", "MenuItem", "useInternalTheme", "ElevationLevels", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "Portal", "Surface", "SCREEN_INDENT", "ANIMATION_DURATION", "EASING", "bezier", "WINDOW_LAYOUT", "get", "DEFAULT_ELEVATION", "ELEVATION_LEVELS_MAP", "Object", "values", "DEFAULT_MODE", "focusFirstDOMNode", "el", "OS", "node", "focusableNode", "querySelector", "focus", "isCoordinate", "anchor", "isValidElement", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "global", "<PERSON><PERSON>", "_ref", "visible", "statusBarHeight", "_ref$overlayAccessibi", "overlayAccessibilityLabel", "_ref$testID", "testID", "on<PERSON><PERSON><PERSON>", "anchorPosition", "contentStyle", "style", "_ref$elevation", "elevation", "_ref$mode", "mode", "children", "themeOverrides", "theme", "keyboardShouldPersistTaps", "insets", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "rendered", "setRendered", "_React$useState3", "_React$useState4", "left", "setLeft", "_React$useState5", "_React$useState6", "top", "setTop", "_React$useState7", "width", "height", "_React$useState8", "menuLayout", "setMenuLayout", "_React$useState9", "_React$useState0", "anchorLayout", "setAnchorLayout", "_React$useState1", "_React$useState10", "windowLayout", "setWindowLayout", "opacityAnimationRef", "useRef", "Value", "scaleAnimationRef", "ValueXY", "keyboardHeightRef", "prevVisible", "anchorRef", "menuRef", "prevRendered", "keyboardDidShow", "useCallback", "e", "keyboardHeight", "endCoordinates", "current", "keyboardDidHide", "keyboardDidShowListenerRef", "keyboardDidHideListenerRef", "backHandlerSubscriptionRef", "dimensionsSubscriptionRef", "handle<PERSON><PERSON><PERSON>", "handleKeypress", "key", "removeListeners", "_backHandlerSubscript", "_dimensionsSubscripti", "remove", "document", "removeEventListener", "attachListeners", "measureMenuLayout", "Promise", "resolve", "measureInWindow", "measureAnchorLayout", "show", "_asyncToGenerator", "windowLayoutResult", "_yield$Promise$all", "all", "_yield$Promise$all2", "menuLayoutResult", "anchorLayoutResult", "requestAnimationFrame", "animation", "parallel", "timing", "toValue", "duration", "scale", "easing", "useNativeDriver", "start", "_ref2", "finished", "hide", "_ref3", "updateVisibility", "_ref5", "display", "then", "_x", "apply", "arguments", "useEffect", "opacityAnimation", "scaleAnimation", "addListener", "_keyboardDidShowListe", "_keyboardDidHideListe", "removeAllListeners", "additionalVerticalValue", "select", "android", "default", "positionTransforms", "leftTransformation", "topTransformation", "push", "translateX", "interpolate", "inputRange", "outputRange", "right", "scrollableMenuHeight", "translateY", "bottom", "shadowMenuContainerStyle", "_objectSpread", "opacity", "transform", "scaleX", "scaleY", "borderRadius", "roundness", "isV3", "positionStyle", "getConstants", "isRTL", "pointerEvents", "createElement", "ref", "collapsable", "accessibilityLabel", "accessibilityRole", "onPress", "styles", "pressableOverlay", "accessibilityViewIsModal", "wrapper", "onAccessibilityEscape", "_extends", "shadowMenuContainer", "backgroundColor", "colors", "Fragment", "<PERSON><PERSON>", "create", "position", "paddingVertical", "web", "cursor", "absoluteFillObject"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Menu/Menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Dimensions,\n  Easing,\n  EmitterSubscription,\n  findNodeHandle,\n  I18nManager,\n  Keyboard,\n  KeyboardEvent as RNKeyboardEvent,\n  LayoutRectangle,\n  NativeEventSubscription,\n  Platform,\n  ScrollView,\n  ScrollViewProps,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n  Pressable,\n} from 'react-native';\n\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\n\nimport MenuItem from './MenuItem';\nimport { useInternalTheme } from '../../core/theming';\nimport type { MD3Elevation, ThemeProp } from '../../types';\nimport { ElevationLevels } from '../../types';\nimport { addEventListener } from '../../utils/addEventListener';\nimport { BackHandler } from '../../utils/BackHandler/BackHandler';\nimport Portal from '../Portal/Portal';\nimport Surface from '../Surface';\n\nexport type Props = {\n  /**\n   * Whether the Menu is currently visible.\n   */\n  visible: boolean;\n  /**\n   * The anchor to open the menu from. In most cases, it will be a button that opens the menu.\n   */\n  anchor: React.ReactNode | { x: number; y: number };\n  /**\n   * Whether the menu should open at the top of the anchor or at its bottom.\n   * Applied only when anchor is a node, not an x/y position.\n   */\n  anchorPosition?: 'top' | 'bottom';\n  /**\n   * Extra margin to add at the top of the menu to account for translucent status bar on Android.\n   * If you are using Expo, we assume translucent status bar and set a height for status bar automatically.\n   * Pass `0` or a custom value to and customize it.\n   * This is automatically handled on iOS.\n   */\n  statusBarHeight?: number;\n  /**\n   * Callback called when Menu is dismissed. The `visible` prop needs to be updated when this is called.\n   */\n  onDismiss?: () => void;\n  /**\n   * Accessibility label for the overlay. This is read by the screen reader when the user taps outside the menu.\n   */\n  overlayAccessibilityLabel?: string;\n  /**\n   * Content of the `Menu`.\n   */\n  children: React.ReactNode;\n  /**\n   * Style of menu's inner content.\n   */\n  contentStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Elevation level of the menu's content. Shadow styles are calculated based on this value. Default `backgroundColor` is taken from the corresponding `theme.colors.elevation` property. By default equals `2`.\n   * @supported Available in v5.x with theme version 3\n   */\n  elevation?: MD3Elevation;\n  /**\n   * Mode of the menu's content.\n   * - `elevated` - Surface with a shadow and background color corresponding to set `elevation` value.\n   * - `flat` - Surface without a shadow, with the background color corresponding to set `elevation` value.\n   *\n   * @supported Available in v5.x with theme version 3\n   */\n  mode?: 'flat' | 'elevated';\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Inner ScrollView prop\n   */\n  keyboardShouldPersistTaps?: ScrollViewProps['keyboardShouldPersistTaps'];\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n// Minimum padding between the edge of the screen and the menu\nconst SCREEN_INDENT = 8;\n// From https://material.io/design/motion/speed.html#duration\nconst ANIMATION_DURATION = 250;\n// From the 'Standard easing' section of https://material.io/design/motion/speed.html#easing\nconst EASING = Easing.bezier(0.4, 0, 0.2, 1);\n\nconst WINDOW_LAYOUT = Dimensions.get('window');\n\nconst DEFAULT_ELEVATION: MD3Elevation = 2;\nexport const ELEVATION_LEVELS_MAP = Object.values(\n  ElevationLevels\n) as ElevationLevels[];\n\nconst DEFAULT_MODE = 'elevated';\n\nconst focusFirstDOMNode = (el: View | null | undefined) => {\n  if (el && Platform.OS === 'web') {\n    // When in the browser, we want to focus the first focusable item on toggle\n    // For example, when menu is shown, focus the first item in the menu\n    // And when menu is dismissed, send focus back to the button to resume tabbing\n    const node: any = findNodeHandle(el);\n    const focusableNode = node.querySelector(\n      // This is a rough list of selectors that can be focused\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n    );\n\n    focusableNode?.focus();\n  }\n};\n\nconst isCoordinate = (anchor: any): anchor is { x: number; y: number } =>\n  !React.isValidElement(anchor) &&\n  typeof anchor?.x === 'number' &&\n  typeof anchor?.y === 'number';\n\nconst isBrowser = () => Platform.OS === 'web' && 'document' in global;\n\n/**\n * Menus display a list of choices on temporary elevated surfaces. Their placement varies based on the element that opens them.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Button, Menu, Divider, PaperProvider } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const openMenu = () => setVisible(true);\n *\n *   const closeMenu = () => setVisible(false);\n *\n *   return (\n *     <PaperProvider>\n *       <View\n *         style={{\n *           paddingTop: 50,\n *           flexDirection: 'row',\n *           justifyContent: 'center',\n *         }}>\n *         <Menu\n *           visible={visible}\n *           onDismiss={closeMenu}\n *           anchor={<Button onPress={openMenu}>Show menu</Button>}>\n *           <Menu.Item onPress={() => {}} title=\"Item 1\" />\n *           <Menu.Item onPress={() => {}} title=\"Item 2\" />\n *           <Divider />\n *           <Menu.Item onPress={() => {}} title=\"Item 3\" />\n *         </Menu>\n *       </View>\n *     </PaperProvider>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n *\n * ### Note\n * When using `Menu` within a React Native's `Modal` component, you need to wrap all\n * `Modal` contents within a `PaperProvider` in order for the menu to show. This\n * wrapping is not necessary if you use Paper's `Modal` instead.\n */\n\nconst Menu = ({\n  visible,\n  statusBarHeight,\n  overlayAccessibilityLabel = 'Close menu',\n  testID = 'menu',\n  anchor,\n  onDismiss,\n  anchorPosition,\n  contentStyle,\n  style,\n  elevation = DEFAULT_ELEVATION,\n  mode = DEFAULT_MODE,\n  children,\n  theme: themeOverrides,\n  keyboardShouldPersistTaps,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const insets = useSafeAreaInsets();\n  const [rendered, setRendered] = React.useState(visible);\n  const [left, setLeft] = React.useState(0);\n  const [top, setTop] = React.useState(0);\n  const [menuLayout, setMenuLayout] = React.useState({ width: 0, height: 0 });\n  const [anchorLayout, setAnchorLayout] = React.useState({\n    width: 0,\n    height: 0,\n  });\n  const [windowLayout, setWindowLayout] = React.useState({\n    width: WINDOW_LAYOUT.width,\n    height: WINDOW_LAYOUT.height,\n  });\n\n  const opacityAnimationRef = React.useRef(new Animated.Value(0));\n  const scaleAnimationRef = React.useRef(new Animated.ValueXY({ x: 0, y: 0 }));\n  const keyboardHeightRef = React.useRef(0);\n  const prevVisible = React.useRef<boolean | null>(null);\n  const anchorRef = React.useRef<View | null>(null);\n  const menuRef = React.useRef<View | null>(null);\n  const prevRendered = React.useRef(false);\n\n  const keyboardDidShow = React.useCallback((e: RNKeyboardEvent) => {\n    const keyboardHeight = e.endCoordinates.height;\n    keyboardHeightRef.current = keyboardHeight;\n  }, []);\n\n  const keyboardDidHide = React.useCallback(() => {\n    keyboardHeightRef.current = 0;\n  }, []);\n\n  const keyboardDidShowListenerRef: React.MutableRefObject<\n    EmitterSubscription | undefined\n  > = React.useRef();\n  const keyboardDidHideListenerRef: React.MutableRefObject<\n    EmitterSubscription | undefined\n  > = React.useRef();\n\n  const backHandlerSubscriptionRef: React.MutableRefObject<\n    NativeEventSubscription | undefined\n  > = React.useRef();\n  const dimensionsSubscriptionRef: React.MutableRefObject<\n    NativeEventSubscription | undefined\n  > = React.useRef();\n\n  const handleDismiss = React.useCallback(() => {\n    if (visible) {\n      onDismiss?.();\n    }\n  }, [onDismiss, visible]);\n\n  const handleKeypress = React.useCallback(\n    (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onDismiss?.();\n      }\n    },\n    [onDismiss]\n  );\n\n  const removeListeners = React.useCallback(() => {\n    backHandlerSubscriptionRef.current?.remove();\n    dimensionsSubscriptionRef.current?.remove();\n    isBrowser() && document.removeEventListener('keyup', handleKeypress);\n  }, [handleKeypress]);\n\n  const attachListeners = React.useCallback(() => {\n    backHandlerSubscriptionRef.current = addEventListener(\n      BackHandler,\n      'hardwareBackPress',\n      handleDismiss\n    );\n    dimensionsSubscriptionRef.current = addEventListener(\n      Dimensions,\n      'change',\n      handleDismiss\n    );\n    Platform.OS === 'web' && document.addEventListener('keyup', handleKeypress);\n  }, [handleDismiss, handleKeypress]);\n\n  const measureMenuLayout = () =>\n    new Promise<LayoutRectangle>((resolve) => {\n      if (menuRef.current) {\n        menuRef.current.measureInWindow((x, y, width, height) => {\n          resolve({ x, y, width, height });\n        });\n      }\n    });\n\n  const measureAnchorLayout = React.useCallback(\n    () =>\n      new Promise<LayoutRectangle>((resolve) => {\n        if (isCoordinate(anchor)) {\n          resolve({ x: anchor.x, y: anchor.y, width: 0, height: 0 });\n          return;\n        }\n\n        if (anchorRef.current) {\n          anchorRef.current.measureInWindow((x, y, width, height) => {\n            resolve({ x, y, width, height });\n          });\n        }\n      }),\n    [anchor]\n  );\n\n  const show = React.useCallback(async () => {\n    const windowLayoutResult = Dimensions.get('window');\n    const [menuLayoutResult, anchorLayoutResult] = await Promise.all([\n      measureMenuLayout(),\n      measureAnchorLayout(),\n    ]);\n\n    // When visible is true for first render\n    // native views can be still not rendered and\n    // measureMenuLayout/measureAnchorLayout functions\n    // return wrong values e.g { x:0, y: 0, width: 0, height: 0 }\n    // so we have to wait until views are ready\n    // and rerun this function to show menu\n    if (\n      !windowLayoutResult.width ||\n      !windowLayoutResult.height ||\n      !menuLayoutResult.width ||\n      !menuLayoutResult.height ||\n      (!anchorLayoutResult.width && !isCoordinate(anchor)) ||\n      (!anchorLayoutResult.height && !isCoordinate(anchor))\n    ) {\n      requestAnimationFrame(show);\n      return;\n    }\n\n    setLeft(anchorLayoutResult.x);\n    setTop(anchorLayoutResult.y);\n    setAnchorLayout({\n      height: anchorLayoutResult.height,\n      width: anchorLayoutResult.width,\n    });\n\n    setMenuLayout({\n      height: menuLayoutResult.height,\n      width: menuLayoutResult.width,\n    });\n\n    setWindowLayout({\n      height: windowLayoutResult.height - keyboardHeightRef.current,\n      width: windowLayoutResult.width,\n    });\n\n    attachListeners();\n    const { animation } = theme;\n    Animated.parallel([\n      Animated.timing(scaleAnimationRef.current, {\n        toValue: { x: menuLayoutResult.width, y: menuLayoutResult.height },\n        duration: ANIMATION_DURATION * animation.scale,\n        easing: EASING,\n        useNativeDriver: true,\n      }),\n      Animated.timing(opacityAnimationRef.current, {\n        toValue: 1,\n        duration: ANIMATION_DURATION * animation.scale,\n        easing: EASING,\n        useNativeDriver: true,\n      }),\n    ]).start(({ finished }) => {\n      if (finished) {\n        focusFirstDOMNode(menuRef.current);\n        prevRendered.current = true;\n      }\n    });\n  }, [anchor, attachListeners, measureAnchorLayout, theme]);\n\n  const hide = React.useCallback(() => {\n    removeListeners();\n\n    const { animation } = theme;\n\n    Animated.timing(opacityAnimationRef.current, {\n      toValue: 0,\n      duration: ANIMATION_DURATION * animation.scale,\n      easing: EASING,\n      useNativeDriver: true,\n    }).start(({ finished }) => {\n      if (finished) {\n        setMenuLayout({ width: 0, height: 0 });\n        setRendered(false);\n        prevRendered.current = false;\n        focusFirstDOMNode(anchorRef.current);\n      }\n    });\n  }, [removeListeners, theme]);\n\n  const updateVisibility = React.useCallback(\n    async (display: boolean) => {\n      // Menu is rendered in Portal, which updates items asynchronously\n      // We need to do the same here so that the ref is up-to-date\n      await Promise.resolve().then(() => {\n        if (display && !prevRendered.current) {\n          show();\n          return;\n        }\n\n        if (!display && prevRendered.current) {\n          hide();\n        }\n\n        return;\n      });\n    },\n    [hide, show]\n  );\n\n  React.useEffect(() => {\n    const opacityAnimation = opacityAnimationRef.current;\n    const scaleAnimation = scaleAnimationRef.current;\n    keyboardDidShowListenerRef.current = Keyboard.addListener(\n      'keyboardDidShow',\n      keyboardDidShow\n    );\n    keyboardDidHideListenerRef.current = Keyboard.addListener(\n      'keyboardDidHide',\n      keyboardDidHide\n    );\n\n    return () => {\n      removeListeners();\n      keyboardDidShowListenerRef.current?.remove();\n      keyboardDidHideListenerRef.current?.remove();\n      scaleAnimation.removeAllListeners();\n      opacityAnimation?.removeAllListeners();\n    };\n  }, [removeListeners, keyboardDidHide, keyboardDidShow]);\n\n  React.useEffect(() => {\n    if (prevVisible.current !== visible) {\n      prevVisible.current = visible;\n\n      if (visible !== rendered) {\n        setRendered(visible);\n      }\n    }\n  }, [visible, rendered]);\n\n  React.useEffect(() => {\n    updateVisibility(rendered);\n  }, [rendered, updateVisibility]);\n\n  // I don't know why but on Android measure function is wrong by 24\n  const additionalVerticalValue = Platform.select({\n    android: statusBarHeight ?? insets.top,\n    default: 0,\n  });\n\n  // We need to translate menu while animating scale to imitate transform origin for scale animation\n  const positionTransforms = [];\n  let leftTransformation = left;\n  let topTransformation =\n    !isCoordinate(anchorRef.current) && anchorPosition === 'bottom'\n      ? top + anchorLayout.height\n      : top;\n\n  // Check if menu fits horizontally and if not align it to right.\n  if (left <= windowLayout.width - menuLayout.width - SCREEN_INDENT) {\n    positionTransforms.push({\n      translateX: scaleAnimationRef.current.x.interpolate({\n        inputRange: [0, menuLayout.width],\n        outputRange: [-(menuLayout.width / 2), 0],\n      }),\n    });\n\n    // Check if menu position has enough space from left side\n    if (leftTransformation < SCREEN_INDENT) {\n      leftTransformation = SCREEN_INDENT;\n    }\n  } else {\n    positionTransforms.push({\n      translateX: scaleAnimationRef.current.x.interpolate({\n        inputRange: [0, menuLayout.width],\n        outputRange: [menuLayout.width / 2, 0],\n      }),\n    });\n\n    leftTransformation += anchorLayout.width - menuLayout.width;\n\n    const right = leftTransformation + menuLayout.width;\n    // Check if menu position has enough space from right side\n    if (right > windowLayout.width - SCREEN_INDENT) {\n      leftTransformation =\n        windowLayout.width - SCREEN_INDENT - menuLayout.width;\n    }\n  }\n\n  // If the menu is larger than available vertical space,\n  // calculate the height of scrollable view\n  let scrollableMenuHeight = 0;\n\n  // Check if the menu should be scrollable\n  if (\n    // Check if the menu overflows from bottom side\n    topTransformation >=\n      windowLayout.height -\n        menuLayout.height -\n        SCREEN_INDENT -\n        additionalVerticalValue &&\n    // And bottom side of the screen has more space than top side\n    topTransformation <= windowLayout.height - topTransformation\n  ) {\n    // Scrollable menu should be below the anchor (expands downwards)\n    scrollableMenuHeight =\n      windowLayout.height -\n      topTransformation -\n      SCREEN_INDENT -\n      additionalVerticalValue;\n  } else if (\n    // Check if the menu overflows from bottom side\n    topTransformation >=\n      windowLayout.height -\n        menuLayout.height -\n        SCREEN_INDENT -\n        additionalVerticalValue &&\n    // And top side of the screen has more space than bottom side\n    topTransformation >= windowLayout.height - top &&\n    // And menu overflows from top side\n    topTransformation <=\n      menuLayout.height -\n        anchorLayout.height +\n        SCREEN_INDENT -\n        additionalVerticalValue\n  ) {\n    // Scrollable menu should be above the anchor (expands upwards)\n    scrollableMenuHeight =\n      topTransformation +\n      anchorLayout.height -\n      SCREEN_INDENT +\n      additionalVerticalValue;\n  }\n\n  // Scrollable menu max height\n  scrollableMenuHeight =\n    scrollableMenuHeight > windowLayout.height - 2 * SCREEN_INDENT\n      ? windowLayout.height - 2 * SCREEN_INDENT\n      : scrollableMenuHeight;\n\n  // Menu is typically positioned below the element that generates it\n  // So first check if it fits below the anchor (expands downwards)\n  if (\n    // Check if menu fits vertically\n    topTransformation <=\n      windowLayout.height -\n        menuLayout.height -\n        SCREEN_INDENT -\n        additionalVerticalValue ||\n    // Or if the menu overflows from bottom side\n    (topTransformation >=\n      windowLayout.height -\n        menuLayout.height -\n        SCREEN_INDENT -\n        additionalVerticalValue &&\n      // And bottom side of the screen has more space than top side\n      topTransformation <= windowLayout.height - topTransformation)\n  ) {\n    positionTransforms.push({\n      translateY: scaleAnimationRef.current.y.interpolate({\n        inputRange: [0, menuLayout.height],\n        outputRange: [-((scrollableMenuHeight || menuLayout.height) / 2), 0],\n      }),\n    });\n\n    // Check if menu position has enough space from top side\n    if (topTransformation < SCREEN_INDENT) {\n      topTransformation = SCREEN_INDENT;\n    }\n  } else {\n    positionTransforms.push({\n      translateY: scaleAnimationRef.current.y.interpolate({\n        inputRange: [0, menuLayout.height],\n        outputRange: [(scrollableMenuHeight || menuLayout.height) / 2, 0],\n      }),\n    });\n\n    topTransformation +=\n      anchorLayout.height - (scrollableMenuHeight || menuLayout.height);\n\n    const bottom =\n      topTransformation +\n      (scrollableMenuHeight || menuLayout.height) +\n      additionalVerticalValue;\n\n    // Check if menu position has enough space from bottom side\n    if (bottom > windowLayout.height - SCREEN_INDENT) {\n      topTransformation =\n        scrollableMenuHeight === windowLayout.height - 2 * SCREEN_INDENT\n          ? -SCREEN_INDENT * 2\n          : windowLayout.height -\n            menuLayout.height -\n            SCREEN_INDENT -\n            additionalVerticalValue;\n    }\n  }\n\n  const shadowMenuContainerStyle = {\n    opacity: opacityAnimationRef.current,\n    transform: [\n      {\n        scaleX: scaleAnimationRef.current.x.interpolate({\n          inputRange: [0, menuLayout.width],\n          outputRange: [0, 1],\n        }),\n      },\n      {\n        scaleY: scaleAnimationRef.current.y.interpolate({\n          inputRange: [0, menuLayout.height],\n          outputRange: [0, 1],\n        }),\n      },\n    ],\n    borderRadius: theme.roundness,\n    ...(!theme.isV3 && { elevation: 8 }),\n    ...(scrollableMenuHeight ? { height: scrollableMenuHeight } : {}),\n  };\n\n  const positionStyle = {\n    top: isCoordinate(anchor)\n      ? topTransformation\n      : topTransformation + additionalVerticalValue,\n    ...(I18nManager.getConstants().isRTL\n      ? { right: leftTransformation }\n      : { left: leftTransformation }),\n  };\n\n  const pointerEvents = visible ? 'box-none' : 'none';\n\n  return (\n    <View ref={(ref) => (anchorRef.current = ref)} collapsable={false}>\n      {isCoordinate(anchor) ? null : anchor}\n      {rendered ? (\n        <Portal>\n          <Pressable\n            accessibilityLabel={overlayAccessibilityLabel}\n            accessibilityRole=\"button\"\n            onPress={onDismiss}\n            style={styles.pressableOverlay}\n          />\n          <View\n            ref={(ref) => (menuRef.current = ref)}\n            collapsable={false}\n            accessibilityViewIsModal={visible}\n            style={[styles.wrapper, positionStyle, style]}\n            pointerEvents={pointerEvents}\n            onAccessibilityEscape={onDismiss}\n            testID={`${testID}-view`}\n          >\n            <Animated.View\n              pointerEvents={pointerEvents}\n              style={{\n                transform: positionTransforms,\n              }}\n            >\n              <Surface\n                mode={mode}\n                pointerEvents={pointerEvents}\n                style={[\n                  styles.shadowMenuContainer,\n                  shadowMenuContainerStyle,\n                  theme.isV3 && {\n                    backgroundColor:\n                      theme.colors.elevation[ELEVATION_LEVELS_MAP[elevation]],\n                  },\n                  contentStyle,\n                ]}\n                {...(theme.isV3 && { elevation })}\n                testID={`${testID}-surface`}\n                theme={theme}\n              >\n                {(scrollableMenuHeight && (\n                  <ScrollView\n                    keyboardShouldPersistTaps={keyboardShouldPersistTaps}\n                  >\n                    {children}\n                  </ScrollView>\n                )) || <React.Fragment>{children}</React.Fragment>}\n              </Surface>\n            </Animated.View>\n          </View>\n        </Portal>\n      ) : null}\n    </View>\n  );\n};\n\nMenu.Item = MenuItem;\n\nconst styles = StyleSheet.create({\n  wrapper: {\n    position: 'absolute',\n  },\n  shadowMenuContainer: {\n    opacity: 0,\n    paddingVertical: 8,\n  },\n  pressableOverlay: {\n    ...Platform.select({\n      web: {\n        cursor: 'auto',\n      },\n    }),\n    ...StyleSheet.absoluteFillObject,\n    width: '100%',\n  },\n});\n\nexport default Menu;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,cAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAsB9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,OAAOC,QAAQ;AACf,SAASC,gBAAgB;AAEzB,SAASC,eAAe;AACxB,SAASC,gBAAgB;AACzB,SAASC,WAAW;AACpB,OAAOC,MAAM;AACb,OAAOC,OAAO;AAoEd,IAAMC,aAAa,GAAG,CAAC;AAEvB,IAAMC,kBAAkB,GAAG,GAAG;AAE9B,IAAMC,MAAM,GAAGnB,MAAM,CAACoB,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAE5C,IAAMC,aAAa,GAAGtB,UAAU,CAACuB,GAAG,CAAC,QAAQ,CAAC;AAE9C,IAAMC,iBAA+B,GAAG,CAAC;AACzC,OAAO,IAAMC,oBAAoB,GAAGC,MAAM,CAACC,MAAM,CAC/Cd,eACF,CAAsB;AAEtB,IAAMe,YAAY,GAAG,UAAU;AAE/B,IAAMC,iBAAiB,GAAI,SAArBA,iBAAiBA,CAAIC,EAA2B,EAAK;EACzD,IAAIA,EAAE,IAAIzB,QAAQ,CAAC0B,EAAE,KAAK,KAAK,EAAE;IAI/B,IAAMC,IAAS,GAAG9B,cAAc,CAAC4B,EAAE,CAAC;IACpC,IAAMG,aAAa,GAAGD,IAAI,CAACE,aAAa,CAEtC,0EACF,CAAC;IAEDD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,KAAK,CAAC,CAAC;EACxB;AACF,CAAC;AAED,IAAMC,YAAY,GAAI,SAAhBA,YAAYA,CAAIC,MAAW;EAAA,OAC/B,CAACvC,KAAK,CAACwC,cAAc,CAACD,MAAM,CAAC,IAC7B,QAAOA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,CAAC,MAAK,QAAQ,IAC7B,QAAOF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,CAAC,MAAK,QAAQ;AAAA;AAE/B,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OAASpC,QAAQ,CAAC0B,EAAE,KAAK,KAAK,IAAI,UAAU,IAAIW,MAAM;AAAA;AAiDrE,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAGC,IAAA,EAeA;EAAA,IAdXC,OAAO,GAcDD,IAAA,CAdNC,OAAO;IACPC,eAAe,GAaTF,IAAA,CAbNE,eAAe;IAAAC,qBAAA,GAaTH,IAAA,CAZNI,yBAAyB;IAAzBA,yBAAyB,GAAAD,qBAAA,cAAG,YAAY,GAAAA,qBAAA;IAAAE,WAAA,GAYlCL,IAAA,CAXNM,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,MAAM,GAAAA,WAAA;IACfZ,MAAM,GAUAO,IAAA,CAVNP,MAAM;IACNc,SAAS,GASHP,IAAA,CATNO,SAAS;IACTC,cAAc,GAQRR,IAAA,CARNQ,cAAc;IACdC,YAAY,GAONT,IAAA,CAPNS,YAAY;IACZC,KAAK,GAMCV,IAAA,CANNU,KAAK;IAAAC,cAAA,GAMCX,IAAA,CALNY,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG/B,iBAAiB,GAAA+B,cAAA;IAAAE,SAAA,GAKvBb,IAAA,CAJNc,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG7B,YAAY,GAAA6B,SAAA;IACnBE,QAAQ,GAGFf,IAAA,CAHNe,QAAQ;IACDC,cAAc,GAEfhB,IAAA,CAFNiB,KAAK;IACLC,yBAAA,GACMlB,IAAA,CADNkB,yBAAA;EAEA,IAAMD,KAAK,GAAGjD,gBAAgB,CAACgD,cAAc,CAAC;EAC9C,IAAMG,MAAM,GAAGrD,iBAAiB,CAAC,CAAC;EAClC,IAAAsD,eAAA,GAAgClE,KAAK,CAACmE,QAAQ,CAACpB,OAAO,CAAC;IAAAqB,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAhDI,QAAQ,GAAAF,gBAAA;IAAEG,WAAW,GAAAH,gBAAA;EAC5B,IAAAI,gBAAA,GAAwBxE,KAAK,CAACmE,QAAQ,CAAC,CAAC,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAAlCE,IAAI,GAAAD,gBAAA;IAAEE,OAAO,GAAAF,gBAAA;EACpB,IAAAG,gBAAA,GAAsB5E,KAAK,CAACmE,QAAQ,CAAC,CAAC,CAAC;IAAAU,gBAAA,GAAAR,cAAA,CAAAO,gBAAA;IAAhCE,GAAG,GAAAD,gBAAA;IAAEE,MAAM,GAAAF,gBAAA;EAClB,IAAAG,gBAAA,GAAoChF,KAAK,CAACmE,QAAQ,CAAC;MAAEc,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC;IAAAC,gBAAA,GAAAd,cAAA,CAAAW,gBAAA;IAApEI,UAAU,GAAAD,gBAAA;IAAEE,aAAa,GAAAF,gBAAA;EAChC,IAAAG,gBAAA,GAAwCtF,KAAK,CAACmE,QAAQ,CAAC;MACrDc,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC,CAAC;IAAAK,gBAAA,GAAAlB,cAAA,CAAAiB,gBAAA;IAHKE,YAAY,GAAAD,gBAAA;IAAEE,eAAe,GAAAF,gBAAA;EAIpC,IAAAG,gBAAA,GAAwC1F,KAAK,CAACmE,QAAQ,CAAC;MACrDc,KAAK,EAAEzD,aAAa,CAACyD,KAAK;MAC1BC,MAAM,EAAE1D,aAAa,CAAC0D;IACxB,CAAC,CAAC;IAAAS,iBAAA,GAAAtB,cAAA,CAAAqB,gBAAA;IAHKE,YAAY,GAAAD,iBAAA;IAAEE,eAAe,GAAAF,iBAAA;EAKpC,IAAMG,mBAAmB,GAAG9F,KAAK,CAAC+F,MAAM,CAAC,IAAI9F,QAAQ,CAAC+F,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/D,IAAMC,iBAAiB,GAAGjG,KAAK,CAAC+F,MAAM,CAAC,IAAI9F,QAAQ,CAACiG,OAAO,CAAC;IAAEzD,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC,CAAC;EAC5E,IAAMyD,iBAAiB,GAAGnG,KAAK,CAAC+F,MAAM,CAAC,CAAC,CAAC;EACzC,IAAMK,WAAW,GAAGpG,KAAK,CAAC+F,MAAM,CAAiB,IAAI,CAAC;EACtD,IAAMM,SAAS,GAAGrG,KAAK,CAAC+F,MAAM,CAAc,IAAI,CAAC;EACjD,IAAMO,OAAO,GAAGtG,KAAK,CAAC+F,MAAM,CAAc,IAAI,CAAC;EAC/C,IAAMQ,YAAY,GAAGvG,KAAK,CAAC+F,MAAM,CAAC,KAAK,CAAC;EAExC,IAAMS,eAAe,GAAGxG,KAAK,CAACyG,WAAW,CAAE,UAAAC,CAAkB,EAAK;IAChE,IAAMC,cAAc,GAAGD,CAAC,CAACE,cAAc,CAAC1B,MAAM;IAC9CiB,iBAAiB,CAACU,OAAO,GAAGF,cAAc;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMG,eAAe,GAAG9G,KAAK,CAACyG,WAAW,CAAC,YAAM;IAC9CN,iBAAiB,CAACU,OAAO,GAAG,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,IAAME,0BAEL,GAAG/G,KAAK,CAAC+F,MAAM,CAAC,CAAC;EAClB,IAAMiB,0BAEL,GAAGhH,KAAK,CAAC+F,MAAM,CAAC,CAAC;EAElB,IAAMkB,0BAEL,GAAGjH,KAAK,CAAC+F,MAAM,CAAC,CAAC;EAClB,IAAMmB,yBAEL,GAAGlH,KAAK,CAAC+F,MAAM,CAAC,CAAC;EAElB,IAAMoB,aAAa,GAAGnH,KAAK,CAACyG,WAAW,CAAC,YAAM;IAC5C,IAAI1D,OAAO,EAAE;MACXM,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACA,SAAS,EAAEN,OAAO,CAAC,CAAC;EAExB,IAAMqE,cAAc,GAAGpH,KAAK,CAACyG,WAAW,CACrC,UAAAC,CAAgB,EAAK;IACpB,IAAIA,CAAC,CAACW,GAAG,KAAK,QAAQ,EAAE;MACtBhE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,CAAC;IACf;EACF,CAAC,EACD,CAACA,SAAS,CACZ,CAAC;EAED,IAAMiE,eAAe,GAAGtH,KAAK,CAACyG,WAAW,CAAC,YAAM;IAAA,IAAAc,qBAAA,EAAAC,qBAAA;IAC9C,CAAAD,qBAAA,GAAAN,0BAA0B,CAACJ,OAAO,cAAAU,qBAAA,uBAAlCA,qBAAA,CAAoCE,MAAM,CAAC,CAAC;IAC5C,CAAAD,qBAAA,GAAAN,yBAAyB,CAACL,OAAO,cAAAW,qBAAA,uBAAjCA,qBAAA,CAAmCC,MAAM,CAAC,CAAC;IAC3C9E,SAAS,CAAC,CAAC,IAAI+E,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEP,cAAc,CAAC;EACtE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,IAAMQ,eAAe,GAAG5H,KAAK,CAACyG,WAAW,CAAC,YAAM;IAC9CQ,0BAA0B,CAACJ,OAAO,GAAG7F,gBAAgB,CACnDC,WAAW,EACX,mBAAmB,EACnBkG,aACF,CAAC;IACDD,yBAAyB,CAACL,OAAO,GAAG7F,gBAAgB,CAClDd,UAAU,EACV,QAAQ,EACRiH,aACF,CAAC;IACD5G,QAAQ,CAAC0B,EAAE,KAAK,KAAK,IAAIyF,QAAQ,CAAC1G,gBAAgB,CAAC,OAAO,EAAEoG,cAAc,CAAC;EAC7E,CAAC,EAAE,CAACD,aAAa,EAAEC,cAAc,CAAC,CAAC;EAEnC,IAAMS,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;IAAA,OACrB,IAAIC,OAAO,CAAmB,UAAAC,OAAO,EAAK;MACxC,IAAIzB,OAAO,CAACO,OAAO,EAAE;QACnBP,OAAO,CAACO,OAAO,CAACmB,eAAe,CAAC,UAACvF,CAAC,EAAEC,CAAC,EAAEuC,KAAK,EAAEC,MAAM,EAAK;UACvD6C,OAAO,CAAC;YAAEtF,CAAC,EAADA,CAAC;YAAEC,CAAC,EAADA,CAAC;YAAEuC,KAAK,EAALA,KAAK;YAAEC,MAAA,EAAAA;UAAO,CAAC,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EAAA;EAEJ,IAAM+C,mBAAmB,GAAGjI,KAAK,CAACyG,WAAW,CAC3C;IAAA,OACE,IAAIqB,OAAO,CAAmB,UAAAC,OAAO,EAAK;MACxC,IAAIzF,YAAY,CAACC,MAAM,CAAC,EAAE;QACxBwF,OAAO,CAAC;UAAEtF,CAAC,EAAEF,MAAM,CAACE,CAAC;UAAEC,CAAC,EAAEH,MAAM,CAACG,CAAC;UAAEuC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC,CAAC;QAC1D;MACF;MAEA,IAAImB,SAAS,CAACQ,OAAO,EAAE;QACrBR,SAAS,CAACQ,OAAO,CAACmB,eAAe,CAAC,UAACvF,CAAC,EAAEC,CAAC,EAAEuC,KAAK,EAAEC,MAAM,EAAK;UACzD6C,OAAO,CAAC;YAAEtF,CAAC,EAADA,CAAC;YAAEC,CAAC,EAADA,CAAC;YAAEuC,KAAK,EAALA,KAAK;YAAEC,MAAA,EAAAA;UAAO,CAAC,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EAAA,GACJ,CAAC3C,MAAM,CACT,CAAC;EAED,IAAM2F,IAAI,GAAGlI,KAAK,CAACyG,WAAW,CAAA0B,iBAAA,CAAC,aAAY;IACzC,IAAMC,kBAAkB,GAAGlI,UAAU,CAACuB,GAAG,CAAC,QAAQ,CAAC;IACnD,IAAA4G,kBAAA,SAAqDP,OAAO,CAACQ,GAAG,CAAC,CAC/DT,iBAAiB,CAAC,CAAC,EACnBI,mBAAmB,CAAC,CAAC,CACtB,CAAC;MAAAM,mBAAA,GAAAlE,cAAA,CAAAgE,kBAAA;MAHKG,gBAAgB,GAAAD,mBAAA;MAAEE,kBAAkB,GAAAF,mBAAA;IAW3C,IACE,CAACH,kBAAkB,CAACnD,KAAK,IACzB,CAACmD,kBAAkB,CAAClD,MAAM,IAC1B,CAACsD,gBAAgB,CAACvD,KAAK,IACvB,CAACuD,gBAAgB,CAACtD,MAAM,IACvB,CAACuD,kBAAkB,CAACxD,KAAK,IAAI,CAAC3C,YAAY,CAACC,MAAM,CAAE,IACnD,CAACkG,kBAAkB,CAACvD,MAAM,IAAI,CAAC5C,YAAY,CAACC,MAAM,CAAE,EACrD;MACAmG,qBAAqB,CAACR,IAAI,CAAC;MAC3B;IACF;IAEAvD,OAAO,CAAC8D,kBAAkB,CAAChG,CAAC,CAAC;IAC7BsC,MAAM,CAAC0D,kBAAkB,CAAC/F,CAAC,CAAC;IAC5B+C,eAAe,CAAC;MACdP,MAAM,EAAEuD,kBAAkB,CAACvD,MAAM;MACjCD,KAAK,EAAEwD,kBAAkB,CAACxD;IAC5B,CAAC,CAAC;IAEFI,aAAa,CAAC;MACZH,MAAM,EAAEsD,gBAAgB,CAACtD,MAAM;MAC/BD,KAAK,EAAEuD,gBAAgB,CAACvD;IAC1B,CAAC,CAAC;IAEFY,eAAe,CAAC;MACdX,MAAM,EAAEkD,kBAAkB,CAAClD,MAAM,GAAGiB,iBAAiB,CAACU,OAAO;MAC7D5B,KAAK,EAAEmD,kBAAkB,CAACnD;IAC5B,CAAC,CAAC;IAEF2C,eAAe,CAAC,CAAC;IACjB,IAAQe,SAAA,GAAc5E,KAAK,CAAnB4E,SAAA;IACR1I,QAAQ,CAAC2I,QAAQ,CAAC,CAChB3I,QAAQ,CAAC4I,MAAM,CAAC5C,iBAAiB,CAACY,OAAO,EAAE;MACzCiC,OAAO,EAAE;QAAErG,CAAC,EAAE+F,gBAAgB,CAACvD,KAAK;QAAEvC,CAAC,EAAE8F,gBAAgB,CAACtD;MAAO,CAAC;MAClE6D,QAAQ,EAAE1H,kBAAkB,GAAGsH,SAAS,CAACK,KAAK;MAC9CC,MAAM,EAAE3H,MAAM;MACd4H,eAAe,EAAE;IACnB,CAAC,CAAC,EACFjJ,QAAQ,CAAC4I,MAAM,CAAC/C,mBAAmB,CAACe,OAAO,EAAE;MAC3CiC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE1H,kBAAkB,GAAGsH,SAAS,CAACK,KAAK;MAC9CC,MAAM,EAAE3H,MAAM;MACd4H,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAA,EAAkB;MAAA,IAAfC,QAAA,GAAUD,KAAA,CAAVC,QAAA;MACV,IAAIA,QAAQ,EAAE;QACZtH,iBAAiB,CAACuE,OAAO,CAACO,OAAO,CAAC;QAClCN,YAAY,CAACM,OAAO,GAAG,IAAI;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC,GAAE,CAACtE,MAAM,EAAEqF,eAAe,EAAEK,mBAAmB,EAAElE,KAAK,CAAC,CAAC;EAEzD,IAAMuF,IAAI,GAAGtJ,KAAK,CAACyG,WAAW,CAAC,YAAM;IACnCa,eAAe,CAAC,CAAC;IAEjB,IAAQqB,SAAA,GAAc5E,KAAK,CAAnB4E,SAAA;IAER1I,QAAQ,CAAC4I,MAAM,CAAC/C,mBAAmB,CAACe,OAAO,EAAE;MAC3CiC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE1H,kBAAkB,GAAGsH,SAAS,CAACK,KAAK;MAC9CC,MAAM,EAAE3H,MAAM;MACd4H,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAI,KAAA,EAAkB;MAAA,IAAfF,QAAA,GAAUE,KAAA,CAAVF,QAAA;MACV,IAAIA,QAAQ,EAAE;QACZhE,aAAa,CAAC;UAAEJ,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC,CAAC;QACtCX,WAAW,CAAC,KAAK,CAAC;QAClBgC,YAAY,CAACM,OAAO,GAAG,KAAK;QAC5B9E,iBAAiB,CAACsE,SAAS,CAACQ,OAAO,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACS,eAAe,EAAEvD,KAAK,CAAC,CAAC;EAE5B,IAAMyF,gBAAgB,GAAGxJ,KAAK,CAACyG,WAAW;IAAA,IAAAgD,KAAA,GAAAtB,iBAAA,CACxC,WAAOuB,OAAgB,EAAK;MAG1B,MAAM5B,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC4B,IAAI,CAAC,YAAM;QACjC,IAAID,OAAO,IAAI,CAACnD,YAAY,CAACM,OAAO,EAAE;UACpCqB,IAAI,CAAC,CAAC;UACN;QACF;QAEA,IAAI,CAACwB,OAAO,IAAInD,YAAY,CAACM,OAAO,EAAE;UACpCyC,IAAI,CAAC,CAAC;QACR;QAEA;MACF,CAAC,CAAC;IACJ,CAAC;IAAA,iBAAAM,EAAA;MAAA,OAAAH,KAAA,CAAAI,KAAA,OAAAC,SAAA;IAAA;EAAA,KACD,CAACR,IAAI,EAAEpB,IAAI,CACb,CAAC;EAEDlI,KAAK,CAAC+J,SAAS,CAAC,YAAM;IACpB,IAAMC,gBAAgB,GAAGlE,mBAAmB,CAACe,OAAO;IACpD,IAAMoD,cAAc,GAAGhE,iBAAiB,CAACY,OAAO;IAChDE,0BAA0B,CAACF,OAAO,GAAGvG,QAAQ,CAAC4J,WAAW,CACvD,iBAAiB,EACjB1D,eACF,CAAC;IACDQ,0BAA0B,CAACH,OAAO,GAAGvG,QAAQ,CAAC4J,WAAW,CACvD,iBAAiB,EACjBpD,eACF,CAAC;IAED,OAAO,YAAM;MAAA,IAAAqD,qBAAA,EAAAC,qBAAA;MACX9C,eAAe,CAAC,CAAC;MACjB,CAAA6C,qBAAA,GAAApD,0BAA0B,CAACF,OAAO,cAAAsD,qBAAA,uBAAlCA,qBAAA,CAAoC1C,MAAM,CAAC,CAAC;MAC5C,CAAA2C,qBAAA,GAAApD,0BAA0B,CAACH,OAAO,cAAAuD,qBAAA,uBAAlCA,qBAAA,CAAoC3C,MAAM,CAAC,CAAC;MAC5CwC,cAAc,CAACI,kBAAkB,CAAC,CAAC;MACnCL,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,kBAAkB,CAAC,CAAC;IACxC,CAAC;EACH,CAAC,EAAE,CAAC/C,eAAe,EAAER,eAAe,EAAEN,eAAe,CAAC,CAAC;EAEvDxG,KAAK,CAAC+J,SAAS,CAAC,YAAM;IACpB,IAAI3D,WAAW,CAACS,OAAO,KAAK9D,OAAO,EAAE;MACnCqD,WAAW,CAACS,OAAO,GAAG9D,OAAO;MAE7B,IAAIA,OAAO,KAAKuB,QAAQ,EAAE;QACxBC,WAAW,CAACxB,OAAO,CAAC;MACtB;IACF;EACF,CAAC,EAAE,CAACA,OAAO,EAAEuB,QAAQ,CAAC,CAAC;EAEvBtE,KAAK,CAAC+J,SAAS,CAAC,YAAM;IACpBP,gBAAgB,CAAClF,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACA,QAAQ,EAAEkF,gBAAgB,CAAC,CAAC;EAGhC,IAAMc,uBAAuB,GAAG/J,QAAQ,CAACgK,MAAM,CAAC;IAC9CC,OAAO,EAAExH,eAAe,WAAfA,eAAe,GAAIiB,MAAM,CAACa,GAAG;IACtC2F,OAAO,EAAE;EACX,CAAC,CAAC;EAGF,IAAMC,kBAAkB,GAAG,EAAE;EAC7B,IAAIC,kBAAkB,GAAGjG,IAAI;EAC7B,IAAIkG,iBAAiB,GACnB,CAACtI,YAAY,CAAC+D,SAAS,CAACQ,OAAO,CAAC,IAAIvD,cAAc,KAAK,QAAQ,GAC3DwB,GAAG,GAAGU,YAAY,CAACN,MAAM,GACzBJ,GAAG;EAGT,IAAIJ,IAAI,IAAIkB,YAAY,CAACX,KAAK,GAAGG,UAAU,CAACH,KAAK,GAAG7D,aAAa,EAAE;IACjEsJ,kBAAkB,CAACG,IAAI,CAAC;MACtBC,UAAU,EAAE7E,iBAAiB,CAACY,OAAO,CAACpE,CAAC,CAACsI,WAAW,CAAC;QAClDC,UAAU,EAAE,CAAC,CAAC,EAAE5F,UAAU,CAACH,KAAK,CAAC;QACjCgG,WAAW,EAAE,CAAC,EAAE7F,UAAU,CAACH,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;MAC1C,CAAC;IACH,CAAC,CAAC;IAGF,IAAI0F,kBAAkB,GAAGvJ,aAAa,EAAE;MACtCuJ,kBAAkB,GAAGvJ,aAAa;IACpC;EACF,CAAC,MAAM;IACLsJ,kBAAkB,CAACG,IAAI,CAAC;MACtBC,UAAU,EAAE7E,iBAAiB,CAACY,OAAO,CAACpE,CAAC,CAACsI,WAAW,CAAC;QAClDC,UAAU,EAAE,CAAC,CAAC,EAAE5F,UAAU,CAACH,KAAK,CAAC;QACjCgG,WAAW,EAAE,CAAC7F,UAAU,CAACH,KAAK,GAAG,CAAC,EAAE,CAAC;MACvC,CAAC;IACH,CAAC,CAAC;IAEF0F,kBAAkB,IAAInF,YAAY,CAACP,KAAK,GAAGG,UAAU,CAACH,KAAK;IAE3D,IAAMiG,KAAK,GAAGP,kBAAkB,GAAGvF,UAAU,CAACH,KAAK;IAEnD,IAAIiG,KAAK,GAAGtF,YAAY,CAACX,KAAK,GAAG7D,aAAa,EAAE;MAC9CuJ,kBAAkB,GAChB/E,YAAY,CAACX,KAAK,GAAG7D,aAAa,GAAGgE,UAAU,CAACH,KAAK;IACzD;EACF;EAIA,IAAIkG,oBAAoB,GAAG,CAAC;EAG5B,IAEEP,iBAAiB,IACfhF,YAAY,CAACV,MAAM,GACjBE,UAAU,CAACF,MAAM,GACjB9D,aAAa,GACbkJ,uBAAuB,IAE3BM,iBAAiB,IAAIhF,YAAY,CAACV,MAAM,GAAG0F,iBAAiB,EAC5D;IAEAO,oBAAoB,GAClBvF,YAAY,CAACV,MAAM,GACnB0F,iBAAiB,GACjBxJ,aAAa,GACbkJ,uBAAuB;EAC3B,CAAC,MAAM,IAELM,iBAAiB,IACfhF,YAAY,CAACV,MAAM,GACjBE,UAAU,CAACF,MAAM,GACjB9D,aAAa,GACbkJ,uBAAuB,IAE3BM,iBAAiB,IAAIhF,YAAY,CAACV,MAAM,GAAGJ,GAAG,IAE9C8F,iBAAiB,IACfxF,UAAU,CAACF,MAAM,GACfM,YAAY,CAACN,MAAM,GACnB9D,aAAa,GACbkJ,uBAAuB,EAC3B;IAEAa,oBAAoB,GAClBP,iBAAiB,GACjBpF,YAAY,CAACN,MAAM,GACnB9D,aAAa,GACbkJ,uBAAuB;EAC3B;EAGAa,oBAAoB,GAClBA,oBAAoB,GAAGvF,YAAY,CAACV,MAAM,GAAG,CAAC,GAAG9D,aAAa,GAC1DwE,YAAY,CAACV,MAAM,GAAG,CAAC,GAAG9D,aAAa,GACvC+J,oBAAoB;EAI1B,IAEEP,iBAAiB,IACfhF,YAAY,CAACV,MAAM,GACjBE,UAAU,CAACF,MAAM,GACjB9D,aAAa,GACbkJ,uBAAuB,IAE1BM,iBAAiB,IAChBhF,YAAY,CAACV,MAAM,GACjBE,UAAU,CAACF,MAAM,GACjB9D,aAAa,GACbkJ,uBAAuB,IAEzBM,iBAAiB,IAAIhF,YAAY,CAACV,MAAM,GAAG0F,iBAAkB,EAC/D;IACAF,kBAAkB,CAACG,IAAI,CAAC;MACtBO,UAAU,EAAEnF,iBAAiB,CAACY,OAAO,CAACnE,CAAC,CAACqI,WAAW,CAAC;QAClDC,UAAU,EAAE,CAAC,CAAC,EAAE5F,UAAU,CAACF,MAAM,CAAC;QAClC+F,WAAW,EAAE,CAAC,EAAE,CAACE,oBAAoB,IAAI/F,UAAU,CAACF,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC;MACrE,CAAC;IACH,CAAC,CAAC;IAGF,IAAI0F,iBAAiB,GAAGxJ,aAAa,EAAE;MACrCwJ,iBAAiB,GAAGxJ,aAAa;IACnC;EACF,CAAC,MAAM;IACLsJ,kBAAkB,CAACG,IAAI,CAAC;MACtBO,UAAU,EAAEnF,iBAAiB,CAACY,OAAO,CAACnE,CAAC,CAACqI,WAAW,CAAC;QAClDC,UAAU,EAAE,CAAC,CAAC,EAAE5F,UAAU,CAACF,MAAM,CAAC;QAClC+F,WAAW,EAAE,CAAC,CAACE,oBAAoB,IAAI/F,UAAU,CAACF,MAAM,IAAI,CAAC,EAAE,CAAC;MAClE,CAAC;IACH,CAAC,CAAC;IAEF0F,iBAAiB,IACfpF,YAAY,CAACN,MAAM,IAAIiG,oBAAoB,IAAI/F,UAAU,CAACF,MAAM,CAAC;IAEnE,IAAMmG,MAAM,GACVT,iBAAiB,IAChBO,oBAAoB,IAAI/F,UAAU,CAACF,MAAM,CAAC,GAC3CoF,uBAAuB;IAGzB,IAAIe,MAAM,GAAGzF,YAAY,CAACV,MAAM,GAAG9D,aAAa,EAAE;MAChDwJ,iBAAiB,GACfO,oBAAoB,KAAKvF,YAAY,CAACV,MAAM,GAAG,CAAC,GAAG9D,aAAa,GAC5D,CAACA,aAAa,GAAG,CAAC,GAClBwE,YAAY,CAACV,MAAM,GACnBE,UAAU,CAACF,MAAM,GACjB9D,aAAa,GACbkJ,uBAAuB;IAC/B;EACF;EAEA,IAAMgB,wBAAwB,GAAAC,aAAA,CAAAA,aAAA;IAC5BC,OAAO,EAAE1F,mBAAmB,CAACe,OAAO;IACpC4E,SAAS,EAAE,CACT;MACEC,MAAM,EAAEzF,iBAAiB,CAACY,OAAO,CAACpE,CAAC,CAACsI,WAAW,CAAC;QAC9CC,UAAU,EAAE,CAAC,CAAC,EAAE5F,UAAU,CAACH,KAAK,CAAC;QACjCgG,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;MACpB,CAAC;IACH,CAAC,EACD;MACEU,MAAM,EAAE1F,iBAAiB,CAACY,OAAO,CAACnE,CAAC,CAACqI,WAAW,CAAC;QAC9CC,UAAU,EAAE,CAAC,CAAC,EAAE5F,UAAU,CAACF,MAAM,CAAC;QAClC+F,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;MACpB,CAAC;IACH,CAAC,CACF;IACDW,YAAY,EAAE7H,KAAK,CAAC8H;EAAS,GACzB,CAAC9H,KAAK,CAAC+H,IAAI,IAAI;IAAEpI,SAAS,EAAE;EAAE,CAAC,GAC/ByH,oBAAoB,GAAG;IAAEjG,MAAM,EAAEiG;EAAqB,CAAC,GAAG,CAAC,CAAC,CACjE;EAED,IAAMY,aAAa,GAAAR,aAAA;IACjBzG,GAAG,EAAExC,YAAY,CAACC,MAAM,CAAC,GACrBqI,iBAAiB,GACjBA,iBAAiB,GAAGN;EAAuB,GAC3CjK,WAAW,CAAC2L,YAAY,CAAC,CAAC,CAACC,KAAK,GAChC;IAAEf,KAAK,EAAEP;EAAmB,CAAC,GAC7B;IAAEjG,IAAI,EAAEiG;EAAmB,CAAC,CACjC;EAED,IAAMuB,aAAa,GAAGnJ,OAAO,GAAG,UAAU,GAAG,MAAM;EAEnD,OACE/C,KAAA,CAAAmM,aAAA,CAACzL,IAAI;IAAC0L,GAAG,EAAG,SAANA,GAAGA,CAAGA,KAAG;MAAA,OAAM/F,SAAS,CAACQ,OAAO,GAAGuF,KAAK;IAAA;IAACC,WAAW,EAAE;EAAM,GAC/D/J,YAAY,CAACC,MAAM,CAAC,GAAG,IAAI,GAAGA,MAAM,EACpC+B,QAAQ,GACPtE,KAAA,CAAAmM,aAAA,CAACjL,MAAM,QACLlB,KAAA,CAAAmM,aAAA,CAACxL,SAAS;IACR2L,kBAAkB,EAAEpJ,yBAA0B;IAC9CqJ,iBAAiB,EAAC,QAAQ;IAC1BC,OAAO,EAAEnJ,SAAU;IACnBG,KAAK,EAAEiJ,MAAM,CAACC;EAAiB,CAChC,CAAC,EACF1M,KAAA,CAAAmM,aAAA,CAACzL,IAAI;IACH0L,GAAG,EAAG,SAANA,GAAGA,CAAGA,KAAG;MAAA,OAAM9F,OAAO,CAACO,OAAO,GAAGuF,KAAK;IAAA;IACtCC,WAAW,EAAE,KAAM;IACnBM,wBAAwB,EAAE5J,OAAQ;IAClCS,KAAK,EAAE,CAACiJ,MAAM,CAACG,OAAO,EAAEb,aAAa,EAAEvI,KAAK,CAAE;IAC9C0I,aAAa,EAAEA,aAAc;IAC7BW,qBAAqB,EAAExJ,SAAU;IACjCD,MAAM,EAAG,GAAEA,MAAO;EAAO,GAEzBpD,KAAA,CAAAmM,aAAA,CAAClM,QAAQ,CAACS,IAAI;IACZwL,aAAa,EAAEA,aAAc;IAC7B1I,KAAK,EAAE;MACLiI,SAAS,EAAEf;IACb;EAAE,GAEF1K,KAAA,CAAAmM,aAAA,CAAChL,OAAO,EAAA2L,QAAA;IACNlJ,IAAI,EAAEA,IAAK;IACXsI,aAAa,EAAEA,aAAc;IAC7B1I,KAAK,EAAE,CACLiJ,MAAM,CAACM,mBAAmB,EAC1BzB,wBAAwB,EACxBvH,KAAK,CAAC+H,IAAI,IAAI;MACZkB,eAAe,EACbjJ,KAAK,CAACkJ,MAAM,CAACvJ,SAAS,CAAC/B,oBAAoB,CAAC+B,SAAS,CAAC;IAC1D,CAAC,EACDH,YAAY;EACZ,GACGQ,KAAK,CAAC+H,IAAI,IAAI;IAAEpI,SAAA,EAAAA;EAAU,CAAC;IAChCN,MAAM,EAAG,GAAEA,MAAO,UAAU;IAC5BW,KAAK,EAAEA;EAAM,IAEXoH,oBAAoB,IACpBnL,KAAA,CAAAmM,aAAA,CAAC3L,UAAU;IACTwD,yBAAyB,EAAEA;EAA0B,GAEpDH,QACS,CACb,IAAK7D,KAAA,CAAAmM,aAAA,CAACnM,KAAK,CAACkN,QAAQ,QAAErJ,QAAyB,CACzC,CACI,CACX,CACA,CAAC,GACP,IACA,CAAC;AAEX,CAAC;AAEDhB,IAAI,CAACsK,IAAI,GAAGtM,QAAQ;AAEpB,IAAM4L,MAAM,GAAGhM,UAAU,CAAC2M,MAAM,CAAC;EAC/BR,OAAO,EAAE;IACPS,QAAQ,EAAE;EACZ,CAAC;EACDN,mBAAmB,EAAE;IACnBvB,OAAO,EAAE,CAAC;IACV8B,eAAe,EAAE;EACnB,CAAC;EACDZ,gBAAgB,EAAAnB,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACXhL,QAAQ,CAACgK,MAAM,CAAC;IACjBgD,GAAG,EAAE;MACHC,MAAM,EAAE;IACV;EACF,CAAC,CAAC,GACC/M,UAAU,CAACgN,kBAAkB;IAChCxI,KAAK,EAAE;EAAA;AAEX,CAAC,CAAC;AAEF,eAAepC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}