{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport NativeAnimatedNonTurboModule from \"./NativeAnimatedModule\";\nimport NativeAnimatedTurboModule from \"./NativeAnimatedTurboModule\";\nimport NativeEventEmitter from \"../EventEmitter/NativeEventEmitter\";\nimport Platform from \"../Utilities/Platform\";\nimport ReactNativeFeatureFlags from \"../ReactNative/ReactNativeFeatureFlags\";\nimport invariant from 'fbjs/lib/invariant';\nimport RCTDeviceEventEmitter from \"../EventEmitter/RCTDeviceEventEmitter\";\nvar NativeAnimatedModule = Platform.OS === 'ios' && global.RN$Bridgeless === true ? NativeAnimatedTurboModule : NativeAnimatedNonTurboModule;\nvar __nativeAnimatedNodeTagCount = 1;\nvar __nativeAnimationIdCount = 1;\nvar nativeEventEmitter;\nvar waitingForQueuedOperations = new Set();\nvar queueOperations = false;\nvar queue = [];\nvar singleOpQueue = [];\nvar useSingleOpBatching = false;\nPlatform.OS === 'android' && !!(NativeAnimatedModule != null && NativeAnimatedModule.queueAndExecuteBatchedOperations) && ReactNativeFeatureFlags.animatedShouldUseSingleOp();\nvar flushQueueTimeout = null;\nvar eventListenerGetValueCallbacks = {};\nvar eventListenerAnimationFinishedCallbacks = {};\nvar globalEventEmitterGetValueListener = null;\nvar globalEventEmitterAnimationFinishedListener = null;\nvar nativeOps = useSingleOpBatching ? function () {\n  var apis = ['createAnimatedNode', 'updateAnimatedNodeConfig', 'getValue', 'startListeningToAnimatedNodeValue', 'stopListeningToAnimatedNodeValue', 'connectAnimatedNodes', 'disconnectAnimatedNodes', 'startAnimatingNode', 'stopAnimation', 'setAnimatedNodeValue', 'setAnimatedNodeOffset', 'flattenAnimatedNodeOffset', 'extractAnimatedNodeOffset', 'connectAnimatedNodeToView', 'disconnectAnimatedNodeFromView', 'restoreDefaultValues', 'dropAnimatedNode', 'addAnimatedEventToView', 'removeAnimatedEventFromView', 'addListener', 'removeListener'];\n  return apis.reduce(function (acc, functionName, i) {\n    acc[functionName] = i + 1;\n    return acc;\n  }, {});\n}() : NativeAnimatedModule;\nvar API = {\n  getValue: function getValue(tag, saveValueCallback) {\n    invariant(nativeOps, 'Native animated module is not available');\n    if (useSingleOpBatching) {\n      if (saveValueCallback) {\n        eventListenerGetValueCallbacks[tag] = saveValueCallback;\n      }\n      API.queueOperation(nativeOps.getValue, tag);\n    } else {\n      API.queueOperation(nativeOps.getValue, tag, saveValueCallback);\n    }\n  },\n  setWaitingForIdentifier: function setWaitingForIdentifier(id) {\n    waitingForQueuedOperations.add(id);\n    queueOperations = true;\n    if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush() && flushQueueTimeout) {\n      clearTimeout(flushQueueTimeout);\n    }\n  },\n  unsetWaitingForIdentifier: function unsetWaitingForIdentifier(id) {\n    waitingForQueuedOperations.delete(id);\n    if (waitingForQueuedOperations.size === 0) {\n      queueOperations = false;\n      API.disableQueue();\n    }\n  },\n  disableQueue: function disableQueue() {\n    invariant(nativeOps, 'Native animated module is not available');\n    if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush()) {\n      var prevTimeout = flushQueueTimeout;\n      clearImmediate(prevTimeout);\n      flushQueueTimeout = setImmediate(API.flushQueue);\n    } else {\n      API.flushQueue();\n    }\n  },\n  flushQueue: function flushQueue() {},\n  queueOperation: function queueOperation(fn) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (useSingleOpBatching) {\n      singleOpQueue.push.apply(singleOpQueue, [fn].concat(args));\n      return;\n    }\n    if (queueOperations || queue.length !== 0) {\n      queue.push(function () {\n        return fn.apply(void 0, args);\n      });\n    } else {\n      fn.apply(void 0, args);\n    }\n  },\n  createAnimatedNode: function createAnimatedNode(tag, config) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.createAnimatedNode, tag, config);\n  },\n  updateAnimatedNodeConfig: function updateAnimatedNodeConfig(tag, config) {\n    invariant(nativeOps, 'Native animated module is not available');\n  },\n  startListeningToAnimatedNodeValue: function startListeningToAnimatedNodeValue(tag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.startListeningToAnimatedNodeValue, tag);\n  },\n  stopListeningToAnimatedNodeValue: function stopListeningToAnimatedNodeValue(tag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.stopListeningToAnimatedNodeValue, tag);\n  },\n  connectAnimatedNodes: function connectAnimatedNodes(parentTag, childTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.connectAnimatedNodes, parentTag, childTag);\n  },\n  disconnectAnimatedNodes: function disconnectAnimatedNodes(parentTag, childTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.disconnectAnimatedNodes, parentTag, childTag);\n  },\n  startAnimatingNode: function startAnimatingNode(animationId, nodeTag, config, endCallback) {\n    invariant(nativeOps, 'Native animated module is not available');\n    if (useSingleOpBatching) {\n      if (endCallback) {\n        eventListenerAnimationFinishedCallbacks[animationId] = endCallback;\n      }\n      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config);\n    } else {\n      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config, endCallback);\n    }\n  },\n  stopAnimation: function stopAnimation(animationId) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.stopAnimation, animationId);\n  },\n  setAnimatedNodeValue: function setAnimatedNodeValue(nodeTag, value) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.setAnimatedNodeValue, nodeTag, value);\n  },\n  setAnimatedNodeOffset: function setAnimatedNodeOffset(nodeTag, offset) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.setAnimatedNodeOffset, nodeTag, offset);\n  },\n  flattenAnimatedNodeOffset: function flattenAnimatedNodeOffset(nodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.flattenAnimatedNodeOffset, nodeTag);\n  },\n  extractAnimatedNodeOffset: function extractAnimatedNodeOffset(nodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.extractAnimatedNodeOffset, nodeTag);\n  },\n  connectAnimatedNodeToView: function connectAnimatedNodeToView(nodeTag, viewTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.connectAnimatedNodeToView, nodeTag, viewTag);\n  },\n  disconnectAnimatedNodeFromView: function disconnectAnimatedNodeFromView(nodeTag, viewTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.disconnectAnimatedNodeFromView, nodeTag, viewTag);\n  },\n  restoreDefaultValues: function restoreDefaultValues(nodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    if (nativeOps.restoreDefaultValues != null) {\n      API.queueOperation(nativeOps.restoreDefaultValues, nodeTag);\n    }\n  },\n  dropAnimatedNode: function dropAnimatedNode(tag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.dropAnimatedNode, tag);\n  },\n  addAnimatedEventToView: function addAnimatedEventToView(viewTag, eventName, eventMapping) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.addAnimatedEventToView, viewTag, eventName, eventMapping);\n  },\n  removeAnimatedEventFromView: function removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.removeAnimatedEventFromView, viewTag, eventName, animatedNodeTag);\n  }\n};\nfunction setupGlobalEventEmitterListeners() {\n  globalEventEmitterGetValueListener = RCTDeviceEventEmitter.addListener('onNativeAnimatedModuleGetValue', function (params) {\n    var tag = params.tag;\n    var callback = eventListenerGetValueCallbacks[tag];\n    if (!callback) {\n      return;\n    }\n    callback(params.value);\n    delete eventListenerGetValueCallbacks[tag];\n  });\n  globalEventEmitterAnimationFinishedListener = RCTDeviceEventEmitter.addListener('onNativeAnimatedModuleAnimationFinished', function (params) {\n    var animationId = params.animationId;\n    var callback = eventListenerAnimationFinishedCallbacks[animationId];\n    if (!callback) {\n      return;\n    }\n    callback(params);\n    delete eventListenerAnimationFinishedCallbacks[animationId];\n  });\n}\nvar SUPPORTED_COLOR_STYLES = {\n  backgroundColor: true,\n  borderBottomColor: true,\n  borderColor: true,\n  borderEndColor: true,\n  borderLeftColor: true,\n  borderRightColor: true,\n  borderStartColor: true,\n  borderTopColor: true,\n  color: true,\n  tintColor: true\n};\nvar SUPPORTED_STYLES = _objectSpread(_objectSpread({}, SUPPORTED_COLOR_STYLES), {}, {\n  borderBottomEndRadius: true,\n  borderBottomLeftRadius: true,\n  borderBottomRightRadius: true,\n  borderBottomStartRadius: true,\n  borderRadius: true,\n  borderTopEndRadius: true,\n  borderTopLeftRadius: true,\n  borderTopRightRadius: true,\n  borderTopStartRadius: true,\n  elevation: true,\n  opacity: true,\n  transform: true,\n  zIndex: true,\n  shadowOpacity: true,\n  shadowRadius: true,\n  scaleX: true,\n  scaleY: true,\n  translateX: true,\n  translateY: true\n});\nvar SUPPORTED_TRANSFORMS = {\n  translateX: true,\n  translateY: true,\n  scale: true,\n  scaleX: true,\n  scaleY: true,\n  rotate: true,\n  rotateX: true,\n  rotateY: true,\n  rotateZ: true,\n  perspective: true\n};\nvar SUPPORTED_INTERPOLATION_PARAMS = {\n  inputRange: true,\n  outputRange: true,\n  extrapolate: true,\n  extrapolateRight: true,\n  extrapolateLeft: true\n};\nfunction addWhitelistedStyleProp(prop) {\n  SUPPORTED_STYLES[prop] = true;\n}\nfunction addWhitelistedTransformProp(prop) {\n  SUPPORTED_TRANSFORMS[prop] = true;\n}\nfunction addWhitelistedInterpolationParam(param) {\n  SUPPORTED_INTERPOLATION_PARAMS[param] = true;\n}\nfunction isSupportedColorStyleProp(prop) {\n  return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);\n}\nfunction isSupportedStyleProp(prop) {\n  return SUPPORTED_STYLES.hasOwnProperty(prop);\n}\nfunction isSupportedTransformProp(prop) {\n  return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);\n}\nfunction isSupportedInterpolationParam(param) {\n  return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);\n}\nfunction validateTransform(configs) {\n  configs.forEach(function (config) {\n    if (!isSupportedTransformProp(config.property)) {\n      throw new Error(\"Property '\" + config.property + \"' is not supported by native animated module\");\n    }\n  });\n}\nfunction validateStyles(styles) {\n  for (var _key2 in styles) {\n    if (!isSupportedStyleProp(_key2)) {\n      throw new Error(\"Style property '\" + _key2 + \"' is not supported by native animated module\");\n    }\n  }\n}\nfunction validateInterpolation(config) {\n  for (var _key3 in config) {\n    if (!isSupportedInterpolationParam(_key3)) {\n      throw new Error(\"Interpolation property '\" + _key3 + \"' is not supported by native animated module\");\n    }\n  }\n}\nfunction generateNewNodeTag() {\n  return __nativeAnimatedNodeTagCount++;\n}\nfunction generateNewAnimationId() {\n  return __nativeAnimationIdCount++;\n}\nfunction assertNativeAnimatedModule() {\n  invariant(NativeAnimatedModule, 'Native animated module is not available');\n}\nvar _warnedMissingNativeAnimated = false;\nfunction shouldUseNativeDriver(config) {\n  if (config.useNativeDriver == null) {\n    console.warn('Animated: `useNativeDriver` was not specified. This is a required ' + 'option and must be explicitly set to `true` or `false`');\n  }\n  if (config.useNativeDriver === true && !NativeAnimatedModule) {\n    if (!_warnedMissingNativeAnimated) {\n      console.warn('Animated: `useNativeDriver` is not supported because the native ' + 'animated module is missing. Falling back to JS-based animation. To ' + 'resolve this, add `RCTAnimation` module to this app, or remove ' + '`useNativeDriver`. ' + 'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md');\n      _warnedMissingNativeAnimated = true;\n    }\n    return false;\n  }\n  return config.useNativeDriver || false;\n}\nfunction transformDataType(value) {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  if (/deg$/.test(value)) {\n    var degrees = parseFloat(value) || 0;\n    var radians = degrees * Math.PI / 180.0;\n    return radians;\n  } else {\n    return value;\n  }\n}\nexport { API, isSupportedColorStyleProp, isSupportedStyleProp, isSupportedTransformProp, isSupportedInterpolationParam, addWhitelistedStyleProp, addWhitelistedTransformProp, addWhitelistedInterpolationParam, validateStyles, validateTransform, validateInterpolation, generateNewNodeTag, generateNewAnimationId, assertNativeAnimatedModule, shouldUseNativeDriver, transformDataType };\nexport default {\n  API: API,\n  isSupportedColorStyleProp: isSupportedColorStyleProp,\n  isSupportedStyleProp: isSupportedStyleProp,\n  isSupportedTransformProp: isSupportedTransformProp,\n  isSupportedInterpolationParam: isSupportedInterpolationParam,\n  addWhitelistedStyleProp: addWhitelistedStyleProp,\n  addWhitelistedTransformProp: addWhitelistedTransformProp,\n  addWhitelistedInterpolationParam: addWhitelistedInterpolationParam,\n  validateStyles: validateStyles,\n  validateTransform: validateTransform,\n  validateInterpolation: validateInterpolation,\n  generateNewNodeTag: generateNewNodeTag,\n  generateNewAnimationId: generateNewAnimationId,\n  assertNativeAnimatedModule: assertNativeAnimatedModule,\n  shouldUseNativeDriver: shouldUseNativeDriver,\n  transformDataType: transformDataType,\n  get nativeEventEmitter() {\n    if (!nativeEventEmitter) {\n      nativeEventEmitter = new NativeEventEmitter(Platform.OS !== 'ios' ? null : NativeAnimatedModule);\n    }\n    return nativeEventEmitter;\n  }\n};", "map": {"version": 3, "names": ["_objectSpread", "NativeAnimatedNonTurboModule", "NativeAnimatedTurboModule", "NativeEventEmitter", "Platform", "ReactNativeFeatureFlags", "invariant", "RCTDeviceEventEmitter", "NativeAnimatedModule", "OS", "global", "RN$Bridgeless", "__nativeAnimatedNodeTagCount", "__nativeAnimationIdCount", "nativeEventEmitter", "waitingForQueuedOperations", "Set", "queueOperations", "queue", "singleOpQueue", "useSingleOpBatching", "queueAndExecuteBatchedOperations", "animatedShouldUseSingleOp", "flushQueueTimeout", "eventListenerGetValueCallbacks", "eventListenerAnimationFinishedCallbacks", "globalEventEmitterGetValueListener", "globalEventEmitterAnimationFinishedListener", "nativeOps", "apis", "reduce", "acc", "functionName", "i", "API", "getValue", "tag", "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queueOperation", "setWaitingForIdentifier", "id", "add", "animatedShouldDebounceQueueFlush", "clearTimeout", "unsetWaitingForIdentifier", "delete", "size", "disableQueue", "prevTimeout", "clearImmediate", "setImmediate", "flushQueue", "fn", "_len", "arguments", "length", "args", "Array", "_key", "push", "apply", "concat", "createAnimatedNode", "config", "updateAnimatedNodeConfig", "startListeningToAnimatedNodeValue", "stopListeningToAnimatedNodeValue", "connectAnimatedNodes", "parentTag", "childTag", "disconnectAnimatedNodes", "startAnimatingNode", "animationId", "nodeTag", "endCallback", "stopAnimation", "setAnimatedNodeValue", "value", "setAnimatedNodeOffset", "offset", "flattenAnimatedNodeOffset", "extractAnimatedNodeOffset", "connectAnimatedNodeToView", "viewTag", "disconnectAnimatedNodeFromView", "restoreDefaultValues", "dropAnimatedNode", "addAnimatedEventToView", "eventName", "eventMapping", "removeAnimatedEventFromView", "animatedNodeTag", "setupGlobalEventEmitterListeners", "addListener", "params", "callback", "SUPPORTED_COLOR_STYLES", "backgroundColor", "borderBottomColor", "borderColor", "borderEndColor", "borderLeftColor", "borderRightColor", "borderStartColor", "borderTopColor", "color", "tintColor", "SUPPORTED_STYLES", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderRadius", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "elevation", "opacity", "transform", "zIndex", "shadowOpacity", "shadowRadius", "scaleX", "scaleY", "translateX", "translateY", "SUPPORTED_TRANSFORMS", "scale", "rotate", "rotateX", "rotateY", "rotateZ", "perspective", "SUPPORTED_INTERPOLATION_PARAMS", "inputRange", "outputRange", "extrapolate", "extrapolateRight", "extrapolateLeft", "addWhitelistedStyleProp", "prop", "addWhitelistedTransformProp", "addWhitelistedInterpolationParam", "param", "isSupportedColorStyleProp", "hasOwnProperty", "isSupportedStyleProp", "isSupportedTransformProp", "isSupportedInterpolationParam", "validateTransform", "configs", "for<PERSON>ach", "property", "Error", "validateStyles", "styles", "_key2", "validateInterpolation", "_key3", "generateNewNodeTag", "generateNewAnimationId", "assertNativeAnimatedModule", "_warnedMissingNativeAnimated", "shouldUseNativeDriver", "useNativeDriver", "console", "warn", "transformDataType", "test", "degrees", "parseFloat", "radians", "Math", "PI"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/react-native-web/dist/vendor/react-native/Animated/NativeAnimatedHelper.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport NativeAnimatedNonTurboModule from './NativeAnimatedModule';\nimport NativeAnimatedTurboModule from './NativeAnimatedTurboModule';\nimport NativeEventEmitter from '../EventEmitter/NativeEventEmitter';\nimport Platform from '../Utilities/Platform';\nimport ReactNativeFeatureFlags from '../ReactNative/ReactNativeFeatureFlags';\nimport invariant from 'fbjs/lib/invariant';\nimport RCTDeviceEventEmitter from '../EventEmitter/RCTDeviceEventEmitter';\n// TODO ********* @petetheheat - Delete this fork when Fabric ships to 100%.\nvar NativeAnimatedModule = Platform.OS === 'ios' && global.RN$Bridgeless === true ? NativeAnimatedTurboModule : NativeAnimatedNonTurboModule;\nvar __nativeAnimatedNodeTagCount = 1; /* used for animated nodes */\nvar __nativeAnimationIdCount = 1; /* used for started animations */\n\nvar nativeEventEmitter;\nvar waitingForQueuedOperations = new Set();\nvar queueOperations = false;\nvar queue = [];\n// $FlowFixMe\nvar singleOpQueue = [];\nvar useSingleOpBatching = false;\nPlatform.OS === 'android' && !!(NativeAnimatedModule != null && NativeAnimatedModule.queueAndExecuteBatchedOperations) && ReactNativeFeatureFlags.animatedShouldUseSingleOp();\nvar flushQueueTimeout = null;\nvar eventListenerGetValueCallbacks = {};\nvar eventListenerAnimationFinishedCallbacks = {};\nvar globalEventEmitterGetValueListener = null;\nvar globalEventEmitterAnimationFinishedListener = null;\nvar nativeOps = useSingleOpBatching ? function () {\n  var apis = ['createAnimatedNode',\n  // 1\n  'updateAnimatedNodeConfig',\n  // 2\n  'getValue',\n  // 3\n  'startListeningToAnimatedNodeValue',\n  // 4\n  'stopListeningToAnimatedNodeValue',\n  // 5\n  'connectAnimatedNodes',\n  // 6\n  'disconnectAnimatedNodes',\n  // 7\n  'startAnimatingNode',\n  // 8\n  'stopAnimation',\n  // 9\n  'setAnimatedNodeValue',\n  // 10\n  'setAnimatedNodeOffset',\n  // 11\n  'flattenAnimatedNodeOffset',\n  // 12\n  'extractAnimatedNodeOffset',\n  // 13\n  'connectAnimatedNodeToView',\n  // 14\n  'disconnectAnimatedNodeFromView',\n  // 15\n  'restoreDefaultValues',\n  // 16\n  'dropAnimatedNode',\n  // 17\n  'addAnimatedEventToView',\n  // 18\n  'removeAnimatedEventFromView',\n  // 19\n  'addListener',\n  // 20\n  'removeListener' // 21\n  ];\n  return apis.reduce((acc, functionName, i) => {\n    // These indices need to be kept in sync with the indices in native (see NativeAnimatedModule in Java, or the equivalent for any other native platform).\n    // $FlowFixMe[prop-missing]\n    acc[functionName] = i + 1;\n    return acc;\n  }, {});\n}() : NativeAnimatedModule;\n\n/**\n * Wrappers around NativeAnimatedModule to provide flow and autocomplete support for\n * the native module methods, and automatic queue management on Android\n */\nvar API = {\n  getValue: function getValue(tag, saveValueCallback) {\n    invariant(nativeOps, 'Native animated module is not available');\n    if (useSingleOpBatching) {\n      if (saveValueCallback) {\n        eventListenerGetValueCallbacks[tag] = saveValueCallback;\n      }\n      // $FlowFixMe\n      API.queueOperation(nativeOps.getValue, tag);\n    } else {\n      API.queueOperation(nativeOps.getValue, tag, saveValueCallback);\n    }\n  },\n  setWaitingForIdentifier: function setWaitingForIdentifier(id) {\n    waitingForQueuedOperations.add(id);\n    queueOperations = true;\n    if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush() && flushQueueTimeout) {\n      clearTimeout(flushQueueTimeout);\n    }\n  },\n  unsetWaitingForIdentifier: function unsetWaitingForIdentifier(id) {\n    waitingForQueuedOperations.delete(id);\n    if (waitingForQueuedOperations.size === 0) {\n      queueOperations = false;\n      API.disableQueue();\n    }\n  },\n  disableQueue: function disableQueue() {\n    invariant(nativeOps, 'Native animated module is not available');\n    if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush()) {\n      var prevTimeout = flushQueueTimeout;\n      clearImmediate(prevTimeout);\n      flushQueueTimeout = setImmediate(API.flushQueue);\n    } else {\n      API.flushQueue();\n    }\n  },\n  flushQueue: function flushQueue() {\n    /*\n    invariant(NativeAnimatedModule, 'Native animated module is not available');\n    flushQueueTimeout = null;\n     // Early returns before calling any APIs\n    if (useSingleOpBatching && singleOpQueue.length === 0) {\n      return;\n    }\n    if (!useSingleOpBatching && queue.length === 0) {\n      return;\n    }\n     if (useSingleOpBatching) {\n      // Set up event listener for callbacks if it's not set up\n      if (\n        !globalEventEmitterGetValueListener ||\n        !globalEventEmitterAnimationFinishedListener\n      ) {\n        setupGlobalEventEmitterListeners();\n      }\n      // Single op batching doesn't use callback functions, instead we\n      // use RCTDeviceEventEmitter. This reduces overhead of sending lots of\n      // JSI functions across to native code; but also, TM infrastructure currently\n      // does not support packing a function into native arrays.\n      NativeAnimatedModule.queueAndExecuteBatchedOperations?.(singleOpQueue);\n      singleOpQueue.length = 0;\n    } else {\n      Platform.OS === 'android' && NativeAnimatedModule.startOperationBatch?.();\n      for (let q = 0, l = queue.length; q < l; q++) {\n        queue[q]();\n      }\n      queue.length = 0;\n      Platform.OS === 'android' &&\n        NativeAnimatedModule.finishOperationBatch?.();\n    }\n    */\n  },\n  queueOperation: function queueOperation(fn) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (useSingleOpBatching) {\n      // Get the command ID from the queued function, and push that ID and any arguments needed to execute the operation\n      // $FlowFixMe: surprise, fn is actually a number\n      singleOpQueue.push(fn, ...args);\n      return;\n    }\n\n    // If queueing is explicitly on, *or* the queue has not yet\n    // been flushed, use the queue. This is to prevent operations\n    // from being executed out of order.\n    if (queueOperations || queue.length !== 0) {\n      queue.push(() => fn(...args));\n    } else {\n      fn(...args);\n    }\n  },\n  createAnimatedNode: function createAnimatedNode(tag, config) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.createAnimatedNode, tag, config);\n  },\n  updateAnimatedNodeConfig: function updateAnimatedNodeConfig(tag, config) {\n    invariant(nativeOps, 'Native animated module is not available');\n    //if (nativeOps.updateAnimatedNodeConfig) {\n    //  API.queueOperation(nativeOps.updateAnimatedNodeConfig, tag, config);\n    //}\n  },\n  startListeningToAnimatedNodeValue: function startListeningToAnimatedNodeValue(tag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.startListeningToAnimatedNodeValue, tag);\n  },\n  stopListeningToAnimatedNodeValue: function stopListeningToAnimatedNodeValue(tag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.stopListeningToAnimatedNodeValue, tag);\n  },\n  connectAnimatedNodes: function connectAnimatedNodes(parentTag, childTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.connectAnimatedNodes, parentTag, childTag);\n  },\n  disconnectAnimatedNodes: function disconnectAnimatedNodes(parentTag, childTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.disconnectAnimatedNodes, parentTag, childTag);\n  },\n  startAnimatingNode: function startAnimatingNode(animationId, nodeTag, config, endCallback) {\n    invariant(nativeOps, 'Native animated module is not available');\n    if (useSingleOpBatching) {\n      if (endCallback) {\n        eventListenerAnimationFinishedCallbacks[animationId] = endCallback;\n      }\n      // $FlowFixMe\n      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config);\n    } else {\n      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config, endCallback);\n    }\n  },\n  stopAnimation: function stopAnimation(animationId) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.stopAnimation, animationId);\n  },\n  setAnimatedNodeValue: function setAnimatedNodeValue(nodeTag, value) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.setAnimatedNodeValue, nodeTag, value);\n  },\n  setAnimatedNodeOffset: function setAnimatedNodeOffset(nodeTag, offset) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.setAnimatedNodeOffset, nodeTag, offset);\n  },\n  flattenAnimatedNodeOffset: function flattenAnimatedNodeOffset(nodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.flattenAnimatedNodeOffset, nodeTag);\n  },\n  extractAnimatedNodeOffset: function extractAnimatedNodeOffset(nodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.extractAnimatedNodeOffset, nodeTag);\n  },\n  connectAnimatedNodeToView: function connectAnimatedNodeToView(nodeTag, viewTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.connectAnimatedNodeToView, nodeTag, viewTag);\n  },\n  disconnectAnimatedNodeFromView: function disconnectAnimatedNodeFromView(nodeTag, viewTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.disconnectAnimatedNodeFromView, nodeTag, viewTag);\n  },\n  restoreDefaultValues: function restoreDefaultValues(nodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    // Backwards compat with older native runtimes, can be removed later.\n    if (nativeOps.restoreDefaultValues != null) {\n      API.queueOperation(nativeOps.restoreDefaultValues, nodeTag);\n    }\n  },\n  dropAnimatedNode: function dropAnimatedNode(tag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.dropAnimatedNode, tag);\n  },\n  addAnimatedEventToView: function addAnimatedEventToView(viewTag, eventName, eventMapping) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.addAnimatedEventToView, viewTag, eventName, eventMapping);\n  },\n  removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag) {\n    invariant(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.removeAnimatedEventFromView, viewTag, eventName, animatedNodeTag);\n  }\n};\nfunction setupGlobalEventEmitterListeners() {\n  globalEventEmitterGetValueListener = RCTDeviceEventEmitter.addListener('onNativeAnimatedModuleGetValue', function (params) {\n    var tag = params.tag;\n    var callback = eventListenerGetValueCallbacks[tag];\n    if (!callback) {\n      return;\n    }\n    callback(params.value);\n    delete eventListenerGetValueCallbacks[tag];\n  });\n  globalEventEmitterAnimationFinishedListener = RCTDeviceEventEmitter.addListener('onNativeAnimatedModuleAnimationFinished', function (params) {\n    var animationId = params.animationId;\n    var callback = eventListenerAnimationFinishedCallbacks[animationId];\n    if (!callback) {\n      return;\n    }\n    callback(params);\n    delete eventListenerAnimationFinishedCallbacks[animationId];\n  });\n}\n\n/**\n * Styles allowed by the native animated implementation.\n *\n * In general native animated implementation should support any numeric or color property that\n * doesn't need to be updated through the shadow view hierarchy (all non-layout properties).\n */\nvar SUPPORTED_COLOR_STYLES = {\n  backgroundColor: true,\n  borderBottomColor: true,\n  borderColor: true,\n  borderEndColor: true,\n  borderLeftColor: true,\n  borderRightColor: true,\n  borderStartColor: true,\n  borderTopColor: true,\n  color: true,\n  tintColor: true\n};\nvar SUPPORTED_STYLES = _objectSpread(_objectSpread({}, SUPPORTED_COLOR_STYLES), {}, {\n  borderBottomEndRadius: true,\n  borderBottomLeftRadius: true,\n  borderBottomRightRadius: true,\n  borderBottomStartRadius: true,\n  borderRadius: true,\n  borderTopEndRadius: true,\n  borderTopLeftRadius: true,\n  borderTopRightRadius: true,\n  borderTopStartRadius: true,\n  elevation: true,\n  opacity: true,\n  transform: true,\n  zIndex: true,\n  /* ios styles */\n  shadowOpacity: true,\n  shadowRadius: true,\n  /* legacy android transform properties */\n  scaleX: true,\n  scaleY: true,\n  translateX: true,\n  translateY: true\n});\nvar SUPPORTED_TRANSFORMS = {\n  translateX: true,\n  translateY: true,\n  scale: true,\n  scaleX: true,\n  scaleY: true,\n  rotate: true,\n  rotateX: true,\n  rotateY: true,\n  rotateZ: true,\n  perspective: true\n};\nvar SUPPORTED_INTERPOLATION_PARAMS = {\n  inputRange: true,\n  outputRange: true,\n  extrapolate: true,\n  extrapolateRight: true,\n  extrapolateLeft: true\n};\nfunction addWhitelistedStyleProp(prop) {\n  SUPPORTED_STYLES[prop] = true;\n}\nfunction addWhitelistedTransformProp(prop) {\n  SUPPORTED_TRANSFORMS[prop] = true;\n}\nfunction addWhitelistedInterpolationParam(param) {\n  SUPPORTED_INTERPOLATION_PARAMS[param] = true;\n}\nfunction isSupportedColorStyleProp(prop) {\n  return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);\n}\nfunction isSupportedStyleProp(prop) {\n  return SUPPORTED_STYLES.hasOwnProperty(prop);\n}\nfunction isSupportedTransformProp(prop) {\n  return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);\n}\nfunction isSupportedInterpolationParam(param) {\n  return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);\n}\nfunction validateTransform(configs) {\n  configs.forEach(config => {\n    if (!isSupportedTransformProp(config.property)) {\n      throw new Error(\"Property '\" + config.property + \"' is not supported by native animated module\");\n    }\n  });\n}\nfunction validateStyles(styles) {\n  for (var _key2 in styles) {\n    if (!isSupportedStyleProp(_key2)) {\n      throw new Error(\"Style property '\" + _key2 + \"' is not supported by native animated module\");\n    }\n  }\n}\nfunction validateInterpolation(config) {\n  for (var _key3 in config) {\n    if (!isSupportedInterpolationParam(_key3)) {\n      throw new Error(\"Interpolation property '\" + _key3 + \"' is not supported by native animated module\");\n    }\n  }\n}\nfunction generateNewNodeTag() {\n  return __nativeAnimatedNodeTagCount++;\n}\nfunction generateNewAnimationId() {\n  return __nativeAnimationIdCount++;\n}\nfunction assertNativeAnimatedModule() {\n  invariant(NativeAnimatedModule, 'Native animated module is not available');\n}\nvar _warnedMissingNativeAnimated = false;\nfunction shouldUseNativeDriver(config) {\n  if (config.useNativeDriver == null) {\n    console.warn('Animated: `useNativeDriver` was not specified. This is a required ' + 'option and must be explicitly set to `true` or `false`');\n  }\n  if (config.useNativeDriver === true && !NativeAnimatedModule) {\n    if (!_warnedMissingNativeAnimated) {\n      console.warn('Animated: `useNativeDriver` is not supported because the native ' + 'animated module is missing. Falling back to JS-based animation. To ' + 'resolve this, add `RCTAnimation` module to this app, or remove ' + '`useNativeDriver`. ' + 'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md');\n      _warnedMissingNativeAnimated = true;\n    }\n    return false;\n  }\n  return config.useNativeDriver || false;\n}\nfunction transformDataType(value) {\n  // Change the string type to number type so we can reuse the same logic in\n  // iOS and Android platform\n  if (typeof value !== 'string') {\n    return value;\n  }\n  if (/deg$/.test(value)) {\n    var degrees = parseFloat(value) || 0;\n    var radians = degrees * Math.PI / 180.0;\n    return radians;\n  } else {\n    return value;\n  }\n}\nexport { API, isSupportedColorStyleProp, isSupportedStyleProp, isSupportedTransformProp, isSupportedInterpolationParam, addWhitelistedStyleProp, addWhitelistedTransformProp, addWhitelistedInterpolationParam, validateStyles, validateTransform, validateInterpolation, generateNewNodeTag, generateNewAnimationId, assertNativeAnimatedModule, shouldUseNativeDriver, transformDataType };\nexport default {\n  API,\n  isSupportedColorStyleProp,\n  isSupportedStyleProp,\n  isSupportedTransformProp,\n  isSupportedInterpolationParam,\n  addWhitelistedStyleProp,\n  addWhitelistedTransformProp,\n  addWhitelistedInterpolationParam,\n  validateStyles,\n  validateTransform,\n  validateInterpolation,\n  generateNewNodeTag,\n  generateNewAnimationId,\n  assertNativeAnimatedModule,\n  shouldUseNativeDriver,\n  transformDataType,\n  // $FlowExpectedError[unsafe-getters-setters] - unsafe getter lint suppresion\n  // $FlowExpectedError[missing-type-arg] - unsafe getter lint suppresion\n  get nativeEventEmitter() {\n    if (!nativeEventEmitter) {\n      nativeEventEmitter = new NativeEventEmitter(\n      // *********: NativeEventEmitter only used this parameter on iOS. Now it uses it on all platforms, so this code was modified automatically to preserve its behavior\n      // If you want to use the native module on other platforms, please remove this condition and test its behavior\n      Platform.OS !== 'ios' ? null : NativeAnimatedModule);\n    }\n    return nativeEventEmitter;\n  }\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAWhE,OAAOC,4BAA4B;AACnC,OAAOC,yBAAyB;AAChC,OAAOC,kBAAkB;AACzB,OAAOC,QAAQ;AACf,OAAOC,uBAAuB;AAC9B,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,qBAAqB;AAE5B,IAAIC,oBAAoB,GAAGJ,QAAQ,CAACK,EAAE,KAAK,KAAK,IAAIC,MAAM,CAACC,aAAa,KAAK,IAAI,GAAGT,yBAAyB,GAAGD,4BAA4B;AAC5I,IAAIW,4BAA4B,GAAG,CAAC;AACpC,IAAIC,wBAAwB,GAAG,CAAC;AAEhC,IAAIC,kBAAkB;AACtB,IAAIC,0BAA0B,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC1C,IAAIC,eAAe,GAAG,KAAK;AAC3B,IAAIC,KAAK,GAAG,EAAE;AAEd,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,mBAAmB,GAAG,KAAK;AAC/BhB,QAAQ,CAACK,EAAE,KAAK,SAAS,IAAI,CAAC,EAAED,oBAAoB,IAAI,IAAI,IAAIA,oBAAoB,CAACa,gCAAgC,CAAC,IAAIhB,uBAAuB,CAACiB,yBAAyB,CAAC,CAAC;AAC7K,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,8BAA8B,GAAG,CAAC,CAAC;AACvC,IAAIC,uCAAuC,GAAG,CAAC,CAAC;AAChD,IAAIC,kCAAkC,GAAG,IAAI;AAC7C,IAAIC,2CAA2C,GAAG,IAAI;AACtD,IAAIC,SAAS,GAAGR,mBAAmB,GAAG,YAAY;EAChD,IAAIS,IAAI,GAAG,CAAC,oBAAoB,EAEhC,0BAA0B,EAE1B,UAAU,EAEV,mCAAmC,EAEnC,kCAAkC,EAElC,sBAAsB,EAEtB,yBAAyB,EAEzB,oBAAoB,EAEpB,eAAe,EAEf,sBAAsB,EAEtB,uBAAuB,EAEvB,2BAA2B,EAE3B,2BAA2B,EAE3B,2BAA2B,EAE3B,gCAAgC,EAEhC,sBAAsB,EAEtB,kBAAkB,EAElB,wBAAwB,EAExB,6BAA6B,EAE7B,aAAa,EAEb,gBAAgB,CACf;EACD,OAAOA,IAAI,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,YAAY,EAAEC,CAAC,EAAK;IAG3CF,GAAG,CAACC,YAAY,CAAC,GAAGC,CAAC,GAAG,CAAC;IACzB,OAAOF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC,GAAGvB,oBAAoB;AAM1B,IAAI0B,GAAG,GAAG;EACRC,QAAQ,EAAE,SAASA,QAAQA,CAACC,GAAG,EAAEC,iBAAiB,EAAE;IAClD/B,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/D,IAAIR,mBAAmB,EAAE;MACvB,IAAIiB,iBAAiB,EAAE;QACrBb,8BAA8B,CAACY,GAAG,CAAC,GAAGC,iBAAiB;MACzD;MAEAH,GAAG,CAACI,cAAc,CAACV,SAAS,CAACO,QAAQ,EAAEC,GAAG,CAAC;IAC7C,CAAC,MAAM;MACLF,GAAG,CAACI,cAAc,CAACV,SAAS,CAACO,QAAQ,EAAEC,GAAG,EAAEC,iBAAiB,CAAC;IAChE;EACF,CAAC;EACDE,uBAAuB,EAAE,SAASA,uBAAuBA,CAACC,EAAE,EAAE;IAC5DzB,0BAA0B,CAAC0B,GAAG,CAACD,EAAE,CAAC;IAClCvB,eAAe,GAAG,IAAI;IACtB,IAAIZ,uBAAuB,CAACqC,gCAAgC,CAAC,CAAC,IAAInB,iBAAiB,EAAE;MACnFoB,YAAY,CAACpB,iBAAiB,CAAC;IACjC;EACF,CAAC;EACDqB,yBAAyB,EAAE,SAASA,yBAAyBA,CAACJ,EAAE,EAAE;IAChEzB,0BAA0B,CAAC8B,MAAM,CAACL,EAAE,CAAC;IACrC,IAAIzB,0BAA0B,CAAC+B,IAAI,KAAK,CAAC,EAAE;MACzC7B,eAAe,GAAG,KAAK;MACvBiB,GAAG,CAACa,YAAY,CAAC,CAAC;IACpB;EACF,CAAC;EACDA,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpCzC,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/D,IAAIvB,uBAAuB,CAACqC,gCAAgC,CAAC,CAAC,EAAE;MAC9D,IAAIM,WAAW,GAAGzB,iBAAiB;MACnC0B,cAAc,CAACD,WAAW,CAAC;MAC3BzB,iBAAiB,GAAG2B,YAAY,CAAChB,GAAG,CAACiB,UAAU,CAAC;IAClD,CAAC,MAAM;MACLjB,GAAG,CAACiB,UAAU,CAAC,CAAC;IAClB;EACF,CAAC;EACDA,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG,CAmClC,CAAC;EACDb,cAAc,EAAE,SAASA,cAAcA,CAACc,EAAE,EAAE;IAC1C,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAClC;IACA,IAAItC,mBAAmB,EAAE;MAGvBD,aAAa,CAACwC,IAAI,CAAAC,KAAA,CAAlBzC,aAAa,GAAMiC,EAAE,EAAAS,MAAA,CAAKL,IAAI,EAAC;MAC/B;IACF;IAKA,IAAIvC,eAAe,IAAIC,KAAK,CAACqC,MAAM,KAAK,CAAC,EAAE;MACzCrC,KAAK,CAACyC,IAAI,CAAC;QAAA,OAAMP,EAAE,CAAAQ,KAAA,SAAIJ,IAAI,CAAC;MAAA,EAAC;IAC/B,CAAC,MAAM;MACLJ,EAAE,CAAAQ,KAAA,SAAIJ,IAAI,CAAC;IACb;EACF,CAAC;EACDM,kBAAkB,EAAE,SAASA,kBAAkBA,CAAC1B,GAAG,EAAE2B,MAAM,EAAE;IAC3DzD,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACkC,kBAAkB,EAAE1B,GAAG,EAAE2B,MAAM,CAAC;EAC/D,CAAC;EACDC,wBAAwB,EAAE,SAASA,wBAAwBA,CAAC5B,GAAG,EAAE2B,MAAM,EAAE;IACvEzD,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;EAIjE,CAAC;EACDqC,iCAAiC,EAAE,SAASA,iCAAiCA,CAAC7B,GAAG,EAAE;IACjF9B,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACqC,iCAAiC,EAAE7B,GAAG,CAAC;EACtE,CAAC;EACD8B,gCAAgC,EAAE,SAASA,gCAAgCA,CAAC9B,GAAG,EAAE;IAC/E9B,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACsC,gCAAgC,EAAE9B,GAAG,CAAC;EACrE,CAAC;EACD+B,oBAAoB,EAAE,SAASA,oBAAoBA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACvE/D,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACuC,oBAAoB,EAAEC,SAAS,EAAEC,QAAQ,CAAC;EACzE,CAAC;EACDC,uBAAuB,EAAE,SAASA,uBAAuBA,CAACF,SAAS,EAAEC,QAAQ,EAAE;IAC7E/D,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAAC0C,uBAAuB,EAAEF,SAAS,EAAEC,QAAQ,CAAC;EAC5E,CAAC;EACDE,kBAAkB,EAAE,SAASA,kBAAkBA,CAACC,WAAW,EAAEC,OAAO,EAAEV,MAAM,EAAEW,WAAW,EAAE;IACzFpE,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/D,IAAIR,mBAAmB,EAAE;MACvB,IAAIsD,WAAW,EAAE;QACfjD,uCAAuC,CAAC+C,WAAW,CAAC,GAAGE,WAAW;MACpE;MAEAxC,GAAG,CAACI,cAAc,CAACV,SAAS,CAAC2C,kBAAkB,EAAEC,WAAW,EAAEC,OAAO,EAAEV,MAAM,CAAC;IAChF,CAAC,MAAM;MACL7B,GAAG,CAACI,cAAc,CAACV,SAAS,CAAC2C,kBAAkB,EAAEC,WAAW,EAAEC,OAAO,EAAEV,MAAM,EAAEW,WAAW,CAAC;IAC7F;EACF,CAAC;EACDC,aAAa,EAAE,SAASA,aAAaA,CAACH,WAAW,EAAE;IACjDlE,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAAC+C,aAAa,EAAEH,WAAW,CAAC;EAC1D,CAAC;EACDI,oBAAoB,EAAE,SAASA,oBAAoBA,CAACH,OAAO,EAAEI,KAAK,EAAE;IAClEvE,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACgD,oBAAoB,EAAEH,OAAO,EAAEI,KAAK,CAAC;EACpE,CAAC;EACDC,qBAAqB,EAAE,SAASA,qBAAqBA,CAACL,OAAO,EAAEM,MAAM,EAAE;IACrEzE,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACkD,qBAAqB,EAAEL,OAAO,EAAEM,MAAM,CAAC;EACtE,CAAC;EACDC,yBAAyB,EAAE,SAASA,yBAAyBA,CAACP,OAAO,EAAE;IACrEnE,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACoD,yBAAyB,EAAEP,OAAO,CAAC;EAClE,CAAC;EACDQ,yBAAyB,EAAE,SAASA,yBAAyBA,CAACR,OAAO,EAAE;IACrEnE,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACqD,yBAAyB,EAAER,OAAO,CAAC;EAClE,CAAC;EACDS,yBAAyB,EAAE,SAASA,yBAAyBA,CAACT,OAAO,EAAEU,OAAO,EAAE;IAC9E7E,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACsD,yBAAyB,EAAET,OAAO,EAAEU,OAAO,CAAC;EAC3E,CAAC;EACDC,8BAA8B,EAAE,SAASA,8BAA8BA,CAACX,OAAO,EAAEU,OAAO,EAAE;IACxF7E,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAACwD,8BAA8B,EAAEX,OAAO,EAAEU,OAAO,CAAC;EAChF,CAAC;EACDE,oBAAoB,EAAE,SAASA,oBAAoBA,CAACZ,OAAO,EAAE;IAC3DnE,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAE/D,IAAIA,SAAS,CAACyD,oBAAoB,IAAI,IAAI,EAAE;MAC1CnD,GAAG,CAACI,cAAc,CAACV,SAAS,CAACyD,oBAAoB,EAAEZ,OAAO,CAAC;IAC7D;EACF,CAAC;EACDa,gBAAgB,EAAE,SAASA,gBAAgBA,CAAClD,GAAG,EAAE;IAC/C9B,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAAC0D,gBAAgB,EAAElD,GAAG,CAAC;EACrD,CAAC;EACDmD,sBAAsB,EAAE,SAASA,sBAAsBA,CAACJ,OAAO,EAAEK,SAAS,EAAEC,YAAY,EAAE;IACxFnF,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAAC2D,sBAAsB,EAAEJ,OAAO,EAAEK,SAAS,EAAEC,YAAY,CAAC;EACxF,CAAC;EACDC,2BAA2B,WAA3BA,2BAA2BA,CAACP,OAAO,EAAEK,SAAS,EAAEG,eAAe,EAAE;IAC/DrF,SAAS,CAACsB,SAAS,EAAE,yCAAyC,CAAC;IAC/DM,GAAG,CAACI,cAAc,CAACV,SAAS,CAAC8D,2BAA2B,EAAEP,OAAO,EAAEK,SAAS,EAAEG,eAAe,CAAC;EAChG;AACF,CAAC;AACD,SAASC,gCAAgCA,CAAA,EAAG;EAC1ClE,kCAAkC,GAAGnB,qBAAqB,CAACsF,WAAW,CAAC,gCAAgC,EAAE,UAAUC,MAAM,EAAE;IACzH,IAAI1D,GAAG,GAAG0D,MAAM,CAAC1D,GAAG;IACpB,IAAI2D,QAAQ,GAAGvE,8BAA8B,CAACY,GAAG,CAAC;IAClD,IAAI,CAAC2D,QAAQ,EAAE;MACb;IACF;IACAA,QAAQ,CAACD,MAAM,CAACjB,KAAK,CAAC;IACtB,OAAOrD,8BAA8B,CAACY,GAAG,CAAC;EAC5C,CAAC,CAAC;EACFT,2CAA2C,GAAGpB,qBAAqB,CAACsF,WAAW,CAAC,yCAAyC,EAAE,UAAUC,MAAM,EAAE;IAC3I,IAAItB,WAAW,GAAGsB,MAAM,CAACtB,WAAW;IACpC,IAAIuB,QAAQ,GAAGtE,uCAAuC,CAAC+C,WAAW,CAAC;IACnE,IAAI,CAACuB,QAAQ,EAAE;MACb;IACF;IACAA,QAAQ,CAACD,MAAM,CAAC;IAChB,OAAOrE,uCAAuC,CAAC+C,WAAW,CAAC;EAC7D,CAAC,CAAC;AACJ;AAQA,IAAIwB,sBAAsB,GAAG;EAC3BC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE;AACb,CAAC;AACD,IAAIC,gBAAgB,GAAG3G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgG,sBAAsB,CAAC,EAAE,CAAC,CAAC,EAAE;EAClFY,qBAAqB,EAAE,IAAI;EAC3BC,sBAAsB,EAAE,IAAI;EAC5BC,uBAAuB,EAAE,IAAI;EAC7BC,uBAAuB,EAAE,IAAI;EAC7BC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,oBAAoB,EAAE,IAAI;EAC1BC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EAEZC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAElBC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,IAAIC,oBAAoB,GAAG;EACzBF,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBE,KAAK,EAAE,IAAI;EACXL,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZK,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE;AACf,CAAC;AACD,IAAIC,8BAA8B,GAAG;EACnCC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,eAAe,EAAE;AACnB,CAAC;AACD,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EACrClC,gBAAgB,CAACkC,IAAI,CAAC,GAAG,IAAI;AAC/B;AACA,SAASC,2BAA2BA,CAACD,IAAI,EAAE;EACzCd,oBAAoB,CAACc,IAAI,CAAC,GAAG,IAAI;AACnC;AACA,SAASE,gCAAgCA,CAACC,KAAK,EAAE;EAC/CV,8BAA8B,CAACU,KAAK,CAAC,GAAG,IAAI;AAC9C;AACA,SAASC,yBAAyBA,CAACJ,IAAI,EAAE;EACvC,OAAO7C,sBAAsB,CAACkD,cAAc,CAACL,IAAI,CAAC;AACpD;AACA,SAASM,oBAAoBA,CAACN,IAAI,EAAE;EAClC,OAAOlC,gBAAgB,CAACuC,cAAc,CAACL,IAAI,CAAC;AAC9C;AACA,SAASO,wBAAwBA,CAACP,IAAI,EAAE;EACtC,OAAOd,oBAAoB,CAACmB,cAAc,CAACL,IAAI,CAAC;AAClD;AACA,SAASQ,6BAA6BA,CAACL,KAAK,EAAE;EAC5C,OAAOV,8BAA8B,CAACY,cAAc,CAACF,KAAK,CAAC;AAC7D;AACA,SAASM,iBAAiBA,CAACC,OAAO,EAAE;EAClCA,OAAO,CAACC,OAAO,CAAC,UAAAzF,MAAM,EAAI;IACxB,IAAI,CAACqF,wBAAwB,CAACrF,MAAM,CAAC0F,QAAQ,CAAC,EAAE;MAC9C,MAAM,IAAIC,KAAK,CAAC,YAAY,GAAG3F,MAAM,CAAC0F,QAAQ,GAAG,8CAA8C,CAAC;IAClG;EACF,CAAC,CAAC;AACJ;AACA,SAASE,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,KAAK,IAAID,MAAM,EAAE;IACxB,IAAI,CAACT,oBAAoB,CAACU,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIH,KAAK,CAAC,kBAAkB,GAAGG,KAAK,GAAG,8CAA8C,CAAC;IAC9F;EACF;AACF;AACA,SAASC,qBAAqBA,CAAC/F,MAAM,EAAE;EACrC,KAAK,IAAIgG,KAAK,IAAIhG,MAAM,EAAE;IACxB,IAAI,CAACsF,6BAA6B,CAACU,KAAK,CAAC,EAAE;MACzC,MAAM,IAAIL,KAAK,CAAC,0BAA0B,GAAGK,KAAK,GAAG,8CAA8C,CAAC;IACtG;EACF;AACF;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,OAAOpJ,4BAA4B,EAAE;AACvC;AACA,SAASqJ,sBAAsBA,CAAA,EAAG;EAChC,OAAOpJ,wBAAwB,EAAE;AACnC;AACA,SAASqJ,0BAA0BA,CAAA,EAAG;EACpC5J,SAAS,CAACE,oBAAoB,EAAE,yCAAyC,CAAC;AAC5E;AACA,IAAI2J,4BAA4B,GAAG,KAAK;AACxC,SAASC,qBAAqBA,CAACrG,MAAM,EAAE;EACrC,IAAIA,MAAM,CAACsG,eAAe,IAAI,IAAI,EAAE;IAClCC,OAAO,CAACC,IAAI,CAAC,oEAAoE,GAAG,wDAAwD,CAAC;EAC/I;EACA,IAAIxG,MAAM,CAACsG,eAAe,KAAK,IAAI,IAAI,CAAC7J,oBAAoB,EAAE;IAC5D,IAAI,CAAC2J,4BAA4B,EAAE;MACjCG,OAAO,CAACC,IAAI,CAAC,kEAAkE,GAAG,qEAAqE,GAAG,iEAAiE,GAAG,qBAAqB,GAAG,8JAA8J,CAAC;MACrZJ,4BAA4B,GAAG,IAAI;IACrC;IACA,OAAO,KAAK;EACd;EACA,OAAOpG,MAAM,CAACsG,eAAe,IAAI,KAAK;AACxC;AACA,SAASG,iBAAiBA,CAAC3F,KAAK,EAAE;EAGhC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,IAAI,MAAM,CAAC4F,IAAI,CAAC5F,KAAK,CAAC,EAAE;IACtB,IAAI6F,OAAO,GAAGC,UAAU,CAAC9F,KAAK,CAAC,IAAI,CAAC;IACpC,IAAI+F,OAAO,GAAGF,OAAO,GAAGG,IAAI,CAACC,EAAE,GAAG,KAAK;IACvC,OAAOF,OAAO;EAChB,CAAC,MAAM;IACL,OAAO/F,KAAK;EACd;AACF;AACA,SAAS3C,GAAG,EAAE+G,yBAAyB,EAAEE,oBAAoB,EAAEC,wBAAwB,EAAEC,6BAA6B,EAAET,uBAAuB,EAAEE,2BAA2B,EAAEC,gCAAgC,EAAEY,cAAc,EAAEL,iBAAiB,EAAEQ,qBAAqB,EAAEE,kBAAkB,EAAEC,sBAAsB,EAAEC,0BAA0B,EAAEE,qBAAqB,EAAEI,iBAAiB;AAC1X,eAAe;EACbtI,GAAG,EAAHA,GAAG;EACH+G,yBAAyB,EAAzBA,yBAAyB;EACzBE,oBAAoB,EAApBA,oBAAoB;EACpBC,wBAAwB,EAAxBA,wBAAwB;EACxBC,6BAA6B,EAA7BA,6BAA6B;EAC7BT,uBAAuB,EAAvBA,uBAAuB;EACvBE,2BAA2B,EAA3BA,2BAA2B;EAC3BC,gCAAgC,EAAhCA,gCAAgC;EAChCY,cAAc,EAAdA,cAAc;EACdL,iBAAiB,EAAjBA,iBAAiB;EACjBQ,qBAAqB,EAArBA,qBAAqB;EACrBE,kBAAkB,EAAlBA,kBAAkB;EAClBC,sBAAsB,EAAtBA,sBAAsB;EACtBC,0BAA0B,EAA1BA,0BAA0B;EAC1BE,qBAAqB,EAArBA,qBAAqB;EACrBI,iBAAiB,EAAjBA,iBAAiB;EAGjB,IAAI1J,kBAAkBA,CAAA,EAAG;IACvB,IAAI,CAACA,kBAAkB,EAAE;MACvBA,kBAAkB,GAAG,IAAIX,kBAAkB,CAG3CC,QAAQ,CAACK,EAAE,KAAK,KAAK,GAAG,IAAI,GAAGD,oBAAoB,CAAC;IACtD;IACA,OAAOM,kBAAkB;EAC3B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}