{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ToggleButton from \"./ToggleButton\";\nimport ToggleButtonGroup from \"./ToggleButtonGroup\";\nvar ToggleButtonRow = function ToggleButtonRow(_ref) {\n  var value = _ref.value,\n    onValueChange = _ref.onValueChange,\n    children = _ref.children,\n    style = _ref.style;\n  var count = React.Children.count(children);\n  return React.createElement(ToggleButtonGroup, {\n    value: value,\n    onValueChange: onValueChange\n  }, React.createElement(View, {\n    style: [styles.row, style]\n  }, React.Children.map(children, function (child, i) {\n    if (child && child.type === ToggleButton) {\n      return React.cloneElement(child, {\n        style: [styles.button, i === 0 ? styles.first : i === count - 1 ? styles.last : styles.middle, child.props.style]\n      });\n    }\n    return child;\n  })));\n};\nToggleButtonRow.displayName = 'ToggleButton.Row';\nvar styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row'\n  },\n  button: {\n    borderWidth: StyleSheet.hairlineWidth\n  },\n  first: {\n    borderTopRightRadius: 0,\n    borderBottomRightRadius: 0\n  },\n  middle: {\n    borderRadius: 0,\n    borderLeftWidth: 0\n  },\n  last: {\n    borderLeftWidth: 0,\n    borderTopLeftRadius: 0,\n    borderBottomLeftRadius: 0\n  }\n});\nexport default ToggleButtonRow;\nexport { ToggleButtonRow };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "ToggleButton", "ToggleButtonGroup", "ToggleButtonRow", "_ref", "value", "onValueChange", "children", "style", "count", "Children", "createElement", "styles", "row", "map", "child", "i", "type", "cloneElement", "button", "first", "last", "middle", "props", "displayName", "create", "flexDirection", "borderWidth", "hairlineWidth", "borderTopRightRadius", "borderBottomRightRadius", "borderRadius", "borderLeftWidth", "borderTopLeftRadius", "borderBottomLeftRadius"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/ToggleButton/ToggleButtonRow.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleSheet, View, StyleProp, ViewStyle } from 'react-native';\n\nimport ToggleButton from './ToggleButton';\nimport ToggleButtonGroup from './ToggleButtonGroup';\n\nexport type Props = {\n  /**\n   * Function to execute on selection change.\n   */\n  onValueChange: (value: string) => void;\n  /**\n   * Value of the currently selected toggle button.\n   */\n  value: string;\n  /**\n   * React elements containing toggle buttons.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n};\n\n/**\n * Toggle button row renders a group of toggle buttons in a row.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { ToggleButton } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [value, setValue] = React.useState('left');\n *\n *   return (\n *     <ToggleButton.Row onValueChange={value => setValue(value)} value={value}>\n *       <ToggleButton icon=\"format-align-left\" value=\"left\" />\n *       <ToggleButton icon=\"format-align-right\" value=\"right\" />\n *     </ToggleButton.Row>\n *   );\n * };\n *\n * export default MyComponent;\n *\n *```\n */\nconst ToggleButtonRow = ({ value, onValueChange, children, style }: Props) => {\n  const count = React.Children.count(children);\n\n  return (\n    <ToggleButtonGroup value={value} onValueChange={onValueChange}>\n      <View style={[styles.row, style]}>\n        {React.Children.map(children, (child, i) => {\n          // @ts-expect-error: TypeScript complains about child.type but it doesn't matter\n          if (child && child.type === ToggleButton) {\n            // @ts-expect-error: We're sure that child is a React Element\n            return React.cloneElement(child, {\n              style: [\n                styles.button,\n                i === 0\n                  ? styles.first\n                  : i === count - 1\n                  ? styles.last\n                  : styles.middle,\n                // @ts-expect-error: We're sure that child is a React Element\n                child.props.style,\n              ],\n            });\n          }\n\n          return child;\n        })}\n      </View>\n    </ToggleButtonGroup>\n  );\n};\n\nToggleButtonRow.displayName = 'ToggleButton.Row';\n\nconst styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row',\n  },\n  button: {\n    borderWidth: StyleSheet.hairlineWidth,\n  },\n\n  first: {\n    borderTopRightRadius: 0,\n    borderBottomRightRadius: 0,\n  },\n\n  middle: {\n    borderRadius: 0,\n    borderLeftWidth: 0,\n  },\n\n  last: {\n    borderLeftWidth: 0,\n    borderTopLeftRadius: 0,\n    borderBottomLeftRadius: 0,\n  },\n});\n\nexport default ToggleButtonRow;\n\n// @component-docs ignore-next-line\nexport { ToggleButtonRow };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,YAAY;AACnB,OAAOC,iBAAiB;AAyCxB,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,IAAA,EAAsD;EAAA,IAAnDC,KAAK,GAAyCD,IAAA,CAA9CC,KAAK;IAAEC,aAAa,GAA0BF,IAAA,CAAvCE,aAAa;IAAEC,QAAQ,GAAgBH,IAAA,CAAxBG,QAAQ;IAAEC,KAAA,GAAcJ,IAAA,CAAdI,KAAA;EACzD,IAAMC,KAAK,GAAGX,KAAK,CAACY,QAAQ,CAACD,KAAK,CAACF,QAAQ,CAAC;EAE5C,OACET,KAAA,CAAAa,aAAA,CAACT,iBAAiB;IAACG,KAAK,EAAEA,KAAM;IAACC,aAAa,EAAEA;EAAc,GAC5DR,KAAA,CAAAa,aAAA,CAACX,IAAI;IAACQ,KAAK,EAAE,CAACI,MAAM,CAACC,GAAG,EAAEL,KAAK;EAAE,GAC9BV,KAAK,CAACY,QAAQ,CAACI,GAAG,CAACP,QAAQ,EAAE,UAACQ,KAAK,EAAEC,CAAC,EAAK;IAE1C,IAAID,KAAK,IAAIA,KAAK,CAACE,IAAI,KAAKhB,YAAY,EAAE;MAExC,OAAOH,KAAK,CAACoB,YAAY,CAACH,KAAK,EAAE;QAC/BP,KAAK,EAAE,CACLI,MAAM,CAACO,MAAM,EACbH,CAAC,KAAK,CAAC,GACHJ,MAAM,CAACQ,KAAK,GACZJ,CAAC,KAAKP,KAAK,GAAG,CAAC,GACfG,MAAM,CAACS,IAAI,GACXT,MAAM,CAACU,MAAM,EAEjBP,KAAK,CAACQ,KAAK,CAACf,KAAK;MAErB,CAAC,CAAC;IACJ;IAEA,OAAOO,KAAK;EACd,CAAC,CACG,CACW,CAAC;AAExB,CAAC;AAEDZ,eAAe,CAACqB,WAAW,GAAG,kBAAkB;AAEhD,IAAMZ,MAAM,GAAGb,UAAU,CAAC0B,MAAM,CAAC;EAC/BZ,GAAG,EAAE;IACHa,aAAa,EAAE;EACjB,CAAC;EACDP,MAAM,EAAE;IACNQ,WAAW,EAAE5B,UAAU,CAAC6B;EAC1B,CAAC;EAEDR,KAAK,EAAE;IACLS,oBAAoB,EAAE,CAAC;IACvBC,uBAAuB,EAAE;EAC3B,CAAC;EAEDR,MAAM,EAAE;IACNS,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE;EACnB,CAAC;EAEDX,IAAI,EAAE;IACJW,eAAe,EAAE,CAAC;IAClBC,mBAAmB,EAAE,CAAC;IACtBC,sBAAsB,EAAE;EAC1B;AACF,CAAC,CAAC;AAEF,eAAe/B,eAAe;AAG9B,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}