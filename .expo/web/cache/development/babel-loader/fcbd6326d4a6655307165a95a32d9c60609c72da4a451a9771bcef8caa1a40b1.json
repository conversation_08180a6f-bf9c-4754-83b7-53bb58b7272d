{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"mode\", \"children\", \"icon\", \"avatar\", \"selected\", \"disabled\", \"background\", \"accessibilityLabel\", \"accessibilityRole\", \"closeIconAccessibilityLabel\", \"onPress\", \"onLongPress\", \"onPressOut\", \"onPressIn\", \"delayLongPress\", \"onClose\", \"closeIcon\", \"textStyle\", \"style\", \"theme\", \"testID\", \"selectedColor\", \"rippleColor\", \"showSelectedOverlay\", \"showSelectedCheck\", \"ellipsizeMode\", \"compact\", \"elevated\", \"maxFontSizeMultiplier\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport View from \"react-native-web/dist/exports/View\";\nimport useLatestCallback from 'use-latest-callback';\nimport { getChipColors } from \"./helpers\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { white } from \"../../styles/themes/v2/colors\";\nimport hasTouchHandler from \"../../utils/hasTouchHandler\";\nimport Icon from \"../Icon\";\nimport MaterialCommunityIcon from \"../MaterialCommunityIcon\";\nimport Surface from \"../Surface\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar Chip = function Chip(_ref) {\n  var _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'flat' : _ref$mode,\n    children = _ref.children,\n    icon = _ref.icon,\n    avatar = _ref.avatar,\n    _ref$selected = _ref.selected,\n    selected = _ref$selected === void 0 ? false : _ref$selected,\n    _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    background = _ref.background,\n    accessibilityLabel = _ref.accessibilityLabel,\n    _ref$accessibilityRol = _ref.accessibilityRole,\n    accessibilityRole = _ref$accessibilityRol === void 0 ? 'button' : _ref$accessibilityRol,\n    _ref$closeIconAccessi = _ref.closeIconAccessibilityLabel,\n    closeIconAccessibilityLabel = _ref$closeIconAccessi === void 0 ? 'Close' : _ref$closeIconAccessi,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    onPressOut = _ref.onPressOut,\n    onPressIn = _ref.onPressIn,\n    delayLongPress = _ref.delayLongPress,\n    onClose = _ref.onClose,\n    closeIcon = _ref.closeIcon,\n    textStyle = _ref.textStyle,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'chip' : _ref$testID,\n    selectedColor = _ref.selectedColor,\n    customRippleColor = _ref.rippleColor,\n    _ref$showSelectedOver = _ref.showSelectedOverlay,\n    showSelectedOverlay = _ref$showSelectedOver === void 0 ? false : _ref$showSelectedOver,\n    _ref$showSelectedChec = _ref.showSelectedCheck,\n    showSelectedCheck = _ref$showSelectedChec === void 0 ? true : _ref$showSelectedChec,\n    ellipsizeMode = _ref.ellipsizeMode,\n    compact = _ref.compact,\n    _ref$elevated = _ref.elevated,\n    elevated = _ref$elevated === void 0 ? false : _ref$elevated,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3,\n    roundness = theme.roundness;\n  var _React$useRef = React.useRef(new Animated.Value(isV3 && elevated ? 1 : 0)),\n    elevation = _React$useRef.current;\n  var hasPassedTouchHandler = hasTouchHandler({\n    onPress: onPress,\n    onLongPress: onLongPress,\n    onPressIn: onPressIn,\n    onPressOut: onPressOut\n  });\n  var isOutlined = mode === 'outlined';\n  var handlePressIn = useLatestCallback(function (e) {\n    var scale = theme.animation.scale;\n    onPressIn === null || onPressIn === void 0 ? void 0 : onPressIn(e);\n    Animated.timing(elevation, {\n      toValue: isV3 ? elevated ? 2 : 0 : 4,\n      duration: 200 * scale,\n      useNativeDriver: Platform.OS === 'web' || Platform.constants.reactNativeVersion.minor <= 72\n    }).start();\n  });\n  var handlePressOut = useLatestCallback(function (e) {\n    var scale = theme.animation.scale;\n    onPressOut === null || onPressOut === void 0 ? void 0 : onPressOut(e);\n    Animated.timing(elevation, {\n      toValue: isV3 && elevated ? 1 : 0,\n      duration: 150 * scale,\n      useNativeDriver: Platform.OS === 'web' || Platform.constants.reactNativeVersion.minor <= 72\n    }).start();\n  });\n  var opacity = isV3 ? 0.38 : 0.26;\n  var defaultBorderRadius = roundness * (isV3 ? 2 : 4);\n  var iconSize = isV3 ? 18 : 16;\n  var _ref2 = StyleSheet.flatten(style) || {},\n    customBackgroundColor = _ref2.backgroundColor,\n    _ref2$borderRadius = _ref2.borderRadius,\n    borderRadius = _ref2$borderRadius === void 0 ? defaultBorderRadius : _ref2$borderRadius;\n  var _getChipColors = getChipColors({\n      isOutlined: isOutlined,\n      theme: theme,\n      selectedColor: selectedColor,\n      showSelectedOverlay: showSelectedOverlay,\n      customBackgroundColor: customBackgroundColor,\n      disabled: disabled,\n      customRippleColor: customRippleColor\n    }),\n    borderColor = _getChipColors.borderColor,\n    textColor = _getChipColors.textColor,\n    iconColor = _getChipColors.iconColor,\n    rippleColor = _getChipColors.rippleColor,\n    selectedBackgroundColor = _getChipColors.selectedBackgroundColor,\n    backgroundColor = _getChipColors.backgroundColor;\n  var accessibilityState = {\n    selected: selected,\n    disabled: disabled\n  };\n  var elevationStyle = isV3 || Platform.OS === 'android' ? elevation : 0;\n  var multiplier = isV3 ? compact ? 1.5 : 2 : 1;\n  var labelSpacings = {\n    marginRight: onClose ? 0 : 8 * multiplier,\n    marginLeft: avatar || icon || selected && showSelectedCheck ? 4 * multiplier : 8 * multiplier\n  };\n  var contentSpacings = {\n    paddingRight: isV3 ? onClose ? 34 : 0 : onClose ? 32 : 4\n  };\n  var labelTextStyle = _objectSpread({\n    color: textColor\n  }, isV3 ? theme.fonts.labelLarge : theme.fonts.regular);\n  return React.createElement(Surface, _extends({\n    style: [styles.container, isV3 && styles.md3Container, !theme.isV3 && {\n      elevation: elevationStyle\n    }, {\n      backgroundColor: selected ? selectedBackgroundColor : backgroundColor,\n      borderColor: borderColor,\n      borderRadius: borderRadius\n    }, style]\n  }, theme.isV3 && {\n    elevation: elevationStyle\n  }, rest, {\n    testID: `${testID}-container`,\n    theme: theme\n  }), React.createElement(TouchableRipple, {\n    borderless: true,\n    background: background,\n    style: [{\n      borderRadius: borderRadius\n    }, styles.touchable],\n    onPress: onPress,\n    onLongPress: onLongPress,\n    onPressIn: hasPassedTouchHandler ? handlePressIn : undefined,\n    onPressOut: hasPassedTouchHandler ? handlePressOut : undefined,\n    delayLongPress: delayLongPress,\n    rippleColor: rippleColor,\n    disabled: disabled,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: accessibilityRole,\n    accessibilityState: accessibilityState,\n    testID: testID,\n    theme: theme\n  }, React.createElement(View, {\n    style: [styles.content, isV3 && styles.md3Content, contentSpacings]\n  }, avatar && !icon ? React.createElement(View, {\n    style: [styles.avatarWrapper, isV3 && styles.md3AvatarWrapper, disabled && {\n      opacity: opacity\n    }]\n  }, React.isValidElement(avatar) ? React.cloneElement(avatar, {\n    style: [styles.avatar, avatar.props.style]\n  }) : avatar) : null, icon || selected && showSelectedCheck ? React.createElement(View, {\n    style: [styles.icon, isV3 && styles.md3Icon, avatar ? [styles.avatar, styles.avatarSelected, isV3 && selected && styles.md3SelectedIcon] : null]\n  }, icon ? React.createElement(Icon, {\n    source: icon,\n    color: avatar ? white : !disabled && theme.isV3 ? theme.colors.primary : iconColor,\n    size: 18,\n    theme: theme\n  }) : React.createElement(MaterialCommunityIcon, {\n    name: \"check\",\n    color: avatar ? white : iconColor,\n    size: 18,\n    direction: \"ltr\"\n  })) : null, React.createElement(Text, {\n    variant: \"labelLarge\",\n    selectable: false,\n    numberOfLines: 1,\n    style: [isV3 ? styles.md3LabelText : styles.labelText, labelTextStyle, labelSpacings, textStyle],\n    ellipsizeMode: ellipsizeMode,\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, children))), onClose ? React.createElement(View, {\n    style: styles.closeButtonStyle\n  }, React.createElement(Pressable, {\n    onPress: onClose,\n    disabled: disabled,\n    accessibilityRole: \"button\",\n    accessibilityLabel: closeIconAccessibilityLabel\n  }, React.createElement(View, {\n    style: [styles.icon, styles.closeIcon, isV3 && styles.md3CloseIcon]\n  }, closeIcon ? React.createElement(Icon, {\n    source: closeIcon,\n    color: iconColor,\n    size: iconSize\n  }) : React.createElement(MaterialCommunityIcon, {\n    name: isV3 ? 'close' : 'close-circle',\n    size: iconSize,\n    color: iconColor,\n    direction: \"ltr\"\n  })))) : null);\n};\nvar styles = StyleSheet.create({\n  container: {\n    borderWidth: StyleSheet.hairlineWidth,\n    borderStyle: 'solid',\n    flexDirection: Platform.select({\n      default: 'column',\n      web: 'row'\n    })\n  },\n  md3Container: {\n    borderWidth: 1\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    paddingLeft: 4,\n    position: 'relative'\n  },\n  md3Content: {\n    paddingLeft: 0\n  },\n  icon: {\n    padding: 4,\n    alignSelf: 'center'\n  },\n  md3Icon: {\n    paddingLeft: 8,\n    paddingRight: 0\n  },\n  closeIcon: {\n    marginRight: 4\n  },\n  md3CloseIcon: {\n    marginRight: 8,\n    padding: 0\n  },\n  labelText: {\n    minHeight: 24,\n    lineHeight: 24,\n    textAlignVertical: 'center',\n    marginVertical: 4\n  },\n  md3LabelText: {\n    textAlignVertical: 'center',\n    marginVertical: 6\n  },\n  avatar: {\n    width: 24,\n    height: 24,\n    borderRadius: 12\n  },\n  avatarWrapper: {\n    marginRight: 4\n  },\n  md3AvatarWrapper: {\n    marginLeft: 4,\n    marginRight: 0\n  },\n  md3SelectedIcon: {\n    paddingLeft: 4\n  },\n  avatarSelected: {\n    position: 'absolute',\n    top: 4,\n    left: 4,\n    backgroundColor: 'rgba(0, 0, 0, .29)'\n  },\n  closeButtonStyle: {\n    position: 'absolute',\n    right: 0,\n    height: '100%',\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  touchable: {\n    width: '100%'\n  }\n});\nexport default Chip;", "map": {"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "Pressable", "View", "useLatestCallback", "getChipColors", "useInternalTheme", "white", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "Icon", "MaterialCommunityIcon", "Surface", "TouchableRipple", "Text", "Chip", "_ref", "_ref$mode", "mode", "children", "icon", "avatar", "_ref$selected", "selected", "_ref$disabled", "disabled", "background", "accessibilityLabel", "_ref$accessibilityRol", "accessibilityRole", "_ref$closeIconAccessi", "closeIconAccessibilityLabel", "onPress", "onLongPress", "onPressOut", "onPressIn", "delayLongPress", "onClose", "closeIcon", "textStyle", "style", "themeOverrides", "theme", "_ref$testID", "testID", "selectedColor", "customRippleColor", "rippleColor", "_ref$showSelectedOver", "showSelectedOverlay", "_ref$showSelectedChec", "showSelectedCheck", "ellipsizeMode", "compact", "_ref$elevated", "elevated", "maxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "isV3", "roundness", "_React$useRef", "useRef", "Value", "elevation", "current", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isOutlined", "handlePressIn", "e", "scale", "animation", "timing", "toValue", "duration", "useNativeDriver", "OS", "constants", "reactNativeVersion", "minor", "start", "handlePressOut", "opacity", "defaultBorderRadius", "iconSize", "_ref2", "flatten", "customBackgroundColor", "backgroundColor", "_ref2$borderRadius", "borderRadius", "_getChipColors", "borderColor", "textColor", "iconColor", "selectedBackgroundColor", "accessibilityState", "elevationStyle", "multiplier", "labelSpacings", "marginRight", "marginLeft", "contentSpacings", "paddingRight", "labelTextStyle", "_objectSpread", "color", "fonts", "labelLarge", "regular", "createElement", "_extends", "styles", "container", "md3Container", "borderless", "touchable", "undefined", "content", "md3Content", "avatar<PERSON><PERSON>per", "md3AvatarWrapper", "isValidElement", "cloneElement", "props", "md3Icon", "avatarSelected", "md3SelectedIcon", "source", "colors", "primary", "size", "name", "direction", "variant", "selectable", "numberOfLines", "md3LabelText", "labelText", "closeButtonStyle", "md3CloseIcon", "create", "borderWidth", "hairlineWidth", "borderStyle", "flexDirection", "select", "default", "web", "alignItems", "paddingLeft", "position", "padding", "alignSelf", "minHeight", "lineHeight", "textAlignVertical", "marginVertical", "width", "height", "top", "left", "right", "justifyContent"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Chip/Chip.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  AccessibilityState,\n  Animated,\n  ColorValue,\n  GestureResponderEvent,\n  Platform,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  Pressable,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport useLatestCallback from 'use-latest-callback';\n\nimport { getChipColors } from './helpers';\nimport { useInternalTheme } from '../../core/theming';\nimport { white } from '../../styles/themes/v2/colors';\nimport type { $Omit, EllipsizeProp, ThemeProp } from '../../types';\nimport hasTouchHandler from '../../utils/hasTouchHandler';\nimport type { IconSource } from '../Icon';\nimport Icon from '../Icon';\nimport MaterialCommunityIcon from '../MaterialCommunityIcon';\nimport Surface from '../Surface';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = $Omit<React.ComponentProps<typeof Surface>, 'mode'> & {\n  /**\n   * Mode of the chip.\n   * - `flat` - flat chip without outline.\n   * - `outlined` - chip with an outline.\n   */\n  mode?: 'flat' | 'outlined';\n  /**\n   * Text content of the `Chip`.\n   */\n  children: React.ReactNode;\n  /**\n   * Icon to display for the `Chip`. Both icon and avatar cannot be specified.\n   */\n  icon?: IconSource;\n  /**\n   * Avatar to display for the `Chip`. Both icon and avatar cannot be specified.\n   */\n  avatar?: React.ReactNode;\n  /**\n   * Icon to display as the close button for the `Chip`. The icon appears only when the onClose prop is specified.\n   */\n  closeIcon?: IconSource;\n  /**\n   * Whether chip is selected.\n   */\n  selected?: boolean;\n  /**\n   * Whether to style the chip color as selected.\n   * Note: With theme version 3 `selectedColor` doesn't apply to the `icon`.\n   *       If you want specify custom color for the `icon`, render your own `Icon` component.\n   */\n  selectedColor?: string;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Whether to display overlay on selected chip\n   */\n  showSelectedOverlay?: boolean;\n  /**\n   * Whether to display default check icon on selected chip.\n   * Note: Check will not be shown if `icon` is specified. If specified, `icon` will be shown regardless of `selected`.\n   */\n  showSelectedCheck?: boolean;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Whether the chip is disabled. A disabled chip is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the chip. This is read by the screen reader when the user taps the chip.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Accessibility label for the close icon. This is read by the screen reader when the user taps the close icon.\n   */\n  closeIconAccessibilityLabel?: string;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: () => void;\n  /**\n   * Function to execute as soon as the touchable element is pressed and invoked even before onPress.\n   */\n  onPressIn?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute as soon as the touch is released even before onPress.\n   */\n  onPressOut?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on close button press. The close button appears only when this prop is specified.\n   */\n  onClose?: () => void;\n  /**\n   * The number of milliseconds a user must touch the element before executing `onLongPress`.\n   */\n  delayLongPress?: number;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Sets smaller horizontal paddings `12dp` around label, when there is only label.\n   */\n  compact?: boolean;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Whether chip should have the elevation.\n   */\n  elevated?: boolean;\n  /**\n   * Style of chip's text\n   */\n  textStyle?: StyleProp<TextStyle>;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Pass down testID from chip props to touchable for Detox tests.\n   */\n  testID?: string;\n  /**\n   * Ellipsize Mode for the children text\n   */\n  ellipsizeMode?: EllipsizeProp;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n};\n\n/**\n * Chips are compact elements that can represent inputs, attributes, or actions.\n * They can have an icon or avatar on the left, and a close button icon on the right.\n * They are typically used to:\n * <ul>\n *  <li>Present multiple options </li>\n *  <li>Represent attributes active or chosen </li>\n *  <li>Present filter options </li>\n *  <li>Trigger actions related to primary content </li>\n * </ul>\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Chip } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Chip icon=\"information\" onPress={() => console.log('Pressed')}>Example Chip</Chip>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Chip = ({\n  mode = 'flat',\n  children,\n  icon,\n  avatar,\n  selected = false,\n  disabled = false,\n  background,\n  accessibilityLabel,\n  accessibilityRole = 'button',\n  closeIconAccessibilityLabel = 'Close',\n  onPress,\n  onLongPress,\n  onPressOut,\n  onPressIn,\n  delayLongPress,\n  onClose,\n  closeIcon,\n  textStyle,\n  style,\n  theme: themeOverrides,\n  testID = 'chip',\n  selectedColor,\n  rippleColor: customRippleColor,\n  showSelectedOverlay = false,\n  showSelectedCheck = true,\n  ellipsizeMode,\n  compact,\n  elevated = false,\n  maxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { isV3, roundness } = theme;\n\n  const { current: elevation } = React.useRef<Animated.Value>(\n    new Animated.Value(isV3 && elevated ? 1 : 0)\n  );\n\n  const hasPassedTouchHandler = hasTouchHandler({\n    onPress,\n    onLongPress,\n    onPressIn,\n    onPressOut,\n  });\n\n  const isOutlined = mode === 'outlined';\n\n  const handlePressIn = useLatestCallback((e: GestureResponderEvent) => {\n    const { scale } = theme.animation;\n    onPressIn?.(e);\n    Animated.timing(elevation, {\n      toValue: isV3 ? (elevated ? 2 : 0) : 4,\n      duration: 200 * scale,\n      useNativeDriver:\n        Platform.OS === 'web' ||\n        Platform.constants.reactNativeVersion.minor <= 72,\n    }).start();\n  });\n\n  const handlePressOut = useLatestCallback((e: GestureResponderEvent) => {\n    const { scale } = theme.animation;\n    onPressOut?.(e);\n    Animated.timing(elevation, {\n      toValue: isV3 && elevated ? 1 : 0,\n      duration: 150 * scale,\n      useNativeDriver:\n        Platform.OS === 'web' ||\n        Platform.constants.reactNativeVersion.minor <= 72,\n    }).start();\n  });\n\n  const opacity = isV3 ? 0.38 : 0.26;\n  const defaultBorderRadius = roundness * (isV3 ? 2 : 4);\n  const iconSize = isV3 ? 18 : 16;\n\n  const {\n    backgroundColor: customBackgroundColor,\n    borderRadius = defaultBorderRadius,\n  } = (StyleSheet.flatten(style) || {}) as ViewStyle;\n\n  const {\n    borderColor,\n    textColor,\n    iconColor,\n    rippleColor,\n    selectedBackgroundColor,\n    backgroundColor,\n  } = getChipColors({\n    isOutlined,\n    theme,\n    selectedColor,\n    showSelectedOverlay,\n    customBackgroundColor,\n    disabled,\n    customRippleColor,\n  });\n\n  const accessibilityState: AccessibilityState = {\n    selected,\n    disabled,\n  };\n\n  const elevationStyle = isV3 || Platform.OS === 'android' ? elevation : 0;\n  const multiplier = isV3 ? (compact ? 1.5 : 2) : 1;\n  const labelSpacings = {\n    marginRight: onClose ? 0 : 8 * multiplier,\n    marginLeft:\n      avatar || icon || (selected && showSelectedCheck)\n        ? 4 * multiplier\n        : 8 * multiplier,\n  };\n  const contentSpacings = {\n    paddingRight: isV3 ? (onClose ? 34 : 0) : onClose ? 32 : 4,\n  };\n  const labelTextStyle = {\n    color: textColor,\n    ...(isV3 ? theme.fonts.labelLarge : theme.fonts.regular),\n  };\n  return (\n    <Surface\n      style={[\n        styles.container,\n        isV3 && styles.md3Container,\n        !theme.isV3 && {\n          elevation: elevationStyle,\n        },\n        {\n          backgroundColor: selected ? selectedBackgroundColor : backgroundColor,\n          borderColor,\n          borderRadius,\n        },\n        style,\n      ]}\n      {...(theme.isV3 && { elevation: elevationStyle })}\n      {...rest}\n      testID={`${testID}-container`}\n      theme={theme}\n    >\n      <TouchableRipple\n        borderless\n        background={background}\n        style={[{ borderRadius }, styles.touchable]}\n        onPress={onPress}\n        onLongPress={onLongPress}\n        onPressIn={hasPassedTouchHandler ? handlePressIn : undefined}\n        onPressOut={hasPassedTouchHandler ? handlePressOut : undefined}\n        delayLongPress={delayLongPress}\n        rippleColor={rippleColor}\n        disabled={disabled}\n        accessibilityLabel={accessibilityLabel}\n        accessibilityRole={accessibilityRole}\n        accessibilityState={accessibilityState}\n        testID={testID}\n        theme={theme}\n      >\n        <View\n          style={[styles.content, isV3 && styles.md3Content, contentSpacings]}\n        >\n          {avatar && !icon ? (\n            <View\n              style={[\n                styles.avatarWrapper,\n                isV3 && styles.md3AvatarWrapper,\n                disabled && { opacity },\n              ]}\n            >\n              {React.isValidElement(avatar)\n                ? React.cloneElement(avatar as React.ReactElement<any>, {\n                    style: [styles.avatar, avatar.props.style],\n                  })\n                : avatar}\n            </View>\n          ) : null}\n          {icon || (selected && showSelectedCheck) ? (\n            <View\n              style={[\n                styles.icon,\n                isV3 && styles.md3Icon,\n                avatar\n                  ? [\n                      styles.avatar,\n                      styles.avatarSelected,\n                      isV3 && selected && styles.md3SelectedIcon,\n                    ]\n                  : null,\n              ]}\n            >\n              {icon ? (\n                <Icon\n                  source={icon}\n                  color={\n                    avatar\n                      ? white\n                      : !disabled && theme.isV3\n                      ? theme.colors.primary\n                      : iconColor\n                  }\n                  size={18}\n                  theme={theme}\n                />\n              ) : (\n                <MaterialCommunityIcon\n                  name=\"check\"\n                  color={avatar ? white : iconColor}\n                  size={18}\n                  direction=\"ltr\"\n                />\n              )}\n            </View>\n          ) : null}\n          <Text\n            variant=\"labelLarge\"\n            selectable={false}\n            numberOfLines={1}\n            style={[\n              isV3 ? styles.md3LabelText : styles.labelText,\n              labelTextStyle,\n              labelSpacings,\n              textStyle,\n            ]}\n            ellipsizeMode={ellipsizeMode}\n            maxFontSizeMultiplier={maxFontSizeMultiplier}\n          >\n            {children}\n          </Text>\n        </View>\n      </TouchableRipple>\n      {onClose ? (\n        <View style={styles.closeButtonStyle}>\n          <Pressable\n            onPress={onClose}\n            disabled={disabled}\n            accessibilityRole=\"button\"\n            accessibilityLabel={closeIconAccessibilityLabel}\n          >\n            <View\n              style={[\n                styles.icon,\n                styles.closeIcon,\n                isV3 && styles.md3CloseIcon,\n              ]}\n            >\n              {closeIcon ? (\n                <Icon source={closeIcon} color={iconColor} size={iconSize} />\n              ) : (\n                <MaterialCommunityIcon\n                  name={isV3 ? 'close' : 'close-circle'}\n                  size={iconSize}\n                  color={iconColor}\n                  direction=\"ltr\"\n                />\n              )}\n            </View>\n          </Pressable>\n        </View>\n      ) : null}\n    </Surface>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    borderWidth: StyleSheet.hairlineWidth,\n    borderStyle: 'solid',\n    flexDirection: Platform.select({ default: 'column', web: 'row' }),\n  },\n  md3Container: {\n    borderWidth: 1,\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    paddingLeft: 4,\n    position: 'relative',\n  },\n  md3Content: {\n    paddingLeft: 0,\n  },\n  icon: {\n    padding: 4,\n    alignSelf: 'center',\n  },\n  md3Icon: {\n    paddingLeft: 8,\n    paddingRight: 0,\n  },\n  closeIcon: {\n    marginRight: 4,\n  },\n  md3CloseIcon: {\n    marginRight: 8,\n    padding: 0,\n  },\n  labelText: {\n    minHeight: 24,\n    lineHeight: 24,\n    textAlignVertical: 'center',\n    marginVertical: 4,\n  },\n  md3LabelText: {\n    textAlignVertical: 'center',\n    marginVertical: 6,\n  },\n  avatar: {\n    width: 24,\n    height: 24,\n    borderRadius: 12,\n  },\n  avatarWrapper: {\n    marginRight: 4,\n  },\n  md3AvatarWrapper: {\n    marginLeft: 4,\n    marginRight: 0,\n  },\n  md3SelectedIcon: {\n    paddingLeft: 4,\n  },\n  // eslint-disable-next-line react-native/no-color-literals\n  avatarSelected: {\n    position: 'absolute',\n    top: 4,\n    left: 4,\n    backgroundColor: 'rgba(0, 0, 0, .29)',\n  },\n  closeButtonStyle: {\n    position: 'absolute',\n    right: 0,\n    height: '100%',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  touchable: {\n    width: '100%',\n  },\n});\n\nexport default Chip;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,IAAA;AAgB9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,SAASC,aAAa;AACtB,SAASC,gBAAgB;AACzB,SAASC,KAAK;AAEd,OAAOC,eAAe;AAEtB,OAAOC,IAAI;AACX,OAAOC,qBAAqB;AAC5B,OAAOC,OAAO;AACd,OAAOC,eAAe;AACtB,OAAOC,IAAI;AAkJX,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAGC,IAAA,EA+BA;EAAA,IAAAC,SAAA,GAALD,IAAA,CA9BNE,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,MAAM,GAAAA,SAAA;IACbE,QAAQ,GA6BFH,IAAA,CA7BNG,QAAQ;IACRC,IAAI,GA4BEJ,IAAA,CA5BNI,IAAI;IACJC,MAAM,GA2BAL,IAAA,CA3BNK,MAAM;IAAAC,aAAA,GA2BAN,IAAA,CA1BNO,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,aAAA,GA0BVR,IAAA,CAzBNS,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAChBE,UAAU,GAwBJV,IAAA,CAxBNU,UAAU;IACVC,kBAAkB,GAuBZX,IAAA,CAvBNW,kBAAkB;IAAAC,qBAAA,GAuBZZ,IAAA,CAtBNa,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,cAAG,QAAQ,GAAAA,qBAAA;IAAAE,qBAAA,GAsBtBd,IAAA,CArBNe,2BAA2B;IAA3BA,2BAA2B,GAAAD,qBAAA,cAAG,OAAO,GAAAA,qBAAA;IACrCE,OAAO,GAoBDhB,IAAA,CApBNgB,OAAO;IACPC,WAAW,GAmBLjB,IAAA,CAnBNiB,WAAW;IACXC,UAAU,GAkBJlB,IAAA,CAlBNkB,UAAU;IACVC,SAAS,GAiBHnB,IAAA,CAjBNmB,SAAS;IACTC,cAAc,GAgBRpB,IAAA,CAhBNoB,cAAc;IACdC,OAAO,GAeDrB,IAAA,CAfNqB,OAAO;IACPC,SAAS,GAcHtB,IAAA,CAdNsB,SAAS;IACTC,SAAS,GAaHvB,IAAA,CAbNuB,SAAS;IACTC,KAAK,GAYCxB,IAAA,CAZNwB,KAAK;IACEC,cAAc,GAWfzB,IAAA,CAXN0B,KAAK;IAAAC,WAAA,GAWC3B,IAAA,CAVN4B,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,MAAM,GAAAA,WAAA;IACfE,aAAa,GASP7B,IAAA,CATN6B,aAAa;IACAC,iBAAiB,GAQxB9B,IAAA,CARN+B,WAAW;IAAAC,qBAAA,GAQLhC,IAAA,CAPNiC,mBAAmB;IAAnBA,mBAAmB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IAAAE,qBAAA,GAOrBlC,IAAA,CANNmC,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IACxBE,aAAa,GAKPpC,IAAA,CALNoC,aAAa;IACbC,OAAO,GAIDrC,IAAA,CAJNqC,OAAO;IAAAC,aAAA,GAIDtC,IAAA,CAHNuC,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAChBE,qBAAqB,GAEfxC,IAAA,CAFNwC,qBAAqB;IAClBC,IAAA,GAAAC,wBAAA,CACG1C,IAAA,EAAA2C,SAAA;EACN,IAAMjB,KAAK,GAAGnC,gBAAgB,CAACkC,cAAc,CAAC;EAC9C,IAAQmB,IAAI,GAAgBlB,KAAK,CAAzBkB,IAAI;IAAEC,SAAA,GAAcnB,KAAK,CAAnBmB,SAAA;EAEd,IAAAC,aAAA,GAA+B/D,KAAK,CAACgE,MAAM,CACzC,IAAI/D,QAAQ,CAACgE,KAAK,CAACJ,IAAI,IAAIL,QAAQ,GAAG,CAAC,GAAG,CAAC,CAC7C,CAAC;IAFgBU,SAAA,GAAAH,aAAA,CAATI,OAAO;EAIf,IAAMC,qBAAqB,GAAG1D,eAAe,CAAC;IAC5CuB,OAAO,EAAPA,OAAO;IACPC,WAAW,EAAXA,WAAW;IACXE,SAAS,EAATA,SAAS;IACTD,UAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAMkC,UAAU,GAAGlD,IAAI,KAAK,UAAU;EAEtC,IAAMmD,aAAa,GAAGhE,iBAAiB,CAAE,UAAAiE,CAAwB,EAAK;IACpE,IAAQC,KAAA,GAAU7B,KAAK,CAAC8B,SAAS,CAAzBD,KAAA;IACRpC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGmC,CAAC,CAAC;IACdtE,QAAQ,CAACyE,MAAM,CAACR,SAAS,EAAE;MACzBS,OAAO,EAAEd,IAAI,GAAIL,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAI,CAAC;MACtCoB,QAAQ,EAAE,GAAG,GAAGJ,KAAK;MACrBK,eAAe,EACb3E,QAAQ,CAAC4E,EAAE,KAAK,KAAK,IACrB5E,QAAQ,CAAC6E,SAAS,CAACC,kBAAkB,CAACC,KAAK,IAAI;IACnD,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,CAAC;EAEF,IAAMC,cAAc,GAAG7E,iBAAiB,CAAE,UAAAiE,CAAwB,EAAK;IACrE,IAAQC,KAAA,GAAU7B,KAAK,CAAC8B,SAAS,CAAzBD,KAAA;IACRrC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAGoC,CAAC,CAAC;IACftE,QAAQ,CAACyE,MAAM,CAACR,SAAS,EAAE;MACzBS,OAAO,EAAEd,IAAI,IAAIL,QAAQ,GAAG,CAAC,GAAG,CAAC;MACjCoB,QAAQ,EAAE,GAAG,GAAGJ,KAAK;MACrBK,eAAe,EACb3E,QAAQ,CAAC4E,EAAE,KAAK,KAAK,IACrB5E,QAAQ,CAAC6E,SAAS,CAACC,kBAAkB,CAACC,KAAK,IAAI;IACnD,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,CAAC;EAEF,IAAME,OAAO,GAAGvB,IAAI,GAAG,IAAI,GAAG,IAAI;EAClC,IAAMwB,mBAAmB,GAAGvB,SAAS,IAAID,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EACtD,IAAMyB,QAAQ,GAAGzB,IAAI,GAAG,EAAE,GAAG,EAAE;EAE/B,IAAA0B,KAAA,GAGKpF,UAAU,CAACqF,OAAO,CAAC/C,KAAK,CAAC,IAAI,CAAC,CAAe;IAF/BgD,qBAAqB,GAAAF,KAAA,CAAtCG,eAAe;IAAAC,kBAAA,GAAAJ,KAAA,CACfK,YAAY;IAAZA,YAAY,GAAAD,kBAAA,cAAGN,mBAAA,GAAAM,kBAAA;EAGjB,IAAAE,cAAA,GAOItF,aAAa,CAAC;MAChB8D,UAAU,EAAVA,UAAU;MACV1B,KAAK,EAALA,KAAK;MACLG,aAAa,EAAbA,aAAa;MACbI,mBAAmB,EAAnBA,mBAAmB;MACnBuC,qBAAqB,EAArBA,qBAAqB;MACrB/D,QAAQ,EAARA,QAAQ;MACRqB,iBAAA,EAAAA;IACF,CAAC,CAAC;IAdA+C,WAAW,GAAAD,cAAA,CAAXC,WAAW;IACXC,SAAS,GAAAF,cAAA,CAATE,SAAS;IACTC,SAAS,GAAAH,cAAA,CAATG,SAAS;IACThD,WAAW,GAAA6C,cAAA,CAAX7C,WAAW;IACXiD,uBAAuB,GAAAJ,cAAA,CAAvBI,uBAAuB;IACvBP,eAAA,GAAAG,cAAA,CAAAH,eAAA;EAWF,IAAMQ,kBAAsC,GAAG;IAC7C1E,QAAQ,EAARA,QAAQ;IACRE,QAAA,EAAAA;EACF,CAAC;EAED,IAAMyE,cAAc,GAAGtC,IAAI,IAAI3D,QAAQ,CAAC4E,EAAE,KAAK,SAAS,GAAGZ,SAAS,GAAG,CAAC;EACxE,IAAMkC,UAAU,GAAGvC,IAAI,GAAIP,OAAO,GAAG,GAAG,GAAG,CAAC,GAAI,CAAC;EACjD,IAAM+C,aAAa,GAAG;IACpBC,WAAW,EAAEhE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG8D,UAAU;IACzCG,UAAU,EACRjF,MAAM,IAAID,IAAI,IAAKG,QAAQ,IAAI4B,iBAAkB,GAC7C,CAAC,GAAGgD,UAAU,GACd,CAAC,GAAGA;EACZ,CAAC;EACD,IAAMI,eAAe,GAAG;IACtBC,YAAY,EAAE5C,IAAI,GAAIvB,OAAO,GAAG,EAAE,GAAG,CAAC,GAAIA,OAAO,GAAG,EAAE,GAAG;EAC3D,CAAC;EACD,IAAMoE,cAAc,GAAAC,aAAA;IAClBC,KAAK,EAAEb;EAAS,GACZlC,IAAI,GAAGlB,KAAK,CAACkE,KAAK,CAACC,UAAU,GAAGnE,KAAK,CAACkE,KAAK,CAACE,OAAO,CACxD;EACD,OACE/G,KAAA,CAAAgH,aAAA,CAACnG,OAAO,EAAAoG,QAAA;IACNxE,KAAK,EAAE,CACLyE,MAAM,CAACC,SAAS,EAChBtD,IAAI,IAAIqD,MAAM,CAACE,YAAY,EAC3B,CAACzE,KAAK,CAACkB,IAAI,IAAI;MACbK,SAAS,EAAEiC;IACb,CAAC,EACD;MACET,eAAe,EAAElE,QAAQ,GAAGyE,uBAAuB,GAAGP,eAAe;MACrEI,WAAW,EAAXA,WAAW;MACXF,YAAA,EAAAA;IACF,CAAC,EACDnD,KAAK;EACL,GACGE,KAAK,CAACkB,IAAI,IAAI;IAAEK,SAAS,EAAEiC;EAAe,CAAC,EAC5CzC,IAAI;IACRb,MAAM,EAAG,GAAEA,MAAO,YAAY;IAC9BF,KAAK,EAAEA;EAAM,IAEb3C,KAAA,CAAAgH,aAAA,CAAClG,eAAe;IACduG,UAAU;IACV1F,UAAU,EAAEA,UAAW;IACvBc,KAAK,EAAE,CAAC;MAAEmD,YAAA,EAAAA;IAAa,CAAC,EAAEsB,MAAM,CAACI,SAAS,CAAE;IAC5CrF,OAAO,EAAEA,OAAQ;IACjBC,WAAW,EAAEA,WAAY;IACzBE,SAAS,EAAEgC,qBAAqB,GAAGE,aAAa,GAAGiD,SAAU;IAC7DpF,UAAU,EAAEiC,qBAAqB,GAAGe,cAAc,GAAGoC,SAAU;IAC/DlF,cAAc,EAAEA,cAAe;IAC/BW,WAAW,EAAEA,WAAY;IACzBtB,QAAQ,EAAEA,QAAS;IACnBE,kBAAkB,EAAEA,kBAAmB;IACvCE,iBAAiB,EAAEA,iBAAkB;IACrCoE,kBAAkB,EAAEA,kBAAmB;IACvCrD,MAAM,EAAEA,MAAO;IACfF,KAAK,EAAEA;EAAM,GAEb3C,KAAA,CAAAgH,aAAA,CAAC3G,IAAI;IACHoC,KAAK,EAAE,CAACyE,MAAM,CAACM,OAAO,EAAE3D,IAAI,IAAIqD,MAAM,CAACO,UAAU,EAAEjB,eAAe;EAAE,GAEnElF,MAAM,IAAI,CAACD,IAAI,GACdrB,KAAA,CAAAgH,aAAA,CAAC3G,IAAI;IACHoC,KAAK,EAAE,CACLyE,MAAM,CAACQ,aAAa,EACpB7D,IAAI,IAAIqD,MAAM,CAACS,gBAAgB,EAC/BjG,QAAQ,IAAI;MAAE0D,OAAA,EAAAA;IAAQ,CAAC;EACvB,GAEDpF,KAAK,CAAC4H,cAAc,CAACtG,MAAM,CAAC,GACzBtB,KAAK,CAAC6H,YAAY,CAACvG,MAAM,EAA6B;IACpDmB,KAAK,EAAE,CAACyE,MAAM,CAAC5F,MAAM,EAAEA,MAAM,CAACwG,KAAK,CAACrF,KAAK;EAC3C,CAAC,CAAC,GACFnB,MACA,CAAC,GACL,IAAI,EACPD,IAAI,IAAKG,QAAQ,IAAI4B,iBAAkB,GACtCpD,KAAA,CAAAgH,aAAA,CAAC3G,IAAI;IACHoC,KAAK,EAAE,CACLyE,MAAM,CAAC7F,IAAI,EACXwC,IAAI,IAAIqD,MAAM,CAACa,OAAO,EACtBzG,MAAM,GACF,CACE4F,MAAM,CAAC5F,MAAM,EACb4F,MAAM,CAACc,cAAc,EACrBnE,IAAI,IAAIrC,QAAQ,IAAI0F,MAAM,CAACe,eAAe,CAC3C,GACD,IAAI;EACR,GAED5G,IAAI,GACHrB,KAAA,CAAAgH,aAAA,CAACrG,IAAI;IACHuH,MAAM,EAAE7G,IAAK;IACbuF,KAAK,EACHtF,MAAM,GACFb,KAAK,GACL,CAACiB,QAAQ,IAAIiB,KAAK,CAACkB,IAAI,GACvBlB,KAAK,CAACwF,MAAM,CAACC,OAAO,GACpBpC,SACL;IACDqC,IAAI,EAAE,EAAG;IACT1F,KAAK,EAAEA;EAAM,CACd,CAAC,GAEF3C,KAAA,CAAAgH,aAAA,CAACpG,qBAAqB;IACpB0H,IAAI,EAAC,OAAO;IACZ1B,KAAK,EAAEtF,MAAM,GAAGb,KAAK,GAAGuF,SAAU;IAClCqC,IAAI,EAAE,EAAG;IACTE,SAAS,EAAC;EAAK,CAChB,CAEC,CAAC,GACL,IAAI,EACRvI,KAAA,CAAAgH,aAAA,CAACjG,IAAI;IACHyH,OAAO,EAAC,YAAY;IACpBC,UAAU,EAAE,KAAM;IAClBC,aAAa,EAAE,CAAE;IACjBjG,KAAK,EAAE,CACLoB,IAAI,GAAGqD,MAAM,CAACyB,YAAY,GAAGzB,MAAM,CAAC0B,SAAS,EAC7ClC,cAAc,EACdL,aAAa,EACb7D,SAAS,CACT;IACFa,aAAa,EAAEA,aAAc;IAC7BI,qBAAqB,EAAEA;EAAsB,GAE5CrC,QACG,CACF,CACS,CAAC,EACjBkB,OAAO,GACNtC,KAAA,CAAAgH,aAAA,CAAC3G,IAAI;IAACoC,KAAK,EAAEyE,MAAM,CAAC2B;EAAiB,GACnC7I,KAAA,CAAAgH,aAAA,CAAC5G,SAAS;IACR6B,OAAO,EAAEK,OAAQ;IACjBZ,QAAQ,EAAEA,QAAS;IACnBI,iBAAiB,EAAC,QAAQ;IAC1BF,kBAAkB,EAAEI;EAA4B,GAEhDhC,KAAA,CAAAgH,aAAA,CAAC3G,IAAI;IACHoC,KAAK,EAAE,CACLyE,MAAM,CAAC7F,IAAI,EACX6F,MAAM,CAAC3E,SAAS,EAChBsB,IAAI,IAAIqD,MAAM,CAAC4B,YAAY;EAC3B,GAEDvG,SAAS,GACRvC,KAAA,CAAAgH,aAAA,CAACrG,IAAI;IAACuH,MAAM,EAAE3F,SAAU;IAACqE,KAAK,EAAEZ,SAAU;IAACqC,IAAI,EAAE/C;EAAS,CAAE,CAAC,GAE7DtF,KAAA,CAAAgH,aAAA,CAACpG,qBAAqB;IACpB0H,IAAI,EAAEzE,IAAI,GAAG,OAAO,GAAG,cAAe;IACtCwE,IAAI,EAAE/C,QAAS;IACfsB,KAAK,EAAEZ,SAAU;IACjBuC,SAAS,EAAC;EAAK,CAChB,CAEC,CACG,CACP,CAAC,GACL,IACG,CAAC;AAEd,CAAC;AAED,IAAMrB,MAAM,GAAG/G,UAAU,CAAC4I,MAAM,CAAC;EAC/B5B,SAAS,EAAE;IACT6B,WAAW,EAAE7I,UAAU,CAAC8I,aAAa;IACrCC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAEjJ,QAAQ,CAACkJ,MAAM,CAAC;MAAEC,OAAO,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAM,CAAC;EAClE,CAAC;EACDlC,YAAY,EAAE;IACZ4B,WAAW,EAAE;EACf,CAAC;EACDxB,OAAO,EAAE;IACP2B,aAAa,EAAE,KAAK;IACpBI,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDhC,UAAU,EAAE;IACV+B,WAAW,EAAE;EACf,CAAC;EACDnI,IAAI,EAAE;IACJqI,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACb,CAAC;EACD5B,OAAO,EAAE;IACPyB,WAAW,EAAE,CAAC;IACd/C,YAAY,EAAE;EAChB,CAAC;EACDlE,SAAS,EAAE;IACT+D,WAAW,EAAE;EACf,CAAC;EACDwC,YAAY,EAAE;IACZxC,WAAW,EAAE,CAAC;IACdoD,OAAO,EAAE;EACX,CAAC;EACDd,SAAS,EAAE;IACTgB,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,QAAQ;IAC3BC,cAAc,EAAE;EAClB,CAAC;EACDpB,YAAY,EAAE;IACZmB,iBAAiB,EAAE,QAAQ;IAC3BC,cAAc,EAAE;EAClB,CAAC;EACDzI,MAAM,EAAE;IACN0I,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVrE,YAAY,EAAE;EAChB,CAAC;EACD8B,aAAa,EAAE;IACbpB,WAAW,EAAE;EACf,CAAC;EACDqB,gBAAgB,EAAE;IAChBpB,UAAU,EAAE,CAAC;IACbD,WAAW,EAAE;EACf,CAAC;EACD2B,eAAe,EAAE;IACfuB,WAAW,EAAE;EACf,CAAC;EAEDxB,cAAc,EAAE;IACdyB,QAAQ,EAAE,UAAU;IACpBS,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPzE,eAAe,EAAE;EACnB,CAAC;EACDmD,gBAAgB,EAAE;IAChBY,QAAQ,EAAE,UAAU;IACpBW,KAAK,EAAE,CAAC;IACRH,MAAM,EAAE,MAAM;IACdI,cAAc,EAAE,QAAQ;IACxBd,UAAU,EAAE;EACd,CAAC;EACDjC,SAAS,EAAE;IACT0C,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAehJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}