{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useMemo, useRef } from 'react';\nimport pick from \"../../modules/pick\";\nimport useMergeRefs from \"../../modules/useMergeRefs\";\nimport usePressEvents from \"../../modules/usePressEvents\";\nimport { warnOnce } from \"../../modules/warnOnce\";\nvar forwardPropsList = {\n  accessibilityDisabled: true,\n  accessibilityLabel: true,\n  accessibilityLiveRegion: true,\n  accessibilityRole: true,\n  accessibilityState: true,\n  accessibilityValue: true,\n  children: true,\n  disabled: true,\n  focusable: true,\n  nativeID: true,\n  onBlur: true,\n  onFocus: true,\n  onLayout: true,\n  testID: true\n};\nvar pickProps = function pickProps(props) {\n  return pick(props, forwardPropsList);\n};\nfunction TouchableWithoutFeedback(props, forwardedRef) {\n  warnOnce('TouchableWithoutFeedback', 'TouchableWithoutFeedback is deprecated. Please use Pressable.');\n  var delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    rejectResponderTermination = props.rejectResponderTermination;\n  var hostRef = useRef(null);\n  var pressConfig = useMemo(function () {\n    return {\n      cancelable: !rejectResponderTermination,\n      disabled: disabled,\n      delayLongPress: delayLongPress,\n      delayPressStart: delayPressIn,\n      delayPressEnd: delayPressOut,\n      onLongPress: onLongPress,\n      onPress: onPress,\n      onPressStart: onPressIn,\n      onPressEnd: onPressOut\n    };\n  }, [disabled, delayPressIn, delayPressOut, delayLongPress, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination]);\n  var pressEventHandlers = usePressEvents(hostRef, pressConfig);\n  var element = React.Children.only(props.children);\n  var children = [element.props.children];\n  var supportedProps = pickProps(props);\n  supportedProps.accessibilityDisabled = disabled;\n  supportedProps.focusable = !disabled && focusable !== false;\n  supportedProps.ref = useMergeRefs(forwardedRef, hostRef, element.ref);\n  var elementProps = Object.assign(supportedProps, pressEventHandlers);\n  return React.cloneElement.apply(React, [element, elementProps].concat(children));\n}\nvar MemoedTouchableWithoutFeedback = React.memo(React.forwardRef(TouchableWithoutFeedback));\nMemoedTouchableWithoutFeedback.displayName = 'TouchableWithoutFeedback';\nexport default MemoedTouchableWithoutFeedback;", "map": {"version": 3, "names": ["React", "useMemo", "useRef", "pick", "useMergeRefs", "usePressEvents", "warnOnce", "forwardPropsList", "accessibilityDisabled", "accessibilityLabel", "accessibilityLiveRegion", "accessibilityRole", "accessibilityState", "accessibilityValue", "children", "disabled", "focusable", "nativeID", "onBlur", "onFocus", "onLayout", "testID", "pickProps", "props", "TouchableWithoutFeedback", "forwardedRef", "delayPressIn", "delayPressOut", "delayLongPress", "onLongPress", "onPress", "onPressIn", "onPressOut", "rejectResponderTermination", "hostRef", "pressConfig", "cancelable", "delayPressStart", "delayPressEnd", "onPressStart", "onPressEnd", "pressEventHandlers", "element", "Children", "only", "supportedProps", "ref", "elementProps", "Object", "assign", "cloneElement", "apply", "concat", "MemoedTouchableWithoutFeedback", "memo", "forwardRef", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/TouchableWithoutFeedback/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use client';\n\nimport * as React from 'react';\nimport { useMemo, useRef } from 'react';\nimport pick from '../../modules/pick';\nimport useMergeRefs from '../../modules/useMergeRefs';\nimport usePressEvents from '../../modules/usePressEvents';\nimport { warnOnce } from '../../modules/warnOnce';\nvar forwardPropsList = {\n  accessibilityDisabled: true,\n  accessibilityLabel: true,\n  accessibilityLiveRegion: true,\n  accessibilityRole: true,\n  accessibilityState: true,\n  accessibilityValue: true,\n  children: true,\n  disabled: true,\n  focusable: true,\n  nativeID: true,\n  onBlur: true,\n  onFocus: true,\n  onLayout: true,\n  testID: true\n};\nvar pickProps = props => pick(props, forwardPropsList);\nfunction TouchableWithoutFeedback(props, forwardedRef) {\n  warnOnce('TouchableWithoutFeedback', 'TouchableWithoutFeedback is deprecated. Please use Pressable.');\n  var delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    rejectResponderTermination = props.rejectResponderTermination;\n  var hostRef = useRef(null);\n  var pressConfig = useMemo(() => ({\n    cancelable: !rejectResponderTermination,\n    disabled,\n    delayLongPress,\n    delayPressStart: delayPressIn,\n    delayPressEnd: delayPressOut,\n    onLongPress,\n    onPress,\n    onPressStart: onPressIn,\n    onPressEnd: onPressOut\n  }), [disabled, delayPressIn, delayPressOut, delayLongPress, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination]);\n  var pressEventHandlers = usePressEvents(hostRef, pressConfig);\n  var element = React.Children.only(props.children);\n  var children = [element.props.children];\n  var supportedProps = pickProps(props);\n  supportedProps.accessibilityDisabled = disabled;\n  supportedProps.focusable = !disabled && focusable !== false;\n  supportedProps.ref = useMergeRefs(forwardedRef, hostRef, element.ref);\n  var elementProps = Object.assign(supportedProps, pressEventHandlers);\n  return /*#__PURE__*/React.cloneElement(element, elementProps, ...children);\n}\nvar MemoedTouchableWithoutFeedback = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(TouchableWithoutFeedback));\nMemoedTouchableWithoutFeedback.displayName = 'TouchableWithoutFeedback';\nexport default MemoedTouchableWithoutFeedback;"], "mappings": "AAUA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,OAAOC,IAAI;AACX,OAAOC,YAAY;AACnB,OAAOC,cAAc;AACrB,SAASC,QAAQ;AACjB,IAAIC,gBAAgB,GAAG;EACrBC,qBAAqB,EAAE,IAAI;EAC3BC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE,IAAI;EAC7BC,iBAAiB,EAAE,IAAI;EACvBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,IAAI;EACxBC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAGC,KAAK;EAAA,OAAIpB,IAAI,CAACoB,KAAK,EAAEhB,gBAAgB,CAAC;AAAA;AACtD,SAASiB,wBAAwBA,CAACD,KAAK,EAAEE,YAAY,EAAE;EACrDnB,QAAQ,CAAC,0BAA0B,EAAE,+DAA+D,CAAC;EACrG,IAAIoB,YAAY,GAAGH,KAAK,CAACG,YAAY;IACnCC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCb,QAAQ,GAAGQ,KAAK,CAACR,QAAQ;IACzBC,SAAS,GAAGO,KAAK,CAACP,SAAS;IAC3Ba,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,UAAU,GAAGT,KAAK,CAACS,UAAU;IAC7BC,0BAA0B,GAAGV,KAAK,CAACU,0BAA0B;EAC/D,IAAIC,OAAO,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAIiC,WAAW,GAAGlC,OAAO,CAAC;IAAA,OAAO;MAC/BmC,UAAU,EAAE,CAACH,0BAA0B;MACvClB,QAAQ,EAARA,QAAQ;MACRa,cAAc,EAAdA,cAAc;MACdS,eAAe,EAAEX,YAAY;MAC7BY,aAAa,EAAEX,aAAa;MAC5BE,WAAW,EAAXA,WAAW;MACXC,OAAO,EAAPA,OAAO;MACPS,YAAY,EAAER,SAAS;MACvBS,UAAU,EAAER;IACd,CAAC;EAAA,CAAC,EAAE,CAACjB,QAAQ,EAAEW,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEC,0BAA0B,CAAC,CAAC;EACrI,IAAIQ,kBAAkB,GAAGpC,cAAc,CAAC6B,OAAO,EAAEC,WAAW,CAAC;EAC7D,IAAIO,OAAO,GAAG1C,KAAK,CAAC2C,QAAQ,CAACC,IAAI,CAACrB,KAAK,CAACT,QAAQ,CAAC;EACjD,IAAIA,QAAQ,GAAG,CAAC4B,OAAO,CAACnB,KAAK,CAACT,QAAQ,CAAC;EACvC,IAAI+B,cAAc,GAAGvB,SAAS,CAACC,KAAK,CAAC;EACrCsB,cAAc,CAACrC,qBAAqB,GAAGO,QAAQ;EAC/C8B,cAAc,CAAC7B,SAAS,GAAG,CAACD,QAAQ,IAAIC,SAAS,KAAK,KAAK;EAC3D6B,cAAc,CAACC,GAAG,GAAG1C,YAAY,CAACqB,YAAY,EAAES,OAAO,EAAEQ,OAAO,CAACI,GAAG,CAAC;EACrE,IAAIC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACJ,cAAc,EAAEJ,kBAAkB,CAAC;EACpE,OAAoBzC,KAAK,CAACkD,YAAY,CAAAC,KAAA,CAAlBnD,KAAK,GAAc0C,OAAO,EAAEK,YAAY,EAAAK,MAAA,CAAKtC,QAAQ,EAAC;AAC5E;AACA,IAAIuC,8BAA8B,GAAgBrD,KAAK,CAACsD,IAAI,CAActD,KAAK,CAACuD,UAAU,CAAC/B,wBAAwB,CAAC,CAAC;AACrH6B,8BAA8B,CAACG,WAAW,GAAG,0BAA0B;AACvE,eAAeH,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}