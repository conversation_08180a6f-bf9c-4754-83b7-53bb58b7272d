{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { getContentMaxWidth, getMenuItemColor, MAX_WIDTH, MIN_WIDTH } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Icon from \"../Icon\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar MenuItem = function MenuItem(_ref) {\n  var leadingIcon = _ref.leadingIcon,\n    trailingIcon = _ref.trailingIcon,\n    dense = _ref.dense,\n    title = _ref.title,\n    disabled = _ref.disabled,\n    background = _ref.background,\n    onPress = _ref.onPress,\n    style = _ref.style,\n    containerStyle = _ref.containerStyle,\n    contentStyle = _ref.contentStyle,\n    titleStyle = _ref.titleStyle,\n    customRippleColor = _ref.rippleColor,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'menu-item' : _ref$testID,\n    accessibilityLabel = _ref.accessibilityLabel,\n    accessibilityState = _ref.accessibilityState,\n    themeOverrides = _ref.theme,\n    _ref$titleMaxFontSize = _ref.titleMaxFontSizeMultiplier,\n    titleMaxFontSizeMultiplier = _ref$titleMaxFontSize === void 0 ? 1.5 : _ref$titleMaxFontSize;\n  var theme = useInternalTheme(themeOverrides);\n  var _getMenuItemColor = getMenuItemColor({\n      theme: theme,\n      disabled: disabled,\n      customRippleColor: customRippleColor\n    }),\n    titleColor = _getMenuItemColor.titleColor,\n    iconColor = _getMenuItemColor.iconColor,\n    rippleColor = _getMenuItemColor.rippleColor;\n  var isV3 = theme.isV3;\n  var containerPadding = isV3 ? 12 : 8;\n  var iconWidth = isV3 ? 24 : 40;\n  var minWidth = MIN_WIDTH - (isV3 ? 12 : 16);\n  var maxWidth = getContentMaxWidth({\n    isV3: isV3,\n    iconWidth: iconWidth,\n    leadingIcon: leadingIcon,\n    trailingIcon: trailingIcon\n  });\n  var titleTextStyle = _objectSpread({\n    color: titleColor\n  }, isV3 ? theme.fonts.bodyLarge : {});\n  var newAccessibilityState = _objectSpread(_objectSpread({}, accessibilityState), {}, {\n    disabled: disabled\n  });\n  return React.createElement(TouchableRipple, {\n    style: [styles.container, {\n      paddingHorizontal: containerPadding\n    }, dense && styles.md3DenseContainer, style],\n    onPress: onPress,\n    disabled: disabled,\n    testID: testID,\n    background: background,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"menuitem\",\n    accessibilityState: newAccessibilityState,\n    rippleColor: rippleColor\n  }, React.createElement(View, {\n    style: [styles.row, containerStyle]\n  }, leadingIcon ? React.createElement(View, {\n    style: [!isV3 && styles.item, {\n      width: iconWidth\n    }],\n    pointerEvents: \"box-none\"\n  }, React.createElement(Icon, {\n    source: leadingIcon,\n    size: 24,\n    color: iconColor\n  })) : null, React.createElement(View, {\n    style: [!isV3 && styles.item, styles.content, {\n      minWidth: minWidth,\n      maxWidth: maxWidth\n    }, isV3 && (leadingIcon ? styles.md3LeadingIcon : styles.md3WithoutLeadingIcon), contentStyle],\n    pointerEvents: \"none\"\n  }, React.createElement(Text, {\n    variant: \"bodyLarge\",\n    selectable: false,\n    numberOfLines: 1,\n    testID: `${testID}-title`,\n    style: [!isV3 && styles.title, titleTextStyle, titleStyle],\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }, title)), isV3 && trailingIcon ? React.createElement(View, {\n    style: [!isV3 && styles.item, {\n      width: iconWidth\n    }],\n    pointerEvents: \"box-none\"\n  }, React.createElement(Icon, {\n    source: trailingIcon,\n    size: 24,\n    color: iconColor\n  })) : null));\n};\nMenuItem.displayName = 'Menu.Item';\nvar styles = StyleSheet.create({\n  container: {\n    minWidth: MIN_WIDTH,\n    maxWidth: MAX_WIDTH,\n    height: 48,\n    justifyContent: 'center'\n  },\n  md3DenseContainer: {\n    height: 32\n  },\n  row: {\n    flexDirection: 'row'\n  },\n  title: {\n    fontSize: 16\n  },\n  item: {\n    marginHorizontal: 8\n  },\n  content: {\n    justifyContent: 'center'\n  },\n  md3LeadingIcon: {\n    marginLeft: 12\n  },\n  md3WithoutLeadingIcon: {\n    marginLeft: 4\n  }\n});\nexport default MenuItem;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "getContentMaxWidth", "getMenuItemColor", "MAX_WIDTH", "MIN_WIDTH", "useInternalTheme", "Icon", "TouchableRipple", "Text", "MenuItem", "_ref", "leadingIcon", "trailingIcon", "dense", "title", "disabled", "background", "onPress", "style", "containerStyle", "contentStyle", "titleStyle", "customRippleColor", "rippleColor", "_ref$testID", "testID", "accessibilityLabel", "accessibilityState", "themeOverrides", "theme", "_ref$titleMaxFontSize", "titleMaxFontSizeMultiplier", "_getMenuItemColor", "titleColor", "iconColor", "isV3", "containerPadding", "iconWidth", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "titleTextStyle", "_objectSpread", "color", "fonts", "bodyLarge", "newAccessibilityState", "createElement", "styles", "container", "paddingHorizontal", "md3DenseContainer", "accessibilityRole", "row", "item", "width", "pointerEvents", "source", "size", "content", "md3LeadingIcon", "md3WithoutLeadingIcon", "variant", "selectable", "numberOfLines", "maxFontSizeMultiplier", "displayName", "create", "height", "justifyContent", "flexDirection", "fontSize", "marginHorizontal", "marginLeft"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Menu/MenuItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  AccessibilityState,\n  ColorValue,\n  GestureResponderEvent,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport {\n  getContentMaxWidth,\n  getMenuItemColor,\n  MAX_WIDTH,\n  MIN_WIDTH,\n} from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport Icon, { IconSource } from '../Icon';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Title text for the `MenuItem`.\n   */\n  title: React.ReactNode;\n  /**\n   * @renamed Renamed from 'icon' to 'leadingIcon' in v5.x\n   *\n   * Leading icon to display for the `MenuItem`.\n   */\n  leadingIcon?: IconSource;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Trailing icon to display for the `MenuItem`.\n   */\n  trailingIcon?: IconSource;\n  /**\n   * Whether the 'item' is disabled. A disabled 'item' is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Sets min height with densed layout.\n   */\n  dense?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  /**\n   * Style that is passed to the root TouchableRipple container.\n   * @optional\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the outermost container that wraps the entire content, including leading and trailing icons and title text.\n   */\n  containerStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the content container, which wraps the title text.\n   */\n  contentStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the Title element.\n   */\n  titleStyle?: StyleProp<TextStyle>;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  /**\n   * Accessibility label for the Touchable. This is read by the screen reader when the user taps the component.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Accessibility state for the Touchable. This is read by the screen reader when the user taps the component.\n   */\n  accessibilityState?: AccessibilityState;\n};\n\n/**\n * A component to show a single list item inside a Menu.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Menu } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <View style={{ flex: 1 }}>\n *     <Menu.Item leadingIcon=\"redo\" onPress={() => {}} title=\"Redo\" />\n *     <Menu.Item leadingIcon=\"undo\" onPress={() => {}} title=\"Undo\" />\n *     <Menu.Item leadingIcon=\"content-cut\" onPress={() => {}} title=\"Cut\" disabled />\n *     <Menu.Item leadingIcon=\"content-copy\" onPress={() => {}} title=\"Copy\" disabled />\n *     <Menu.Item leadingIcon=\"content-paste\" onPress={() => {}} title=\"Paste\" />\n *   </View>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst MenuItem = ({\n  leadingIcon,\n  trailingIcon,\n  dense,\n  title,\n  disabled,\n  background,\n  onPress,\n  style,\n  containerStyle,\n  contentStyle,\n  titleStyle,\n  rippleColor: customRippleColor,\n  testID = 'menu-item',\n  accessibilityLabel,\n  accessibilityState,\n  theme: themeOverrides,\n  titleMaxFontSizeMultiplier = 1.5,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { titleColor, iconColor, rippleColor } = getMenuItemColor({\n    theme,\n    disabled,\n    customRippleColor,\n  });\n  const { isV3 } = theme;\n\n  const containerPadding = isV3 ? 12 : 8;\n\n  const iconWidth = isV3 ? 24 : 40;\n\n  const minWidth = MIN_WIDTH - (isV3 ? 12 : 16);\n\n  const maxWidth = getContentMaxWidth({\n    isV3,\n    iconWidth,\n    leadingIcon,\n    trailingIcon,\n  });\n\n  const titleTextStyle = {\n    color: titleColor,\n    ...(isV3 ? theme.fonts.bodyLarge : {}),\n  };\n\n  const newAccessibilityState = { ...accessibilityState, disabled };\n\n  return (\n    <TouchableRipple\n      style={[\n        styles.container,\n        { paddingHorizontal: containerPadding },\n        dense && styles.md3DenseContainer,\n        style,\n      ]}\n      onPress={onPress}\n      disabled={disabled}\n      testID={testID}\n      background={background}\n      accessibilityLabel={accessibilityLabel}\n      accessibilityRole=\"menuitem\"\n      accessibilityState={newAccessibilityState}\n      rippleColor={rippleColor}\n    >\n      <View style={[styles.row, containerStyle]}>\n        {leadingIcon ? (\n          <View\n            style={[!isV3 && styles.item, { width: iconWidth }]}\n            pointerEvents=\"box-none\"\n          >\n            <Icon source={leadingIcon} size={24} color={iconColor} />\n          </View>\n        ) : null}\n        <View\n          style={[\n            !isV3 && styles.item,\n            styles.content,\n            { minWidth, maxWidth },\n            isV3 &&\n              (leadingIcon\n                ? styles.md3LeadingIcon\n                : styles.md3WithoutLeadingIcon),\n            contentStyle,\n          ]}\n          pointerEvents=\"none\"\n        >\n          <Text\n            variant=\"bodyLarge\"\n            selectable={false}\n            numberOfLines={1}\n            testID={`${testID}-title`}\n            style={[!isV3 && styles.title, titleTextStyle, titleStyle]}\n            maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n          >\n            {title}\n          </Text>\n        </View>\n        {isV3 && trailingIcon ? (\n          <View\n            style={[!isV3 && styles.item, { width: iconWidth }]}\n            pointerEvents=\"box-none\"\n          >\n            <Icon source={trailingIcon} size={24} color={iconColor} />\n          </View>\n        ) : null}\n      </View>\n    </TouchableRipple>\n  );\n};\n\nMenuItem.displayName = 'Menu.Item';\n\nconst styles = StyleSheet.create({\n  container: {\n    minWidth: MIN_WIDTH,\n    maxWidth: MAX_WIDTH,\n    height: 48,\n    justifyContent: 'center',\n  },\n  md3DenseContainer: {\n    height: 32,\n  },\n  row: {\n    flexDirection: 'row',\n  },\n  title: {\n    fontSize: 16,\n  },\n  item: {\n    marginHorizontal: 8,\n  },\n  content: {\n    justifyContent: 'center',\n  },\n  md3LeadingIcon: {\n    marginLeft: 12,\n  },\n  md3WithoutLeadingIcon: {\n    marginLeft: 4,\n  },\n});\n\nexport default MenuItem;\n"], "mappings": ";;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAa9B,SACEC,kBAAkB,EAClBC,gBAAgB,EAChBC,SAAS,EACTC,SAAS;AAEX,SAASC,gBAAgB;AAEzB,OAAOC,IAAI;AACX,OAAOC,eAAe;AACtB,OAAOC,IAAI;AAuGX,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,IAAA,EAkBJ;EAAA,IAjBXC,WAAW,GAiBLD,IAAA,CAjBNC,WAAW;IACXC,YAAY,GAgBNF,IAAA,CAhBNE,YAAY;IACZC,KAAK,GAeCH,IAAA,CAfNG,KAAK;IACLC,KAAK,GAcCJ,IAAA,CAdNI,KAAK;IACLC,QAAQ,GAaFL,IAAA,CAbNK,QAAQ;IACRC,UAAU,GAYJN,IAAA,CAZNM,UAAU;IACVC,OAAO,GAWDP,IAAA,CAXNO,OAAO;IACPC,KAAK,GAUCR,IAAA,CAVNQ,KAAK;IACLC,cAAc,GASRT,IAAA,CATNS,cAAc;IACdC,YAAY,GAQNV,IAAA,CARNU,YAAY;IACZC,UAAU,GAOJX,IAAA,CAPNW,UAAU;IACGC,iBAAiB,GAMxBZ,IAAA,CANNa,WAAW;IAAAC,WAAA,GAMLd,IAAA,CALNe,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,WAAW,GAAAA,WAAA;IACpBE,kBAAkB,GAIZhB,IAAA,CAJNgB,kBAAkB;IAClBC,kBAAkB,GAGZjB,IAAA,CAHNiB,kBAAkB;IACXC,cAAc,GAEflB,IAAA,CAFNmB,KAAK;IAAAC,qBAAA,GAECpB,IAAA,CADNqB,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAG,MAAAA,qBAAA;EAE7B,IAAMD,KAAK,GAAGxB,gBAAgB,CAACuB,cAAc,CAAC;EAC9C,IAAAI,iBAAA,GAA+C9B,gBAAgB,CAAC;MAC9D2B,KAAK,EAALA,KAAK;MACLd,QAAQ,EAARA,QAAQ;MACRO,iBAAA,EAAAA;IACF,CAAC,CAAC;IAJMW,UAAU,GAAAD,iBAAA,CAAVC,UAAU;IAAEC,SAAS,GAAAF,iBAAA,CAATE,SAAS;IAAEX,WAAA,GAAAS,iBAAA,CAAAT,WAAA;EAK/B,IAAQY,IAAA,GAASN,KAAK,CAAdM,IAAA;EAER,IAAMC,gBAAgB,GAAGD,IAAI,GAAG,EAAE,GAAG,CAAC;EAEtC,IAAME,SAAS,GAAGF,IAAI,GAAG,EAAE,GAAG,EAAE;EAEhC,IAAMG,QAAQ,GAAGlC,SAAS,IAAI+B,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;EAE7C,IAAMI,QAAQ,GAAGtC,kBAAkB,CAAC;IAClCkC,IAAI,EAAJA,IAAI;IACJE,SAAS,EAATA,SAAS;IACT1B,WAAW,EAAXA,WAAW;IACXC,YAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAM4B,cAAc,GAAAC,aAAA;IAClBC,KAAK,EAAET;EAAU,GACbE,IAAI,GAAGN,KAAK,CAACc,KAAK,CAACC,SAAS,GAAG,CAAC,CAAC,CACtC;EAED,IAAMC,qBAAqB,GAAAJ,aAAA,CAAAA,aAAA,KAAQd,kBAAkB;IAAEZ,QAAA,EAAAA;EAAA,EAAU;EAEjE,OACEjB,KAAA,CAAAgD,aAAA,CAACvC,eAAe;IACdW,KAAK,EAAE,CACL6B,MAAM,CAACC,SAAS,EAChB;MAAEC,iBAAiB,EAAEb;IAAiB,CAAC,EACvCvB,KAAK,IAAIkC,MAAM,CAACG,iBAAiB,EACjChC,KAAK,CACL;IACFD,OAAO,EAAEA,OAAQ;IACjBF,QAAQ,EAAEA,QAAS;IACnBU,MAAM,EAAEA,MAAO;IACfT,UAAU,EAAEA,UAAW;IACvBU,kBAAkB,EAAEA,kBAAmB;IACvCyB,iBAAiB,EAAC,UAAU;IAC5BxB,kBAAkB,EAAEkB,qBAAsB;IAC1CtB,WAAW,EAAEA;EAAY,GAEzBzB,KAAA,CAAAgD,aAAA,CAAC9C,IAAI;IAACkB,KAAK,EAAE,CAAC6B,MAAM,CAACK,GAAG,EAAEjC,cAAc;EAAE,GACvCR,WAAW,GACVb,KAAA,CAAAgD,aAAA,CAAC9C,IAAI;IACHkB,KAAK,EAAE,CAAC,CAACiB,IAAI,IAAIY,MAAM,CAACM,IAAI,EAAE;MAAEC,KAAK,EAAEjB;IAAU,CAAC,CAAE;IACpDkB,aAAa,EAAC;EAAU,GAExBzD,KAAA,CAAAgD,aAAA,CAACxC,IAAI;IAACkD,MAAM,EAAE7C,WAAY;IAAC8C,IAAI,EAAE,EAAG;IAACf,KAAK,EAAER;EAAU,CAAE,CACpD,CAAC,GACL,IAAI,EACRpC,KAAA,CAAAgD,aAAA,CAAC9C,IAAI;IACHkB,KAAK,EAAE,CACL,CAACiB,IAAI,IAAIY,MAAM,CAACM,IAAI,EACpBN,MAAM,CAACW,OAAO,EACd;MAAEpB,QAAQ,EAARA,QAAQ;MAAEC,QAAA,EAAAA;IAAS,CAAC,EACtBJ,IAAI,KACDxB,WAAW,GACRoC,MAAM,CAACY,cAAc,GACrBZ,MAAM,CAACa,qBAAqB,CAAC,EACnCxC,YAAY,CACZ;IACFmC,aAAa,EAAC;EAAM,GAEpBzD,KAAA,CAAAgD,aAAA,CAACtC,IAAI;IACHqD,OAAO,EAAC,WAAW;IACnBC,UAAU,EAAE,KAAM;IAClBC,aAAa,EAAE,CAAE;IACjBtC,MAAM,EAAG,GAAEA,MAAO,QAAQ;IAC1BP,KAAK,EAAE,CAAC,CAACiB,IAAI,IAAIY,MAAM,CAACjC,KAAK,EAAE0B,cAAc,EAAEnB,UAAU,CAAE;IAC3D2C,qBAAqB,EAAEjC;EAA2B,GAEjDjB,KACG,CACF,CAAC,EACNqB,IAAI,IAAIvB,YAAY,GACnBd,KAAA,CAAAgD,aAAA,CAAC9C,IAAI;IACHkB,KAAK,EAAE,CAAC,CAACiB,IAAI,IAAIY,MAAM,CAACM,IAAI,EAAE;MAAEC,KAAK,EAAEjB;IAAU,CAAC,CAAE;IACpDkB,aAAa,EAAC;EAAU,GAExBzD,KAAA,CAAAgD,aAAA,CAACxC,IAAI;IAACkD,MAAM,EAAE5C,YAAa;IAAC6C,IAAI,EAAE,EAAG;IAACf,KAAK,EAAER;EAAU,CAAE,CACrD,CAAC,GACL,IACA,CACS,CAAC;AAEtB,CAAC;AAEDzB,QAAQ,CAACwD,WAAW,GAAG,WAAW;AAElC,IAAMlB,MAAM,GAAGhD,UAAU,CAACmE,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTV,QAAQ,EAAElC,SAAS;IACnBmC,QAAQ,EAAEpC,SAAS;IACnBgE,MAAM,EAAE,EAAE;IACVC,cAAc,EAAE;EAClB,CAAC;EACDlB,iBAAiB,EAAE;IACjBiB,MAAM,EAAE;EACV,CAAC;EACDf,GAAG,EAAE;IACHiB,aAAa,EAAE;EACjB,CAAC;EACDvD,KAAK,EAAE;IACLwD,QAAQ,EAAE;EACZ,CAAC;EACDjB,IAAI,EAAE;IACJkB,gBAAgB,EAAE;EACpB,CAAC;EACDb,OAAO,EAAE;IACPU,cAAc,EAAE;EAClB,CAAC;EACDT,cAAc,EAAE;IACda,UAAU,EAAE;EACd,CAAC;EACDZ,qBAAqB,EAAE;IACrBY,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAe/D,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}