{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/AntDesign.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/AntDesign.json\";\nexport default createIconSet(glyphMap, 'anticon', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/src/AntDesign.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/AntDesign.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/AntDesign.json';\n\nexport default createIconSet(glyphMap, 'anticon', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,SAAS,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}