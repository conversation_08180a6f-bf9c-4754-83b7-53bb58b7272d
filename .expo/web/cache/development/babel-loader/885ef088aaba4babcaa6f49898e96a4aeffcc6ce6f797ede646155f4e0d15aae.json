{"ast": null, "code": "'use client';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport invariant from 'fbjs/lib/invariant';\nimport EventEmitter from \"../../vendor/react-native/vendor/emitter/EventEmitter\";\nimport canUseDOM from \"../../modules/canUseDom\";\nvar isPrefixed = canUseDOM && !document.hasOwnProperty('hidden') && document.hasOwnProperty('webkitHidden');\nvar EVENT_TYPES = ['change', 'memoryWarning'];\nvar VISIBILITY_CHANGE_EVENT = isPrefixed ? 'webkitvisibilitychange' : 'visibilitychange';\nvar VISIBILITY_STATE_PROPERTY = isPrefixed ? 'webkitVisibilityState' : 'visibilityState';\nvar AppStates = {\n  BACKGROUND: 'background',\n  ACTIVE: 'active'\n};\nvar changeEmitter = null;\nvar AppState = function () {\n  function AppState() {\n    _classCallCheck(this, AppState);\n  }\n  return _createClass(AppState, null, [{\n    key: \"currentState\",\n    get: function get() {\n      if (!AppState.isAvailable) {\n        return AppStates.ACTIVE;\n      }\n      switch (document[VISIBILITY_STATE_PROPERTY]) {\n        case 'hidden':\n        case 'prerender':\n        case 'unloaded':\n          return AppStates.BACKGROUND;\n        default:\n          return AppStates.ACTIVE;\n      }\n    }\n  }, {\n    key: \"addEventListener\",\n    value: function addEventListener(type, handler) {\n      if (AppState.isAvailable) {\n        invariant(EVENT_TYPES.indexOf(type) !== -1, 'Trying to subscribe to unknown event: \"%s\"', type);\n        if (type === 'change') {\n          if (!changeEmitter) {\n            changeEmitter = new EventEmitter();\n            document.addEventListener(VISIBILITY_CHANGE_EVENT, function () {\n              if (changeEmitter) {\n                changeEmitter.emit('change', AppState.currentState);\n              }\n            }, false);\n          }\n          return changeEmitter.addListener(type, handler);\n        }\n      }\n    }\n  }]);\n}();\nexport { AppState as default };\nAppState.isAvailable = canUseDOM && !!document[VISIBILITY_STATE_PROPERTY];", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "invariant", "EventEmitter", "canUseDOM", "isPrefixed", "document", "hasOwnProperty", "EVENT_TYPES", "VISIBILITY_CHANGE_EVENT", "VISIBILITY_STATE_PROPERTY", "AppStates", "BACKGROUND", "ACTIVE", "changeEmitter", "AppState", "key", "get", "isAvailable", "value", "addEventListener", "type", "handler", "indexOf", "emit", "currentState", "addListener", "default"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/AppState/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport invariant from 'fbjs/lib/invariant';\nimport EventEmitter from '../../vendor/react-native/vendor/emitter/EventEmitter';\nimport canUseDOM from '../../modules/canUseDom';\n\n// Android 4.4 browser\nvar isPrefixed = canUseDOM && !document.hasOwnProperty('hidden') && document.hasOwnProperty('webkitHidden');\nvar EVENT_TYPES = ['change', 'memoryWarning'];\nvar VISIBILITY_CHANGE_EVENT = isPrefixed ? 'webkitvisibilitychange' : 'visibilitychange';\nvar VISIBILITY_STATE_PROPERTY = isPrefixed ? 'webkitVisibilityState' : 'visibilityState';\nvar AppStates = {\n  BACKGROUND: 'background',\n  ACTIVE: 'active'\n};\nvar changeEmitter = null;\nexport default class AppState {\n  static get currentState() {\n    if (!AppState.isAvailable) {\n      return AppStates.ACTIVE;\n    }\n    switch (document[VISIBILITY_STATE_PROPERTY]) {\n      case 'hidden':\n      case 'prerender':\n      case 'unloaded':\n        return AppStates.BACKGROUND;\n      default:\n        return AppStates.ACTIVE;\n    }\n  }\n  static addEventListener(type, handler) {\n    if (AppState.isAvailable) {\n      invariant(EVENT_TYPES.indexOf(type) !== -1, 'Trying to subscribe to unknown event: \"%s\"', type);\n      if (type === 'change') {\n        if (!changeEmitter) {\n          changeEmitter = new EventEmitter();\n          document.addEventListener(VISIBILITY_CHANGE_EVENT, () => {\n            if (changeEmitter) {\n              changeEmitter.emit('change', AppState.currentState);\n            }\n          }, false);\n        }\n        return changeEmitter.addListener(type, handler);\n      }\n    }\n  }\n}\nAppState.isAvailable = canUseDOM && !!document[VISIBILITY_STATE_PROPERTY];"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,YAAY;AACnB,OAAOC,SAAS;AAGhB,IAAIC,UAAU,GAAGD,SAAS,IAAI,CAACE,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAC,IAAID,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;AAC3G,IAAIC,WAAW,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC;AAC7C,IAAIC,uBAAuB,GAAGJ,UAAU,GAAG,wBAAwB,GAAG,kBAAkB;AACxF,IAAIK,yBAAyB,GAAGL,UAAU,GAAG,uBAAuB,GAAG,iBAAiB;AACxF,IAAIM,SAAS,GAAG;EACdC,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,aAAa,GAAG,IAAI;AAAC,IACJC,QAAQ;EAAA,SAAAA,SAAA;IAAAf,eAAA,OAAAe,QAAA;EAAA;EAAA,OAAAd,YAAA,CAAAc,QAAA;IAAAC,GAAA;IAAAC,GAAA,EAC3B,SAAAA,IAAA,EAA0B;MACxB,IAAI,CAACF,QAAQ,CAACG,WAAW,EAAE;QACzB,OAAOP,SAAS,CAACE,MAAM;MACzB;MACA,QAAQP,QAAQ,CAACI,yBAAyB,CAAC;QACzC,KAAK,QAAQ;QACb,KAAK,WAAW;QAChB,KAAK,UAAU;UACb,OAAOC,SAAS,CAACC,UAAU;QAC7B;UACE,OAAOD,SAAS,CAACE,MAAM;MAC3B;IACF;EAAC;IAAAG,GAAA;IAAAG,KAAA,EACD,SAAOC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MACrC,IAAIP,QAAQ,CAACG,WAAW,EAAE;QACxBhB,SAAS,CAACM,WAAW,CAACe,OAAO,CAACF,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,4CAA4C,EAAEA,IAAI,CAAC;QAC/F,IAAIA,IAAI,KAAK,QAAQ,EAAE;UACrB,IAAI,CAACP,aAAa,EAAE;YAClBA,aAAa,GAAG,IAAIX,YAAY,CAAC,CAAC;YAClCG,QAAQ,CAACc,gBAAgB,CAACX,uBAAuB,EAAE,YAAM;cACvD,IAAIK,aAAa,EAAE;gBACjBA,aAAa,CAACU,IAAI,CAAC,QAAQ,EAAET,QAAQ,CAACU,YAAY,CAAC;cACrD;YACF,CAAC,EAAE,KAAK,CAAC;UACX;UACA,OAAOX,aAAa,CAACY,WAAW,CAACL,IAAI,EAAEC,OAAO,CAAC;QACjD;MACF;IACF;EAAC;AAAA;AAAA,SA7BkBP,QAAQ,IAAAY,OAAA;AA+B7BZ,QAAQ,CAACG,WAAW,GAAGd,SAAS,IAAI,CAAC,CAACE,QAAQ,CAACI,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}