{"ast": null, "code": "'use client';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"behavior\", \"contentContainerStyle\", \"keyboardVerticalOffset\"];\nimport * as React from 'react';\nimport View from \"../View\";\nvar KeyboardAvoidingView = function (_React$Component) {\n  function KeyboardAvoidingView() {\n    var _this;\n    _classCallCheck(this, KeyboardAvoidingView);\n    _this = _callSuper(this, KeyboardAvoidingView, arguments);\n    _this.frame = null;\n    _this.onLayout = function (event) {\n      _this.frame = event.nativeEvent.layout;\n    };\n    return _this;\n  }\n  _inherits(KeyboardAvoidingView, _React$Component);\n  return _createClass(KeyboardAvoidingView, [{\n    key: \"relativeKeyboardHeight\",\n    value: function relativeKeyboardHeight(keyboardFrame) {\n      var frame = this.frame;\n      if (!frame || !keyboardFrame) {\n        return 0;\n      }\n      var keyboardY = keyboardFrame.screenY - (this.props.keyboardVerticalOffset || 0);\n      return Math.max(frame.y + frame.height - keyboardY, 0);\n    }\n  }, {\n    key: \"onKeyboardChange\",\n    value: function onKeyboardChange(event) {}\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        behavior = _this$props.behavior,\n        contentContainerStyle = _this$props.contentContainerStyle,\n        keyboardVerticalOffset = _this$props.keyboardVerticalOffset,\n        rest = _objectWithoutPropertiesLoose(_this$props, _excluded);\n      return React.createElement(View, _extends({\n        onLayout: this.onLayout\n      }, rest));\n    }\n  }]);\n}(React.Component);\nexport default KeyboardAvoidingView;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "View", "KeyboardAvoidingView", "_React$Component", "_this", "arguments", "frame", "onLayout", "event", "nativeEvent", "layout", "key", "value", "relativeKeyboardHeight", "keyboardFrame", "keyboardY", "screenY", "props", "keyboardVerticalOffset", "Math", "max", "y", "height", "onKeyboardChange", "render", "_this$props", "behavior", "contentContainerStyle", "rest", "createElement", "Component"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/KeyboardAvoidingView/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"behavior\", \"contentContainerStyle\", \"keyboardVerticalOffset\"];\nimport * as React from 'react';\nimport View from '../View';\nclass KeyboardAvoidingView extends React.Component {\n  constructor() {\n    super(...arguments);\n    this.frame = null;\n    this.onLayout = event => {\n      this.frame = event.nativeEvent.layout;\n    };\n  }\n  relativeKeyboardHeight(keyboardFrame) {\n    var frame = this.frame;\n    if (!frame || !keyboardFrame) {\n      return 0;\n    }\n    var keyboardY = keyboardFrame.screenY - (this.props.keyboardVerticalOffset || 0);\n    return Math.max(frame.y + frame.height - keyboardY, 0);\n  }\n  onKeyboardChange(event) {}\n  render() {\n    var _this$props = this.props,\n      behavior = _this$props.behavior,\n      contentContainerStyle = _this$props.contentContainerStyle,\n      keyboardVerticalOffset = _this$props.keyboardVerticalOffset,\n      rest = _objectWithoutPropertiesLoose(_this$props, _excluded);\n    return /*#__PURE__*/React.createElement(View, _extends({\n      onLayout: this.onLayout\n    }, rest));\n  }\n}\nexport default KeyboardAvoidingView;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAJ,eAAA,CAAAI,CAAA,GAAAL,0BAAA,CAAAI,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAL,eAAA,CAAAG,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAEb,OAAOY,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,uBAAuB,EAAE,wBAAwB,CAAC;AAC/E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI;AAAgB,IACrBC,oBAAoB,aAAAC,gBAAA;EACxB,SAAAD,qBAAA,EAAc;IAAA,IAAAE,KAAA;IAAAzB,eAAA,OAAAuB,oBAAA;IACZE,KAAA,GAAApB,UAAA,OAAAkB,oBAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,KAAK,GAAG,IAAI;IACjBF,KAAA,CAAKG,QAAQ,GAAG,UAAAC,KAAK,EAAI;MACvBJ,KAAA,CAAKE,KAAK,GAAGE,KAAK,CAACC,WAAW,CAACC,MAAM;IACvC,CAAC;IAAC,OAAAN,KAAA;EACJ;EAACrB,SAAA,CAAAmB,oBAAA,EAAAC,gBAAA;EAAA,OAAAvB,YAAA,CAAAsB,oBAAA;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAC,sBAAsBA,CAACC,aAAa,EAAE;MACpC,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI,CAACA,KAAK,IAAI,CAACQ,aAAa,EAAE;QAC5B,OAAO,CAAC;MACV;MACA,IAAIC,SAAS,GAAGD,aAAa,CAACE,OAAO,IAAI,IAAI,CAACC,KAAK,CAACC,sBAAsB,IAAI,CAAC,CAAC;MAChF,OAAOC,IAAI,CAACC,GAAG,CAACd,KAAK,CAACe,CAAC,GAAGf,KAAK,CAACgB,MAAM,GAAGP,SAAS,EAAE,CAAC,CAAC;IACxD;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAW,gBAAgBA,CAACf,KAAK,EAAE,CAAC;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAC1B,SAAAY,MAAMA,CAAA,EAAG;MACP,IAAIC,WAAW,GAAG,IAAI,CAACR,KAAK;QAC1BS,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,qBAAqB,GAAGF,WAAW,CAACE,qBAAqB;QACzDT,sBAAsB,GAAGO,WAAW,CAACP,sBAAsB;QAC3DU,IAAI,GAAG9B,6BAA6B,CAAC2B,WAAW,EAAE1B,SAAS,CAAC;MAC9D,OAAoBC,KAAK,CAAC6B,aAAa,CAAC5B,IAAI,EAAEJ,QAAQ,CAAC;QACrDU,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAEqB,IAAI,CAAC,CAAC;IACX;EAAC;AAAA,EA1BgC5B,KAAK,CAAC8B,SAAS;AA4BlD,eAAe5B,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}