{"ast": null, "code": "import dangerousStyleValue from \"./dangerousStyleValue\";\nfunction setValueForStyles(node, styles) {\n  var style = node.style;\n  for (var styleName in styles) {\n    if (!styles.hasOwnProperty(styleName)) {\n      continue;\n    }\n    var isCustomProperty = styleName.indexOf('--') === 0;\n    var styleValue = dangerousStyleValue(styleName, styles[styleName], isCustomProperty);\n    if (styleName === 'float') {\n      styleName = 'cssFloat';\n    }\n    if (isCustomProperty) {\n      style.setProperty(styleName, styleValue);\n    } else {\n      style[styleName] = styleValue;\n    }\n  }\n}\nexport default setValueForStyles;", "map": {"version": 3, "names": ["dangerousStyleValue", "setValueForStyles", "node", "styles", "style", "styleName", "hasOwnProperty", "isCustomProperty", "indexOf", "styleValue", "setProperty"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-web/dist/modules/setValueForStyles/index.js"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * From React 16.3.0\n * \n */\n\nimport dangerousStyleValue from './dangerousStyleValue';\n\n/**\n * Sets the value for multiple styles on a node.  If a value is specified as\n * '' (empty string), the corresponding style property will be unset.\n *\n * @param {DOMElement} node\n * @param {object} styles\n */\nfunction setValueForStyles(node, styles) {\n  var style = node.style;\n  for (var styleName in styles) {\n    if (!styles.hasOwnProperty(styleName)) {\n      continue;\n    }\n    var isCustomProperty = styleName.indexOf('--') === 0;\n    var styleValue = dangerousStyleValue(styleName, styles[styleName], isCustomProperty);\n    if (styleName === 'float') {\n      styleName = 'cssFloat';\n    }\n    if (isCustomProperty) {\n      style.setProperty(styleName, styleValue);\n    } else {\n      style[styleName] = styleValue;\n    }\n  }\n}\nexport default setValueForStyles;"], "mappings": "AAYA,OAAOA,mBAAmB;AAS1B,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACvC,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;EACtB,KAAK,IAAIC,SAAS,IAAIF,MAAM,EAAE;IAC5B,IAAI,CAACA,MAAM,CAACG,cAAc,CAACD,SAAS,CAAC,EAAE;MACrC;IACF;IACA,IAAIE,gBAAgB,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;IACpD,IAAIC,UAAU,GAAGT,mBAAmB,CAACK,SAAS,EAAEF,MAAM,CAACE,SAAS,CAAC,EAAEE,gBAAgB,CAAC;IACpF,IAAIF,SAAS,KAAK,OAAO,EAAE;MACzBA,SAAS,GAAG,UAAU;IACxB;IACA,IAAIE,gBAAgB,EAAE;MACpBH,KAAK,CAACM,WAAW,CAACL,SAAS,EAAEI,UAAU,CAAC;IAC1C,CAAC,MAAM;MACLL,KAAK,CAACC,SAAS,CAAC,GAAGI,UAAU;IAC/B;EACF;AACF;AACA,eAAeR,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}