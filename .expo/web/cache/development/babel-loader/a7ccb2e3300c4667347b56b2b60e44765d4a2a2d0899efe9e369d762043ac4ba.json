{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = createIconSourceCache;\nvar TYPE_VALUE = 'value';\nvar TYPE_ERROR = 'error';\nfunction createIconSourceCache() {\n  var cache = new Map();\n  var setValue = function setValue(key, value) {\n    return cache.set(key, {\n      type: TYPE_VALUE,\n      data: value\n    });\n  };\n  var setError = function setError(key, error) {\n    return cache.set(key, {\n      type: TYPE_ERROR,\n      data: error\n    });\n  };\n  var has = function has(key) {\n    return cache.has(key);\n  };\n  var get = function get(key) {\n    var value = cache.get(key);\n    if (!value) {\n      return undefined;\n    }\n    var type = value.type,\n      data = value.data;\n    if (type === TYPE_ERROR) {\n      throw data;\n    }\n    return data;\n  };\n  return {\n    setValue: setValue,\n    setError: setError,\n    has: has,\n    get: get\n  };\n}", "map": {"version": 3, "names": ["TYPE_VALUE", "TYPE_ERROR", "createIconSourceCache", "cache", "Map", "setValue", "key", "value", "set", "type", "data", "setError", "error", "has", "get", "undefined"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-vector-icons/common/src/create-icon-source-cache.ts"], "sourcesContent": ["const TYPE_VALUE = 'value';\nconst TYPE_ERROR = 'error';\n\ntype ValueData = { uri: string; scale: number };\n\ntype Value = { type: typeof TYPE_VALUE; data: ValueData } | { type: typeof TYPE_ERROR; data: Error };\n\nexport default function createIconSourceCache() {\n  const cache = new Map<string, Value>();\n\n  const setValue = (key: string, value: ValueData) => cache.set(key, { type: TYPE_VALUE, data: value });\n\n  const setError = (key: string, error: Error) => cache.set(key, { type: TYPE_ERROR, data: error });\n\n  const has = (key: string) => cache.has(key);\n\n  const get = (key: string) => {\n    const value = cache.get(key);\n    if (!value) {\n      return undefined;\n    }\n\n    const { type, data } = value;\n    if (type === TYPE_ERROR) {\n      throw data;\n    }\n    return data;\n  };\n\n  return { setValue, setError, has, get };\n}\n"], "mappings": ";;;;;;AAAA,IAAMA,UAAU,GAAG,OAAO;AAC1B,IAAMC,UAAU,GAAG,OAAO;AAMX,SAASC,qBAAqBA,CAAA,EAAG;EAC9C,IAAMC,KAAK,GAAG,IAAIC,GAAG,CAAgB,CAAC;EAEtC,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,GAAW,EAAEC,KAAgB;IAAA,OAAKJ,KAAK,CAACK,GAAG,CAACF,GAAG,EAAE;MAAEG,IAAI,EAAET,UAAU;MAAEU,IAAI,EAAEH;IAAM,CAAC,CAAC;EAAA;EAErG,IAAMI,QAAQ,GAAG,SAAXA,QAAQA,CAAIL,GAAW,EAAEM,KAAY;IAAA,OAAKT,KAAK,CAACK,GAAG,CAACF,GAAG,EAAE;MAAEG,IAAI,EAAER,UAAU;MAAES,IAAI,EAAEE;IAAM,CAAC,CAAC;EAAA;EAEjG,IAAMC,GAAG,GAAI,SAAPA,GAAGA,CAAIP,GAAW;IAAA,OAAKH,KAAK,CAACU,GAAG,CAACP,GAAG,CAAC;EAAA;EAE3C,IAAMQ,GAAG,GAAI,SAAPA,GAAGA,CAAIR,GAAW,EAAK;IAC3B,IAAMC,KAAK,GAAGJ,KAAK,CAACW,GAAG,CAACR,GAAG,CAAC;IAC5B,IAAI,CAACC,KAAK,EAAE;MACV,OAAOQ,SAAS;IAClB;IAEA,IAAQN,IAAI,GAAWF,KAAK,CAApBE,IAAI;MAAEC,IAAA,GAASH,KAAK,CAAdG,IAAA;IACd,IAAID,IAAI,KAAKR,UAAU,EAAE;MACvB,MAAMS,IAAI;IACZ;IACA,OAAOA,IAAI;EACb,CAAC;EAED,OAAO;IAAEL,QAAQ,EAARA,QAAQ;IAAEM,QAAQ,EAARA,QAAQ;IAAEE,GAAG,EAAHA,GAAG;IAAEC,GAAA,EAAAA;EAAI,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}