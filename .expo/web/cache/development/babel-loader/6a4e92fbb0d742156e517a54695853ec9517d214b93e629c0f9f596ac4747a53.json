{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"index\", \"total\", \"style\", \"theme\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Image from \"react-native-web/dist/exports/Image\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { getCardCoverStyle } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { grey200 } from \"../../styles/themes/v2/colors\";\nimport { splitStyles } from \"../../utils/splitStyles\";\nvar CardCover = function CardCover(_ref) {\n  var index = _ref.index,\n    total = _ref.total,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var flattenedStyles = StyleSheet.flatten(style) || {};\n  var _splitStyles = splitStyles(flattenedStyles, function (style) {\n      return style.startsWith('border') && style.endsWith('Radius');\n    }),\n    _splitStyles2 = _slicedToArray(_splitStyles, 2),\n    borderRadiusStyles = _splitStyles2[1];\n  var coverStyle = getCardCoverStyle({\n    theme: theme,\n    index: index,\n    total: total,\n    borderRadiusStyles: borderRadiusStyles\n  });\n  return React.createElement(View, {\n    style: [styles.container, coverStyle, style]\n  }, React.createElement(Image, _extends({}, rest, {\n    style: [styles.image, coverStyle],\n    accessibilityIgnoresInvertColors: true\n  })));\n};\nCardCover.displayName = 'Card.Cover';\nvar styles = StyleSheet.create({\n  container: {\n    height: 195,\n    backgroundColor: grey200,\n    overflow: 'hidden'\n  },\n  image: {\n    flex: 1,\n    height: undefined,\n    width: undefined,\n    justifyContent: 'flex-end'\n  }\n});\nexport default CardCover;\nexport { CardCover };", "map": {"version": 3, "names": ["React", "Image", "StyleSheet", "View", "getCardCoverStyle", "useInternalTheme", "grey200", "splitStyles", "CardCover", "_ref", "index", "total", "style", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "flattenedStyles", "flatten", "_splitStyles", "startsWith", "endsWith", "_splitStyles2", "_slicedToArray", "borderRadiusStyles", "coverStyle", "createElement", "styles", "container", "_extends", "image", "accessibilityIgnoresInvertColors", "displayName", "create", "height", "backgroundColor", "overflow", "flex", "undefined", "width", "justifyContent"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Card/CardCover.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Image, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport { getCardCoverStyle } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport { grey200 } from '../../styles/themes/v2/colors';\nimport type { ThemeProp } from '../../types';\nimport { splitStyles } from '../../utils/splitStyles';\n\nexport type Props = React.ComponentPropsWithRef<typeof Image> & {\n  /**\n   * @internal\n   */\n  index?: number;\n  /**\n   * @internal\n   */\n  total?: number;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to show a cover image inside a Card.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Card } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Card>\n *     <Card.Cover source={{ uri: 'https://picsum.photos/700' }} />\n *   </Card>\n * );\n *\n * export default MyComponent;\n * ```\n *\n * @extends Image props https://reactnative.dev/docs/image#props\n */\nconst CardCover = ({\n  index,\n  total,\n  style,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  const flattenedStyles = (StyleSheet.flatten(style) || {}) as ViewStyle;\n  const [, borderRadiusStyles] = splitStyles(\n    flattenedStyles,\n    (style) => style.startsWith('border') && style.endsWith('Radius')\n  );\n\n  const coverStyle = getCardCoverStyle({\n    theme,\n    index,\n    total,\n    borderRadiusStyles,\n  });\n\n  return (\n    <View style={[styles.container, coverStyle, style]}>\n      <Image\n        {...rest}\n        style={[styles.image, coverStyle]}\n        accessibilityIgnoresInvertColors\n      />\n    </View>\n  );\n};\n\nCardCover.displayName = 'Card.Cover';\nconst styles = StyleSheet.create({\n  container: {\n    height: 195,\n    backgroundColor: grey200,\n    overflow: 'hidden',\n  },\n  image: {\n    flex: 1,\n    height: undefined,\n    width: undefined,\n    justifyContent: 'flex-end',\n  },\n});\n\nexport default CardCover;\n\n// @component-docs ignore-next-line\nexport { CardCover };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,SAASC,iBAAiB;AAC1B,SAASC,gBAAgB;AACzB,SAASC,OAAO;AAEhB,SAASC,WAAW;AAqCpB,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAGC,IAAA,EAML;EAAA,IALXC,KAAK,GAKCD,IAAA,CALNC,KAAK;IACLC,KAAK,GAICF,IAAA,CAJNE,KAAK;IACLC,KAAK,GAGCH,IAAA,CAHNG,KAAK;IACEC,cAAc,GAEfJ,IAAA,CAFNK,KAAK;IACFC,IAAA,GAAAC,wBAAA,CACGP,IAAA,EAAAQ,SAAA;EACN,IAAMH,KAAK,GAAGT,gBAAgB,CAACQ,cAAc,CAAC;EAE9C,IAAMK,eAAe,GAAIhB,UAAU,CAACiB,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAe;EACtE,IAAAQ,YAAA,GAA+Bb,WAAW,CACxCW,eAAe,EACd,UAAAN,KAAK;MAAA,OAAKA,KAAK,CAACS,UAAU,CAAC,QAAQ,CAAC,IAAIT,KAAK,CAACU,QAAQ,CAAC,QAAQ,CAClE;IAAA,EAAC;IAAAC,aAAA,GAAAC,cAAA,CAAAJ,YAAA;IAHQK,kBAAkB,GAAAF,aAAA;EAK3B,IAAMG,UAAU,GAAGtB,iBAAiB,CAAC;IACnCU,KAAK,EAALA,KAAK;IACLJ,KAAK,EAALA,KAAK;IACLC,KAAK,EAALA,KAAK;IACLc,kBAAA,EAAAA;EACF,CAAC,CAAC;EAEF,OACEzB,KAAA,CAAA2B,aAAA,CAACxB,IAAI;IAACS,KAAK,EAAE,CAACgB,MAAM,CAACC,SAAS,EAAEH,UAAU,EAAEd,KAAK;EAAE,GACjDZ,KAAA,CAAA2B,aAAA,CAAC1B,KAAK,EAAA6B,QAAA,KACAf,IAAI;IACRH,KAAK,EAAE,CAACgB,MAAM,CAACG,KAAK,EAAEL,UAAU,CAAE;IAClCM,gCAAgC;EAAA,EACjC,CACG,CAAC;AAEX,CAAC;AAEDxB,SAAS,CAACyB,WAAW,GAAG,YAAY;AACpC,IAAML,MAAM,GAAG1B,UAAU,CAACgC,MAAM,CAAC;EAC/BL,SAAS,EAAE;IACTM,MAAM,EAAE,GAAG;IACXC,eAAe,EAAE9B,OAAO;IACxB+B,QAAQ,EAAE;EACZ,CAAC;EACDN,KAAK,EAAE;IACLO,IAAI,EAAE,CAAC;IACPH,MAAM,EAAEI,SAAS;IACjBC,KAAK,EAAED,SAAS;IAChBE,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAejC,SAAS;AAGxB,SAASA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}