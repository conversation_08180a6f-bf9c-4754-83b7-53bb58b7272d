{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"variant\", \"theme\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport NativeText from \"react-native-web/dist/exports/Text\";\nimport AnimatedText from \"./AnimatedText\";\nimport StyledText from \"./v2/StyledText\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nvar Text = function Text(_ref, ref) {\n  var style = _ref.style,\n    variant = _ref.variant,\n    initialTheme = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var root = React.useRef(null);\n  var theme = useInternalTheme(initialTheme);\n  var writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n  React.useImperativeHandle(ref, function () {\n    return {\n      setNativeProps: function setNativeProps(args) {\n        var _root$current;\n        return (_root$current = root.current) === null || _root$current === void 0 ? void 0 : _root$current.setNativeProps(args);\n      }\n    };\n  });\n  if (theme.isV3 && variant) {\n    var font = theme.fonts[variant];\n    var textStyle = [font, style];\n    if (React.isValidElement(rest.children) && (rest.children.type === Component || rest.children.type === AnimatedText || rest.children.type === StyledText)) {\n      var props = rest.children.props;\n      if (props.variant) {\n        font = theme.fonts[props.variant];\n        textStyle = [style, font];\n      }\n      if (!props.variant) {\n        textStyle = [style, props.style];\n      }\n    }\n    if (typeof font !== 'object') {\n      throw new Error(`Variant ${variant} was not provided properly. Valid variants are ${Object.keys(theme.fonts).join(', ')}.`);\n    }\n    return React.createElement(NativeText, _extends({\n      ref: root,\n      style: [styles.text, {\n        writingDirection: writingDirection,\n        color: theme.colors.onSurface\n      }, textStyle]\n    }, rest));\n  } else {\n    var _theme$fonts, _theme$colors;\n    var _font = theme.isV3 ? theme.fonts.default : (_theme$fonts = theme.fonts) === null || _theme$fonts === void 0 ? void 0 : _theme$fonts.regular;\n    var _textStyle = _objectSpread(_objectSpread({}, _font), {}, {\n      color: theme.isV3 ? (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.onSurface : theme.colors.text\n    });\n    return React.createElement(NativeText, _extends({}, rest, {\n      ref: root,\n      style: [styles.text, _textStyle, {\n        writingDirection: writingDirection\n      }, style]\n    }));\n  }\n};\nvar styles = StyleSheet.create({\n  text: {\n    textAlign: 'left'\n  }\n});\nvar Component = forwardRef(Text);\nexport var customText = function customText() {\n  return Component;\n};\nexport default Component;", "map": {"version": 3, "names": ["React", "I18nManager", "StyleSheet", "NativeText", "AnimatedText", "StyledText", "useInternalTheme", "forwardRef", "Text", "_ref", "ref", "style", "variant", "initialTheme", "theme", "rest", "_objectWithoutProperties", "_excluded", "root", "useRef", "writingDirection", "getConstants", "isRTL", "useImperativeHandle", "setNativeProps", "args", "_root$current", "current", "isV3", "font", "fonts", "textStyle", "isValidElement", "children", "type", "Component", "props", "Error", "Object", "keys", "join", "createElement", "_extends", "styles", "text", "color", "colors", "onSurface", "_theme$fonts", "_theme$colors", "default", "regular", "_objectSpread", "create", "textAlign", "customText"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Typography/Text.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  I18nManager,\n  StyleProp,\n  StyleSheet,\n  Text as NativeText,\n  TextStyle,\n} from 'react-native';\n\nimport AnimatedText from './AnimatedText';\nimport type { VariantProp } from './types';\nimport StyledText from './v2/StyledText';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\n\nexport type Props<T> = React.ComponentProps<typeof NativeText> & {\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Variant defines appropriate text styles for type role and its size.\n   * Available variants:\n   *\n   *  Display: `displayLarge`, `displayMedium`, `displaySmall`\n   *\n   *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`\n   *\n   *  Title: `titleLarge`, `titleMedium`, `titleSmall`\n   *\n   *  Label:  `labelLarge`, `labelMedium`, `labelSmall`\n   *\n   *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`\n   */\n  variant?: VariantProp<T>;\n  children: React.ReactNode;\n  theme?: ThemeProp;\n  style?: StyleProp<TextStyle>;\n};\n\nexport type TextRef = React.ForwardedRef<{\n  setNativeProps(args: Object): void;\n}>;\n\n// @component-group Typography\n\n/**\n * Typography component showing styles complied with passed `variant` prop and supported by the type system.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Text } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <>\n *     <Text variant=\"displayLarge\">Display Large</Text>\n *     <Text variant=\"displayMedium\">Display Medium</Text>\n *     <Text variant=\"displaySmall\">Display small</Text>\n *\n *     <Text variant=\"headlineLarge\">Headline Large</Text>\n *     <Text variant=\"headlineMedium\">Headline Medium</Text>\n *     <Text variant=\"headlineSmall\">Headline Small</Text>\n *\n *     <Text variant=\"titleLarge\">Title Large</Text>\n *     <Text variant=\"titleMedium\">Title Medium</Text>\n *     <Text variant=\"titleSmall\">Title Small</Text>\n *\n *     <Text variant=\"bodyLarge\">Body Large</Text>\n *     <Text variant=\"bodyMedium\">Body Medium</Text>\n *     <Text variant=\"bodySmall\">Body Small</Text>\n *\n *     <Text variant=\"labelLarge\">Label Large</Text>\n *     <Text variant=\"labelMedium\">Label Medium</Text>\n *     <Text variant=\"labelSmall\">Label Small</Text>\n *  </>\n * );\n *\n * export default MyComponent;\n * ```\n *\n * @extends Text props https://reactnative.dev/docs/text#props\n */\nconst Text = (\n  { style, variant, theme: initialTheme, ...rest }: Props<string>,\n  ref: TextRef\n) => {\n  const root = React.useRef<NativeText | null>(null);\n  // FIXME: destructure it in TS 4.6+\n  const theme = useInternalTheme(initialTheme);\n  const writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n\n  React.useImperativeHandle(ref, () => ({\n    setNativeProps: (args: Object) => root.current?.setNativeProps(args),\n  }));\n\n  if (theme.isV3 && variant) {\n    let font = theme.fonts[variant];\n    let textStyle = [font, style];\n\n    if (\n      React.isValidElement(rest.children) &&\n      (rest.children.type === Component ||\n        rest.children.type === AnimatedText ||\n        rest.children.type === StyledText)\n    ) {\n      const { props } = rest.children;\n\n      // Context:   Some components have the built-in `Text` component with a predefined variant,\n      //            that also accepts `children` as a `React.Node`. This can result in a situation,\n      //            where another `Text` component is rendered within the built-in `Text` component.\n      //            By doing that, we assume that user doesn't want to consume pre-defined font properties.\n      // Case one:  Nested `Text` has different `variant` that specified in parent. For example:\n      //              <Chip>\n      //                <Text variant=\"displayMedium\">Nested</Text>\n      //              </Chip>\n      // Solution:  To address the following scenario, the code below overrides the `variant`\n      //            specified in a parent in favor of children's variant:\n      if (props.variant) {\n        font = theme.fonts[props.variant as VariantProp<typeof props.variant>];\n        textStyle = [style, font];\n      }\n\n      // Case two:  Nested `Text` has specified `styles` which intefere\n      //            with font properties, from the parent's `variant`. For example:\n      //              <Chip>\n      //                <Text style={{fontSize: 30}}>Nested</Text>\n      //              </Chip>\n      // Solution:  To address the following scenario, the code below overrides the\n      //            parent's style with children's style:\n      if (!props.variant) {\n        textStyle = [style, props.style];\n      }\n    }\n\n    if (typeof font !== 'object') {\n      throw new Error(\n        `Variant ${variant} was not provided properly. Valid variants are ${Object.keys(\n          theme.fonts\n        ).join(', ')}.`\n      );\n    }\n\n    return (\n      <NativeText\n        ref={root}\n        style={[\n          styles.text,\n          { writingDirection, color: theme.colors.onSurface },\n          textStyle,\n        ]}\n        {...rest}\n      />\n    );\n  } else {\n    const font = theme.isV3 ? theme.fonts.default : theme.fonts?.regular;\n    const textStyle = {\n      ...font,\n      color: theme.isV3 ? theme.colors?.onSurface : theme.colors.text,\n    };\n    return (\n      <NativeText\n        {...rest}\n        ref={root}\n        style={[styles.text, textStyle, { writingDirection }, style]}\n      />\n    );\n  }\n};\n\nconst styles = StyleSheet.create({\n  text: {\n    textAlign: 'left',\n  },\n});\n\ntype TextComponent<T> = (\n  props: Props<T> & { ref?: React.RefObject<TextRef> }\n) => JSX.Element;\n\nconst Component = forwardRef(Text) as TextComponent<never>;\n\nexport const customText = <T,>() => Component as unknown as TextComponent<T>;\n\nexport default Component;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAS9B,OAAOC,YAAY;AAEnB,OAAOC,UAAU;AACjB,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AAoEnB,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAGC,IAAA,EAEXC,GAAY,EACT;EAAA,IAFDC,KAAK,GAAwDF,IAAA,CAA7DE,KAAK;IAAEC,OAAO,GAA+CH,IAAA,CAAtDG,OAAO;IAASC,YAAY,GAA0BJ,IAAA,CAA7CK,KAAK;IAAmBC,IAAA,GAAAC,wBAAA,CAAqBP,IAAA,EAAAQ,SAAA;EAG/D,IAAMC,IAAI,GAAGlB,KAAK,CAACmB,MAAM,CAAoB,IAAI,CAAC;EAElD,IAAML,KAAK,GAAGR,gBAAgB,CAACO,YAAY,CAAC;EAC5C,IAAMO,gBAAgB,GAAGnB,WAAW,CAACoB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzEtB,KAAK,CAACuB,mBAAmB,CAACb,GAAG,EAAE;IAAA,OAAO;MACpCc,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,IAAY;QAAA,IAAAC,aAAA;QAAA,QAAAA,aAAA,GAAKR,IAAI,CAACS,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;MAAA;IACtE,CAAC;EAAA,CAAC,CAAC;EAEH,IAAIX,KAAK,CAACc,IAAI,IAAIhB,OAAO,EAAE;IACzB,IAAIiB,IAAI,GAAGf,KAAK,CAACgB,KAAK,CAAClB,OAAO,CAAC;IAC/B,IAAImB,SAAS,GAAG,CAACF,IAAI,EAAElB,KAAK,CAAC;IAE7B,IACEX,KAAK,CAACgC,cAAc,CAACjB,IAAI,CAACkB,QAAQ,CAAC,KAClClB,IAAI,CAACkB,QAAQ,CAACC,IAAI,KAAKC,SAAS,IAC/BpB,IAAI,CAACkB,QAAQ,CAACC,IAAI,KAAK9B,YAAY,IACnCW,IAAI,CAACkB,QAAQ,CAACC,IAAI,KAAK7B,UAAU,CAAC,EACpC;MACA,IAAQ+B,KAAA,GAAUrB,IAAI,CAACkB,QAAQ,CAAvBG,KAAA;MAYR,IAAIA,KAAK,CAACxB,OAAO,EAAE;QACjBiB,IAAI,GAAGf,KAAK,CAACgB,KAAK,CAACM,KAAK,CAACxB,OAAO,CAAsC;QACtEmB,SAAS,GAAG,CAACpB,KAAK,EAAEkB,IAAI,CAAC;MAC3B;MASA,IAAI,CAACO,KAAK,CAACxB,OAAO,EAAE;QAClBmB,SAAS,GAAG,CAACpB,KAAK,EAAEyB,KAAK,CAACzB,KAAK,CAAC;MAClC;IACF;IAEA,IAAI,OAAOkB,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIQ,KAAK,CACZ,WAAUzB,OAAQ,kDAAiD0B,MAAM,CAACC,IAAI,CAC7EzB,KAAK,CAACgB,KACR,CAAC,CAACU,IAAI,CAAC,IAAI,CAAE,GACf,CAAC;IACH;IAEA,OACExC,KAAA,CAAAyC,aAAA,CAACtC,UAAU,EAAAuC,QAAA;MACThC,GAAG,EAAEQ,IAAK;MACVP,KAAK,EAAE,CACLgC,MAAM,CAACC,IAAI,EACX;QAAExB,gBAAgB,EAAhBA,gBAAgB;QAAEyB,KAAK,EAAE/B,KAAK,CAACgC,MAAM,CAACC;MAAU,CAAC,EACnDhB,SAAS;IACT,GACEhB,IAAI,CACT,CAAC;EAEN,CAAC,MAAM;IAAA,IAAAiC,YAAA,EAAAC,aAAA;IACL,IAAMpB,KAAI,GAAGf,KAAK,CAACc,IAAI,GAAGd,KAAK,CAACgB,KAAK,CAACoB,OAAO,IAAAF,YAAA,GAAGlC,KAAK,CAACgB,KAAK,cAAAkB,YAAA,uBAAXA,YAAA,CAAaG,OAAO;IACpE,IAAMpB,UAAS,GAAAqB,aAAA,CAAAA,aAAA,KACVvB,KAAI;MACPgB,KAAK,EAAE/B,KAAK,CAACc,IAAI,IAAAqB,aAAA,GAAGnC,KAAK,CAACgC,MAAM,cAAAG,aAAA,uBAAZA,aAAA,CAAcF,SAAS,GAAGjC,KAAK,CAACgC,MAAM,CAACF;IAAA,EAC5D;IACD,OACE5C,KAAA,CAAAyC,aAAA,CAACtC,UAAU,EAAAuC,QAAA,KACL3B,IAAI;MACRL,GAAG,EAAEQ,IAAK;MACVP,KAAK,EAAE,CAACgC,MAAM,CAACC,IAAI,EAAEb,UAAS,EAAE;QAAEX,gBAAA,EAAAA;MAAiB,CAAC,EAAET,KAAK;IAAE,EAC9D,CAAC;EAEN;AACF,CAAC;AAED,IAAMgC,MAAM,GAAGzC,UAAU,CAACmD,MAAM,CAAC;EAC/BT,IAAI,EAAE;IACJU,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAMF,IAAMnB,SAAS,GAAG5B,UAAU,CAACC,IAAI,CAAyB;AAE1D,OAAO,IAAM+C,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAapB,SAAwC;AAAA;AAE5E,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}