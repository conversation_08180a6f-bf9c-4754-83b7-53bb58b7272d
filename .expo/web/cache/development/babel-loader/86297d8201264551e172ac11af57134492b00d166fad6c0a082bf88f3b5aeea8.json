{"ast": null, "code": "export { default as Background } from \"./Background\";\nexport { default as getDefaultHeaderHeight } from \"./Header/getDefaultHeaderHeight\";\nexport { default as getHeaderTitle } from \"./Header/getHeaderTitle\";\nexport { default as Header } from \"./Header/Header\";\nexport { default as HeaderBackButton } from \"./Header/HeaderBackButton\";\nexport { default as HeaderBackContext } from \"./Header/HeaderBackContext\";\nexport { default as HeaderBackground } from \"./Header/HeaderBackground\";\nexport { default as HeaderHeightContext } from \"./Header/HeaderHeightContext\";\nexport { default as HeaderShownContext } from \"./Header/HeaderShownContext\";\nexport { default as HeaderTitle } from \"./Header/HeaderTitle\";\nexport { default as useHeaderHeight } from \"./Header/useHeaderHeight\";\nexport { default as MissingIcon } from \"./MissingIcon\";\nexport { default as PlatformPressable } from \"./PlatformPressable\";\nexport { default as ResourceSavingView } from \"./ResourceSavingView\";\nexport { default as SafeAreaProviderCompat } from \"./SafeAreaProviderCompat\";\nexport { default as Screen } from \"./Screen\";\nexport var Assets = [require(\"./assets/back-icon.png\"), require(\"./assets/back-icon-mask.png\")];\nexport * from \"./types\";", "map": {"version": 3, "names": ["default", "Background", "getDefaultHeaderHeight", "getHeaderTitle", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderBackContext", "HeaderBackground", "HeaderHeightContext", "HeaderShownContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useHeaderHeight", "MissingIcon", "PlatformPressable", "ResourceSavingView", "SafeAreaProviderCompat", "Screen", "Assets", "require"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/elements/src/index.tsx"], "sourcesContent": ["export { default as Background } from './Background';\nexport { default as getDefaultHeaderHeight } from './Header/getDefaultHeaderHeight';\nexport { default as getHeaderTitle } from './Header/getHeaderTitle';\nexport { default as Header } from './Header/Header';\nexport { default as HeaderBackButton } from './Header/HeaderBackButton';\nexport { default as HeaderBackContext } from './Header/HeaderBackContext';\nexport { default as HeaderBackground } from './Header/HeaderBackground';\nexport { default as HeaderHeightContext } from './Header/HeaderHeightContext';\nexport { default as HeaderShownContext } from './Header/HeaderShownContext';\nexport { default as HeaderTitle } from './Header/HeaderTitle';\nexport { default as useHeaderHeight } from './Header/useHeaderHeight';\nexport { default as MissingIcon } from './MissingIcon';\nexport { default as PlatformPressable } from './PlatformPressable';\nexport { default as ResourceSavingView } from './ResourceSavingView';\nexport { default as SafeAreaProviderCompat } from './SafeAreaProviderCompat';\nexport { default as Screen } from './Screen';\n\nexport const Assets = [\n  // eslint-disable-next-line import/no-commonjs\n  require('./assets/back-icon.png'),\n  // eslint-disable-next-line import/no-commonjs\n  require('./assets/back-icon-mask.png'),\n];\n\nexport * from './types';\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,UAAU;AAC9B,SAASD,OAAO,IAAIE,sBAAsB;AAC1C,SAASF,OAAO,IAAIG,cAAc;AAClC,SAASH,OAAO,IAAII,MAAM;AAC1B,SAASJ,OAAO,IAAIK,gBAAgB;AACpC,SAASL,OAAO,IAAIM,iBAAiB;AACrC,SAASN,OAAO,IAAIO,gBAAgB;AACpC,SAASP,OAAO,IAAIQ,mBAAmB;AACvC,SAASR,OAAO,IAAIS,kBAAkB;AACtC,SAAST,OAAO,IAAIU,WAAW;AAC/B,SAASV,OAAO,IAAIW,eAAe;AACnC,SAASX,OAAO,IAAIY,WAAW;AAC/B,SAASZ,OAAO,IAAIa,iBAAiB;AACrC,SAASb,OAAO,IAAIc,kBAAkB;AACtC,SAASd,OAAO,IAAIe,sBAAsB;AAC1C,SAASf,OAAO,IAAIgB,MAAM;AAE1B,OAAO,IAAMC,MAAM,GAAG,CAEpBC,OAAO,yBAAyB,CAAC,EAEjCA,OAAO,8BAA8B,CAAC,CACvC;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}