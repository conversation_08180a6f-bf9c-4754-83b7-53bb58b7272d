{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nimport Keyboard from \"react-native-web/dist/exports/Keyboard\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nexport default function useIsKeyboardShown() {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isKeyboardShown = _React$useState2[0],\n    setIsKeyboardShown = _React$useState2[1];\n  React.useEffect(function () {\n    var handleKeyboardShow = function handleKeyboardShow() {\n      return setIsKeyboardShown(true);\n    };\n    var handleKeyboardHide = function handleKeyboardHide() {\n      return setIsKeyboardShown(false);\n    };\n    var subscriptions;\n    if (Platform.OS === 'ios') {\n      subscriptions = [Keyboard.addListener('keyboardWillShow', handleKeyboardShow), Keyboard.addListener('keyboardWillHide', handleKeyboardHide)];\n    } else {\n      subscriptions = [Keyboard.addListener('keyboardDidShow', handleKeyboardShow), Keyboard.addListener('keyboardDidHide', handleKeyboardHide)];\n    }\n    return function () {\n      subscriptions.forEach(function (s) {\n        return s.remove();\n      });\n    };\n  }, []);\n  return isKeyboardShown;\n}", "map": {"version": 3, "names": ["React", "Keyboard", "Platform", "useIsKeyboardShown", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "isKeyboardShown", "setIsKeyboardShown", "useEffect", "handleKeyboardShow", "handleKeyboardHide", "subscriptions", "OS", "addListener", "for<PERSON>ach", "s", "remove"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/bottom-tabs/src/utils/useIsKeyboardShown.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { EmitterSubscription, Keyboard, Platform } from 'react-native';\n\nexport default function useIsKeyboardShown() {\n  const [isKeyboardShown, setIsKeyboardShown] = React.useState(false);\n\n  React.useEffect(() => {\n    const handleKeyboardShow = () => setIsKeyboardShown(true);\n    const handleKeyboardHide = () => setIsKeyboardShown(false);\n\n    let subscriptions: EmitterSubscription[];\n\n    if (Platform.OS === 'ios') {\n      subscriptions = [\n        Keyboard.addListener('keyboardWillShow', handleKeyboardShow),\n        Keyboard.addListener('keyboardWillHide', handleKeyboardHide),\n      ];\n    } else {\n      subscriptions = [\n        Keyboard.addListener('keyboardDidShow', handleKeyboardShow),\n        Keyboard.addListener('keyboardDidHide', handleKeyboardHide),\n      ];\n    }\n\n    return () => {\n      subscriptions.forEach((s) => s.remove());\n    };\n  }, []);\n\n  return isKeyboardShown;\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAG9B,eAAe,SAASC,kBAAkBA,CAAA,EAAG;EAC3C,IAAAC,eAAA,GAA8CJ,KAAK,CAACK,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA5DI,eAAe,GAAAF,gBAAA;IAAEG,kBAAkB,GAAAH,gBAAA;EAE1CN,KAAK,CAACU,SAAS,CAAC,YAAM;IACpB,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;MAAA,OAASF,kBAAkB,CAAC,IAAI,CAAC;IAAA;IACzD,IAAMG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;MAAA,OAASH,kBAAkB,CAAC,KAAK,CAAC;IAAA;IAE1D,IAAII,aAAoC;IAExC,IAAIX,QAAQ,CAACY,EAAE,KAAK,KAAK,EAAE;MACzBD,aAAa,GAAG,CACdZ,QAAQ,CAACc,WAAW,CAAC,kBAAkB,EAAEJ,kBAAkB,CAAC,EAC5DV,QAAQ,CAACc,WAAW,CAAC,kBAAkB,EAAEH,kBAAkB,CAAC,CAC7D;IACH,CAAC,MAAM;MACLC,aAAa,GAAG,CACdZ,QAAQ,CAACc,WAAW,CAAC,iBAAiB,EAAEJ,kBAAkB,CAAC,EAC3DV,QAAQ,CAACc,WAAW,CAAC,iBAAiB,EAAEH,kBAAkB,CAAC,CAC5D;IACH;IAEA,OAAO,YAAM;MACXC,aAAa,CAACG,OAAO,CAAE,UAAAC,CAAC;QAAA,OAAKA,CAAC,CAACC,MAAM,EAAE;MAAA,EAAC;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOV,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}