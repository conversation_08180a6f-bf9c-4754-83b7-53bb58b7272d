{"ast": null, "code": "export var MD3TypescaleKey = function (MD3TypescaleKey) {\n  MD3TypescaleKey[\"displayLarge\"] = \"displayLarge\";\n  MD3TypescaleKey[\"displayMedium\"] = \"displayMedium\";\n  MD3TypescaleKey[\"displaySmall\"] = \"displaySmall\";\n  MD3TypescaleKey[\"headlineLarge\"] = \"headlineLarge\";\n  MD3TypescaleKey[\"headlineMedium\"] = \"headlineMedium\";\n  MD3TypescaleKey[\"headlineSmall\"] = \"headlineSmall\";\n  MD3TypescaleKey[\"titleLarge\"] = \"titleLarge\";\n  MD3TypescaleKey[\"titleMedium\"] = \"titleMedium\";\n  MD3TypescaleKey[\"titleSmall\"] = \"titleSmall\";\n  MD3TypescaleKey[\"labelLarge\"] = \"labelLarge\";\n  MD3TypescaleKey[\"labelMedium\"] = \"labelMedium\";\n  MD3TypescaleKey[\"labelSmall\"] = \"labelSmall\";\n  MD3TypescaleKey[\"bodyLarge\"] = \"bodyLarge\";\n  MD3TypescaleKey[\"bodyMedium\"] = \"bodyMedium\";\n  MD3TypescaleKey[\"bodySmall\"] = \"bodySmall\";\n  return MD3TypescaleKey;\n}({});\nexport var ElevationLevels = function (ElevationLevels) {\n  ElevationLevels[ElevationLevels[\"level0\"] = 0] = \"level0\";\n  ElevationLevels[ElevationLevels[\"level1\"] = 1] = \"level1\";\n  ElevationLevels[ElevationLevels[\"level2\"] = 2] = \"level2\";\n  ElevationLevels[ElevationLevels[\"level3\"] = 3] = \"level3\";\n  ElevationLevels[ElevationLevels[\"level4\"] = 4] = \"level4\";\n  ElevationLevels[ElevationLevels[\"level5\"] = 5] = \"level5\";\n  return ElevationLevels;\n}({});", "map": {"version": 3, "names": ["MD3TypescaleKey", "ElevationLevels"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/types.tsx"], "sourcesContent": ["import type * as React from 'react';\n\nimport type { $DeepPartial } from '@callstack/react-theme-provider';\n\nexport type Font = {\n  fontFamily: string;\n  fontWeight?:\n    | 'normal'\n    | 'bold'\n    | '100'\n    | '200'\n    | '300'\n    | '400'\n    | '500'\n    | '600'\n    | '700'\n    | '800'\n    | '900';\n  fontStyle?: 'normal' | 'italic' | undefined;\n};\n\nexport type Fonts = {\n  regular: Font;\n  medium: Font;\n  light: Font;\n  thin: Font;\n};\n\ntype Mode = 'adaptive' | 'exact';\n\nexport type MD2Colors = {\n  primary: string;\n  background: string;\n  surface: string;\n  accent: string;\n  error: string;\n  text: string;\n  onSurface: string;\n  disabled: string;\n  placeholder: string;\n  backdrop: string;\n  notification: string;\n  tooltip: string;\n};\n\nexport type MD3Colors = {\n  primary: string;\n  primaryContainer: string;\n  secondary: string;\n  secondaryContainer: string;\n  tertiary: string;\n  tertiaryContainer: string;\n  surface: string;\n  surfaceVariant: string;\n  surfaceDisabled: string;\n  background: string;\n  error: string;\n  errorContainer: string;\n  onPrimary: string;\n  onPrimaryContainer: string;\n  onSecondary: string;\n  onSecondaryContainer: string;\n  onTertiary: string;\n  onTertiaryContainer: string;\n  onSurface: string;\n  onSurfaceVariant: string;\n  onSurfaceDisabled: string;\n  onError: string;\n  onErrorContainer: string;\n  onBackground: string;\n  outline: string;\n  outlineVariant: string;\n  inverseSurface: string;\n  inverseOnSurface: string;\n  inversePrimary: string;\n  shadow: string;\n  scrim: string;\n  backdrop: string;\n  elevation: MD3ElevationColors;\n};\n\nexport type MD3AndroidColors = {\n  primary: number;\n  primaryContainer: number;\n  secondary: number;\n  secondaryContainer: number;\n  tertiary: number;\n  tertiaryContainer: number;\n  surface: number;\n  surfaceVariant: number;\n  background: number;\n  error: number;\n  errorContainer: number;\n  onPrimary: number;\n  onPrimaryContainer: number;\n  onSecondary: number;\n  onSecondaryContainer: number;\n  onTertiary: number;\n  onTertiaryContainer: number;\n  onSurface: number;\n  onSurfaceVariant: number;\n  onError: number;\n  onErrorContainer: number;\n  onBackground: number;\n  outline: number;\n  outlineVariant: number;\n  inverseSurface: number;\n  inverseOnSurface: number;\n  inversePrimary: number;\n  shadow: number;\n  scrim: number;\n};\n\nexport type MD3Palette = {};\n\nexport type ThemeProp = $DeepPartial<InternalTheme>;\n\nexport type ThemeBase = {\n  dark: boolean;\n  mode?: Mode;\n  roundness: number;\n  animation: {\n    scale: number;\n    defaultAnimationDuration?: number;\n  };\n};\n\nexport type MD3Theme = ThemeBase & {\n  version: 3;\n  isV3: true;\n  colors: MD3Colors;\n  fonts: MD3Typescale;\n};\n\nexport type MD2Theme = ThemeBase & {\n  version: 2;\n  isV3: false;\n  colors: MD2Colors;\n  fonts: Fonts;\n};\n\nexport type InternalTheme = MD2Theme | MD3Theme;\n\n// MD3 types\nexport enum MD3TypescaleKey {\n  displayLarge = 'displayLarge',\n  displayMedium = 'displayMedium',\n  displaySmall = 'displaySmall',\n\n  headlineLarge = 'headlineLarge',\n  headlineMedium = 'headlineMedium',\n  headlineSmall = 'headlineSmall',\n\n  titleLarge = 'titleLarge',\n  titleMedium = 'titleMedium',\n  titleSmall = 'titleSmall',\n\n  labelLarge = 'labelLarge',\n  labelMedium = 'labelMedium',\n  labelSmall = 'labelSmall',\n\n  bodyLarge = 'bodyLarge',\n  bodyMedium = 'bodyMedium',\n  bodySmall = 'bodySmall',\n}\n\nexport type MD3Type = {\n  fontFamily: string;\n  letterSpacing: number;\n  fontWeight: Font['fontWeight'];\n  lineHeight: number;\n  fontSize: number;\n  fontStyle?: Font['fontStyle'];\n};\n\nexport type MD3Typescale =\n  | {\n      [key in MD3TypescaleKey]: MD3Type;\n    } & {\n      ['default']: Omit<MD3Type, 'lineHeight' | 'fontSize'>;\n    };\n\nexport type MD3Elevation = 0 | 1 | 2 | 3 | 4 | 5;\n\nexport enum ElevationLevels {\n  'level0',\n  'level1',\n  'level2',\n  'level3',\n  'level4',\n  'level5',\n}\n\nexport type MD3ElevationColors = {\n  [key in keyof typeof ElevationLevels]: string;\n};\n\nexport type $Omit<T, K> = Pick<T, Exclude<keyof T, K>>;\nexport type $RemoveChildren<T extends React.ComponentType<any>> = $Omit<\n  React.ComponentPropsWithoutRef<T>,\n  'children'\n>;\n\nexport type EllipsizeProp = 'head' | 'middle' | 'tail' | 'clip';\n\nexport type NavigationTheme = {\n  dark: boolean;\n  colors: {\n    primary: string;\n    background: string;\n    card: string;\n    text: string;\n    border: string;\n    notification: string;\n  };\n};\n"], "mappings": "AAgJA,WAAYA,eAAe,aAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAA,OAAfA,eAAe;AAAA;AAwC3B,WAAYC,eAAe,aAAfA,eAAe;EAAfA,eAAe,CAAfA,eAAe;EAAfA,eAAe,CAAfA,eAAe;EAAfA,eAAe,CAAfA,eAAe;EAAfA,eAAe,CAAfA,eAAe;EAAfA,eAAe,CAAfA,eAAe;EAAfA,eAAe,CAAfA,eAAe;EAAA,OAAfA,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}