{"ast": null, "code": "import * as React from 'react';\nexport var ToggleButtonGroupContext = React.createContext(null);\nvar ToggleButtonGroup = function ToggleButtonGroup(_ref) {\n  var value = _ref.value,\n    onValueChange = _ref.onValueChange,\n    children = _ref.children;\n  return React.createElement(ToggleButtonGroupContext.Provider, {\n    value: {\n      value: value,\n      onValueChange: onValueChange\n    }\n  }, children);\n};\nToggleButtonGroup.displayName = 'ToggleButton.Group';\nexport default ToggleButtonGroup;\nexport { ToggleButtonGroup };", "map": {"version": 3, "names": ["React", "ToggleButtonGroupContext", "createContext", "ToggleButtonGroup", "_ref", "value", "onValueChange", "children", "createElement", "Provider", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/ToggleButton/ToggleButtonGroup.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport type Props<Value = string> = {\n  /**\n   * Function to execute on selection change.\n   */\n  onValueChange: (value: Value) => void;\n  /**\n   * Value of the currently selected toggle button.\n   */\n  value: Value | null;\n  /**\n   * React elements containing toggle buttons.\n   */\n  children: React.ReactNode;\n};\n\ntype ToggleButtonContextType<Value> = {\n  value: Value | null;\n  onValueChange: (item: Value) => void;\n};\n\nexport const ToggleButtonGroupContext =\n  //@ts-expect-error: TS can't ensure the type from Group to children\n  React.createContext<ToggleButtonContextType>(null as any);\n\n/**\n * Toggle group allows to control a group of toggle buttons.</br>\n * It doesn't change the appearance of the toggle buttons. If you want to group them in a row, check out [ToggleButton.Row](ToggleButtonRow).\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { ToggleButton } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [value, setValue] = React.useState('left');\n *\n *   return (\n *     <ToggleButton.Group\n *       onValueChange={value => setValue(value)}\n *       value={value}>\n *       <ToggleButton icon=\"format-align-left\" value=\"left\" />\n *       <ToggleButton icon=\"format-align-right\" value=\"right\" />\n *     </ToggleButton.Group>\n *   );\n * };\n *\n * export default MyComponent;\n *```\n */\nconst ToggleButtonGroup = <Value = string,>({\n  value,\n  onValueChange,\n  children,\n}: Props<Value>) => (\n  <ToggleButtonGroupContext.Provider\n    value={{\n      value,\n      onValueChange,\n    }}\n  >\n    {children}\n  </ToggleButtonGroupContext.Provider>\n);\n\nToggleButtonGroup.displayName = 'ToggleButton.Group';\n\nexport default ToggleButtonGroup;\n\n// @component-docs ignore-next-line\nexport { ToggleButtonGroup };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAsB9B,OAAO,IAAMC,wBAAwB,GAEnCD,KAAK,CAACE,aAAa,CAA0B,IAAW,CAAC;AA2B3D,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAGC,IAAA;EAAA,IACxBC,KAAK,GAGQD,IAAA,CAHbC,KAAK;IACLC,aAAa,GAEAF,IAAA,CAFbE,aAAa;IACbC,QAAA,GACaH,IAAA,CADbG,QAAA;EACa,OACbP,KAAA,CAAAQ,aAAA,CAACP,wBAAwB,CAACQ,QAAQ;IAChCJ,KAAK,EAAE;MACLA,KAAK,EAALA,KAAK;MACLC,aAAA,EAAAA;IACF;EAAE,GAEDC,QACgC,CAAC;AAAA,CACrC;AAEDJ,iBAAiB,CAACO,WAAW,GAAG,oBAAoB;AAEpD,eAAeP,iBAAiB;AAGhC,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}