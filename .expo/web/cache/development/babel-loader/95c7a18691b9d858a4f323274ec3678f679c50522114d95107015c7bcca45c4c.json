{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-sqlite/src/SQLite.types.ts"], "sourcesContent": ["// Definitions copied from `@types/websql` as we want\n// to expose a custom version of the API that:\n// - uses primitive `string` instead of `String`\n// - excludes some methods that are not exposed by our API.\n//\n// Original definitions by: TeamworkGuy2 <https://github.com/TeamworkGuy2>\n\n// @docsMissing\nexport interface Window {\n  openDatabase?: (\n    name: string,\n    version: string,\n    displayName: string,\n    estimatedSize: number,\n    creationCallback?: DatabaseCallback\n  ) => Database;\n}\n\n// @docsMissing\nexport type DatabaseCallback = (database: Database) => void;\n\n// @needsAudit @docsMissing\n/**\n * `Database` objects are returned by calls to `SQLite.openDatabase()`. Such an object represents a\n * connection to a database on your device.\n */\nexport interface Database {\n  version: string;\n\n  /**\n   * Execute a database transaction.\n   * @param callback A function representing the transaction to perform. Takes a Transaction\n   * (see below) as its only parameter, on which it can add SQL statements to execute.\n   * @param errorCallback Called if an error occurred processing this transaction. Takes a single\n   * parameter describing the error.\n   * @param successCallback Called when the transaction has completed executing on the database.\n   */\n  transaction(\n    callback: SQLTransactionCallback,\n    errorCallback?: SQLTransactionErrorCallback,\n    successCallback?: () => void\n  ): void;\n\n  readTransaction(\n    callback: SQLTransactionCallback,\n    errorCallback?: SQLTransactionErrorCallback,\n    successCallback?: () => void\n  ): void;\n}\n\n// @docsMissing\nexport type SQLTransactionCallback = (transaction: SQLTransaction) => void;\n\n// @docsMissing\nexport type SQLTransactionErrorCallback = (error: SQLError) => void;\n\n// @needsAudit\n/**\n * A `SQLTransaction` object is passed in as a parameter to the `callback` parameter for the\n * `db.transaction()` method on a `Database` (see above). It allows enqueuing SQL statements to\n * perform in a database transaction.\n */\nexport interface SQLTransaction {\n  /**\n   * Enqueue a SQL statement to execute in the transaction. Authors are strongly recommended to make\n   * use of the `?` placeholder feature of the method to avoid against SQL injection attacks, and to\n   * never construct SQL statements on the fly.\n   * @param sqlStatement A string containing a database query to execute expressed as SQL. The string\n   * may contain `?` placeholders, with values to be substituted listed in the `arguments` parameter.\n   * @param args An array of values (numbers, strings or nulls) to substitute for `?` placeholders in the\n   * SQL statement.\n   * @param callback Called when the query is successfully completed during the transaction. Takes\n   * two parameters: the transaction itself, and a `ResultSet` object (see below) with the results\n   * of the query.\n   * @param errorCallback Called if an error occurred executing this particular query in the\n   * transaction. Takes two parameters: the transaction itself, and the error object.\n   */\n  executeSql(\n    sqlStatement: string,\n    args?: (number | string | null)[],\n    callback?: SQLStatementCallback,\n    errorCallback?: SQLStatementErrorCallback\n  ): void;\n}\n\n// @docsMissing\nexport type SQLStatementCallback = (transaction: SQLTransaction, resultSet: SQLResultSet) => void;\n\n// @docsMissing\nexport type SQLStatementErrorCallback = (transaction: SQLTransaction, error: SQLError) => boolean;\n\n// @needsAudit\nexport type SQLResultSet = {\n  /**\n   * The row ID of the row that the SQL statement inserted into the database, if a row was inserted.\n   */\n  insertId?: number;\n  /**\n   * The number of rows that were changed by the SQL statement.\n   */\n  rowsAffected: number;\n  rows: SQLResultSetRowList;\n};\n\n// @needsAudit\nexport interface SQLResultSetRowList {\n  /**\n   * The number of rows returned by the query.\n   */\n  length: number;\n  /**\n   * Returns the row with the given `index`. If there is no such row, returns `null`.\n   * @param index Index of row to get.\n   */\n  item(index: number): any;\n  /**\n   * The actual array of rows returned by the query. Can be used directly instead of\n   * getting rows through rows.item().\n   */\n  _array: any[];\n}\n\n// @docsMissing\nexport declare class SQLError {\n  static UNKNOWN_ERR: number;\n  static DATABASE_ERR: number;\n  static VERSION_ERR: number;\n  static TOO_LARGE_ERR: number;\n  static QUOTA_ERR: number;\n  static SYNTAX_ERR: number;\n  static CONSTRAINT_ERR: number;\n  static TIMEOUT_ERR: number;\n\n  code: number;\n  message: string;\n}\n\n/** @deprecated Use `SQLiteDatabase` instead. */\nexport interface WebSQLDatabase extends Database {\n  exec(queries: Query[], readOnly: boolean, callback: SQLiteCallback): void;\n\n  /**\n   * Close the database.\n   */\n  closeAsync(): void;\n\n  /**\n   * Delete the database file.\n   * > The database has to be closed prior to deletion.\n   */\n  deleteAsync(): Promise<void>;\n}\n\n// @docsMissing\nexport type Query = { sql: string; args: unknown[] };\n\n// @docsMissing\nexport interface ResultSetError {\n  error: Error;\n}\n\n// @needsAudit\n/**\n * `ResultSet` objects are returned through second parameter of the `success` callback for the\n * `tx.executeSql()` method on a `SQLTransaction` (see above).\n */\nexport interface ResultSet {\n  /**\n   * The row ID of the row that the SQL statement inserted into the database, if a row was inserted.\n   */\n  insertId?: number;\n  /**\n   * The number of rows that were changed by the SQL statement.\n   */\n  rowsAffected: number;\n  rows: { [column: string]: any }[];\n}\n\n// @docsMissing\nexport type SQLiteCallback = (\n  error?: Error | null,\n  resultSet?: (ResultSetError | ResultSet)[]\n) => void;\n\n/** A transaction object to perform SQL statements in async mode. */\nexport interface SQLTransactionAsync {\n  /** Executes a SQL statement in async mode. */\n  executeSqlAsync(sqlStatement: string, args?: (number | string)[]): Promise<ResultSet>;\n}\n\n/** A transaction callback with given `SQLTransactionAsync` object to perform SQL statements in async mode. */\nexport type SQLTransactionAsyncCallback = (transaction: SQLTransactionAsync) => Promise<void>;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}