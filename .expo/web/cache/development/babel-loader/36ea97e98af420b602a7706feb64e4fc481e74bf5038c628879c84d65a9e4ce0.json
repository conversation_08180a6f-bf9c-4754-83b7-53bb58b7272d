{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { PureComponent } from 'react';\nimport createIconSet from \"./createIconSet\";\nexport default function createMultiStyleIconSet(styles) {\n  var optionsInput = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var styleNames = Object.keys(styles);\n  if (styleNames.length === 0) {\n    throw new Error('You need to add at least one style');\n  }\n  var options = _objectSpread({\n    defaultStyle: styleNames[0],\n    fallbackFamily: function fallbackFamily(_unused) {\n      return styleNames[0];\n    },\n    glyphValidator: function glyphValidator(_unused, __unused) {\n      return true;\n    }\n  }, optionsInput);\n  var iconSets = styleNames.reduce(function (acc, name) {\n    var style = styles[name];\n    acc[name] = createIconSet(style.glyphMap || {}, style.fontFamily || '', style.fontFile || '', style.fontStyle || {});\n    return acc;\n  }, {});\n  function styleFromProps(props) {\n    return Object.keys(props).reduce(function (result, propName) {\n      return styleNames.indexOf(propName) !== -1 && props[propName] === true ? propName : result;\n    }, options.defaultStyle);\n  }\n  function getIconSetForProps(props) {\n    var name = props.name;\n    var style = styleFromProps(props);\n    if (options.glyphValidator(name, style)) return iconSets[style];\n    var family = options.fallbackFamily(name);\n    if (styleNames.indexOf(family) === -1) {\n      return options.defaultStyle;\n    }\n    return iconSets[family];\n  }\n  function selectIconClass(iconSet, iconClass) {\n    return iconClass.length > 0 ? iconSet[iconClass] : iconSet;\n  }\n  function reduceProps(props) {\n    return Object.keys(props).reduce(function (acc, prop) {\n      if (styleNames.indexOf(prop) === -1) {\n        acc[prop] = props[prop];\n      }\n      return acc;\n    }, {});\n  }\n  function getStyledIconSet(style) {\n    var name = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    if (styleNames.indexOf(style) === -1) {\n      return iconSets[options.defaultStyle];\n    }\n    return !name ? iconSets[styleFromProps(_defineProperty({}, style, true))] : getIconSetForProps(_defineProperty({\n      name: name\n    }, style, true));\n  }\n  function getFontFamily() {\n    var style = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : options.defaultStyle;\n    return getStyledIconSet(style).getFontFamily();\n  }\n  function getRawGlyphMap() {\n    var style = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : options.defaultStyle;\n    return getStyledIconSet(style).getRawGlyphMap();\n  }\n  function hasIcon(name) {\n    var style = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : options.defaultStyle;\n    return options.glyphValidator(name, style);\n  }\n  function createStyledIconClass() {\n    var selectClass = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var IconClass = function (_PureComponent) {\n      function IconClass() {\n        _classCallCheck(this, IconClass);\n        return _callSuper(this, IconClass, arguments);\n      }\n      _inherits(IconClass, _PureComponent);\n      return _createClass(IconClass, [{\n        key: \"render\",\n        value: function render() {\n          var selectedIconSet = getIconSetForProps(this.props);\n          var SelectedIconClass = selectIconClass(selectedIconSet, selectClass);\n          var props = reduceProps(this.props);\n          return React.createElement(SelectedIconClass, props);\n        }\n      }]);\n    }(PureComponent);\n    IconClass.defaultProps = styleNames.reduce(function (acc, name) {\n      acc[name] = false;\n      return acc;\n    }, {});\n    IconClass.font = Object.values(styles).reduce(function (acc, style) {\n      acc[style.fontFamily] = style.fontFile;\n      return acc;\n    }, {});\n    IconClass.StyledIconSet = getStyledIconSet;\n    IconClass.getFontFamily = getFontFamily;\n    IconClass.getRawGlyphMap = getRawGlyphMap;\n    IconClass.hasIcon = hasIcon;\n    return IconClass;\n  }\n  var Icon = createStyledIconClass();\n  Icon.Button = createStyledIconClass('Button');\n  return Icon;\n}", "map": {"version": 3, "names": ["React", "PureComponent", "createIconSet", "createMultiStyleIconSet", "styles", "optionsInput", "arguments", "length", "undefined", "styleNames", "Object", "keys", "Error", "options", "_objectSpread", "defaultStyle", "fallbackFamily", "_unused", "glyphValidator", "__unused", "iconSets", "reduce", "acc", "name", "style", "glyphMap", "fontFamily", "fontFile", "fontStyle", "styleFromProps", "props", "result", "propName", "indexOf", "getIconSetForProps", "family", "selectIconClass", "iconSet", "iconClass", "reduceProps", "prop", "getStyledIconSet", "_defineProperty", "getFontFamily", "getRawGlyphMap", "hasIcon", "createStyledIconClass", "selectClass", "IconClass", "_PureComponent", "_classCallCheck", "_callSuper", "_inherits", "_createClass", "key", "value", "render", "selectedIconSet", "SelectedIconClass", "createElement", "defaultProps", "font", "values", "StyledIconSet", "Icon", "<PERSON><PERSON>"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/src/createMultiStyleIconSet.ts"], "sourcesContent": ["import React, { PureComponent } from 'react';\n\nimport createIconSet from './createIconSet';\n\ntype FontStyle = {\n  fontFamily: string;\n  fontFile: any;\n  glyphMap: any;\n  fontStyle: any;\n};\n\ntype FontStyles = {\n  [key: string]: FontStyle;\n};\n\nexport default function createMultiStyleIconSet(\n  styles: FontStyles,\n  optionsInput = {}\n): any {\n  const styleNames = Object.keys(styles);\n\n  if (styleNames.length === 0) {\n    throw new Error('You need to add at least one style');\n  }\n\n  const options = {\n    defaultStyle: styleNames[0],\n    fallbackFamily: (_unused: any) => styleNames[0],\n    glyphValidator: (_unused: any, __unused: any) => true,\n    ...optionsInput,\n  };\n\n  const iconSets = styleNames.reduce((acc, name) => {\n    const style = styles[name];\n\n    acc[name] = createIconSet(\n      style.glyphMap || {},\n      style.fontFamily || '',\n      style.fontFile || '',\n      style.fontStyle || {}\n    );\n\n    return acc;\n  }, {});\n\n  function styleFromProps(props) {\n    return Object.keys(props).reduce(\n      (result, propName) =>\n        styleNames.indexOf(propName) !== -1 && props[propName] === true\n          ? propName\n          : result,\n      options.defaultStyle\n    );\n  }\n\n  function getIconSetForProps(props) {\n    const { name } = props;\n    const style = styleFromProps(props);\n\n    if (options.glyphValidator(name, style)) return iconSets[style];\n\n    const family = options.fallbackFamily(name);\n\n    if (styleNames.indexOf(family) === -1) {\n      return options.defaultStyle;\n    }\n\n    return iconSets[family];\n  }\n\n  function selectIconClass(iconSet, iconClass) {\n    return iconClass.length > 0 ? iconSet[iconClass] : iconSet;\n  }\n\n  function reduceProps(props) {\n    return Object.keys(props).reduce((acc, prop) => {\n      if (styleNames.indexOf(prop) === -1) {\n        acc[prop] = props[prop];\n      }\n\n      return acc;\n    }, {});\n  }\n\n  function getStyledIconSet(style, name = '') {\n    if (styleNames.indexOf(style) === -1) {\n      return iconSets[options.defaultStyle];\n    }\n\n    return !name\n      ? iconSets[styleFromProps({ [style]: true })]\n      : getIconSetForProps({ name, [style]: true });\n  }\n\n  function getFontFamily(style = options.defaultStyle) {\n    return getStyledIconSet(style).getFontFamily();\n  }\n\n  function getRawGlyphMap(style = options.defaultStyle) {\n    return getStyledIconSet(style).getRawGlyphMap();\n  }\n\n  function hasIcon(name, style = options.defaultStyle) {\n    return options.glyphValidator(name, style);\n  }\n\n  function createStyledIconClass(selectClass = '') {\n    class IconClass extends PureComponent {\n      static defaultProps = styleNames.reduce((acc, name) => {\n        acc[name] = false;\n        return acc;\n      }, {});\n\n      static font = Object.values(styles).reduce((acc, style) => {\n        acc[style.fontFamily] = style.fontFile;\n        return acc;\n      }, {});\n\n      static Button: any;\n\n      static StyledIconSet = getStyledIconSet;\n      static getFontFamily = getFontFamily;\n      static getRawGlyphMap = getRawGlyphMap;\n      static hasIcon = hasIcon;\n\n      render() {\n        const selectedIconSet = getIconSetForProps(this.props);\n        const SelectedIconClass = selectIconClass(selectedIconSet, selectClass);\n        const props = reduceProps(this.props);\n\n        return React.createElement(SelectedIconClass, props);\n      }\n    }\n\n    return IconClass;\n  }\n\n  const Icon = createStyledIconClass();\n  Icon.Button = createStyledIconClass('Button');\n  return Icon;\n}\n"], "mappings": ";;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAE5C,OAAOC,aAAa;AAapB,eAAc,SAAUC,uBAAuBA,CAC7CC,MAAkB,EACD;EAAA,IAAjBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAEjB,IAAMG,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACP,MAAM,CAAC;EAEtC,IAAIK,UAAU,CAACF,MAAM,KAAK,CAAC,EAAE;IAC3B,MAAM,IAAIK,KAAK,CAAC,oCAAoC,CAAC;;EAGvD,IAAMC,OAAO,GAAAC,aAAA;IACXC,YAAY,EAAEN,UAAU,CAAC,CAAC,CAAC;IAC3BO,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,OAAY;MAAA,OAAKR,UAAU,CAAC,CAAC,CAAC;IAAA;IAC/CS,cAAc,EAAE,SAAhBA,cAAcA,CAAGD,OAAY,EAAEE,QAAa;MAAA,OAAK,IAAI;IAAA;EAAA,GAClDd,YAAY,CAChB;EAED,IAAMe,QAAQ,GAAGX,UAAU,CAACY,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAI;IAC/C,IAAMC,KAAK,GAAGpB,MAAM,CAACmB,IAAI,CAAC;IAE1BD,GAAG,CAACC,IAAI,CAAC,GAAGrB,aAAa,CACvBsB,KAAK,CAACC,QAAQ,IAAI,EAAE,EACpBD,KAAK,CAACE,UAAU,IAAI,EAAE,EACtBF,KAAK,CAACG,QAAQ,IAAI,EAAE,EACpBH,KAAK,CAACI,SAAS,IAAI,EAAE,CACtB;IAED,OAAON,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,SAASO,cAAcA,CAACC,KAAK;IAC3B,OAAOpB,MAAM,CAACC,IAAI,CAACmB,KAAK,CAAC,CAACT,MAAM,CAC9B,UAACU,MAAM,EAAEC,QAAQ;MAAA,OACfvB,UAAU,CAACwB,OAAO,CAACD,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,KAAK,IAAI,GAC3DA,QAAQ,GACRD,MAAM;IAAA,GACZlB,OAAO,CAACE,YAAY,CACrB;EACH;EAEA,SAASmB,kBAAkBA,CAACJ,KAAK;IAC/B,IAAQP,IAAI,GAAKO,KAAK,CAAdP,IAAI;IACZ,IAAMC,KAAK,GAAGK,cAAc,CAACC,KAAK,CAAC;IAEnC,IAAIjB,OAAO,CAACK,cAAc,CAACK,IAAI,EAAEC,KAAK,CAAC,EAAE,OAAOJ,QAAQ,CAACI,KAAK,CAAC;IAE/D,IAAMW,MAAM,GAAGtB,OAAO,CAACG,cAAc,CAACO,IAAI,CAAC;IAE3C,IAAId,UAAU,CAACwB,OAAO,CAACE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MACrC,OAAOtB,OAAO,CAACE,YAAY;;IAG7B,OAAOK,QAAQ,CAACe,MAAM,CAAC;EACzB;EAEA,SAASC,eAAeA,CAACC,OAAO,EAAEC,SAAS;IACzC,OAAOA,SAAS,CAAC/B,MAAM,GAAG,CAAC,GAAG8B,OAAO,CAACC,SAAS,CAAC,GAAGD,OAAO;EAC5D;EAEA,SAASE,WAAWA,CAACT,KAAK;IACxB,OAAOpB,MAAM,CAACC,IAAI,CAACmB,KAAK,CAAC,CAACT,MAAM,CAAC,UAACC,GAAG,EAAEkB,IAAI,EAAI;MAC7C,IAAI/B,UAAU,CAACwB,OAAO,CAACO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACnClB,GAAG,CAACkB,IAAI,CAAC,GAAGV,KAAK,CAACU,IAAI,CAAC;;MAGzB,OAAOlB,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EAEA,SAASmB,gBAAgBA,CAACjB,KAAK,EAAW;IAAA,IAATD,IAAI,GAAAjB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IACxC,IAAIG,UAAU,CAACwB,OAAO,CAACT,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;MACpC,OAAOJ,QAAQ,CAACP,OAAO,CAACE,YAAY,CAAC;;IAGvC,OAAO,CAACQ,IAAI,GACRH,QAAQ,CAACS,cAAc,CAAAa,eAAA,KAAIlB,KAAK,EAAG,IAAI,CAAE,CAAC,CAAC,GAC3CU,kBAAkB,CAAAQ,eAAA;MAAGnB,IAAI,EAAJA;IAAI,GAAGC,KAAK,EAAG,IAAI,CAAE,CAAC;EACjD;EAEA,SAASmB,aAAaA,CAAA,EAA6B;IAAA,IAA5BnB,KAAK,GAAAlB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGO,OAAO,CAACE,YAAY;IACjD,OAAO0B,gBAAgB,CAACjB,KAAK,CAAC,CAACmB,aAAa,EAAE;EAChD;EAEA,SAASC,cAAcA,CAAA,EAA6B;IAAA,IAA5BpB,KAAK,GAAAlB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGO,OAAO,CAACE,YAAY;IAClD,OAAO0B,gBAAgB,CAACjB,KAAK,CAAC,CAACoB,cAAc,EAAE;EACjD;EAEA,SAASC,OAAOA,CAACtB,IAAI,EAA8B;IAAA,IAA5BC,KAAK,GAAAlB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGO,OAAO,CAACE,YAAY;IACjD,OAAOF,OAAO,CAACK,cAAc,CAACK,IAAI,EAAEC,KAAK,CAAC;EAC5C;EAEA,SAASsB,qBAAqBA,CAAA,EAAiB;IAAA,IAAhBC,WAAW,GAAAzC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAAA,IACvC0C,SAAU,aAAAC,cAAA;MAAA,SAAAD,UAAA;QAAAE,eAAA,OAAAF,SAAA;QAAA,OAAAG,UAAA,OAAAH,SAAA,EAAA1C,SAAA;MAAA;MAAA8C,SAAA,CAAAJ,SAAA,EAAAC,cAAA;MAAA,OAAAI,YAAA,CAAAL,SAAA;QAAAM,GAAA;QAAAC,KAAA,EAkBd,SAAAC,MAAMA,CAAA;UACJ,IAAMC,eAAe,GAAGvB,kBAAkB,CAAC,IAAI,CAACJ,KAAK,CAAC;UACtD,IAAM4B,iBAAiB,GAAGtB,eAAe,CAACqB,eAAe,EAAEV,WAAW,CAAC;UACvE,IAAMjB,KAAK,GAAGS,WAAW,CAAC,IAAI,CAACT,KAAK,CAAC;UAErC,OAAO9B,KAAK,CAAC2D,aAAa,CAACD,iBAAiB,EAAE5B,KAAK,CAAC;QACtD;MAAC;IAAA,EAxBqB7B,aAAa;IAC5B+C,SAAA,CAAAY,YAAY,GAAGnD,UAAU,CAACY,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAI;MACpDD,GAAG,CAACC,IAAI,CAAC,GAAG,KAAK;MACjB,OAAOD,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAEC0B,SAAA,CAAAa,IAAI,GAAGnD,MAAM,CAACoD,MAAM,CAAC1D,MAAM,CAAC,CAACiB,MAAM,CAAC,UAACC,GAAG,EAAEE,KAAK,EAAI;MACxDF,GAAG,CAACE,KAAK,CAACE,UAAU,CAAC,GAAGF,KAAK,CAACG,QAAQ;MACtC,OAAOL,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAIC0B,SAAA,CAAAe,aAAa,GAAGtB,gBAAgB;IAChCO,SAAA,CAAAL,aAAa,GAAGA,aAAa;IAC7BK,SAAA,CAAAJ,cAAc,GAAGA,cAAc;IAC/BI,SAAA,CAAAH,OAAO,GAAGA,OAAO;IAW1B,OAAOG,SAAS;EAClB;EAEA,IAAMgB,IAAI,GAAGlB,qBAAqB,EAAE;EACpCkB,IAAI,CAACC,MAAM,GAAGnB,qBAAqB,CAAC,QAAQ,CAAC;EAC7C,OAAOkB,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}