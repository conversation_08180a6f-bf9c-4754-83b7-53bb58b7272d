{"ast": null, "code": "export var roundLayoutSize = function roundLayoutSize(size) {\n  return Math.round(size * 1000) / 1000;\n};", "map": {"version": 3, "names": ["roundLayoutSize", "size", "Math", "round"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-paper/src/utils/roundLayoutSize.ts"], "sourcesContent": ["export const roundLayoutSize = (size: number): number =>\n  Math.round(size * 1000) / 1000;\n"], "mappings": "AAAA,OAAO,IAAMA,eAAe,GAAI,SAAnBA,eAAeA,CAAIC,IAAY;EAAA,OAC1CC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}