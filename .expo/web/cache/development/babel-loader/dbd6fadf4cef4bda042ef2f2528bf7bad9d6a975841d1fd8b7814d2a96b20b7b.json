{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ensureGetImageAvailable = void 0;\nvar NativeIconAPI = null;\ntry {\n  NativeIconAPI = require('@react-native-vector-icons/get-image');\n} catch (_unused) {}\nvar ensureGetImageAvailable = function ensureGetImageAvailable() {\n  if (!NativeIconAPI) {\n    throw new Error('Could not import @react-native-vector-icons/get-image, did you install it? It is required for getImageSource*');\n  }\n  NativeIconAPI.ensureNativeModuleAvailable();\n  return NativeIconAPI;\n};\nexports.ensureGetImageAvailable = ensureGetImageAvailable;", "map": {"version": 3, "names": ["NativeIconAPI", "require", "_unused", "ensureGetImageAvailable", "Error", "ensureNativeModuleAvailable", "exports"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-vector-icons/common/src/get-image-library.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-mutable-exports\nlet NativeIconAPI: typeof import('@react-native-vector-icons/get-image') | null = null;\n\ntry {\n  // eslint-disable-next-line global-require,import/no-extraneous-dependencies,@typescript-eslint/no-require-imports\n  NativeIconAPI = require('@react-native-vector-icons/get-image');\n} catch {\n  // No warning at this stage\n}\n\nexport const ensureGetImageAvailable = () => {\n  if (!NativeIconAPI) {\n    throw new Error(\n      'Could not import @react-native-vector-icons/get-image, did you install it? It is required for getImageSource*',\n    );\n  }\n\n  NativeIconAPI.ensureNativeModuleAvailable();\n\n  return NativeIconAPI;\n};\n"], "mappings": ";;;;;;AACA,IAAIA,aAA2E,GAAG,IAAI;AAEtF,IAAI;EAEFA,aAAa,GAAGC,OAAO,CAAC,sCAAsC,CAAC;AACjE,CAAC,CAAC,OAAAC,OAAA,EAAM,CACN;AAGK,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAS;EAC3C,IAAI,CAACH,aAAa,EAAE;IAClB,MAAM,IAAII,KAAK,CACb,+GACF,CAAC;EACH;EAEAJ,aAAa,CAACK,2BAA2B,CAAC,CAAC;EAE3C,OAAOL,aAAa;AACtB,CAAC;AAACM,OAAA,CAAAH,uBAAA,GAAAA,uBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}