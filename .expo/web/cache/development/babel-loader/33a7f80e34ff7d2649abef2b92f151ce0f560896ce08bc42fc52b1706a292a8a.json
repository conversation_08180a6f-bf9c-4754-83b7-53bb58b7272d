{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"animating\", \"color\", \"hidesWhenStopped\", \"size\", \"style\"];\nimport * as React from 'react';\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nvar createSvgCircle = function createSvgCircle(style) {\n  return React.createElement(\"circle\", {\n    cx: \"16\",\n    cy: \"16\",\n    fill: \"none\",\n    r: \"14\",\n    strokeWidth: \"4\",\n    style: style\n  });\n};\nvar ActivityIndicator = React.forwardRef(function (props, forwardedRef) {\n  var _props$animating = props.animating,\n    animating = _props$animating === void 0 ? true : _props$animating,\n    _props$color = props.color,\n    color = _props$color === void 0 ? '#1976D2' : _props$color,\n    _props$hidesWhenStopp = props.hidesWhenStopped,\n    hidesWhenStopped = _props$hidesWhenStopp === void 0 ? true : _props$hidesWhenStopp,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'small' : _props$size,\n    style = props.style,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var svg = React.createElement(\"svg\", {\n    height: \"100%\",\n    viewBox: \"0 0 32 32\",\n    width: \"100%\"\n  }, createSvgCircle({\n    stroke: color,\n    opacity: 0.2\n  }), createSvgCircle({\n    stroke: color,\n    strokeDasharray: 80,\n    strokeDashoffset: 60\n  }));\n  return React.createElement(View, _extends({}, other, {\n    \"aria-valuemax\": 1,\n    \"aria-valuemin\": 0,\n    ref: forwardedRef,\n    role: \"progressbar\",\n    style: [styles.container, style]\n  }), React.createElement(View, {\n    children: svg,\n    style: [typeof size === 'number' ? {\n      height: size,\n      width: size\n    } : indicatorSizes[size], styles.animation, !animating && styles.animationPause, !animating && hidesWhenStopped && styles.hidesWhenStopped]\n  }));\n});\nActivityIndicator.displayName = 'ActivityIndicator';\nvar styles = StyleSheet.create({\n  container: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  hidesWhenStopped: {\n    visibility: 'hidden'\n  },\n  animation: {\n    animationDuration: '0.75s',\n    animationKeyframes: [{\n      '0%': {\n        transform: 'rotate(0deg)'\n      },\n      '100%': {\n        transform: 'rotate(360deg)'\n      }\n    }],\n    animationTimingFunction: 'linear',\n    animationIterationCount: 'infinite'\n  },\n  animationPause: {\n    animationPlayState: 'paused'\n  }\n});\nvar indicatorSizes = StyleSheet.create({\n  small: {\n    width: 20,\n    height: 20\n  },\n  large: {\n    width: 36,\n    height: 36\n  }\n});\nexport default ActivityIndicator;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "StyleSheet", "View", "createSvgCircle", "style", "createElement", "cx", "cy", "fill", "r", "strokeWidth", "ActivityIndicator", "forwardRef", "props", "forwardedRef", "_props$animating", "animating", "_props$color", "color", "_props$hidesWhenStopp", "hidesWhenStopped", "_props$size", "size", "other", "svg", "height", "viewBox", "width", "stroke", "opacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "ref", "role", "styles", "container", "children", "indicatorSizes", "animation", "animationPause", "displayName", "create", "alignItems", "justifyContent", "visibility", "animationDuration", "animationKeyframes", "transform", "animationTimingFunction", "animationIterationCount", "animationPlayState", "small", "large"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/ActivityIndicator/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"animating\", \"color\", \"hidesWhenStopped\", \"size\", \"style\"];\nimport * as React from 'react';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\nvar createSvgCircle = style => /*#__PURE__*/React.createElement(\"circle\", {\n  cx: \"16\",\n  cy: \"16\",\n  fill: \"none\",\n  r: \"14\",\n  strokeWidth: \"4\",\n  style: style\n});\nvar ActivityIndicator = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var _props$animating = props.animating,\n    animating = _props$animating === void 0 ? true : _props$animating,\n    _props$color = props.color,\n    color = _props$color === void 0 ? '#1976D2' : _props$color,\n    _props$hidesWhenStopp = props.hidesWhenStopped,\n    hidesWhenStopped = _props$hidesWhenStopp === void 0 ? true : _props$hidesWhenStopp,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'small' : _props$size,\n    style = props.style,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var svg = /*#__PURE__*/React.createElement(\"svg\", {\n    height: \"100%\",\n    viewBox: \"0 0 32 32\",\n    width: \"100%\"\n  }, createSvgCircle({\n    stroke: color,\n    opacity: 0.2\n  }), createSvgCircle({\n    stroke: color,\n    strokeDasharray: 80,\n    strokeDashoffset: 60\n  }));\n  return /*#__PURE__*/React.createElement(View, _extends({}, other, {\n    \"aria-valuemax\": 1,\n    \"aria-valuemin\": 0,\n    ref: forwardedRef,\n    role: \"progressbar\",\n    style: [styles.container, style]\n  }), /*#__PURE__*/React.createElement(View, {\n    children: svg,\n    style: [typeof size === 'number' ? {\n      height: size,\n      width: size\n    } : indicatorSizes[size], styles.animation, !animating && styles.animationPause, !animating && hidesWhenStopped && styles.hidesWhenStopped]\n  }));\n});\nActivityIndicator.displayName = 'ActivityIndicator';\nvar styles = StyleSheet.create({\n  container: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  hidesWhenStopped: {\n    visibility: 'hidden'\n  },\n  animation: {\n    animationDuration: '0.75s',\n    animationKeyframes: [{\n      '0%': {\n        transform: 'rotate(0deg)'\n      },\n      '100%': {\n        transform: 'rotate(360deg)'\n      }\n    }],\n    animationTimingFunction: 'linear',\n    animationIterationCount: 'infinite'\n  },\n  animationPause: {\n    animationPlayState: 'paused'\n  }\n});\nvar indicatorSizes = StyleSheet.create({\n  small: {\n    width: 20,\n    height: 20\n  },\n  large: {\n    width: 36,\n    height: 36\n  }\n});\nexport default ActivityIndicator;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,CAAC;AAC3E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,IAAIC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,KAAK;EAAA,OAAiBJ,KAAK,CAACK,aAAa,CAAC,QAAQ,EAAE;IACxEC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,MAAM;IACZC,CAAC,EAAE,IAAI;IACPC,WAAW,EAAE,GAAG;IAChBN,KAAK,EAAEA;EACT,CAAC,CAAC;AAAA;AACF,IAAIO,iBAAiB,GAAgBX,KAAK,CAACY,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAC7E,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACjEE,YAAY,GAAGJ,KAAK,CAACK,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,YAAY;IAC1DE,qBAAqB,GAAGN,KAAK,CAACO,gBAAgB;IAC9CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAClFE,WAAW,GAAGR,KAAK,CAACS,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,WAAW;IACrDjB,KAAK,GAAGS,KAAK,CAACT,KAAK;IACnBmB,KAAK,GAAGzB,6BAA6B,CAACe,KAAK,EAAEd,SAAS,CAAC;EACzD,IAAIyB,GAAG,GAAgBxB,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IAChDoB,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC,EAAExB,eAAe,CAAC;IACjByB,MAAM,EAAEV,KAAK;IACbW,OAAO,EAAE;EACX,CAAC,CAAC,EAAE1B,eAAe,CAAC;IAClByB,MAAM,EAAEV,KAAK;IACbY,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE;EACpB,CAAC,CAAC,CAAC;EACH,OAAoB/B,KAAK,CAACK,aAAa,CAACH,IAAI,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAE0B,KAAK,EAAE;IAChE,eAAe,EAAE,CAAC;IAClB,eAAe,EAAE,CAAC;IAClBS,GAAG,EAAElB,YAAY;IACjBmB,IAAI,EAAE,aAAa;IACnB7B,KAAK,EAAE,CAAC8B,MAAM,CAACC,SAAS,EAAE/B,KAAK;EACjC,CAAC,CAAC,EAAeJ,KAAK,CAACK,aAAa,CAACH,IAAI,EAAE;IACzCkC,QAAQ,EAAEZ,GAAG;IACbpB,KAAK,EAAE,CAAC,OAAOkB,IAAI,KAAK,QAAQ,GAAG;MACjCG,MAAM,EAAEH,IAAI;MACZK,KAAK,EAAEL;IACT,CAAC,GAAGe,cAAc,CAACf,IAAI,CAAC,EAAEY,MAAM,CAACI,SAAS,EAAE,CAACtB,SAAS,IAAIkB,MAAM,CAACK,cAAc,EAAE,CAACvB,SAAS,IAAII,gBAAgB,IAAIc,MAAM,CAACd,gBAAgB;EAC5I,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFT,iBAAiB,CAAC6B,WAAW,GAAG,mBAAmB;AACnD,IAAIN,MAAM,GAAGjC,UAAU,CAACwC,MAAM,CAAC;EAC7BN,SAAS,EAAE;IACTO,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDvB,gBAAgB,EAAE;IAChBwB,UAAU,EAAE;EACd,CAAC;EACDN,SAAS,EAAE;IACTO,iBAAiB,EAAE,OAAO;IAC1BC,kBAAkB,EAAE,CAAC;MACnB,IAAI,EAAE;QACJC,SAAS,EAAE;MACb,CAAC;MACD,MAAM,EAAE;QACNA,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IACFC,uBAAuB,EAAE,QAAQ;IACjCC,uBAAuB,EAAE;EAC3B,CAAC;EACDV,cAAc,EAAE;IACdW,kBAAkB,EAAE;EACtB;AACF,CAAC,CAAC;AACF,IAAIb,cAAc,GAAGpC,UAAU,CAACwC,MAAM,CAAC;EACrCU,KAAK,EAAE;IACLxB,KAAK,EAAE,EAAE;IACTF,MAAM,EAAE;EACV,CAAC;EACD2B,KAAK,EAAE;IACLzB,KAAK,EAAE,EAAE;IACTF,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AACF,eAAed,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}