{"ast": null, "code": "import * as React from 'react';\nexport default React.createContext(undefined);", "map": {"version": 3, "names": ["React", "createContext", "undefined"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/bottom-tabs/src/utils/BottomTabBarHeightCallbackContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport default React.createContext<((height: number) => void) | undefined>(\n  undefined\n);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,eAAeA,KAAK,CAACC,aAAa,CAChCC,SAAS,CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}