{"ast": null, "code": "import * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nexport var RadioButtonContext = React.createContext(null);\nvar RadioButtonGroup = function RadioButtonGroup(_ref) {\n  var value = _ref.value,\n    onValueChange = _ref.onValueChange,\n    children = _ref.children;\n  return React.createElement(RadioButtonContext.Provider, {\n    value: {\n      value: value,\n      onValueChange: onValueChange\n    }\n  }, React.createElement(View, {\n    accessibilityRole: \"radiogroup\"\n  }, children));\n};\nRadioButtonGroup.displayName = 'RadioButton.Group';\nexport default RadioButtonGroup;\nexport { RadioButtonGroup };", "map": {"version": 3, "names": ["React", "View", "RadioButtonContext", "createContext", "RadioButtonGroup", "_ref", "value", "onValueChange", "children", "createElement", "Provider", "accessibilityRole", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/RadioButton/RadioButtonGroup.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { View } from 'react-native';\n\nexport type Props = {\n  /**\n   * Function to execute on selection change.\n   */\n  onValueChange: (value: string) => void;\n  /**\n   * Value of the currently selected radio button.\n   */\n  value: string;\n  /**\n   * React elements containing radio buttons.\n   */\n  children: React.ReactNode;\n};\n\nexport type RadioButtonContextType = {\n  value: string;\n  onValueChange: (item: string) => void;\n};\n\nexport const RadioButtonContext = React.createContext<RadioButtonContextType>(\n  null as any\n);\n\n/**\n * Radio button group allows to control a group of radio buttons.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { RadioButton, Text } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [value, setValue] = React.useState('first');\n *\n *   return (\n *     <RadioButton.Group onValueChange={newValue => setValue(newValue)} value={value}>\n *       <View>\n *         <Text>First</Text>\n *         <RadioButton value=\"first\" />\n *       </View>\n *       <View>\n *         <Text>Second</Text>\n *         <RadioButton value=\"second\" />\n *       </View>\n *     </RadioButton.Group>\n *   );\n * };\n *\n * export default MyComponent;\n *```\n */\nconst RadioButtonGroup = ({ value, onValueChange, children }: Props) => (\n  <RadioButtonContext.Provider value={{ value, onValueChange }}>\n    <View accessibilityRole=\"radiogroup\">{children}</View>\n  </RadioButtonContext.Provider>\n);\n\nRadioButtonGroup.displayName = 'RadioButton.Group';\nexport default RadioButtonGroup;\n\n// @component-docs ignore-next-line\nexport { RadioButtonGroup };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAuB9B,OAAO,IAAMC,kBAAkB,GAAGF,KAAK,CAACG,aAAa,CACnD,IACF,CAAC;AA+BD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAGC,IAAA;EAAA,IAAGC,KAAK,GAAkCD,IAAA,CAAvCC,KAAK;IAAEC,aAAa,GAAmBF,IAAA,CAAhCE,aAAa;IAAEC,QAAA,GAAiBH,IAAA,CAAjBG,QAAA;EAAiB,OACjER,KAAA,CAAAS,aAAA,CAACP,kBAAkB,CAACQ,QAAQ;IAACJ,KAAK,EAAE;MAAEA,KAAK,EAALA,KAAK;MAAEC,aAAA,EAAAA;IAAc;EAAE,GAC3DP,KAAA,CAAAS,aAAA,CAACR,IAAI;IAACU,iBAAiB,EAAC;EAAY,GAAEH,QAAe,CAC1B,CAAC;AAAA,CAC/B;AAEDJ,gBAAgB,CAACQ,WAAW,GAAG,mBAAmB;AAClD,eAAeR,gBAAgB;AAG/B,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}