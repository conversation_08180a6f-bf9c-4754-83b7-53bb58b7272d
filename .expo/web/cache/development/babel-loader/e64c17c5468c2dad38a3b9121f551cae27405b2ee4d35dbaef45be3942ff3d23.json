{"ast": null, "code": "\"use client\";\n\nexport { default as AntDesign } from \"./AntDesign\";\nexport { default as <PERSON><PERSON><PERSON> } from \"./Entypo\";\nexport { default as EvilIcons } from \"./EvilIcons\";\nexport { default as Feather } from \"./Feather\";\nexport { default as <PERSON>ontisto } from \"./Fontisto\";\nexport { default as FontAwesome } from \"./FontAwesome\";\nexport { default as FontAwesome5 } from \"./FontAwesome5\";\nexport { default as FontAwesome6 } from \"./FontAwesome6\";\nexport { default as Foundation } from \"./Foundation\";\nexport { default as Ionicons } from \"./Ionicons\";\nexport { default as MaterialCommunityIcons } from \"./MaterialCommunityIcons\";\nexport { default as MaterialIcons } from \"./MaterialIcons\";\nexport { default as Octicons } from \"./Octicons\";\nexport { default as SimpleLineIcons } from \"./SimpleLineIcons\";\nexport { default as Zocial } from \"./Zocial\";\nexport { default as createMultiStyleIconSet } from \"./createMultiStyleIconSet\";\nexport { default as createIconSet } from \"./createIconSet\";\nexport { default as createIconSetFromFontello } from \"./createIconSetFromFontello\";\nexport { default as createIconSetFromIcoMoon } from \"./createIconSetFromIcoMoon\";", "map": {"version": 3, "names": ["default", "AntDesign", "<PERSON><PERSON><PERSON>", "EvilIcons", "<PERSON><PERSON>", "Fontisto", "FontAwesome", "FontAwesome5", "FontAwesome6", "Foundation", "Ionicons", "MaterialCommunityIcons", "MaterialIcons", "Octicons", "SimpleLineIcons", "Zocial", "createMultiStyleIconSet", "createIconSet", "createIconSetFromFontello", "createIconSetFromIcoMoon"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/@expo/vector-icons/src/Icons.ts"], "sourcesContent": ["\"use client\";\n\nexport { default as AntDesign } from './AntDesign';\nexport { default as <PERSON><PERSON><PERSON> } from './Entypo';\nexport { default as EvilIcons } from './EvilIcons';\nexport { default as Feather } from './Feather';\nexport { default as <PERSON>ontisto } from './Fontisto';\nexport { default as FontAwesome } from './FontAwesome';\nexport { default as FontAwesome5 } from './FontAwesome5';\nexport { default as FontAwesome6 } from './FontAwesome6';\nexport { default as Foundation } from './Foundation';\nexport { default as Ionicons } from './Ionicons';\nexport { default as MaterialCommunityIcons } from './MaterialCommunityIcons';\nexport { default as MaterialIcons } from './MaterialIcons';\nexport { default as Octicons } from './Octicons';\nexport { default as SimpleLineIcons } from './SimpleLineIcons';\nexport { default as Zocial } from './Zocial';\nexport { default as createMultiStyleIconSet } from './createMultiStyleIconSet';\nexport { default as createIconSet } from './createIconSet';\nexport { default as createIconSetFromFontello } from './createIconSetFromFontello';\nexport { default as createIconSetFromIcoMoon } from './createIconSetFromIcoMoon';\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,IAAIC,SAAS;AAC7B,SAASD,OAAO,IAAIE,MAAM;AAC1B,SAASF,OAAO,IAAIG,SAAS;AAC7B,SAASH,OAAO,IAAII,OAAO;AAC3B,SAASJ,OAAO,IAAIK,QAAQ;AAC5B,SAASL,OAAO,IAAIM,WAAW;AAC/B,SAASN,OAAO,IAAIO,YAAY;AAChC,SAASP,OAAO,IAAIQ,YAAY;AAChC,SAASR,OAAO,IAAIS,UAAU;AAC9B,SAAST,OAAO,IAAIU,QAAQ;AAC5B,SAASV,OAAO,IAAIW,sBAAsB;AAC1C,SAASX,OAAO,IAAIY,aAAa;AACjC,SAASZ,OAAO,IAAIa,QAAQ;AAC5B,SAASb,OAAO,IAAIc,eAAe;AACnC,SAASd,OAAO,IAAIe,MAAM;AAC1B,SAASf,OAAO,IAAIgB,uBAAuB;AAC3C,SAAShB,OAAO,IAAIiB,aAAa;AACjC,SAASjB,OAAO,IAAIkB,yBAAyB;AAC7C,SAASlB,OAAO,IAAImB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}