{"ast": null, "code": "export { default as Caption } from \"./Caption\";\nexport { default as Headline } from \"./Headline\";\nexport { default as Paragraph } from \"./Paragraph\";\nexport { default as Subheading } from \"./Subheading\";\nexport { default as Title } from \"./Title\";", "map": {"version": 3, "names": ["default", "Caption", "Headline", "Paragraph", "Subheading", "Title"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Typography/v2/index.ts"], "sourcesContent": ["export { default as Caption } from './Caption';\nexport { default as Headline } from './Headline';\nexport { default as Paragraph } from './Paragraph';\nexport { default as Subheading } from './Subheading';\nexport { default as Title } from './Title';\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,OAAO;AAC3B,SAASD,OAAO,IAAIE,QAAQ;AAC5B,SAASF,OAAO,IAAIG,SAAS;AAC7B,SAASH,OAAO,IAAII,UAAU;AAC9B,SAASJ,OAAO,IAAIK,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}