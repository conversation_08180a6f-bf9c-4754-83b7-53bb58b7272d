{"ast": null, "code": "'use strict';\n\nimport PressResponder from \"./PressResponder\";\nimport { useDebugValue, useEffect, useRef } from 'react';\nexport default function usePressEvents(hostRef, config) {\n  var pressResponderRef = useRef(null);\n  if (pressResponderRef.current == null) {\n    pressResponderRef.current = new PressResponder(config);\n  }\n  var pressResponder = pressResponderRef.current;\n  useEffect(function () {\n    pressResponder.configure(config);\n  }, [config, pressResponder]);\n  useEffect(function () {\n    return function () {\n      pressResponder.reset();\n    };\n  }, [pressResponder]);\n  useDebugValue(config);\n  return pressResponder.getEventHandlers();\n}", "map": {"version": 3, "names": ["PressResponder", "useDebugValue", "useEffect", "useRef", "usePressEvents", "hostRef", "config", "pressResponderRef", "current", "pressResponder", "configure", "reset", "getEventHandlers"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/modules/usePressEvents/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport PressResponder from './PressResponder';\nimport { useDebugValue, useEffect, useRef } from 'react';\nexport default function usePressEvents(hostRef, config) {\n  var pressResponderRef = useRef(null);\n  if (pressResponderRef.current == null) {\n    pressResponderRef.current = new PressResponder(config);\n  }\n  var pressResponder = pressResponderRef.current;\n\n  // Re-configure to use the current node and configuration.\n  useEffect(() => {\n    pressResponder.configure(config);\n  }, [config, pressResponder]);\n\n  // Reset the `pressResponder` when cleanup needs to occur. This is\n  // a separate effect because we do not want to rest the responder when `config` changes.\n  useEffect(() => {\n    return () => {\n      pressResponder.reset();\n    };\n  }, [pressResponder]);\n  useDebugValue(config);\n  return pressResponder.getEventHandlers();\n}"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,cAAc;AACrB,SAASC,aAAa,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACxD,eAAe,SAASC,cAAcA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACtD,IAAIC,iBAAiB,GAAGJ,MAAM,CAAC,IAAI,CAAC;EACpC,IAAII,iBAAiB,CAACC,OAAO,IAAI,IAAI,EAAE;IACrCD,iBAAiB,CAACC,OAAO,GAAG,IAAIR,cAAc,CAACM,MAAM,CAAC;EACxD;EACA,IAAIG,cAAc,GAAGF,iBAAiB,CAACC,OAAO;EAG9CN,SAAS,CAAC,YAAM;IACdO,cAAc,CAACC,SAAS,CAACJ,MAAM,CAAC;EAClC,CAAC,EAAE,CAACA,MAAM,EAAEG,cAAc,CAAC,CAAC;EAI5BP,SAAS,CAAC,YAAM;IACd,OAAO,YAAM;MACXO,cAAc,CAACE,KAAK,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACF,cAAc,CAAC,CAAC;EACpBR,aAAa,CAACK,MAAM,CAAC;EACrB,OAAOG,cAAc,CAACG,gBAAgB,CAAC,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}