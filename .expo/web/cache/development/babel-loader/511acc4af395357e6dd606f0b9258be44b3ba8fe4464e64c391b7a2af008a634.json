{"ast": null, "code": "var getRandomValues = typeof crypto != 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto != 'undefined' && typeof window.msCrypto.getRandomValues == 'function' && msCrypto.getRandomValues.bind(msCrypto);\nif (getRandomValues) {\n  var rnds8 = new Uint8Array(16);\n  module.exports = function whatwgRNG() {\n    getRandomValues(rnds8);\n    return rnds8;\n  };\n} else {\n  var rnds = new Array(16);\n  module.exports = function mathRNG() {\n    for (var i = 0, r; i < 16; i++) {\n      if ((i & 0x03) === 0) r = Math.random() * 0x100000000;\n      rnds[i] = r >>> ((i & 0x03) << 3) & 0xff;\n    }\n    return rnds;\n  };\n}", "map": {"version": 3, "names": ["getRandomValues", "crypto", "bind", "msCrypto", "window", "rnds8", "Uint8Array", "module", "exports", "whatwgRNG", "rnds", "Array", "mathRNG", "i", "r", "Math", "random"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-file-system/node_modules/uuid/lib/rng-browser.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator.  In the\n// browser this is a little complicated due to unknown quality of Math.random()\n// and inconsistent support for the `crypto` API.  We do the best we can via\n// feature-detection\n\n// getRandomValues needs to be invoked in a context where \"this\" is a Crypto\n// implementation. Also, find the complete implementation of crypto on IE11.\nvar getRandomValues = (typeof(crypto) != 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto)) ||\n                      (typeof(msCrypto) != 'undefined' && typeof window.msCrypto.getRandomValues == 'function' && msCrypto.getRandomValues.bind(msCrypto));\n\nif (getRandomValues) {\n  // WHATWG crypto RNG - http://wiki.whatwg.org/wiki/Crypto\n  var rnds8 = new Uint8Array(16); // eslint-disable-line no-undef\n\n  module.exports = function whatwgRNG() {\n    getRandomValues(rnds8);\n    return rnds8;\n  };\n} else {\n  // Math.random()-based (RNG)\n  //\n  // If all else fails, use Math.random().  It's fast, but is of unspecified\n  // quality.\n  var rnds = new Array(16);\n\n  module.exports = function mathRNG() {\n    for (var i = 0, r; i < 16; i++) {\n      if ((i & 0x03) === 0) r = Math.random() * 0x100000000;\n      rnds[i] = r >>> ((i & 0x03) << 3) & 0xff;\n    }\n\n    return rnds;\n  };\n}\n"], "mappings": "AAOA,IAAIA,eAAe,GAAI,OAAOC,MAAO,IAAI,WAAW,IAAIA,MAAM,CAACD,eAAe,IAAIC,MAAM,CAACD,eAAe,CAACE,IAAI,CAACD,MAAM,CAAC,IAC9F,OAAOE,QAAS,IAAI,WAAW,IAAI,OAAOC,MAAM,CAACD,QAAQ,CAACH,eAAe,IAAI,UAAU,IAAIG,QAAQ,CAACH,eAAe,CAACE,IAAI,CAACC,QAAQ,CAAE;AAE1J,IAAIH,eAAe,EAAE;EAEnB,IAAIK,KAAK,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;EAE9BC,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAAA,EAAG;IACpCT,eAAe,CAACK,KAAK,CAAC;IACtB,OAAOA,KAAK;EACd,CAAC;AACH,CAAC,MAAM;EAKL,IAAIK,IAAI,GAAG,IAAIC,KAAK,CAAC,EAAE,CAAC;EAExBJ,MAAM,CAACC,OAAO,GAAG,SAASI,OAAOA,CAAA,EAAG;IAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAED,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACA,CAAC,GAAG,IAAI,MAAM,CAAC,EAAEC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,WAAW;MACrDN,IAAI,CAACG,CAAC,CAAC,GAAGC,CAAC,MAAM,CAACD,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI;IAC1C;IAEA,OAAOH,IAAI;EACb,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}