{"ast": null, "code": "import { UnavailabilityError } from 'expo-modules-core';\nexport function openDatabase(name) {\n  var version = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '1.0';\n  var description = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : name;\n  var size = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  var callback = arguments.length > 4 ? arguments[4] : undefined;\n  var typedWindow = window;\n  if ('openDatabase' in typedWindow && typedWindow.openDatabase) {\n    return typedWindow.openDatabase(name, version, description, size, callback);\n  }\n  throw new UnavailabilityError('window', 'openDatabase');\n}", "map": {"version": 3, "names": ["UnavailabilityError", "openDatabase", "name", "version", "arguments", "length", "undefined", "description", "size", "callback", "typedWindow", "window"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-sqlite/src/SQLite.web.ts"], "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport { Window, DatabaseCallback } from './SQLite.types';\n\nexport function openDatabase(\n  name: string,\n  version: string = '1.0',\n  description: string = name,\n  size: number = 1,\n  callback?: DatabaseCallback\n) {\n  const typedWindow: Window = window as Window;\n  if ('openDatabase' in typedWindow && typedWindow.openDatabase) {\n    return typedWindow.openDatabase(name, version, description, size, callback);\n  }\n  throw new UnavailabilityError('window', 'openDatabase');\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mBAAmB;AAIvD,OAAM,SAAUC,YAAYA,CAC1BC,IAAY,EAIe;EAAA,IAH3BC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkB,KAAK;EAAA,IACvBG,WAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAsBF,IAAI;EAAA,IAC1BM,IAAA,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAe,CAAC;EAAA,IAChBK,QAA2B,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAE3B,IAAMI,WAAW,GAAWC,MAAgB;EAC5C,IAAI,cAAc,IAAID,WAAW,IAAIA,WAAW,CAACT,YAAY,EAAE;IAC7D,OAAOS,WAAW,CAACT,YAAY,CAACC,IAAI,EAAEC,OAAO,EAAEI,WAAW,EAAEC,IAAI,EAAEC,QAAQ,CAAC;;EAE7E,MAAM,IAAIT,mBAAmB,CAAC,QAAQ,EAAE,cAAc,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}