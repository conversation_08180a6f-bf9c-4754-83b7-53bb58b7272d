{"ast": null, "code": "import React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport { ActivityIndicator, Button } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SplashScreen = function SplashScreen(_ref) {\n  var message = _ref.message,\n    _ref$isError = _ref.isError,\n    isError = _ref$isError === void 0 ? false : _ref$isError,\n    onRetry = _ref.onRetry;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  return _jsxs(View, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: [_jsx(Text, {\n      style: [styles.title, {\n        color: theme.colors.primary\n      }],\n      children: \"Zn\\xFCniZ\\xE4hler\"\n    }), isError ? _jsx(Text, {\n      style: [styles.errorMessage, {\n        color: theme.colors.error\n      }],\n      children: message\n    }) : _jsx(Text, {\n      style: [styles.message, {\n        color: theme.colors.text\n      }],\n      children: message\n    }), isError ? _jsx(Button, {\n      mode: \"contained\",\n      onPress: onRetry,\n      style: styles.retryButton,\n      children: \"Retry\"\n    }) : _jsx(ActivityIndicator, {\n      size: \"large\",\n      color: theme.colors.primary,\n      style: styles.loader\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20\n  },\n  title: {\n    fontSize: 32,\n    fontWeight: 'bold',\n    marginBottom: 20\n  },\n  message: {\n    fontSize: 16,\n    textAlign: 'center',\n    marginBottom: 20\n  },\n  errorMessage: {\n    fontSize: 16,\n    textAlign: 'center',\n    marginBottom: 20\n  },\n  loader: {\n    marginTop: 20\n  },\n  retryButton: {\n    marginTop: 20\n  }\n});\nexport default SplashScreen;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "Text", "ActivityIndicator", "<PERSON><PERSON>", "useTheme", "jsx", "_jsx", "jsxs", "_jsxs", "SplashScreen", "_ref", "message", "_ref$isError", "isError", "onRetry", "_useTheme", "theme", "style", "styles", "container", "backgroundColor", "colors", "background", "children", "title", "color", "primary", "errorMessage", "error", "text", "mode", "onPress", "retryButton", "size", "loader", "create", "flex", "justifyContent", "alignItems", "padding", "fontSize", "fontWeight", "marginBottom", "textAlign", "marginTop"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/screens/SplashScreen.js"], "sourcesContent": ["/**\n * Splash Screen for ZnüniZähler\n * Displays a loading screen while the app initializes\n */\n\nimport React from 'react';\nimport { StyleSheet, View, Text } from 'react-native';\nimport { ActivityIndicator, Button } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\n\n/**\n * Splash Screen Component\n * @param {Object} props - Component props\n * @param {string} props.message - Message to display\n * @param {boolean} props.isError - Whether the message is an error\n * @param {Function} props.onRetry - Callback function to retry initialization\n * @returns {JSX.Element} - Splash screen component\n */\nconst SplashScreen = ({ message, isError = false, onRetry }) => {\n  const { theme } = useTheme();\n  \n  return (\n    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <Text style={[styles.title, { color: theme.colors.primary }]}><PERSON>n<PERSON><PERSON><PERSON><PERSON>hler</Text>\n      \n      {isError ? (\n        <Text style={[styles.errorMessage, { color: theme.colors.error }]}>\n          {message}\n        </Text>\n      ) : (\n        <Text style={[styles.message, { color: theme.colors.text }]}>\n          {message}\n        </Text>\n      )}\n      \n      {isError ? (\n        <Button \n          mode=\"contained\" \n          onPress={onRetry}\n          style={styles.retryButton}\n        >\n          Retry\n        </Button>\n      ) : (\n        <ActivityIndicator \n          size=\"large\" \n          color={theme.colors.primary}\n          style={styles.loader}\n        />\n      )}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n  },\n  title: {\n    fontSize: 32,\n    fontWeight: 'bold',\n    marginBottom: 20,\n  },\n  message: {\n    fontSize: 16,\n    textAlign: 'center',\n    marginBottom: 20,\n  },\n  errorMessage: {\n    fontSize: 16,\n    textAlign: 'center',\n    marginBottom: 20,\n  },\n  loader: {\n    marginTop: 20,\n  },\n  retryButton: {\n    marginTop: 20,\n  },\n});\n\nexport default SplashScreen;\n"], "mappings": "AAKA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAE1B,SAASC,iBAAiB,EAAEC,MAAM,QAAQ,oBAAoB;AAC9D,SAASC,QAAQ;AAAiC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAUlD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAA8C;EAAA,IAAxCC,OAAO,GAAAD,IAAA,CAAPC,OAAO;IAAAC,YAAA,GAAAF,IAAA,CAAEG,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IAAEE,OAAO,GAAAJ,IAAA,CAAPI,OAAO;EACvD,IAAAC,SAAA,GAAkBX,QAAQ,CAAC,CAAC;IAApBY,KAAK,GAAAD,SAAA,CAALC,KAAK;EAEb,OACER,KAAA,CAACR,IAAI;IAACiB,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAEJ,KAAK,CAACK,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,GAC5EjB,IAAA,CAACL,IAAI;MAACgB,KAAK,EAAE,CAACC,MAAM,CAACM,KAAK,EAAE;QAAEC,KAAK,EAAET,KAAK,CAACK,MAAM,CAACK;MAAQ,CAAC,CAAE;MAAAH,QAAA,EAAC;IAAW,CAAM,CAAC,EAE/EV,OAAO,GACNP,IAAA,CAACL,IAAI;MAACgB,KAAK,EAAE,CAACC,MAAM,CAACS,YAAY,EAAE;QAAEF,KAAK,EAAET,KAAK,CAACK,MAAM,CAACO;MAAM,CAAC,CAAE;MAAAL,QAAA,EAC/DZ;IAAO,CACJ,CAAC,GAEPL,IAAA,CAACL,IAAI;MAACgB,KAAK,EAAE,CAACC,MAAM,CAACP,OAAO,EAAE;QAAEc,KAAK,EAAET,KAAK,CAACK,MAAM,CAACQ;MAAK,CAAC,CAAE;MAAAN,QAAA,EACzDZ;IAAO,CACJ,CACP,EAEAE,OAAO,GACNP,IAAA,CAACH,MAAM;MACL2B,IAAI,EAAC,WAAW;MAChBC,OAAO,EAAEjB,OAAQ;MACjBG,KAAK,EAAEC,MAAM,CAACc,WAAY;MAAAT,QAAA,EAC3B;IAED,CAAQ,CAAC,GAETjB,IAAA,CAACJ,iBAAiB;MAChB+B,IAAI,EAAC,OAAO;MACZR,KAAK,EAAET,KAAK,CAACK,MAAM,CAACK,OAAQ;MAC5BT,KAAK,EAAEC,MAAM,CAACgB;IAAO,CACtB,CACF;EAAA,CACG,CAAC;AAEX,CAAC;AAED,IAAMhB,MAAM,GAAGnB,UAAU,CAACoC,MAAM,CAAC;EAC/BhB,SAAS,EAAE;IACTiB,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDf,KAAK,EAAE;IACLgB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD/B,OAAO,EAAE;IACP6B,QAAQ,EAAE,EAAE;IACZG,SAAS,EAAE,QAAQ;IACnBD,YAAY,EAAE;EAChB,CAAC;EACDf,YAAY,EAAE;IACZa,QAAQ,EAAE,EAAE;IACZG,SAAS,EAAE,QAAQ;IACnBD,YAAY,EAAE;EAChB,CAAC;EACDR,MAAM,EAAE;IACNU,SAAS,EAAE;EACb,CAAC;EACDZ,WAAW,EAAE;IACXY,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAenC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}