{"ast": null, "code": "var canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport default canUseDOM;", "map": {"version": 3, "names": ["canUseDOM", "window", "document", "createElement"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/modules/canUseDom/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport default canUseDOM;"], "mappings": "AASA,IAAIA,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC;AACrG,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}