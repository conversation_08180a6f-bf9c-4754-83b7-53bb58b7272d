{"ast": null, "code": "import color from 'color';\nexport default function getContrastingColor(input, light, dark) {\n  if (typeof input === 'string') {\n    return color(input).isLight() ? dark : light;\n  }\n  return light;\n}", "map": {"version": 3, "names": ["color", "getContrastingColor", "input", "light", "dark", "isLight"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/utils/getContrastingColor.tsx"], "sourcesContent": ["import type { ColorValue } from 'react-native';\n\nimport color from 'color';\n\nexport default function getContrastingColor(\n  input: ColorValue,\n  light: string,\n  dark: string\n): string {\n  if (typeof input === 'string') {\n    return color(input).isLight() ? dark : light;\n  }\n\n  return light;\n}\n"], "mappings": "AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,eAAe,SAASC,mBAAmBA,CACzCC,KAAiB,EACjBC,KAAa,EACbC,IAAY,EACJ;EACR,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOF,KAAK,CAACE,KAAK,CAAC,CAACG,OAAO,CAAC,CAAC,GAAGD,IAAI,GAAGD,KAAK;EAC9C;EAEA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}