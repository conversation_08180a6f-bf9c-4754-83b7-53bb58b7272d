{"ast": null, "code": "export var FileSystemSessionType;\n(function (FileSystemSessionType) {\n  FileSystemSessionType[FileSystemSessionType[\"BACKGROUND\"] = 0] = \"BACKGROUND\";\n  FileSystemSessionType[FileSystemSessionType[\"FOREGROUND\"] = 1] = \"FOREGROUND\";\n})(FileSystemSessionType || (FileSystemSessionType = {}));\nexport var FileSystemUploadType;\n(function (FileSystemUploadType) {\n  FileSystemUploadType[FileSystemUploadType[\"BINARY_CONTENT\"] = 0] = \"BINARY_CONTENT\";\n  FileSystemUploadType[FileSystemUploadType[\"MULTIPART\"] = 1] = \"MULTIPART\";\n})(FileSystemUploadType || (FileSystemUploadType = {}));\nexport var EncodingType;\n(function (EncodingType) {\n  EncodingType[\"UTF8\"] = \"utf8\";\n  EncodingType[\"Base64\"] = \"base64\";\n})(EncodingType || (EncodingType = {}));", "map": {"version": 3, "names": ["FileSystemSessionType", "FileSystemUploadType", "EncodingType"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-file-system/src/FileSystem.types.ts"], "sourcesContent": ["/**\n * These values can be used to define how sessions work on iOS.\n * @platform ios\n */\nexport enum FileSystemSessionType {\n  /**\n   * Using this mode means that the downloading/uploading session on the native side will work even if the application is moved to background.\n   * If the task completes while the application is in background, the Promise will be either resolved immediately or (if the application execution has already been stopped) once the app is moved to foreground again.\n   * > Note: The background session doesn't fail if the server or your connection is down. Rather, it continues retrying until the task succeeds or is canceled manually.\n   */\n  BACKGROUND = 0,\n  /**\n   * Using this mode means that downloading/uploading session on the native side will be terminated once the application becomes inactive (e.g. when it goes to background).\n   * Bringing the application to foreground again would trigger Promise rejection.\n   */\n  FOREGROUND = 1,\n}\n\nexport enum FileSystemUploadType {\n  /**\n   * The file will be sent as a request's body. The request can't contain additional data.\n   */\n  BINARY_CONTENT = 0,\n  /**\n   * An [RFC 2387-compliant](https://www.ietf.org/rfc/rfc2387.txt) request body. The provided file will be encoded into HTTP request.\n   * This request can contain additional data represented by [`UploadOptionsMultipart`](#uploadoptionsmultipart) type.\n   */\n  MULTIPART = 1,\n}\n\nexport type DownloadOptions = {\n  /**\n   * If `true`, include the MD5 hash of the file in the returned object. Provided for convenience since it is common to check the integrity of a file immediately after downloading.\n   * @default false\n   */\n  md5?: boolean;\n  // @docsMissing\n  cache?: boolean;\n  /**\n   * An object containing all the HTTP header fields and their values for the download network request. The keys and values of the object are the header names and values respectively.\n   */\n  headers?: Record<string, string>;\n  /**\n   * A session type. Determines if tasks can be handled in the background. On Android, sessions always work in the background and you can't change it.\n   * @default FileSystemSessionType.BACKGROUND\n   * @platform ios\n   */\n  sessionType?: FileSystemSessionType;\n};\n\nexport type FileSystemHttpResult = {\n  /**\n   * An object containing all the HTTP response header fields and their values for the download network request.\n   * The keys and values of the object are the header names and values respectively.\n   */\n  headers: Record<string, string>;\n  /**\n   * The HTTP response status code for the download network request.\n   */\n  status: number;\n  // @docsMissing\n  mimeType: string | null;\n};\n\nexport type FileSystemDownloadResult = FileSystemHttpResult & {\n  /**\n   * A `file://` URI pointing to the file. This is the same as the `fileUri` input parameter.\n   */\n  uri: string;\n  /**\n   * Present if the `md5` option was truthy. Contains the MD5 hash of the file.\n   */\n  md5?: string;\n};\n\n/**\n * @deprecated Use `FileSystemDownloadResult` instead.\n */\nexport type DownloadResult = FileSystemDownloadResult;\n\nexport type FileSystemUploadOptions = (UploadOptionsBinary | UploadOptionsMultipart) & {\n  /**\n   * An object containing all the HTTP header fields and their values for the upload network request.\n   * The keys and values of the object are the header names and values respectively.\n   */\n  headers?: Record<string, string>;\n  /**\n   * The request method.\n   * @default FileSystemAcceptedUploadHttpMethod.POST\n   */\n  httpMethod?: FileSystemAcceptedUploadHttpMethod;\n  /**\n   * A session type. Determines if tasks can be handled in the background. On Android, sessions always work in the background and you can't change it.\n   * @default FileSystemSessionType.BACKGROUND\n   * @platform ios\n   */\n  sessionType?: FileSystemSessionType;\n};\n\n/**\n * Upload options when upload type is set to binary.\n */\nexport type UploadOptionsBinary = {\n  /**\n   * Upload type determines how the file will be sent to the server.\n   * Value will be `FileSystemUploadType.BINARY_CONTENT`.\n   */\n  uploadType?: FileSystemUploadType;\n};\n\n/**\n * Upload options when upload type is set to multipart.\n */\nexport type UploadOptionsMultipart = {\n  /**\n   * Upload type determines how the file will be sent to the server.\n   * Value will be `FileSystemUploadType.MULTIPART`.\n   */\n  uploadType: FileSystemUploadType;\n  /**\n   * The name of the field which will hold uploaded file. Defaults to the file name without an extension.\n   */\n  fieldName?: string;\n  /**\n   * The MIME type of the provided file. If not provided, the module will try to guess it based on the extension.\n   */\n  mimeType?: string;\n  /**\n   * Additional form properties. They will be located in the request body.\n   */\n  parameters?: Record<string, string>;\n};\n\nexport type FileSystemUploadResult = FileSystemHttpResult & {\n  /**\n   * The body of the server response.\n   */\n  body: string;\n};\n\n// @docsMissing\nexport type FileSystemNetworkTaskProgressCallback<\n  T extends DownloadProgressData | UploadProgressData\n> = (data: T) => void;\n\n/**\n * @deprecated use `FileSystemNetworkTaskProgressCallback<DownloadProgressData>` instead.\n */\nexport type DownloadProgressCallback = FileSystemNetworkTaskProgressCallback<DownloadProgressData>;\n\nexport type DownloadProgressData = {\n  /**\n   * The total bytes written by the download operation.\n   */\n  totalBytesWritten: number;\n  /**\n   * The total bytes expected to be written by the download operation. A value of `-1` means that the server did not return the `Content-Length` header\n   * and the total size is unknown. Without this header, you won't be able to track the download progress.\n   */\n  totalBytesExpectedToWrite: number;\n};\n\nexport type UploadProgressData = {\n  /**\n   * The total bytes sent by the upload operation.\n   */\n  totalBytesSent: number;\n  /**\n   * The total bytes expected to be sent by the upload operation.\n   */\n  totalBytesExpectedToSend: number;\n};\n\nexport type DownloadPauseState = {\n  /**\n   * The remote URI to download from.\n   */\n  url: string;\n  /**\n   * The local URI of the file to download to. If there is no file at this URI, a new one is created. If there is a file at this URI, its contents are replaced.\n   */\n  fileUri: string;\n  /**\n   * Object representing the file download options.\n   */\n  options: DownloadOptions;\n  /**\n   * The string which allows the API to resume a paused download.\n   */\n  resumeData?: string;\n};\n\n/* eslint-disable */\nexport type FileInfo =\n  /**\n   * Object returned when file exist.\n   */\n  {\n    /**\n     * Signifies that the requested file exist.\n     */\n    exists: true;\n    /**\n     * A `file://` URI pointing to the file. This is the same as the `fileUri` input parameter.\n     */\n    uri: string;\n    /**\n     * The size of the file in bytes. If operating on a source such as an iCloud file, only present if the `size` option was truthy.\n     */\n    size: number;\n    /**\n     * Boolean set to `true` if this is a directory and `false` if it is a file.\n     */\n    isDirectory: boolean;\n    /**\n     * The last modification time of the file expressed in seconds since epoch.\n     */\n    modificationTime: number;\n    /**\n     * Present if the `md5` option was truthy. Contains the MD5 hash of the file.\n     */\n    md5?: string;\n  } |\n  /**\n   * Object returned when file do not exist.\n   */\n  {\n    exists: false;\n    uri: string;\n    isDirectory: false;\n  };\n/* eslint-enable */\n\n/**\n * These values can be used to define how file system data is read / written.\n */\nexport enum EncodingType {\n  /**\n   * Standard encoding format.\n   */\n  UTF8 = 'utf8',\n  /**\n   * Binary, radix-64 representation.\n   */\n  Base64 = 'base64',\n}\n\n// @docsMissing\nexport type FileSystemAcceptedUploadHttpMethod = 'POST' | 'PUT' | 'PATCH';\n\nexport type ReadingOptions = {\n  /**\n   * The encoding format to use when reading the file.\n   * @default EncodingType.UTF8\n   */\n  encoding?: EncodingType | 'utf8' | 'base64';\n  /**\n   * Optional number of bytes to skip. This option is only used when `encoding: FileSystem.EncodingType.Base64` and `length` is defined.\n   * */\n  position?: number;\n  /**\n   * Optional number of bytes to read. This option is only used when `encoding: FileSystem.EncodingType.Base64` and `position` is defined.\n   */\n  length?: number;\n};\n\nexport type WritingOptions = {\n  /**\n   * The encoding format to use when writing the file.\n   * @default FileSystem.EncodingType.UTF8\n   */\n  encoding?: EncodingType | 'utf8' | 'base64';\n};\n\nexport type DeletingOptions = {\n  /**\n   * If `true`, don't throw an error if there is no file or directory at this URI.\n   * @default false\n   */\n  idempotent?: boolean;\n};\n\nexport type InfoOptions = {\n  /**\n   * Whether to return the MD5 hash of the file.\n   * @default false\n   */\n  md5?: boolean;\n  /**\n   * Explicitly specify that the file size should be included. For example, skipping this can prevent downloading the file if it's stored in iCloud.\n   * The size is always returned for `file://` locations.\n   */\n  size?: boolean;\n};\n\nexport type RelocatingOptions = {\n  /**\n   * URI or [SAF](#saf-uri) URI to the asset, file, or directory. See [supported URI schemes](#supported-uri-schemes-1).\n   */\n  from: string;\n  /**\n   * `file://` URI to the file or directory which should be its new location.\n   */\n  to: string;\n};\n\nexport type MakeDirectoryOptions = {\n  /**\n   * If `true`, don't throw an error if there is no file or directory at this URI.\n   * @default false\n   */\n  intermediates?: boolean;\n};\n\n// @docsMissing\nexport type ProgressEvent<T> = {\n  uuid: string;\n  data: T;\n};\n\n/* eslint-disable */\nexport type FileSystemRequestDirectoryPermissionsResult =\n  /**\n   * If the permissions were not granted.\n   */\n  {\n    granted: false;\n  } |\n  /**\n   * If the permissions were granted.\n   */\n  {\n    granted: true;\n    /**\n     * The [SAF URI](#saf-uri) to the user's selected directory. Available only if permissions were granted.\n     */\n    directoryUri: string;\n  };\n/* eslint-enable */\n"], "mappings": "AAIA,WAAYA,qBAYX;AAZD,WAAYA,qBAAqB;EAM/BA,qBAAA,CAAAA,qBAAA,kCAAc;EAKdA,qBAAA,CAAAA,qBAAA,kCAAc;AAChB,CAAC,EAZWA,qBAAqB,KAArBA,qBAAqB;AAcjC,WAAYC,oBAUX;AAVD,WAAYA,oBAAoB;EAI9BA,oBAAA,CAAAA,oBAAA,0CAAkB;EAKlBA,oBAAA,CAAAA,oBAAA,gCAAa;AACf,CAAC,EAVWA,oBAAoB,KAApBA,oBAAoB;AA0NhC,WAAYC,YASX;AATD,WAAYA,YAAY;EAItBA,YAAA,iBAAa;EAIbA,YAAA,qBAAiB;AACnB,CAAC,EATWA,YAAY,KAAZA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}