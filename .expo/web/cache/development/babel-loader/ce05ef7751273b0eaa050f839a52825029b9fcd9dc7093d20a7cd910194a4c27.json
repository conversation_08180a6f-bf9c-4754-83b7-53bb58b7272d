{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"style\"];\nimport View from \"../../exports/View\";\nimport React from 'react';\nfunction UnimplementedView(_ref) {\n  var style = _ref.style,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return React.createElement(View, _extends({}, props, {\n    style: [unimplementedViewStyles, style]\n  }));\n}\nvar unimplementedViewStyles = process.env.NODE_ENV !== 'production' ? {\n  alignSelf: 'flex-start',\n  borderColor: 'red',\n  borderWidth: 1\n} : {};\nexport default UnimplementedView;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "View", "React", "UnimplementedView", "_ref", "style", "props", "createElement", "unimplementedViewStyles", "process", "env", "NODE_ENV", "alignSelf", "borderColor", "borderWidth"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/modules/UnimplementedView/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"style\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport View from '../../exports/View';\nimport React from 'react';\n\n/**\n * Common implementation for a simple stubbed view.\n */\nfunction UnimplementedView(_ref) {\n  var style = _ref.style,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(View, _extends({}, props, {\n    style: [unimplementedViewStyles, style]\n  }));\n}\nvar unimplementedViewStyles = process.env.NODE_ENV !== 'production' ? {\n  alignSelf: 'flex-start',\n  borderColor: 'red',\n  borderWidth: 1\n} : {};\nexport default UnimplementedView;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,OAAO,CAAC;AAUzB,OAAOC,IAAI;AACX,OAAOC,KAAK,MAAM,OAAO;AAKzB,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,KAAK,GAAGP,6BAA6B,CAACK,IAAI,EAAEJ,SAAS,CAAC;EACxD,OAAoBE,KAAK,CAACK,aAAa,CAACN,IAAI,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IAChED,KAAK,EAAE,CAACG,uBAAuB,EAAEH,KAAK;EACxC,CAAC,CAAC,CAAC;AACL;AACA,IAAIG,uBAAuB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EACpEC,SAAS,EAAE,YAAY;EACvBC,WAAW,EAAE,KAAK;EAClBC,WAAW,EAAE;AACf,CAAC,GAAG,CAAC,CAAC;AACN,eAAeX,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}