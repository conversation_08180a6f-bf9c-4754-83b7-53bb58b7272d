{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"status\", \"theme\", \"disabled\", \"onPress\", \"testID\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { getAndroidSelectionControlColor } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport MaterialCommunityIcon from \"../MaterialCommunityIcon\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nvar ANIMATION_DURATION = 100;\nvar CheckboxAndroid = function CheckboxAndroid(_ref) {\n  var status = _ref.status,\n    themeOverrides = _ref.theme,\n    disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(1)),\n    scaleAnim = _React$useRef.current;\n  var isFirstRendering = React.useRef(true);\n  var scale = theme.animation.scale;\n  React.useEffect(function () {\n    if (isFirstRendering.current) {\n      isFirstRendering.current = false;\n      return;\n    }\n    var checked = status === 'checked';\n    Animated.sequence([Animated.timing(scaleAnim, {\n      toValue: 0.85,\n      duration: checked ? ANIMATION_DURATION * scale : 0,\n      useNativeDriver: false\n    }), Animated.timing(scaleAnim, {\n      toValue: 1,\n      duration: checked ? ANIMATION_DURATION * scale : ANIMATION_DURATION * scale * 1.75,\n      useNativeDriver: false\n    })]).start();\n  }, [status, scaleAnim, scale]);\n  var checked = status === 'checked';\n  var indeterminate = status === 'indeterminate';\n  var _getAndroidSelectionC = getAndroidSelectionControlColor({\n      theme: theme,\n      disabled: disabled,\n      checked: checked,\n      customColor: rest.color,\n      customUncheckedColor: rest.uncheckedColor\n    }),\n    rippleColor = _getAndroidSelectionC.rippleColor,\n    selectionControlColor = _getAndroidSelectionC.selectionControlColor;\n  var borderWidth = scaleAnim.interpolate({\n    inputRange: [0.8, 1],\n    outputRange: [7, 0]\n  });\n  var icon = indeterminate ? 'minus-box' : checked ? 'checkbox-marked' : 'checkbox-blank-outline';\n  return React.createElement(TouchableRipple, _extends({}, rest, {\n    borderless: true,\n    rippleColor: rippleColor,\n    onPress: onPress,\n    disabled: disabled,\n    accessibilityRole: \"checkbox\",\n    accessibilityState: {\n      disabled: disabled,\n      checked: checked\n    },\n    accessibilityLiveRegion: \"polite\",\n    style: styles.container,\n    testID: testID,\n    theme: theme\n  }), React.createElement(Animated.View, {\n    style: {\n      transform: [{\n        scale: scaleAnim\n      }]\n    }\n  }, React.createElement(MaterialCommunityIcon, {\n    allowFontScaling: false,\n    name: icon,\n    size: 24,\n    color: selectionControlColor,\n    direction: \"ltr\"\n  }), React.createElement(View, {\n    style: [StyleSheet.absoluteFill, styles.fillContainer]\n  }, React.createElement(Animated.View, {\n    style: [styles.fill, {\n      borderColor: selectionControlColor\n    }, {\n      borderWidth: borderWidth\n    }]\n  }))));\n};\nCheckboxAndroid.displayName = 'Checkbox.Android';\nvar styles = StyleSheet.create({\n  container: {\n    borderRadius: 18,\n    width: 36,\n    height: 36,\n    padding: 6\n  },\n  fillContainer: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  fill: {\n    height: 14,\n    width: 14\n  }\n});\nexport default CheckboxAndroid;\nexport { CheckboxAndroid };", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "View", "getAndroidSelectionControlColor", "useInternalTheme", "MaterialCommunityIcon", "TouchableRipple", "ANIMATION_DURATION", "CheckboxAndroid", "_ref", "status", "themeOverrides", "theme", "disabled", "onPress", "testID", "rest", "_objectWithoutProperties", "_excluded", "_React$useRef", "useRef", "Value", "scaleAnim", "current", "isFirstRendering", "scale", "animation", "useEffect", "checked", "sequence", "timing", "toValue", "duration", "useNativeDriver", "start", "indeterminate", "_getAndroidSelectionC", "customColor", "color", "customUncheckedColor", "uncheckedColor", "rippleColor", "selectionControlColor", "borderWidth", "interpolate", "inputRange", "outputRange", "icon", "createElement", "_extends", "borderless", "accessibilityRole", "accessibilityState", "accessibilityLiveRegion", "style", "styles", "container", "transform", "allowFontScaling", "name", "size", "direction", "absoluteFill", "<PERSON><PERSON><PERSON><PERSON>", "fill", "borderColor", "displayName", "create", "borderRadius", "width", "height", "padding", "alignItems", "justifyContent"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/react-native-paper/src/components/Checkbox/CheckboxAndroid.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  GestureResponderEvent,\n  StyleSheet,\n  View,\n} from 'react-native';\n\nimport { getAndroidSelectionControlColor } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $RemoveChildren, ThemeProp } from '../../types';\nimport MaterialCommunityIcon from '../MaterialCommunityIcon';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\n\nexport type Props = $RemoveChildren<typeof TouchableRipple> & {\n  /**\n   * Status of checkbox.\n   */\n  status: 'checked' | 'unchecked' | 'indeterminate';\n  /**\n   * Whether checkbox is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Custom color for unchecked checkbox.\n   */\n  uncheckedColor?: string;\n  /**\n   * Custom color for checkbox.\n   */\n  color?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n// From https://material.io/design/motion/speed.html#duration\nconst ANIMATION_DURATION = 100;\n\n/**\n * Checkboxes allow the selection of multiple options from a set.\n * This component follows platform guidelines for Android, but can be used\n * on any platform.\n *\n * @extends TouchableRipple props https://callstack.github.io/react-native-paper/docs/components/TouchableRipple\n */\nconst CheckboxAndroid = ({\n  status,\n  theme: themeOverrides,\n  disabled,\n  onPress,\n  testID,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { current: scaleAnim } = React.useRef<Animated.Value>(\n    new Animated.Value(1)\n  );\n  const isFirstRendering = React.useRef<boolean>(true);\n\n  const {\n    animation: { scale },\n  } = theme;\n\n  React.useEffect(() => {\n    // Do not run animation on very first rendering\n    if (isFirstRendering.current) {\n      isFirstRendering.current = false;\n      return;\n    }\n\n    const checked = status === 'checked';\n\n    Animated.sequence([\n      Animated.timing(scaleAnim, {\n        toValue: 0.85,\n        duration: checked ? ANIMATION_DURATION * scale : 0,\n        useNativeDriver: false,\n      }),\n      Animated.timing(scaleAnim, {\n        toValue: 1,\n        duration: checked\n          ? ANIMATION_DURATION * scale\n          : ANIMATION_DURATION * scale * 1.75,\n        useNativeDriver: false,\n      }),\n    ]).start();\n  }, [status, scaleAnim, scale]);\n\n  const checked = status === 'checked';\n  const indeterminate = status === 'indeterminate';\n\n  const { rippleColor, selectionControlColor } =\n    getAndroidSelectionControlColor({\n      theme,\n      disabled,\n      checked,\n      customColor: rest.color,\n      customUncheckedColor: rest.uncheckedColor,\n    });\n\n  const borderWidth = scaleAnim.interpolate({\n    inputRange: [0.8, 1],\n    outputRange: [7, 0],\n  });\n\n  const icon = indeterminate\n    ? 'minus-box'\n    : checked\n    ? 'checkbox-marked'\n    : 'checkbox-blank-outline';\n\n  return (\n    <TouchableRipple\n      {...rest}\n      borderless\n      rippleColor={rippleColor}\n      onPress={onPress}\n      disabled={disabled}\n      accessibilityRole=\"checkbox\"\n      accessibilityState={{ disabled, checked }}\n      accessibilityLiveRegion=\"polite\"\n      style={styles.container}\n      testID={testID}\n      theme={theme}\n    >\n      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>\n        <MaterialCommunityIcon\n          allowFontScaling={false}\n          name={icon}\n          size={24}\n          color={selectionControlColor}\n          direction=\"ltr\"\n        />\n        <View style={[StyleSheet.absoluteFill, styles.fillContainer]}>\n          <Animated.View\n            style={[\n              styles.fill,\n              { borderColor: selectionControlColor },\n              { borderWidth },\n            ]}\n          />\n        </View>\n      </Animated.View>\n    </TouchableRipple>\n  );\n};\n\nCheckboxAndroid.displayName = 'Checkbox.Android';\n\nconst styles = StyleSheet.create({\n  container: {\n    borderRadius: 18,\n    width: 36,\n    height: 36,\n    padding: 6,\n  },\n  fillContainer: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  fill: {\n    height: 14,\n    width: 14,\n  },\n});\n\nexport default CheckboxAndroid;\n\n// @component-docs ignore-next-line\nexport { CheckboxAndroid };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAQ9B,SAASC,+BAA+B;AACxC,SAASC,gBAAgB;AAEzB,OAAOC,qBAAqB;AAC5B,OAAOC,eAAe;AAkCtB,IAAMC,kBAAkB,GAAG,GAAG;AAS9B,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,IAAA,EAOX;EAAA,IANXC,MAAM,GAMAD,IAAA,CANNC,MAAM;IACCC,cAAc,GAKfF,IAAA,CALNG,KAAK;IACLC,QAAQ,GAIFJ,IAAA,CAJNI,QAAQ;IACRC,OAAO,GAGDL,IAAA,CAHNK,OAAO;IACPC,MAAM,GAEAN,IAAA,CAFNM,MAAM;IACHC,IAAA,GAAAC,wBAAA,CACGR,IAAA,EAAAS,SAAA;EACN,IAAMN,KAAK,GAAGR,gBAAgB,CAACO,cAAc,CAAC;EAC9C,IAAAQ,aAAA,GAA+BpB,KAAK,CAACqB,MAAM,CACzC,IAAIpB,QAAQ,CAACqB,KAAK,CAAC,CAAC,CACtB,CAAC;IAFgBC,SAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAMC,gBAAgB,GAAGzB,KAAK,CAACqB,MAAM,CAAU,IAAI,CAAC;EAEpD,IACeK,KAAA,GACXb,KAAK,CADPc,SAAS,CAAID,KAAA;EAGf1B,KAAK,CAAC4B,SAAS,CAAC,YAAM;IAEpB,IAAIH,gBAAgB,CAACD,OAAO,EAAE;MAC5BC,gBAAgB,CAACD,OAAO,GAAG,KAAK;MAChC;IACF;IAEA,IAAMK,OAAO,GAAGlB,MAAM,KAAK,SAAS;IAEpCV,QAAQ,CAAC6B,QAAQ,CAAC,CAChB7B,QAAQ,CAAC8B,MAAM,CAACR,SAAS,EAAE;MACzBS,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAEJ,OAAO,GAAGrB,kBAAkB,GAAGkB,KAAK,GAAG,CAAC;MAClDQ,eAAe,EAAE;IACnB,CAAC,CAAC,EACFjC,QAAQ,CAAC8B,MAAM,CAACR,SAAS,EAAE;MACzBS,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAEJ,OAAO,GACbrB,kBAAkB,GAAGkB,KAAK,GAC1BlB,kBAAkB,GAAGkB,KAAK,GAAG,IAAI;MACrCQ,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACxB,MAAM,EAAEY,SAAS,EAAEG,KAAK,CAAC,CAAC;EAE9B,IAAMG,OAAO,GAAGlB,MAAM,KAAK,SAAS;EACpC,IAAMyB,aAAa,GAAGzB,MAAM,KAAK,eAAe;EAEhD,IAAA0B,qBAAA,GACEjC,+BAA+B,CAAC;MAC9BS,KAAK,EAALA,KAAK;MACLC,QAAQ,EAARA,QAAQ;MACRe,OAAO,EAAPA,OAAO;MACPS,WAAW,EAAErB,IAAI,CAACsB,KAAK;MACvBC,oBAAoB,EAAEvB,IAAI,CAACwB;IAC7B,CAAC,CAAC;IAPIC,WAAW,GAAAL,qBAAA,CAAXK,WAAW;IAAEC,qBAAA,GAAAN,qBAAA,CAAAM,qBAAA;EASrB,IAAMC,WAAW,GAAGrB,SAAS,CAACsB,WAAW,CAAC;IACxCC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC;EAEF,IAAMC,IAAI,GAAGZ,aAAa,GACtB,WAAW,GACXP,OAAO,GACP,iBAAiB,GACjB,wBAAwB;EAE5B,OACE7B,KAAA,CAAAiD,aAAA,CAAC1C,eAAe,EAAA2C,QAAA,KACVjC,IAAI;IACRkC,UAAU;IACVT,WAAW,EAAEA,WAAY;IACzB3B,OAAO,EAAEA,OAAQ;IACjBD,QAAQ,EAAEA,QAAS;IACnBsC,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAAEvC,QAAQ,EAARA,QAAQ;MAAEe,OAAA,EAAAA;IAAQ,CAAE;IAC1CyB,uBAAuB,EAAC,QAAQ;IAChCC,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxBzC,MAAM,EAAEA,MAAO;IACfH,KAAK,EAAEA;EAAM,IAEbb,KAAA,CAAAiD,aAAA,CAAChD,QAAQ,CAACE,IAAI;IAACoD,KAAK,EAAE;MAAEG,SAAS,EAAE,CAAC;QAAEhC,KAAK,EAAEH;MAAU,CAAC;IAAE;EAAE,GAC1DvB,KAAA,CAAAiD,aAAA,CAAC3C,qBAAqB;IACpBqD,gBAAgB,EAAE,KAAM;IACxBC,IAAI,EAAEZ,IAAK;IACXa,IAAI,EAAE,EAAG;IACTtB,KAAK,EAAEI,qBAAsB;IAC7BmB,SAAS,EAAC;EAAK,CAChB,CAAC,EACF9D,KAAA,CAAAiD,aAAA,CAAC9C,IAAI;IAACoD,KAAK,EAAE,CAACrD,UAAU,CAAC6D,YAAY,EAAEP,MAAM,CAACQ,aAAa;EAAE,GAC3DhE,KAAA,CAAAiD,aAAA,CAAChD,QAAQ,CAACE,IAAI;IACZoD,KAAK,EAAE,CACLC,MAAM,CAACS,IAAI,EACX;MAAEC,WAAW,EAAEvB;IAAsB,CAAC,EACtC;MAAEC,WAAA,EAAAA;IAAY,CAAC;EACf,CACH,CACG,CACO,CACA,CAAC;AAEtB,CAAC;AAEDnC,eAAe,CAAC0D,WAAW,GAAG,kBAAkB;AAEhD,IAAMX,MAAM,GAAGtD,UAAU,CAACkE,MAAM,CAAC;EAC/BX,SAAS,EAAE;IACTY,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC;EACDR,aAAa,EAAE;IACbS,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDT,IAAI,EAAE;IACJM,MAAM,EAAE,EAAE;IACVD,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAe7D,eAAe;AAG9B,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}