{"ast": null, "code": "var platformModule = {\n  get name() {\n    return 'ExponentFileSystem';\n  },\n  get documentDirectory() {\n    return null;\n  },\n  get cacheDirectory() {\n    return null;\n  },\n  get bundledAssets() {\n    return null;\n  },\n  get bundleDirectory() {\n    return null;\n  },\n  addListener: function addListener(eventName) {},\n  removeListeners: function removeListeners(count) {}\n};\nexport default platformModule;", "map": {"version": 3, "names": ["platformModule", "name", "documentDirectory", "cacheDirectory", "bundledAssets", "bundleDirectory", "addListener", "eventName", "removeListeners", "count"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-file-system/src/ExponentFileSystemShim.ts"], "sourcesContent": ["import { ExponentFileSystemModule } from './types';\n\nconst platformModule: ExponentFileSystemModule = {\n  get name(): 'ExponentFileSystem' {\n    return 'ExponentFileSystem';\n  },\n  get documentDirectory(): string | null {\n    return null;\n  },\n  get cacheDirectory(): string | null {\n    return null;\n  },\n  get bundledAssets(): string | null {\n    return null;\n  },\n  get bundleDirectory(): string | null {\n    return null;\n  },\n  addListener(eventName: string): void {},\n  removeListeners(count: number): void {},\n};\n\nexport default platformModule;\n"], "mappings": "AAEA,IAAMA,cAAc,GAA6B;EAC/C,IAAIC,IAAIA,CAAA;IACN,OAAO,oBAAoB;EAC7B,CAAC;EACD,IAAIC,iBAAiBA,CAAA;IACnB,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,cAAcA,CAAA;IAChB,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI;EACb,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAACC,SAAiB,GAAS,CAAC;EACvCC,eAAe,WAAfA,eAAeA,CAACC,KAAa,GAAS;CACvC;AAED,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}