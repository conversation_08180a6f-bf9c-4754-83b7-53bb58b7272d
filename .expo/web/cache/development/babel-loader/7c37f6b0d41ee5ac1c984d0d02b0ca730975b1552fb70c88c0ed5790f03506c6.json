{"ast": null, "code": "\"use strict\";\n\nvar _asyncToGenerator = require(\"@babel/runtime/helpers/asyncToGenerator\");\nvar _defineProperty = require(\"@babel/runtime/helpers/defineProperty\");\nvar _slicedToArray = require(\"@babel/runtime/helpers/slicedToArray\");\nvar _objectWithoutProperties = require(\"@babel/runtime/helpers/objectWithoutProperties\");\nvar _excluded = [\"name\", \"size\", \"color\", \"style\", \"children\", \"allowFontScaling\", \"innerRef\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_ICON_SIZE = exports.DEFAULT_ICON_COLOR = void 0;\nexports.createIconSet = createIconSet;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _reactNative = require(\"react-native-web/dist/index\");\nvar _createIconSourceCache = _interopRequireDefault(require(\"./create-icon-source-cache.js\"));\nvar _dynamicFontLoading = require(\"./dynamicLoading/dynamic-font-loading\");\nvar _dynamicLoadingSetting = require(\"./dynamicLoading/dynamic-loading-setting.js\");\nvar _getImageLibrary = require(\"./get-image-library.js\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    default: e\n  };\n}\nfunction _getRequireWildcardCache(e) {\n  if (\"function\" != typeof WeakMap) return null;\n  var r = new WeakMap(),\n    t = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(e) {\n    return e ? t : r;\n  })(e);\n}\nfunction _interopRequireWildcard(e, r) {\n  if (!r && e && e.__esModule) return e;\n  if (null === e || \"object\" != typeof e && \"function\" != typeof e) return {\n    default: e\n  };\n  var t = _getRequireWildcardCache(r);\n  if (t && t.has(e)) return t.get(e);\n  var n = {\n      __proto__: null\n    },\n    a = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) {\n    var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;\n    i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];\n  }\n  return n.default = e, t && t.set(e, n), n;\n}\nvar DEFAULT_ICON_SIZE = exports.DEFAULT_ICON_SIZE = 12;\nvar DEFAULT_ICON_COLOR = exports.DEFAULT_ICON_COLOR = 'black';\nfunction createIconSet(glyphMap, postScriptNameOrOptions, fontFileNameParam, fontStyleParam) {\n  var _ref = typeof postScriptNameOrOptions === 'string' ? {\n      postScriptName: postScriptNameOrOptions,\n      fontFileName: fontFileNameParam,\n      fontStyle: fontStyleParam\n    } : postScriptNameOrOptions,\n    postScriptName = _ref.postScriptName,\n    fontFileName = _ref.fontFileName,\n    fontStyle = _ref.fontStyle;\n  var fontBasename = fontFileName ? fontFileName.replace(/\\.(otf|ttf)$/, '') : postScriptName;\n  var fontReference = _reactNative.Platform.select({\n    windows: `/Assets/${fontFileName}#${postScriptName}`,\n    android: fontBasename,\n    web: fontBasename,\n    default: postScriptName\n  });\n  var resolveGlyph = function resolveGlyph(name) {\n    var glyph = glyphMap[name] || '?';\n    if (typeof glyph === 'number') {\n      return String.fromCodePoint(glyph);\n    }\n    return glyph;\n  };\n  var Icon = function Icon(_ref2) {\n    var name = _ref2.name,\n      _ref2$size = _ref2.size,\n      size = _ref2$size === void 0 ? DEFAULT_ICON_SIZE : _ref2$size,\n      _ref2$color = _ref2.color,\n      color = _ref2$color === void 0 ? DEFAULT_ICON_COLOR : _ref2$color,\n      style = _ref2.style,\n      children = _ref2.children,\n      _ref2$allowFontScalin = _ref2.allowFontScaling,\n      allowFontScaling = _ref2$allowFontScalin === void 0 ? false : _ref2$allowFontScalin,\n      innerRef = _ref2.innerRef,\n      props = _objectWithoutProperties(_ref2, _excluded);\n    var _react$default$useSta = _react.default.useState((0, _dynamicLoadingSetting.isDynamicLoadingEnabled)() ? _dynamicFontLoading.dynamicLoader.isLoaded(fontReference) : true),\n      _react$default$useSta2 = _slicedToArray(_react$default$useSta, 2),\n      isFontLoaded = _react$default$useSta2[0],\n      setIsFontLoaded = _react$default$useSta2[1];\n    var glyph = isFontLoaded && name ? resolveGlyph(name) : '';\n    (0, _react.useEffect)(function () {\n      var isMounted = true;\n      if (!isFontLoaded && typeof postScriptNameOrOptions === 'object' && typeof postScriptNameOrOptions.fontSource !== 'undefined') {\n        _dynamicFontLoading.dynamicLoader.loadFontAsync(fontReference, postScriptNameOrOptions.fontSource).finally(function () {\n          if (isMounted) {\n            setIsFontLoaded(true);\n          }\n        });\n      }\n      return function () {\n        isMounted = false;\n      };\n    }, []);\n    var styleDefaults = {\n      fontSize: size,\n      color: color\n    };\n    var styleOverrides = {\n      fontFamily: fontReference,\n      fontWeight: 'normal',\n      fontStyle: 'normal'\n    };\n    var newProps = _objectSpread(_objectSpread({}, props), {}, {\n      style: [styleDefaults, style, styleOverrides, fontStyle || {}],\n      allowFontScaling: allowFontScaling\n    });\n    return (0, _jsxRuntime.jsxs)(_reactNative.Text, _objectSpread(_objectSpread({\n      ref: innerRef,\n      selectable: false\n    }, newProps), {}, {\n      children: [glyph, children]\n    }));\n  };\n  var WrappedIcon = (0, _react.forwardRef)(function (props, ref) {\n    return (0, _jsxRuntime.jsx)(Icon, _objectSpread({\n      innerRef: ref\n    }, props));\n  });\n  WrappedIcon.displayName = 'Icon';\n  var imageSourceCache = (0, _createIconSourceCache.default)();\n  var getImageSourceSync = function getImageSourceSync(name) {\n    var size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEFAULT_ICON_SIZE;\n    var color = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : DEFAULT_ICON_COLOR;\n    var NativeIconAPI = (0, _getImageLibrary.ensureGetImageAvailable)();\n    var glyph = resolveGlyph(name);\n    var processedColor = (0, _reactNative.processColor)(color);\n    var cacheKey = `${glyph}:${size}:${String(processedColor)}`;\n    if (imageSourceCache.has(cacheKey)) {\n      return imageSourceCache.get(cacheKey);\n    }\n    try {\n      var imagePath = NativeIconAPI.getImageForFontSync(fontReference, glyph, size, processedColor);\n      var value = {\n        uri: imagePath,\n        scale: _reactNative.PixelRatio.get()\n      };\n      imageSourceCache.setValue(cacheKey, value);\n      return value;\n    } catch (error) {\n      imageSourceCache.setError(cacheKey, error);\n      throw error;\n    }\n  };\n  var getImageSource = function () {\n    var _ref3 = _asyncToGenerator(function* (name) {\n      var size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEFAULT_ICON_SIZE;\n      var color = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : DEFAULT_ICON_COLOR;\n      var NativeIconAPI = (0, _getImageLibrary.ensureGetImageAvailable)();\n      var glyph = resolveGlyph(name);\n      var processedColor = (0, _reactNative.processColor)(color);\n      var cacheKey = `${glyph}:${size}:${String(processedColor)}`;\n      if (imageSourceCache.has(cacheKey)) {\n        return imageSourceCache.get(cacheKey);\n      }\n      try {\n        var imagePath = yield NativeIconAPI.getImageForFont(fontReference, glyph, size, processedColor);\n        var value = {\n          uri: imagePath,\n          scale: _reactNative.PixelRatio.get()\n        };\n        imageSourceCache.setValue(cacheKey, value);\n        return value;\n      } catch (error) {\n        imageSourceCache.setError(cacheKey, error);\n        throw error;\n      }\n    });\n    return function getImageSource(_x) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var IconNamespace = Object.assign(WrappedIcon, {\n    getImageSource: getImageSource,\n    getImageSourceSync: getImageSourceSync\n  });\n  return IconNamespace;\n}", "map": {"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_createIconSourceCache", "_interopRequireDefault", "_dynamicFontLoading", "_dynamicLoadingSetting", "_getImageLibrary", "_jsxRuntime", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "DEFAULT_ICON_SIZE", "exports", "DEFAULT_ICON_COLOR", "createIconSet", "glyphMap", "postScriptNameOrOptions", "fontFileNameParam", "fontStyleParam", "_ref", "postScriptName", "fontFileName", "fontStyle", "fontBasename", "replace", "fontReference", "Platform", "select", "windows", "android", "web", "resolveGlyph", "name", "glyph", "String", "fromCodePoint", "Icon", "_ref2", "_ref2$size", "size", "_ref2$color", "color", "style", "children", "_ref2$allowFontScalin", "allowFontScaling", "innerRef", "props", "_objectWithoutProperties", "_excluded", "_react$default$useSta", "useState", "isDynamicLoadingEnabled", "dynamic<PERSON>oader", "isLoaded", "_react$default$useSta2", "_slicedToArray", "isFontLoaded", "setIsFontLoaded", "useEffect", "isMounted", "fontSource", "loadFontAsync", "finally", "styleDefaults", "fontSize", "styleOverrides", "fontFamily", "fontWeight", "newProps", "_objectSpread", "jsxs", "Text", "ref", "selectable", "WrappedIcon", "forwardRef", "jsx", "displayName", "imageSourceCache", "getImageSourceSync", "arguments", "length", "undefined", "NativeIconAPI", "ensureGetImageAvailable", "processedColor", "processColor", "cache<PERSON>ey", "imagePath", "getImageForFontSync", "value", "uri", "scale", "PixelRatio", "setValue", "error", "setError", "getImageSource", "_ref3", "_asyncToGenerator", "getImageForFont", "_x", "apply", "IconNamespace", "assign"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-vector-icons/common/src/create-icon-set.tsx"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nimport React, { forwardRef, type Ref, useEffect } from 'react';\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { PixelRatio, Platform, Text, type TextProps, type TextStyle, processColor } from 'react-native';\n\nimport createIconSourceCache from './create-icon-source-cache';\nimport { dynamicLoader } from './dynamicLoading/dynamic-font-loading';\nimport { isDynamicLoadingEnabled } from './dynamicLoading/dynamic-loading-setting';\nimport type { FontSource } from './dynamicLoading/types';\nimport { ensureGetImageAvailable } from './get-image-library';\n\nexport const DEFAULT_ICON_SIZE = 12;\nexport const DEFAULT_ICON_COLOR = 'black';\n\ntype ValueData = { uri: string; scale: number };\ntype GetImageSourceSyncIconFunc<GM> = (name: GM, size?: number, color?: TextStyle['color']) => ValueData | undefined;\ntype GetImageSourceIconFunc<GM> = (\n  name: GM,\n  size?: number,\n  color?: TextStyle['color'],\n) => Promise<ValueData | undefined>;\n\nexport type IconProps<T> = TextProps & {\n  name: T;\n  size?: number;\n  color?: TextStyle['color'];\n  innerRef?: Ref<Text>;\n};\n\ntype IconComponent<GM extends Record<string, number>> = React.FC<\n  TextProps & {\n    name: keyof GM;\n    size?: number;\n    color?: TextStyle['color'];\n    innerRef?: Ref<Text>;\n  } & React.RefAttributes<Text>\n> & {\n  getImageSource: GetImageSourceIconFunc<keyof GM>;\n  getImageSourceSync: GetImageSourceSyncIconFunc<keyof GM>;\n};\n\nexport type CreateIconSetOptions = {\n  postScriptName: string;\n  fontFileName: string;\n  fontSource?: FontSource;\n  fontStyle?: TextProps['style'];\n};\n\nexport function createIconSet<GM extends Record<string, number>>(\n  glyphMap: GM,\n  postScriptName: string,\n  fontFileName: string,\n  fontStyle?: TextProps['style'],\n): IconComponent<GM>;\nexport function createIconSet<GM extends Record<string, number>>(\n  glyphMap: GM,\n  options: CreateIconSetOptions,\n): IconComponent<GM>;\nexport function createIconSet<GM extends Record<string, number>>(\n  glyphMap: GM,\n  postScriptNameOrOptions: string | CreateIconSetOptions,\n  fontFileNameParam?: string,\n  fontStyleParam?: TextProps['style'],\n): IconComponent<GM> {\n  const { postScriptName, fontFileName, fontStyle } =\n    typeof postScriptNameOrOptions === 'string'\n      ? { postScriptName: postScriptNameOrOptions, fontFileName: fontFileNameParam, fontStyle: fontStyleParam }\n      : postScriptNameOrOptions;\n\n  const fontBasename = fontFileName ? fontFileName.replace(/\\.(otf|ttf)$/, '') : postScriptName;\n\n  const fontReference = Platform.select({\n    windows: `/Assets/${fontFileName}#${postScriptName}`,\n    android: fontBasename,\n    web: fontBasename,\n    default: postScriptName,\n  });\n\n  const resolveGlyph = (name: keyof GM) => {\n    const glyph = glyphMap[name] || '?';\n\n    if (typeof glyph === 'number') {\n      return String.fromCodePoint(glyph);\n    }\n\n    return glyph;\n  };\n\n  const Icon = ({\n    name,\n    size = DEFAULT_ICON_SIZE,\n    color = DEFAULT_ICON_COLOR,\n    style,\n    children,\n    allowFontScaling = false,\n    innerRef,\n    ...props\n  }: IconProps<keyof GM>) => {\n    const [isFontLoaded, setIsFontLoaded] = React.useState(\n      isDynamicLoadingEnabled() ? dynamicLoader.isLoaded(fontReference) : true,\n    );\n    const glyph = isFontLoaded && name ? resolveGlyph(name) : '';\n\n    // biome-ignore lint/correctness/useExhaustiveDependencies: the dependencies never change\n    useEffect(() => {\n      let isMounted = true;\n\n      if (\n        !isFontLoaded &&\n        typeof postScriptNameOrOptions === 'object' &&\n        typeof postScriptNameOrOptions.fontSource !== 'undefined'\n      ) {\n        dynamicLoader.loadFontAsync(fontReference, postScriptNameOrOptions.fontSource).finally(() => {\n          if (isMounted) {\n            setIsFontLoaded(true);\n          }\n        });\n      }\n      return () => {\n        isMounted = false;\n      };\n    }, []);\n\n    const styleDefaults = {\n      fontSize: size,\n      color,\n    };\n\n    const styleOverrides: TextProps['style'] = {\n      fontFamily: fontReference,\n      fontWeight: 'normal',\n      fontStyle: 'normal',\n    };\n\n    const newProps: TextProps = {\n      ...props,\n      style: [styleDefaults, style, styleOverrides, fontStyle || {}],\n      allowFontScaling,\n    };\n\n    return (\n      <Text ref={innerRef} selectable={false} {...newProps}>\n        {glyph}\n        {children}\n      </Text>\n    );\n  };\n\n  const WrappedIcon = forwardRef<Text, IconProps<keyof typeof glyphMap>>((props, ref) => (\n    <Icon innerRef={ref} {...props} />\n  ));\n  WrappedIcon.displayName = 'Icon';\n\n  const imageSourceCache = createIconSourceCache();\n\n  const getImageSourceSync = (\n    name: keyof GM,\n    size = DEFAULT_ICON_SIZE,\n    color: TextStyle['color'] = DEFAULT_ICON_COLOR,\n  ) => {\n    const NativeIconAPI = ensureGetImageAvailable();\n\n    const glyph = resolveGlyph(name);\n    const processedColor = processColor(color);\n    const cacheKey = `${glyph}:${size}:${String(processedColor)}`;\n\n    if (imageSourceCache.has(cacheKey)) {\n      // FIXME: Should this check if it's an error and throw it again?\n      return imageSourceCache.get(cacheKey);\n    }\n\n    try {\n      const imagePath = NativeIconAPI.getImageForFontSync(\n        fontReference,\n        glyph,\n        size,\n        processedColor as number, // FIXME what if a non existant colour was passed in?\n      );\n      const value = { uri: imagePath, scale: PixelRatio.get() };\n      imageSourceCache.setValue(cacheKey, value);\n      return value;\n    } catch (error) {\n      imageSourceCache.setError(cacheKey, error as Error);\n      throw error;\n    }\n  };\n\n  const getImageSource = async (\n    name: keyof GM,\n    size = DEFAULT_ICON_SIZE,\n    color: TextStyle['color'] = DEFAULT_ICON_COLOR,\n  ) => {\n    const NativeIconAPI = ensureGetImageAvailable();\n\n    const glyph = resolveGlyph(name);\n    const processedColor = processColor(color);\n    const cacheKey = `${glyph}:${size}:${String(processedColor)}`;\n\n    if (imageSourceCache.has(cacheKey)) {\n      // FIXME: Should this check if it's an error and throw it again?\n      return imageSourceCache.get(cacheKey);\n    }\n\n    try {\n      const imagePath = await NativeIconAPI.getImageForFont(\n        fontReference,\n        glyph,\n        size,\n        processedColor as number, // FIXME what if a non existant colour was passed in?\n      );\n      const value = { uri: imagePath, scale: PixelRatio.get() };\n      imageSourceCache.setValue(cacheKey, value);\n      return value;\n    } catch (error) {\n      imageSourceCache.setError(cacheKey, error as Error);\n      throw error;\n    }\n  };\n\n  const IconNamespace = Object.assign(WrappedIcon, {\n    getImageSource,\n    getImageSourceSync,\n  });\n\n  return IconNamespace;\n}\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAA,IAAAC,YAAA,GAAAD,OAAA;AAKA,IAAAE,sBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAJ,OAAA;AACA,IAAAK,sBAAA,GAAAL,OAAA;AAEA,IAAAM,gBAAA,GAAAN,OAAA;AAA8D,IAAAO,WAAA,GAAAP,OAAA;AAAA,SAAAG,uBAAAK,CAAA;EAAA,OAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA;IAAAE,OAAA,EAAAF;EAAA;AAAA;AAAA,SAAAG,yBAAAH,CAAA;EAAA,yBAAAI,OAAA;EAAA,IAAAC,CAAA,OAAAD,OAAA;IAAAE,CAAA,OAAAF,OAAA;EAAA,QAAAD,wBAAA,YAAAA,yBAAAH,CAAA;IAAA,OAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA;EAAA,GAAAL,CAAA;AAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAK,CAAA;EAAA,KAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA;EAAA,aAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA;IAAAE,OAAA,EAAAF;EAAA;EAAA,IAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA;EAAA,IAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA;EAAA,IAAAS,CAAA;MAAAC,SAAA;IAAA;IAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA;EAAA,SAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA;IAAA,IAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA;IAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA;EAAA;EAAA,OAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAAA;AAEvD,IAAMW,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,GAAG,EAAE;AAC5B,IAAME,kBAAkB,GAAAD,OAAA,CAAAC,kBAAA,GAAG,OAAO;AA8ClC,SAASC,aAAaA,CAC3BC,QAAY,EACZC,uBAAsD,EACtDC,iBAA0B,EAC1BC,cAAmC,EAChB;EACnB,IAAAC,IAAA,GACE,OAAOH,uBAAuB,KAAK,QAAQ,GACvC;MAAEI,cAAc,EAAEJ,uBAAuB;MAAEK,YAAY,EAAEJ,iBAAiB;MAAEK,SAAS,EAAEJ;IAAe,CAAC,GACvGF,uBAAuB;IAHrBI,cAAc,GAAAD,IAAA,CAAdC,cAAc;IAAEC,YAAY,GAAAF,IAAA,CAAZE,YAAY;IAAEC,SAAA,GAAAH,IAAA,CAAAG,SAAA;EAKtC,IAAMC,YAAY,GAAGF,YAAY,GAAGA,YAAY,CAACG,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,GAAGJ,cAAc;EAE7F,IAAMK,aAAa,GAAGzC,YAAA,CAAA0C,QAAQ,CAACC,MAAM,CAAC;IACpCC,OAAO,EAAE,WAAWP,YAAY,IAAID,cAAc,EAAE;IACpDS,OAAO,EAAEN,YAAY;IACrBO,GAAG,EAAEP,YAAY;IACjB9B,OAAO,EAAE2B;EACX,CAAC,CAAC;EAEF,IAAMW,YAAY,GAAI,SAAhBA,YAAYA,CAAIC,IAAc,EAAK;IACvC,IAAMC,KAAK,GAAGlB,QAAQ,CAACiB,IAAI,CAAC,IAAI,GAAG;IAEnC,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOC,MAAM,CAACC,aAAa,CAACF,KAAK,CAAC;IACpC;IAEA,OAAOA,KAAK;EACd,CAAC;EAED,IAAMG,IAAI,GAAG,SAAPA,IAAIA,CAAAC,KAAA,EASiB;IAAA,IARzBL,IAAI,GAAAK,KAAA,CAAJL,IAAI;MAAAM,UAAA,GAAAD,KAAA,CACJE,IAAI;MAAJA,IAAI,GAAAD,UAAA,cAAG3B,iBAAiB,GAAA2B,UAAA;MAAAE,WAAA,GAAAH,KAAA,CACxBI,KAAK;MAALA,KAAK,GAAAD,WAAA,cAAG3B,kBAAkB,GAAA2B,WAAA;MAC1BE,KAAK,GAAAL,KAAA,CAALK,KAAK;MACLC,QAAQ,GAAAN,KAAA,CAARM,QAAQ;MAAAC,qBAAA,GAAAP,KAAA,CACRQ,gBAAgB;MAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;MACxBE,QAAQ,GAAAT,KAAA,CAARS,QAAQ;MACLC,KAAA,GAAAC,wBAAA,CAAAX,KAAA,EAAAY,SAAA;IAEH,IAAAC,qBAAA,GAAwCrE,MAAA,CAAAY,OAAK,CAAC0D,QAAQ,CACpD,IAAA/D,sBAAA,CAAAgE,uBAAuB,EAAC,CAAC,GAAGjE,mBAAA,CAAAkE,aAAa,CAACC,QAAQ,CAAC7B,aAAa,CAAC,GAAG,IACtE,CAAC;MAAA8B,sBAAA,GAAAC,cAAA,CAAAN,qBAAA;MAFMO,YAAY,GAAAF,sBAAA;MAAEG,eAAe,GAAAH,sBAAA;IAGpC,IAAMtB,KAAK,GAAGwB,YAAY,IAAIzB,IAAI,GAAGD,YAAY,CAACC,IAAI,CAAC,GAAG,EAAE;IAG5D,IAAAnD,MAAA,CAAA8E,SAAS,EAAC,YAAM;MACd,IAAIC,SAAS,GAAG,IAAI;MAEpB,IACE,CAACH,YAAY,IACb,OAAOzC,uBAAuB,KAAK,QAAQ,IAC3C,OAAOA,uBAAuB,CAAC6C,UAAU,KAAK,WAAW,EACzD;QACA1E,mBAAA,CAAAkE,aAAa,CAACS,aAAa,CAACrC,aAAa,EAAET,uBAAuB,CAAC6C,UAAU,CAAC,CAACE,OAAO,CAAC,YAAM;UAC3F,IAAIH,SAAS,EAAE;YACbF,eAAe,CAAC,IAAI,CAAC;UACvB;QACF,CAAC,CAAC;MACJ;MACA,OAAO,YAAM;QACXE,SAAS,GAAG,KAAK;MACnB,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IAEN,IAAMI,aAAa,GAAG;MACpBC,QAAQ,EAAE1B,IAAI;MACdE,KAAA,EAAAA;IACF,CAAC;IAED,IAAMyB,cAAkC,GAAG;MACzCC,UAAU,EAAE1C,aAAa;MACzB2C,UAAU,EAAE,QAAQ;MACpB9C,SAAS,EAAE;IACb,CAAC;IAED,IAAM+C,QAAmB,GAAAC,aAAA,CAAAA,aAAA,KACpBvB,KAAK;MACRL,KAAK,EAAE,CAACsB,aAAa,EAAEtB,KAAK,EAAEwB,cAAc,EAAE5C,SAAS,IAAI,CAAC,CAAC,CAAC;MAC9DuB,gBAAA,EAAAA;IAAA,EACD;IAED,OACE,IAAAvD,WAAA,CAAAiF,IAAA,EAACvF,YAAA,CAAAwF,IAAI,EAAAF,aAAA,CAAAA,aAAA;MAACG,GAAG,EAAE3B,QAAS;MAAC4B,UAAU,EAAE;IAAM,GAAKL,QAAQ;MAAA1B,QAAA,GACjDV,KAAK,EACLU,QAAQ;IAAA,EACL,CAAC;EAEX,CAAC;EAED,IAAMgC,WAAW,GAAG,IAAA9F,MAAA,CAAA+F,UAAU,EAAyC,UAAC7B,KAAK,EAAE0B,GAAG;IAAA,OAChF,IAAAnF,WAAA,CAAAuF,GAAA,EAACzC,IAAI,EAAAkC,aAAA;MAACxB,QAAQ,EAAE2B;IAAI,GAAK1B,KAAA,CAAQ,CAClC;EAAA,EAAC;EACF4B,WAAW,CAACG,WAAW,GAAG,MAAM;EAEhC,IAAMC,gBAAgB,GAAG,IAAA9F,sBAAA,CAAAQ,OAAqB,EAAC,CAAC;EAEhD,IAAMuF,kBAAkB,GAAG,SAArBA,kBAAkBA,CACtBhD,IAAc,EAGX;IAAA,IAFHO,IAAI,GAAA0C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGtE,iBAAiB;IAAA,IACxB8B,KAAyB,GAAAwC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGpE,kBAAkB;IAE9C,IAAMuE,aAAa,GAAG,IAAA/F,gBAAA,CAAAgG,uBAAuB,EAAC,CAAC;IAE/C,IAAMpD,KAAK,GAAGF,YAAY,CAACC,IAAI,CAAC;IAChC,IAAMsD,cAAc,GAAG,IAAAtG,YAAA,CAAAuG,YAAY,EAAC9C,KAAK,CAAC;IAC1C,IAAM+C,QAAQ,GAAG,GAAGvD,KAAK,IAAIM,IAAI,IAAIL,MAAM,CAACoD,cAAc,CAAC,EAAE;IAE7D,IAAIP,gBAAgB,CAACjF,GAAG,CAAC0F,QAAQ,CAAC,EAAE;MAElC,OAAOT,gBAAgB,CAAChF,GAAG,CAACyF,QAAQ,CAAC;IACvC;IAEA,IAAI;MACF,IAAMC,SAAS,GAAGL,aAAa,CAACM,mBAAmB,CACjDjE,aAAa,EACbQ,KAAK,EACLM,IAAI,EACJ+C,cACF,CAAC;MACD,IAAMK,KAAK,GAAG;QAAEC,GAAG,EAAEH,SAAS;QAAEI,KAAK,EAAE7G,YAAA,CAAA8G,UAAU,CAAC/F,GAAG,CAAC;MAAE,CAAC;MACzDgF,gBAAgB,CAACgB,QAAQ,CAACP,QAAQ,EAAEG,KAAK,CAAC;MAC1C,OAAOA,KAAK;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdjB,gBAAgB,CAACkB,QAAQ,CAACT,QAAQ,EAAEQ,KAAc,CAAC;MACnD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,IAAME,cAAc;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,WACrBpE,IAAc,EAGX;MAAA,IAFHO,IAAI,GAAA0C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGtE,iBAAiB;MAAA,IACxB8B,KAAyB,GAAAwC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGpE,kBAAkB;MAE9C,IAAMuE,aAAa,GAAG,IAAA/F,gBAAA,CAAAgG,uBAAuB,EAAC,CAAC;MAE/C,IAAMpD,KAAK,GAAGF,YAAY,CAACC,IAAI,CAAC;MAChC,IAAMsD,cAAc,GAAG,IAAAtG,YAAA,CAAAuG,YAAY,EAAC9C,KAAK,CAAC;MAC1C,IAAM+C,QAAQ,GAAG,GAAGvD,KAAK,IAAIM,IAAI,IAAIL,MAAM,CAACoD,cAAc,CAAC,EAAE;MAE7D,IAAIP,gBAAgB,CAACjF,GAAG,CAAC0F,QAAQ,CAAC,EAAE;QAElC,OAAOT,gBAAgB,CAAChF,GAAG,CAACyF,QAAQ,CAAC;MACvC;MAEA,IAAI;QACF,IAAMC,SAAS,SAASL,aAAa,CAACiB,eAAe,CACnD5E,aAAa,EACbQ,KAAK,EACLM,IAAI,EACJ+C,cACF,CAAC;QACD,IAAMK,KAAK,GAAG;UAAEC,GAAG,EAAEH,SAAS;UAAEI,KAAK,EAAE7G,YAAA,CAAA8G,UAAU,CAAC/F,GAAG,CAAC;QAAE,CAAC;QACzDgF,gBAAgB,CAACgB,QAAQ,CAACP,QAAQ,EAAEG,KAAK,CAAC;QAC1C,OAAOA,KAAK;MACd,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdjB,gBAAgB,CAACkB,QAAQ,CAACT,QAAQ,EAAEQ,KAAc,CAAC;QACnD,MAAMA,KAAK;MACb;IACF,CAAC;IAAA,gBA9BKE,cAAcA,CAAAI,EAAA;MAAA,OAAAH,KAAA,CAAAI,KAAA,OAAAtB,SAAA;IAAA;EAAA,GA8BnB;EAED,IAAMuB,aAAa,GAAGrG,MAAM,CAACsG,MAAM,CAAC9B,WAAW,EAAE;IAC/CuB,cAAc,EAAdA,cAAc;IACdlB,kBAAA,EAAAA;EACF,CAAC,CAAC;EAEF,OAAOwB,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}