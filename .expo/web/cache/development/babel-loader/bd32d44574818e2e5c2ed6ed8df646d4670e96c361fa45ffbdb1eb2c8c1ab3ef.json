{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"aria-activedescendant\", \"accessibilityActiveDescendant\", \"aria-atomic\", \"accessibilityAtomic\", \"aria-autocomplete\", \"accessibilityAutoComplete\", \"aria-busy\", \"accessibilityBusy\", \"aria-checked\", \"accessibilityChecked\", \"aria-colcount\", \"accessibilityColumnCount\", \"aria-colindex\", \"accessibilityColumnIndex\", \"aria-colspan\", \"accessibilityColumnSpan\", \"aria-controls\", \"accessibilityControls\", \"aria-current\", \"accessibilityCurrent\", \"aria-describedby\", \"accessibilityDescribedBy\", \"aria-details\", \"accessibilityDetails\", \"aria-disabled\", \"accessibilityDisabled\", \"aria-errormessage\", \"accessibilityErrorMessage\", \"aria-expanded\", \"accessibilityExpanded\", \"aria-flowto\", \"accessibilityFlowTo\", \"aria-haspopup\", \"accessibilityHasPopup\", \"aria-hidden\", \"accessibilityHidden\", \"aria-invalid\", \"accessibilityInvalid\", \"aria-keyshortcuts\", \"accessibilityKeyShortcuts\", \"aria-label\", \"accessibilityLabel\", \"aria-labelledby\", \"accessibilityLabelledBy\", \"aria-level\", \"accessibilityLevel\", \"aria-live\", \"accessibilityLiveRegion\", \"aria-modal\", \"accessibilityModal\", \"aria-multiline\", \"accessibilityMultiline\", \"aria-multiselectable\", \"accessibilityMultiSelectable\", \"aria-orientation\", \"accessibilityOrientation\", \"aria-owns\", \"accessibilityOwns\", \"aria-placeholder\", \"accessibilityPlaceholder\", \"aria-posinset\", \"accessibilityPosInSet\", \"aria-pressed\", \"accessibilityPressed\", \"aria-readonly\", \"accessibilityReadOnly\", \"aria-required\", \"accessibilityRequired\", \"role\", \"accessibilityRole\", \"aria-roledescription\", \"accessibilityRoleDescription\", \"aria-rowcount\", \"accessibilityRowCount\", \"aria-rowindex\", \"accessibilityRowIndex\", \"aria-rowspan\", \"accessibilityRowSpan\", \"aria-selected\", \"accessibilitySelected\", \"aria-setsize\", \"accessibilitySetSize\", \"aria-sort\", \"accessibilitySort\", \"aria-valuemax\", \"accessibilityValueMax\", \"aria-valuemin\", \"accessibilityValueMin\", \"aria-valuenow\", \"accessibilityValueNow\", \"aria-valuetext\", \"accessibilityValueText\", \"dataSet\", \"focusable\", \"id\", \"nativeID\", \"pointerEvents\", \"style\", \"tabIndex\", \"testID\"];\nimport AccessibilityUtil from \"../AccessibilityUtil\";\nimport StyleSheet from \"../../exports/StyleSheet\";\nimport { warnOnce } from \"../warnOnce\";\nvar emptyObject = {};\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\nvar uppercasePattern = /[A-Z]/g;\nfunction toHyphenLower(match) {\n  return '-' + match.toLowerCase();\n}\nfunction hyphenateString(str) {\n  return str.replace(uppercasePattern, toHyphenLower);\n}\nfunction processIDRefList(idRefList) {\n  return isArray(idRefList) ? idRefList.join(' ') : idRefList;\n}\nvar pointerEventsStyles = StyleSheet.create({\n  auto: {\n    pointerEvents: 'auto'\n  },\n  'box-none': {\n    pointerEvents: 'box-none'\n  },\n  'box-only': {\n    pointerEvents: 'box-only'\n  },\n  none: {\n    pointerEvents: 'none'\n  }\n});\nvar createDOMProps = function createDOMProps(elementType, props, options) {\n  if (!props) {\n    props = emptyObject;\n  }\n  var _props = props,\n    ariaActiveDescendant = _props['aria-activedescendant'],\n    accessibilityActiveDescendant = _props.accessibilityActiveDescendant,\n    ariaAtomic = _props['aria-atomic'],\n    accessibilityAtomic = _props.accessibilityAtomic,\n    ariaAutoComplete = _props['aria-autocomplete'],\n    accessibilityAutoComplete = _props.accessibilityAutoComplete,\n    ariaBusy = _props['aria-busy'],\n    accessibilityBusy = _props.accessibilityBusy,\n    ariaChecked = _props['aria-checked'],\n    accessibilityChecked = _props.accessibilityChecked,\n    ariaColumnCount = _props['aria-colcount'],\n    accessibilityColumnCount = _props.accessibilityColumnCount,\n    ariaColumnIndex = _props['aria-colindex'],\n    accessibilityColumnIndex = _props.accessibilityColumnIndex,\n    ariaColumnSpan = _props['aria-colspan'],\n    accessibilityColumnSpan = _props.accessibilityColumnSpan,\n    ariaControls = _props['aria-controls'],\n    accessibilityControls = _props.accessibilityControls,\n    ariaCurrent = _props['aria-current'],\n    accessibilityCurrent = _props.accessibilityCurrent,\n    ariaDescribedBy = _props['aria-describedby'],\n    accessibilityDescribedBy = _props.accessibilityDescribedBy,\n    ariaDetails = _props['aria-details'],\n    accessibilityDetails = _props.accessibilityDetails,\n    ariaDisabled = _props['aria-disabled'],\n    accessibilityDisabled = _props.accessibilityDisabled,\n    ariaErrorMessage = _props['aria-errormessage'],\n    accessibilityErrorMessage = _props.accessibilityErrorMessage,\n    ariaExpanded = _props['aria-expanded'],\n    accessibilityExpanded = _props.accessibilityExpanded,\n    ariaFlowTo = _props['aria-flowto'],\n    accessibilityFlowTo = _props.accessibilityFlowTo,\n    ariaHasPopup = _props['aria-haspopup'],\n    accessibilityHasPopup = _props.accessibilityHasPopup,\n    ariaHidden = _props['aria-hidden'],\n    accessibilityHidden = _props.accessibilityHidden,\n    ariaInvalid = _props['aria-invalid'],\n    accessibilityInvalid = _props.accessibilityInvalid,\n    ariaKeyShortcuts = _props['aria-keyshortcuts'],\n    accessibilityKeyShortcuts = _props.accessibilityKeyShortcuts,\n    ariaLabel = _props['aria-label'],\n    accessibilityLabel = _props.accessibilityLabel,\n    ariaLabelledBy = _props['aria-labelledby'],\n    accessibilityLabelledBy = _props.accessibilityLabelledBy,\n    ariaLevel = _props['aria-level'],\n    accessibilityLevel = _props.accessibilityLevel,\n    ariaLive = _props['aria-live'],\n    accessibilityLiveRegion = _props.accessibilityLiveRegion,\n    ariaModal = _props['aria-modal'],\n    accessibilityModal = _props.accessibilityModal,\n    ariaMultiline = _props['aria-multiline'],\n    accessibilityMultiline = _props.accessibilityMultiline,\n    ariaMultiSelectable = _props['aria-multiselectable'],\n    accessibilityMultiSelectable = _props.accessibilityMultiSelectable,\n    ariaOrientation = _props['aria-orientation'],\n    accessibilityOrientation = _props.accessibilityOrientation,\n    ariaOwns = _props['aria-owns'],\n    accessibilityOwns = _props.accessibilityOwns,\n    ariaPlaceholder = _props['aria-placeholder'],\n    accessibilityPlaceholder = _props.accessibilityPlaceholder,\n    ariaPosInSet = _props['aria-posinset'],\n    accessibilityPosInSet = _props.accessibilityPosInSet,\n    ariaPressed = _props['aria-pressed'],\n    accessibilityPressed = _props.accessibilityPressed,\n    ariaReadOnly = _props['aria-readonly'],\n    accessibilityReadOnly = _props.accessibilityReadOnly,\n    ariaRequired = _props['aria-required'],\n    accessibilityRequired = _props.accessibilityRequired,\n    ariaRole = _props.role,\n    accessibilityRole = _props.accessibilityRole,\n    ariaRoleDescription = _props['aria-roledescription'],\n    accessibilityRoleDescription = _props.accessibilityRoleDescription,\n    ariaRowCount = _props['aria-rowcount'],\n    accessibilityRowCount = _props.accessibilityRowCount,\n    ariaRowIndex = _props['aria-rowindex'],\n    accessibilityRowIndex = _props.accessibilityRowIndex,\n    ariaRowSpan = _props['aria-rowspan'],\n    accessibilityRowSpan = _props.accessibilityRowSpan,\n    ariaSelected = _props['aria-selected'],\n    accessibilitySelected = _props.accessibilitySelected,\n    ariaSetSize = _props['aria-setsize'],\n    accessibilitySetSize = _props.accessibilitySetSize,\n    ariaSort = _props['aria-sort'],\n    accessibilitySort = _props.accessibilitySort,\n    ariaValueMax = _props['aria-valuemax'],\n    accessibilityValueMax = _props.accessibilityValueMax,\n    ariaValueMin = _props['aria-valuemin'],\n    accessibilityValueMin = _props.accessibilityValueMin,\n    ariaValueNow = _props['aria-valuenow'],\n    accessibilityValueNow = _props.accessibilityValueNow,\n    ariaValueText = _props['aria-valuetext'],\n    accessibilityValueText = _props.accessibilityValueText,\n    dataSet = _props.dataSet,\n    focusable = _props.focusable,\n    id = _props.id,\n    nativeID = _props.nativeID,\n    pointerEvents = _props.pointerEvents,\n    style = _props.style,\n    tabIndex = _props.tabIndex,\n    testID = _props.testID,\n    domProps = _objectWithoutPropertiesLoose(_props, _excluded);\n  var disabled = ariaDisabled || accessibilityDisabled;\n  var role = AccessibilityUtil.propsToAriaRole(props);\n  var _ariaActiveDescendant = ariaActiveDescendant != null ? ariaActiveDescendant : accessibilityActiveDescendant;\n  if (_ariaActiveDescendant != null) {\n    domProps['aria-activedescendant'] = _ariaActiveDescendant;\n  }\n  var _ariaAtomic = ariaAtomic != null ? ariaActiveDescendant : accessibilityAtomic;\n  if (_ariaAtomic != null) {\n    domProps['aria-atomic'] = _ariaAtomic;\n  }\n  var _ariaAutoComplete = ariaAutoComplete != null ? ariaAutoComplete : accessibilityAutoComplete;\n  if (_ariaAutoComplete != null) {\n    domProps['aria-autocomplete'] = _ariaAutoComplete;\n  }\n  var _ariaBusy = ariaBusy != null ? ariaBusy : accessibilityBusy;\n  if (_ariaBusy != null) {\n    domProps['aria-busy'] = _ariaBusy;\n  }\n  var _ariaChecked = ariaChecked != null ? ariaChecked : accessibilityChecked;\n  if (_ariaChecked != null) {\n    domProps['aria-checked'] = _ariaChecked;\n  }\n  var _ariaColumnCount = ariaColumnCount != null ? ariaColumnCount : accessibilityColumnCount;\n  if (_ariaColumnCount != null) {\n    domProps['aria-colcount'] = _ariaColumnCount;\n  }\n  var _ariaColumnIndex = ariaColumnIndex != null ? ariaColumnIndex : accessibilityColumnIndex;\n  if (_ariaColumnIndex != null) {\n    domProps['aria-colindex'] = _ariaColumnIndex;\n  }\n  var _ariaColumnSpan = ariaColumnSpan != null ? ariaColumnSpan : accessibilityColumnSpan;\n  if (_ariaColumnSpan != null) {\n    domProps['aria-colspan'] = _ariaColumnSpan;\n  }\n  var _ariaControls = ariaControls != null ? ariaControls : accessibilityControls;\n  if (_ariaControls != null) {\n    domProps['aria-controls'] = processIDRefList(_ariaControls);\n  }\n  var _ariaCurrent = ariaCurrent != null ? ariaCurrent : accessibilityCurrent;\n  if (_ariaCurrent != null) {\n    domProps['aria-current'] = _ariaCurrent;\n  }\n  var _ariaDescribedBy = ariaDescribedBy != null ? ariaDescribedBy : accessibilityDescribedBy;\n  if (_ariaDescribedBy != null) {\n    domProps['aria-describedby'] = processIDRefList(_ariaDescribedBy);\n  }\n  var _ariaDetails = ariaDetails != null ? ariaDetails : accessibilityDetails;\n  if (_ariaDetails != null) {\n    domProps['aria-details'] = _ariaDetails;\n  }\n  if (disabled === true) {\n    domProps['aria-disabled'] = true;\n    if (elementType === 'button' || elementType === 'form' || elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      domProps.disabled = true;\n    }\n  }\n  var _ariaErrorMessage = ariaErrorMessage != null ? ariaErrorMessage : accessibilityErrorMessage;\n  if (_ariaErrorMessage != null) {\n    domProps['aria-errormessage'] = _ariaErrorMessage;\n  }\n  var _ariaExpanded = ariaExpanded != null ? ariaExpanded : accessibilityExpanded;\n  if (_ariaExpanded != null) {\n    domProps['aria-expanded'] = _ariaExpanded;\n  }\n  var _ariaFlowTo = ariaFlowTo != null ? ariaFlowTo : accessibilityFlowTo;\n  if (_ariaFlowTo != null) {\n    domProps['aria-flowto'] = processIDRefList(_ariaFlowTo);\n  }\n  var _ariaHasPopup = ariaHasPopup != null ? ariaHasPopup : accessibilityHasPopup;\n  if (_ariaHasPopup != null) {\n    domProps['aria-haspopup'] = _ariaHasPopup;\n  }\n  var _ariaHidden = ariaHidden != null ? ariaHidden : accessibilityHidden;\n  if (_ariaHidden === true) {\n    domProps['aria-hidden'] = _ariaHidden;\n  }\n  var _ariaInvalid = ariaInvalid != null ? ariaInvalid : accessibilityInvalid;\n  if (_ariaInvalid != null) {\n    domProps['aria-invalid'] = _ariaInvalid;\n  }\n  var _ariaKeyShortcuts = ariaKeyShortcuts != null ? ariaKeyShortcuts : accessibilityKeyShortcuts;\n  if (_ariaKeyShortcuts != null) {\n    domProps['aria-keyshortcuts'] = processIDRefList(_ariaKeyShortcuts);\n  }\n  var _ariaLabel = ariaLabel != null ? ariaLabel : accessibilityLabel;\n  if (_ariaLabel != null) {\n    domProps['aria-label'] = _ariaLabel;\n  }\n  var _ariaLabelledBy = ariaLabelledBy != null ? ariaLabelledBy : accessibilityLabelledBy;\n  if (_ariaLabelledBy != null) {\n    domProps['aria-labelledby'] = processIDRefList(_ariaLabelledBy);\n  }\n  var _ariaLevel = ariaLevel != null ? ariaLevel : accessibilityLevel;\n  if (_ariaLevel != null) {\n    domProps['aria-level'] = _ariaLevel;\n  }\n  var _ariaLive = ariaLive != null ? ariaLive : accessibilityLiveRegion;\n  if (_ariaLive != null) {\n    domProps['aria-live'] = _ariaLive === 'none' ? 'off' : _ariaLive;\n  }\n  var _ariaModal = ariaModal != null ? ariaModal : accessibilityModal;\n  if (_ariaModal != null) {\n    domProps['aria-modal'] = _ariaModal;\n  }\n  var _ariaMultiline = ariaMultiline != null ? ariaMultiline : accessibilityMultiline;\n  if (_ariaMultiline != null) {\n    domProps['aria-multiline'] = _ariaMultiline;\n  }\n  var _ariaMultiSelectable = ariaMultiSelectable != null ? ariaMultiSelectable : accessibilityMultiSelectable;\n  if (_ariaMultiSelectable != null) {\n    domProps['aria-multiselectable'] = _ariaMultiSelectable;\n  }\n  var _ariaOrientation = ariaOrientation != null ? ariaOrientation : accessibilityOrientation;\n  if (_ariaOrientation != null) {\n    domProps['aria-orientation'] = _ariaOrientation;\n  }\n  var _ariaOwns = ariaOwns != null ? ariaOwns : accessibilityOwns;\n  if (_ariaOwns != null) {\n    domProps['aria-owns'] = processIDRefList(_ariaOwns);\n  }\n  var _ariaPlaceholder = ariaPlaceholder != null ? ariaPlaceholder : accessibilityPlaceholder;\n  if (_ariaPlaceholder != null) {\n    domProps['aria-placeholder'] = _ariaPlaceholder;\n  }\n  var _ariaPosInSet = ariaPosInSet != null ? ariaPosInSet : accessibilityPosInSet;\n  if (_ariaPosInSet != null) {\n    domProps['aria-posinset'] = _ariaPosInSet;\n  }\n  var _ariaPressed = ariaPressed != null ? ariaPressed : accessibilityPressed;\n  if (_ariaPressed != null) {\n    domProps['aria-pressed'] = _ariaPressed;\n  }\n  var _ariaReadOnly = ariaReadOnly != null ? ariaReadOnly : accessibilityReadOnly;\n  if (_ariaReadOnly != null) {\n    domProps['aria-readonly'] = _ariaReadOnly;\n    if (elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      domProps.readOnly = true;\n    }\n  }\n  var _ariaRequired = ariaRequired != null ? ariaRequired : accessibilityRequired;\n  if (_ariaRequired != null) {\n    domProps['aria-required'] = _ariaRequired;\n    if (elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      domProps.required = accessibilityRequired;\n    }\n  }\n  if (role != null) {\n    domProps['role'] = role === 'none' ? 'presentation' : role;\n  }\n  var _ariaRoleDescription = ariaRoleDescription != null ? ariaRoleDescription : accessibilityRoleDescription;\n  if (_ariaRoleDescription != null) {\n    domProps['aria-roledescription'] = _ariaRoleDescription;\n  }\n  var _ariaRowCount = ariaRowCount != null ? ariaRowCount : accessibilityRowCount;\n  if (_ariaRowCount != null) {\n    domProps['aria-rowcount'] = _ariaRowCount;\n  }\n  var _ariaRowIndex = ariaRowIndex != null ? ariaRowIndex : accessibilityRowIndex;\n  if (_ariaRowIndex != null) {\n    domProps['aria-rowindex'] = _ariaRowIndex;\n  }\n  var _ariaRowSpan = ariaRowSpan != null ? ariaRowSpan : accessibilityRowSpan;\n  if (_ariaRowSpan != null) {\n    domProps['aria-rowspan'] = _ariaRowSpan;\n  }\n  var _ariaSelected = ariaSelected != null ? ariaSelected : accessibilitySelected;\n  if (_ariaSelected != null) {\n    domProps['aria-selected'] = _ariaSelected;\n  }\n  var _ariaSetSize = ariaSetSize != null ? ariaSetSize : accessibilitySetSize;\n  if (_ariaSetSize != null) {\n    domProps['aria-setsize'] = _ariaSetSize;\n  }\n  var _ariaSort = ariaSort != null ? ariaSort : accessibilitySort;\n  if (_ariaSort != null) {\n    domProps['aria-sort'] = _ariaSort;\n  }\n  var _ariaValueMax = ariaValueMax != null ? ariaValueMax : accessibilityValueMax;\n  if (_ariaValueMax != null) {\n    domProps['aria-valuemax'] = _ariaValueMax;\n  }\n  var _ariaValueMin = ariaValueMin != null ? ariaValueMin : accessibilityValueMin;\n  if (_ariaValueMin != null) {\n    domProps['aria-valuemin'] = _ariaValueMin;\n  }\n  var _ariaValueNow = ariaValueNow != null ? ariaValueNow : accessibilityValueNow;\n  if (_ariaValueNow != null) {\n    domProps['aria-valuenow'] = _ariaValueNow;\n  }\n  var _ariaValueText = ariaValueText != null ? ariaValueText : accessibilityValueText;\n  if (_ariaValueText != null) {\n    domProps['aria-valuetext'] = _ariaValueText;\n  }\n  if (dataSet != null) {\n    for (var dataProp in dataSet) {\n      if (hasOwnProperty.call(dataSet, dataProp)) {\n        var dataName = hyphenateString(dataProp);\n        var dataValue = dataSet[dataProp];\n        if (dataValue != null) {\n          domProps[\"data-\" + dataName] = dataValue;\n        }\n      }\n    }\n  }\n  if (tabIndex === 0 || tabIndex === '0' || tabIndex === -1 || tabIndex === '-1') {\n    domProps.tabIndex = tabIndex;\n  } else {\n    if (focusable === false) {\n      domProps.tabIndex = '-1';\n    }\n    if (elementType === 'a' || elementType === 'button' || elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      if (focusable === false || accessibilityDisabled === true) {\n        domProps.tabIndex = '-1';\n      }\n    } else if (role === 'button' || role === 'checkbox' || role === 'link' || role === 'radio' || role === 'textbox' || role === 'switch') {\n      if (focusable !== false) {\n        domProps.tabIndex = '0';\n      }\n    } else {\n      if (focusable === true) {\n        domProps.tabIndex = '0';\n      }\n    }\n  }\n  if (pointerEvents != null) {\n    warnOnce('pointerEvents', \"props.pointerEvents is deprecated. Use style.pointerEvents\");\n  }\n  var _StyleSheet = StyleSheet([style, pointerEvents && pointerEventsStyles[pointerEvents]], _objectSpread({\n      writingDirection: 'ltr'\n    }, options)),\n    className = _StyleSheet[0],\n    inlineStyle = _StyleSheet[1];\n  if (className) {\n    domProps.className = className;\n  }\n  if (inlineStyle) {\n    domProps.style = inlineStyle;\n  }\n  var _id = id != null ? id : nativeID;\n  if (_id != null) {\n    domProps.id = _id;\n  }\n  if (testID != null) {\n    domProps['data-testid'] = testID;\n  }\n  if (domProps.type == null && elementType === 'button') {\n    domProps.type = 'button';\n  }\n  return domProps;\n};\nexport default createDOMProps;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutPropertiesLoose", "_excluded", "AccessibilityUtil", "StyleSheet", "warnOnce", "emptyObject", "hasOwnProperty", "Object", "prototype", "isArray", "Array", "uppercasePattern", "toHyphenLower", "match", "toLowerCase", "hyphenateString", "str", "replace", "processIDRefList", "idRefList", "join", "pointerEventsStyles", "create", "auto", "pointerEvents", "none", "createDOMProps", "elementType", "props", "options", "_props", "ariaActiveDescendant", "accessibilityActiveDescendant", "ariaAtomic", "accessibilityAtomic", "ariaAutoComplete", "accessibilityAutoComplete", "ariaBusy", "accessibilityBusy", "ariaChe<PERSON>", "accessibilityChecked", "ariaColumnCount", "accessibilityColumnCount", "ariaColumnIndex", "accessibilityColumnIndex", "ariaColumnSpan", "accessibilityColumnSpan", "ariaControls", "accessibilityControls", "aria<PERSON>urrent", "accessibilityCurrent", "ariaDescribedBy", "accessibilityDescribedBy", "ariaDetails", "accessibilityDetails", "ariaDisabled", "accessibilityDisabled", "ariaErrorMessage", "accessibilityErrorMessage", "ariaExpanded", "accessibilityExpanded", "ariaFlowTo", "accessibilityFlowTo", "aria<PERSON>as<PERSON><PERSON><PERSON>", "accessibilityHasPopup", "ariaHidden", "accessibilityHidden", "ariaInvalid", "accessibilityInvalid", "ariaKeyShortcuts", "accessibilityKeyShortcuts", "aria<PERSON><PERSON><PERSON>", "accessibilityLabel", "ariaLabelledBy", "accessibilityLabelledBy", "ariaLevel", "accessibilityLevel", "ariaLive", "accessibilityLiveRegion", "ariaModal", "accessibilityModal", "ariaMultiline", "accessibilityMultiline", "ariaMultiSelectable", "accessibilityMultiSelectable", "ariaOrientation", "accessibilityOrientation", "ariaOwns", "accessibilityOwns", "ariaPlaceholder", "accessibilityPlaceholder", "ariaPosInSet", "accessibilityPosInSet", "ariaPressed", "accessibilityPressed", "ariaReadOnly", "accessibilityReadOnly", "ariaRequired", "accessibilityRequired", "ariaRole", "role", "accessibilityRole", "ariaRoleDescription", "accessibilityRoleDescription", "ariaRowCount", "accessibilityRowCount", "ariaRowIndex", "accessibilityRowIndex", "ariaRowSpan", "accessibilityRowSpan", "ariaSelected", "accessibilitySelected", "ariaSetSize", "accessibilitySetSize", "ariaSort", "accessibilitySort", "ariaValueMax", "accessibilityValueMax", "ariaValueMin", "accessibilityValueMin", "ariaValueNow", "accessibilityValueNow", "ariaValueText", "accessibilityValueText", "dataSet", "focusable", "id", "nativeID", "style", "tabIndex", "testID", "domProps", "disabled", "propsToAriaRole", "_ariaActiveDescendant", "_ariaAtomic", "_ariaAutoComplete", "_ariaBusy", "_aria<PERSON>he<PERSON>", "_ariaColumnCount", "_ariaColumnIndex", "_ariaColumnSpan", "_ariaControls", "_aria<PERSON><PERSON>rent", "_ariaDescribedBy", "_ariaDetails", "_ariaErrorMessage", "_ariaExpanded", "_ariaFlowTo", "_ariaHasPopup", "_ariaH<PERSON>den", "_ariaInvalid", "_ariaKeyShortcuts", "_a<PERSON><PERSON><PERSON><PERSON>", "_ariaLabelledBy", "_ariaLevel", "_ariaLive", "_ariaModal", "_ariaMultiline", "_ariaMultiSelectable", "_ariaOrientation", "_ariaOwns", "_ariaPlaceholder", "_ariaPosInSet", "_ariaPressed", "_ariaReadOnly", "readOnly", "_ariaRequired", "required", "_ariaRoleDescription", "_ariaRowCount", "_ariaRowIndex", "_ariaRowSpan", "_ariaSelected", "_ariaSetSize", "_ariaSort", "_ariaValueMax", "_ariaValueMin", "_ariaValueNow", "_ariaValueText", "dataProp", "call", "dataName", "dataValue", "_StyleSheet", "writingDirection", "className", "inlineStyle", "_id", "type"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/modules/createDOMProps/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"aria-activedescendant\", \"accessibilityActiveDescendant\", \"aria-atomic\", \"accessibilityAtomic\", \"aria-autocomplete\", \"accessibilityAutoComplete\", \"aria-busy\", \"accessibilityBusy\", \"aria-checked\", \"accessibilityChecked\", \"aria-colcount\", \"accessibilityColumnCount\", \"aria-colindex\", \"accessibilityColumnIndex\", \"aria-colspan\", \"accessibilityColumnSpan\", \"aria-controls\", \"accessibilityControls\", \"aria-current\", \"accessibilityCurrent\", \"aria-describedby\", \"accessibilityDescribedBy\", \"aria-details\", \"accessibilityDetails\", \"aria-disabled\", \"accessibilityDisabled\", \"aria-errormessage\", \"accessibilityErrorMessage\", \"aria-expanded\", \"accessibilityExpanded\", \"aria-flowto\", \"accessibilityFlowTo\", \"aria-haspopup\", \"accessibilityHasPopup\", \"aria-hidden\", \"accessibilityHidden\", \"aria-invalid\", \"accessibilityInvalid\", \"aria-keyshortcuts\", \"accessibilityKeyShortcuts\", \"aria-label\", \"accessibilityLabel\", \"aria-labelledby\", \"accessibilityLabelledBy\", \"aria-level\", \"accessibilityLevel\", \"aria-live\", \"accessibilityLiveRegion\", \"aria-modal\", \"accessibilityModal\", \"aria-multiline\", \"accessibilityMultiline\", \"aria-multiselectable\", \"accessibilityMultiSelectable\", \"aria-orientation\", \"accessibilityOrientation\", \"aria-owns\", \"accessibilityOwns\", \"aria-placeholder\", \"accessibilityPlaceholder\", \"aria-posinset\", \"accessibilityPosInSet\", \"aria-pressed\", \"accessibilityPressed\", \"aria-readonly\", \"accessibilityReadOnly\", \"aria-required\", \"accessibilityRequired\", \"role\", \"accessibilityRole\", \"aria-roledescription\", \"accessibilityRoleDescription\", \"aria-rowcount\", \"accessibilityRowCount\", \"aria-rowindex\", \"accessibilityRowIndex\", \"aria-rowspan\", \"accessibilityRowSpan\", \"aria-selected\", \"accessibilitySelected\", \"aria-setsize\", \"accessibilitySetSize\", \"aria-sort\", \"accessibilitySort\", \"aria-valuemax\", \"accessibilityValueMax\", \"aria-valuemin\", \"accessibilityValueMin\", \"aria-valuenow\", \"accessibilityValueNow\", \"aria-valuetext\", \"accessibilityValueText\", \"dataSet\", \"focusable\", \"id\", \"nativeID\", \"pointerEvents\", \"style\", \"tabIndex\", \"testID\"];\n/**\n * Copyright (c) Nicolas Gallagher.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport AccessibilityUtil from '../AccessibilityUtil';\nimport StyleSheet from '../../exports/StyleSheet';\nimport { warnOnce } from '../warnOnce';\nvar emptyObject = {};\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\nvar uppercasePattern = /[A-Z]/g;\nfunction toHyphenLower(match) {\n  return '-' + match.toLowerCase();\n}\nfunction hyphenateString(str) {\n  return str.replace(uppercasePattern, toHyphenLower);\n}\nfunction processIDRefList(idRefList) {\n  return isArray(idRefList) ? idRefList.join(' ') : idRefList;\n}\nvar pointerEventsStyles = StyleSheet.create({\n  auto: {\n    pointerEvents: 'auto'\n  },\n  'box-none': {\n    pointerEvents: 'box-none'\n  },\n  'box-only': {\n    pointerEvents: 'box-only'\n  },\n  none: {\n    pointerEvents: 'none'\n  }\n});\nvar createDOMProps = (elementType, props, options) => {\n  if (!props) {\n    props = emptyObject;\n  }\n  var _props = props,\n    ariaActiveDescendant = _props['aria-activedescendant'],\n    accessibilityActiveDescendant = _props.accessibilityActiveDescendant,\n    ariaAtomic = _props['aria-atomic'],\n    accessibilityAtomic = _props.accessibilityAtomic,\n    ariaAutoComplete = _props['aria-autocomplete'],\n    accessibilityAutoComplete = _props.accessibilityAutoComplete,\n    ariaBusy = _props['aria-busy'],\n    accessibilityBusy = _props.accessibilityBusy,\n    ariaChecked = _props['aria-checked'],\n    accessibilityChecked = _props.accessibilityChecked,\n    ariaColumnCount = _props['aria-colcount'],\n    accessibilityColumnCount = _props.accessibilityColumnCount,\n    ariaColumnIndex = _props['aria-colindex'],\n    accessibilityColumnIndex = _props.accessibilityColumnIndex,\n    ariaColumnSpan = _props['aria-colspan'],\n    accessibilityColumnSpan = _props.accessibilityColumnSpan,\n    ariaControls = _props['aria-controls'],\n    accessibilityControls = _props.accessibilityControls,\n    ariaCurrent = _props['aria-current'],\n    accessibilityCurrent = _props.accessibilityCurrent,\n    ariaDescribedBy = _props['aria-describedby'],\n    accessibilityDescribedBy = _props.accessibilityDescribedBy,\n    ariaDetails = _props['aria-details'],\n    accessibilityDetails = _props.accessibilityDetails,\n    ariaDisabled = _props['aria-disabled'],\n    accessibilityDisabled = _props.accessibilityDisabled,\n    ariaErrorMessage = _props['aria-errormessage'],\n    accessibilityErrorMessage = _props.accessibilityErrorMessage,\n    ariaExpanded = _props['aria-expanded'],\n    accessibilityExpanded = _props.accessibilityExpanded,\n    ariaFlowTo = _props['aria-flowto'],\n    accessibilityFlowTo = _props.accessibilityFlowTo,\n    ariaHasPopup = _props['aria-haspopup'],\n    accessibilityHasPopup = _props.accessibilityHasPopup,\n    ariaHidden = _props['aria-hidden'],\n    accessibilityHidden = _props.accessibilityHidden,\n    ariaInvalid = _props['aria-invalid'],\n    accessibilityInvalid = _props.accessibilityInvalid,\n    ariaKeyShortcuts = _props['aria-keyshortcuts'],\n    accessibilityKeyShortcuts = _props.accessibilityKeyShortcuts,\n    ariaLabel = _props['aria-label'],\n    accessibilityLabel = _props.accessibilityLabel,\n    ariaLabelledBy = _props['aria-labelledby'],\n    accessibilityLabelledBy = _props.accessibilityLabelledBy,\n    ariaLevel = _props['aria-level'],\n    accessibilityLevel = _props.accessibilityLevel,\n    ariaLive = _props['aria-live'],\n    accessibilityLiveRegion = _props.accessibilityLiveRegion,\n    ariaModal = _props['aria-modal'],\n    accessibilityModal = _props.accessibilityModal,\n    ariaMultiline = _props['aria-multiline'],\n    accessibilityMultiline = _props.accessibilityMultiline,\n    ariaMultiSelectable = _props['aria-multiselectable'],\n    accessibilityMultiSelectable = _props.accessibilityMultiSelectable,\n    ariaOrientation = _props['aria-orientation'],\n    accessibilityOrientation = _props.accessibilityOrientation,\n    ariaOwns = _props['aria-owns'],\n    accessibilityOwns = _props.accessibilityOwns,\n    ariaPlaceholder = _props['aria-placeholder'],\n    accessibilityPlaceholder = _props.accessibilityPlaceholder,\n    ariaPosInSet = _props['aria-posinset'],\n    accessibilityPosInSet = _props.accessibilityPosInSet,\n    ariaPressed = _props['aria-pressed'],\n    accessibilityPressed = _props.accessibilityPressed,\n    ariaReadOnly = _props['aria-readonly'],\n    accessibilityReadOnly = _props.accessibilityReadOnly,\n    ariaRequired = _props['aria-required'],\n    accessibilityRequired = _props.accessibilityRequired,\n    ariaRole = _props.role,\n    accessibilityRole = _props.accessibilityRole,\n    ariaRoleDescription = _props['aria-roledescription'],\n    accessibilityRoleDescription = _props.accessibilityRoleDescription,\n    ariaRowCount = _props['aria-rowcount'],\n    accessibilityRowCount = _props.accessibilityRowCount,\n    ariaRowIndex = _props['aria-rowindex'],\n    accessibilityRowIndex = _props.accessibilityRowIndex,\n    ariaRowSpan = _props['aria-rowspan'],\n    accessibilityRowSpan = _props.accessibilityRowSpan,\n    ariaSelected = _props['aria-selected'],\n    accessibilitySelected = _props.accessibilitySelected,\n    ariaSetSize = _props['aria-setsize'],\n    accessibilitySetSize = _props.accessibilitySetSize,\n    ariaSort = _props['aria-sort'],\n    accessibilitySort = _props.accessibilitySort,\n    ariaValueMax = _props['aria-valuemax'],\n    accessibilityValueMax = _props.accessibilityValueMax,\n    ariaValueMin = _props['aria-valuemin'],\n    accessibilityValueMin = _props.accessibilityValueMin,\n    ariaValueNow = _props['aria-valuenow'],\n    accessibilityValueNow = _props.accessibilityValueNow,\n    ariaValueText = _props['aria-valuetext'],\n    accessibilityValueText = _props.accessibilityValueText,\n    dataSet = _props.dataSet,\n    focusable = _props.focusable,\n    id = _props.id,\n    nativeID = _props.nativeID,\n    pointerEvents = _props.pointerEvents,\n    style = _props.style,\n    tabIndex = _props.tabIndex,\n    testID = _props.testID,\n    domProps = _objectWithoutPropertiesLoose(_props, _excluded);\n\n  /*\n  if (accessibilityDisabled != null) {\n    warnOnce('accessibilityDisabled', `accessibilityDisabled is deprecated.`);\n  }\n  */\n  var disabled = ariaDisabled || accessibilityDisabled;\n  var role = AccessibilityUtil.propsToAriaRole(props);\n\n  // ACCESSIBILITY\n  /*\n  if (accessibilityActiveDescendant != null) {\n    warnOnce(\n      'accessibilityActiveDescendant',\n      `accessibilityActiveDescendant is deprecated. Use aria-activedescendant.`\n    );\n  }\n  */\n  var _ariaActiveDescendant = ariaActiveDescendant != null ? ariaActiveDescendant : accessibilityActiveDescendant;\n  if (_ariaActiveDescendant != null) {\n    domProps['aria-activedescendant'] = _ariaActiveDescendant;\n  }\n\n  /*\n  if (accessibilityAtomic != null) {\n    warnOnce(\n      'accessibilityAtomic',\n      `accessibilityAtomic is deprecated. Use aria-atomic.`\n    );\n  }\n  */\n  var _ariaAtomic = ariaAtomic != null ? ariaActiveDescendant : accessibilityAtomic;\n  if (_ariaAtomic != null) {\n    domProps['aria-atomic'] = _ariaAtomic;\n  }\n\n  /*\n  if (accessibilityAutoComplete != null) {\n    warnOnce(\n      'accessibilityAutoComplete',\n      `accessibilityAutoComplete is deprecated. Use aria-autocomplete.`\n    );\n  }\n  */\n  var _ariaAutoComplete = ariaAutoComplete != null ? ariaAutoComplete : accessibilityAutoComplete;\n  if (_ariaAutoComplete != null) {\n    domProps['aria-autocomplete'] = _ariaAutoComplete;\n  }\n\n  /*\n  if (accessibilityBusy != null) {\n    warnOnce(\n      'accessibilityBusy',\n      `accessibilityBusy is deprecated. Use aria-busy.`\n    );\n  }\n  */\n  var _ariaBusy = ariaBusy != null ? ariaBusy : accessibilityBusy;\n  if (_ariaBusy != null) {\n    domProps['aria-busy'] = _ariaBusy;\n  }\n\n  /*\n  if (accessibilityChecked != null) {\n    warnOnce(\n      'accessibilityChecked',\n      `accessibilityChecked is deprecated. Use aria-checked.`\n    );\n  }\n  */\n  var _ariaChecked = ariaChecked != null ? ariaChecked : accessibilityChecked;\n  if (_ariaChecked != null) {\n    domProps['aria-checked'] = _ariaChecked;\n  }\n\n  /*\n  if (accessibilityColumnCount != null) {\n    warnOnce(\n      'accessibilityColumnCount',\n      `accessibilityColumnCount is deprecated. Use aria-colcount.`\n    );\n  }\n  */\n  var _ariaColumnCount = ariaColumnCount != null ? ariaColumnCount : accessibilityColumnCount;\n  if (_ariaColumnCount != null) {\n    domProps['aria-colcount'] = _ariaColumnCount;\n  }\n\n  /*\n  if (accessibilityColumnIndex != null) {\n    warnOnce(\n      'accessibilityColumnIndex',\n      `accessibilityColumnIndex is deprecated. Use aria-colindex.`\n    );\n  }\n  */\n  var _ariaColumnIndex = ariaColumnIndex != null ? ariaColumnIndex : accessibilityColumnIndex;\n  if (_ariaColumnIndex != null) {\n    domProps['aria-colindex'] = _ariaColumnIndex;\n  }\n\n  /*\n  if (accessibilityColumnSpan != null) {\n    warnOnce(\n      'accessibilityColumnSpan',\n      `accessibilityColumnSpan is deprecated. Use aria-colspan.`\n    );\n  }\n  */\n  var _ariaColumnSpan = ariaColumnSpan != null ? ariaColumnSpan : accessibilityColumnSpan;\n  if (_ariaColumnSpan != null) {\n    domProps['aria-colspan'] = _ariaColumnSpan;\n  }\n\n  /*\n  if (accessibilityControls != null) {\n    warnOnce(\n      'accessibilityControls',\n      `accessibilityControls is deprecated. Use aria-controls.`\n    );\n  }\n  */\n  var _ariaControls = ariaControls != null ? ariaControls : accessibilityControls;\n  if (_ariaControls != null) {\n    domProps['aria-controls'] = processIDRefList(_ariaControls);\n  }\n\n  /*\n  if (accessibilityCurrent != null) {\n    warnOnce(\n      'accessibilityCurrent',\n      `accessibilityCurrent is deprecated. Use aria-current.`\n    );\n  }\n  */\n  var _ariaCurrent = ariaCurrent != null ? ariaCurrent : accessibilityCurrent;\n  if (_ariaCurrent != null) {\n    domProps['aria-current'] = _ariaCurrent;\n  }\n\n  /*\n  if (accessibilityDescribedBy != null) {\n    warnOnce(\n      'accessibilityDescribedBy',\n      `accessibilityDescribedBy is deprecated. Use aria-describedby.`\n    );\n  }\n  */\n  var _ariaDescribedBy = ariaDescribedBy != null ? ariaDescribedBy : accessibilityDescribedBy;\n  if (_ariaDescribedBy != null) {\n    domProps['aria-describedby'] = processIDRefList(_ariaDescribedBy);\n  }\n\n  /*\n  if (accessibilityDetails != null) {\n    warnOnce(\n      'accessibilityDetails',\n      `accessibilityDetails is deprecated. Use aria-details.`\n    );\n  }\n  */\n  var _ariaDetails = ariaDetails != null ? ariaDetails : accessibilityDetails;\n  if (_ariaDetails != null) {\n    domProps['aria-details'] = _ariaDetails;\n  }\n  if (disabled === true) {\n    domProps['aria-disabled'] = true;\n    // Enhance with native semantics\n    if (elementType === 'button' || elementType === 'form' || elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      domProps.disabled = true;\n    }\n  }\n\n  /*\n  if (accessibilityErrorMessage != null) {\n    warnOnce(\n      'accessibilityErrorMessage',\n      `accessibilityErrorMessage is deprecated. Use aria-errormessage.`\n    );\n  }\n  */\n  var _ariaErrorMessage = ariaErrorMessage != null ? ariaErrorMessage : accessibilityErrorMessage;\n  if (_ariaErrorMessage != null) {\n    domProps['aria-errormessage'] = _ariaErrorMessage;\n  }\n\n  /*\n  if (accessibilityExpanded != null) {\n    warnOnce(\n      'accessibilityExpanded',\n      `accessibilityExpanded is deprecated. Use aria-expanded.`\n    );\n  }\n  */\n  var _ariaExpanded = ariaExpanded != null ? ariaExpanded : accessibilityExpanded;\n  if (_ariaExpanded != null) {\n    domProps['aria-expanded'] = _ariaExpanded;\n  }\n\n  /*\n  if (accessibilityFlowTo != null) {\n    warnOnce(\n      'accessibilityFlowTo',\n      `accessibilityFlowTo is deprecated. Use aria-flowto.`\n    );\n  }\n  */\n  var _ariaFlowTo = ariaFlowTo != null ? ariaFlowTo : accessibilityFlowTo;\n  if (_ariaFlowTo != null) {\n    domProps['aria-flowto'] = processIDRefList(_ariaFlowTo);\n  }\n\n  /*\n  if (accessibilityHasPopup != null) {\n    warnOnce(\n      'accessibilityHasPopup',\n      `accessibilityHasPopup is deprecated. Use aria-haspopup.`\n    );\n  }\n  */\n  var _ariaHasPopup = ariaHasPopup != null ? ariaHasPopup : accessibilityHasPopup;\n  if (_ariaHasPopup != null) {\n    domProps['aria-haspopup'] = _ariaHasPopup;\n  }\n\n  /*\n  if (accessibilityHidden != null) {\n    warnOnce(\n      'accessibilityHidden',\n      `accessibilityHidden is deprecated. Use aria-hidden.`\n    );\n  }\n  */\n  var _ariaHidden = ariaHidden != null ? ariaHidden : accessibilityHidden;\n  if (_ariaHidden === true) {\n    domProps['aria-hidden'] = _ariaHidden;\n  }\n\n  /*\n  if (accessibilityInvalid != null) {\n    warnOnce(\n      'accessibilityInvalid',\n      `accessibilityInvalid is deprecated. Use aria-invalid.`\n    );\n  }\n  */\n  var _ariaInvalid = ariaInvalid != null ? ariaInvalid : accessibilityInvalid;\n  if (_ariaInvalid != null) {\n    domProps['aria-invalid'] = _ariaInvalid;\n  }\n\n  /*\n  if (accessibilityKeyShortcuts != null) {\n    warnOnce(\n      'accessibilityKeyShortcuts',\n      `accessibilityKeyShortcuts is deprecated. Use aria-keyshortcuts.`\n    );\n  }\n  */\n  var _ariaKeyShortcuts = ariaKeyShortcuts != null ? ariaKeyShortcuts : accessibilityKeyShortcuts;\n  if (_ariaKeyShortcuts != null) {\n    domProps['aria-keyshortcuts'] = processIDRefList(_ariaKeyShortcuts);\n  }\n\n  /*\n  if (accessibilityLabel != null) {\n    warnOnce(\n      'accessibilityLabel',\n      `accessibilityLabel is deprecated. Use aria-label.`\n    );\n  }\n  */\n  var _ariaLabel = ariaLabel != null ? ariaLabel : accessibilityLabel;\n  if (_ariaLabel != null) {\n    domProps['aria-label'] = _ariaLabel;\n  }\n\n  /*\n  if (accessibilityLabelledBy != null) {\n    warnOnce(\n      'accessibilityLabelledBy',\n      `accessibilityLabelledBy is deprecated. Use aria-labelledby.`\n    );\n  }\n  */\n  var _ariaLabelledBy = ariaLabelledBy != null ? ariaLabelledBy : accessibilityLabelledBy;\n  if (_ariaLabelledBy != null) {\n    domProps['aria-labelledby'] = processIDRefList(_ariaLabelledBy);\n  }\n\n  /*\n  if (accessibilityLevel != null) {\n    warnOnce(\n      'accessibilityLevel',\n      `accessibilityLevel is deprecated. Use aria-level.`\n    );\n  }\n  */\n  var _ariaLevel = ariaLevel != null ? ariaLevel : accessibilityLevel;\n  if (_ariaLevel != null) {\n    domProps['aria-level'] = _ariaLevel;\n  }\n\n  /*\n  if (accessibilityLiveRegion != null) {\n    warnOnce(\n      'accessibilityLiveRegion',\n      `accessibilityLiveRegion is deprecated. Use aria-live.`\n    );\n  }\n  */\n  var _ariaLive = ariaLive != null ? ariaLive : accessibilityLiveRegion;\n  if (_ariaLive != null) {\n    domProps['aria-live'] = _ariaLive === 'none' ? 'off' : _ariaLive;\n  }\n\n  /*\n  if (accessibilityModal != null) {\n    warnOnce(\n      'accessibilityModal',\n      `accessibilityModal is deprecated. Use aria-modal.`\n    );\n  }\n  */\n  var _ariaModal = ariaModal != null ? ariaModal : accessibilityModal;\n  if (_ariaModal != null) {\n    domProps['aria-modal'] = _ariaModal;\n  }\n\n  /*\n  if (accessibilityMultiline != null) {\n    warnOnce(\n      'accessibilityMultiline',\n      `accessibilityMultiline is deprecated. Use aria-multiline.`\n    );\n  }\n  */\n  var _ariaMultiline = ariaMultiline != null ? ariaMultiline : accessibilityMultiline;\n  if (_ariaMultiline != null) {\n    domProps['aria-multiline'] = _ariaMultiline;\n  }\n\n  /*\n  if (accessibilityMultiSelectable != null) {\n    warnOnce(\n      'accessibilityMultiSelectable',\n      `accessibilityMultiSelectable is deprecated. Use aria-multiselectable.`\n    );\n  }\n  */\n  var _ariaMultiSelectable = ariaMultiSelectable != null ? ariaMultiSelectable : accessibilityMultiSelectable;\n  if (_ariaMultiSelectable != null) {\n    domProps['aria-multiselectable'] = _ariaMultiSelectable;\n  }\n\n  /*\n  if (accessibilityOrientation != null) {\n    warnOnce(\n      'accessibilityOrientation',\n      `accessibilityOrientation is deprecated. Use aria-orientation.`\n    );\n  }\n  */\n  var _ariaOrientation = ariaOrientation != null ? ariaOrientation : accessibilityOrientation;\n  if (_ariaOrientation != null) {\n    domProps['aria-orientation'] = _ariaOrientation;\n  }\n\n  /*\n  if (accessibilityOwns != null) {\n    warnOnce(\n      'accessibilityOwns',\n      `accessibilityOwns is deprecated. Use aria-owns.`\n    );\n  }\n  */\n  var _ariaOwns = ariaOwns != null ? ariaOwns : accessibilityOwns;\n  if (_ariaOwns != null) {\n    domProps['aria-owns'] = processIDRefList(_ariaOwns);\n  }\n\n  /*\n  if (accessibilityPlaceholder != null) {\n    warnOnce(\n      'accessibilityPlaceholder',\n      `accessibilityPlaceholder is deprecated. Use aria-placeholder.`\n    );\n  }\n  */\n  var _ariaPlaceholder = ariaPlaceholder != null ? ariaPlaceholder : accessibilityPlaceholder;\n  if (_ariaPlaceholder != null) {\n    domProps['aria-placeholder'] = _ariaPlaceholder;\n  }\n\n  /*\n  if (accessibilityPosInSet != null) {\n    warnOnce(\n      'accessibilityPosInSet',\n      `accessibilityPosInSet is deprecated. Use aria-posinset.`\n    );\n  }\n  */\n  var _ariaPosInSet = ariaPosInSet != null ? ariaPosInSet : accessibilityPosInSet;\n  if (_ariaPosInSet != null) {\n    domProps['aria-posinset'] = _ariaPosInSet;\n  }\n\n  /*\n  if (accessibilityPressed != null) {\n    warnOnce(\n      'accessibilityPressed',\n      `accessibilityPressed is deprecated. Use aria-pressed.`\n    );\n  }\n  */\n  var _ariaPressed = ariaPressed != null ? ariaPressed : accessibilityPressed;\n  if (_ariaPressed != null) {\n    domProps['aria-pressed'] = _ariaPressed;\n  }\n\n  /*\n  if (accessibilityReadOnly != null) {\n    warnOnce(\n      'accessibilityReadOnly',\n      `accessibilityReadOnly is deprecated. Use aria-readonly.`\n    );\n  }\n  */\n  var _ariaReadOnly = ariaReadOnly != null ? ariaReadOnly : accessibilityReadOnly;\n  if (_ariaReadOnly != null) {\n    domProps['aria-readonly'] = _ariaReadOnly;\n    // Enhance with native semantics\n    if (elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      domProps.readOnly = true;\n    }\n  }\n\n  /*\n  if (accessibilityRequired != null) {\n    warnOnce(\n      'accessibilityRequired',\n      `accessibilityRequired is deprecated. Use aria-required.`\n    );\n  }\n  */\n  var _ariaRequired = ariaRequired != null ? ariaRequired : accessibilityRequired;\n  if (_ariaRequired != null) {\n    domProps['aria-required'] = _ariaRequired;\n    // Enhance with native semantics\n    if (elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      domProps.required = accessibilityRequired;\n    }\n  }\n\n  /*\n  if (accessibilityRole != null) {\n    warnOnce('accessibilityRole', `accessibilityRole is deprecated. Use role.`);\n  }\n  */\n  if (role != null) {\n    // 'presentation' synonym has wider browser support\n    domProps['role'] = role === 'none' ? 'presentation' : role;\n  }\n\n  /*\n  if (accessibilityRoleDescription != null) {\n    warnOnce(\n      'accessibilityRoleDescription',\n      `accessibilityRoleDescription is deprecated. Use aria-roledescription.`\n    );\n  }\n  */\n  var _ariaRoleDescription = ariaRoleDescription != null ? ariaRoleDescription : accessibilityRoleDescription;\n  if (_ariaRoleDescription != null) {\n    domProps['aria-roledescription'] = _ariaRoleDescription;\n  }\n\n  /*\n  if (accessibilityRowCount != null) {\n    warnOnce(\n      'accessibilityRowCount',\n      `accessibilityRowCount is deprecated. Use aria-rowcount.`\n    );\n  }\n  */\n  var _ariaRowCount = ariaRowCount != null ? ariaRowCount : accessibilityRowCount;\n  if (_ariaRowCount != null) {\n    domProps['aria-rowcount'] = _ariaRowCount;\n  }\n\n  /*\n  if (accessibilityRowIndex != null) {\n    warnOnce(\n      'accessibilityRowIndex',\n      `accessibilityRowIndex is deprecated. Use aria-rowindex.`\n    );\n  }\n  */\n  var _ariaRowIndex = ariaRowIndex != null ? ariaRowIndex : accessibilityRowIndex;\n  if (_ariaRowIndex != null) {\n    domProps['aria-rowindex'] = _ariaRowIndex;\n  }\n\n  /*\n  if (accessibilityRowSpan != null) {\n    warnOnce(\n      'accessibilityRowSpan',\n      `accessibilityRowSpan is deprecated. Use aria-rowspan.`\n    );\n  }\n  */\n  var _ariaRowSpan = ariaRowSpan != null ? ariaRowSpan : accessibilityRowSpan;\n  if (_ariaRowSpan != null) {\n    domProps['aria-rowspan'] = _ariaRowSpan;\n  }\n\n  /*\n  if (accessibilitySelected != null) {\n    warnOnce(\n      'accessibilitySelected',\n      `accessibilitySelected is deprecated. Use aria-selected.`\n    );\n  }\n  */\n  var _ariaSelected = ariaSelected != null ? ariaSelected : accessibilitySelected;\n  if (_ariaSelected != null) {\n    domProps['aria-selected'] = _ariaSelected;\n  }\n\n  /*\n  if (accessibilitySetSize != null) {\n    warnOnce(\n      'accessibilitySetSize',\n      `accessibilitySetSize is deprecated. Use aria-setsize.`\n    );\n  }\n  */\n  var _ariaSetSize = ariaSetSize != null ? ariaSetSize : accessibilitySetSize;\n  if (_ariaSetSize != null) {\n    domProps['aria-setsize'] = _ariaSetSize;\n  }\n\n  /*\n  if (accessibilitySort != null) {\n    warnOnce(\n      'accessibilitySort',\n      `accessibilitySort is deprecated. Use aria-sort.`\n    );\n  }\n  */\n  var _ariaSort = ariaSort != null ? ariaSort : accessibilitySort;\n  if (_ariaSort != null) {\n    domProps['aria-sort'] = _ariaSort;\n  }\n\n  /*\n  if (accessibilityValueMax != null) {\n    warnOnce(\n      'accessibilityValueMax',\n      `accessibilityValueMax is deprecated. Use aria-valuemax.`\n    );\n  }\n  */\n  var _ariaValueMax = ariaValueMax != null ? ariaValueMax : accessibilityValueMax;\n  if (_ariaValueMax != null) {\n    domProps['aria-valuemax'] = _ariaValueMax;\n  }\n\n  /*\n  if (accessibilityValueMin != null) {\n    warnOnce(\n      'accessibilityValueMin',\n      `accessibilityValueMin is deprecated. Use aria-valuemin.`\n    );\n  }\n  */\n  var _ariaValueMin = ariaValueMin != null ? ariaValueMin : accessibilityValueMin;\n  if (_ariaValueMin != null) {\n    domProps['aria-valuemin'] = _ariaValueMin;\n  }\n\n  /*\n  if (accessibilityValueNow != null) {\n    warnOnce(\n      'accessibilityValueNow',\n      `accessibilityValueNow is deprecated. Use aria-valuenow.`\n    );\n  }\n  */\n  var _ariaValueNow = ariaValueNow != null ? ariaValueNow : accessibilityValueNow;\n  if (_ariaValueNow != null) {\n    domProps['aria-valuenow'] = _ariaValueNow;\n  }\n\n  /*\n  if (accessibilityValueText != null) {\n    warnOnce(\n      'accessibilityValueText',\n      `accessibilityValueText is deprecated. Use aria-valuetext.`\n    );\n  }\n  */\n  var _ariaValueText = ariaValueText != null ? ariaValueText : accessibilityValueText;\n  if (_ariaValueText != null) {\n    domProps['aria-valuetext'] = _ariaValueText;\n  }\n\n  // \"dataSet\" replaced with \"data-*\"\n  if (dataSet != null) {\n    for (var dataProp in dataSet) {\n      if (hasOwnProperty.call(dataSet, dataProp)) {\n        var dataName = hyphenateString(dataProp);\n        var dataValue = dataSet[dataProp];\n        if (dataValue != null) {\n          domProps[\"data-\" + dataName] = dataValue;\n        }\n      }\n    }\n  }\n\n  // FOCUS\n  if (tabIndex === 0 || tabIndex === '0' || tabIndex === -1 || tabIndex === '-1') {\n    domProps.tabIndex = tabIndex;\n  } else {\n    /*\n    if (focusable != null) {\n      warnOnce('focusable', `focusable is deprecated.`);\n    }\n    */\n\n    // \"focusable\" indicates that an element may be a keyboard tab-stop.\n    if (focusable === false) {\n      domProps.tabIndex = '-1';\n    }\n    if (\n    // These native elements are keyboard focusable by default\n    elementType === 'a' || elementType === 'button' || elementType === 'input' || elementType === 'select' || elementType === 'textarea') {\n      if (focusable === false || accessibilityDisabled === true) {\n        domProps.tabIndex = '-1';\n      }\n    } else if (\n    // These roles are made keyboard focusable by default\n    role === 'button' || role === 'checkbox' || role === 'link' || role === 'radio' || role === 'textbox' || role === 'switch') {\n      if (focusable !== false) {\n        domProps.tabIndex = '0';\n      }\n    } else {\n      // Everything else must explicitly set the prop\n      if (focusable === true) {\n        domProps.tabIndex = '0';\n      }\n    }\n  }\n\n  // Resolve styles\n  if (pointerEvents != null) {\n    warnOnce('pointerEvents', \"props.pointerEvents is deprecated. Use style.pointerEvents\");\n  }\n  var _StyleSheet = StyleSheet([style, pointerEvents && pointerEventsStyles[pointerEvents]], _objectSpread({\n      writingDirection: 'ltr'\n    }, options)),\n    className = _StyleSheet[0],\n    inlineStyle = _StyleSheet[1];\n  if (className) {\n    domProps.className = className;\n  }\n  if (inlineStyle) {\n    domProps.style = inlineStyle;\n  }\n\n  // OTHER\n  // Native element ID\n  /*\n  if (nativeID != null) {\n    warnOnce('nativeID', `nativeID is deprecated. Use id.`);\n  }\n  */\n  var _id = id != null ? id : nativeID;\n  if (_id != null) {\n    domProps.id = _id;\n  }\n  // Automated test IDs\n  if (testID != null) {\n    domProps['data-testid'] = testID;\n  }\n  if (domProps.type == null && elementType === 'button') {\n    domProps.type = 'button';\n  }\n  return domProps;\n};\nexport default createDOMProps;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAChE,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,uBAAuB,EAAE,+BAA+B,EAAE,aAAa,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,WAAW,EAAE,mBAAmB,EAAE,cAAc,EAAE,sBAAsB,EAAE,eAAe,EAAE,0BAA0B,EAAE,eAAe,EAAE,0BAA0B,EAAE,cAAc,EAAE,yBAAyB,EAAE,eAAe,EAAE,uBAAuB,EAAE,cAAc,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,cAAc,EAAE,sBAAsB,EAAE,eAAe,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,eAAe,EAAE,uBAAuB,EAAE,aAAa,EAAE,qBAAqB,EAAE,eAAe,EAAE,uBAAuB,EAAE,aAAa,EAAE,qBAAqB,EAAE,cAAc,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,YAAY,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,YAAY,EAAE,oBAAoB,EAAE,WAAW,EAAE,yBAAyB,EAAE,YAAY,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,sBAAsB,EAAE,8BAA8B,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,WAAW,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,eAAe,EAAE,uBAAuB,EAAE,cAAc,EAAE,sBAAsB,EAAE,eAAe,EAAE,uBAAuB,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,8BAA8B,EAAE,eAAe,EAAE,uBAAuB,EAAE,eAAe,EAAE,uBAAuB,EAAE,cAAc,EAAE,sBAAsB,EAAE,eAAe,EAAE,uBAAuB,EAAE,cAAc,EAAE,sBAAsB,EAAE,WAAW,EAAE,mBAAmB,EAAE,eAAe,EAAE,uBAAuB,EAAE,eAAe,EAAE,uBAAuB,EAAE,eAAe,EAAE,uBAAuB,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,eAAe,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC;AAUxgE,OAAOC,iBAAiB;AACxB,OAAOC,UAAU;AACjB,SAASC,QAAQ;AACjB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AACpD,IAAIG,OAAO,GAAGC,KAAK,CAACD,OAAO;AAC3B,IAAIE,gBAAgB,GAAG,QAAQ;AAC/B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,GAAG,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;AAClC;AACA,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACC,OAAO,CAACN,gBAAgB,EAAEC,aAAa,CAAC;AACrD;AACA,SAASM,gBAAgBA,CAACC,SAAS,EAAE;EACnC,OAAOV,OAAO,CAACU,SAAS,CAAC,GAAGA,SAAS,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGD,SAAS;AAC7D;AACA,IAAIE,mBAAmB,GAAGlB,UAAU,CAACmB,MAAM,CAAC;EAC1CC,IAAI,EAAE;IACJC,aAAa,EAAE;EACjB,CAAC;EACD,UAAU,EAAE;IACVA,aAAa,EAAE;EACjB,CAAC;EACD,UAAU,EAAE;IACVA,aAAa,EAAE;EACjB,CAAC;EACDC,IAAI,EAAE;IACJD,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AACF,IAAIE,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,WAAW,EAAEC,KAAK,EAAEC,OAAO,EAAK;EACpD,IAAI,CAACD,KAAK,EAAE;IACVA,KAAK,GAAGvB,WAAW;EACrB;EACA,IAAIyB,MAAM,GAAGF,KAAK;IAChBG,oBAAoB,GAAGD,MAAM,CAAC,uBAAuB,CAAC;IACtDE,6BAA6B,GAAGF,MAAM,CAACE,6BAA6B;IACpEC,UAAU,GAAGH,MAAM,CAAC,aAAa,CAAC;IAClCI,mBAAmB,GAAGJ,MAAM,CAACI,mBAAmB;IAChDC,gBAAgB,GAAGL,MAAM,CAAC,mBAAmB,CAAC;IAC9CM,yBAAyB,GAAGN,MAAM,CAACM,yBAAyB;IAC5DC,QAAQ,GAAGP,MAAM,CAAC,WAAW,CAAC;IAC9BQ,iBAAiB,GAAGR,MAAM,CAACQ,iBAAiB;IAC5CC,WAAW,GAAGT,MAAM,CAAC,cAAc,CAAC;IACpCU,oBAAoB,GAAGV,MAAM,CAACU,oBAAoB;IAClDC,eAAe,GAAGX,MAAM,CAAC,eAAe,CAAC;IACzCY,wBAAwB,GAAGZ,MAAM,CAACY,wBAAwB;IAC1DC,eAAe,GAAGb,MAAM,CAAC,eAAe,CAAC;IACzCc,wBAAwB,GAAGd,MAAM,CAACc,wBAAwB;IAC1DC,cAAc,GAAGf,MAAM,CAAC,cAAc,CAAC;IACvCgB,uBAAuB,GAAGhB,MAAM,CAACgB,uBAAuB;IACxDC,YAAY,GAAGjB,MAAM,CAAC,eAAe,CAAC;IACtCkB,qBAAqB,GAAGlB,MAAM,CAACkB,qBAAqB;IACpDC,WAAW,GAAGnB,MAAM,CAAC,cAAc,CAAC;IACpCoB,oBAAoB,GAAGpB,MAAM,CAACoB,oBAAoB;IAClDC,eAAe,GAAGrB,MAAM,CAAC,kBAAkB,CAAC;IAC5CsB,wBAAwB,GAAGtB,MAAM,CAACsB,wBAAwB;IAC1DC,WAAW,GAAGvB,MAAM,CAAC,cAAc,CAAC;IACpCwB,oBAAoB,GAAGxB,MAAM,CAACwB,oBAAoB;IAClDC,YAAY,GAAGzB,MAAM,CAAC,eAAe,CAAC;IACtC0B,qBAAqB,GAAG1B,MAAM,CAAC0B,qBAAqB;IACpDC,gBAAgB,GAAG3B,MAAM,CAAC,mBAAmB,CAAC;IAC9C4B,yBAAyB,GAAG5B,MAAM,CAAC4B,yBAAyB;IAC5DC,YAAY,GAAG7B,MAAM,CAAC,eAAe,CAAC;IACtC8B,qBAAqB,GAAG9B,MAAM,CAAC8B,qBAAqB;IACpDC,UAAU,GAAG/B,MAAM,CAAC,aAAa,CAAC;IAClCgC,mBAAmB,GAAGhC,MAAM,CAACgC,mBAAmB;IAChDC,YAAY,GAAGjC,MAAM,CAAC,eAAe,CAAC;IACtCkC,qBAAqB,GAAGlC,MAAM,CAACkC,qBAAqB;IACpDC,UAAU,GAAGnC,MAAM,CAAC,aAAa,CAAC;IAClCoC,mBAAmB,GAAGpC,MAAM,CAACoC,mBAAmB;IAChDC,WAAW,GAAGrC,MAAM,CAAC,cAAc,CAAC;IACpCsC,oBAAoB,GAAGtC,MAAM,CAACsC,oBAAoB;IAClDC,gBAAgB,GAAGvC,MAAM,CAAC,mBAAmB,CAAC;IAC9CwC,yBAAyB,GAAGxC,MAAM,CAACwC,yBAAyB;IAC5DC,SAAS,GAAGzC,MAAM,CAAC,YAAY,CAAC;IAChC0C,kBAAkB,GAAG1C,MAAM,CAAC0C,kBAAkB;IAC9CC,cAAc,GAAG3C,MAAM,CAAC,iBAAiB,CAAC;IAC1C4C,uBAAuB,GAAG5C,MAAM,CAAC4C,uBAAuB;IACxDC,SAAS,GAAG7C,MAAM,CAAC,YAAY,CAAC;IAChC8C,kBAAkB,GAAG9C,MAAM,CAAC8C,kBAAkB;IAC9CC,QAAQ,GAAG/C,MAAM,CAAC,WAAW,CAAC;IAC9BgD,uBAAuB,GAAGhD,MAAM,CAACgD,uBAAuB;IACxDC,SAAS,GAAGjD,MAAM,CAAC,YAAY,CAAC;IAChCkD,kBAAkB,GAAGlD,MAAM,CAACkD,kBAAkB;IAC9CC,aAAa,GAAGnD,MAAM,CAAC,gBAAgB,CAAC;IACxCoD,sBAAsB,GAAGpD,MAAM,CAACoD,sBAAsB;IACtDC,mBAAmB,GAAGrD,MAAM,CAAC,sBAAsB,CAAC;IACpDsD,4BAA4B,GAAGtD,MAAM,CAACsD,4BAA4B;IAClEC,eAAe,GAAGvD,MAAM,CAAC,kBAAkB,CAAC;IAC5CwD,wBAAwB,GAAGxD,MAAM,CAACwD,wBAAwB;IAC1DC,QAAQ,GAAGzD,MAAM,CAAC,WAAW,CAAC;IAC9B0D,iBAAiB,GAAG1D,MAAM,CAAC0D,iBAAiB;IAC5CC,eAAe,GAAG3D,MAAM,CAAC,kBAAkB,CAAC;IAC5C4D,wBAAwB,GAAG5D,MAAM,CAAC4D,wBAAwB;IAC1DC,YAAY,GAAG7D,MAAM,CAAC,eAAe,CAAC;IACtC8D,qBAAqB,GAAG9D,MAAM,CAAC8D,qBAAqB;IACpDC,WAAW,GAAG/D,MAAM,CAAC,cAAc,CAAC;IACpCgE,oBAAoB,GAAGhE,MAAM,CAACgE,oBAAoB;IAClDC,YAAY,GAAGjE,MAAM,CAAC,eAAe,CAAC;IACtCkE,qBAAqB,GAAGlE,MAAM,CAACkE,qBAAqB;IACpDC,YAAY,GAAGnE,MAAM,CAAC,eAAe,CAAC;IACtCoE,qBAAqB,GAAGpE,MAAM,CAACoE,qBAAqB;IACpDC,QAAQ,GAAGrE,MAAM,CAACsE,IAAI;IACtBC,iBAAiB,GAAGvE,MAAM,CAACuE,iBAAiB;IAC5CC,mBAAmB,GAAGxE,MAAM,CAAC,sBAAsB,CAAC;IACpDyE,4BAA4B,GAAGzE,MAAM,CAACyE,4BAA4B;IAClEC,YAAY,GAAG1E,MAAM,CAAC,eAAe,CAAC;IACtC2E,qBAAqB,GAAG3E,MAAM,CAAC2E,qBAAqB;IACpDC,YAAY,GAAG5E,MAAM,CAAC,eAAe,CAAC;IACtC6E,qBAAqB,GAAG7E,MAAM,CAAC6E,qBAAqB;IACpDC,WAAW,GAAG9E,MAAM,CAAC,cAAc,CAAC;IACpC+E,oBAAoB,GAAG/E,MAAM,CAAC+E,oBAAoB;IAClDC,YAAY,GAAGhF,MAAM,CAAC,eAAe,CAAC;IACtCiF,qBAAqB,GAAGjF,MAAM,CAACiF,qBAAqB;IACpDC,WAAW,GAAGlF,MAAM,CAAC,cAAc,CAAC;IACpCmF,oBAAoB,GAAGnF,MAAM,CAACmF,oBAAoB;IAClDC,QAAQ,GAAGpF,MAAM,CAAC,WAAW,CAAC;IAC9BqF,iBAAiB,GAAGrF,MAAM,CAACqF,iBAAiB;IAC5CC,YAAY,GAAGtF,MAAM,CAAC,eAAe,CAAC;IACtCuF,qBAAqB,GAAGvF,MAAM,CAACuF,qBAAqB;IACpDC,YAAY,GAAGxF,MAAM,CAAC,eAAe,CAAC;IACtCyF,qBAAqB,GAAGzF,MAAM,CAACyF,qBAAqB;IACpDC,YAAY,GAAG1F,MAAM,CAAC,eAAe,CAAC;IACtC2F,qBAAqB,GAAG3F,MAAM,CAAC2F,qBAAqB;IACpDC,aAAa,GAAG5F,MAAM,CAAC,gBAAgB,CAAC;IACxC6F,sBAAsB,GAAG7F,MAAM,CAAC6F,sBAAsB;IACtDC,OAAO,GAAG9F,MAAM,CAAC8F,OAAO;IACxBC,SAAS,GAAG/F,MAAM,CAAC+F,SAAS;IAC5BC,EAAE,GAAGhG,MAAM,CAACgG,EAAE;IACdC,QAAQ,GAAGjG,MAAM,CAACiG,QAAQ;IAC1BvG,aAAa,GAAGM,MAAM,CAACN,aAAa;IACpCwG,KAAK,GAAGlG,MAAM,CAACkG,KAAK;IACpBC,QAAQ,GAAGnG,MAAM,CAACmG,QAAQ;IAC1BC,MAAM,GAAGpG,MAAM,CAACoG,MAAM;IACtBC,QAAQ,GAAGnI,6BAA6B,CAAC8B,MAAM,EAAE7B,SAAS,CAAC;EAO7D,IAAImI,QAAQ,GAAG7E,YAAY,IAAIC,qBAAqB;EACpD,IAAI4C,IAAI,GAAGlG,iBAAiB,CAACmI,eAAe,CAACzG,KAAK,CAAC;EAWnD,IAAI0G,qBAAqB,GAAGvG,oBAAoB,IAAI,IAAI,GAAGA,oBAAoB,GAAGC,6BAA6B;EAC/G,IAAIsG,qBAAqB,IAAI,IAAI,EAAE;IACjCH,QAAQ,CAAC,uBAAuB,CAAC,GAAGG,qBAAqB;EAC3D;EAUA,IAAIC,WAAW,GAAGtG,UAAU,IAAI,IAAI,GAAGF,oBAAoB,GAAGG,mBAAmB;EACjF,IAAIqG,WAAW,IAAI,IAAI,EAAE;IACvBJ,QAAQ,CAAC,aAAa,CAAC,GAAGI,WAAW;EACvC;EAUA,IAAIC,iBAAiB,GAAGrG,gBAAgB,IAAI,IAAI,GAAGA,gBAAgB,GAAGC,yBAAyB;EAC/F,IAAIoG,iBAAiB,IAAI,IAAI,EAAE;IAC7BL,QAAQ,CAAC,mBAAmB,CAAC,GAAGK,iBAAiB;EACnD;EAUA,IAAIC,SAAS,GAAGpG,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGC,iBAAiB;EAC/D,IAAImG,SAAS,IAAI,IAAI,EAAE;IACrBN,QAAQ,CAAC,WAAW,CAAC,GAAGM,SAAS;EACnC;EAUA,IAAIC,YAAY,GAAGnG,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGC,oBAAoB;EAC3E,IAAIkG,YAAY,IAAI,IAAI,EAAE;IACxBP,QAAQ,CAAC,cAAc,CAAC,GAAGO,YAAY;EACzC;EAUA,IAAIC,gBAAgB,GAAGlG,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAGC,wBAAwB;EAC3F,IAAIiG,gBAAgB,IAAI,IAAI,EAAE;IAC5BR,QAAQ,CAAC,eAAe,CAAC,GAAGQ,gBAAgB;EAC9C;EAUA,IAAIC,gBAAgB,GAAGjG,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAGC,wBAAwB;EAC3F,IAAIgG,gBAAgB,IAAI,IAAI,EAAE;IAC5BT,QAAQ,CAAC,eAAe,CAAC,GAAGS,gBAAgB;EAC9C;EAUA,IAAIC,eAAe,GAAGhG,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGC,uBAAuB;EACvF,IAAI+F,eAAe,IAAI,IAAI,EAAE;IAC3BV,QAAQ,CAAC,cAAc,CAAC,GAAGU,eAAe;EAC5C;EAUA,IAAIC,aAAa,GAAG/F,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAI8F,aAAa,IAAI,IAAI,EAAE;IACzBX,QAAQ,CAAC,eAAe,CAAC,GAAGjH,gBAAgB,CAAC4H,aAAa,CAAC;EAC7D;EAUA,IAAIC,YAAY,GAAG9F,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGC,oBAAoB;EAC3E,IAAI6F,YAAY,IAAI,IAAI,EAAE;IACxBZ,QAAQ,CAAC,cAAc,CAAC,GAAGY,YAAY;EACzC;EAUA,IAAIC,gBAAgB,GAAG7F,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAGC,wBAAwB;EAC3F,IAAI4F,gBAAgB,IAAI,IAAI,EAAE;IAC5Bb,QAAQ,CAAC,kBAAkB,CAAC,GAAGjH,gBAAgB,CAAC8H,gBAAgB,CAAC;EACnE;EAUA,IAAIC,YAAY,GAAG5F,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGC,oBAAoB;EAC3E,IAAI2F,YAAY,IAAI,IAAI,EAAE;IACxBd,QAAQ,CAAC,cAAc,CAAC,GAAGc,YAAY;EACzC;EACA,IAAIb,QAAQ,KAAK,IAAI,EAAE;IACrBD,QAAQ,CAAC,eAAe,CAAC,GAAG,IAAI;IAEhC,IAAIxG,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,UAAU,EAAE;MAC3IwG,QAAQ,CAACC,QAAQ,GAAG,IAAI;IAC1B;EACF;EAUA,IAAIc,iBAAiB,GAAGzF,gBAAgB,IAAI,IAAI,GAAGA,gBAAgB,GAAGC,yBAAyB;EAC/F,IAAIwF,iBAAiB,IAAI,IAAI,EAAE;IAC7Bf,QAAQ,CAAC,mBAAmB,CAAC,GAAGe,iBAAiB;EACnD;EAUA,IAAIC,aAAa,GAAGxF,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIuF,aAAa,IAAI,IAAI,EAAE;IACzBhB,QAAQ,CAAC,eAAe,CAAC,GAAGgB,aAAa;EAC3C;EAUA,IAAIC,WAAW,GAAGvF,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGC,mBAAmB;EACvE,IAAIsF,WAAW,IAAI,IAAI,EAAE;IACvBjB,QAAQ,CAAC,aAAa,CAAC,GAAGjH,gBAAgB,CAACkI,WAAW,CAAC;EACzD;EAUA,IAAIC,aAAa,GAAGtF,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIqF,aAAa,IAAI,IAAI,EAAE;IACzBlB,QAAQ,CAAC,eAAe,CAAC,GAAGkB,aAAa;EAC3C;EAUA,IAAIC,WAAW,GAAGrF,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGC,mBAAmB;EACvE,IAAIoF,WAAW,KAAK,IAAI,EAAE;IACxBnB,QAAQ,CAAC,aAAa,CAAC,GAAGmB,WAAW;EACvC;EAUA,IAAIC,YAAY,GAAGpF,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGC,oBAAoB;EAC3E,IAAImF,YAAY,IAAI,IAAI,EAAE;IACxBpB,QAAQ,CAAC,cAAc,CAAC,GAAGoB,YAAY;EACzC;EAUA,IAAIC,iBAAiB,GAAGnF,gBAAgB,IAAI,IAAI,GAAGA,gBAAgB,GAAGC,yBAAyB;EAC/F,IAAIkF,iBAAiB,IAAI,IAAI,EAAE;IAC7BrB,QAAQ,CAAC,mBAAmB,CAAC,GAAGjH,gBAAgB,CAACsI,iBAAiB,CAAC;EACrE;EAUA,IAAIC,UAAU,GAAGlF,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,kBAAkB;EACnE,IAAIiF,UAAU,IAAI,IAAI,EAAE;IACtBtB,QAAQ,CAAC,YAAY,CAAC,GAAGsB,UAAU;EACrC;EAUA,IAAIC,eAAe,GAAGjF,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGC,uBAAuB;EACvF,IAAIgF,eAAe,IAAI,IAAI,EAAE;IAC3BvB,QAAQ,CAAC,iBAAiB,CAAC,GAAGjH,gBAAgB,CAACwI,eAAe,CAAC;EACjE;EAUA,IAAIC,UAAU,GAAGhF,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,kBAAkB;EACnE,IAAI+E,UAAU,IAAI,IAAI,EAAE;IACtBxB,QAAQ,CAAC,YAAY,CAAC,GAAGwB,UAAU;EACrC;EAUA,IAAIC,SAAS,GAAG/E,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGC,uBAAuB;EACrE,IAAI8E,SAAS,IAAI,IAAI,EAAE;IACrBzB,QAAQ,CAAC,WAAW,CAAC,GAAGyB,SAAS,KAAK,MAAM,GAAG,KAAK,GAAGA,SAAS;EAClE;EAUA,IAAIC,UAAU,GAAG9E,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,kBAAkB;EACnE,IAAI6E,UAAU,IAAI,IAAI,EAAE;IACtB1B,QAAQ,CAAC,YAAY,CAAC,GAAG0B,UAAU;EACrC;EAUA,IAAIC,cAAc,GAAG7E,aAAa,IAAI,IAAI,GAAGA,aAAa,GAAGC,sBAAsB;EACnF,IAAI4E,cAAc,IAAI,IAAI,EAAE;IAC1B3B,QAAQ,CAAC,gBAAgB,CAAC,GAAG2B,cAAc;EAC7C;EAUA,IAAIC,oBAAoB,GAAG5E,mBAAmB,IAAI,IAAI,GAAGA,mBAAmB,GAAGC,4BAA4B;EAC3G,IAAI2E,oBAAoB,IAAI,IAAI,EAAE;IAChC5B,QAAQ,CAAC,sBAAsB,CAAC,GAAG4B,oBAAoB;EACzD;EAUA,IAAIC,gBAAgB,GAAG3E,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAGC,wBAAwB;EAC3F,IAAI0E,gBAAgB,IAAI,IAAI,EAAE;IAC5B7B,QAAQ,CAAC,kBAAkB,CAAC,GAAG6B,gBAAgB;EACjD;EAUA,IAAIC,SAAS,GAAG1E,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGC,iBAAiB;EAC/D,IAAIyE,SAAS,IAAI,IAAI,EAAE;IACrB9B,QAAQ,CAAC,WAAW,CAAC,GAAGjH,gBAAgB,CAAC+I,SAAS,CAAC;EACrD;EAUA,IAAIC,gBAAgB,GAAGzE,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAGC,wBAAwB;EAC3F,IAAIwE,gBAAgB,IAAI,IAAI,EAAE;IAC5B/B,QAAQ,CAAC,kBAAkB,CAAC,GAAG+B,gBAAgB;EACjD;EAUA,IAAIC,aAAa,GAAGxE,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIuE,aAAa,IAAI,IAAI,EAAE;IACzBhC,QAAQ,CAAC,eAAe,CAAC,GAAGgC,aAAa;EAC3C;EAUA,IAAIC,YAAY,GAAGvE,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGC,oBAAoB;EAC3E,IAAIsE,YAAY,IAAI,IAAI,EAAE;IACxBjC,QAAQ,CAAC,cAAc,CAAC,GAAGiC,YAAY;EACzC;EAUA,IAAIC,aAAa,GAAGtE,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIqE,aAAa,IAAI,IAAI,EAAE;IACzBlC,QAAQ,CAAC,eAAe,CAAC,GAAGkC,aAAa;IAEzC,IAAI1I,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,UAAU,EAAE;MACrFwG,QAAQ,CAACmC,QAAQ,GAAG,IAAI;IAC1B;EACF;EAUA,IAAIC,aAAa,GAAGtE,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIqE,aAAa,IAAI,IAAI,EAAE;IACzBpC,QAAQ,CAAC,eAAe,CAAC,GAAGoC,aAAa;IAEzC,IAAI5I,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,UAAU,EAAE;MACrFwG,QAAQ,CAACqC,QAAQ,GAAGtE,qBAAqB;IAC3C;EACF;EAOA,IAAIE,IAAI,IAAI,IAAI,EAAE;IAEhB+B,QAAQ,CAAC,MAAM,CAAC,GAAG/B,IAAI,KAAK,MAAM,GAAG,cAAc,GAAGA,IAAI;EAC5D;EAUA,IAAIqE,oBAAoB,GAAGnE,mBAAmB,IAAI,IAAI,GAAGA,mBAAmB,GAAGC,4BAA4B;EAC3G,IAAIkE,oBAAoB,IAAI,IAAI,EAAE;IAChCtC,QAAQ,CAAC,sBAAsB,CAAC,GAAGsC,oBAAoB;EACzD;EAUA,IAAIC,aAAa,GAAGlE,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIiE,aAAa,IAAI,IAAI,EAAE;IACzBvC,QAAQ,CAAC,eAAe,CAAC,GAAGuC,aAAa;EAC3C;EAUA,IAAIC,aAAa,GAAGjE,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIgE,aAAa,IAAI,IAAI,EAAE;IACzBxC,QAAQ,CAAC,eAAe,CAAC,GAAGwC,aAAa;EAC3C;EAUA,IAAIC,YAAY,GAAGhE,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGC,oBAAoB;EAC3E,IAAI+D,YAAY,IAAI,IAAI,EAAE;IACxBzC,QAAQ,CAAC,cAAc,CAAC,GAAGyC,YAAY;EACzC;EAUA,IAAIC,aAAa,GAAG/D,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAI8D,aAAa,IAAI,IAAI,EAAE;IACzB1C,QAAQ,CAAC,eAAe,CAAC,GAAG0C,aAAa;EAC3C;EAUA,IAAIC,YAAY,GAAG9D,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGC,oBAAoB;EAC3E,IAAI6D,YAAY,IAAI,IAAI,EAAE;IACxB3C,QAAQ,CAAC,cAAc,CAAC,GAAG2C,YAAY;EACzC;EAUA,IAAIC,SAAS,GAAG7D,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGC,iBAAiB;EAC/D,IAAI4D,SAAS,IAAI,IAAI,EAAE;IACrB5C,QAAQ,CAAC,WAAW,CAAC,GAAG4C,SAAS;EACnC;EAUA,IAAIC,aAAa,GAAG5D,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAI2D,aAAa,IAAI,IAAI,EAAE;IACzB7C,QAAQ,CAAC,eAAe,CAAC,GAAG6C,aAAa;EAC3C;EAUA,IAAIC,aAAa,GAAG3D,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAI0D,aAAa,IAAI,IAAI,EAAE;IACzB9C,QAAQ,CAAC,eAAe,CAAC,GAAG8C,aAAa;EAC3C;EAUA,IAAIC,aAAa,GAAG1D,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGC,qBAAqB;EAC/E,IAAIyD,aAAa,IAAI,IAAI,EAAE;IACzB/C,QAAQ,CAAC,eAAe,CAAC,GAAG+C,aAAa;EAC3C;EAUA,IAAIC,cAAc,GAAGzD,aAAa,IAAI,IAAI,GAAGA,aAAa,GAAGC,sBAAsB;EACnF,IAAIwD,cAAc,IAAI,IAAI,EAAE;IAC1BhD,QAAQ,CAAC,gBAAgB,CAAC,GAAGgD,cAAc;EAC7C;EAGA,IAAIvD,OAAO,IAAI,IAAI,EAAE;IACnB,KAAK,IAAIwD,QAAQ,IAAIxD,OAAO,EAAE;MAC5B,IAAItH,cAAc,CAAC+K,IAAI,CAACzD,OAAO,EAAEwD,QAAQ,CAAC,EAAE;QAC1C,IAAIE,QAAQ,GAAGvK,eAAe,CAACqK,QAAQ,CAAC;QACxC,IAAIG,SAAS,GAAG3D,OAAO,CAACwD,QAAQ,CAAC;QACjC,IAAIG,SAAS,IAAI,IAAI,EAAE;UACrBpD,QAAQ,CAAC,OAAO,GAAGmD,QAAQ,CAAC,GAAGC,SAAS;QAC1C;MACF;IACF;EACF;EAGA,IAAItD,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,EAAE;IAC9EE,QAAQ,CAACF,QAAQ,GAAGA,QAAQ;EAC9B,CAAC,MAAM;IAQL,IAAIJ,SAAS,KAAK,KAAK,EAAE;MACvBM,QAAQ,CAACF,QAAQ,GAAG,IAAI;IAC1B;IACA,IAEAtG,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,UAAU,EAAE;MACpI,IAAIkG,SAAS,KAAK,KAAK,IAAIrE,qBAAqB,KAAK,IAAI,EAAE;QACzD2E,QAAQ,CAACF,QAAQ,GAAG,IAAI;MAC1B;IACF,CAAC,MAAM,IAEP7B,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,QAAQ,EAAE;MAC1H,IAAIyB,SAAS,KAAK,KAAK,EAAE;QACvBM,QAAQ,CAACF,QAAQ,GAAG,GAAG;MACzB;IACF,CAAC,MAAM;MAEL,IAAIJ,SAAS,KAAK,IAAI,EAAE;QACtBM,QAAQ,CAACF,QAAQ,GAAG,GAAG;MACzB;IACF;EACF;EAGA,IAAIzG,aAAa,IAAI,IAAI,EAAE;IACzBpB,QAAQ,CAAC,eAAe,EAAE,4DAA4D,CAAC;EACzF;EACA,IAAIoL,WAAW,GAAGrL,UAAU,CAAC,CAAC6H,KAAK,EAAExG,aAAa,IAAIH,mBAAmB,CAACG,aAAa,CAAC,CAAC,EAAEzB,aAAa,CAAC;MACrG0L,gBAAgB,EAAE;IACpB,CAAC,EAAE5J,OAAO,CAAC,CAAC;IACZ6J,SAAS,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC1BG,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;EAC9B,IAAIE,SAAS,EAAE;IACbvD,QAAQ,CAACuD,SAAS,GAAGA,SAAS;EAChC;EACA,IAAIC,WAAW,EAAE;IACfxD,QAAQ,CAACH,KAAK,GAAG2D,WAAW;EAC9B;EASA,IAAIC,GAAG,GAAG9D,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAGC,QAAQ;EACpC,IAAI6D,GAAG,IAAI,IAAI,EAAE;IACfzD,QAAQ,CAACL,EAAE,GAAG8D,GAAG;EACnB;EAEA,IAAI1D,MAAM,IAAI,IAAI,EAAE;IAClBC,QAAQ,CAAC,aAAa,CAAC,GAAGD,MAAM;EAClC;EACA,IAAIC,QAAQ,CAAC0D,IAAI,IAAI,IAAI,IAAIlK,WAAW,KAAK,QAAQ,EAAE;IACrDwG,QAAQ,CAAC0D,IAAI,GAAG,QAAQ;EAC1B;EACA,OAAO1D,QAAQ;AACjB,CAAC;AACD,eAAezG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}