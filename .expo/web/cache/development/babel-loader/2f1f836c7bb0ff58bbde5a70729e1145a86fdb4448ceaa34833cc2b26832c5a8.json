{"ast": null, "code": "export { default as BaseNavigationContainer } from \"./BaseNavigationContainer\";\nexport { default as createNavigationContainerRef } from \"./createNavigationContainerRef\";\nexport { default as createNavigatorFactory } from \"./createNavigatorFactory\";\nexport { default as CurrentRenderContext } from \"./CurrentRenderContext\";\nexport { default as findFocusedRoute } from \"./findFocusedRoute\";\nexport { default as getActionFromState } from \"./getActionFromState\";\nexport { default as getFocusedRouteNameFromRoute } from \"./getFocusedRouteNameFromRoute\";\nexport { default as getPathFromState } from \"./getPathFromState\";\nexport { default as getStateFromPath } from \"./getStateFromPath\";\nexport { default as NavigationContainerRefContext } from \"./NavigationContainerRefContext\";\nexport { default as NavigationContext } from \"./NavigationContext\";\nexport { default as NavigationHelpersContext } from \"./NavigationHelpersContext\";\nexport { default as NavigationRouteContext } from \"./NavigationRouteContext\";\nexport { default as PreventRemoveContext } from \"./PreventRemoveContext\";\nexport { default as PreventRemoveProvider } from \"./PreventRemoveProvider\";\nexport * from \"./types\";\nexport { default as useFocusEffect } from \"./useFocusEffect\";\nexport { default as useIsFocused } from \"./useIsFocused\";\nexport { default as useNavigation } from \"./useNavigation\";\nexport { default as useNavigationBuilder } from \"./useNavigationBuilder\";\nexport { default as useNavigationContainerRef } from \"./useNavigationContainerRef\";\nexport { default as useNavigationState } from \"./useNavigationState\";\nexport { default as UNSTABLE_usePreventRemove } from \"./usePreventRemove\";\nexport { default as usePreventRemoveContext } from \"./usePreventRemoveContext\";\nexport { default as useRoute } from \"./useRoute\";\nexport { default as validatePathConfig } from \"./validatePathConfig\";\nexport * from '@react-navigation/routers';", "map": {"version": 3, "names": ["default", "BaseNavigationContainer", "createNavigationContainerRef", "createNavigatorFactory", "CurrentRenderContext", "findFocusedRoute", "getActionFromState", "getFocusedRouteNameFromRoute", "getPathFromState", "getStateFromPath", "NavigationContainerRefContext", "NavigationContext", "NavigationHelpersContext", "NavigationRouteContext", "PreventRemoveContext", "PreventRemoveProvider", "useFocusEffect", "useIsFocused", "useNavigation", "useNavigationBuilder", "useNavigationContainerRef", "useNavigationState", "UNSTABLE_usePreventRemove", "usePreventRemoveContext", "useRoute", "validatePathConfig"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/index.tsx"], "sourcesContent": ["export { default as BaseNavigationContainer } from './BaseNavigationContainer';\nexport { default as createNavigationContainerRef } from './createNavigationContainerRef';\nexport { default as createNavigatorFactory } from './createNavigatorFactory';\nexport { default as CurrentRenderContext } from './CurrentRenderContext';\nexport { default as findFocusedRoute } from './findFocusedRoute';\nexport { default as getActionFromState } from './getActionFromState';\nexport { default as getFocusedRouteNameFromRoute } from './getFocusedRouteNameFromRoute';\nexport { default as getPathFromState } from './getPathFromState';\nexport { default as getStateFromPath } from './getStateFromPath';\nexport { default as NavigationContainerRefContext } from './NavigationContainerRefContext';\nexport { default as NavigationContext } from './NavigationContext';\nexport { default as NavigationHelpersContext } from './NavigationHelpersContext';\nexport { default as NavigationRouteContext } from './NavigationRouteContext';\nexport { default as PreventRemoveContext } from './PreventRemoveContext';\nexport { default as PreventRemoveProvider } from './PreventRemoveProvider';\nexport * from './types';\nexport { default as useFocusEffect } from './useFocusEffect';\nexport { default as useIsFocused } from './useIsFocused';\nexport { default as useNavigation } from './useNavigation';\nexport { default as useNavigationBuilder } from './useNavigationBuilder';\nexport { default as useNavigationContainerRef } from './useNavigationContainerRef';\nexport { default as useNavigationState } from './useNavigationState';\nexport { default as UNSTABLE_usePreventRemove } from './usePreventRemove';\nexport { default as usePreventRemoveContext } from './usePreventRemoveContext';\nexport { default as useRoute } from './useRoute';\nexport { default as validatePathConfig } from './validatePathConfig';\nexport * from '@react-navigation/routers';\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,uBAAuB;AAC3C,SAASD,OAAO,IAAIE,4BAA4B;AAChD,SAASF,OAAO,IAAIG,sBAAsB;AAC1C,SAASH,OAAO,IAAII,oBAAoB;AACxC,SAASJ,OAAO,IAAIK,gBAAgB;AACpC,SAASL,OAAO,IAAIM,kBAAkB;AACtC,SAASN,OAAO,IAAIO,4BAA4B;AAChD,SAASP,OAAO,IAAIQ,gBAAgB;AACpC,SAASR,OAAO,IAAIS,gBAAgB;AACpC,SAAST,OAAO,IAAIU,6BAA6B;AACjD,SAASV,OAAO,IAAIW,iBAAiB;AACrC,SAASX,OAAO,IAAIY,wBAAwB;AAC5C,SAASZ,OAAO,IAAIa,sBAAsB;AAC1C,SAASb,OAAO,IAAIc,oBAAoB;AACxC,SAASd,OAAO,IAAIe,qBAAqB;AACzC;AACA,SAASf,OAAO,IAAIgB,cAAc;AAClC,SAAShB,OAAO,IAAIiB,YAAY;AAChC,SAASjB,OAAO,IAAIkB,aAAa;AACjC,SAASlB,OAAO,IAAImB,oBAAoB;AACxC,SAASnB,OAAO,IAAIoB,yBAAyB;AAC7C,SAASpB,OAAO,IAAIqB,kBAAkB;AACtC,SAASrB,OAAO,IAAIsB,yBAAyB;AAC7C,SAAStB,OAAO,IAAIuB,uBAAuB;AAC3C,SAASvB,OAAO,IAAIwB,QAAQ;AAC5B,SAASxB,OAAO,IAAIyB,kBAAkB;AACtC,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}