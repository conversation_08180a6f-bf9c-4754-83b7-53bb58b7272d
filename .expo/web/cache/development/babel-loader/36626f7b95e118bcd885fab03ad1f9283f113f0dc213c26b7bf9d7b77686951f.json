{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/MaterialIcons.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/MaterialIcons.json\";\nexport default createIconSet(glyphMap, 'material', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/src/MaterialIcons.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/MaterialIcons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/MaterialIcons.json';\n\nexport default createIconSet(glyphMap, 'material', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,UAAU,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}