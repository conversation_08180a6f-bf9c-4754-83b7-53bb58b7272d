{"ast": null, "code": "export var KeepAwakeEventState;\n(function (KeepAwakeEventState) {\n  KeepAwakeEventState[\"RELEASE\"] = \"release\";\n})(KeepAwakeEventState || (KeepAwakeEventState = {}));", "map": {"version": 3, "names": ["KeepAwakeEventState"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-keep-awake/src/KeepAwake.types.ts"], "sourcesContent": ["// @needsAudit\nexport type KeepAwakeEvent = {\n  /** Keep awake state. */\n  state: KeepAwakeEventState;\n};\n\n// @needsAudit\nexport enum KeepAwakeEventState {\n  RELEASE = 'release',\n}\n\n// @needsAudit\nexport type KeepAwakeListener = (event: KeepAwakeEvent) => void;\n\nexport type KeepAwakeOptions = {\n  /**\n   * The call will throw an unhandled promise rejection on Android when the original Activity is dead or deactivated.\n   * Set the value to `true` for suppressing the uncaught exception.\n   */\n  suppressDeactivateWarnings?: boolean;\n\n  /**\n   * A callback that is invoked when the keep-awake state changes.\n   * @platform web\n   */\n  listener?: KeepAwakeListener;\n};\n"], "mappings": "AAOA,WAAYA,mBAEX;AAFD,WAAYA,mBAAmB;EAC7BA,mBAAA,uBAAmB;AACrB,CAAC,EAFWA,mBAAmB,KAAnBA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}