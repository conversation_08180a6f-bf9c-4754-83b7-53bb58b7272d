{"ast": null, "code": "export var transparent = 'rgba(255, 255, 255, 0)';\nexport var red50 = '#ffebee';\nexport var red100 = '#ffcdd2';\nexport var red200 = '#ef9a9a';\nexport var red300 = '#e57373';\nexport var red400 = '#ef5350';\nexport var red500 = '#f44336';\nexport var red600 = '#e53935';\nexport var red700 = '#d32f2f';\nexport var red800 = '#c62828';\nexport var red900 = '#b71c1c';\nexport var redA100 = '#ff8a80';\nexport var redA200 = '#ff5252';\nexport var redA400 = '#ff1744';\nexport var redA700 = '#d50000';\nexport var pink50 = '#fce4ec';\nexport var pink100 = '#f8bbd0';\nexport var pink200 = '#f48fb1';\nexport var pink300 = '#f06292';\nexport var pink400 = '#ec407a';\nexport var pink500 = '#e91e63';\nexport var pink600 = '#d81b60';\nexport var pink700 = '#c2185b';\nexport var pink800 = '#ad1457';\nexport var pink900 = '#880e4f';\nexport var pinkA100 = '#ff80ab';\nexport var pinkA200 = '#ff4081';\nexport var pinkA400 = '#f50057';\nexport var pinkA700 = '#c51162';\nexport var purple50 = '#f3e5f5';\nexport var purple100 = '#e1bee7';\nexport var purple200 = '#ce93d8';\nexport var purple300 = '#ba68c8';\nexport var purple400 = '#ab47bc';\nexport var purple500 = '#9c27b0';\nexport var purple600 = '#8e24aa';\nexport var purple700 = '#7b1fa2';\nexport var purple800 = '#6a1b9a';\nexport var purple900 = '#4a148c';\nexport var purpleA100 = '#ea80fc';\nexport var purpleA200 = '#e040fb';\nexport var purpleA400 = '#d500f9';\nexport var purpleA700 = '#aa00ff';\nexport var deepPurple50 = '#ede7f6';\nexport var deepPurple100 = '#d1c4e9';\nexport var deepPurple200 = '#b39ddb';\nexport var deepPurple300 = '#9575cd';\nexport var deepPurple400 = '#7e57c2';\nexport var deepPurple500 = '#673ab7';\nexport var deepPurple600 = '#5e35b1';\nexport var deepPurple700 = '#512da8';\nexport var deepPurple800 = '#4527a0';\nexport var deepPurple900 = '#311b92';\nexport var deepPurpleA100 = '#b388ff';\nexport var deepPurpleA200 = '#7c4dff';\nexport var deepPurpleA400 = '#651fff';\nexport var deepPurpleA700 = '#6200ea';\nexport var indigo50 = '#e8eaf6';\nexport var indigo100 = '#c5cae9';\nexport var indigo200 = '#9fa8da';\nexport var indigo300 = '#7986cb';\nexport var indigo400 = '#5c6bc0';\nexport var indigo500 = '#3f51b5';\nexport var indigo600 = '#3949ab';\nexport var indigo700 = '#303f9f';\nexport var indigo800 = '#283593';\nexport var indigo900 = '#1a237e';\nexport var indigoA100 = '#8c9eff';\nexport var indigoA200 = '#536dfe';\nexport var indigoA400 = '#3d5afe';\nexport var indigoA700 = '#304ffe';\nexport var blue50 = '#e3f2fd';\nexport var blue100 = '#bbdefb';\nexport var blue200 = '#90caf9';\nexport var blue300 = '#64b5f6';\nexport var blue400 = '#42a5f5';\nexport var blue500 = '#2196f3';\nexport var blue600 = '#1e88e5';\nexport var blue700 = '#1976d2';\nexport var blue800 = '#1565c0';\nexport var blue900 = '#0d47a1';\nexport var blueA100 = '#82b1ff';\nexport var blueA200 = '#448aff';\nexport var blueA400 = '#2979ff';\nexport var blueA700 = '#2962ff';\nexport var lightBlue50 = '#e1f5fe';\nexport var lightBlue100 = '#b3e5fc';\nexport var lightBlue200 = '#81d4fa';\nexport var lightBlue300 = '#4fc3f7';\nexport var lightBlue400 = '#29b6f6';\nexport var lightBlue500 = '#03a9f4';\nexport var lightBlue600 = '#039be5';\nexport var lightBlue700 = '#0288d1';\nexport var lightBlue800 = '#0277bd';\nexport var lightBlue900 = '#01579b';\nexport var lightBlueA100 = '#80d8ff';\nexport var lightBlueA200 = '#40c4ff';\nexport var lightBlueA400 = '#00b0ff';\nexport var lightBlueA700 = '#0091ea';\nexport var cyan50 = '#e0f7fa';\nexport var cyan100 = '#b2ebf2';\nexport var cyan200 = '#80deea';\nexport var cyan300 = '#4dd0e1';\nexport var cyan400 = '#26c6da';\nexport var cyan500 = '#00bcd4';\nexport var cyan600 = '#00acc1';\nexport var cyan700 = '#0097a7';\nexport var cyan800 = '#00838f';\nexport var cyan900 = '#006064';\nexport var cyanA100 = '#84ffff';\nexport var cyanA200 = '#18ffff';\nexport var cyanA400 = '#00e5ff';\nexport var cyanA700 = '#00b8d4';\nexport var teal50 = '#e0f2f1';\nexport var teal100 = '#b2dfdb';\nexport var teal200 = '#80cbc4';\nexport var teal300 = '#4db6ac';\nexport var teal400 = '#26a69a';\nexport var teal500 = '#009688';\nexport var teal600 = '#00897b';\nexport var teal700 = '#00796b';\nexport var teal800 = '#00695c';\nexport var teal900 = '#004d40';\nexport var tealA100 = '#a7ffeb';\nexport var tealA200 = '#64ffda';\nexport var tealA400 = '#1de9b6';\nexport var tealA700 = '#00bfa5';\nexport var green50 = '#e8f5e9';\nexport var green100 = '#c8e6c9';\nexport var green200 = '#a5d6a7';\nexport var green300 = '#81c784';\nexport var green400 = '#66bb6a';\nexport var green500 = '#4caf50';\nexport var green600 = '#43a047';\nexport var green700 = '#388e3c';\nexport var green800 = '#2e7d32';\nexport var green900 = '#1b5e20';\nexport var greenA100 = '#b9f6ca';\nexport var greenA200 = '#69f0ae';\nexport var greenA400 = '#00e676';\nexport var greenA700 = '#00c853';\nexport var lightGreen50 = '#f1f8e9';\nexport var lightGreen100 = '#dcedc8';\nexport var lightGreen200 = '#c5e1a5';\nexport var lightGreen300 = '#aed581';\nexport var lightGreen400 = '#9ccc65';\nexport var lightGreen500 = '#8bc34a';\nexport var lightGreen600 = '#7cb342';\nexport var lightGreen700 = '#689f38';\nexport var lightGreen800 = '#558b2f';\nexport var lightGreen900 = '#33691e';\nexport var lightGreenA100 = '#ccff90';\nexport var lightGreenA200 = '#b2ff59';\nexport var lightGreenA400 = '#76ff03';\nexport var lightGreenA700 = '#64dd17';\nexport var lime50 = '#f9fbe7';\nexport var lime100 = '#f0f4c3';\nexport var lime200 = '#e6ee9c';\nexport var lime300 = '#dce775';\nexport var lime400 = '#d4e157';\nexport var lime500 = '#cddc39';\nexport var lime600 = '#c0ca33';\nexport var lime700 = '#afb42b';\nexport var lime800 = '#9e9d24';\nexport var lime900 = '#827717';\nexport var limeA100 = '#f4ff81';\nexport var limeA200 = '#eeff41';\nexport var limeA400 = '#c6ff00';\nexport var limeA700 = '#aeea00';\nexport var yellow50 = '#fffde7';\nexport var yellow100 = '#fff9c4';\nexport var yellow200 = '#fff59d';\nexport var yellow300 = '#fff176';\nexport var yellow400 = '#ffee58';\nexport var yellow500 = '#ffeb3b';\nexport var yellow600 = '#fdd835';\nexport var yellow700 = '#fbc02d';\nexport var yellow800 = '#f9a825';\nexport var yellow900 = '#f57f17';\nexport var yellowA100 = '#ffff8d';\nexport var yellowA200 = '#ffff00';\nexport var yellowA400 = '#ffea00';\nexport var yellowA700 = '#ffd600';\nexport var amber50 = '#fff8e1';\nexport var amber100 = '#ffecb3';\nexport var amber200 = '#ffe082';\nexport var amber300 = '#ffd54f';\nexport var amber400 = '#ffca28';\nexport var amber500 = '#ffc107';\nexport var amber600 = '#ffb300';\nexport var amber700 = '#ffa000';\nexport var amber800 = '#ff8f00';\nexport var amber900 = '#ff6f00';\nexport var amberA100 = '#ffe57f';\nexport var amberA200 = '#ffd740';\nexport var amberA400 = '#ffc400';\nexport var amberA700 = '#ffab00';\nexport var orange50 = '#fff3e0';\nexport var orange100 = '#ffe0b2';\nexport var orange200 = '#ffcc80';\nexport var orange300 = '#ffb74d';\nexport var orange400 = '#ffa726';\nexport var orange500 = '#ff9800';\nexport var orange600 = '#fb8c00';\nexport var orange700 = '#f57c00';\nexport var orange800 = '#ef6c00';\nexport var orange900 = '#e65100';\nexport var orangeA100 = '#ffd180';\nexport var orangeA200 = '#ffab40';\nexport var orangeA400 = '#ff9100';\nexport var orangeA700 = '#ff6d00';\nexport var deepOrange50 = '#fbe9e7';\nexport var deepOrange100 = '#ffccbc';\nexport var deepOrange200 = '#ffab91';\nexport var deepOrange300 = '#ff8a65';\nexport var deepOrange400 = '#ff7043';\nexport var deepOrange500 = '#ff5722';\nexport var deepOrange600 = '#f4511e';\nexport var deepOrange700 = '#e64a19';\nexport var deepOrange800 = '#d84315';\nexport var deepOrange900 = '#bf360c';\nexport var deepOrangeA100 = '#ff9e80';\nexport var deepOrangeA200 = '#ff6e40';\nexport var deepOrangeA400 = '#ff3d00';\nexport var deepOrangeA700 = '#dd2c00';\nexport var brown50 = '#efebe9';\nexport var brown100 = '#d7ccc8';\nexport var brown200 = '#bcaaa4';\nexport var brown300 = '#a1887f';\nexport var brown400 = '#8d6e63';\nexport var brown500 = '#795548';\nexport var brown600 = '#6d4c41';\nexport var brown700 = '#5d4037';\nexport var brown800 = '#4e342e';\nexport var brown900 = '#3e2723';\nexport var blueGrey50 = '#eceff1';\nexport var blueGrey100 = '#cfd8dc';\nexport var blueGrey200 = '#b0bec5';\nexport var blueGrey300 = '#90a4ae';\nexport var blueGrey400 = '#78909c';\nexport var blueGrey500 = '#607d8b';\nexport var blueGrey600 = '#546e7a';\nexport var blueGrey700 = '#455a64';\nexport var blueGrey800 = '#37474f';\nexport var blueGrey900 = '#263238';\nexport var grey50 = '#fafafa';\nexport var grey100 = '#f5f5f5';\nexport var grey200 = '#eeeeee';\nexport var grey300 = '#e0e0e0';\nexport var grey400 = '#bdbdbd';\nexport var grey500 = '#9e9e9e';\nexport var grey600 = '#757575';\nexport var grey700 = '#616161';\nexport var grey800 = '#424242';\nexport var grey900 = '#212121';\nexport var black = '#000000';\nexport var white = '#ffffff';", "map": {"version": 3, "names": ["transparent", "red50", "red100", "red200", "red300", "red400", "red500", "red600", "red700", "red800", "red900", "redA100", "redA200", "redA400", "redA700", "pink50", "pink100", "pink200", "pink300", "pink400", "pink500", "pink600", "pink700", "pink800", "pink900", "pinkA100", "pinkA200", "pinkA400", "pinkA700", "purple50", "purple100", "purple200", "purple300", "purple400", "purple500", "purple600", "purple700", "purple800", "purple900", "purpleA100", "purpleA200", "purpleA400", "purpleA700", "deepPurple50", "deepPurple100", "deepPurple200", "deepPurple300", "deepPurple400", "deepPurple500", "deepPurple600", "deepPurple700", "deepPurple800", "deepPurple900", "deepPurpleA100", "deepPurpleA200", "deepPurpleA400", "deepPurpleA700", "indigo50", "indigo100", "indigo200", "indigo300", "indigo400", "indigo500", "indigo600", "indigo700", "indigo800", "indigo900", "indigoA100", "indigoA200", "indigoA400", "indigoA700", "blue50", "blue100", "blue200", "blue300", "blue400", "blue500", "blue600", "blue700", "blue800", "blue900", "blueA100", "blueA200", "blueA400", "blueA700", "lightBlue50", "lightBlue100", "lightBlue200", "lightBlue300", "lightBlue400", "lightBlue500", "lightBlue600", "lightBlue700", "lightBlue800", "lightBlue900", "lightBlueA100", "lightBlueA200", "lightBlueA400", "lightBlueA700", "cyan50", "cyan100", "cyan200", "cyan300", "cyan400", "cyan500", "cyan600", "cyan700", "cyan800", "cyan900", "cyanA100", "cyanA200", "cyanA400", "cyanA700", "teal50", "teal100", "teal200", "teal300", "teal400", "teal500", "teal600", "teal700", "teal800", "teal900", "tealA100", "tealA200", "tealA400", "tealA700", "green50", "green100", "green200", "green300", "green400", "green500", "green600", "green700", "green800", "green900", "greenA100", "greenA200", "greenA400", "greenA700", "lightGreen50", "lightGreen100", "lightGreen200", "lightGreen300", "lightGreen400", "lightGreen500", "lightGreen600", "lightGreen700", "lightGreen800", "lightGreen900", "lightGreenA100", "lightGreenA200", "lightGreenA400", "lightGreenA700", "lime50", "lime100", "lime200", "lime300", "lime400", "lime500", "lime600", "lime700", "lime800", "lime900", "limeA100", "limeA200", "limeA400", "limeA700", "yellow50", "yellow100", "yellow200", "yellow300", "yellow400", "yellow500", "yellow600", "yellow700", "yellow800", "yellow900", "yellowA100", "yellowA200", "yellowA400", "yellowA700", "amber50", "amber100", "amber200", "amber300", "amber400", "amber500", "amber600", "amber700", "amber800", "amber900", "amberA100", "amberA200", "amberA400", "amberA700", "orange50", "orange100", "orange200", "orange300", "orange400", "orange500", "orange600", "orange700", "orange800", "orange900", "orangeA100", "orangeA200", "orangeA400", "orangeA700", "deepOrange50", "deepOrange100", "deepOrange200", "deepOrange300", "deepOrange400", "deepOrange500", "deepOrange600", "deepOrange700", "deepOrange800", "deepOrange900", "deepOrangeA100", "deepOrangeA200", "deepOrangeA400", "deepOrangeA700", "brown50", "brown100", "brown200", "brown300", "brown400", "brown500", "brown600", "brown700", "brown800", "brown900", "blueGrey50", "blueGrey100", "blueGrey200", "blueGrey300", "blueGrey400", "blueGrey500", "blueGrey600", "blueGrey700", "blueGrey800", "blueGrey900", "grey50", "grey100", "grey200", "grey300", "grey400", "grey500", "grey600", "grey700", "grey800", "grey900", "black", "white"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/styles/themes/v2/colors.tsx"], "sourcesContent": ["export const transparent = 'rgba(255, 255, 255, 0)';\n\nexport const red50 = '#ffebee';\nexport const red100 = '#ffcdd2';\nexport const red200 = '#ef9a9a';\nexport const red300 = '#e57373';\nexport const red400 = '#ef5350';\nexport const red500 = '#f44336';\nexport const red600 = '#e53935';\nexport const red700 = '#d32f2f';\nexport const red800 = '#c62828';\nexport const red900 = '#b71c1c';\nexport const redA100 = '#ff8a80';\nexport const redA200 = '#ff5252';\nexport const redA400 = '#ff1744';\nexport const redA700 = '#d50000';\n\nexport const pink50 = '#fce4ec';\nexport const pink100 = '#f8bbd0';\nexport const pink200 = '#f48fb1';\nexport const pink300 = '#f06292';\nexport const pink400 = '#ec407a';\nexport const pink500 = '#e91e63';\nexport const pink600 = '#d81b60';\nexport const pink700 = '#c2185b';\nexport const pink800 = '#ad1457';\nexport const pink900 = '#880e4f';\nexport const pinkA100 = '#ff80ab';\nexport const pinkA200 = '#ff4081';\nexport const pinkA400 = '#f50057';\nexport const pinkA700 = '#c51162';\n\nexport const purple50 = '#f3e5f5';\nexport const purple100 = '#e1bee7';\nexport const purple200 = '#ce93d8';\nexport const purple300 = '#ba68c8';\nexport const purple400 = '#ab47bc';\nexport const purple500 = '#9c27b0';\nexport const purple600 = '#8e24aa';\nexport const purple700 = '#7b1fa2';\nexport const purple800 = '#6a1b9a';\nexport const purple900 = '#4a148c';\nexport const purpleA100 = '#ea80fc';\nexport const purpleA200 = '#e040fb';\nexport const purpleA400 = '#d500f9';\nexport const purpleA700 = '#aa00ff';\n\nexport const deepPurple50 = '#ede7f6';\nexport const deepPurple100 = '#d1c4e9';\nexport const deepPurple200 = '#b39ddb';\nexport const deepPurple300 = '#9575cd';\nexport const deepPurple400 = '#7e57c2';\nexport const deepPurple500 = '#673ab7';\nexport const deepPurple600 = '#5e35b1';\nexport const deepPurple700 = '#512da8';\nexport const deepPurple800 = '#4527a0';\nexport const deepPurple900 = '#311b92';\nexport const deepPurpleA100 = '#b388ff';\nexport const deepPurpleA200 = '#7c4dff';\nexport const deepPurpleA400 = '#651fff';\nexport const deepPurpleA700 = '#6200ea';\n\nexport const indigo50 = '#e8eaf6';\nexport const indigo100 = '#c5cae9';\nexport const indigo200 = '#9fa8da';\nexport const indigo300 = '#7986cb';\nexport const indigo400 = '#5c6bc0';\nexport const indigo500 = '#3f51b5';\nexport const indigo600 = '#3949ab';\nexport const indigo700 = '#303f9f';\nexport const indigo800 = '#283593';\nexport const indigo900 = '#1a237e';\nexport const indigoA100 = '#8c9eff';\nexport const indigoA200 = '#536dfe';\nexport const indigoA400 = '#3d5afe';\nexport const indigoA700 = '#304ffe';\n\nexport const blue50 = '#e3f2fd';\nexport const blue100 = '#bbdefb';\nexport const blue200 = '#90caf9';\nexport const blue300 = '#64b5f6';\nexport const blue400 = '#42a5f5';\nexport const blue500 = '#2196f3';\nexport const blue600 = '#1e88e5';\nexport const blue700 = '#1976d2';\nexport const blue800 = '#1565c0';\nexport const blue900 = '#0d47a1';\nexport const blueA100 = '#82b1ff';\nexport const blueA200 = '#448aff';\nexport const blueA400 = '#2979ff';\nexport const blueA700 = '#2962ff';\n\nexport const lightBlue50 = '#e1f5fe';\nexport const lightBlue100 = '#b3e5fc';\nexport const lightBlue200 = '#81d4fa';\nexport const lightBlue300 = '#4fc3f7';\nexport const lightBlue400 = '#29b6f6';\nexport const lightBlue500 = '#03a9f4';\nexport const lightBlue600 = '#039be5';\nexport const lightBlue700 = '#0288d1';\nexport const lightBlue800 = '#0277bd';\nexport const lightBlue900 = '#01579b';\nexport const lightBlueA100 = '#80d8ff';\nexport const lightBlueA200 = '#40c4ff';\nexport const lightBlueA400 = '#00b0ff';\nexport const lightBlueA700 = '#0091ea';\n\nexport const cyan50 = '#e0f7fa';\nexport const cyan100 = '#b2ebf2';\nexport const cyan200 = '#80deea';\nexport const cyan300 = '#4dd0e1';\nexport const cyan400 = '#26c6da';\nexport const cyan500 = '#00bcd4';\nexport const cyan600 = '#00acc1';\nexport const cyan700 = '#0097a7';\nexport const cyan800 = '#00838f';\nexport const cyan900 = '#006064';\nexport const cyanA100 = '#84ffff';\nexport const cyanA200 = '#18ffff';\nexport const cyanA400 = '#00e5ff';\nexport const cyanA700 = '#00b8d4';\n\nexport const teal50 = '#e0f2f1';\nexport const teal100 = '#b2dfdb';\nexport const teal200 = '#80cbc4';\nexport const teal300 = '#4db6ac';\nexport const teal400 = '#26a69a';\nexport const teal500 = '#009688';\nexport const teal600 = '#00897b';\nexport const teal700 = '#00796b';\nexport const teal800 = '#00695c';\nexport const teal900 = '#004d40';\nexport const tealA100 = '#a7ffeb';\nexport const tealA200 = '#64ffda';\nexport const tealA400 = '#1de9b6';\nexport const tealA700 = '#00bfa5';\n\nexport const green50 = '#e8f5e9';\nexport const green100 = '#c8e6c9';\nexport const green200 = '#a5d6a7';\nexport const green300 = '#81c784';\nexport const green400 = '#66bb6a';\nexport const green500 = '#4caf50';\nexport const green600 = '#43a047';\nexport const green700 = '#388e3c';\nexport const green800 = '#2e7d32';\nexport const green900 = '#1b5e20';\nexport const greenA100 = '#b9f6ca';\nexport const greenA200 = '#69f0ae';\nexport const greenA400 = '#00e676';\nexport const greenA700 = '#00c853';\n\nexport const lightGreen50 = '#f1f8e9';\nexport const lightGreen100 = '#dcedc8';\nexport const lightGreen200 = '#c5e1a5';\nexport const lightGreen300 = '#aed581';\nexport const lightGreen400 = '#9ccc65';\nexport const lightGreen500 = '#8bc34a';\nexport const lightGreen600 = '#7cb342';\nexport const lightGreen700 = '#689f38';\nexport const lightGreen800 = '#558b2f';\nexport const lightGreen900 = '#33691e';\nexport const lightGreenA100 = '#ccff90';\nexport const lightGreenA200 = '#b2ff59';\nexport const lightGreenA400 = '#76ff03';\nexport const lightGreenA700 = '#64dd17';\n\nexport const lime50 = '#f9fbe7';\nexport const lime100 = '#f0f4c3';\nexport const lime200 = '#e6ee9c';\nexport const lime300 = '#dce775';\nexport const lime400 = '#d4e157';\nexport const lime500 = '#cddc39';\nexport const lime600 = '#c0ca33';\nexport const lime700 = '#afb42b';\nexport const lime800 = '#9e9d24';\nexport const lime900 = '#827717';\nexport const limeA100 = '#f4ff81';\nexport const limeA200 = '#eeff41';\nexport const limeA400 = '#c6ff00';\nexport const limeA700 = '#aeea00';\n\nexport const yellow50 = '#fffde7';\nexport const yellow100 = '#fff9c4';\nexport const yellow200 = '#fff59d';\nexport const yellow300 = '#fff176';\nexport const yellow400 = '#ffee58';\nexport const yellow500 = '#ffeb3b';\nexport const yellow600 = '#fdd835';\nexport const yellow700 = '#fbc02d';\nexport const yellow800 = '#f9a825';\nexport const yellow900 = '#f57f17';\nexport const yellowA100 = '#ffff8d';\nexport const yellowA200 = '#ffff00';\nexport const yellowA400 = '#ffea00';\nexport const yellowA700 = '#ffd600';\n\nexport const amber50 = '#fff8e1';\nexport const amber100 = '#ffecb3';\nexport const amber200 = '#ffe082';\nexport const amber300 = '#ffd54f';\nexport const amber400 = '#ffca28';\nexport const amber500 = '#ffc107';\nexport const amber600 = '#ffb300';\nexport const amber700 = '#ffa000';\nexport const amber800 = '#ff8f00';\nexport const amber900 = '#ff6f00';\nexport const amberA100 = '#ffe57f';\nexport const amberA200 = '#ffd740';\nexport const amberA400 = '#ffc400';\nexport const amberA700 = '#ffab00';\n\nexport const orange50 = '#fff3e0';\nexport const orange100 = '#ffe0b2';\nexport const orange200 = '#ffcc80';\nexport const orange300 = '#ffb74d';\nexport const orange400 = '#ffa726';\nexport const orange500 = '#ff9800';\nexport const orange600 = '#fb8c00';\nexport const orange700 = '#f57c00';\nexport const orange800 = '#ef6c00';\nexport const orange900 = '#e65100';\nexport const orangeA100 = '#ffd180';\nexport const orangeA200 = '#ffab40';\nexport const orangeA400 = '#ff9100';\nexport const orangeA700 = '#ff6d00';\n\nexport const deepOrange50 = '#fbe9e7';\nexport const deepOrange100 = '#ffccbc';\nexport const deepOrange200 = '#ffab91';\nexport const deepOrange300 = '#ff8a65';\nexport const deepOrange400 = '#ff7043';\nexport const deepOrange500 = '#ff5722';\nexport const deepOrange600 = '#f4511e';\nexport const deepOrange700 = '#e64a19';\nexport const deepOrange800 = '#d84315';\nexport const deepOrange900 = '#bf360c';\nexport const deepOrangeA100 = '#ff9e80';\nexport const deepOrangeA200 = '#ff6e40';\nexport const deepOrangeA400 = '#ff3d00';\nexport const deepOrangeA700 = '#dd2c00';\n\nexport const brown50 = '#efebe9';\nexport const brown100 = '#d7ccc8';\nexport const brown200 = '#bcaaa4';\nexport const brown300 = '#a1887f';\nexport const brown400 = '#8d6e63';\nexport const brown500 = '#795548';\nexport const brown600 = '#6d4c41';\nexport const brown700 = '#5d4037';\nexport const brown800 = '#4e342e';\nexport const brown900 = '#3e2723';\n\nexport const blueGrey50 = '#eceff1';\nexport const blueGrey100 = '#cfd8dc';\nexport const blueGrey200 = '#b0bec5';\nexport const blueGrey300 = '#90a4ae';\nexport const blueGrey400 = '#78909c';\nexport const blueGrey500 = '#607d8b';\nexport const blueGrey600 = '#546e7a';\nexport const blueGrey700 = '#455a64';\nexport const blueGrey800 = '#37474f';\nexport const blueGrey900 = '#263238';\n\nexport const grey50 = '#fafafa';\nexport const grey100 = '#f5f5f5';\nexport const grey200 = '#eeeeee';\nexport const grey300 = '#e0e0e0';\nexport const grey400 = '#bdbdbd';\nexport const grey500 = '#9e9e9e';\nexport const grey600 = '#757575';\nexport const grey700 = '#616161';\nexport const grey800 = '#424242';\nexport const grey900 = '#212121';\n\nexport const black = '#000000';\nexport const white = '#ffffff';\n"], "mappings": "AAAA,OAAO,IAAMA,WAAW,GAAG,wBAAwB;AAEnD,OAAO,IAAMC,KAAK,GAAG,SAAS;AAC9B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAEhC,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AAEjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AAEnC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AAEvC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AAEnC,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AAEjC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AAEtC,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AAEjC,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AAEjC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAElC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AAEvC,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AAEjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AAEnC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAElC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,SAAS,GAAG,SAAS;AAClC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,UAAU,GAAG,SAAS;AAEnC,OAAO,IAAMC,YAAY,GAAG,SAAS;AACrC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AACvC,OAAO,IAAMC,cAAc,GAAG,SAAS;AAEvC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,IAAMC,QAAQ,GAAG,SAAS;AAEjC,OAAO,IAAMC,UAAU,GAAG,SAAS;AACnC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AACpC,OAAO,IAAMC,WAAW,GAAG,SAAS;AAEpC,OAAO,IAAMC,MAAM,GAAG,SAAS;AAC/B,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAChC,OAAO,IAAMC,OAAO,GAAG,SAAS;AAEhC,OAAO,IAAMC,KAAK,GAAG,SAAS;AAC9B,OAAO,IAAMC,KAAK,GAAG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}