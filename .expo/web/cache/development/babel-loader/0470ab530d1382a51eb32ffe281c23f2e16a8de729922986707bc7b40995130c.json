{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"icon\", \"onPress\", \"forceTextInputFocus\", \"color\", \"theme\", \"rippleColor\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { getIconColor } from \"./utils\";\nimport { useInternalTheme } from \"../../../core/theming\";\nimport IconButton from \"../../IconButton/IconButton\";\nimport { ICON_SIZE } from \"../constants\";\nimport { getConstants } from \"../helpers\";\nvar StyleContext = React.createContext({\n  style: {},\n  isTextInputFocused: false,\n  forceFocus: function forceFocus() {},\n  testID: ''\n});\nvar IconAdornment = function IconAdornment(_ref) {\n  var icon = _ref.icon,\n    topPosition = _ref.topPosition,\n    side = _ref.side,\n    isTextInputFocused = _ref.isTextInputFocused,\n    forceFocus = _ref.forceFocus,\n    testID = _ref.testID,\n    themeOverrides = _ref.theme,\n    disabled = _ref.disabled;\n  var _useInternalTheme = useInternalTheme(themeOverrides),\n    isV3 = _useInternalTheme.isV3;\n  var _getConstants = getConstants(isV3),\n    ICON_OFFSET = _getConstants.ICON_OFFSET;\n  var style = _defineProperty({\n    top: topPosition\n  }, side, ICON_OFFSET);\n  var contextState = {\n    style: style,\n    isTextInputFocused: isTextInputFocused,\n    forceFocus: forceFocus,\n    testID: testID,\n    disabled: disabled\n  };\n  return React.createElement(StyleContext.Provider, {\n    value: contextState\n  }, icon);\n};\nvar TextInputIcon = function TextInputIcon(_ref2) {\n  var icon = _ref2.icon,\n    onPress = _ref2.onPress,\n    _ref2$forceTextInputF = _ref2.forceTextInputFocus,\n    forceTextInputFocus = _ref2$forceTextInputF === void 0 ? true : _ref2$forceTextInputF,\n    customColor = _ref2.color,\n    themeOverrides = _ref2.theme,\n    rippleColor = _ref2.rippleColor,\n    rest = _objectWithoutProperties(_ref2, _excluded);\n  var _React$useContext = React.useContext(StyleContext),\n    style = _React$useContext.style,\n    isTextInputFocused = _React$useContext.isTextInputFocused,\n    forceFocus = _React$useContext.forceFocus,\n    testID = _React$useContext.testID,\n    disabled = _React$useContext.disabled;\n  var onPressWithFocusControl = React.useCallback(function (e) {\n    if (forceTextInputFocus && !isTextInputFocused) {\n      forceFocus();\n    }\n    onPress === null || onPress === void 0 ? void 0 : onPress(e);\n  }, [forceTextInputFocus, forceFocus, isTextInputFocused, onPress]);\n  var theme = useInternalTheme(themeOverrides);\n  var iconColor = getIconColor({\n    theme: theme,\n    disabled: disabled,\n    isTextInputFocused: isTextInputFocused,\n    customColor: customColor\n  });\n  return React.createElement(View, {\n    style: [styles.container, style]\n  }, React.createElement(IconButton, _extends({\n    icon: icon,\n    style: styles.iconButton,\n    size: ICON_SIZE,\n    onPress: onPressWithFocusControl,\n    iconColor: iconColor,\n    testID: testID,\n    theme: themeOverrides,\n    rippleColor: rippleColor\n  }, rest)));\n};\nTextInputIcon.displayName = 'TextInput.Icon';\nvar styles = StyleSheet.create({\n  container: {\n    position: 'absolute',\n    width: ICON_SIZE,\n    height: ICON_SIZE,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  iconButton: {\n    margin: 0\n  }\n});\nexport default TextInputIcon;\nexport { IconAdornment };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "getIconColor", "useInternalTheme", "IconButton", "ICON_SIZE", "getConstants", "StyleContext", "createContext", "style", "isTextInputFocused", "forceFocus", "testID", "IconAdornment", "_ref", "icon", "topPosition", "side", "themeOverrides", "theme", "disabled", "_useInternalTheme", "isV3", "_getConstants", "ICON_OFFSET", "_defineProperty", "top", "contextState", "createElement", "Provider", "value", "TextInputIcon", "_ref2", "onPress", "_ref2$forceTextInputF", "forceTextInputFocus", "customColor", "color", "rippleColor", "rest", "_objectWithoutProperties", "_excluded", "_React$useContext", "useContext", "onPressWithFocusControl", "useCallback", "e", "iconColor", "styles", "container", "_extends", "iconButton", "size", "displayName", "create", "position", "width", "height", "justifyContent", "alignItems", "margin"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/TextInput/Adornment/TextInputIcon.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  ColorValue,\n  GestureResponderEvent,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { getIconColor } from './utils';\nimport { useInternalTheme } from '../../../core/theming';\nimport type { $Omit, ThemeProp } from '../../../types';\nimport type { IconSource } from '../../Icon';\nimport IconButton from '../../IconButton/IconButton';\nimport { ICON_SIZE } from '../constants';\nimport { getConstants } from '../helpers';\n\nexport type Props = $Omit<\n  React.ComponentProps<typeof IconButton>,\n  'icon' | 'theme' | 'color' | 'iconColor'\n> & {\n  /**\n   * @renamed Renamed from 'name' to 'icon` in v5.x\n   * Icon to show.\n   */\n  icon: IconSource;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Whether the TextInput will focus after onPress.\n   */\n  forceTextInputFocus?: boolean;\n  /**\n   * Color of the icon or a function receiving a boolean indicating whether the TextInput is focused and returning the color.\n   */\n  color?: ((isTextInputFocused: boolean) => string | undefined) | string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\ntype StyleContextType = {\n  style: StyleProp<ViewStyle>;\n  isTextInputFocused: boolean;\n  forceFocus: () => void;\n  testID: string;\n  disabled?: boolean;\n};\n\nconst StyleContext = React.createContext<StyleContextType>({\n  style: {},\n  isTextInputFocused: false,\n  forceFocus: () => {},\n  testID: '',\n});\n\nconst IconAdornment: React.FunctionComponent<\n  {\n    testID: string;\n    icon: React.ReactNode;\n    topPosition: number;\n    side: 'left' | 'right';\n    theme?: ThemeProp;\n    disabled?: boolean;\n  } & Omit<StyleContextType, 'style'>\n> = ({\n  icon,\n  topPosition,\n  side,\n  isTextInputFocused,\n  forceFocus,\n  testID,\n  theme: themeOverrides,\n  disabled,\n}) => {\n  const { isV3 } = useInternalTheme(themeOverrides);\n  const { ICON_OFFSET } = getConstants(isV3);\n\n  const style = {\n    top: topPosition,\n    [side]: ICON_OFFSET,\n  };\n  const contextState = {\n    style,\n    isTextInputFocused,\n    forceFocus,\n    testID,\n    disabled,\n  };\n\n  return (\n    <StyleContext.Provider value={contextState}>{icon}</StyleContext.Provider>\n  );\n};\n\n/**\n * A component to render a leading / trailing icon in the TextInput\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { TextInput } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [text, setText] = React.useState('');\n *\n *   return (\n *     <TextInput\n *       label=\"Password\"\n *       secureTextEntry\n *       right={<TextInput.Icon icon=\"eye\" />}\n *     />\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\n\nconst TextInputIcon = ({\n  icon,\n  onPress,\n  forceTextInputFocus = true,\n  color: customColor,\n  theme: themeOverrides,\n  rippleColor,\n  ...rest\n}: Props) => {\n  const { style, isTextInputFocused, forceFocus, testID, disabled } =\n    React.useContext(StyleContext);\n\n  const onPressWithFocusControl = React.useCallback(\n    (e: GestureResponderEvent) => {\n      if (forceTextInputFocus && !isTextInputFocused) {\n        forceFocus();\n      }\n\n      onPress?.(e);\n    },\n    [forceTextInputFocus, forceFocus, isTextInputFocused, onPress]\n  );\n\n  const theme = useInternalTheme(themeOverrides);\n\n  const iconColor = getIconColor({\n    theme,\n    disabled,\n    isTextInputFocused,\n    customColor,\n  });\n\n  return (\n    <View style={[styles.container, style]}>\n      <IconButton\n        icon={icon}\n        style={styles.iconButton}\n        size={ICON_SIZE}\n        onPress={onPressWithFocusControl}\n        iconColor={iconColor}\n        testID={testID}\n        theme={themeOverrides}\n        rippleColor={rippleColor}\n        {...rest}\n      />\n    </View>\n  );\n};\nTextInputIcon.displayName = 'TextInput.Icon';\n\nconst styles = StyleSheet.create({\n  container: {\n    position: 'absolute',\n    width: ICON_SIZE,\n    height: ICON_SIZE,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  iconButton: {\n    margin: 0,\n  },\n});\n\nexport default TextInputIcon;\n\n// @component-docs ignore-next-line\nexport { IconAdornment };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAUzB,SAASC,YAAY;AACrB,SAASC,gBAAgB;AAGzB,OAAOC,UAAU;AACjB,SAASC,SAAS;AAClB,SAASC,YAAY;AA0CrB,IAAMC,YAAY,GAAGR,KAAK,CAACS,aAAa,CAAmB;EACzDC,KAAK,EAAE,CAAC,CAAC;EACTC,kBAAkB,EAAE,KAAK;EACzBC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ,CAAC,CAAC;EACpBC,MAAM,EAAE;AACV,CAAC,CAAC;AAEF,IAAMC,aASL,GAAG,SATEA,aASLA,CAAGC,IAAA,EASE;EAAA,IARJC,IAAI,GAQLD,IAAA,CARCC,IAAI;IACJC,WAAW,GAOZF,IAAA,CAPCE,WAAW;IACXC,IAAI,GAMLH,IAAA,CANCG,IAAI;IACJP,kBAAkB,GAKnBI,IAAA,CALCJ,kBAAkB;IAClBC,UAAU,GAIXG,IAAA,CAJCH,UAAU;IACVC,MAAM,GAGPE,IAAA,CAHCF,MAAM;IACCM,cAAc,GAEtBJ,IAAA,CAFCK,KAAK;IACLC,QAAA,GACDN,IAAA,CADCM,QAAA;EAEA,IAAAC,iBAAA,GAAiBlB,gBAAgB,CAACe,cAAc,CAAC;IAAzCI,IAAA,GAAAD,iBAAA,CAAAC,IAAA;EACR,IAAAC,aAAA,GAAwBjB,YAAY,CAACgB,IAAI,CAAC;IAAlCE,WAAA,GAAAD,aAAA,CAAAC,WAAA;EAER,IAAMf,KAAK,GAAAgB,eAAA;IACTC,GAAG,EAAEV;EAAW,GACfC,IAAI,EAAGO,WAAA,CACT;EACD,IAAMG,YAAY,GAAG;IACnBlB,KAAK,EAALA,KAAK;IACLC,kBAAkB,EAAlBA,kBAAkB;IAClBC,UAAU,EAAVA,UAAU;IACVC,MAAM,EAANA,MAAM;IACNQ,QAAA,EAAAA;EACF,CAAC;EAED,OACErB,KAAA,CAAA6B,aAAA,CAACrB,YAAY,CAACsB,QAAQ;IAACC,KAAK,EAAEH;EAAa,GAAEZ,IAA4B,CAAC;AAE9E,CAAC;AA0BD,IAAMgB,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,KAAA,EAQT;EAAA,IAPXjB,IAAI,GAOEiB,KAAA,CAPNjB,IAAI;IACJkB,OAAO,GAMDD,KAAA,CANNC,OAAO;IAAAC,qBAAA,GAMDF,KAAA,CALNG,mBAAmB;IAAnBA,mBAAmB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IACnBE,WAAW,GAIZJ,KAAA,CAJNK,KAAK;IACEnB,cAAc,GAGfc,KAAA,CAHNb,KAAK;IACLmB,WAAW,GAELN,KAAA,CAFNM,WAAW;IACRC,IAAA,GAAAC,wBAAA,CACGR,KAAA,EAAAS,SAAA;EACN,IAAAC,iBAAA,GACE3C,KAAK,CAAC4C,UAAU,CAACpC,YAAY,CAAC;IADxBE,KAAK,GAAAiC,iBAAA,CAALjC,KAAK;IAAEC,kBAAkB,GAAAgC,iBAAA,CAAlBhC,kBAAkB;IAAEC,UAAU,GAAA+B,iBAAA,CAAV/B,UAAU;IAAEC,MAAM,GAAA8B,iBAAA,CAAN9B,MAAM;IAAEQ,QAAA,GAAAsB,iBAAA,CAAAtB,QAAA;EAGvD,IAAMwB,uBAAuB,GAAG7C,KAAK,CAAC8C,WAAW,CAC9C,UAAAC,CAAwB,EAAK;IAC5B,IAAIX,mBAAmB,IAAI,CAACzB,kBAAkB,EAAE;MAC9CC,UAAU,CAAC,CAAC;IACd;IAEAsB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGa,CAAC,CAAC;EACd,CAAC,EACD,CAACX,mBAAmB,EAAExB,UAAU,EAAED,kBAAkB,EAAEuB,OAAO,CAC/D,CAAC;EAED,IAAMd,KAAK,GAAGhB,gBAAgB,CAACe,cAAc,CAAC;EAE9C,IAAM6B,SAAS,GAAG7C,YAAY,CAAC;IAC7BiB,KAAK,EAALA,KAAK;IACLC,QAAQ,EAARA,QAAQ;IACRV,kBAAkB,EAAlBA,kBAAkB;IAClB0B,WAAA,EAAAA;EACF,CAAC,CAAC;EAEF,OACErC,KAAA,CAAA6B,aAAA,CAAC3B,IAAI;IAACQ,KAAK,EAAE,CAACuC,MAAM,CAACC,SAAS,EAAExC,KAAK;EAAE,GACrCV,KAAA,CAAA6B,aAAA,CAACxB,UAAU,EAAA8C,QAAA;IACTnC,IAAI,EAAEA,IAAK;IACXN,KAAK,EAAEuC,MAAM,CAACG,UAAW;IACzBC,IAAI,EAAE/C,SAAU;IAChB4B,OAAO,EAAEW,uBAAwB;IACjCG,SAAS,EAAEA,SAAU;IACrBnC,MAAM,EAAEA,MAAO;IACfO,KAAK,EAAED,cAAe;IACtBoB,WAAW,EAAEA;EAAY,GACrBC,IAAI,CACT,CACG,CAAC;AAEX,CAAC;AACDR,aAAa,CAACsB,WAAW,GAAG,gBAAgB;AAE5C,IAAML,MAAM,GAAGhD,UAAU,CAACsD,MAAM,CAAC;EAC/BL,SAAS,EAAE;IACTM,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAEnD,SAAS;IAChBoD,MAAM,EAAEpD,SAAS;IACjBqD,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDR,UAAU,EAAE;IACVS,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAe7B,aAAa;AAG5B,SAASlB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}