{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"component\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport useLatestCallback from 'use-latest-callback';\nimport BottomNavigationBar from \"./BottomNavigationBar\";\nimport BottomNavigationRouteScreen from \"./BottomNavigationRouteScreen\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport useAnimatedValueArray from \"../../utils/useAnimatedValueArray\";\nvar FAR_FAR_AWAY = Platform.OS === 'web' ? 0 : 9999;\nvar SceneComponent = React.memo(function (_ref) {\n  var component = _ref.component,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return React.createElement(component, rest);\n});\nvar BottomNavigation = function BottomNavigation(_ref2) {\n  var navigationState = _ref2.navigationState,\n    renderScene = _ref2.renderScene,\n    renderIcon = _ref2.renderIcon,\n    renderLabel = _ref2.renderLabel,\n    renderTouchable = _ref2.renderTouchable,\n    getLabelText = _ref2.getLabelText,\n    getBadge = _ref2.getBadge,\n    getColor = _ref2.getColor,\n    getAccessibilityLabel = _ref2.getAccessibilityLabel,\n    getTestID = _ref2.getTestID,\n    activeColor = _ref2.activeColor,\n    inactiveColor = _ref2.inactiveColor,\n    _ref2$keyboardHidesNa = _ref2.keyboardHidesNavigationBar,\n    keyboardHidesNavigationBar = _ref2$keyboardHidesNa === void 0 ? Platform.OS === 'android' : _ref2$keyboardHidesNa,\n    barStyle = _ref2.barStyle,\n    _ref2$labeled = _ref2.labeled,\n    labeled = _ref2$labeled === void 0 ? true : _ref2$labeled,\n    style = _ref2.style,\n    activeIndicatorStyle = _ref2.activeIndicatorStyle,\n    _ref2$sceneAnimationE = _ref2.sceneAnimationEnabled,\n    sceneAnimationEnabled = _ref2$sceneAnimationE === void 0 ? false : _ref2$sceneAnimationE,\n    _ref2$sceneAnimationT = _ref2.sceneAnimationType,\n    sceneAnimationType = _ref2$sceneAnimationT === void 0 ? 'opacity' : _ref2$sceneAnimationT,\n    sceneAnimationEasing = _ref2.sceneAnimationEasing,\n    onTabPress = _ref2.onTabPress,\n    onTabLongPress = _ref2.onTabLongPress,\n    onIndexChange = _ref2.onIndexChange,\n    shiftingProp = _ref2.shifting,\n    safeAreaInsets = _ref2.safeAreaInsets,\n    _ref2$labelMaxFontSiz = _ref2.labelMaxFontSizeMultiplier,\n    labelMaxFontSizeMultiplier = _ref2$labelMaxFontSiz === void 0 ? 1 : _ref2$labelMaxFontSiz,\n    compactProp = _ref2.compact,\n    _ref2$testID = _ref2.testID,\n    testID = _ref2$testID === void 0 ? 'bottom-navigation' : _ref2$testID,\n    themeOverrides = _ref2.theme,\n    _ref2$getLazy = _ref2.getLazy,\n    getLazy = _ref2$getLazy === void 0 ? function (_ref3) {\n      var route = _ref3.route;\n      return route.lazy;\n    } : _ref2$getLazy;\n  var theme = useInternalTheme(themeOverrides);\n  var scale = theme.animation.scale;\n  var compact = compactProp != null ? compactProp : !theme.isV3;\n  var shifting = shiftingProp != null ? shiftingProp : theme.isV3 ? false : navigationState.routes.length > 3;\n  if (shifting && navigationState.routes.length < 2) {\n    shifting = false;\n    console.warn('BottomNavigation needs at least 2 tabs to run shifting animation');\n  }\n  var focusedKey = navigationState.routes[navigationState.index].key;\n  var tabsPositionAnims = useAnimatedValueArray(navigationState.routes.map(function (_, i) {\n    return i === navigationState.index ? 0 : i >= navigationState.index ? 1 : -1;\n  }));\n  var offsetsAnims = useAnimatedValueArray(navigationState.routes.map(function (_, i) {\n    return i === navigationState.index ? 0 : 1;\n  }));\n  var _React$useState = React.useState([focusedKey]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    loaded = _React$useState2[0],\n    setLoaded = _React$useState2[1];\n  if (!loaded.includes(focusedKey)) {\n    setLoaded(function (loaded) {\n      return [].concat(_toConsumableArray(loaded), [focusedKey]);\n    });\n  }\n  var animateToIndex = React.useCallback(function (index) {\n    Animated.parallel(_toConsumableArray(navigationState.routes.map(function (_, i) {\n      return Animated.timing(tabsPositionAnims[i], {\n        toValue: i === index ? 0 : i >= index ? 1 : -1,\n        duration: theme.isV3 || shifting ? 150 * scale : 0,\n        useNativeDriver: true,\n        easing: sceneAnimationEasing\n      });\n    }))).start(function (_ref4) {\n      var finished = _ref4.finished;\n      if (finished) {\n        offsetsAnims.forEach(function (offset, i) {\n          if (i === index) {\n            offset.setValue(0);\n          } else {\n            offset.setValue(1);\n          }\n        });\n      }\n    });\n  }, [shifting, navigationState.routes, offsetsAnims, scale, tabsPositionAnims, sceneAnimationEasing, theme]);\n  React.useEffect(function () {\n    animateToIndex(navigationState.index);\n  }, []);\n  var prevNavigationState = React.useRef();\n  React.useEffect(function () {\n    offsetsAnims.forEach(function (offset, i) {\n      var _prevNavigationState$;\n      if (i === navigationState.index || i === ((_prevNavigationState$ = prevNavigationState.current) === null || _prevNavigationState$ === void 0 ? void 0 : _prevNavigationState$.index)) {\n        offset.setValue(0);\n      }\n    });\n    animateToIndex(navigationState.index);\n  }, [navigationState.index, animateToIndex, offsetsAnims]);\n  var handleTabPress = useLatestCallback(function (event) {\n    onTabPress === null || onTabPress === void 0 ? void 0 : onTabPress(event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    var index = navigationState.routes.findIndex(function (route) {\n      return event.route.key === route.key;\n    });\n    if (index !== navigationState.index) {\n      prevNavigationState.current = navigationState;\n      onIndexChange(index);\n    }\n  });\n  var jumpTo = useLatestCallback(function (key) {\n    var index = navigationState.routes.findIndex(function (route) {\n      return route.key === key;\n    });\n    prevNavigationState.current = navigationState;\n    onIndexChange(index);\n  });\n  var routes = navigationState.routes;\n  var colors = theme.colors;\n  return React.createElement(View, {\n    style: [styles.container, style],\n    testID: testID\n  }, React.createElement(View, {\n    style: [styles.content, {\n      backgroundColor: colors === null || colors === void 0 ? void 0 : colors.background\n    }]\n  }, routes.map(function (route, index) {\n    var _prevNavigationState$2;\n    if (getLazy({\n      route: route\n    }) !== false && !loaded.includes(route.key)) {\n      return null;\n    }\n    var focused = navigationState.index === index;\n    var previouslyFocused = ((_prevNavigationState$2 = prevNavigationState.current) === null || _prevNavigationState$2 === void 0 ? void 0 : _prevNavigationState$2.index) === index;\n    var countAlphaOffscreen = sceneAnimationEnabled && (focused || previouslyFocused);\n    var renderToHardwareTextureAndroid = sceneAnimationEnabled && focused;\n    var opacity = sceneAnimationEnabled ? tabsPositionAnims[index].interpolate({\n      inputRange: [-1, 0, 1],\n      outputRange: [0, 1, 0]\n    }) : focused ? 1 : 0;\n    var offsetTarget = focused ? 0 : FAR_FAR_AWAY;\n    var top = sceneAnimationEnabled ? offsetsAnims[index].interpolate({\n      inputRange: [0, 1],\n      outputRange: [0, offsetTarget]\n    }) : offsetTarget;\n    var left = sceneAnimationType === 'shifting' ? tabsPositionAnims[index].interpolate({\n      inputRange: [-1, 0, 1],\n      outputRange: [-50, 0, 50]\n    }) : 0;\n    var zIndex = focused ? 1 : 0;\n    return React.createElement(BottomNavigationRouteScreen, {\n      key: route.key,\n      pointerEvents: focused ? 'auto' : 'none',\n      accessibilityElementsHidden: !focused,\n      importantForAccessibility: focused ? 'auto' : 'no-hide-descendants',\n      index: index,\n      visibility: opacity,\n      style: [StyleSheet.absoluteFill, {\n        zIndex: zIndex\n      }],\n      collapsable: false,\n      removeClippedSubviews: Platform.OS === 'ios' ? navigationState.index !== index : true\n    }, React.createElement(Animated.View, _extends({}, Platform.OS === 'android' && {\n      needsOffscreenAlphaCompositing: countAlphaOffscreen\n    }, {\n      renderToHardwareTextureAndroid: renderToHardwareTextureAndroid,\n      style: [styles.content, {\n        opacity: opacity,\n        transform: [{\n          translateX: left\n        }, {\n          translateY: top\n        }]\n      }]\n    }), renderScene({\n      route: route,\n      jumpTo: jumpTo\n    })));\n  })), React.createElement(BottomNavigationBar, {\n    navigationState: navigationState,\n    renderIcon: renderIcon,\n    renderLabel: renderLabel,\n    renderTouchable: renderTouchable,\n    getLabelText: getLabelText,\n    getBadge: getBadge,\n    getColor: getColor,\n    getAccessibilityLabel: getAccessibilityLabel,\n    getTestID: getTestID,\n    activeColor: activeColor,\n    inactiveColor: inactiveColor,\n    keyboardHidesNavigationBar: keyboardHidesNavigationBar,\n    style: barStyle,\n    activeIndicatorStyle: activeIndicatorStyle,\n    labeled: labeled,\n    animationEasing: sceneAnimationEasing,\n    onTabPress: handleTabPress,\n    onTabLongPress: onTabLongPress,\n    shifting: shifting,\n    safeAreaInsets: safeAreaInsets,\n    labelMaxFontSizeMultiplier: labelMaxFontSizeMultiplier,\n    compact: compact,\n    testID: `${testID}-bar`,\n    theme: theme\n  }));\n};\nBottomNavigation.SceneMap = function (scenes) {\n  return function (_ref5) {\n    var route = _ref5.route,\n      jumpTo = _ref5.jumpTo;\n    return React.createElement(SceneComponent, {\n      key: route.key,\n      component: scenes[route.key ? route.key : ''],\n      route: route,\n      jumpTo: jumpTo\n    });\n  };\n};\nBottomNavigation.Bar = BottomNavigationBar;\nexport default BottomNavigation;\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    overflow: 'hidden'\n  },\n  content: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "View", "useLatestCallback", "BottomNavigationBar", "BottomNavigationRouteScreen", "useInternalTheme", "useAnimatedValueArray", "FAR_FAR_AWAY", "OS", "SceneComponent", "memo", "_ref", "component", "rest", "_objectWithoutProperties", "_excluded", "createElement", "BottomNavigation", "_ref2", "navigationState", "renderScene", "renderIcon", "renderLabel", "renderTouchable", "getLabelText", "getBadge", "getColor", "getAccessibilityLabel", "getTestID", "activeColor", "inactiveColor", "_ref2$keyboardHidesNa", "keyboardHidesNavigationBar", "barStyle", "_ref2$labeled", "labeled", "style", "activeIndicatorStyle", "_ref2$sceneAnimationE", "sceneAnimationEnabled", "_ref2$sceneAnimationT", "sceneAnimationType", "sceneAnimationEasing", "onTabPress", "onTabLongPress", "onIndexChange", "shiftingProp", "shifting", "safeAreaInsets", "_ref2$labelMaxFontSiz", "labelMaxFontSizeMultiplier", "compactProp", "compact", "_ref2$testID", "testID", "themeOverrides", "theme", "_ref2$getLazy", "getLazy", "_ref3", "route", "lazy", "scale", "animation", "isV3", "routes", "length", "console", "warn", "<PERSON><PERSON><PERSON>", "index", "key", "tabsPositionAnims", "map", "_", "i", "offsetsAnims", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "loaded", "setLoaded", "includes", "concat", "_toConsumableArray", "animateToIndex", "useCallback", "parallel", "timing", "toValue", "duration", "useNativeDriver", "easing", "start", "_ref4", "finished", "for<PERSON>ach", "offset", "setValue", "useEffect", "prevNavigationState", "useRef", "_prevNavigationState$", "current", "handleTabPress", "event", "defaultPrevented", "findIndex", "jumpTo", "colors", "styles", "container", "content", "backgroundColor", "background", "_prevNavigationState$2", "focused", "previouslyFocused", "countAlphaOffscreen", "renderToHardwareTextureAndroid", "opacity", "interpolate", "inputRange", "outputRange", "offsetTarget", "top", "left", "zIndex", "pointerEvents", "accessibilityElementsHidden", "importantForAccessibility", "visibility", "absoluteFill", "collapsable", "removeClippedSubviews", "_extends", "needsOffscreenAlphaCompositing", "transform", "translateX", "translateY", "animationEasing", "SceneMap", "scenes", "_ref5", "Bar", "create", "flex", "overflow"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/BottomNavigation/BottomNavigation.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  ColorValue,\n  EasingFunction,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport useLatestCallback from 'use-latest-callback';\n\nimport BottomNavigationBar from './BottomNavigationBar';\nimport BottomNavigationRouteScreen from './BottomNavigationRouteScreen';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport useAnimatedValueArray from '../../utils/useAnimatedValueArray';\nimport type { IconSource } from '../Icon';\nimport { Props as TouchableRippleProps } from '../TouchableRipple/TouchableRipple';\n\nexport type BaseRoute = {\n  key: string;\n  title?: string;\n  focusedIcon?: IconSource;\n  unfocusedIcon?: IconSource;\n  badge?: string | number | boolean;\n  /**\n   * @deprecated In v5.x works only with theme version 2.\n   */\n  color?: string;\n  accessibilityLabel?: string;\n  testID?: string;\n  lazy?: boolean;\n};\n\ntype NavigationState<Route extends BaseRoute> = {\n  index: number;\n  routes: Route[];\n};\n\ntype TabPressEvent = {\n  defaultPrevented: boolean;\n  preventDefault(): void;\n};\n\ntype TouchableProps<Route extends BaseRoute> = TouchableRippleProps & {\n  key: string;\n  route: Route;\n  children: React.ReactNode;\n  borderless?: boolean;\n  centered?: boolean;\n  rippleColor?: ColorValue;\n};\n\nexport type Props<Route extends BaseRoute> = {\n  /**\n   * Whether the shifting style is used, the active tab icon shifts up to show the label and the inactive tabs won't have a label.\n   *\n   * By default, this is `false` with theme version 3 and `true` when you have more than 3 tabs.\n   * Pass `shifting={false}` to explicitly disable this animation, or `shifting={true}` to always use this animation.\n   * Note that you need at least 2 tabs be able to run this animation.\n   */\n  shifting?: boolean;\n  /**\n   * Whether to show labels in tabs. When `false`, only icons will be displayed.\n   */\n  labeled?: boolean;\n  /**\n   * Whether tabs should be spread across the entire width.\n   */\n  compact?: boolean;\n  /**\n   * State for the bottom navigation. The state should contain the following properties:\n   *\n   * - `index`: a number representing the index of the active route in the `routes` array\n   * - `routes`: an array containing a list of route objects used for rendering the tabs\n   *\n   * Each route object should contain the following properties:\n   *\n   * - `key`: a unique key to identify the route (required)\n   * - `title`: title of the route to use as the tab label\n   * - `focusedIcon`:  icon to use as the focused tab icon, can be a string, an image source or a react component @renamed Renamed from 'icon' to 'focusedIcon' in v5.x\n   * - `unfocusedIcon`:  icon to use as the unfocused tab icon, can be a string, an image source or a react component @supported Available in v5.x with theme version 3\n   * - `color`: color to use as background color for shifting bottom navigation @deprecatedProperty In v5.x works only with theme version 2.\n   * - `badge`: badge to show on the tab icon, can be `true` to show a dot, `string` or `number` to show text.\n   * - `accessibilityLabel`: accessibility label for the tab button\n   * - `testID`: test id for the tab button\n   *\n   * Example:\n   *\n   * ```js\n   * {\n   *   index: 1,\n   *   routes: [\n   *     { key: 'music', title: 'Favorites', focusedIcon: 'heart', unfocusedIcon: 'heart-outline'},\n   *     { key: 'albums', title: 'Albums', focusedIcon: 'album' },\n   *     { key: 'recents', title: 'Recents', focusedIcon: 'history' },\n   *     { key: 'notifications', title: 'Notifications', focusedIcon: 'bell', unfocusedIcon: 'bell-outline' },\n   *   ]\n   * }\n   * ```\n   *\n   * `BottomNavigation` is a controlled component, which means the `index` needs to be updated via the `onIndexChange` callback.\n   */\n  navigationState: NavigationState<Route>;\n  /**\n   * Callback which is called on tab change, receives the index of the new tab as argument.\n   * The navigation state needs to be updated when it's called, otherwise the change is dropped.\n   */\n  onIndexChange: (index: number) => void;\n  /**\n   * Callback which returns a react element to render as the page for the tab. Receives an object containing the route as the argument:\n   *\n   * ```js\n   * renderScene = ({ route, jumpTo }) => {\n   *   switch (route.key) {\n   *     case 'music':\n   *       return <MusicRoute jumpTo={jumpTo} />;\n   *     case 'albums':\n   *       return <AlbumsRoute jumpTo={jumpTo} />;\n   *   }\n   * }\n   * ```\n   *\n   * Pages are lazily rendered, which means that a page will be rendered the first time you navigate to it.\n   * After initial render, all the pages stay rendered to preserve their state.\n   *\n   * You need to make sure that your individual routes implement a `shouldComponentUpdate` to improve the performance.\n   * To make it easier to specify the components, you can use the `SceneMap` helper:\n   *\n   * ```js\n   * renderScene = BottomNavigation.SceneMap({\n   *   music: MusicRoute,\n   *   albums: AlbumsRoute,\n   * });\n   * ```\n   *\n   * Specifying the components this way is easier and takes care of implementing a `shouldComponentUpdate` method.\n   * Each component will receive the current route and a `jumpTo` method as it's props.\n   * The `jumpTo` method can be used to navigate to other tabs programmatically:\n   *\n   * ```js\n   * this.props.jumpTo('albums')\n   * ```\n   */\n  renderScene: (props: {\n    route: Route;\n    jumpTo: (key: string) => void;\n  }) => React.ReactNode | null;\n  /**\n   * Callback which returns a React Element to be used as tab icon.\n   */\n  renderIcon?: (props: {\n    route: Route;\n    focused: boolean;\n    color: string;\n  }) => React.ReactNode;\n  /**\n   * Callback which React Element to be used as tab label.\n   */\n  renderLabel?: (props: {\n    route: Route;\n    focused: boolean;\n    color: string;\n  }) => React.ReactNode;\n  /**\n   * Callback which returns a React element to be used as the touchable for the tab item.\n   * Renders a `TouchableRipple` on Android and `Pressable` on iOS.\n   */\n  renderTouchable?: (props: TouchableProps<Route>) => React.ReactNode;\n  /**\n   * Get accessibility label for the tab button. This is read by the screen reader when the user taps the tab.\n   * Uses `route.accessibilityLabel` by default.\n   */\n  getAccessibilityLabel?: (props: { route: Route }) => string | undefined;\n  /**\n   * Get badge for the tab, uses `route.badge` by default.\n   */\n  getBadge?: (props: { route: Route }) => boolean | number | string | undefined;\n  /**\n   * Get color for the tab, uses `route.color` by default.\n   */\n  getColor?: (props: { route: Route }) => string | undefined;\n  /**\n   * Get label text for the tab, uses `route.title` by default. Use `renderLabel` to replace label component.\n   */\n  getLabelText?: (props: { route: Route }) => string | undefined;\n  /**\n   * Get lazy for the current screen. Uses true by default.\n   */\n  getLazy?: (props: { route: Route }) => boolean | undefined;\n  /**\n   * Get the id to locate this tab button in tests, uses `route.testID` by default.\n   */\n  getTestID?: (props: { route: Route }) => string | undefined;\n  /**\n   * Function to execute on tab press. It receives the route for the pressed tab, useful for things like scroll to top.\n   */\n  onTabPress?: (props: { route: Route } & TabPressEvent) => void;\n  /**\n   * Function to execute on tab long press. It receives the route for the pressed tab, useful for things like custom action when longed pressed.\n   */\n  onTabLongPress?: (props: { route: Route } & TabPressEvent) => void;\n  /**\n   * Custom color for icon and label in the active tab.\n   */\n  activeColor?: string;\n  /**\n   * Custom color for icon and label in the inactive tab.\n   */\n  inactiveColor?: string;\n  /**\n   * Whether animation is enabled for scenes transitions in `shifting` mode.\n   * By default, the scenes cross-fade during tab change when `shifting` is enabled.\n   * Specify `sceneAnimationEnabled` as `false` to disable the animation.\n   */\n  sceneAnimationEnabled?: boolean;\n  /**\n   * The scene animation effect. Specify `'shifting'` for a different effect.\n   * By default, 'opacity' will be used.\n   */\n  sceneAnimationType?: 'opacity' | 'shifting';\n  /**\n   * The scene animation Easing.\n   */\n  sceneAnimationEasing?: EasingFunction | undefined;\n  /**\n   * Whether the bottom navigation bar is hidden when keyboard is shown.\n   * On Android, this works best when [`windowSoftInputMode`](https://developer.android.com/guide/topics/manifest/activity-element#wsoft) is set to `adjustResize`.\n   */\n  keyboardHidesNavigationBar?: boolean;\n  /**\n   * Safe area insets for the tab bar. This can be used to avoid elements like the navigation bar on Android and bottom safe area on iOS.\n   * The bottom insets for iOS is added by default. You can override the behavior with this option.\n   */\n  safeAreaInsets?: {\n    top?: number;\n    right?: number;\n    bottom?: number;\n    left?: number;\n  };\n  /**\n   * Style for the bottom navigation bar.  You can pass a custom background color here:\n   *\n   * ```js\n   * barStyle={{ backgroundColor: '#694fad' }}\n   * ```\n   */\n  barStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  style?: StyleProp<ViewStyle>;\n  activeIndicatorStyle?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n};\n\nconst FAR_FAR_AWAY = Platform.OS === 'web' ? 0 : 9999;\n\nconst SceneComponent = React.memo(({ component, ...rest }: any) =>\n  React.createElement(component, rest)\n);\n\n/**\n * BottomNavigation provides quick navigation between top-level views of an app with a bottom navigation bar.\n * It is primarily designed for use on mobile. If you want to use the navigation bar only see [`BottomNavigation.Bar`](BottomNavigationBar).\n *\n * By default BottomNavigation uses primary color as a background, in dark theme with `adaptive` mode it will use surface colour instead.\n * See [Dark Theme](https://callstack.github.io/react-native-paper/docs/guides/theming#dark-theme) for more information.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { BottomNavigation, Text } from 'react-native-paper';\n *\n * const MusicRoute = () => <Text>Music</Text>;\n *\n * const AlbumsRoute = () => <Text>Albums</Text>;\n *\n * const RecentsRoute = () => <Text>Recents</Text>;\n *\n * const NotificationsRoute = () => <Text>Notifications</Text>;\n *\n * const MyComponent = () => {\n *   const [index, setIndex] = React.useState(0);\n *   const [routes] = React.useState([\n *     { key: 'music', title: 'Favorites', focusedIcon: 'heart', unfocusedIcon: 'heart-outline'},\n *     { key: 'albums', title: 'Albums', focusedIcon: 'album' },\n *     { key: 'recents', title: 'Recents', focusedIcon: 'history' },\n *     { key: 'notifications', title: 'Notifications', focusedIcon: 'bell', unfocusedIcon: 'bell-outline' },\n *   ]);\n *\n *   const renderScene = BottomNavigation.SceneMap({\n *     music: MusicRoute,\n *     albums: AlbumsRoute,\n *     recents: RecentsRoute,\n *     notifications: NotificationsRoute,\n *   });\n *\n *   return (\n *     <BottomNavigation\n *       navigationState={{ index, routes }}\n *       onIndexChange={setIndex}\n *       renderScene={renderScene}\n *     />\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst BottomNavigation = <Route extends BaseRoute>({\n  navigationState,\n  renderScene,\n  renderIcon,\n  renderLabel,\n  renderTouchable,\n  getLabelText,\n  getBadge,\n  getColor,\n  getAccessibilityLabel,\n  getTestID,\n  activeColor,\n  inactiveColor,\n  keyboardHidesNavigationBar = Platform.OS === 'android',\n  barStyle,\n  labeled = true,\n  style,\n  activeIndicatorStyle,\n  sceneAnimationEnabled = false,\n  sceneAnimationType = 'opacity',\n  sceneAnimationEasing,\n  onTabPress,\n  onTabLongPress,\n  onIndexChange,\n  shifting: shiftingProp,\n  safeAreaInsets,\n  labelMaxFontSizeMultiplier = 1,\n  compact: compactProp,\n  testID = 'bottom-navigation',\n  theme: themeOverrides,\n  getLazy = ({ route }: { route: Route }) => route.lazy,\n}: Props<Route>) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { scale } = theme.animation;\n  const compact = compactProp ?? !theme.isV3;\n  let shifting =\n    shiftingProp ?? (theme.isV3 ? false : navigationState.routes.length > 3);\n\n  if (shifting && navigationState.routes.length < 2) {\n    shifting = false;\n    console.warn(\n      'BottomNavigation needs at least 2 tabs to run shifting animation'\n    );\n  }\n\n  const focusedKey = navigationState.routes[navigationState.index].key;\n\n  /**\n   * Active state of individual tab item positions:\n   * -1 if they're before the active tab, 0 if they're active, 1 if they're after the active tab\n   */\n  const tabsPositionAnims = useAnimatedValueArray(\n    navigationState.routes.map((_, i) =>\n      i === navigationState.index ? 0 : i >= navigationState.index ? 1 : -1\n    )\n  );\n\n  /**\n   * The top offset for each tab item to position it offscreen.\n   * Placing items offscreen helps to save memory usage for inactive screens with removeClippedSubviews.\n   * We use animated values for this to prevent unnecessary re-renders.\n   */\n  const offsetsAnims = useAnimatedValueArray(\n    navigationState.routes.map(\n      // offscreen === 1, normal === 0\n      (_, i) => (i === navigationState.index ? 0 : 1)\n    )\n  );\n\n  /**\n   * List of loaded tabs, tabs will be loaded when navigated to.\n   */\n  const [loaded, setLoaded] = React.useState<string[]>([focusedKey]);\n\n  if (!loaded.includes(focusedKey)) {\n    // Set the current tab to be loaded if it was not loaded before\n    setLoaded((loaded) => [...loaded, focusedKey]);\n  }\n\n  const animateToIndex = React.useCallback(\n    (index: number) => {\n      Animated.parallel([\n        ...navigationState.routes.map((_, i) =>\n          Animated.timing(tabsPositionAnims[i], {\n            toValue: i === index ? 0 : i >= index ? 1 : -1,\n            duration: theme.isV3 || shifting ? 150 * scale : 0,\n            useNativeDriver: true,\n            easing: sceneAnimationEasing,\n          })\n        ),\n      ]).start(({ finished }) => {\n        if (finished) {\n          // Position all inactive screens offscreen to save memory usage\n          // Only do it when animation has finished to avoid glitches mid-transition if switching fast\n          offsetsAnims.forEach((offset, i) => {\n            if (i === index) {\n              offset.setValue(0);\n            } else {\n              offset.setValue(1);\n            }\n          });\n        }\n      });\n    },\n    [\n      shifting,\n      navigationState.routes,\n      offsetsAnims,\n      scale,\n      tabsPositionAnims,\n      sceneAnimationEasing,\n      theme,\n    ]\n  );\n\n  React.useEffect(() => {\n    // Workaround for native animated bug in react-native@^0.57\n    // Context: https://github.com/callstack/react-native-paper/pull/637\n    animateToIndex(navigationState.index);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const prevNavigationState = React.useRef<NavigationState<Route>>();\n\n  React.useEffect(() => {\n    // Reset offsets of previous and current tabs before animation\n    offsetsAnims.forEach((offset, i) => {\n      if (\n        i === navigationState.index ||\n        i === prevNavigationState.current?.index\n      ) {\n        offset.setValue(0);\n      }\n    });\n\n    animateToIndex(navigationState.index);\n  }, [navigationState.index, animateToIndex, offsetsAnims]);\n\n  const handleTabPress = useLatestCallback(\n    (event: { route: Route } & TabPressEvent) => {\n      onTabPress?.(event);\n\n      if (event.defaultPrevented) {\n        return;\n      }\n\n      const index = navigationState.routes.findIndex(\n        (route) => event.route.key === route.key\n      );\n\n      if (index !== navigationState.index) {\n        prevNavigationState.current = navigationState;\n        onIndexChange(index);\n      }\n    }\n  );\n\n  const jumpTo = useLatestCallback((key: string) => {\n    const index = navigationState.routes.findIndex(\n      (route) => route.key === key\n    );\n\n    prevNavigationState.current = navigationState;\n    onIndexChange(index);\n  });\n\n  const { routes } = navigationState;\n  const { colors } = theme;\n\n  return (\n    <View style={[styles.container, style]} testID={testID}>\n      <View style={[styles.content, { backgroundColor: colors?.background }]}>\n        {routes.map((route, index) => {\n          if (getLazy({ route }) !== false && !loaded.includes(route.key)) {\n            // Don't render a screen if we've never navigated to it\n            return null;\n          }\n\n          const focused = navigationState.index === index;\n          const previouslyFocused =\n            prevNavigationState.current?.index === index;\n          const countAlphaOffscreen =\n            sceneAnimationEnabled && (focused || previouslyFocused);\n          const renderToHardwareTextureAndroid =\n            sceneAnimationEnabled && focused;\n\n          const opacity = sceneAnimationEnabled\n            ? tabsPositionAnims[index].interpolate({\n                inputRange: [-1, 0, 1],\n                outputRange: [0, 1, 0],\n              })\n            : focused\n            ? 1\n            : 0;\n\n          const offsetTarget = focused ? 0 : FAR_FAR_AWAY;\n\n          const top = sceneAnimationEnabled\n            ? offsetsAnims[index].interpolate({\n                inputRange: [0, 1],\n                outputRange: [0, offsetTarget],\n              })\n            : offsetTarget;\n\n          const left =\n            sceneAnimationType === 'shifting'\n              ? tabsPositionAnims[index].interpolate({\n                  inputRange: [-1, 0, 1],\n                  outputRange: [-50, 0, 50],\n                })\n              : 0;\n\n          const zIndex = focused ? 1 : 0;\n\n          return (\n            <BottomNavigationRouteScreen\n              key={route.key}\n              pointerEvents={focused ? 'auto' : 'none'}\n              accessibilityElementsHidden={!focused}\n              importantForAccessibility={\n                focused ? 'auto' : 'no-hide-descendants'\n              }\n              index={index}\n              visibility={opacity}\n              style={[StyleSheet.absoluteFill, { zIndex }]}\n              collapsable={false}\n              removeClippedSubviews={\n                // On iOS, set removeClippedSubviews to true only when not focused\n                // This is an workaround for a bug where the clipped view never re-appears\n                Platform.OS === 'ios' ? navigationState.index !== index : true\n              }\n            >\n              <Animated.View\n                {...(Platform.OS === 'android' && {\n                  needsOffscreenAlphaCompositing: countAlphaOffscreen,\n                })}\n                renderToHardwareTextureAndroid={renderToHardwareTextureAndroid}\n                style={[\n                  styles.content,\n                  {\n                    opacity,\n                    transform: [{ translateX: left }, { translateY: top }],\n                  },\n                ]}\n              >\n                {renderScene({ route, jumpTo })}\n              </Animated.View>\n            </BottomNavigationRouteScreen>\n          );\n        })}\n      </View>\n      <BottomNavigationBar\n        navigationState={navigationState}\n        renderIcon={renderIcon}\n        renderLabel={renderLabel}\n        renderTouchable={renderTouchable}\n        getLabelText={getLabelText}\n        getBadge={getBadge}\n        getColor={getColor}\n        getAccessibilityLabel={getAccessibilityLabel}\n        getTestID={getTestID}\n        activeColor={activeColor}\n        inactiveColor={inactiveColor}\n        keyboardHidesNavigationBar={keyboardHidesNavigationBar}\n        style={barStyle}\n        activeIndicatorStyle={activeIndicatorStyle}\n        labeled={labeled}\n        animationEasing={sceneAnimationEasing}\n        onTabPress={handleTabPress}\n        onTabLongPress={onTabLongPress}\n        shifting={shifting}\n        safeAreaInsets={safeAreaInsets}\n        labelMaxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n        compact={compact}\n        testID={`${testID}-bar`}\n        theme={theme}\n      />\n    </View>\n  );\n};\n\n/**\n * Function which takes a map of route keys to components.\n * Pure components are used to minimize re-rendering of the pages.\n * This drastically improves the animation performance.\n */\nBottomNavigation.SceneMap = <Route extends BaseRoute>(scenes: {\n  [key: string]: React.ComponentType<{\n    route: Route;\n    jumpTo: (key: string) => void;\n  }>;\n}) => {\n  return ({\n    route,\n    jumpTo,\n  }: {\n    route: Route;\n    jumpTo: (key: string) => void;\n  }) => (\n    <SceneComponent\n      key={route.key}\n      component={scenes[route.key ? route.key : '']}\n      route={route}\n      jumpTo={jumpTo}\n    />\n  );\n};\n\n// @component ./BottomNavigationBar.tsx\nBottomNavigation.Bar = BottomNavigationBar;\n\nexport default BottomNavigation;\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    overflow: 'hidden',\n  },\n  content: {\n    flex: 1,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAY9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,OAAOC,mBAAmB;AAC1B,OAAOC,2BAA2B;AAClC,SAASC,gBAAgB;AAEzB,OAAOC,qBAAqB;AAyP5B,IAAMC,YAAY,GAAGR,QAAQ,CAACS,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI;AAErD,IAAMC,cAAc,GAAGZ,KAAK,CAACa,IAAI,CAAC,UAAAC,IAAA;EAAA,IAAGC,SAAS,GAAgBD,IAAA,CAAzBC,SAAS;IAAKC,IAAA,GAAAC,wBAAA,CAAWH,IAAA,EAAAI,SAAA;EAAA,OAC5DlB,KAAK,CAACmB,aAAa,CAACJ,SAAS,EAAEC,IAAI,CAAC;AAAA,CACtC,CAAC;AAkDD,IAAMI,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAGC,KAAA,EA+BL;EAAA,IA9BlBC,eAAe,GA8BFD,KAAA,CA9BbC,eAAe;IACfC,WAAW,GA6BEF,KAAA,CA7BbE,WAAW;IACXC,UAAU,GA4BGH,KAAA,CA5BbG,UAAU;IACVC,WAAW,GA2BEJ,KAAA,CA3BbI,WAAW;IACXC,eAAe,GA0BFL,KAAA,CA1BbK,eAAe;IACfC,YAAY,GAyBCN,KAAA,CAzBbM,YAAY;IACZC,QAAQ,GAwBKP,KAAA,CAxBbO,QAAQ;IACRC,QAAQ,GAuBKR,KAAA,CAvBbQ,QAAQ;IACRC,qBAAqB,GAsBRT,KAAA,CAtBbS,qBAAqB;IACrBC,SAAS,GAqBIV,KAAA,CArBbU,SAAS;IACTC,WAAW,GAoBEX,KAAA,CApBbW,WAAW;IACXC,aAAa,GAmBAZ,KAAA,CAnBbY,aAAa;IAAAC,qBAAA,GAmBAb,KAAA,CAlBbc,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAGhC,QAAQ,CAACS,EAAE,KAAK,SAAS,GAAAuB,qBAAA;IACtDE,QAAQ,GAiBKf,KAAA,CAjBbe,QAAQ;IAAAC,aAAA,GAiBKhB,KAAA,CAhBbiB,OAAO;IAAPA,OAAO,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACdE,KAAK,GAeQlB,KAAA,CAfbkB,KAAK;IACLC,oBAAoB,GAcPnB,KAAA,CAdbmB,oBAAoB;IAAAC,qBAAA,GAcPpB,KAAA,CAbbqB,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IAAAE,qBAAA,GAahBtB,KAAA,CAZbuB,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAG,SAAS,GAAAA,qBAAA;IAC9BE,oBAAoB,GAWPxB,KAAA,CAXbwB,oBAAoB;IACpBC,UAAU,GAUGzB,KAAA,CAVbyB,UAAU;IACVC,cAAc,GASD1B,KAAA,CATb0B,cAAc;IACdC,aAAa,GAQA3B,KAAA,CARb2B,aAAa;IACHC,YAAY,GAOT5B,KAAA,CAPb6B,QAAQ;IACRC,cAAc,GAMD9B,KAAA,CANb8B,cAAc;IAAAC,qBAAA,GAMD/B,KAAA,CALbgC,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;IACrBE,WAAW,GAIPjC,KAAA,CAJbkC,OAAO;IAAAC,YAAA,GAIMnC,KAAA,CAHboC,MAAM;IAANA,MAAM,GAAAD,YAAA,cAAG,mBAAmB,GAAAA,YAAA;IACrBE,cAAc,GAERrC,KAAA,CAFbsC,KAAK;IAAAC,aAAA,GAEQvC,KAAA,CADbwC,OAAO;IAAPA,OAAO,GAAAD,aAAA,cAAG,UAAAE,KAAA;MAAA,IAAGC,KAAA,GAAyBD,KAAA,CAAzBC,KAAA;MAAyB,OAAKA,KAAK,CAACC,IAAI;IAAA,IAAAJ,aAAA;EAErD,IAAMD,KAAK,GAAGnD,gBAAgB,CAACkD,cAAc,CAAC;EAC9C,IAAQO,KAAA,GAAUN,KAAK,CAACO,SAAS,CAAzBD,KAAA;EACR,IAAMV,OAAO,GAAGD,WAAW,WAAXA,WAAW,GAAI,CAACK,KAAK,CAACQ,IAAI;EAC1C,IAAIjB,QAAQ,GACVD,YAAY,WAAZA,YAAY,GAAKU,KAAK,CAACQ,IAAI,GAAG,KAAK,GAAG7C,eAAe,CAAC8C,MAAM,CAACC,MAAM,GAAG,CAAE;EAE1E,IAAInB,QAAQ,IAAI5B,eAAe,CAAC8C,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;IACjDnB,QAAQ,GAAG,KAAK;IAChBoB,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACH;EAEA,IAAMC,UAAU,GAAGlD,eAAe,CAAC8C,MAAM,CAAC9C,eAAe,CAACmD,KAAK,CAAC,CAACC,GAAG;EAMpE,IAAMC,iBAAiB,GAAGlE,qBAAqB,CAC7Ca,eAAe,CAAC8C,MAAM,CAACQ,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;IAAA,OAC9BA,CAAC,KAAKxD,eAAe,CAACmD,KAAK,GAAG,CAAC,GAAGK,CAAC,IAAIxD,eAAe,CAACmD,KAAK,GAAG,CAAC,GAAG,CAAC,CACtE;EAAA,EACF,CAAC;EAOD,IAAMM,YAAY,GAAGtE,qBAAqB,CACxCa,eAAe,CAAC8C,MAAM,CAACQ,GAAG,CAExB,UAACC,CAAC,EAAEC,CAAC;IAAA,OAAMA,CAAC,KAAKxD,eAAe,CAACmD,KAAK,GAAG,CAAC,GAAG,CAC/C;EAAA,EACF,CAAC;EAKD,IAAAO,eAAA,GAA4BhF,KAAK,CAACiF,QAAQ,CAAW,CAACT,UAAU,CAAC,CAAC;IAAAU,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA3DI,MAAM,GAAAF,gBAAA;IAAEG,SAAS,GAAAH,gBAAA;EAExB,IAAI,CAACE,MAAM,CAACE,QAAQ,CAACd,UAAU,CAAC,EAAE;IAEhCa,SAAS,CAAE,UAAAD,MAAM;MAAA,UAAAG,MAAA,CAAAC,kBAAA,CAASJ,MAAM,IAAEZ,UAAU;IAAA,CAAC,CAAC;EAChD;EAEA,IAAMiB,cAAc,GAAGzF,KAAK,CAAC0F,WAAW,CACrC,UAAAjB,KAAa,EAAK;IACjBxE,QAAQ,CAAC0F,QAAQ,CAAAH,kBAAA,CACZlE,eAAe,CAAC8C,MAAM,CAACQ,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;MAAA,OACjC7E,QAAQ,CAAC2F,MAAM,CAACjB,iBAAiB,CAACG,CAAC,CAAC,EAAE;QACpCe,OAAO,EAAEf,CAAC,KAAKL,KAAK,GAAG,CAAC,GAAGK,CAAC,IAAIL,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9CqB,QAAQ,EAAEnC,KAAK,CAACQ,IAAI,IAAIjB,QAAQ,GAAG,GAAG,GAAGe,KAAK,GAAG,CAAC;QAClD8B,eAAe,EAAE,IAAI;QACrBC,MAAM,EAAEnD;MACV,CAAC,CACH;IAAA,EAAC,CACF,CAAC,CAACoD,KAAK,CAAC,UAAAC,KAAA,EAAkB;MAAA,IAAfC,QAAA,GAAUD,KAAA,CAAVC,QAAA;MACV,IAAIA,QAAQ,EAAE;QAGZpB,YAAY,CAACqB,OAAO,CAAC,UAACC,MAAM,EAAEvB,CAAC,EAAK;UAClC,IAAIA,CAAC,KAAKL,KAAK,EAAE;YACf4B,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;UACpB,CAAC,MAAM;YACLD,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;UACpB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,EACD,CACEpD,QAAQ,EACR5B,eAAe,CAAC8C,MAAM,EACtBW,YAAY,EACZd,KAAK,EACLU,iBAAiB,EACjB9B,oBAAoB,EACpBc,KAAK,CAET,CAAC;EAED3D,KAAK,CAACuG,SAAS,CAAC,YAAM;IAGpBd,cAAc,CAACnE,eAAe,CAACmD,KAAK,CAAC;EAEvC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM+B,mBAAmB,GAAGxG,KAAK,CAACyG,MAAM,CAAyB,CAAC;EAElEzG,KAAK,CAACuG,SAAS,CAAC,YAAM;IAEpBxB,YAAY,CAACqB,OAAO,CAAC,UAACC,MAAM,EAAEvB,CAAC,EAAK;MAAA,IAAA4B,qBAAA;MAClC,IACE5B,CAAC,KAAKxD,eAAe,CAACmD,KAAK,IAC3BK,CAAC,OAAA4B,qBAAA,GAAKF,mBAAmB,CAACG,OAAO,cAAAD,qBAAA,uBAA3BA,qBAAA,CAA6BjC,KAAK,GACxC;QACA4B,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEFb,cAAc,CAACnE,eAAe,CAACmD,KAAK,CAAC;EACvC,CAAC,EAAE,CAACnD,eAAe,CAACmD,KAAK,EAAEgB,cAAc,EAAEV,YAAY,CAAC,CAAC;EAEzD,IAAM6B,cAAc,GAAGvG,iBAAiB,CACrC,UAAAwG,KAAuC,EAAK;IAC3C/D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG+D,KAAK,CAAC;IAEnB,IAAIA,KAAK,CAACC,gBAAgB,EAAE;MAC1B;IACF;IAEA,IAAMrC,KAAK,GAAGnD,eAAe,CAAC8C,MAAM,CAAC2C,SAAS,CAC3C,UAAAhD,KAAK;MAAA,OAAK8C,KAAK,CAAC9C,KAAK,CAACW,GAAG,KAAKX,KAAK,CAACW,GACvC;IAAA,EAAC;IAED,IAAID,KAAK,KAAKnD,eAAe,CAACmD,KAAK,EAAE;MACnC+B,mBAAmB,CAACG,OAAO,GAAGrF,eAAe;MAC7C0B,aAAa,CAACyB,KAAK,CAAC;IACtB;EACF,CACF,CAAC;EAED,IAAMuC,MAAM,GAAG3G,iBAAiB,CAAE,UAAAqE,GAAW,EAAK;IAChD,IAAMD,KAAK,GAAGnD,eAAe,CAAC8C,MAAM,CAAC2C,SAAS,CAC3C,UAAAhD,KAAK;MAAA,OAAKA,KAAK,CAACW,GAAG,KAAKA,GAC3B;IAAA,EAAC;IAED8B,mBAAmB,CAACG,OAAO,GAAGrF,eAAe;IAC7C0B,aAAa,CAACyB,KAAK,CAAC;EACtB,CAAC,CAAC;EAEF,IAAQL,MAAA,GAAW9C,eAAe,CAA1B8C,MAAA;EACR,IAAQ6C,MAAA,GAAWtD,KAAK,CAAhBsD,MAAA;EAER,OACEjH,KAAA,CAAAmB,aAAA,CAACf,IAAI;IAACmC,KAAK,EAAE,CAAC2E,MAAM,CAACC,SAAS,EAAE5E,KAAK,CAAE;IAACkB,MAAM,EAAEA;EAAO,GACrDzD,KAAA,CAAAmB,aAAA,CAACf,IAAI;IAACmC,KAAK,EAAE,CAAC2E,MAAM,CAACE,OAAO,EAAE;MAAEC,eAAe,EAAEJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK;IAAW,CAAC;EAAE,GACpElD,MAAM,CAACQ,GAAG,CAAC,UAACb,KAAK,EAAEU,KAAK,EAAK;IAAA,IAAA8C,sBAAA;IAC5B,IAAI1D,OAAO,CAAC;MAAEE,KAAA,EAAAA;IAAM,CAAC,CAAC,KAAK,KAAK,IAAI,CAACqB,MAAM,CAACE,QAAQ,CAACvB,KAAK,CAACW,GAAG,CAAC,EAAE;MAE/D,OAAO,IAAI;IACb;IAEA,IAAM8C,OAAO,GAAGlG,eAAe,CAACmD,KAAK,KAAKA,KAAK;IAC/C,IAAMgD,iBAAiB,GACrB,EAAAF,sBAAA,GAAAf,mBAAmB,CAACG,OAAO,cAAAY,sBAAA,uBAA3BA,sBAAA,CAA6B9C,KAAK,MAAKA,KAAK;IAC9C,IAAMiD,mBAAmB,GACvBhF,qBAAqB,KAAK8E,OAAO,IAAIC,iBAAiB,CAAC;IACzD,IAAME,8BAA8B,GAClCjF,qBAAqB,IAAI8E,OAAO;IAElC,IAAMI,OAAO,GAAGlF,qBAAqB,GACjCiC,iBAAiB,CAACF,KAAK,CAAC,CAACoD,WAAW,CAAC;MACnCC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC,GACFP,OAAO,GACP,CAAC,GACD,CAAC;IAEL,IAAMQ,YAAY,GAAGR,OAAO,GAAG,CAAC,GAAG9G,YAAY;IAE/C,IAAMuH,GAAG,GAAGvF,qBAAqB,GAC7BqC,YAAY,CAACN,KAAK,CAAC,CAACoD,WAAW,CAAC;MAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAAC,EAAEC,YAAY;IAC/B,CAAC,CAAC,GACFA,YAAY;IAEhB,IAAME,IAAI,GACRtF,kBAAkB,KAAK,UAAU,GAC7B+B,iBAAiB,CAACF,KAAK,CAAC,CAACoD,WAAW,CAAC;MACnCC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;IAC1B,CAAC,CAAC,GACF,CAAC;IAEP,IAAMI,MAAM,GAAGX,OAAO,GAAG,CAAC,GAAG,CAAC;IAE9B,OACExH,KAAA,CAAAmB,aAAA,CAACZ,2BAA2B;MAC1BmE,GAAG,EAAEX,KAAK,CAACW,GAAI;MACf0D,aAAa,EAAEZ,OAAO,GAAG,MAAM,GAAG,MAAO;MACzCa,2BAA2B,EAAE,CAACb,OAAQ;MACtCc,yBAAyB,EACvBd,OAAO,GAAG,MAAM,GAAG,qBACpB;MACD/C,KAAK,EAAEA,KAAM;MACb8D,UAAU,EAAEX,OAAQ;MACpBrF,KAAK,EAAE,CAACpC,UAAU,CAACqI,YAAY,EAAE;QAAEL,MAAA,EAAAA;MAAO,CAAC,CAAE;MAC7CM,WAAW,EAAE,KAAM;MACnBC,qBAAqB,EAGnBxI,QAAQ,CAACS,EAAE,KAAK,KAAK,GAAGW,eAAe,CAACmD,KAAK,KAAKA,KAAK,GAAG;IAC3D,GAEDzE,KAAA,CAAAmB,aAAA,CAAClB,QAAQ,CAACG,IAAI,EAAAuI,QAAA,KACPzI,QAAQ,CAACS,EAAE,KAAK,SAAS,IAAI;MAChCiI,8BAA8B,EAAElB;IAClC,CAAC;MACDC,8BAA8B,EAAEA,8BAA+B;MAC/DpF,KAAK,EAAE,CACL2E,MAAM,CAACE,OAAO,EACd;QACEQ,OAAO,EAAPA,OAAO;QACPiB,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAEZ;QAAK,CAAC,EAAE;UAAEa,UAAU,EAAEd;QAAI,CAAC;MACvD,CAAC;IACD,IAED1G,WAAW,CAAC;MAAEwC,KAAK,EAALA,KAAK;MAAEiD,MAAA,EAAAA;IAAO,CAAC,CACjB,CACY,CAAC;EAElC,CAAC,CACG,CAAC,EACPhH,KAAA,CAAAmB,aAAA,CAACb,mBAAmB;IAClBgB,eAAe,EAAEA,eAAgB;IACjCE,UAAU,EAAEA,UAAW;IACvBC,WAAW,EAAEA,WAAY;IACzBC,eAAe,EAAEA,eAAgB;IACjCC,YAAY,EAAEA,YAAa;IAC3BC,QAAQ,EAAEA,QAAS;IACnBC,QAAQ,EAAEA,QAAS;IACnBC,qBAAqB,EAAEA,qBAAsB;IAC7CC,SAAS,EAAEA,SAAU;IACrBC,WAAW,EAAEA,WAAY;IACzBC,aAAa,EAAEA,aAAc;IAC7BE,0BAA0B,EAAEA,0BAA2B;IACvDI,KAAK,EAAEH,QAAS;IAChBI,oBAAoB,EAAEA,oBAAqB;IAC3CF,OAAO,EAAEA,OAAQ;IACjB0G,eAAe,EAAEnG,oBAAqB;IACtCC,UAAU,EAAE8D,cAAe;IAC3B7D,cAAc,EAAEA,cAAe;IAC/BG,QAAQ,EAAEA,QAAS;IACnBC,cAAc,EAAEA,cAAe;IAC/BE,0BAA0B,EAAEA,0BAA2B;IACvDE,OAAO,EAAEA,OAAQ;IACjBE,MAAM,EAAG,GAAEA,MAAO,MAAM;IACxBE,KAAK,EAAEA;EAAM,CACd,CACG,CAAC;AAEX,CAAC;AAODvC,gBAAgB,CAAC6H,QAAQ,GAA6B,UAAAC,MAKrD,EAAK;EACJ,OAAO,UAAAC,KAAA;IAAA,IACLpF,KAAK,GAKNoF,KAAA,CALCpF,KAAK;MACLiD,MAAA,GAIDmC,KAAA,CAJCnC,MAAA;IAID,OACChH,KAAA,CAAAmB,aAAA,CAACP,cAAc;MACb8D,GAAG,EAAEX,KAAK,CAACW,GAAI;MACf3D,SAAS,EAAEmI,MAAM,CAACnF,KAAK,CAACW,GAAG,GAAGX,KAAK,CAACW,GAAG,GAAG,EAAE,CAAE;MAC9CX,KAAK,EAAEA,KAAM;MACbiD,MAAM,EAAEA;IAAO,CAChB,CAAC;EAAA,CACH;AACH,CAAC;AAGD5F,gBAAgB,CAACgI,GAAG,GAAG9I,mBAAmB;AAE1C,eAAec,gBAAgB;AAE/B,IAAM8F,MAAM,GAAG/G,UAAU,CAACkJ,MAAM,CAAC;EAC/BlC,SAAS,EAAE;IACTmC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC;EACDnC,OAAO,EAAE;IACPkC,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}