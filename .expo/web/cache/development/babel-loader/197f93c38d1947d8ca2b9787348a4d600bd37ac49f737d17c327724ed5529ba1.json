{"ast": null, "code": "import { getAssetByID } from 'react-native/Libraries/Image/AssetRegistry';\nimport AssetSourceResolver from \"./AssetSourceResolver\";\nvar _customSourceTransformer;\nexport function setCustomSourceTransformer(transformer) {\n  _customSourceTransformer = transformer;\n}\nexport default function resolveAssetSource(source) {\n  if (typeof source === 'object') {\n    return source;\n  }\n  var asset = getAssetByID(source);\n  if (!asset) {\n    return undefined;\n  }\n  var resolver = new AssetSourceResolver('https://expo.dev', null, asset);\n  if (_customSourceTransformer) {\n    return _customSourceTransformer(resolver);\n  }\n  return resolver.defaultAsset();\n}\nObject.defineProperty(resolveAssetSource, 'setCustomSourceTransformer', {\n  get: function get() {\n    return setCustomSourceTransformer;\n  }\n});\nvar pickScale = AssetSourceResolver.pickScale;\nexport { pickScale };", "map": {"version": 3, "names": ["getAssetByID", "AssetSourceResolver", "_customSourceTransformer", "setCustomSourceTransformer", "transformer", "resolveAssetSource", "source", "asset", "undefined", "resolver", "defaultAsset", "Object", "defineProperty", "get", "pickScale"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-asset/src/resolveAssetSource.web.ts"], "sourcesContent": ["import { getAssetByID } from 'react-native/Libraries/Image/AssetRegistry';\n\nimport AssetSourceResolver, { ResolvedAssetSource } from './AssetSourceResolver';\n\nlet _customSourceTransformer;\n\nexport function setCustomSourceTransformer(\n  transformer: (resolver: AssetSourceResolver) => ResolvedAssetSource\n): void {\n  _customSourceTransformer = transformer;\n}\n\n/**\n * `source` is either a number (opaque type returned by require('./foo.png'))\n * or an `ImageSource` like { uri: '<http location || file path>' }\n */\nexport default function resolveAssetSource(source: any): ResolvedAssetSource | undefined {\n  if (typeof source === 'object') {\n    return source;\n  }\n\n  const asset = getAssetByID(source);\n  if (!asset) {\n    return undefined;\n  }\n\n  const resolver = new AssetSourceResolver(\n    // Doesn't matter since this is removed on web\n    'https://expo.dev',\n    null,\n    asset\n  );\n  if (_customSourceTransformer) {\n    return _customSourceTransformer(resolver);\n  }\n  return resolver.defaultAsset();\n}\n\nObject.defineProperty(resolveAssetSource, 'setCustomSourceTransformer', {\n  get() {\n    return setCustomSourceTransformer;\n  },\n});\n\nexport const { pickScale } = AssetSourceResolver;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4CAA4C;AAEzE,OAAOC,mBAA4C;AAEnD,IAAIC,wBAAwB;AAE5B,OAAM,SAAUC,0BAA0BA,CACxCC,WAAmE;EAEnEF,wBAAwB,GAAGE,WAAW;AACxC;AAMA,eAAc,SAAUC,kBAAkBA,CAACC,MAAW;EACpD,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAOA,MAAM;;EAGf,IAAMC,KAAK,GAAGP,YAAY,CAACM,MAAM,CAAC;EAClC,IAAI,CAACC,KAAK,EAAE;IACV,OAAOC,SAAS;;EAGlB,IAAMC,QAAQ,GAAG,IAAIR,mBAAmB,CAEtC,kBAAkB,EAClB,IAAI,EACJM,KAAK,CACN;EACD,IAAIL,wBAAwB,EAAE;IAC5B,OAAOA,wBAAwB,CAACO,QAAQ,CAAC;;EAE3C,OAAOA,QAAQ,CAACC,YAAY,EAAE;AAChC;AAEAC,MAAM,CAACC,cAAc,CAACP,kBAAkB,EAAE,4BAA4B,EAAE;EACtEQ,GAAG,WAAHA,GAAGA,CAAA;IACD,OAAOV,0BAA0B;EACnC;CACD,CAAC;AAEK,IAAQW,SAAS,GAAKb,mBAAmB,CAAjCa,SAAS;AAAyB,SAAAA,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}