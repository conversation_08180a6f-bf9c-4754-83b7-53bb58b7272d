{"ast": null, "code": "import { getActionFromState, getStateFromPath, NavigationContainerRefContext } from '@react-navigation/core';\nimport * as React from 'react';\nimport LinkingContext from \"./LinkingContext\";\nexport default function useLinkTo() {\n  var navigation = React.useContext(NavigationContainerRefContext);\n  var linking = React.useContext(LinkingContext);\n  var linkTo = React.useCallback(function (to) {\n    if (navigation === undefined) {\n      throw new Error(\"Couldn't find a navigation object. Is your component inside NavigationContainer?\");\n    }\n    if (typeof to !== 'string') {\n      navigation.navigate(to.screen, to.params);\n      return;\n    }\n    if (!to.startsWith('/')) {\n      throw new Error(`The path must start with '/' (${to}).`);\n    }\n    var options = linking.options;\n    var state = options !== null && options !== void 0 && options.getStateFromPath ? options.getStateFromPath(to, options.config) : getStateFromPath(to, options === null || options === void 0 ? void 0 : options.config);\n    if (state) {\n      var action = getActionFromState(state, options === null || options === void 0 ? void 0 : options.config);\n      if (action !== undefined) {\n        navigation.dispatch(action);\n      } else {\n        navigation.reset(state);\n      }\n    } else {\n      throw new Error('Failed to parse the path to a navigation state.');\n    }\n  }, [linking, navigation]);\n  return linkTo;\n}", "map": {"version": 3, "names": ["getActionFromState", "getStateFromPath", "NavigationContainerRefContext", "React", "LinkingContext", "useLinkTo", "navigation", "useContext", "linking", "linkTo", "useCallback", "to", "undefined", "Error", "navigate", "screen", "params", "startsWith", "options", "state", "config", "action", "dispatch", "reset"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/native/src/useLinkTo.tsx"], "sourcesContent": ["import {\n  getActionFromState,\n  getStateFromPath,\n  NavigationContainerRefContext,\n} from '@react-navigation/core';\nimport * as React from 'react';\n\nimport LinkingContext from './LinkingContext';\n\nexport type To<\n  ParamList extends ReactNavigation.RootParamList = ReactNavigation.RootParamList,\n  RouteName extends keyof ParamList = keyof ParamList\n> =\n  | string\n  | (undefined extends ParamList[RouteName]\n      ? {\n          screen: Extract<RouteName, string>;\n          params?: ParamList[RouteName];\n        }\n      : {\n          screen: Extract<RouteName, string>;\n          params: ParamList[RouteName];\n        });\n\nexport default function useLinkTo<\n  ParamList extends ReactNavigation.RootParamList\n>() {\n  const navigation = React.useContext(NavigationContainerRefContext);\n  const linking = React.useContext(LinkingContext);\n\n  const linkTo = React.useCallback(\n    (to: To<ParamList>) => {\n      if (navigation === undefined) {\n        throw new Error(\n          \"Couldn't find a navigation object. Is your component inside NavigationContainer?\"\n        );\n      }\n\n      if (typeof to !== 'string') {\n        // @ts-expect-error: This is fine\n        navigation.navigate(to.screen, to.params);\n        return;\n      }\n\n      if (!to.startsWith('/')) {\n        throw new Error(`The path must start with '/' (${to}).`);\n      }\n\n      const { options } = linking;\n\n      const state = options?.getStateFromPath\n        ? options.getStateFromPath(to, options.config)\n        : getStateFromPath(to, options?.config);\n\n      if (state) {\n        const action = getActionFromState(state, options?.config);\n\n        if (action !== undefined) {\n          navigation.dispatch(action);\n        } else {\n          navigation.reset(state);\n        }\n      } else {\n        throw new Error('Failed to parse the path to a navigation state.');\n      }\n    },\n    [linking, navigation]\n  );\n\n  return linkTo;\n}\n"], "mappings": "AAAA,SACEA,kBAAkB,EAClBC,gBAAgB,EAChBC,6BAA6B,QACxB,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,cAAc;AAiBrB,eAAe,SAASC,SAASA,CAAA,EAE7B;EACF,IAAMC,UAAU,GAAGH,KAAK,CAACI,UAAU,CAACL,6BAA6B,CAAC;EAClE,IAAMM,OAAO,GAAGL,KAAK,CAACI,UAAU,CAACH,cAAc,CAAC;EAEhD,IAAMK,MAAM,GAAGN,KAAK,CAACO,WAAW,CAC7B,UAAAC,EAAiB,EAAK;IACrB,IAAIL,UAAU,KAAKM,SAAS,EAAE;MAC5B,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;IACH;IAEA,IAAI,OAAOF,EAAE,KAAK,QAAQ,EAAE;MAE1BL,UAAU,CAACQ,QAAQ,CAACH,EAAE,CAACI,MAAM,EAAEJ,EAAE,CAACK,MAAM,CAAC;MACzC;IACF;IAEA,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE;MACvB,MAAM,IAAIJ,KAAK,CAAE,iCAAgCF,EAAG,IAAG,CAAC;IAC1D;IAEA,IAAQO,OAAA,GAAYV,OAAO,CAAnBU,OAAA;IAER,IAAMC,KAAK,GAAGD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEjB,gBAAgB,GACnCiB,OAAO,CAACjB,gBAAgB,CAACU,EAAE,EAAEO,OAAO,CAACE,MAAM,CAAC,GAC5CnB,gBAAgB,CAACU,EAAE,EAAEO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,MAAM,CAAC;IAEzC,IAAID,KAAK,EAAE;MACT,IAAME,MAAM,GAAGrB,kBAAkB,CAACmB,KAAK,EAAED,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,MAAM,CAAC;MAEzD,IAAIC,MAAM,KAAKT,SAAS,EAAE;QACxBN,UAAU,CAACgB,QAAQ,CAACD,MAAM,CAAC;MAC7B,CAAC,MAAM;QACLf,UAAU,CAACiB,KAAK,CAACJ,KAAK,CAAC;MACzB;IACF,CAAC,MAAM;MACL,MAAM,IAAIN,KAAK,CAAC,iDAAiD,CAAC;IACpE;EACF,CAAC,EACD,CAACL,OAAO,EAAEF,UAAU,CAAC,CACtB;EAED,OAAOG,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}