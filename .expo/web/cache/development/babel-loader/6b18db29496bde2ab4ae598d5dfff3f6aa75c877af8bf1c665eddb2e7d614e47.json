{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport * as SQLite from 'expo-sqlite';\nimport * as FileSystem from 'expo-file-system';\nimport { Asset } from 'expo-asset';\nimport dbManager from \"../database/DatabaseManager\";\nvar FoodDatabaseImporter = function () {\n  function FoodDatabaseImporter() {\n    _classCallCheck(this, FoodDatabaseImporter);\n  }\n  return _createClass(FoodDatabaseImporter, [{\n    key: \"importFoodDatabase\",\n    value: (function () {\n      var _importFoodDatabase = _asyncToGenerator(function* (databaseName) {\n        var progressCallback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n        try {\n          var stats = {\n            foods: 0,\n            nutrients: 0,\n            ingredients: 0,\n            allergens: 0,\n            errors: 0\n          };\n          if (progressCallback) {\n            progressCallback({\n              status: 'preparing',\n              message: 'Preparing to import food database...',\n              progress: 0.1\n            });\n          }\n          var dbAsset = Asset.fromModule(require(`../assets/databases/${databaseName}.sqlite`));\n          yield dbAsset.downloadAsync();\n          var tempDbPath = `${FileSystem.cacheDirectory}${databaseName}_temp.db`;\n          yield FileSystem.copyAsync({\n            from: dbAsset.localUri,\n            to: tempDbPath\n          });\n          var sourceDb = SQLite.openDatabase(`${databaseName}_temp.db`);\n          if (progressCallback) {\n            progressCallback({\n              status: 'counting',\n              message: 'Counting records to import...',\n              progress: 0.2\n            });\n          }\n          var foodCount = yield this._executeQuery(sourceDb, 'SELECT COUNT(*) as count FROM foods');\n          var totalFoods = foodCount.rows.item(0).count;\n          if (progressCallback) {\n            progressCallback({\n              status: 'importing',\n              message: `Importing ${totalFoods} foods...`,\n              progress: 0.3,\n              totalFoods: totalFoods\n            });\n          }\n          var foods = yield this._executeQuery(sourceDb, 'SELECT * FROM foods');\n          for (var i = 0; i < foods.rows.length; i++) {\n            var food = foods.rows.item(i);\n            try {\n              var foodData = {\n                name: food.name,\n                description: food.description || food.name,\n                brand_name: food.brand_name || '',\n                serving_size: food.serving_size || 100,\n                serving_unit: food.serving_unit || 'g',\n                barcode: food.barcode || '',\n                is_custom: 0,\n                source: food.source || '',\n                source_id: food.source_id || ''\n              };\n              var result = yield dbManager.executeQuery(`INSERT INTO Food (name, description, brand_name, serving_size, serving_unit, barcode, is_custom, source, source_id)\n             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, [foodData.name, foodData.description, foodData.brand_name, foodData.serving_size, foodData.serving_unit, foodData.barcode, foodData.is_custom, foodData.source, foodData.source_id]);\n              var foodId = result.insertId;\n              var nutrients = yield this._executeQuery(sourceDb, 'SELECT * FROM nutrients WHERE food_id = ?', [food.id]);\n              for (var j = 0; j < nutrients.rows.length; j++) {\n                var nutrient = nutrients.rows.item(j);\n                yield dbManager.executeQuery(`INSERT INTO Nutrient (food_id, name, amount, unit, type)\n               VALUES (?, ?, ?, ?, ?)`, [foodId, nutrient.name, nutrient.amount, nutrient.unit, nutrient.type]);\n                stats.nutrients++;\n              }\n              var ingredients = yield this._executeQuery(sourceDb, 'SELECT * FROM ingredients WHERE food_id = ?', [food.id]);\n              for (var _j = 0; _j < ingredients.rows.length; _j++) {\n                var ingredient = ingredients.rows.item(_j);\n                yield dbManager.executeQuery(`INSERT INTO Ingredient (food_id, name, amount, unit)\n               VALUES (?, ?, ?, ?)`, [foodId, ingredient.name, ingredient.amount || 0, ingredient.unit || '']);\n                stats.ingredients++;\n              }\n              var allergens = yield this._executeQuery(sourceDb, 'SELECT * FROM allergens WHERE food_id = ?', [food.id]);\n              for (var _j2 = 0; _j2 < allergens.rows.length; _j2++) {\n                var allergen = allergens.rows.item(_j2);\n                yield dbManager.executeQuery(`INSERT INTO Allergen (food_id, name)\n               VALUES (?, ?)`, [foodId, allergen.name]);\n                stats.allergens++;\n              }\n              stats.foods++;\n              if (progressCallback) {\n                progressCallback({\n                  status: 'importing',\n                  message: `Imported ${stats.foods} of ${totalFoods} foods...`,\n                  progress: 0.3 + 0.6 * (stats.foods / totalFoods),\n                  importedFoods: stats.foods,\n                  totalFoods: totalFoods\n                });\n              }\n            } catch (error) {\n              console.error(`Error importing food ${food.id}:`, error);\n              stats.errors++;\n            }\n          }\n          sourceDb._db.close();\n          yield FileSystem.deleteAsync(tempDbPath, {\n            idempotent: true\n          });\n          if (progressCallback) {\n            progressCallback({\n              status: 'complete',\n              message: 'Import complete!',\n              progress: 1,\n              stats: stats\n            });\n          }\n          return stats;\n        } catch (error) {\n          console.error('Error importing food database:', error);\n          if (progressCallback) {\n            progressCallback({\n              status: 'error',\n              message: `Error: ${error.message}`,\n              progress: 0,\n              error: error\n            });\n          }\n          throw error;\n        }\n      });\n      function importFoodDatabase(_x) {\n        return _importFoodDatabase.apply(this, arguments);\n      }\n      return importFoodDatabase;\n    }())\n  }, {\n    key: \"_executeQuery\",\n    value: function _executeQuery(db, sql) {\n      var params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      return new Promise(function (resolve, reject) {\n        db.transaction(function (tx) {\n          tx.executeSql(sql, params, function (_, result) {\n            resolve(result);\n          }, function (_, error) {\n            console.error('SQL Error:', error);\n            reject(error);\n            return false;\n          });\n        });\n      });\n    }\n  }]);\n}();\nexport default new FoodDatabaseImporter();", "map": {"version": 3, "names": ["SQLite", "FileSystem", "<PERSON><PERSON>", "db<PERSON><PERSON><PERSON>", "FoodDatabaseImporter", "_classCallCheck", "_createClass", "key", "value", "_importFoodDatabase", "_asyncToGenerator", "databaseName", "progressCallback", "arguments", "length", "undefined", "stats", "foods", "nutrients", "ingredients", "allergens", "errors", "status", "message", "progress", "dbAsset", "fromModule", "require", "downloadAsync", "tempDbPath", "cacheDirectory", "copyAsync", "from", "localUri", "to", "sourceDb", "openDatabase", "foodCount", "_executeQuery", "totalFoods", "rows", "item", "count", "i", "food", "foodData", "name", "description", "brand_name", "serving_size", "serving_unit", "barcode", "is_custom", "source", "source_id", "result", "execute<PERSON>uery", "foodId", "insertId", "id", "j", "nutrient", "amount", "unit", "type", "ingredient", "allergen", "importedFoods", "error", "console", "_db", "close", "deleteAsync", "idempotent", "importFoodDatabase", "_x", "apply", "db", "sql", "params", "Promise", "resolve", "reject", "transaction", "tx", "executeSql", "_"], "sources": ["/workspaces/Z<PERSON>eni-<PERSON>/app/src/utils/FoodDatabaseImporter.js"], "sourcesContent": ["/**\n * Food Database Importer for Znü<PERSON>Zähler\n * \n * This utility imports food data from pre-built SQLite databases\n * into the app's main database.\n */\n\nimport * as SQLite from 'expo-sqlite';\nimport * as FileSystem from 'expo-file-system';\nimport { Asset } from 'expo-asset';\nimport dbManager from '../database/DatabaseManager';\n\n/**\n * Food Database Importer class\n */\nclass FoodDatabaseImporter {\n  /**\n   * Import food data from a pre-built SQLite database\n   * @param {string} databaseName - Name of the database file (without extension)\n   * @param {Function} progressCallback - Callback function for progress updates\n   * @returns {Promise<Object>} - Import statistics\n   */\n  async importFoodDatabase(databaseName, progressCallback = null) {\n    try {\n      // Statistics object\n      const stats = {\n        foods: 0,\n        nutrients: 0,\n        ingredients: 0,\n        allergens: 0,\n        errors: 0\n      };\n      \n      // Update progress\n      if (progressCallback) {\n        progressCallback({\n          status: 'preparing',\n          message: 'Preparing to import food database...',\n          progress: 0.1\n        });\n      }\n      \n      // Copy the database file from assets to a temporary location\n      const dbAsset = Asset.fromModule(require(`../assets/databases/${databaseName}.sqlite`));\n      await dbAsset.downloadAsync();\n      \n      const tempDbPath = `${FileSystem.cacheDirectory}${databaseName}_temp.db`;\n      await FileSystem.copyAsync({\n        from: dbAsset.localUri,\n        to: tempDbPath\n      });\n      \n      // Open the source database\n      const sourceDb = SQLite.openDatabase(`${databaseName}_temp.db`);\n      \n      // Update progress\n      if (progressCallback) {\n        progressCallback({\n          status: 'counting',\n          message: 'Counting records to import...',\n          progress: 0.2\n        });\n      }\n      \n      // Count the number of foods to import\n      const foodCount = await this._executeQuery(sourceDb, 'SELECT COUNT(*) as count FROM foods');\n      const totalFoods = foodCount.rows.item(0).count;\n      \n      // Update progress\n      if (progressCallback) {\n        progressCallback({\n          status: 'importing',\n          message: `Importing ${totalFoods} foods...`,\n          progress: 0.3,\n          totalFoods\n        });\n      }\n      \n      // Get all foods from the source database\n      const foods = await this._executeQuery(sourceDb, 'SELECT * FROM foods');\n      \n      // Import each food\n      for (let i = 0; i < foods.rows.length; i++) {\n        const food = foods.rows.item(i);\n        \n        try {\n          // Create food object\n          const foodData = {\n            name: food.name,\n            description: food.description || food.name,\n            brand_name: food.brand_name || '',\n            serving_size: food.serving_size || 100,\n            serving_unit: food.serving_unit || 'g',\n            barcode: food.barcode || '',\n            is_custom: 0,\n            source: food.source || '',\n            source_id: food.source_id || ''\n          };\n          \n          // Save food to database\n          const result = await dbManager.executeQuery(\n            `INSERT INTO Food (name, description, brand_name, serving_size, serving_unit, barcode, is_custom, source, source_id)\n             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,\n            [\n              foodData.name,\n              foodData.description,\n              foodData.brand_name,\n              foodData.serving_size,\n              foodData.serving_unit,\n              foodData.barcode,\n              foodData.is_custom,\n              foodData.source,\n              foodData.source_id\n            ]\n          );\n          \n          const foodId = result.insertId;\n          \n          // Get nutrients for this food\n          const nutrients = await this._executeQuery(\n            sourceDb,\n            'SELECT * FROM nutrients WHERE food_id = ?',\n            [food.id]\n          );\n          \n          // Import nutrients\n          for (let j = 0; j < nutrients.rows.length; j++) {\n            const nutrient = nutrients.rows.item(j);\n            \n            await dbManager.executeQuery(\n              `INSERT INTO Nutrient (food_id, name, amount, unit, type)\n               VALUES (?, ?, ?, ?, ?)`,\n              [\n                foodId,\n                nutrient.name,\n                nutrient.amount,\n                nutrient.unit,\n                nutrient.type\n              ]\n            );\n            \n            stats.nutrients++;\n          }\n          \n          // Get ingredients for this food\n          const ingredients = await this._executeQuery(\n            sourceDb,\n            'SELECT * FROM ingredients WHERE food_id = ?',\n            [food.id]\n          );\n          \n          // Import ingredients\n          for (let j = 0; j < ingredients.rows.length; j++) {\n            const ingredient = ingredients.rows.item(j);\n            \n            await dbManager.executeQuery(\n              `INSERT INTO Ingredient (food_id, name, amount, unit)\n               VALUES (?, ?, ?, ?)`,\n              [\n                foodId,\n                ingredient.name,\n                ingredient.amount || 0,\n                ingredient.unit || ''\n              ]\n            );\n            \n            stats.ingredients++;\n          }\n          \n          // Get allergens for this food\n          const allergens = await this._executeQuery(\n            sourceDb,\n            'SELECT * FROM allergens WHERE food_id = ?',\n            [food.id]\n          );\n          \n          // Import allergens\n          for (let j = 0; j < allergens.rows.length; j++) {\n            const allergen = allergens.rows.item(j);\n            \n            await dbManager.executeQuery(\n              `INSERT INTO Allergen (food_id, name)\n               VALUES (?, ?)`,\n              [\n                foodId,\n                allergen.name\n              ]\n            );\n            \n            stats.allergens++;\n          }\n          \n          stats.foods++;\n          \n          // Update progress\n          if (progressCallback) {\n            progressCallback({\n              status: 'importing',\n              message: `Imported ${stats.foods} of ${totalFoods} foods...`,\n              progress: 0.3 + (0.6 * (stats.foods / totalFoods)),\n              importedFoods: stats.foods,\n              totalFoods\n            });\n          }\n        } catch (error) {\n          console.error(`Error importing food ${food.id}:`, error);\n          stats.errors++;\n        }\n      }\n      \n      // Close the source database\n      sourceDb._db.close();\n      \n      // Delete the temporary database file\n      await FileSystem.deleteAsync(tempDbPath, { idempotent: true });\n      \n      // Update progress\n      if (progressCallback) {\n        progressCallback({\n          status: 'complete',\n          message: 'Import complete!',\n          progress: 1,\n          stats\n        });\n      }\n      \n      return stats;\n    } catch (error) {\n      console.error('Error importing food database:', error);\n      \n      // Update progress with error\n      if (progressCallback) {\n        progressCallback({\n          status: 'error',\n          message: `Error: ${error.message}`,\n          progress: 0,\n          error\n        });\n      }\n      \n      throw error;\n    }\n  }\n  \n  /**\n   * Execute a SQL query on a database\n   * @param {Object} db - SQLite database\n   * @param {string} sql - SQL query\n   * @param {Array} params - Query parameters\n   * @returns {Promise<Object>} - Query result\n   * @private\n   */\n  _executeQuery(db, sql, params = []) {\n    return new Promise((resolve, reject) => {\n      db.transaction(tx => {\n        tx.executeSql(\n          sql,\n          params,\n          (_, result) => {\n            resolve(result);\n          },\n          (_, error) => {\n            console.error('SQL Error:', error);\n            reject(error);\n            return false;\n          }\n        );\n      });\n    });\n  }\n}\n\nexport default new FoodDatabaseImporter();\n"], "mappings": ";;;AAOA,OAAO,KAAKA,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,YAAY;AAClC,OAAOC,SAAS;AAAoC,IAK9CC,oBAAoB;EAAA,SAAAA,qBAAA;IAAAC,eAAA,OAAAD,oBAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,oBAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAC,mBAAA,GAAAC,iBAAA,CAOxB,WAAyBC,YAAY,EAA2B;QAAA,IAAzBC,gBAAgB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;QAC5D,IAAI;UAEF,IAAMG,KAAK,GAAG;YACZC,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE,CAAC;YACZC,WAAW,EAAE,CAAC;YACdC,SAAS,EAAE,CAAC;YACZC,MAAM,EAAE;UACV,CAAC;UAGD,IAAIT,gBAAgB,EAAE;YACpBA,gBAAgB,CAAC;cACfU,MAAM,EAAE,WAAW;cACnBC,OAAO,EAAE,sCAAsC;cAC/CC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UAGA,IAAMC,OAAO,GAAGvB,KAAK,CAACwB,UAAU,CAACC,OAAO,CAAC,uBAAuBhB,YAAY,SAAS,CAAC,CAAC;UACvF,MAAMc,OAAO,CAACG,aAAa,CAAC,CAAC;UAE7B,IAAMC,UAAU,GAAG,GAAG5B,UAAU,CAAC6B,cAAc,GAAGnB,YAAY,UAAU;UACxE,MAAMV,UAAU,CAAC8B,SAAS,CAAC;YACzBC,IAAI,EAAEP,OAAO,CAACQ,QAAQ;YACtBC,EAAE,EAAEL;UACN,CAAC,CAAC;UAGF,IAAMM,QAAQ,GAAGnC,MAAM,CAACoC,YAAY,CAAC,GAAGzB,YAAY,UAAU,CAAC;UAG/D,IAAIC,gBAAgB,EAAE;YACpBA,gBAAgB,CAAC;cACfU,MAAM,EAAE,UAAU;cAClBC,OAAO,EAAE,+BAA+B;cACxCC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UAGA,IAAMa,SAAS,SAAS,IAAI,CAACC,aAAa,CAACH,QAAQ,EAAE,qCAAqC,CAAC;UAC3F,IAAMI,UAAU,GAAGF,SAAS,CAACG,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK;UAG/C,IAAI9B,gBAAgB,EAAE;YACpBA,gBAAgB,CAAC;cACfU,MAAM,EAAE,WAAW;cACnBC,OAAO,EAAE,aAAagB,UAAU,WAAW;cAC3Cf,QAAQ,EAAE,GAAG;cACbe,UAAU,EAAVA;YACF,CAAC,CAAC;UACJ;UAGA,IAAMtB,KAAK,SAAS,IAAI,CAACqB,aAAa,CAACH,QAAQ,EAAE,qBAAqB,CAAC;UAGvE,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,KAAK,CAACuB,IAAI,CAAC1B,MAAM,EAAE6B,CAAC,EAAE,EAAE;YAC1C,IAAMC,IAAI,GAAG3B,KAAK,CAACuB,IAAI,CAACC,IAAI,CAACE,CAAC,CAAC;YAE/B,IAAI;cAEF,IAAME,QAAQ,GAAG;gBACfC,IAAI,EAAEF,IAAI,CAACE,IAAI;gBACfC,WAAW,EAAEH,IAAI,CAACG,WAAW,IAAIH,IAAI,CAACE,IAAI;gBAC1CE,UAAU,EAAEJ,IAAI,CAACI,UAAU,IAAI,EAAE;gBACjCC,YAAY,EAAEL,IAAI,CAACK,YAAY,IAAI,GAAG;gBACtCC,YAAY,EAAEN,IAAI,CAACM,YAAY,IAAI,GAAG;gBACtCC,OAAO,EAAEP,IAAI,CAACO,OAAO,IAAI,EAAE;gBAC3BC,SAAS,EAAE,CAAC;gBACZC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,EAAE;gBACzBC,SAAS,EAAEV,IAAI,CAACU,SAAS,IAAI;cAC/B,CAAC;cAGD,IAAMC,MAAM,SAASpD,SAAS,CAACqD,YAAY,CACzC;AACZ,gDAAgD,EACpC,CACEX,QAAQ,CAACC,IAAI,EACbD,QAAQ,CAACE,WAAW,EACpBF,QAAQ,CAACG,UAAU,EACnBH,QAAQ,CAACI,YAAY,EACrBJ,QAAQ,CAACK,YAAY,EACrBL,QAAQ,CAACM,OAAO,EAChBN,QAAQ,CAACO,SAAS,EAClBP,QAAQ,CAACQ,MAAM,EACfR,QAAQ,CAACS,SAAS,CAEtB,CAAC;cAED,IAAMG,MAAM,GAAGF,MAAM,CAACG,QAAQ;cAG9B,IAAMxC,SAAS,SAAS,IAAI,CAACoB,aAAa,CACxCH,QAAQ,EACR,2CAA2C,EAC3C,CAACS,IAAI,CAACe,EAAE,CACV,CAAC;cAGD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,SAAS,CAACsB,IAAI,CAAC1B,MAAM,EAAE8C,CAAC,EAAE,EAAE;gBAC9C,IAAMC,QAAQ,GAAG3C,SAAS,CAACsB,IAAI,CAACC,IAAI,CAACmB,CAAC,CAAC;gBAEvC,MAAMzD,SAAS,CAACqD,YAAY,CAC1B;AACd,sCAAsC,EACxB,CACEC,MAAM,EACNI,QAAQ,CAACf,IAAI,EACbe,QAAQ,CAACC,MAAM,EACfD,QAAQ,CAACE,IAAI,EACbF,QAAQ,CAACG,IAAI,CAEjB,CAAC;gBAEDhD,KAAK,CAACE,SAAS,EAAE;cACnB;cAGA,IAAMC,WAAW,SAAS,IAAI,CAACmB,aAAa,CAC1CH,QAAQ,EACR,6CAA6C,EAC7C,CAACS,IAAI,CAACe,EAAE,CACV,CAAC;cAGD,KAAK,IAAIC,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGzC,WAAW,CAACqB,IAAI,CAAC1B,MAAM,EAAE8C,EAAC,EAAE,EAAE;gBAChD,IAAMK,UAAU,GAAG9C,WAAW,CAACqB,IAAI,CAACC,IAAI,CAACmB,EAAC,CAAC;gBAE3C,MAAMzD,SAAS,CAACqD,YAAY,CAC1B;AACd,mCAAmC,EACrB,CACEC,MAAM,EACNQ,UAAU,CAACnB,IAAI,EACfmB,UAAU,CAACH,MAAM,IAAI,CAAC,EACtBG,UAAU,CAACF,IAAI,IAAI,EAAE,CAEzB,CAAC;gBAED/C,KAAK,CAACG,WAAW,EAAE;cACrB;cAGA,IAAMC,SAAS,SAAS,IAAI,CAACkB,aAAa,CACxCH,QAAQ,EACR,2CAA2C,EAC3C,CAACS,IAAI,CAACe,EAAE,CACV,CAAC;cAGD,KAAK,IAAIC,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGxC,SAAS,CAACoB,IAAI,CAAC1B,MAAM,EAAE8C,GAAC,EAAE,EAAE;gBAC9C,IAAMM,QAAQ,GAAG9C,SAAS,CAACoB,IAAI,CAACC,IAAI,CAACmB,GAAC,CAAC;gBAEvC,MAAMzD,SAAS,CAACqD,YAAY,CAC1B;AACd,6BAA6B,EACf,CACEC,MAAM,EACNS,QAAQ,CAACpB,IAAI,CAEjB,CAAC;gBAED9B,KAAK,CAACI,SAAS,EAAE;cACnB;cAEAJ,KAAK,CAACC,KAAK,EAAE;cAGb,IAAIL,gBAAgB,EAAE;gBACpBA,gBAAgB,CAAC;kBACfU,MAAM,EAAE,WAAW;kBACnBC,OAAO,EAAE,YAAYP,KAAK,CAACC,KAAK,OAAOsB,UAAU,WAAW;kBAC5Df,QAAQ,EAAE,GAAG,GAAI,GAAG,IAAIR,KAAK,CAACC,KAAK,GAAGsB,UAAU,CAAE;kBAClD4B,aAAa,EAAEnD,KAAK,CAACC,KAAK;kBAC1BsB,UAAU,EAAVA;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;cACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBxB,IAAI,CAACe,EAAE,GAAG,EAAES,KAAK,CAAC;cACxDpD,KAAK,CAACK,MAAM,EAAE;YAChB;UACF;UAGAc,QAAQ,CAACmC,GAAG,CAACC,KAAK,CAAC,CAAC;UAGpB,MAAMtE,UAAU,CAACuE,WAAW,CAAC3C,UAAU,EAAE;YAAE4C,UAAU,EAAE;UAAK,CAAC,CAAC;UAG9D,IAAI7D,gBAAgB,EAAE;YACpBA,gBAAgB,CAAC;cACfU,MAAM,EAAE,UAAU;cAClBC,OAAO,EAAE,kBAAkB;cAC3BC,QAAQ,EAAE,CAAC;cACXR,KAAK,EAALA;YACF,CAAC,CAAC;UACJ;UAEA,OAAOA,KAAK;QACd,CAAC,CAAC,OAAOoD,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAGtD,IAAIxD,gBAAgB,EAAE;YACpBA,gBAAgB,CAAC;cACfU,MAAM,EAAE,OAAO;cACfC,OAAO,EAAE,UAAU6C,KAAK,CAAC7C,OAAO,EAAE;cAClCC,QAAQ,EAAE,CAAC;cACX4C,KAAK,EAALA;YACF,CAAC,CAAC;UACJ;UAEA,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SA5NKM,kBAAkBA,CAAAC,EAAA;QAAA,OAAAlE,mBAAA,CAAAmE,KAAA,OAAA/D,SAAA;MAAA;MAAA,OAAlB6D,kBAAkB;IAAA;EAAA;IAAAnE,GAAA;IAAAC,KAAA,EAsOxB,SAAA8B,aAAaA,CAACuC,EAAE,EAAEC,GAAG,EAAe;MAAA,IAAbC,MAAM,GAAAlE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MAChC,OAAO,IAAImE,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtCL,EAAE,CAACM,WAAW,CAAC,UAAAC,EAAE,EAAI;UACnBA,EAAE,CAACC,UAAU,CACXP,GAAG,EACHC,MAAM,EACN,UAACO,CAAC,EAAE/B,MAAM,EAAK;YACb0B,OAAO,CAAC1B,MAAM,CAAC;UACjB,CAAC,EACD,UAAC+B,CAAC,EAAElB,KAAK,EAAK;YACZC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;YAClCc,MAAM,CAACd,KAAK,CAAC;YACb,OAAO,KAAK;UACd,CACF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAC;AAAA;AAGH,eAAe,IAAIhE,oBAAoB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}