{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport Animation from \"./Animation\";\nimport { shouldUseNativeDriver } from \"../NativeAnimatedHelper\";\nvar DecayAnimation = function (_Animation) {\n  function DecayAnimation(config) {\n    var _this;\n    _classCallCheck(this, DecayAnimation);\n    var _config$deceleration, _config$isInteraction, _config$iterations;\n    _this = _callSuper(this, DecayAnimation);\n    _this._deceleration = (_config$deceleration = config.deceleration) !== null && _config$deceleration !== void 0 ? _config$deceleration : 0.998;\n    _this._velocity = config.velocity;\n    _this._useNativeDriver = shouldUseNativeDriver(config);\n    _this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !_this._useNativeDriver;\n    _this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n    return _this;\n  }\n  _inherits(DecayAnimation, _Animation);\n  return _createClass(DecayAnimation, [{\n    key: \"__getNativeAnimationConfig\",\n    value: function __getNativeAnimationConfig() {\n      return {\n        type: 'decay',\n        deceleration: this._deceleration,\n        velocity: this._velocity,\n        iterations: this.__iterations\n      };\n    }\n  }, {\n    key: \"start\",\n    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n      this.__active = true;\n      this._lastValue = fromValue;\n      this._fromValue = fromValue;\n      this._onUpdate = onUpdate;\n      this.__onEnd = onEnd;\n      this._startTime = Date.now();\n      if (this._useNativeDriver) {\n        this.__startNativeAnimation(animatedValue);\n      } else {\n        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n      }\n    }\n  }, {\n    key: \"onUpdate\",\n    value: function onUpdate() {\n      var now = Date.now();\n      var value = this._fromValue + this._velocity / (1 - this._deceleration) * (1 - Math.exp(-(1 - this._deceleration) * (now - this._startTime)));\n      this._onUpdate(value);\n      if (Math.abs(this._lastValue - value) < 0.1) {\n        this.__debouncedOnEnd({\n          finished: true\n        });\n        return;\n      }\n      this._lastValue = value;\n      if (this.__active) {\n        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n      }\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      _superPropGet(DecayAnimation, \"stop\", this, 3)([]);\n      this.__active = false;\n      global.cancelAnimationFrame(this._animationFrame);\n      this.__debouncedOnEnd({\n        finished: false\n      });\n    }\n  }]);\n}(Animation);\nexport default DecayAnimation;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "Animation", "shouldUseNativeDriver", "DecayAnimation", "_Animation", "config", "_this", "_config$deceleration", "_config$isInteraction", "_config$iterations", "_deceleration", "deceleration", "_velocity", "velocity", "_useNativeDriver", "__isInteraction", "isInteraction", "__iterations", "iterations", "key", "value", "__getNativeAnimationConfig", "type", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "__active", "_lastValue", "_fromValue", "_onUpdate", "__onEnd", "_startTime", "Date", "now", "__startNativeAnimation", "_animationFrame", "requestAnimationFrame", "bind", "Math", "exp", "abs", "__debouncedOnEnd", "finished", "stop", "global", "cancelAnimationFrame"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/Animated/animations/DecayAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport Animation from './Animation';\nimport { shouldUseNativeDriver } from '../NativeAnimatedHelper';\nclass DecayAnimation extends Animation {\n  constructor(config) {\n    var _config$deceleration, _config$isInteraction, _config$iterations;\n    super();\n    this._deceleration = (_config$deceleration = config.deceleration) !== null && _config$deceleration !== void 0 ? _config$deceleration : 0.998;\n    this._velocity = config.velocity;\n    this._useNativeDriver = shouldUseNativeDriver(config);\n    this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !this._useNativeDriver;\n    this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n  }\n  __getNativeAnimationConfig() {\n    return {\n      type: 'decay',\n      deceleration: this._deceleration,\n      velocity: this._velocity,\n      iterations: this.__iterations\n    };\n  }\n  start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n    this.__active = true;\n    this._lastValue = fromValue;\n    this._fromValue = fromValue;\n    this._onUpdate = onUpdate;\n    this.__onEnd = onEnd;\n    this._startTime = Date.now();\n    if (this._useNativeDriver) {\n      this.__startNativeAnimation(animatedValue);\n    } else {\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n  onUpdate() {\n    var now = Date.now();\n    var value = this._fromValue + this._velocity / (1 - this._deceleration) * (1 - Math.exp(-(1 - this._deceleration) * (now - this._startTime)));\n    this._onUpdate(value);\n    if (Math.abs(this._lastValue - value) < 0.1) {\n      this.__debouncedOnEnd({\n        finished: true\n      });\n      return;\n    }\n    this._lastValue = value;\n    if (this.__active) {\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n  stop() {\n    super.stop();\n    this.__active = false;\n    global.cancelAnimationFrame(this._animationFrame);\n    this.__debouncedOnEnd({\n      finished: false\n    });\n  }\n}\nexport default DecayAnimation;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,SAAS;AAChB,SAASC,qBAAqB;AAAkC,IAC1DC,cAAc,aAAAC,UAAA;EAClB,SAAAD,eAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA3B,eAAA,OAAAwB,cAAA;IAClB,IAAII,oBAAoB,EAAEC,qBAAqB,EAAEC,kBAAkB;IACnEH,KAAA,GAAArB,UAAA,OAAAkB,cAAA;IACAG,KAAA,CAAKI,aAAa,GAAG,CAACH,oBAAoB,GAAGF,MAAM,CAACM,YAAY,MAAM,IAAI,IAAIJ,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAG,KAAK;IAC5ID,KAAA,CAAKM,SAAS,GAAGP,MAAM,CAACQ,QAAQ;IAChCP,KAAA,CAAKQ,gBAAgB,GAAGZ,qBAAqB,CAACG,MAAM,CAAC;IACrDC,KAAA,CAAKS,eAAe,GAAG,CAACP,qBAAqB,GAAGH,MAAM,CAACW,aAAa,MAAM,IAAI,IAAIR,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAACF,KAAA,CAAKQ,gBAAgB;IACnKR,KAAA,CAAKW,YAAY,GAAG,CAACR,kBAAkB,GAAGJ,MAAM,CAACa,UAAU,MAAM,IAAI,IAAIT,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAAC,OAAAH,KAAA;EAClI;EAACtB,SAAA,CAAAmB,cAAA,EAAAC,UAAA;EAAA,OAAAxB,YAAA,CAAAuB,cAAA;IAAAgB,GAAA;IAAAC,KAAA,EACD,SAAAC,0BAA0BA,CAAA,EAAG;MAC3B,OAAO;QACLC,IAAI,EAAE,OAAO;QACbX,YAAY,EAAE,IAAI,CAACD,aAAa;QAChCG,QAAQ,EAAE,IAAI,CAACD,SAAS;QACxBM,UAAU,EAAE,IAAI,CAACD;MACnB,CAAC;IACH;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAAG,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAE;MAClE,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,UAAU,GAAGN,SAAS;MAC3B,IAAI,CAACO,UAAU,GAAGP,SAAS;MAC3B,IAAI,CAACQ,SAAS,GAAGP,QAAQ;MACzB,IAAI,CAACQ,OAAO,GAAGP,KAAK;MACpB,IAAI,CAACQ,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACtB,gBAAgB,EAAE;QACzB,IAAI,CAACuB,sBAAsB,CAACT,aAAa,CAAC;MAC5C,CAAC,MAAM;QACL,IAAI,CAACU,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAACd,QAAQ,CAACe,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAArB,GAAA;IAAAC,KAAA,EACD,SAAAK,QAAQA,CAAA,EAAG;MACT,IAAIW,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIhB,KAAK,GAAG,IAAI,CAACW,UAAU,GAAG,IAAI,CAACnB,SAAS,IAAI,CAAC,GAAG,IAAI,CAACF,aAAa,CAAC,IAAI,CAAC,GAAG+B,IAAI,CAACC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAChC,aAAa,CAAC,IAAI0B,GAAG,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC,CAAC;MAC7I,IAAI,CAACF,SAAS,CAACZ,KAAK,CAAC;MACrB,IAAIqB,IAAI,CAACE,GAAG,CAAC,IAAI,CAACb,UAAU,GAAGV,KAAK,CAAC,GAAG,GAAG,EAAE;QAC3C,IAAI,CAACwB,gBAAgB,CAAC;UACpBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MACF;MACA,IAAI,CAACf,UAAU,GAAGV,KAAK;MACvB,IAAI,IAAI,CAACS,QAAQ,EAAE;QACjB,IAAI,CAACS,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAACd,QAAQ,CAACe,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAArB,GAAA;IAAAC,KAAA,EACD,SAAA0B,IAAIA,CAAA,EAAG;MACLhD,aAAA,CAAAK,cAAA;MACA,IAAI,CAAC0B,QAAQ,GAAG,KAAK;MACrBkB,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACV,eAAe,CAAC;MACjD,IAAI,CAACM,gBAAgB,CAAC;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EAAC;AAAA,EArD0B5C,SAAS;AAuDtC,eAAeE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}