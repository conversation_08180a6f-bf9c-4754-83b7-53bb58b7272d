{"ast": null, "code": "import * as React from 'react';\nimport View from \"../View\";\nimport createElement from \"../createElement\";\nimport StyleSheet from \"../StyleSheet\";\nimport UIManager from \"../UIManager\";\nimport canUseDOM from \"../../modules/canUseDom\";\nvar FocusBracket = function FocusBracket() {\n  return createElement('div', {\n    role: 'none',\n    tabIndex: 0,\n    style: styles.focusBracket\n  });\n};\nfunction attemptFocus(element) {\n  if (!canUseDOM) {\n    return false;\n  }\n  try {\n    element.focus();\n  } catch (e) {}\n  return document.activeElement === element;\n}\nfunction focusFirstDescendant(element) {\n  for (var i = 0; i < element.childNodes.length; i++) {\n    var child = element.childNodes[i];\n    if (attemptFocus(child) || focusFirstDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction focusLastDescendant(element) {\n  for (var i = element.childNodes.length - 1; i >= 0; i--) {\n    var child = element.childNodes[i];\n    if (attemptFocus(child) || focusLastDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n}\nvar ModalFocusTrap = function ModalFocusTrap(_ref) {\n  var active = _ref.active,\n    children = _ref.children;\n  var trapElementRef = React.useRef();\n  var focusRef = React.useRef({\n    trapFocusInProgress: false,\n    lastFocusedElement: null\n  });\n  React.useEffect(function () {\n    if (canUseDOM) {\n      var trapFocus = function trapFocus() {\n        if (trapElementRef.current == null || focusRef.current.trapFocusInProgress || !active) {\n          return;\n        }\n        try {\n          focusRef.current.trapFocusInProgress = true;\n          if (document.activeElement instanceof Node && !trapElementRef.current.contains(document.activeElement)) {\n            var hasFocused = focusFirstDescendant(trapElementRef.current);\n            if (focusRef.current.lastFocusedElement === document.activeElement) {\n              hasFocused = focusLastDescendant(trapElementRef.current);\n            }\n            if (!hasFocused && trapElementRef.current != null && document.activeElement) {\n              UIManager.focus(trapElementRef.current);\n            }\n          }\n        } finally {\n          focusRef.current.trapFocusInProgress = false;\n        }\n        focusRef.current.lastFocusedElement = document.activeElement;\n      };\n      trapFocus();\n      document.addEventListener('focus', trapFocus, true);\n      return function () {\n        return document.removeEventListener('focus', trapFocus, true);\n      };\n    }\n  }, [active]);\n  React.useEffect(function () {\n    if (canUseDOM) {\n      var lastFocusedElementOutsideTrap = document.activeElement;\n      return function () {\n        if (lastFocusedElementOutsideTrap && document.contains(lastFocusedElementOutsideTrap)) {\n          UIManager.focus(lastFocusedElementOutsideTrap);\n        }\n      };\n    }\n  }, []);\n  return React.createElement(React.Fragment, null, React.createElement(FocusBracket, null), React.createElement(View, {\n    ref: trapElementRef\n  }, children), React.createElement(FocusBracket, null));\n};\nexport default ModalFocusTrap;\nvar styles = StyleSheet.create({\n  focusBracket: {\n    outlineStyle: 'none'\n  }\n});", "map": {"version": 3, "names": ["React", "View", "createElement", "StyleSheet", "UIManager", "canUseDOM", "FocusBracket", "role", "tabIndex", "style", "styles", "focusBracket", "attemptFocus", "element", "focus", "e", "document", "activeElement", "focusFirstDescendant", "i", "childNodes", "length", "child", "focusLastDescendant", "ModalFocusTrap", "_ref", "active", "children", "trapElementRef", "useRef", "focusRef", "trapFocusInProgress", "lastFocusedElement", "useEffect", "trapFocus", "current", "Node", "contains", "hasFocused", "addEventListener", "removeEventListener", "lastFocusedElementOutsideTrap", "Fragment", "ref", "create", "outlineStyle"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Modal/ModalFocusTrap.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport View from '../View';\nimport createElement from '../createElement';\nimport StyleSheet from '../StyleSheet';\nimport UIManager from '../UIManager';\nimport canUseDOM from '../../modules/canUseDom';\n\n/**\n * This Component is used to \"wrap\" the modal we're opening\n * so that changing focus via tab will never leave the document.\n *\n * This allows us to properly trap the focus within a modal\n * even if the modal is at the start or end of a document.\n */\n\nvar FocusBracket = () => {\n  return createElement('div', {\n    role: 'none',\n    tabIndex: 0,\n    style: styles.focusBracket\n  });\n};\nfunction attemptFocus(element) {\n  if (!canUseDOM) {\n    return false;\n  }\n  try {\n    element.focus();\n  } catch (e) {\n    // Do nothing\n  }\n  return document.activeElement === element;\n}\nfunction focusFirstDescendant(element) {\n  for (var i = 0; i < element.childNodes.length; i++) {\n    var child = element.childNodes[i];\n    if (attemptFocus(child) || focusFirstDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction focusLastDescendant(element) {\n  for (var i = element.childNodes.length - 1; i >= 0; i--) {\n    var child = element.childNodes[i];\n    if (attemptFocus(child) || focusLastDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n}\nvar ModalFocusTrap = _ref => {\n  var active = _ref.active,\n    children = _ref.children;\n  var trapElementRef = React.useRef();\n  var focusRef = React.useRef({\n    trapFocusInProgress: false,\n    lastFocusedElement: null\n  });\n  React.useEffect(() => {\n    if (canUseDOM) {\n      var trapFocus = () => {\n        // We should not trap focus if:\n        // - The modal hasn't fully initialized with an HTMLElement ref\n        // - Focus is already in the process of being trapped (e.g., we're refocusing)\n        // - isTrapActive prop being falsey tells us to do nothing\n        if (trapElementRef.current == null || focusRef.current.trapFocusInProgress || !active) {\n          return;\n        }\n        try {\n          focusRef.current.trapFocusInProgress = true;\n          if (document.activeElement instanceof Node && !trapElementRef.current.contains(document.activeElement)) {\n            // To handle keyboard focusing we can make an assumption here.\n            // If you're tabbing through the focusable elements, the previously\n            // active element will either be the first or the last.\n            // If the previously selected element is the \"first\" descendant\n            // and we're leaving it - this means that we should be looping\n            // around to the other side of the modal.\n            var hasFocused = focusFirstDescendant(trapElementRef.current);\n            if (focusRef.current.lastFocusedElement === document.activeElement) {\n              hasFocused = focusLastDescendant(trapElementRef.current);\n            }\n            // If we couldn't focus a new element then we need to focus onto the trap target\n            if (!hasFocused && trapElementRef.current != null && document.activeElement) {\n              UIManager.focus(trapElementRef.current);\n            }\n          }\n        } finally {\n          focusRef.current.trapFocusInProgress = false;\n        }\n        focusRef.current.lastFocusedElement = document.activeElement;\n      };\n\n      // Call the trapFocus callback at least once when this modal has been activated.\n      trapFocus();\n      document.addEventListener('focus', trapFocus, true);\n      return () => document.removeEventListener('focus', trapFocus, true);\n    }\n  }, [active]);\n\n  // To be fully compliant with WCAG we need to refocus element that triggered opening modal\n  // after closing it\n  React.useEffect(function () {\n    if (canUseDOM) {\n      var lastFocusedElementOutsideTrap = document.activeElement;\n      return function () {\n        if (lastFocusedElementOutsideTrap && document.contains(lastFocusedElementOutsideTrap)) {\n          UIManager.focus(lastFocusedElementOutsideTrap);\n        }\n      };\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(FocusBracket, null), /*#__PURE__*/React.createElement(View, {\n    ref: trapElementRef\n  }, children), /*#__PURE__*/React.createElement(FocusBracket, null));\n};\nexport default ModalFocusTrap;\nvar styles = StyleSheet.create({\n  focusBracket: {\n    outlineStyle: 'none'\n  }\n});"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI;AACX,OAAOC,aAAa;AACpB,OAAOC,UAAU;AACjB,OAAOC,SAAS;AAChB,OAAOC,SAAS;AAUhB,IAAIC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EACvB,OAAOJ,aAAa,CAAC,KAAK,EAAE;IAC1BK,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAEC,MAAM,CAACC;EAChB,CAAC,CAAC;AACJ,CAAC;AACD,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAI,CAACR,SAAS,EAAE;IACd,OAAO,KAAK;EACd;EACA,IAAI;IACFQ,OAAO,CAACC,KAAK,CAAC,CAAC;EACjB,CAAC,CAAC,OAAOC,CAAC,EAAE,CAEZ;EACA,OAAOC,QAAQ,CAACC,aAAa,KAAKJ,OAAO;AAC3C;AACA,SAASK,oBAAoBA,CAACL,OAAO,EAAE;EACrC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,OAAO,CAACO,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAClD,IAAIG,KAAK,GAAGT,OAAO,CAACO,UAAU,CAACD,CAAC,CAAC;IACjC,IAAIP,YAAY,CAACU,KAAK,CAAC,IAAIJ,oBAAoB,CAACI,KAAK,CAAC,EAAE;MACtD,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA,SAASC,mBAAmBA,CAACV,OAAO,EAAE;EACpC,KAAK,IAAIM,CAAC,GAAGN,OAAO,CAACO,UAAU,CAACC,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvD,IAAIG,KAAK,GAAGT,OAAO,CAACO,UAAU,CAACD,CAAC,CAAC;IACjC,IAAIP,YAAY,CAACU,KAAK,CAAC,IAAIC,mBAAmB,CAACD,KAAK,CAAC,EAAE;MACrD,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA,IAAIE,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,IAAI,EAAI;EAC3B,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACtBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,IAAIC,cAAc,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAG9B,KAAK,CAAC6B,MAAM,CAAC;IAC1BE,mBAAmB,EAAE,KAAK;IAC1BC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACFhC,KAAK,CAACiC,SAAS,CAAC,YAAM;IACpB,IAAI5B,SAAS,EAAE;MACb,IAAI6B,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;QAKpB,IAAIN,cAAc,CAACO,OAAO,IAAI,IAAI,IAAIL,QAAQ,CAACK,OAAO,CAACJ,mBAAmB,IAAI,CAACL,MAAM,EAAE;UACrF;QACF;QACA,IAAI;UACFI,QAAQ,CAACK,OAAO,CAACJ,mBAAmB,GAAG,IAAI;UAC3C,IAAIf,QAAQ,CAACC,aAAa,YAAYmB,IAAI,IAAI,CAACR,cAAc,CAACO,OAAO,CAACE,QAAQ,CAACrB,QAAQ,CAACC,aAAa,CAAC,EAAE;YAOtG,IAAIqB,UAAU,GAAGpB,oBAAoB,CAACU,cAAc,CAACO,OAAO,CAAC;YAC7D,IAAIL,QAAQ,CAACK,OAAO,CAACH,kBAAkB,KAAKhB,QAAQ,CAACC,aAAa,EAAE;cAClEqB,UAAU,GAAGf,mBAAmB,CAACK,cAAc,CAACO,OAAO,CAAC;YAC1D;YAEA,IAAI,CAACG,UAAU,IAAIV,cAAc,CAACO,OAAO,IAAI,IAAI,IAAInB,QAAQ,CAACC,aAAa,EAAE;cAC3Eb,SAAS,CAACU,KAAK,CAACc,cAAc,CAACO,OAAO,CAAC;YACzC;UACF;QACF,CAAC,SAAS;UACRL,QAAQ,CAACK,OAAO,CAACJ,mBAAmB,GAAG,KAAK;QAC9C;QACAD,QAAQ,CAACK,OAAO,CAACH,kBAAkB,GAAGhB,QAAQ,CAACC,aAAa;MAC9D,CAAC;MAGDiB,SAAS,CAAC,CAAC;MACXlB,QAAQ,CAACuB,gBAAgB,CAAC,OAAO,EAAEL,SAAS,EAAE,IAAI,CAAC;MACnD,OAAO;QAAA,OAAMlB,QAAQ,CAACwB,mBAAmB,CAAC,OAAO,EAAEN,SAAS,EAAE,IAAI,CAAC;MAAA;IACrE;EACF,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;EAIZ1B,KAAK,CAACiC,SAAS,CAAC,YAAY;IAC1B,IAAI5B,SAAS,EAAE;MACb,IAAIoC,6BAA6B,GAAGzB,QAAQ,CAACC,aAAa;MAC1D,OAAO,YAAY;QACjB,IAAIwB,6BAA6B,IAAIzB,QAAQ,CAACqB,QAAQ,CAACI,6BAA6B,CAAC,EAAE;UACrFrC,SAAS,CAACU,KAAK,CAAC2B,6BAA6B,CAAC;QAChD;MACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAoBzC,KAAK,CAACE,aAAa,CAACF,KAAK,CAAC0C,QAAQ,EAAE,IAAI,EAAe1C,KAAK,CAACE,aAAa,CAACI,YAAY,EAAE,IAAI,CAAC,EAAeN,KAAK,CAACE,aAAa,CAACD,IAAI,EAAE;IACzJ0C,GAAG,EAAEf;EACP,CAAC,EAAED,QAAQ,CAAC,EAAe3B,KAAK,CAACE,aAAa,CAACI,YAAY,EAAE,IAAI,CAAC,CAAC;AACrE,CAAC;AACD,eAAekB,cAAc;AAC7B,IAAId,MAAM,GAAGP,UAAU,CAACyC,MAAM,CAAC;EAC7BjC,YAAY,EAAE;IACZkC,YAAY,EAAE;EAChB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}