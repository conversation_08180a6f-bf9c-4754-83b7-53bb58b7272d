{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nexport var Outline = function Outline(_ref) {\n  var isV3 = _ref.isV3,\n    label = _ref.label,\n    activeColor = _ref.activeColor,\n    backgroundColor = _ref.backgroundColor,\n    hasActiveOutline = _ref.hasActiveOutline,\n    focused = _ref.focused,\n    outlineColor = _ref.outlineColor,\n    roundness = _ref.roundness,\n    style = _ref.style;\n  return React.createElement(View, {\n    testID: \"text-input-outline\",\n    pointerEvents: \"none\",\n    style: [styles.outline, !label && styles.noLabelOutline, {\n      backgroundColor: backgroundColor,\n      borderRadius: roundness,\n      borderWidth: (isV3 ? hasActiveOutline : focused) ? 2 : 1,\n      borderColor: hasActiveOutline ? activeColor : outlineColor\n    }, style]\n  });\n};\nvar styles = StyleSheet.create({\n  outline: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    top: 6,\n    bottom: 0\n  },\n  noLabelOutline: {\n    top: 0\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "Outline", "_ref", "isV3", "label", "activeColor", "backgroundColor", "hasActiveOutline", "focused", "outlineColor", "roundness", "style", "createElement", "testID", "pointerEvents", "styles", "outline", "noLabelOutline", "borderRadius", "borderWidth", "borderColor", "create", "position", "left", "right", "top", "bottom"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-paper/src/components/TextInput/Addons/Outline.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleSheet,\n  ColorValue,\n  StyleProp,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { TextInputLabelProp } from '../types';\n\ntype OutlineProps = {\n  isV3: boolean;\n  activeColor: string;\n  backgroundColor: ColorValue;\n  hasActiveOutline?: boolean;\n  focused?: boolean;\n  outlineColor?: string;\n  roundness?: number;\n  label?: TextInputLabelProp;\n  style?: StyleProp<ViewStyle>;\n};\n\nexport const Outline = ({\n  isV3,\n  label,\n  activeColor,\n  backgroundColor,\n  hasActiveOutline,\n  focused,\n  outlineColor,\n  roundness,\n  style,\n}: OutlineProps) => (\n  <View\n    testID=\"text-input-outline\"\n    pointerEvents=\"none\"\n    style={[\n      styles.outline,\n      !label && styles.noLabelOutline,\n      // eslint-disable-next-line react-native/no-inline-styles\n      {\n        backgroundColor,\n        borderRadius: roundness,\n        borderWidth: (isV3 ? hasActiveOutline : focused) ? 2 : 1,\n        borderColor: hasActiveOutline ? activeColor : outlineColor,\n      },\n      style,\n    ]}\n  />\n);\n\nconst styles = StyleSheet.create({\n  outline: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    top: 6,\n    bottom: 0,\n  },\n  noLabelOutline: {\n    top: 0,\n  },\n});\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAuB9B,OAAO,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAGC,IAAA;EAAA,IACrBC,IAAI,GASSD,IAAA,CATbC,IAAI;IACJC,KAAK,GAQQF,IAAA,CARbE,KAAK;IACLC,WAAW,GAOEH,IAAA,CAPbG,WAAW;IACXC,eAAe,GAMFJ,IAAA,CANbI,eAAe;IACfC,gBAAgB,GAKHL,IAAA,CALbK,gBAAgB;IAChBC,OAAO,GAIMN,IAAA,CAJbM,OAAO;IACPC,YAAY,GAGCP,IAAA,CAHbO,YAAY;IACZC,SAAS,GAEIR,IAAA,CAFbQ,SAAS;IACTC,KAAA,GACaT,IAAA,CADbS,KAAA;EACa,OACbb,KAAA,CAAAc,aAAA,CAACZ,IAAI;IACHa,MAAM,EAAC,oBAAoB;IAC3BC,aAAa,EAAC,MAAM;IACpBH,KAAK,EAAE,CACLI,MAAM,CAACC,OAAO,EACd,CAACZ,KAAK,IAAIW,MAAM,CAACE,cAAc,EAE/B;MACEX,eAAe,EAAfA,eAAe;MACfY,YAAY,EAAER,SAAS;MACvBS,WAAW,EAAE,CAAChB,IAAI,GAAGI,gBAAgB,GAAGC,OAAO,IAAI,CAAC,GAAG,CAAC;MACxDY,WAAW,EAAEb,gBAAgB,GAAGF,WAAW,GAAGI;IAChD,CAAC,EACDE,KAAK;EACL,CACH,CAAC;AAAA,CACH;AAED,IAAMI,MAAM,GAAGhB,UAAU,CAACsB,MAAM,CAAC;EAC/BL,OAAO,EAAE;IACPM,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;EACV,CAAC;EACDT,cAAc,EAAE;IACdQ,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}