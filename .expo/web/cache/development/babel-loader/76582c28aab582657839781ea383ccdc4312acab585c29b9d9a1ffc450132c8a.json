{"ast": null, "code": "export function getInitialSafeArea() {\n  return {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0\n  };\n}", "map": {"version": 3, "names": ["getInitialSafeArea", "top", "bottom", "left", "right"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo/src/environment/getInitialSafeArea.ts"], "sourcesContent": ["/**\n * Get the best estimate safe area before native modules have fully loaded,\n * this is the fallback file which assumes guessing cannot be done.\n */\nexport function getInitialSafeArea(): { top: number; bottom: number; left: number; right: number } {\n  return {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n  };\n}\n"], "mappings": "AAIA,OAAM,SAAUA,kBAAkBA,CAAA;EAChC,OAAO;IACLC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;GACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}