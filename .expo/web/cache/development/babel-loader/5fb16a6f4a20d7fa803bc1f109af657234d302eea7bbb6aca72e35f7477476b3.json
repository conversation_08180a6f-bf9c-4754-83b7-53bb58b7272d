{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Icon, { isEqualIcon, isValidIcon } from \"./Icon\";\nimport { useInternalTheme } from \"../core/theming\";\nvar CrossFadeIcon = function CrossFadeIcon(_ref) {\n  var color = _ref.color,\n    size = _ref.size,\n    source = _ref.source,\n    themeOverrides = _ref.theme,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'cross-fade-icon' : _ref$testID;\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useState = React.useState(function () {\n      return source;\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    currentIcon = _React$useState2[0],\n    setCurrentIcon = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    previousIcon = _React$useState4[0],\n    setPreviousIcon = _React$useState4[1];\n  var _React$useRef = React.useRef(new Animated.Value(1)),\n    fade = _React$useRef.current;\n  var scale = theme.animation.scale;\n  if (currentIcon !== source) {\n    setPreviousIcon(function () {\n      return currentIcon;\n    });\n    setCurrentIcon(function () {\n      return source;\n    });\n  }\n  React.useEffect(function () {\n    if (isValidIcon(previousIcon) && !isEqualIcon(previousIcon, currentIcon)) {\n      fade.setValue(1);\n      Animated.timing(fade, {\n        duration: scale * 200,\n        toValue: 0,\n        useNativeDriver: true\n      }).start();\n    }\n  }, [currentIcon, previousIcon, fade, scale]);\n  var opacityPrev = fade;\n  var opacityNext = previousIcon ? fade.interpolate({\n    inputRange: [0, 1],\n    outputRange: [1, 0]\n  }) : 1;\n  var rotatePrev = fade.interpolate({\n    inputRange: [0, 1],\n    outputRange: ['-90deg', '0deg']\n  });\n  var rotateNext = previousIcon ? fade.interpolate({\n    inputRange: [0, 1],\n    outputRange: ['0deg', '-180deg']\n  }) : '0deg';\n  return React.createElement(View, {\n    style: [styles.content, {\n      height: size,\n      width: size\n    }]\n  }, previousIcon ? React.createElement(Animated.View, {\n    style: [styles.icon, {\n      opacity: opacityPrev,\n      transform: [{\n        rotate: rotatePrev\n      }]\n    }],\n    testID: `${testID}-previous`\n  }, React.createElement(Icon, {\n    source: previousIcon,\n    size: size,\n    color: color,\n    theme: theme\n  })) : null, React.createElement(Animated.View, {\n    style: [styles.icon, {\n      opacity: opacityNext,\n      transform: [{\n        rotate: rotateNext\n      }]\n    }],\n    testID: `${testID}-current`\n  }, React.createElement(Icon, {\n    source: currentIcon,\n    size: size,\n    color: color,\n    theme: theme\n  })));\n};\nexport default CrossFadeIcon;\nvar styles = StyleSheet.create({\n  content: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  icon: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "View", "Icon", "isEqualIcon", "isValidIcon", "useInternalTheme", "CrossFadeIcon", "_ref", "color", "size", "source", "themeOverrides", "theme", "_ref$testID", "testID", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "currentIcon", "setCurrentIcon", "_React$useState3", "_React$useState4", "previousIcon", "setPreviousIcon", "_React$useRef", "useRef", "Value", "fade", "current", "scale", "animation", "useEffect", "setValue", "timing", "duration", "toValue", "useNativeDriver", "start", "opacityPrev", "opacityNext", "interpolate", "inputRange", "outputRange", "rotatePrev", "rotateNext", "createElement", "style", "styles", "content", "height", "width", "icon", "opacity", "transform", "rotate", "create", "alignItems", "justifyContent", "position", "top", "left", "right", "bottom"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/CrossFadeIcon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated, StyleSheet, View } from 'react-native';\n\nimport Icon, { IconSource, isEqualIcon, isValidIcon } from './Icon';\nimport { useInternalTheme } from '../core/theming';\nimport type { ThemeProp } from '../types';\n\ntype Props = {\n  /**\n   * Icon to display for the `CrossFadeIcon`.\n   */\n  source: IconSource;\n  /**\n   * Color of the icon.\n   */\n  color: string;\n  /**\n   * Size of the icon.\n   */\n  size: number;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\nconst CrossFadeIcon = ({\n  color,\n  size,\n  source,\n  theme: themeOverrides,\n  testID = 'cross-fade-icon',\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const [currentIcon, setCurrentIcon] = React.useState<IconSource>(\n    () => source\n  );\n  const [previousIcon, setPreviousIcon] = React.useState<IconSource | null>(\n    null\n  );\n  const { current: fade } = React.useRef<Animated.Value>(new Animated.Value(1));\n\n  const { scale } = theme.animation;\n\n  if (currentIcon !== source) {\n    setPreviousIcon(() => currentIcon);\n    setCurrentIcon(() => source);\n  }\n\n  React.useEffect(() => {\n    if (isValidIcon(previousIcon) && !isEqualIcon(previousIcon, currentIcon)) {\n      fade.setValue(1);\n\n      Animated.timing(fade, {\n        duration: scale * 200,\n        toValue: 0,\n        useNativeDriver: true,\n      }).start();\n    }\n  }, [currentIcon, previousIcon, fade, scale]);\n\n  const opacityPrev = fade;\n  const opacityNext = previousIcon\n    ? fade.interpolate({\n        inputRange: [0, 1],\n        outputRange: [1, 0],\n      })\n    : 1;\n\n  const rotatePrev = fade.interpolate({\n    inputRange: [0, 1],\n    outputRange: ['-90deg', '0deg'],\n  });\n\n  const rotateNext = previousIcon\n    ? fade.interpolate({\n        inputRange: [0, 1],\n        outputRange: ['0deg', '-180deg'],\n      })\n    : '0deg';\n\n  return (\n    <View\n      style={[\n        styles.content,\n        {\n          height: size,\n          width: size,\n        },\n      ]}\n    >\n      {previousIcon ? (\n        <Animated.View\n          style={[\n            styles.icon,\n            {\n              opacity: opacityPrev,\n              transform: [{ rotate: rotatePrev }],\n            },\n          ]}\n          testID={`${testID}-previous`}\n        >\n          <Icon source={previousIcon} size={size} color={color} theme={theme} />\n        </Animated.View>\n      ) : null}\n      <Animated.View\n        style={[\n          styles.icon,\n          {\n            opacity: opacityNext,\n            transform: [{ rotate: rotateNext }],\n          },\n        ]}\n        testID={`${testID}-current`}\n      >\n        <Icon source={currentIcon} size={size} color={color} theme={theme} />\n      </Animated.View>\n    </View>\n  );\n};\n\nexport default CrossFadeIcon;\n\nconst styles = StyleSheet.create({\n  content: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  icon: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n  },\n});\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,IAAI,IAAgBC,WAAW,EAAEC,WAAW;AACnD,SAASC,gBAAgB;AA0BzB,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAA,EAMT;EAAA,IALXC,KAAK,GAKCD,IAAA,CALNC,KAAK;IACLC,IAAI,GAIEF,IAAA,CAJNE,IAAI;IACJC,MAAM,GAGAH,IAAA,CAHNG,MAAM;IACCC,cAAc,GAEfJ,IAAA,CAFNK,KAAK;IAAAC,WAAA,GAECN,IAAA,CADNO,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,oBAAAA,WAAA;EAET,IAAMD,KAAK,GAAGP,gBAAgB,CAACM,cAAc,CAAC;EAC9C,IAAAI,eAAA,GAAsCjB,KAAK,CAACkB,QAAQ,CAClD;MAAA,OAAMN,MACR;IAAA,EAAC;IAAAO,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAFMI,WAAW,GAAAF,gBAAA;IAAEG,cAAc,GAAAH,gBAAA;EAGlC,IAAAI,gBAAA,GAAwCvB,KAAK,CAACkB,QAAQ,CACpD,IACF,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAFME,YAAY,GAAAD,gBAAA;IAAEE,eAAe,GAAAF,gBAAA;EAGpC,IAAAG,aAAA,GAA0B3B,KAAK,CAAC4B,MAAM,CAAiB,IAAI3B,QAAQ,CAAC4B,KAAK,CAAC,CAAC,CAAC,CAAC;IAA5DC,IAAA,GAAAH,aAAA,CAATI,OAAO;EAEf,IAAQC,KAAA,GAAUlB,KAAK,CAACmB,SAAS,CAAzBD,KAAA;EAER,IAAIX,WAAW,KAAKT,MAAM,EAAE;IAC1Bc,eAAe,CAAC;MAAA,OAAML,WAAW;IAAA,EAAC;IAClCC,cAAc,CAAC;MAAA,OAAMV,MAAM;IAAA,EAAC;EAC9B;EAEAZ,KAAK,CAACkC,SAAS,CAAC,YAAM;IACpB,IAAI5B,WAAW,CAACmB,YAAY,CAAC,IAAI,CAACpB,WAAW,CAACoB,YAAY,EAAEJ,WAAW,CAAC,EAAE;MACxES,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC;MAEhBlC,QAAQ,CAACmC,MAAM,CAACN,IAAI,EAAE;QACpBO,QAAQ,EAAEL,KAAK,GAAG,GAAG;QACrBM,OAAO,EAAE,CAAC;QACVC,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACnB,WAAW,EAAEI,YAAY,EAAEK,IAAI,EAAEE,KAAK,CAAC,CAAC;EAE5C,IAAMS,WAAW,GAAGX,IAAI;EACxB,IAAMY,WAAW,GAAGjB,YAAY,GAC5BK,IAAI,CAACa,WAAW,CAAC;IACfC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC,GACF,CAAC;EAEL,IAAMC,UAAU,GAAGhB,IAAI,CAACa,WAAW,CAAC;IAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM;EAChC,CAAC,CAAC;EAEF,IAAME,UAAU,GAAGtB,YAAY,GAC3BK,IAAI,CAACa,WAAW,CAAC;IACfC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,SAAS;EACjC,CAAC,CAAC,GACF,MAAM;EAEV,OACE7C,KAAA,CAAAgD,aAAA,CAAC7C,IAAI;IACH8C,KAAK,EAAE,CACLC,MAAM,CAACC,OAAO,EACd;MACEC,MAAM,EAAEzC,IAAI;MACZ0C,KAAK,EAAE1C;IACT,CAAC;EACD,GAEDc,YAAY,GACXzB,KAAA,CAAAgD,aAAA,CAAC/C,QAAQ,CAACE,IAAI;IACZ8C,KAAK,EAAE,CACLC,MAAM,CAACI,IAAI,EACX;MACEC,OAAO,EAAEd,WAAW;MACpBe,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEX;MAAW,CAAC;IACpC,CAAC,CACD;IACF9B,MAAM,EAAG,GAAEA,MAAO;EAAW,GAE7BhB,KAAA,CAAAgD,aAAA,CAAC5C,IAAI;IAACQ,MAAM,EAAEa,YAAa;IAACd,IAAI,EAAEA,IAAK;IAACD,KAAK,EAAEA,KAAM;IAACI,KAAK,EAAEA;EAAM,CAAE,CACxD,CAAC,GACd,IAAI,EACRd,KAAA,CAAAgD,aAAA,CAAC/C,QAAQ,CAACE,IAAI;IACZ8C,KAAK,EAAE,CACLC,MAAM,CAACI,IAAI,EACX;MACEC,OAAO,EAAEb,WAAW;MACpBc,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEV;MAAW,CAAC;IACpC,CAAC,CACD;IACF/B,MAAM,EAAG,GAAEA,MAAO;EAAU,GAE5BhB,KAAA,CAAAgD,aAAA,CAAC5C,IAAI;IAACQ,MAAM,EAAES,WAAY;IAACV,IAAI,EAAEA,IAAK;IAACD,KAAK,EAAEA,KAAM;IAACI,KAAK,EAAEA;EAAM,CAAE,CACvD,CACX,CAAC;AAEX,CAAC;AAED,eAAeN,aAAa;AAE5B,IAAM0C,MAAM,GAAGhD,UAAU,CAACwD,MAAM,CAAC;EAC/BP,OAAO,EAAE;IACPQ,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDN,IAAI,EAAE;IACJO,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}