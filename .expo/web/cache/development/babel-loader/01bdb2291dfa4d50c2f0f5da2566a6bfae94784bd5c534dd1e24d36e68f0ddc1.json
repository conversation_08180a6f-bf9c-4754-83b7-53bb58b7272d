{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport PortalManager from \"./PortalManager\";\nexport var PortalContext = React.createContext(null);\nvar PortalHost = function (_React$Component) {\n  function PortalHost() {\n    var _this;\n    _classCallCheck(this, PortalHost);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, PortalHost, [].concat(args));\n    _this.setManager = function (manager) {\n      _this.manager = manager;\n    };\n    _this.mount = function (children) {\n      var key = _this.nextKey++;\n      if (_this.manager) {\n        _this.manager.mount(key, children);\n      } else {\n        _this.queue.push({\n          type: 'mount',\n          key: key,\n          children: children\n        });\n      }\n      return key;\n    };\n    _this.update = function (key, children) {\n      if (_this.manager) {\n        _this.manager.update(key, children);\n      } else {\n        var op = {\n          type: 'mount',\n          key: key,\n          children: children\n        };\n        var index = _this.queue.findIndex(function (o) {\n          return o.type === 'mount' || o.type === 'update' && o.key === key;\n        });\n        if (index > -1) {\n          _this.queue[index] = op;\n        } else {\n          _this.queue.push(op);\n        }\n      }\n    };\n    _this.unmount = function (key) {\n      if (_this.manager) {\n        _this.manager.unmount(key);\n      } else {\n        _this.queue.push({\n          type: 'unmount',\n          key: key\n        });\n      }\n    };\n    _this.nextKey = 0;\n    _this.queue = [];\n    return _this;\n  }\n  _inherits(PortalHost, _React$Component);\n  return _createClass(PortalHost, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var manager = this.manager;\n      var queue = this.queue;\n      while (queue.length && manager) {\n        var action = queue.pop();\n        if (action) {\n          switch (action.type) {\n            case 'mount':\n              manager.mount(action.key, action.children);\n              break;\n            case 'update':\n              manager.update(action.key, action.children);\n              break;\n            case 'unmount':\n              manager.unmount(action.key);\n              break;\n          }\n        }\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return React.createElement(PortalContext.Provider, {\n        value: {\n          mount: this.mount,\n          update: this.update,\n          unmount: this.unmount\n        }\n      }, React.createElement(View, {\n        style: styles.container,\n        collapsable: false,\n        pointerEvents: \"box-none\"\n      }, this.props.children), React.createElement(PortalManager, {\n        ref: this.setManager\n      }));\n    }\n  }]);\n}(React.Component);\nPortalHost.displayName = 'Portal.Host';\nexport { PortalHost as default };\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "PortalManager", "PortalContext", "createContext", "PortalHost", "_React$Component", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper", "concat", "setManager", "manager", "mount", "children", "key", "<PERSON><PERSON><PERSON>", "queue", "push", "type", "update", "op", "index", "findIndex", "o", "unmount", "_inherits", "_createClass", "value", "componentDidMount", "action", "pop", "render", "createElement", "Provider", "style", "styles", "container", "collapsable", "pointerEvents", "props", "ref", "Component", "displayName", "default", "create", "flex"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Portal/PortalHost.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { View, StyleSheet } from 'react-native';\n\nimport PortalManager from './PortalManager';\n\nexport type Props = {\n  children: React.ReactNode;\n};\n\ntype Operation =\n  | { type: 'mount'; key: number; children: React.ReactNode }\n  | { type: 'update'; key: number; children: React.ReactNode }\n  | { type: 'unmount'; key: number };\n\nexport type PortalMethods = {\n  mount: (children: React.ReactNode) => number;\n  update: (key: number, children: React.ReactNode) => void;\n  unmount: (key: number) => void;\n};\n\nexport const PortalContext = React.createContext<PortalMethods>(null as any);\n\n/**\n * Portal host renders all of its children `Portal` elements.\n * For example, you can wrap a screen in `Portal.Host` to render items above the screen.\n * If you're using the `Provider` component, it already includes `Portal.Host`.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Text } from 'react-native';\n * import { Portal } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Portal.Host>\n *     <Text>Content of the app</Text>\n *   </Portal.Host>\n * );\n *\n * export default MyComponent;\n * ```\n *\n * Here any `Portal` elements under `<App />` are rendered alongside `<App />` and will appear above `<App />` like a `Modal`.\n */\nexport default class PortalHost extends React.Component<Props> {\n  static displayName = 'Portal.Host';\n\n  componentDidMount() {\n    const manager = this.manager;\n    const queue = this.queue;\n\n    while (queue.length && manager) {\n      const action = queue.pop();\n      if (action) {\n        // eslint-disable-next-line default-case\n        switch (action.type) {\n          case 'mount':\n            manager.mount(action.key, action.children);\n            break;\n          case 'update':\n            manager.update(action.key, action.children);\n            break;\n          case 'unmount':\n            manager.unmount(action.key);\n            break;\n        }\n      }\n    }\n  }\n\n  private setManager = (manager: PortalManager | undefined | null) => {\n    this.manager = manager;\n  };\n\n  private mount = (children: React.ReactNode) => {\n    const key = this.nextKey++;\n\n    if (this.manager) {\n      this.manager.mount(key, children);\n    } else {\n      this.queue.push({ type: 'mount', key, children });\n    }\n\n    return key;\n  };\n\n  private update = (key: number, children: React.ReactNode) => {\n    if (this.manager) {\n      this.manager.update(key, children);\n    } else {\n      const op: Operation = { type: 'mount', key, children };\n      const index = this.queue.findIndex(\n        (o) => o.type === 'mount' || (o.type === 'update' && o.key === key)\n      );\n\n      if (index > -1) {\n        this.queue[index] = op;\n      } else {\n        this.queue.push(op as Operation);\n      }\n    }\n  };\n\n  private unmount = (key: number) => {\n    if (this.manager) {\n      this.manager.unmount(key);\n    } else {\n      this.queue.push({ type: 'unmount', key });\n    }\n  };\n\n  private nextKey = 0;\n  private queue: Operation[] = [];\n  private manager: PortalManager | null | undefined;\n\n  render() {\n    return (\n      <PortalContext.Provider\n        value={{\n          mount: this.mount,\n          update: this.update,\n          unmount: this.unmount,\n        }}\n      >\n        {/* Need collapsable=false here to clip the elevations, otherwise they appear above Portal components */}\n        <View\n          style={styles.container}\n          collapsable={false}\n          pointerEvents=\"box-none\"\n        >\n          {this.props.children}\n        </View>\n        <PortalManager ref={this.setManager} />\n      </PortalContext.Provider>\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n"], "mappings": ";;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAG9B,OAAOC,aAAa;AAiBpB,OAAO,IAAMC,aAAa,GAAGJ,KAAK,CAACK,aAAa,CAAgB,IAAW,CAAC;AAAA,IAwBvDC,UAAU,aAAAC,gBAAA;EAAA,SAAAD,WAAA;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,UAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAQ,UAAA,OAAAV,UAAA,KAAAW,MAAA,CAAAJ,IAAA;IAAAL,KAAA,CA0BrBU,UAAU,GAAI,UAAAC,OAAyC,EAAK;MAClEX,KAAA,CAAKW,OAAO,GAAGA,OAAO;IACxB,CAAC;IAAAX,KAAA,CAEOY,KAAK,GAAI,UAAAC,QAAyB,EAAK;MAC7C,IAAMC,GAAG,GAAGd,KAAA,CAAKe,OAAO,EAAE;MAE1B,IAAIf,KAAA,CAAKW,OAAO,EAAE;QAChBX,KAAA,CAAKW,OAAO,CAACC,KAAK,CAACE,GAAG,EAAED,QAAQ,CAAC;MACnC,CAAC,MAAM;QACLb,KAAA,CAAKgB,KAAK,CAACC,IAAI,CAAC;UAAEC,IAAI,EAAE,OAAO;UAAEJ,GAAG,EAAHA,GAAG;UAAED,QAAA,EAAAA;QAAS,CAAC,CAAC;MACnD;MAEA,OAAOC,GAAG;IACZ,CAAC;IAAAd,KAAA,CAEOmB,MAAM,GAAG,UAACL,GAAW,EAAED,QAAyB,EAAK;MAC3D,IAAIb,KAAA,CAAKW,OAAO,EAAE;QAChBX,KAAA,CAAKW,OAAO,CAACQ,MAAM,CAACL,GAAG,EAAED,QAAQ,CAAC;MACpC,CAAC,MAAM;QACL,IAAMO,EAAa,GAAG;UAAEF,IAAI,EAAE,OAAO;UAAEJ,GAAG,EAAHA,GAAG;UAAED,QAAA,EAAAA;QAAS,CAAC;QACtD,IAAMQ,KAAK,GAAGrB,KAAA,CAAKgB,KAAK,CAACM,SAAS,CAC/B,UAAAC,CAAC;UAAA,OAAKA,CAAC,CAACL,IAAI,KAAK,OAAO,IAAKK,CAAC,CAACL,IAAI,KAAK,QAAQ,IAAIK,CAAC,CAACT,GAAG,KAAKA,GACjE;QAAA,EAAC;QAED,IAAIO,KAAK,GAAG,CAAC,CAAC,EAAE;UACdrB,KAAA,CAAKgB,KAAK,CAACK,KAAK,CAAC,GAAGD,EAAE;QACxB,CAAC,MAAM;UACLpB,KAAA,CAAKgB,KAAK,CAACC,IAAI,CAACG,EAAe,CAAC;QAClC;MACF;IACF,CAAC;IAAApB,KAAA,CAEOwB,OAAO,GAAI,UAAAV,GAAW,EAAK;MACjC,IAAId,KAAA,CAAKW,OAAO,EAAE;QAChBX,KAAA,CAAKW,OAAO,CAACa,OAAO,CAACV,GAAG,CAAC;MAC3B,CAAC,MAAM;QACLd,KAAA,CAAKgB,KAAK,CAACC,IAAI,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEJ,GAAA,EAAAA;QAAI,CAAC,CAAC;MAC3C;IACF,CAAC;IAAAd,KAAA,CAEOe,OAAO,GAAG,CAAC;IAAAf,KAAA,CACXgB,KAAK,GAAgB,EAAE;IAAA,OAAAhB,KAAA;EAAA;EAAAyB,SAAA,CAAA3B,UAAA,EAAAC,gBAAA;EAAA,OAAA2B,YAAA,CAAA5B,UAAA;IAAAgB,GAAA;IAAAa,KAAA,EAjE/B,SAAAC,iBAAiBA,CAAA,EAAG;MAClB,IAAMjB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAMK,KAAK,GAAG,IAAI,CAACA,KAAK;MAExB,OAAOA,KAAK,CAACZ,MAAM,IAAIO,OAAO,EAAE;QAC9B,IAAMkB,MAAM,GAAGb,KAAK,CAACc,GAAG,CAAC,CAAC;QAC1B,IAAID,MAAM,EAAE;UAEV,QAAQA,MAAM,CAACX,IAAI;YACjB,KAAK,OAAO;cACVP,OAAO,CAACC,KAAK,CAACiB,MAAM,CAACf,GAAG,EAAEe,MAAM,CAAChB,QAAQ,CAAC;cAC1C;YACF,KAAK,QAAQ;cACXF,OAAO,CAACQ,MAAM,CAACU,MAAM,CAACf,GAAG,EAAEe,MAAM,CAAChB,QAAQ,CAAC;cAC3C;YACF,KAAK,SAAS;cACZF,OAAO,CAACa,OAAO,CAACK,MAAM,CAACf,GAAG,CAAC;cAC3B;UACJ;QACF;MACF;IACF;EAAA;IAAAA,GAAA;IAAAa,KAAA,EA+CA,SAAAI,MAAMA,CAAA,EAAG;MACP,OACEvC,KAAA,CAAAwC,aAAA,CAACpC,aAAa,CAACqC,QAAQ;QACrBN,KAAK,EAAE;UACLf,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBO,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBK,OAAO,EAAE,IAAI,CAACA;QAChB;MAAE,GAGFhC,KAAA,CAAAwC,aAAA,CAACvC,IAAI;QACHyC,KAAK,EAAEC,MAAM,CAACC,SAAU;QACxBC,WAAW,EAAE,KAAM;QACnBC,aAAa,EAAC;MAAU,GAEvB,IAAI,CAACC,KAAK,CAAC1B,QACR,CAAC,EACPrB,KAAA,CAAAwC,aAAA,CAACrC,aAAa;QAAC6C,GAAG,EAAE,IAAI,CAAC9B;MAAW,CAAE,CAChB,CAAC;IAE7B;EAAA;AAAA,EA3FsClB,KAAK,CAACiD,SAAS;AAAlC3C,UAAU,CACtB4C,WAAW,GAAG,aAAa;AAAA,SADf5C,UAAU,IAAA6C,OAAA;AA8F/B,IAAMR,MAAM,GAAGzC,UAAU,CAACkD,MAAM,CAAC;EAC/BR,SAAS,EAAE;IACTS,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}