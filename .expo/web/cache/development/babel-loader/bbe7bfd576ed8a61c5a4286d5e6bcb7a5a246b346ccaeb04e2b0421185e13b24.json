{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"size\", \"source\", \"style\", \"onError\", \"onLayout\", \"onLoad\", \"onLoadEnd\", \"onLoadStart\", \"onProgress\", \"theme\", \"testID\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Image from \"react-native-web/dist/exports/Image\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar defaultSize = 64;\nvar AvatarImage = function AvatarImage(_ref) {\n  var _ref$size = _ref.size,\n    size = _ref$size === void 0 ? defaultSize : _ref$size,\n    source = _ref.source,\n    style = _ref.style,\n    onError = _ref.onError,\n    onLayout = _ref.onLayout,\n    onLoad = _ref.onLoad,\n    onLoadEnd = _ref.onLoadEnd,\n    onLoadStart = _ref.onLoadStart,\n    onProgress = _ref.onProgress,\n    themeOverrides = _ref.theme,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useInternalTheme = useInternalTheme(themeOverrides),\n    colors = _useInternalTheme.colors;\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$backgroundColor = _ref2.backgroundColor,\n    backgroundColor = _ref2$backgroundColor === void 0 ? colors === null || colors === void 0 ? void 0 : colors.primary : _ref2$backgroundColor;\n  return React.createElement(View, _extends({\n    style: [{\n      width: size,\n      height: size,\n      borderRadius: size / 2,\n      backgroundColor: backgroundColor\n    }, style]\n  }, rest), typeof source === 'function' && source({\n    size: size\n  }), typeof source !== 'function' && React.createElement(Image, {\n    testID: testID,\n    source: source,\n    style: {\n      width: size,\n      height: size,\n      borderRadius: size / 2\n    },\n    onError: onError,\n    onLayout: onLayout,\n    onLoad: onLoad,\n    onLoadEnd: onLoadEnd,\n    onLoadStart: onLoadStart,\n    onProgress: onProgress,\n    accessibilityIgnoresInvertColors: true\n  }));\n};\nAvatarImage.displayName = 'Avatar.Image';\nexport default AvatarImage;", "map": {"version": 3, "names": ["React", "Image", "StyleSheet", "View", "useInternalTheme", "defaultSize", "AvatarImage", "_ref", "_ref$size", "size", "source", "style", "onError", "onLayout", "onLoad", "onLoadEnd", "onLoadStart", "onProgress", "themeOverrides", "theme", "testID", "rest", "_objectWithoutProperties", "_excluded", "_useInternalTheme", "colors", "_ref2", "flatten", "_ref2$backgroundColor", "backgroundColor", "primary", "createElement", "_extends", "width", "height", "borderRadius", "accessibilityIgnoresInvertColors", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Avatar/AvatarImage.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Image,\n  ImageProps,\n  ImageSourcePropType,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\n\nconst defaultSize = 64;\n\nexport type AvatarImageSource =\n  | ImageSourcePropType\n  | ((props: { size: number }) => React.ReactNode);\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Image to display for the `Avatar`.\n   * It accepts a standard React Native Image `source` prop\n   * Or a function that returns an `Image`.\n   */\n  source: AvatarImageSource;\n  /**\n   * Size of the avatar.\n   */\n  size?: number;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Invoked on load error.\n   */\n  onError?: ImageProps['onError'];\n  /**\n   * Invoked on mount and on layout changes.\n   */\n  onLayout?: ImageProps['onLayout'];\n  /**\n   * Invoked when load completes successfully.\n   */\n  onLoad?: ImageProps['onLoad'];\n  /**\n   * Invoked when load either succeeds or fails.\n   */\n  onLoadEnd?: ImageProps['onLoadEnd'];\n  /**\n   * Invoked on load start.\n   */\n  onLoadStart?: ImageProps['onLoadStart'];\n  /**\n   * Invoked on download progress.\n   */\n  onProgress?: ImageProps['onProgress'];\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Avatars can be used to represent people in a graphical way.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Avatar } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Avatar.Image size={24} source={require('../assets/avatar.png')} />\n * );\n * export default MyComponent\n * ```\n */\nconst AvatarImage = ({\n  size = defaultSize,\n  source,\n  style,\n  onError,\n  onLayout,\n  onLoad,\n  onLoadEnd,\n  onLoadStart,\n  onProgress,\n  theme: themeOverrides,\n  testID,\n  ...rest\n}: Props) => {\n  const { colors } = useInternalTheme(themeOverrides);\n  const { backgroundColor = colors?.primary } = StyleSheet.flatten(style) || {};\n\n  return (\n    <View\n      style={[\n        {\n          width: size,\n          height: size,\n          borderRadius: size / 2,\n          backgroundColor,\n        },\n        style,\n      ]}\n      {...rest}\n    >\n      {typeof source === 'function' && source({ size })}\n      {typeof source !== 'function' && (\n        <Image\n          testID={testID}\n          source={source}\n          style={{ width: size, height: size, borderRadius: size / 2 }}\n          onError={onError}\n          onLayout={onLayout}\n          onLoad={onLoad}\n          onLoadEnd={onLoadEnd}\n          onLoadStart={onLoadStart}\n          onProgress={onProgress}\n          accessibilityIgnoresInvertColors\n        />\n      )}\n    </View>\n  );\n};\n\nAvatarImage.displayName = 'Avatar.Image';\n\nexport default AvatarImage;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAW9B,SAASC,gBAAgB;AAGzB,IAAMC,WAAW,GAAG,EAAE;AA8DtB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAGC,IAAA,EAaP;EAAA,IAAAC,SAAA,GAALD,IAAA,CAZNE,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAGH,WAAW,GAAAG,SAAA;IAClBE,MAAM,GAWAH,IAAA,CAXNG,MAAM;IACNC,KAAK,GAUCJ,IAAA,CAVNI,KAAK;IACLC,OAAO,GASDL,IAAA,CATNK,OAAO;IACPC,QAAQ,GAQFN,IAAA,CARNM,QAAQ;IACRC,MAAM,GAOAP,IAAA,CAPNO,MAAM;IACNC,SAAS,GAMHR,IAAA,CANNQ,SAAS;IACTC,WAAW,GAKLT,IAAA,CALNS,WAAW;IACXC,UAAU,GAIJV,IAAA,CAJNU,UAAU;IACHC,cAAc,GAGfX,IAAA,CAHNY,KAAK;IACLC,MAAM,GAEAb,IAAA,CAFNa,MAAM;IACHC,IAAA,GAAAC,wBAAA,CACGf,IAAA,EAAAgB,SAAA;EACN,IAAAC,iBAAA,GAAmBpB,gBAAgB,CAACc,cAAc,CAAC;IAA3CO,MAAA,GAAAD,iBAAA,CAAAC,MAAA;EACR,IAAAC,KAAA,GAA8CxB,UAAU,CAACyB,OAAO,CAAChB,KAAK,CAAC,IAAI,CAAC,CAAC;IAAAiB,qBAAA,GAAAF,KAAA,CAArEG,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAGH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,OAAA,GAAAF,qBAAA;EAElC,OACE5B,KAAA,CAAA+B,aAAA,CAAC5B,IAAI,EAAA6B,QAAA;IACHrB,KAAK,EAAE,CACL;MACEsB,KAAK,EAAExB,IAAI;MACXyB,MAAM,EAAEzB,IAAI;MACZ0B,YAAY,EAAE1B,IAAI,GAAG,CAAC;MACtBoB,eAAA,EAAAA;IACF,CAAC,EACDlB,KAAK;EACL,GACEU,IAAI,GAEP,OAAOX,MAAM,KAAK,UAAU,IAAIA,MAAM,CAAC;IAAED,IAAA,EAAAA;EAAK,CAAC,CAAC,EAChD,OAAOC,MAAM,KAAK,UAAU,IAC3BV,KAAA,CAAA+B,aAAA,CAAC9B,KAAK;IACJmB,MAAM,EAAEA,MAAO;IACfV,MAAM,EAAEA,MAAO;IACfC,KAAK,EAAE;MAAEsB,KAAK,EAAExB,IAAI;MAAEyB,MAAM,EAAEzB,IAAI;MAAE0B,YAAY,EAAE1B,IAAI,GAAG;IAAE,CAAE;IAC7DG,OAAO,EAAEA,OAAQ;IACjBC,QAAQ,EAAEA,QAAS;IACnBC,MAAM,EAAEA,MAAO;IACfC,SAAS,EAAEA,SAAU;IACrBC,WAAW,EAAEA,WAAY;IACzBC,UAAU,EAAEA,UAAW;IACvBmB,gCAAgC;EAAA,CACjC,CAEC,CAAC;AAEX,CAAC;AAED9B,WAAW,CAAC+B,WAAW,GAAG,cAAc;AAExC,eAAe/B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}