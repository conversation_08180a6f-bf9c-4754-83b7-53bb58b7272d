{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"theme\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport CheckboxAndroid from \"./CheckboxAndroid\";\nimport CheckboxIOS from \"./CheckboxIOS\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar Checkbox = function Checkbox(_ref) {\n  var themeOverrides = _ref.theme,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  return Platform.OS === 'ios' ? React.createElement(CheckboxIOS, _extends({}, props, {\n    theme: theme\n  })) : React.createElement(CheckboxAndroid, _extends({}, props, {\n    theme: theme\n  }));\n};\nexport default Checkbox;\nvar CheckboxWithTheme = Checkbox;\nexport { CheckboxWithTheme as Checkbox };", "map": {"version": 3, "names": ["React", "Platform", "CheckboxAndroid", "CheckboxIOS", "useInternalTheme", "Checkbox", "_ref", "themeOverrides", "theme", "props", "_objectWithoutProperties", "_excluded", "OS", "createElement", "_extends", "CheckboxWithTheme"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Checkbox/Checkbox.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { GestureResponderEvent, Platform } from 'react-native';\n\nimport CheckboxAndroid from './CheckboxAndroid';\nimport CheckboxIOS from './CheckboxIOS';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\n\nexport type Props = {\n  /**\n   * Status of checkbox.\n   */\n  status: 'checked' | 'unchecked' | 'indeterminate';\n  /**\n   * Whether checkbox is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Custom color for unchecked checkbox.\n   */\n  uncheckedColor?: string;\n  /**\n   * Custom color for checkbox.\n   */\n  color?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n/**\n * Checkboxes allow the selection of multiple options from a set.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Checkbox } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [checked, setChecked] = React.useState(false);\n *\n *   return (\n *     <Checkbox\n *       status={checked ? 'checked' : 'unchecked'}\n *       onPress={() => {\n *         setChecked(!checked);\n *       }}\n *     />\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst Checkbox = ({ theme: themeOverrides, ...props }: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  return Platform.OS === 'ios' ? (\n    <CheckboxIOS {...props} theme={theme} />\n  ) : (\n    <CheckboxAndroid {...props} theme={theme} />\n  );\n};\n\nexport default Checkbox;\n\n// @component-docs ignore-next-line\nconst CheckboxWithTheme = Checkbox;\n// @component-docs ignore-next-line\nexport { CheckboxWithTheme as Checkbox };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAG9B,OAAOC,eAAe;AACtB,OAAOC,WAAW;AAClB,SAASC,gBAAgB;AA0DzB,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,IAAA,EAAgD;EAAA,IAAtCC,cAAc,GAAmBD,IAAA,CAAxCE,KAAK;IAAqBC,KAAA,GAAAC,wBAAA,CAAcJ,IAAA,EAAAK,SAAA;EAC1D,IAAMH,KAAK,GAAGJ,gBAAgB,CAACG,cAAc,CAAC;EAC9C,OAAON,QAAQ,CAACW,EAAE,KAAK,KAAK,GAC1BZ,KAAA,CAAAa,aAAA,CAACV,WAAW,EAAAW,QAAA,KAAKL,KAAK;IAAED,KAAK,EAAEA;EAAM,EAAE,CAAC,GAExCR,KAAA,CAAAa,aAAA,CAACX,eAAe,EAAAY,QAAA,KAAKL,KAAK;IAAED,KAAK,EAAEA;EAAM,EAAE,CAC5C;AACH,CAAC;AAED,eAAeH,QAAQ;AAGvB,IAAMU,iBAAiB,GAAGV,QAAQ;AAElC,SAASU,iBAAiB,IAAIV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}