{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"disabled\", \"onPress\", \"theme\", \"value\", \"status\", \"testID\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { RadioButtonContext } from \"./RadioButtonGroup\";\nimport { handlePress, isChecked } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { getAndroidSelectionControlColor } from \"../Checkbox/utils\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nvar BORDER_WIDTH = 2;\nvar RadioButtonAndroid = function RadioButtonAndroid(_ref) {\n  var disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    themeOverrides = _ref.theme,\n    value = _ref.value,\n    status = _ref.status,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(BORDER_WIDTH)),\n    borderAnim = _React$useRef.current;\n  var _React$useRef2 = React.useRef(new Animated.Value(1)),\n    radioAnim = _React$useRef2.current;\n  var isFirstRendering = React.useRef(true);\n  var scale = theme.animation.scale;\n  React.useEffect(function () {\n    if (isFirstRendering.current) {\n      isFirstRendering.current = false;\n      return;\n    }\n    if (status === 'checked') {\n      radioAnim.setValue(1.2);\n      Animated.timing(radioAnim, {\n        toValue: 1,\n        duration: 150 * scale,\n        useNativeDriver: true\n      }).start();\n    } else {\n      borderAnim.setValue(10);\n      Animated.timing(borderAnim, {\n        toValue: BORDER_WIDTH,\n        duration: 150 * scale,\n        useNativeDriver: false\n      }).start();\n    }\n  }, [status, borderAnim, radioAnim, scale]);\n  return React.createElement(RadioButtonContext.Consumer, null, function (context) {\n    var checked = isChecked({\n      contextValue: context === null || context === void 0 ? void 0 : context.value,\n      status: status,\n      value: value\n    }) === 'checked';\n    var _getAndroidSelectionC = getAndroidSelectionControlColor({\n        theme: theme,\n        disabled: disabled,\n        checked: checked,\n        customColor: rest.color,\n        customUncheckedColor: rest.uncheckedColor\n      }),\n      rippleColor = _getAndroidSelectionC.rippleColor,\n      selectionControlColor = _getAndroidSelectionC.selectionControlColor;\n    return React.createElement(TouchableRipple, _extends({}, rest, {\n      borderless: true,\n      rippleColor: rippleColor,\n      onPress: disabled ? undefined : function (event) {\n        handlePress({\n          onPress: onPress,\n          onValueChange: context === null || context === void 0 ? void 0 : context.onValueChange,\n          value: value,\n          event: event\n        });\n      },\n      accessibilityRole: \"radio\",\n      accessibilityState: {\n        disabled: disabled,\n        checked: checked\n      },\n      accessibilityLiveRegion: \"polite\",\n      style: styles.container,\n      testID: testID,\n      theme: theme\n    }), React.createElement(Animated.View, {\n      style: [styles.radio, {\n        borderColor: selectionControlColor,\n        borderWidth: borderAnim\n      }]\n    }, checked ? React.createElement(View, {\n      style: [StyleSheet.absoluteFill, styles.radioContainer]\n    }, React.createElement(Animated.View, {\n      style: [styles.dot, {\n        backgroundColor: selectionControlColor,\n        transform: [{\n          scale: radioAnim\n        }]\n      }]\n    })) : null));\n  });\n};\nRadioButtonAndroid.displayName = 'RadioButton.Android';\nvar styles = StyleSheet.create({\n  container: {\n    borderRadius: 18\n  },\n  radioContainer: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  radio: {\n    height: 20,\n    width: 20,\n    borderRadius: 10,\n    margin: 8\n  },\n  dot: {\n    height: 10,\n    width: 10,\n    borderRadius: 5\n  }\n});\nexport default RadioButtonAndroid;\nexport { RadioButtonAndroid };", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "View", "RadioButtonContext", "handlePress", "isChecked", "useInternalTheme", "getAndroidSelectionControlColor", "TouchableRipple", "BORDER_WIDTH", "RadioButtonAndroid", "_ref", "disabled", "onPress", "themeOverrides", "theme", "value", "status", "testID", "rest", "_objectWithoutProperties", "_excluded", "_React$useRef", "useRef", "Value", "borderAnim", "current", "_React$useRef2", "radioAnim", "isFirstRendering", "scale", "animation", "useEffect", "setValue", "timing", "toValue", "duration", "useNativeDriver", "start", "createElement", "Consumer", "context", "checked", "contextValue", "_getAndroidSelectionC", "customColor", "color", "customUncheckedColor", "uncheckedColor", "rippleColor", "selectionControlColor", "_extends", "borderless", "undefined", "event", "onValueChange", "accessibilityRole", "accessibilityState", "accessibilityLiveRegion", "style", "styles", "container", "radio", "borderColor", "borderWidth", "absoluteFill", "radioContainer", "dot", "backgroundColor", "transform", "displayName", "create", "borderRadius", "alignItems", "justifyContent", "height", "width", "margin"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/RadioButton/RadioButtonAndroid.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated, StyleSheet, View } from 'react-native';\n\nimport { RadioButtonContext, RadioButtonContextType } from './RadioButtonGroup';\nimport { handlePress, isChecked } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $RemoveChildren, ThemeProp } from '../../types';\nimport { getAndroidSelectionControlColor } from '../Checkbox/utils';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\n\nexport type Props = $RemoveChildren<typeof TouchableRipple> & {\n  /**\n   * Value of the radio button\n   */\n  value: string;\n  /**\n   * Status of radio button.\n   */\n  status?: 'checked' | 'unchecked';\n  /**\n   * Whether radio is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (param?: any) => void;\n  /**\n   * Custom color for unchecked radio.\n   */\n  uncheckedColor?: string;\n  /**\n   * Custom color for radio.\n   */\n  color?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\nconst BORDER_WIDTH = 2;\n\n/**\n * Radio buttons allow the selection a single option from a set.\n * This component follows platform guidelines for Android, but can be used\n * on any platform.\n *\n * @extends TouchableRipple props https://callstack.github.io/react-native-paper/docs/components/TouchableRipple\n */\nconst RadioButtonAndroid = ({\n  disabled,\n  onPress,\n  theme: themeOverrides,\n  value,\n  status,\n  testID,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { current: borderAnim } = React.useRef<Animated.Value>(\n    new Animated.Value(BORDER_WIDTH)\n  );\n\n  const { current: radioAnim } = React.useRef<Animated.Value>(\n    new Animated.Value(1)\n  );\n\n  const isFirstRendering = React.useRef<boolean>(true);\n\n  const { scale } = theme.animation;\n\n  React.useEffect(() => {\n    // Do not run animation on very first rendering\n    if (isFirstRendering.current) {\n      isFirstRendering.current = false;\n      return;\n    }\n\n    if (status === 'checked') {\n      radioAnim.setValue(1.2);\n\n      Animated.timing(radioAnim, {\n        toValue: 1,\n        duration: 150 * scale,\n        useNativeDriver: true,\n      }).start();\n    } else {\n      borderAnim.setValue(10);\n\n      Animated.timing(borderAnim, {\n        toValue: BORDER_WIDTH,\n        duration: 150 * scale,\n        useNativeDriver: false,\n      }).start();\n    }\n  }, [status, borderAnim, radioAnim, scale]);\n\n  return (\n    <RadioButtonContext.Consumer>\n      {(context?: RadioButtonContextType) => {\n        const checked =\n          isChecked({\n            contextValue: context?.value,\n            status,\n            value,\n          }) === 'checked';\n\n        const { rippleColor, selectionControlColor } =\n          getAndroidSelectionControlColor({\n            theme,\n            disabled,\n            checked,\n            customColor: rest.color,\n            customUncheckedColor: rest.uncheckedColor,\n          });\n\n        return (\n          <TouchableRipple\n            {...rest}\n            borderless\n            rippleColor={rippleColor}\n            onPress={\n              disabled\n                ? undefined\n                : (event) => {\n                    handlePress({\n                      onPress,\n                      onValueChange: context?.onValueChange,\n                      value,\n                      event,\n                    });\n                  }\n            }\n            accessibilityRole=\"radio\"\n            accessibilityState={{ disabled, checked }}\n            accessibilityLiveRegion=\"polite\"\n            style={styles.container}\n            testID={testID}\n            theme={theme}\n          >\n            <Animated.View\n              style={[\n                styles.radio,\n                {\n                  borderColor: selectionControlColor,\n                  borderWidth: borderAnim,\n                },\n              ]}\n            >\n              {checked ? (\n                <View style={[StyleSheet.absoluteFill, styles.radioContainer]}>\n                  <Animated.View\n                    style={[\n                      styles.dot,\n                      {\n                        backgroundColor: selectionControlColor,\n                        transform: [{ scale: radioAnim }],\n                      },\n                    ]}\n                  />\n                </View>\n              ) : null}\n            </Animated.View>\n          </TouchableRipple>\n        );\n      }}\n    </RadioButtonContext.Consumer>\n  );\n};\n\nRadioButtonAndroid.displayName = 'RadioButton.Android';\n\nconst styles = StyleSheet.create({\n  container: {\n    borderRadius: 18,\n  },\n  radioContainer: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  radio: {\n    height: 20,\n    width: 20,\n    borderRadius: 10,\n    margin: 8,\n  },\n  dot: {\n    height: 10,\n    width: 10,\n    borderRadius: 5,\n  },\n});\n\nexport default RadioButtonAndroid;\n\n// @component-docs ignore-next-line\nexport { RadioButtonAndroid };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,SAASC,kBAAkB;AAC3B,SAASC,WAAW,EAAEC,SAAS;AAC/B,SAASC,gBAAgB;AAEzB,SAASC,+BAA+B;AACxC,OAAOC,eAAe;AAqCtB,IAAMC,YAAY,GAAG,CAAC;AAStB,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGC,IAAA,EAQd;EAAA,IAPXC,QAAQ,GAOFD,IAAA,CAPNC,QAAQ;IACRC,OAAO,GAMDF,IAAA,CANNE,OAAO;IACAC,cAAc,GAKfH,IAAA,CALNI,KAAK;IACLC,KAAK,GAICL,IAAA,CAJNK,KAAK;IACLC,MAAM,GAGAN,IAAA,CAHNM,MAAM;IACNC,MAAM,GAEAP,IAAA,CAFNO,MAAM;IACHC,IAAA,GAAAC,wBAAA,CACGT,IAAA,EAAAU,SAAA;EACN,IAAMN,KAAK,GAAGT,gBAAgB,CAACQ,cAAc,CAAC;EAC9C,IAAAQ,aAAA,GAAgCvB,KAAK,CAACwB,MAAM,CAC1C,IAAIvB,QAAQ,CAACwB,KAAK,CAACf,YAAY,CACjC,CAAC;IAFgBgB,UAAA,GAAAH,aAAA,CAATI,OAAO;EAIf,IAAAC,cAAA,GAA+B5B,KAAK,CAACwB,MAAM,CACzC,IAAIvB,QAAQ,CAACwB,KAAK,CAAC,CAAC,CACtB,CAAC;IAFgBI,SAAA,GAAAD,cAAA,CAATD,OAAO;EAIf,IAAMG,gBAAgB,GAAG9B,KAAK,CAACwB,MAAM,CAAU,IAAI,CAAC;EAEpD,IAAQO,KAAA,GAAUf,KAAK,CAACgB,SAAS,CAAzBD,KAAA;EAER/B,KAAK,CAACiC,SAAS,CAAC,YAAM;IAEpB,IAAIH,gBAAgB,CAACH,OAAO,EAAE;MAC5BG,gBAAgB,CAACH,OAAO,GAAG,KAAK;MAChC;IACF;IAEA,IAAIT,MAAM,KAAK,SAAS,EAAE;MACxBW,SAAS,CAACK,QAAQ,CAAC,GAAG,CAAC;MAEvBjC,QAAQ,CAACkC,MAAM,CAACN,SAAS,EAAE;QACzBO,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACLb,UAAU,CAACQ,QAAQ,CAAC,EAAE,CAAC;MAEvBjC,QAAQ,CAACkC,MAAM,CAACT,UAAU,EAAE;QAC1BU,OAAO,EAAE1B,YAAY;QACrB2B,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACrB,MAAM,EAAEQ,UAAU,EAAEG,SAAS,EAAEE,KAAK,CAAC,CAAC;EAE1C,OACE/B,KAAA,CAAAwC,aAAA,CAACpC,kBAAkB,CAACqC,QAAQ,QACxB,UAAAC,OAAgC,EAAK;IACrC,IAAMC,OAAO,GACXrC,SAAS,CAAC;MACRsC,YAAY,EAAEF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEzB,KAAK;MAC5BC,MAAM,EAANA,MAAM;MACND,KAAA,EAAAA;IACF,CAAC,CAAC,KAAK,SAAS;IAElB,IAAA4B,qBAAA,GACErC,+BAA+B,CAAC;QAC9BQ,KAAK,EAALA,KAAK;QACLH,QAAQ,EAARA,QAAQ;QACR8B,OAAO,EAAPA,OAAO;QACPG,WAAW,EAAE1B,IAAI,CAAC2B,KAAK;QACvBC,oBAAoB,EAAE5B,IAAI,CAAC6B;MAC7B,CAAC,CAAC;MAPIC,WAAW,GAAAL,qBAAA,CAAXK,WAAW;MAAEC,qBAAA,GAAAN,qBAAA,CAAAM,qBAAA;IASrB,OACEnD,KAAA,CAAAwC,aAAA,CAAC/B,eAAe,EAAA2C,QAAA,KACVhC,IAAI;MACRiC,UAAU;MACVH,WAAW,EAAEA,WAAY;MACzBpC,OAAO,EACLD,QAAQ,GACJyC,SAAS,GACR,UAAAC,KAAK,EAAK;QACTlD,WAAW,CAAC;UACVS,OAAO,EAAPA,OAAO;UACP0C,aAAa,EAAEd,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEc,aAAa;UACrCvC,KAAK,EAALA,KAAK;UACLsC,KAAA,EAAAA;QACF,CAAC,CAAC;MACJ,CACL;MACDE,iBAAiB,EAAC,OAAO;MACzBC,kBAAkB,EAAE;QAAE7C,QAAQ,EAARA,QAAQ;QAAE8B,OAAA,EAAAA;MAAQ,CAAE;MAC1CgB,uBAAuB,EAAC,QAAQ;MAChCC,KAAK,EAAEC,MAAM,CAACC,SAAU;MACxB3C,MAAM,EAAEA,MAAO;MACfH,KAAK,EAAEA;IAAM,IAEbhB,KAAA,CAAAwC,aAAA,CAACvC,QAAQ,CAACE,IAAI;MACZyD,KAAK,EAAE,CACLC,MAAM,CAACE,KAAK,EACZ;QACEC,WAAW,EAAEb,qBAAqB;QAClCc,WAAW,EAAEvC;MACf,CAAC;IACD,GAEDiB,OAAO,GACN3C,KAAA,CAAAwC,aAAA,CAACrC,IAAI;MAACyD,KAAK,EAAE,CAAC1D,UAAU,CAACgE,YAAY,EAAEL,MAAM,CAACM,cAAc;IAAE,GAC5DnE,KAAA,CAAAwC,aAAA,CAACvC,QAAQ,CAACE,IAAI;MACZyD,KAAK,EAAE,CACLC,MAAM,CAACO,GAAG,EACV;QACEC,eAAe,EAAElB,qBAAqB;QACtCmB,SAAS,EAAE,CAAC;UAAEvC,KAAK,EAAEF;QAAU,CAAC;MAClC,CAAC;IACD,CACH,CACG,CAAC,GACL,IACS,CACA,CAAC;EAEtB,CAC2B,CAAC;AAElC,CAAC;AAEDlB,kBAAkB,CAAC4D,WAAW,GAAG,qBAAqB;AAEtD,IAAMV,MAAM,GAAG3D,UAAU,CAACsE,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,YAAY,EAAE;EAChB,CAAC;EACDN,cAAc,EAAE;IACdO,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDZ,KAAK,EAAE;IACLa,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTJ,YAAY,EAAE,EAAE;IAChBK,MAAM,EAAE;EACV,CAAC;EACDV,GAAG,EAAE;IACHQ,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTJ,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAEF,eAAe9D,kBAAkB;AAGjC,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}