{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport color from 'color';\nvar getBorderColor = function getBorderColor(_ref) {\n  var theme = _ref.theme,\n    disabled = _ref.disabled;\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.surfaceDisabled;\n    }\n    return theme.colors.outline;\n  }\n  return undefined;\n};\nvar getBackgroundColor = function getBackgroundColor(_ref2) {\n  var theme = _ref2.theme,\n    isMode = _ref2.isMode,\n    disabled = _ref2.disabled,\n    selected = _ref2.selected,\n    customContainerColor = _ref2.customContainerColor;\n  if (theme.isV3) {\n    if (disabled) {\n      if (isMode('contained') || isMode('contained-tonal')) {\n        return theme.colors.surfaceDisabled;\n      }\n    }\n    if (typeof customContainerColor !== 'undefined') {\n      return customContainerColor;\n    }\n    if (isMode('contained')) {\n      if (selected) {\n        return theme.colors.primary;\n      }\n      return theme.colors.surfaceVariant;\n    }\n    if (isMode('contained-tonal')) {\n      if (selected) {\n        return theme.colors.secondaryContainer;\n      }\n      return theme.colors.surfaceVariant;\n    }\n    if (isMode('outlined')) {\n      if (selected) {\n        return theme.colors.inverseSurface;\n      }\n    }\n  }\n  if (typeof customContainerColor !== 'undefined') {\n    return customContainerColor;\n  }\n  return undefined;\n};\nvar getIconColor = function getIconColor(_ref3) {\n  var theme = _ref3.theme,\n    isMode = _ref3.isMode,\n    disabled = _ref3.disabled,\n    selected = _ref3.selected,\n    customIconColor = _ref3.customIconColor;\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    if (typeof customIconColor !== 'undefined') {\n      return customIconColor;\n    }\n    if (isMode('contained')) {\n      if (selected) {\n        return theme.colors.onPrimary;\n      }\n      return theme.colors.primary;\n    }\n    if (isMode('contained-tonal')) {\n      if (selected) {\n        return theme.colors.onSecondaryContainer;\n      }\n      return theme.colors.onSurfaceVariant;\n    }\n    if (isMode('outlined')) {\n      if (selected) {\n        return theme.colors.inverseOnSurface;\n      }\n      return theme.colors.onSurfaceVariant;\n    }\n    if (selected) {\n      return theme.colors.primary;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  if (typeof customIconColor !== 'undefined') {\n    return customIconColor;\n  }\n  return theme.colors.text;\n};\nvar getRippleColor = function getRippleColor(_ref4) {\n  var theme = _ref4.theme,\n    iconColor = _ref4.iconColor,\n    customRippleColor = _ref4.customRippleColor;\n  if (customRippleColor) {\n    return customRippleColor;\n  }\n  if (theme.isV3) {\n    return color(iconColor).alpha(0.12).rgb().string();\n  }\n  return color(iconColor).alpha(0.32).rgb().string();\n};\nexport var getIconButtonColor = function getIconButtonColor(_ref5) {\n  var theme = _ref5.theme,\n    disabled = _ref5.disabled,\n    mode = _ref5.mode,\n    selected = _ref5.selected,\n    customIconColor = _ref5.customIconColor,\n    customContainerColor = _ref5.customContainerColor,\n    customRippleColor = _ref5.customRippleColor;\n  var isMode = function isMode(modeToCompare) {\n    return mode === modeToCompare;\n  };\n  var baseIconColorProps = {\n    theme: theme,\n    isMode: isMode,\n    disabled: disabled,\n    selected: selected\n  };\n  var iconColor = getIconColor(_objectSpread(_objectSpread({}, baseIconColorProps), {}, {\n    customIconColor: customIconColor\n  }));\n  return {\n    iconColor: iconColor,\n    backgroundColor: getBackgroundColor(_objectSpread(_objectSpread({}, baseIconColorProps), {}, {\n      customContainerColor: customContainerColor\n    })),\n    rippleColor: getRippleColor({\n      theme: theme,\n      iconColor: iconColor,\n      customRippleColor: customRippleColor\n    }),\n    borderColor: getBorderColor({\n      theme: theme,\n      disabled: disabled\n    })\n  };\n};", "map": {"version": 3, "names": ["color", "getBorderColor", "_ref", "theme", "disabled", "isV3", "colors", "surfaceDisabled", "outline", "undefined", "getBackgroundColor", "_ref2", "isMode", "selected", "customContainerColor", "primary", "surfaceVariant", "secondaryContainer", "inverseSurface", "getIconColor", "_ref3", "customIconColor", "onSurfaceDisabled", "onPrimary", "onSecondaryContainer", "onSurfaceVariant", "inverseOnSurface", "text", "getRippleColor", "_ref4", "iconColor", "customRippleColor", "alpha", "rgb", "string", "getIconButtonColor", "_ref5", "mode", "modeToCompare", "baseIconColorProps", "_objectSpread", "backgroundColor", "rippleColor", "borderColor"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/IconButton/utils.ts"], "sourcesContent": ["import type { ColorValue } from 'react-native';\n\nimport color from 'color';\n\nimport type { InternalTheme } from '../../types';\n\ntype IconButtonMode = 'outlined' | 'contained' | 'contained-tonal';\n\ntype BaseProps = {\n  theme: InternalTheme;\n  isMode: (mode: IconButtonMode) => boolean;\n  disabled?: boolean;\n  selected?: boolean;\n};\n\nconst getBorderColor = ({\n  theme,\n  disabled,\n}: {\n  theme: InternalTheme;\n  disabled?: boolean;\n}) => {\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.surfaceDisabled;\n    }\n\n    return theme.colors.outline;\n  }\n\n  return undefined;\n};\n\nconst getBackgroundColor = ({\n  theme,\n  isMode,\n  disabled,\n  selected,\n  customContainerColor,\n}: BaseProps & { customContainerColor?: string }) => {\n  if (theme.isV3) {\n    if (disabled) {\n      if (isMode('contained') || isMode('contained-tonal')) {\n        return theme.colors.surfaceDisabled;\n      }\n    }\n\n    if (typeof customContainerColor !== 'undefined') {\n      return customContainerColor;\n    }\n\n    if (isMode('contained')) {\n      if (selected) {\n        return theme.colors.primary;\n      }\n      return theme.colors.surfaceVariant;\n    }\n\n    if (isMode('contained-tonal')) {\n      if (selected) {\n        return theme.colors.secondaryContainer;\n      }\n      return theme.colors.surfaceVariant;\n    }\n\n    if (isMode('outlined')) {\n      if (selected) {\n        return theme.colors.inverseSurface;\n      }\n    }\n  }\n\n  if (typeof customContainerColor !== 'undefined') {\n    return customContainerColor;\n  }\n\n  return undefined;\n};\n\nconst getIconColor = ({\n  theme,\n  isMode,\n  disabled,\n  selected,\n  customIconColor,\n}: BaseProps & { customIconColor?: string }) => {\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n\n    if (typeof customIconColor !== 'undefined') {\n      return customIconColor;\n    }\n\n    if (isMode('contained')) {\n      if (selected) {\n        return theme.colors.onPrimary;\n      }\n      return theme.colors.primary;\n    }\n\n    if (isMode('contained-tonal')) {\n      if (selected) {\n        return theme.colors.onSecondaryContainer;\n      }\n      return theme.colors.onSurfaceVariant;\n    }\n\n    if (isMode('outlined')) {\n      if (selected) {\n        return theme.colors.inverseOnSurface;\n      }\n      return theme.colors.onSurfaceVariant;\n    }\n\n    if (selected) {\n      return theme.colors.primary;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n\n  if (typeof customIconColor !== 'undefined') {\n    return customIconColor;\n  }\n\n  return theme.colors.text;\n};\n\nconst getRippleColor = ({\n  theme,\n  iconColor,\n  customRippleColor,\n}: {\n  theme: InternalTheme;\n  iconColor: string;\n  customRippleColor?: ColorValue;\n}) => {\n  if (customRippleColor) {\n    return customRippleColor;\n  }\n  if (theme.isV3) {\n    return color(iconColor).alpha(0.12).rgb().string();\n  }\n  return color(iconColor).alpha(0.32).rgb().string();\n};\n\nexport const getIconButtonColor = ({\n  theme,\n  disabled,\n  mode,\n  selected,\n  customIconColor,\n  customContainerColor,\n  customRippleColor,\n}: {\n  theme: InternalTheme;\n  disabled?: boolean;\n  selected?: boolean;\n  mode?: IconButtonMode;\n  customIconColor?: string;\n  customContainerColor?: string;\n  customRippleColor?: ColorValue;\n}) => {\n  const isMode = (modeToCompare: IconButtonMode) => {\n    return mode === modeToCompare;\n  };\n\n  const baseIconColorProps = {\n    theme,\n    isMode,\n    disabled,\n    selected,\n  };\n\n  const iconColor = getIconColor({\n    ...baseIconColorProps,\n    customIconColor,\n  });\n\n  return {\n    iconColor,\n    backgroundColor: getBackgroundColor({\n      ...baseIconColorProps,\n      customContainerColor,\n    }),\n    rippleColor: getRippleColor({ theme, iconColor, customRippleColor }),\n    borderColor: getBorderColor({ theme, disabled }),\n  };\n};\n"], "mappings": ";;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAazB,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,IAAA,EAMjB;EAAA,IALJC,KAAK,GAKND,IAAA,CALCC,KAAK;IACLC,QAAA,GAIDF,IAAA,CAJCE,QAAA;EAKA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,IAAID,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACG,MAAM,CAACC,eAAe;IACrC;IAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,OAAO;EAC7B;EAEA,OAAOC,SAAS;AAClB,CAAC;AAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGC,KAAA,EAM0B;EAAA,IALnDR,KAAK,GAKyCQ,KAAA,CAL9CR,KAAK;IACLS,MAAM,GAIwCD,KAAA,CAJ9CC,MAAM;IACNR,QAAQ,GAGsCO,KAAA,CAH9CP,QAAQ;IACRS,QAAQ,GAEsCF,KAAA,CAF9CE,QAAQ;IACRC,oBAAA,GAC8CH,KAAA,CAD9CG,oBAAA;EAEA,IAAIX,KAAK,CAACE,IAAI,EAAE;IACd,IAAID,QAAQ,EAAE;MACZ,IAAIQ,MAAM,CAAC,WAAW,CAAC,IAAIA,MAAM,CAAC,iBAAiB,CAAC,EAAE;QACpD,OAAOT,KAAK,CAACG,MAAM,CAACC,eAAe;MACrC;IACF;IAEA,IAAI,OAAOO,oBAAoB,KAAK,WAAW,EAAE;MAC/C,OAAOA,oBAAoB;IAC7B;IAEA,IAAIF,MAAM,CAAC,WAAW,CAAC,EAAE;MACvB,IAAIC,QAAQ,EAAE;QACZ,OAAOV,KAAK,CAACG,MAAM,CAACS,OAAO;MAC7B;MACA,OAAOZ,KAAK,CAACG,MAAM,CAACU,cAAc;IACpC;IAEA,IAAIJ,MAAM,CAAC,iBAAiB,CAAC,EAAE;MAC7B,IAAIC,QAAQ,EAAE;QACZ,OAAOV,KAAK,CAACG,MAAM,CAACW,kBAAkB;MACxC;MACA,OAAOd,KAAK,CAACG,MAAM,CAACU,cAAc;IACpC;IAEA,IAAIJ,MAAM,CAAC,UAAU,CAAC,EAAE;MACtB,IAAIC,QAAQ,EAAE;QACZ,OAAOV,KAAK,CAACG,MAAM,CAACY,cAAc;MACpC;IACF;EACF;EAEA,IAAI,OAAOJ,oBAAoB,KAAK,WAAW,EAAE;IAC/C,OAAOA,oBAAoB;EAC7B;EAEA,OAAOL,SAAS;AAClB,CAAC;AAED,IAAMU,YAAY,GAAG,SAAfA,YAAYA,CAAGC,KAAA,EAM2B;EAAA,IAL9CjB,KAAK,GAKoCiB,KAAA,CALzCjB,KAAK;IACLS,MAAM,GAImCQ,KAAA,CAJzCR,MAAM;IACNR,QAAQ,GAGiCgB,KAAA,CAHzChB,QAAQ;IACRS,QAAQ,GAEiCO,KAAA,CAFzCP,QAAQ;IACRQ,eAAA,GACyCD,KAAA,CADzCC,eAAA;EAEA,IAAIlB,KAAK,CAACE,IAAI,EAAE;IACd,IAAID,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACG,MAAM,CAACgB,iBAAiB;IACvC;IAEA,IAAI,OAAOD,eAAe,KAAK,WAAW,EAAE;MAC1C,OAAOA,eAAe;IACxB;IAEA,IAAIT,MAAM,CAAC,WAAW,CAAC,EAAE;MACvB,IAAIC,QAAQ,EAAE;QACZ,OAAOV,KAAK,CAACG,MAAM,CAACiB,SAAS;MAC/B;MACA,OAAOpB,KAAK,CAACG,MAAM,CAACS,OAAO;IAC7B;IAEA,IAAIH,MAAM,CAAC,iBAAiB,CAAC,EAAE;MAC7B,IAAIC,QAAQ,EAAE;QACZ,OAAOV,KAAK,CAACG,MAAM,CAACkB,oBAAoB;MAC1C;MACA,OAAOrB,KAAK,CAACG,MAAM,CAACmB,gBAAgB;IACtC;IAEA,IAAIb,MAAM,CAAC,UAAU,CAAC,EAAE;MACtB,IAAIC,QAAQ,EAAE;QACZ,OAAOV,KAAK,CAACG,MAAM,CAACoB,gBAAgB;MACtC;MACA,OAAOvB,KAAK,CAACG,MAAM,CAACmB,gBAAgB;IACtC;IAEA,IAAIZ,QAAQ,EAAE;MACZ,OAAOV,KAAK,CAACG,MAAM,CAACS,OAAO;IAC7B;IACA,OAAOZ,KAAK,CAACG,MAAM,CAACmB,gBAAgB;EACtC;EAEA,IAAI,OAAOJ,eAAe,KAAK,WAAW,EAAE;IAC1C,OAAOA,eAAe;EACxB;EAEA,OAAOlB,KAAK,CAACG,MAAM,CAACqB,IAAI;AAC1B,CAAC;AAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,KAAA,EAQjB;EAAA,IAPJ1B,KAAK,GAON0B,KAAA,CAPC1B,KAAK;IACL2B,SAAS,GAMVD,KAAA,CANCC,SAAS;IACTC,iBAAA,GAKDF,KAAA,CALCE,iBAAA;EAMA,IAAIA,iBAAiB,EAAE;IACrB,OAAOA,iBAAiB;EAC1B;EACA,IAAI5B,KAAK,CAACE,IAAI,EAAE;IACd,OAAOL,KAAK,CAAC8B,SAAS,CAAC,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACpD;EACA,OAAOlC,KAAK,CAAC8B,SAAS,CAAC,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACpD,CAAC;AAED,OAAO,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGC,KAAA,EAgB5B;EAAA,IAfJjC,KAAK,GAeNiC,KAAA,CAfCjC,KAAK;IACLC,QAAQ,GAcTgC,KAAA,CAdChC,QAAQ;IACRiC,IAAI,GAaLD,KAAA,CAbCC,IAAI;IACJxB,QAAQ,GAYTuB,KAAA,CAZCvB,QAAQ;IACRQ,eAAe,GAWhBe,KAAA,CAXCf,eAAe;IACfP,oBAAoB,GAUrBsB,KAAA,CAVCtB,oBAAoB;IACpBiB,iBAAA,GASDK,KAAA,CATCL,iBAAA;EAUA,IAAMnB,MAAM,GAAI,SAAVA,MAAMA,CAAI0B,aAA6B,EAAK;IAChD,OAAOD,IAAI,KAAKC,aAAa;EAC/B,CAAC;EAED,IAAMC,kBAAkB,GAAG;IACzBpC,KAAK,EAALA,KAAK;IACLS,MAAM,EAANA,MAAM;IACNR,QAAQ,EAARA,QAAQ;IACRS,QAAA,EAAAA;EACF,CAAC;EAED,IAAMiB,SAAS,GAAGX,YAAY,CAAAqB,aAAA,CAAAA,aAAA,KACzBD,kBAAkB;IACrBlB,eAAA,EAAAA;EAAA,EACD,CAAC;EAEF,OAAO;IACLS,SAAS,EAATA,SAAS;IACTW,eAAe,EAAE/B,kBAAkB,CAAA8B,aAAA,CAAAA,aAAA,KAC9BD,kBAAkB;MACrBzB,oBAAA,EAAAA;IAAA,EACD,CAAC;IACF4B,WAAW,EAAEd,cAAc,CAAC;MAAEzB,KAAK,EAALA,KAAK;MAAE2B,SAAS,EAATA,SAAS;MAAEC,iBAAA,EAAAA;IAAkB,CAAC,CAAC;IACpEY,WAAW,EAAE1C,cAAc,CAAC;MAAEE,KAAK,EAALA,KAAK;MAAEC,QAAA,EAAAA;IAAS,CAAC;EACjD,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}