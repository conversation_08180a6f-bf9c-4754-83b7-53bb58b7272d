{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"size\", \"style\", \"theme\"],\n  _excluded2 = [\"backgroundColor\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { white } from \"../../styles/themes/v2/colors\";\nimport getContrastingColor from \"../../utils/getContrastingColor\";\nimport Icon from \"../Icon\";\nvar defaultSize = 64;\nvar Avatar = function Avatar(_ref) {\n  var _rest$color;\n  var _theme$colors;\n  var icon = _ref.icon,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? defaultSize : _ref$size,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$backgroundColor = _ref2.backgroundColor,\n    backgroundColor = _ref2$backgroundColor === void 0 ? (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.primary : _ref2$backgroundColor,\n    restStyle = _objectWithoutProperties(_ref2, _excluded2);\n  var textColor = (_rest$color = rest.color) != null ? _rest$color : getContrastingColor(backgroundColor, white, 'rgba(0, 0, 0, .54)');\n  return React.createElement(View, _extends({\n    style: [{\n      width: size,\n      height: size,\n      borderRadius: size / 2,\n      backgroundColor: backgroundColor\n    }, styles.container, restStyle]\n  }, rest), React.createElement(Icon, {\n    source: icon,\n    color: textColor,\n    size: size * 0.6\n  }));\n};\nAvatar.displayName = 'Avatar.Icon';\nvar styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center'\n  }\n});\nexport default Avatar;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "white", "getContrastingColor", "Icon", "defaultSize", "Avatar", "_ref", "_rest$color", "_theme$colors", "icon", "_ref$size", "size", "style", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "_ref2", "flatten", "_ref2$backgroundColor", "backgroundColor", "colors", "primary", "restStyle", "_excluded2", "textColor", "color", "createElement", "_extends", "width", "height", "borderRadius", "styles", "container", "source", "displayName", "create", "justifyContent", "alignItems"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Avatar/AvatarIcon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport { white } from '../../styles/themes/v2/colors';\nimport type { ThemeProp } from '../../types';\nimport getContrastingColor from '../../utils/getContrastingColor';\nimport Icon, { IconSource } from '../Icon';\n\nconst defaultSize = 64;\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Icon to display for the `Avatar`.\n   */\n  icon: IconSource;\n  /**\n   * Size of the avatar.\n   */\n  size?: number;\n  /**\n   * Custom color for the icon.\n   */\n  color?: string;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Avatars can be used to represent people in a graphical way.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Avatar } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Avatar.Icon size={24} icon=\"folder\" />\n * );\n * ```\n */\nconst Avatar = ({\n  icon,\n  size = defaultSize,\n  style,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { backgroundColor = theme.colors?.primary, ...restStyle } =\n    StyleSheet.flatten(style) || {};\n  const textColor =\n    rest.color ??\n    getContrastingColor(backgroundColor, white, 'rgba(0, 0, 0, .54)');\n\n  return (\n    <View\n      style={[\n        {\n          width: size,\n          height: size,\n          borderRadius: size / 2,\n          backgroundColor,\n        },\n        styles.container,\n        restStyle,\n      ]}\n      {...rest}\n    >\n      <Icon source={icon} color={textColor} size={size * 0.6} />\n    </View>\n  );\n};\n\nAvatar.displayName = 'Avatar.Icon';\n\nconst styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n});\n\nexport default Avatar;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,SAASC,gBAAgB;AACzB,SAASC,KAAK;AAEd,OAAOC,mBAAmB;AAC1B,OAAOC,IAAI;AAEX,IAAMC,WAAW,GAAG,EAAE;AAmCtB,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAGC,IAAA,EAMF;EAAA,IAAAC,WAAA;EAAA,IAAAC,aAAA;EAAA,IALXC,IAAI,GAKEH,IAAA,CALNG,IAAI;IAAAC,SAAA,GAKEJ,IAAA,CAJNK,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAGN,WAAW,GAAAM,SAAA;IAClBE,KAAK,GAGCN,IAAA,CAHNM,KAAK;IACEC,cAAc,GAEfP,IAAA,CAFNQ,KAAK;IACFC,IAAA,GAAAC,wBAAA,CACGV,IAAA,EAAAW,SAAA;EACN,IAAMH,KAAK,GAAGd,gBAAgB,CAACa,cAAc,CAAC;EAC9C,IAAAK,KAAA,GACEpB,UAAU,CAACqB,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAC;IAAAQ,qBAAA,GAAAF,KAAA,CADzBG,eAAe;IAAfA,eAAe,GAAAD,qBAAA,eAAAZ,aAAA,GAAGM,KAAK,CAACQ,MAAM,cAAAd,aAAA,uBAAZA,aAAA,CAAce,OAAO,GAAAH,qBAAA;IAAKI,SAAA,GAAAR,wBAAA,CAAAE,KAAA,EAAAO,UAAA;EAEpD,IAAMC,SAAS,IAAAnB,WAAA,GACbQ,IAAI,CAACY,KAAK,YAAApB,WAAA,GACVL,mBAAmB,CAACmB,eAAe,EAAEpB,KAAK,EAAE,oBAAoB,CAAC;EAEnE,OACEJ,KAAA,CAAA+B,aAAA,CAAC7B,IAAI,EAAA8B,QAAA;IACHjB,KAAK,EAAE,CACL;MACEkB,KAAK,EAAEnB,IAAI;MACXoB,MAAM,EAAEpB,IAAI;MACZqB,YAAY,EAAErB,IAAI,GAAG,CAAC;MACtBU,eAAA,EAAAA;IACF,CAAC,EACDY,MAAM,CAACC,SAAS,EAChBV,SAAS;EACT,GACET,IAAI,GAERlB,KAAA,CAAA+B,aAAA,CAACzB,IAAI;IAACgC,MAAM,EAAE1B,IAAK;IAACkB,KAAK,EAAED,SAAU;IAACf,IAAI,EAAEA,IAAI,GAAG;EAAI,CAAE,CACrD,CAAC;AAEX,CAAC;AAEDN,MAAM,CAAC+B,WAAW,GAAG,aAAa;AAElC,IAAMH,MAAM,GAAGnC,UAAU,CAACuC,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAelC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}