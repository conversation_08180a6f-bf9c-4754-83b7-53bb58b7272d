{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Provider as PaperProvider } from 'react-native-paper';\nimport { DEFAULT_THEME } from \"./app/src/theme/theme\";\nvar Tab = createBottomTabNavigator();\nvar WebNotice = function WebNotice() {\n  return React.createElement(View, {\n    style: styles.webNotice\n  }, React.createElement(MaterialIcons, {\n    name: \"info\",\n    size: 20,\n    color: \"#2196F3\"\n  }), React.createElement(Text, {\n    style: styles.webNoticeText\n  }, \"Web version has limited functionality. For full features, use the mobile app.\"));\n};\nvar WebHomeScreen = function WebHomeScreen(_ref) {\n  var navigation = _ref.navigation;\n  return React.createElement(View, {\n    style: styles.container\n  }, React.createElement(WebNotice, null), React.createElement(View, {\n    style: styles.content\n  }, React.createElement(Text, {\n    style: styles.title\n  }, \"Zn\\xFCniZ\\xE4hler\"), React.createElement(Text, {\n    style: styles.subtitle\n  }, \"Smart Nutrition Tracking\"), React.createElement(View, {\n    style: styles.featureList\n  }, React.createElement(View, {\n    style: styles.feature\n  }, React.createElement(MaterialIcons, {\n    name: \"document-scanner\",\n    size: 24,\n    color: \"#4CAF50\"\n  }), React.createElement(Text, {\n    style: styles.featureText\n  }, \"OCR Label Scanning (Mobile Only)\")), React.createElement(View, {\n    style: styles.feature\n  }, React.createElement(MaterialIcons, {\n    name: \"qr-code-scanner\",\n    size: 24,\n    color: \"#2196F3\"\n  }), React.createElement(Text, {\n    style: styles.featureText\n  }, \"Barcode Scanning (Mobile Only)\")), React.createElement(View, {\n    style: styles.feature\n  }, React.createElement(MaterialIcons, {\n    name: \"edit\",\n    size: 24,\n    color: \"#FF9800\"\n  }), React.createElement(Text, {\n    style: styles.featureText\n  }, \"Manual Food Entry\")), React.createElement(View, {\n    style: styles.feature\n  }, React.createElement(MaterialIcons, {\n    name: \"analytics\",\n    size: 24,\n    color: \"#9C27B0\"\n  }), React.createElement(Text, {\n    style: styles.featureText\n  }, \"Nutrition Analytics\"))), React.createElement(View, {\n    style: styles.downloadSection\n  }, React.createElement(Text, {\n    style: styles.downloadTitle\n  }, \"Get the Full Experience\"), React.createElement(Text, {\n    style: styles.downloadText\n  }, \"Download the mobile app for OCR scanning, barcode lookup, and offline functionality.\"), React.createElement(View, {\n    style: styles.downloadButtons\n  }, React.createElement(View, {\n    style: styles.downloadButton\n  }, React.createElement(MaterialIcons, {\n    name: \"android\",\n    size: 24,\n    color: \"#3DDC84\"\n  }), React.createElement(Text, {\n    style: styles.downloadButtonText\n  }, \"Android (Coming Soon)\")), React.createElement(View, {\n    style: styles.downloadButton\n  }, React.createElement(MaterialIcons, {\n    name: \"apple\",\n    size: 24,\n    color: \"#000\"\n  }), React.createElement(Text, {\n    style: styles.downloadButtonText\n  }, \"iOS (Coming Soon)\"))))));\n};\nvar WebFoodScreen = function WebFoodScreen() {\n  return React.createElement(View, {\n    style: styles.container\n  }, React.createElement(WebNotice, null), React.createElement(View, {\n    style: styles.content\n  }, React.createElement(Text, {\n    style: styles.title\n  }, \"Food Database\"), React.createElement(Text, {\n    style: styles.subtitle\n  }, \"Food database features are optimized for mobile devices with local storage.\"), React.createElement(View, {\n    style: styles.comingSoon\n  }, React.createElement(MaterialIcons, {\n    name: \"restaurant\",\n    size: 48,\n    color: \"#ccc\"\n  }), React.createElement(Text, {\n    style: styles.comingSoonText\n  }, \"Web food database coming soon!\"))));\n};\nvar WebProfileScreen = function WebProfileScreen() {\n  return React.createElement(View, {\n    style: styles.container\n  }, React.createElement(WebNotice, null), React.createElement(View, {\n    style: styles.content\n  }, React.createElement(Text, {\n    style: styles.title\n  }, \"Profile & Settings\"), React.createElement(Text, {\n    style: styles.subtitle\n  }, \"User profiles and settings are stored locally on mobile devices.\"), React.createElement(View, {\n    style: styles.comingSoon\n  }, React.createElement(MaterialIcons, {\n    name: \"person\",\n    size: 48,\n    color: \"#ccc\"\n  }), React.createElement(Text, {\n    style: styles.comingSoonText\n  }, \"Web profiles coming soon!\"))));\n};\nexport default function App() {\n  return React.createElement(PaperProvider, {\n    theme: DEFAULT_THEME\n  }, React.createElement(NavigationContainer, null, React.createElement(StatusBar, {\n    style: \"auto\"\n  }), React.createElement(Tab.Navigator, {\n    screenOptions: function screenOptions(_ref2) {\n      var route = _ref2.route;\n      return {\n        tabBarIcon: function tabBarIcon(_ref3) {\n          var focused = _ref3.focused,\n            color = _ref3.color,\n            size = _ref3.size;\n          var iconName;\n          if (route.name === 'Home') {\n            iconName = 'home';\n          } else if (route.name === 'Food') {\n            iconName = 'restaurant';\n          } else if (route.name === 'Profile') {\n            iconName = 'person';\n          }\n          return React.createElement(MaterialIcons, {\n            name: iconName,\n            size: size,\n            color: color\n          });\n        },\n        tabBarActiveTintColor: '#4CAF50',\n        tabBarInactiveTintColor: 'gray',\n        headerStyle: {\n          backgroundColor: '#4CAF50'\n        },\n        headerTintColor: '#fff',\n        headerTitleStyle: {\n          fontWeight: 'bold'\n        }\n      };\n    }\n  }, React.createElement(Tab.Screen, {\n    name: \"Home\",\n    component: WebHomeScreen,\n    options: {\n      title: 'ZnüniZähler'\n    }\n  }), React.createElement(Tab.Screen, {\n    name: \"Food\",\n    component: WebFoodScreen,\n    options: {\n      title: 'Food Database'\n    }\n  }), React.createElement(Tab.Screen, {\n    name: \"Profile\",\n    component: WebProfileScreen,\n    options: {\n      title: 'Profile'\n    }\n  }))));\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  webNotice: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    backgroundColor: '#e3f2fd',\n    padding: 12,\n    borderBottomWidth: 1,\n    borderBottomColor: '#bbdefb'\n  },\n  webNoticeText: {\n    marginLeft: 8,\n    color: '#1976d2',\n    fontSize: 14\n  },\n  content: {\n    flex: 1,\n    padding: 20,\n    alignItems: 'center'\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    color: '#4CAF50',\n    marginBottom: 8,\n    textAlign: 'center'\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    marginBottom: 30,\n    textAlign: 'center',\n    maxWidth: 400\n  },\n  featureList: {\n    width: '100%',\n    maxWidth: 400,\n    marginBottom: 40\n  },\n  feature: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 15,\n    backgroundColor: '#fff',\n    marginBottom: 10,\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 1\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2\n  },\n  featureText: {\n    marginLeft: 15,\n    fontSize: 16,\n    color: '#333'\n  },\n  downloadSection: {\n    alignItems: 'center',\n    maxWidth: 400\n  },\n  downloadTitle: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 10\n  },\n  downloadText: {\n    fontSize: 14,\n    color: '#666',\n    textAlign: 'center',\n    marginBottom: 20\n  },\n  downloadButtons: {\n    flexDirection: 'row',\n    gap: 15\n  },\n  downloadButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 12,\n    backgroundColor: '#fff',\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 1\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2\n  },\n  downloadButtonText: {\n    marginLeft: 8,\n    fontSize: 14,\n    color: '#333'\n  },\n  comingSoon: {\n    alignItems: 'center',\n    marginTop: 50\n  },\n  comingSoonText: {\n    fontSize: 18,\n    color: '#999',\n    marginTop: 15\n  }\n});", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "Platform", "StatusBar", "NavigationContainer", "createBottomTabNavigator", "MaterialIcons", "Provider", "PaperProvider", "DEFAULT_THEME", "Tab", "WebNotice", "createElement", "style", "styles", "webNotice", "name", "size", "color", "webNoticeText", "WebHomeScreen", "_ref", "navigation", "container", "content", "title", "subtitle", "featureList", "feature", "featureText", "downloadSection", "downloadTitle", "downloadText", "downloadButtons", "downloadButton", "downloadButtonText", "WebFoodScreen", "comingSoon", "comingSoonText", "WebProfileScreen", "App", "theme", "Navigator", "screenOptions", "_ref2", "route", "tabBarIcon", "_ref3", "focused", "iconName", "tabBarActiveTintColor", "tabBarInactiveTintColor", "headerStyle", "backgroundColor", "headerTintColor", "headerTitleStyle", "fontWeight", "Screen", "component", "options", "create", "flex", "flexDirection", "alignItems", "padding", "borderBottomWidth", "borderBottomColor", "marginLeft", "fontSize", "marginBottom", "textAlign", "max<PERSON><PERSON><PERSON>", "width", "borderRadius", "shadowColor", "shadowOffset", "height", "shadowOpacity", "shadowRadius", "elevation", "gap", "marginTop"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/App.web.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet, Platform } from 'react-native';\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Provider as PaperProvider } from 'react-native-paper';\n\n// Import theme\nimport { DEFAULT_THEME } from './app/src/theme/theme';\n\nconst Tab = createBottomTabNavigator();\n\n// Web-specific notice component\nconst WebNotice = () => (\n  <View style={styles.webNotice}>\n    <MaterialIcons name=\"info\" size={20} color=\"#2196F3\" />\n    <Text style={styles.webNoticeText}>\n      Web version has limited functionality. For full features, use the mobile app.\n    </Text>\n  </View>\n);\n\n// Web-compatible Home Screen\nconst WebHomeScreen = ({ navigation }) => (\n  <View style={styles.container}>\n    <WebNotice />\n    <View style={styles.content}>\n      <Text style={styles.title}>ZnüniZähler</Text>\n      <Text style={styles.subtitle}>Smart Nutrition Tracking</Text>\n\n      <View style={styles.featureList}>\n        <View style={styles.feature}>\n          <MaterialIcons name=\"document-scanner\" size={24} color=\"#4CAF50\" />\n          <Text style={styles.featureText}>OCR Label Scanning (Mobile Only)</Text>\n        </View>\n\n        <View style={styles.feature}>\n          <MaterialIcons name=\"qr-code-scanner\" size={24} color=\"#2196F3\" />\n          <Text style={styles.featureText}>Barcode Scanning (Mobile Only)</Text>\n        </View>\n\n        <View style={styles.feature}>\n          <MaterialIcons name=\"edit\" size={24} color=\"#FF9800\" />\n          <Text style={styles.featureText}>Manual Food Entry</Text>\n        </View>\n\n        <View style={styles.feature}>\n          <MaterialIcons name=\"analytics\" size={24} color=\"#9C27B0\" />\n          <Text style={styles.featureText}>Nutrition Analytics</Text>\n        </View>\n      </View>\n\n      <View style={styles.downloadSection}>\n        <Text style={styles.downloadTitle}>Get the Full Experience</Text>\n        <Text style={styles.downloadText}>\n          Download the mobile app for OCR scanning, barcode lookup, and offline functionality.\n        </Text>\n\n        <View style={styles.downloadButtons}>\n          <View style={styles.downloadButton}>\n            <MaterialIcons name=\"android\" size={24} color=\"#3DDC84\" />\n            <Text style={styles.downloadButtonText}>Android (Coming Soon)</Text>\n          </View>\n\n          <View style={styles.downloadButton}>\n            <MaterialIcons name=\"apple\" size={24} color=\"#000\" />\n            <Text style={styles.downloadButtonText}>iOS (Coming Soon)</Text>\n          </View>\n        </View>\n      </View>\n    </View>\n  </View>\n);\n\n// Web-compatible Food Screen\nconst WebFoodScreen = () => (\n  <View style={styles.container}>\n    <WebNotice />\n    <View style={styles.content}>\n      <Text style={styles.title}>Food Database</Text>\n      <Text style={styles.subtitle}>\n        Food database features are optimized for mobile devices with local storage.\n      </Text>\n\n      <View style={styles.comingSoon}>\n        <MaterialIcons name=\"restaurant\" size={48} color=\"#ccc\" />\n        <Text style={styles.comingSoonText}>Web food database coming soon!</Text>\n      </View>\n    </View>\n  </View>\n);\n\n// Web-compatible Profile Screen\nconst WebProfileScreen = () => (\n  <View style={styles.container}>\n    <WebNotice />\n    <View style={styles.content}>\n      <Text style={styles.title}>Profile & Settings</Text>\n      <Text style={styles.subtitle}>\n        User profiles and settings are stored locally on mobile devices.\n      </Text>\n\n      <View style={styles.comingSoon}>\n        <MaterialIcons name=\"person\" size={48} color=\"#ccc\" />\n        <Text style={styles.comingSoonText}>Web profiles coming soon!</Text>\n      </View>\n    </View>\n  </View>\n);\n\nexport default function App() {\n  return (\n    <PaperProvider theme={DEFAULT_THEME}>\n      <NavigationContainer>\n        <StatusBar style=\"auto\" />\n        <Tab.Navigator\n          screenOptions={({ route }) => ({\n            tabBarIcon: ({ focused, color, size }) => {\n              let iconName;\n\n              if (route.name === 'Home') {\n                iconName = 'home';\n              } else if (route.name === 'Food') {\n                iconName = 'restaurant';\n              } else if (route.name === 'Profile') {\n                iconName = 'person';\n              }\n\n              return <MaterialIcons name={iconName} size={size} color={color} />;\n            },\n            tabBarActiveTintColor: '#4CAF50',\n            tabBarInactiveTintColor: 'gray',\n            headerStyle: {\n              backgroundColor: '#4CAF50',\n            },\n            headerTintColor: '#fff',\n            headerTitleStyle: {\n              fontWeight: 'bold',\n            },\n          })}\n        >\n          <Tab.Screen\n            name=\"Home\"\n            component={WebHomeScreen}\n            options={{ title: 'ZnüniZähler' }}\n          />\n          <Tab.Screen\n            name=\"Food\"\n            component={WebFoodScreen}\n            options={{ title: 'Food Database' }}\n          />\n          <Tab.Screen\n            name=\"Profile\"\n            component={WebProfileScreen}\n            options={{ title: 'Profile' }}\n          />\n        </Tab.Navigator>\n      </NavigationContainer>\n    </PaperProvider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  webNotice: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    backgroundColor: '#e3f2fd',\n    padding: 12,\n    borderBottomWidth: 1,\n    borderBottomColor: '#bbdefb',\n  },\n  webNoticeText: {\n    marginLeft: 8,\n    color: '#1976d2',\n    fontSize: 14,\n  },\n  content: {\n    flex: 1,\n    padding: 20,\n    alignItems: 'center',\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    color: '#4CAF50',\n    marginBottom: 8,\n    textAlign: 'center',\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    marginBottom: 30,\n    textAlign: 'center',\n    maxWidth: 400,\n  },\n  featureList: {\n    width: '100%',\n    maxWidth: 400,\n    marginBottom: 40,\n  },\n  feature: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 15,\n    backgroundColor: '#fff',\n    marginBottom: 10,\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 1 },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2,\n  },\n  featureText: {\n    marginLeft: 15,\n    fontSize: 16,\n    color: '#333',\n  },\n  downloadSection: {\n    alignItems: 'center',\n    maxWidth: 400,\n  },\n  downloadTitle: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 10,\n  },\n  downloadText: {\n    fontSize: 14,\n    color: '#666',\n    textAlign: 'center',\n    marginBottom: 20,\n  },\n  downloadButtons: {\n    flexDirection: 'row',\n    gap: 15,\n  },\n  downloadButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 12,\n    backgroundColor: '#fff',\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 1 },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2,\n  },\n  downloadButtonText: {\n    marginLeft: 8,\n    fontSize: 14,\n    color: '#333',\n  },\n  comingSoon: {\n    alignItems: 'center',\n    marginTop: 50,\n  },\n  comingSoonText: {\n    fontSize: 18,\n    color: '#999',\n    marginTop: 15,\n  },\n});\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAE1B,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQ,IAAIC,aAAa,QAAQ,oBAAoB;AAG9D,SAASC,aAAa;AAEtB,IAAMC,GAAG,GAAGL,wBAAwB,CAAC,CAAC;AAGtC,IAAMM,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OACbb,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACC;EAAU,GAC5BjB,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,MAAM;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAS,CAAE,CAAC,EACvDpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACK;EAAc,GAAC,+EAE7B,CACF,CAAC;AAAA,CACR;AAGD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA;EAAA,IAAMC,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAAA,OACjCxB,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACS;EAAU,GAC5BzB,KAAA,CAAAc,aAAA,CAACD,SAAS,MAAE,CAAC,EACbb,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACU;EAAQ,GAC1B1B,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACW;EAAM,GAAC,mBAAiB,CAAC,EAC7C3B,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACY;EAAS,GAAC,0BAA8B,CAAC,EAE7D5B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACa;EAAY,GAC9B7B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACc;EAAQ,GAC1B9B,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,kBAAkB;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAS,CAAE,CAAC,EACnEpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACe;EAAY,GAAC,kCAAsC,CACnE,CAAC,EAEP/B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACc;EAAQ,GAC1B9B,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,iBAAiB;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAS,CAAE,CAAC,EAClEpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACe;EAAY,GAAC,gCAAoC,CACjE,CAAC,EAEP/B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACc;EAAQ,GAC1B9B,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,MAAM;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAS,CAAE,CAAC,EACvDpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACe;EAAY,GAAC,mBAAuB,CACpD,CAAC,EAEP/B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACc;EAAQ,GAC1B9B,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,WAAW;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAS,CAAE,CAAC,EAC5DpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACe;EAAY,GAAC,qBAAyB,CACtD,CACF,CAAC,EAEP/B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACgB;EAAgB,GAClChC,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACiB;EAAc,GAAC,yBAA6B,CAAC,EACjEjC,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACkB;EAAa,GAAC,sFAE5B,CAAC,EAEPlC,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACmB;EAAgB,GAClCnC,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACoB;EAAe,GACjCpC,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,SAAS;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAS,CAAE,CAAC,EAC1DpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACqB;EAAmB,GAAC,uBAA2B,CAC/D,CAAC,EAEPrC,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACoB;EAAe,GACjCpC,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,OAAO;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAM,CAAE,CAAC,EACrDpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACqB;EAAmB,GAAC,mBAAuB,CAC3D,CACF,CACF,CACF,CACF,CAAC;AAAA,CACR;AAGD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OACjBtC,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACS;EAAU,GAC5BzB,KAAA,CAAAc,aAAA,CAACD,SAAS,MAAE,CAAC,EACbb,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACU;EAAQ,GAC1B1B,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACW;EAAM,GAAC,eAAmB,CAAC,EAC/C3B,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACY;EAAS,GAAC,6EAExB,CAAC,EAEP5B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACuB;EAAW,GAC7BvC,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,YAAY;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAM,CAAE,CAAC,EAC1DpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACwB;EAAe,GAAC,gCAAoC,CACpE,CACF,CACF,CAAC;AAAA,CACR;AAGD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OACpBzC,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACS;EAAU,GAC5BzB,KAAA,CAAAc,aAAA,CAACD,SAAS,MAAE,CAAC,EACbb,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACU;EAAQ,GAC1B1B,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACW;EAAM,GAAC,oBAAwB,CAAC,EACpD3B,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACY;EAAS,GAAC,kEAExB,CAAC,EAEP5B,KAAA,CAAAc,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACuB;EAAW,GAC7BvC,KAAA,CAAAc,aAAA,CAACN,aAAa;IAACU,IAAI,EAAC,QAAQ;IAACC,IAAI,EAAE,EAAG;IAACC,KAAK,EAAC;EAAM,CAAE,CAAC,EACtDpB,KAAA,CAAAc,aAAA,CAACZ,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACwB;EAAe,GAAC,2BAA+B,CAC/D,CACF,CACF,CAAC;AAAA,CACR;AAED,eAAe,SAASE,GAAGA,CAAA,EAAG;EAC5B,OACE1C,KAAA,CAAAc,aAAA,CAACJ,aAAa;IAACiC,KAAK,EAAEhC;EAAc,GAClCX,KAAA,CAAAc,aAAA,CAACR,mBAAmB,QAClBN,KAAA,CAAAc,aAAA,CAACT,SAAS;IAACU,KAAK,EAAC;EAAM,CAAE,CAAC,EAC1Bf,KAAA,CAAAc,aAAA,CAACF,GAAG,CAACgC,SAAS;IACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAC,KAAA;MAAA,IAAKC,KAAK,GAAAD,KAAA,CAALC,KAAK;MAAA,OAAQ;QAC7BC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA,EAAgC;UAAA,IAA3BC,OAAO,GAAAD,KAAA,CAAPC,OAAO;YAAE9B,KAAK,GAAA6B,KAAA,CAAL7B,KAAK;YAAED,IAAI,GAAA8B,KAAA,CAAJ9B,IAAI;UACjC,IAAIgC,QAAQ;UAEZ,IAAIJ,KAAK,CAAC7B,IAAI,KAAK,MAAM,EAAE;YACzBiC,QAAQ,GAAG,MAAM;UACnB,CAAC,MAAM,IAAIJ,KAAK,CAAC7B,IAAI,KAAK,MAAM,EAAE;YAChCiC,QAAQ,GAAG,YAAY;UACzB,CAAC,MAAM,IAAIJ,KAAK,CAAC7B,IAAI,KAAK,SAAS,EAAE;YACnCiC,QAAQ,GAAG,QAAQ;UACrB;UAEA,OAAOnD,KAAA,CAAAc,aAAA,CAACN,aAAa;YAACU,IAAI,EAAEiC,QAAS;YAAChC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA;UAAM,CAAE,CAAC;QACpE,CAAC;QACDgC,qBAAqB,EAAE,SAAS;QAChCC,uBAAuB,EAAE,MAAM;QAC/BC,WAAW,EAAE;UACXC,eAAe,EAAE;QACnB,CAAC;QACDC,eAAe,EAAE,MAAM;QACvBC,gBAAgB,EAAE;UAChBC,UAAU,EAAE;QACd;MACF,CAAC;IAAA;EAAE,GAEH1D,KAAA,CAAAc,aAAA,CAACF,GAAG,CAAC+C,MAAM;IACTzC,IAAI,EAAC,MAAM;IACX0C,SAAS,EAAEtC,aAAc;IACzBuC,OAAO,EAAE;MAAElC,KAAK,EAAE;IAAc;EAAE,CACnC,CAAC,EACF3B,KAAA,CAAAc,aAAA,CAACF,GAAG,CAAC+C,MAAM;IACTzC,IAAI,EAAC,MAAM;IACX0C,SAAS,EAAEtB,aAAc;IACzBuB,OAAO,EAAE;MAAElC,KAAK,EAAE;IAAgB;EAAE,CACrC,CAAC,EACF3B,KAAA,CAAAc,aAAA,CAACF,GAAG,CAAC+C,MAAM;IACTzC,IAAI,EAAC,SAAS;IACd0C,SAAS,EAAEnB,gBAAiB;IAC5BoB,OAAO,EAAE;MAAElC,KAAK,EAAE;IAAU;EAAE,CAC/B,CACY,CACI,CACR,CAAC;AAEpB;AAEA,IAAMX,MAAM,GAAGb,UAAU,CAAC2D,MAAM,CAAC;EAC/BrC,SAAS,EAAE;IACTsC,IAAI,EAAE,CAAC;IACPR,eAAe,EAAE;EACnB,CAAC;EACDtC,SAAS,EAAE;IACT+C,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBV,eAAe,EAAE,SAAS;IAC1BW,OAAO,EAAE,EAAE;IACXC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACD/C,aAAa,EAAE;IACbgD,UAAU,EAAE,CAAC;IACbjD,KAAK,EAAE,SAAS;IAChBkD,QAAQ,EAAE;EACZ,CAAC;EACD5C,OAAO,EAAE;IACPqC,IAAI,EAAE,CAAC;IACPG,OAAO,EAAE,EAAE;IACXD,UAAU,EAAE;EACd,CAAC;EACDtC,KAAK,EAAE;IACL2C,QAAQ,EAAE,EAAE;IACZZ,UAAU,EAAE,MAAM;IAClBtC,KAAK,EAAE,SAAS;IAChBmD,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACD5C,QAAQ,EAAE;IACR0C,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE,MAAM;IACbmD,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;EACZ,CAAC;EACD5C,WAAW,EAAE;IACX6C,KAAK,EAAE,MAAM;IACbD,QAAQ,EAAE,GAAG;IACbF,YAAY,EAAE;EAChB,CAAC;EACDzC,OAAO,EAAE;IACPkC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXX,eAAe,EAAE,MAAM;IACvBgB,YAAY,EAAE,EAAE;IAChBI,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAEH,KAAK,EAAE,CAAC;MAAEI,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDlD,WAAW,EAAE;IACXsC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE;EACT,CAAC;EACDY,eAAe,EAAE;IACfiC,UAAU,EAAE,QAAQ;IACpBQ,QAAQ,EAAE;EACZ,CAAC;EACDxC,aAAa,EAAE;IACbqC,QAAQ,EAAE,EAAE;IACZZ,UAAU,EAAE,MAAM;IAClBtC,KAAK,EAAE,MAAM;IACbmD,YAAY,EAAE;EAChB,CAAC;EACDrC,YAAY,EAAE;IACZoC,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE,MAAM;IACboD,SAAS,EAAE,QAAQ;IACnBD,YAAY,EAAE;EAChB,CAAC;EACDpC,eAAe,EAAE;IACf6B,aAAa,EAAE,KAAK;IACpBkB,GAAG,EAAE;EACP,CAAC;EACD9C,cAAc,EAAE;IACd4B,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXX,eAAe,EAAE,MAAM;IACvBoB,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAEH,KAAK,EAAE,CAAC;MAAEI,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACD5C,kBAAkB,EAAE;IAClBgC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE;EACT,CAAC;EACDmB,UAAU,EAAE;IACV0B,UAAU,EAAE,QAAQ;IACpBkB,SAAS,EAAE;EACb,CAAC;EACD3C,cAAc,EAAE;IACd8B,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE,MAAM;IACb+D,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}