{"ast": null, "code": "import * as React from 'react';\nvar CurrentRenderContext = React.createContext(undefined);\nexport default CurrentRenderContext;", "map": {"version": 3, "names": ["React", "CurrentRenderContext", "createContext", "undefined"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/CurrentRenderContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Context which holds the values for the current navigation tree.\n * Intended for use in SSR. This is not safe to use on the client.\n */\nconst CurrentRenderContext = React.createContext<\n  { options?: object } | undefined\n>(undefined);\n\nexport default CurrentRenderContext;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAM9B,IAAMC,oBAAoB,GAAGD,KAAK,CAACE,aAAa,CAE9CC,SAAS,CAAC;AAEZ,eAAeF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}