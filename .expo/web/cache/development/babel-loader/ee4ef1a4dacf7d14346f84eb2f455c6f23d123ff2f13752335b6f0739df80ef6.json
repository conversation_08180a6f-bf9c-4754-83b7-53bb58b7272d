{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { ListAccordionGroupContext } from \"./ListAccordionGroup\";\nimport { getAccordionColors, getLeftStyles } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport MaterialCommunityIcon from \"../MaterialCommunityIcon\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar ListAccordion = function ListAccordion(_ref) {\n  var _theme$colors, _theme$colors2;\n  var left = _ref.left,\n    right = _ref.right,\n    title = _ref.title,\n    description = _ref.description,\n    children = _ref.children,\n    themeOverrides = _ref.theme,\n    titleStyle = _ref.titleStyle,\n    descriptionStyle = _ref.descriptionStyle,\n    _ref$titleNumberOfLin = _ref.titleNumberOfLines,\n    titleNumberOfLines = _ref$titleNumberOfLin === void 0 ? 1 : _ref$titleNumberOfLin,\n    _ref$descriptionNumbe = _ref.descriptionNumberOfLines,\n    descriptionNumberOfLines = _ref$descriptionNumbe === void 0 ? 2 : _ref$descriptionNumbe,\n    customRippleColor = _ref.rippleColor,\n    style = _ref.style,\n    containerStyle = _ref.containerStyle,\n    contentStyle = _ref.contentStyle,\n    id = _ref.id,\n    testID = _ref.testID,\n    background = _ref.background,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    delayLongPress = _ref.delayLongPress,\n    expandedProp = _ref.expanded,\n    accessibilityLabel = _ref.accessibilityLabel,\n    _ref$pointerEvents = _ref.pointerEvents,\n    pointerEvents = _ref$pointerEvents === void 0 ? 'none' : _ref$pointerEvents,\n    titleMaxFontSizeMultiplier = _ref.titleMaxFontSizeMultiplier,\n    descriptionMaxFontSizeMultiplier = _ref.descriptionMaxFontSizeMultiplier;\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useState = React.useState(expandedProp || false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    expanded = _React$useState2[0],\n    setExpanded = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    alignToTop = _React$useState4[0],\n    setAlignToTop = _React$useState4[1];\n  var onDescriptionTextLayout = function onDescriptionTextLayout(event) {\n    if (!theme.isV3) {\n      return;\n    }\n    var nativeEvent = event.nativeEvent;\n    setAlignToTop(nativeEvent.lines.length >= 2);\n  };\n  var handlePressAction = function handlePressAction(e) {\n    onPress === null || onPress === void 0 ? void 0 : onPress(e);\n    if (expandedProp === undefined) {\n      setExpanded(function (expanded) {\n        return !expanded;\n      });\n    }\n  };\n  var expandedInternal = expandedProp !== undefined ? expandedProp : expanded;\n  var groupContext = React.useContext(ListAccordionGroupContext);\n  if (groupContext !== null && (id === undefined || id === null || id === '')) {\n    throw new Error('List.Accordion is used inside a List.AccordionGroup without specifying an id prop.');\n  }\n  var isExpanded = groupContext ? groupContext.expandedId === id : expandedInternal;\n  var _getAccordionColors = getAccordionColors({\n      theme: theme,\n      isExpanded: isExpanded,\n      customRippleColor: customRippleColor\n    }),\n    titleColor = _getAccordionColors.titleColor,\n    descriptionColor = _getAccordionColors.descriptionColor,\n    titleTextColor = _getAccordionColors.titleTextColor,\n    rippleColor = _getAccordionColors.rippleColor;\n  var handlePress = groupContext && id !== undefined ? function () {\n    return groupContext.onAccordionPress(id);\n  } : handlePressAction;\n  return React.createElement(View, null, React.createElement(View, {\n    style: {\n      backgroundColor: theme === null || theme === void 0 || (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.background\n    }\n  }, React.createElement(TouchableRipple, {\n    style: [theme.isV3 ? styles.containerV3 : styles.container, style],\n    onPress: handlePress,\n    onLongPress: onLongPress,\n    delayLongPress: delayLongPress,\n    rippleColor: rippleColor,\n    accessibilityRole: \"button\",\n    accessibilityState: {\n      expanded: isExpanded\n    },\n    accessibilityLabel: accessibilityLabel,\n    testID: testID,\n    theme: theme,\n    background: background,\n    borderless: true\n  }, React.createElement(View, {\n    style: [theme.isV3 ? styles.rowV3 : styles.row, containerStyle],\n    pointerEvents: pointerEvents\n  }, left ? left({\n    color: isExpanded ? (_theme$colors2 = theme.colors) === null || _theme$colors2 === void 0 ? void 0 : _theme$colors2.primary : descriptionColor,\n    style: getLeftStyles(alignToTop, description, theme.isV3)\n  }) : null, React.createElement(View, {\n    style: [theme.isV3 ? styles.itemV3 : styles.item, styles.content, contentStyle]\n  }, React.createElement(Text, {\n    selectable: false,\n    numberOfLines: titleNumberOfLines,\n    style: [styles.title, {\n      color: titleTextColor\n    }, titleStyle],\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }, title), description ? React.createElement(Text, {\n    selectable: false,\n    numberOfLines: descriptionNumberOfLines,\n    style: [styles.description, {\n      color: descriptionColor\n    }, descriptionStyle],\n    onTextLayout: onDescriptionTextLayout,\n    maxFontSizeMultiplier: descriptionMaxFontSizeMultiplier\n  }, description) : null), React.createElement(View, {\n    style: [styles.item, description ? styles.multiline : undefined]\n  }, right ? right({\n    isExpanded: isExpanded\n  }) : React.createElement(MaterialCommunityIcon, {\n    name: isExpanded ? 'chevron-up' : 'chevron-down',\n    color: theme.isV3 ? descriptionColor : titleColor,\n    size: 24,\n    direction: I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'\n  }))))), isExpanded ? React.Children.map(children, function (child) {\n    if (left && React.isValidElement(child) && !child.props.left && !child.props.right) {\n      return React.cloneElement(child, {\n        style: [theme.isV3 ? styles.childV3 : styles.child, child.props.style],\n        theme: theme\n      });\n    }\n    return child;\n  }) : null);\n};\nListAccordion.displayName = 'List.Accordion';\nvar styles = StyleSheet.create({\n  container: {\n    padding: 8\n  },\n  containerV3: {\n    paddingVertical: 8,\n    paddingRight: 24\n  },\n  row: {\n    flexDirection: 'row',\n    alignItems: 'center'\n  },\n  rowV3: {\n    flexDirection: 'row',\n    marginVertical: 6\n  },\n  multiline: {\n    height: 40,\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  title: {\n    fontSize: 16\n  },\n  description: {\n    fontSize: 14\n  },\n  item: {\n    marginVertical: 6,\n    paddingLeft: 8\n  },\n  itemV3: {\n    paddingLeft: 16\n  },\n  child: {\n    paddingLeft: 64\n  },\n  childV3: {\n    paddingLeft: 40\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center'\n  }\n});\nexport default ListAccordion;", "map": {"version": 3, "names": ["React", "I18nManager", "StyleSheet", "View", "ListAccordionGroupContext", "getAccordionColors", "getLeftStyles", "useInternalTheme", "MaterialCommunityIcon", "TouchableRipple", "Text", "ListAccordion", "_ref", "_theme$colors", "_theme$colors2", "left", "right", "title", "description", "children", "themeOverrides", "theme", "titleStyle", "descriptionStyle", "_ref$titleNumberOfLin", "titleNumberOfLines", "_ref$descriptionNumbe", "descriptionNumberOfLines", "customRippleColor", "rippleColor", "style", "containerStyle", "contentStyle", "id", "testID", "background", "onPress", "onLongPress", "delayLongPress", "expandedProp", "expanded", "accessibilityLabel", "_ref$pointerEvents", "pointerEvents", "titleMaxFontSizeMultiplier", "descriptionMaxFontSizeMultiplier", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "setExpanded", "_React$useState3", "_React$useState4", "alignToTop", "setAlignToTop", "onDescriptionTextLayout", "event", "isV3", "nativeEvent", "lines", "length", "handlePressAction", "e", "undefined", "expandedInternal", "groupContext", "useContext", "Error", "isExpanded", "expandedId", "_getAccordionColors", "titleColor", "descriptionColor", "titleTextColor", "handlePress", "onAccordionPress", "createElement", "backgroundColor", "colors", "styles", "containerV3", "container", "accessibilityRole", "accessibilityState", "borderless", "rowV3", "row", "color", "primary", "itemV3", "item", "content", "selectable", "numberOfLines", "maxFontSizeMultiplier", "onTextLayout", "multiline", "name", "size", "direction", "getConstants", "isRTL", "Children", "map", "child", "isValidElement", "props", "cloneElement", "childV3", "displayName", "create", "padding", "paddingVertical", "paddingRight", "flexDirection", "alignItems", "marginVertical", "height", "justifyContent", "fontSize", "paddingLeft", "flex"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/List/ListAccordion.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  ColorV<PERSON>ue,\n  GestureResponderEvent,\n  I18nManager,\n  NativeSyntheticEvent,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  TextLayoutEventData,\n  View,\n  ViewProps,\n  ViewStyle,\n  PressableAndroidRippleConfig,\n} from 'react-native';\n\nimport { ListAccordionGroupContext } from './ListAccordionGroup';\nimport type { Style } from './utils';\nimport { getAccordionColors, getLeftStyles } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport MaterialCommunityIcon from '../MaterialCommunityIcon';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Title text for the list accordion.\n   */\n  title: React.ReactNode;\n  /**\n   * Description text for the list accordion.\n   */\n  description?: React.ReactNode;\n  /**\n   * Callback which returns a React element to display on the left side.\n   */\n  left?: (props: { color: string; style: Style }) => React.ReactNode;\n  /**\n   * Callback which returns a React element to display on the right side.\n   */\n  right?: (props: { isExpanded: boolean }) => React.ReactNode;\n  /**\n   * Whether the accordion is expanded\n   * If this prop is provided, the accordion will behave as a \"controlled component\".\n   * You'll need to update this prop when you want to toggle the component or on `onPress`.\n   */\n  expanded?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * The number of milliseconds a user must touch the element before executing `onLongPress`.\n   */\n  delayLongPress?: number;\n  /**\n   * Content of the section.\n   */\n  children: React.ReactNode;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Style that is passed to the root TouchableRipple container.\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the outermost container that wraps the entire content, including left and right items and both title and description.\n   */\n  containerStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the content container, which wraps the title and description.\n   */\n  contentStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to Title element.\n   */\n  titleStyle?: StyleProp<TextStyle>;\n  /**\n   * Style that is passed to Description element.\n   */\n  descriptionStyle?: StyleProp<TextStyle>;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Truncate Title text such that the total number of lines does not\n   * exceed this number.\n   */\n  titleNumberOfLines?: number;\n  /**\n   * Truncate Description text such that the total number of lines does not\n   * exceed this number.\n   */\n  descriptionNumberOfLines?: number;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  /**\n   * Specifies the largest possible scale a description font can reach.\n   */\n  descriptionMaxFontSizeMultiplier?: number;\n  /**\n   * Id is used for distinguishing specific accordion when using List.AccordionGroup. Property is required when using List.AccordionGroup and has no impact on behavior when using standalone List.Accordion.\n   */\n  id?: string | number;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  /**\n   * Accessibility label for the TouchableRipple. This is read by the screen reader when the user taps the touchable.\n   */\n  accessibilityLabel?: string;\n  /**\n   * `pointerEvents` passed to the `View` container\n   */\n  pointerEvents?: ViewProps['pointerEvents'];\n};\n\n/**\n * A component used to display an expandable list item.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { List } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [expanded, setExpanded] = React.useState(true);\n *\n *   const handlePress = () => setExpanded(!expanded);\n *\n *   return (\n *     <List.Section title=\"Accordions\">\n *       <List.Accordion\n *         title=\"Uncontrolled Accordion\"\n *         left={props => <List.Icon {...props} icon=\"folder\" />}>\n *         <List.Item title=\"First item\" />\n *         <List.Item title=\"Second item\" />\n *       </List.Accordion>\n *\n *       <List.Accordion\n *         title=\"Controlled Accordion\"\n *         left={props => <List.Icon {...props} icon=\"folder\" />}\n *         expanded={expanded}\n *         onPress={handlePress}>\n *         <List.Item title=\"First item\" />\n *         <List.Item title=\"Second item\" />\n *       </List.Accordion>\n *     </List.Section>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst ListAccordion = ({\n  left,\n  right,\n  title,\n  description,\n  children,\n  theme: themeOverrides,\n  titleStyle,\n  descriptionStyle,\n  titleNumberOfLines = 1,\n  descriptionNumberOfLines = 2,\n  rippleColor: customRippleColor,\n  style,\n  containerStyle,\n  contentStyle,\n  id,\n  testID,\n  background,\n  onPress,\n  onLongPress,\n  delayLongPress,\n  expanded: expandedProp,\n  accessibilityLabel,\n  pointerEvents = 'none',\n  titleMaxFontSizeMultiplier,\n  descriptionMaxFontSizeMultiplier,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const [expanded, setExpanded] = React.useState<boolean>(\n    expandedProp || false\n  );\n  const [alignToTop, setAlignToTop] = React.useState(false);\n\n  const onDescriptionTextLayout = (\n    event: NativeSyntheticEvent<TextLayoutEventData>\n  ) => {\n    if (!theme.isV3) {\n      return;\n    }\n    const { nativeEvent } = event;\n    setAlignToTop(nativeEvent.lines.length >= 2);\n  };\n\n  const handlePressAction = (e: GestureResponderEvent) => {\n    onPress?.(e);\n\n    if (expandedProp === undefined) {\n      // Only update state of the `expanded` prop was not passed\n      // If it was passed, the component will act as a controlled component\n      setExpanded((expanded) => !expanded);\n    }\n  };\n\n  const expandedInternal = expandedProp !== undefined ? expandedProp : expanded;\n\n  const groupContext = React.useContext(ListAccordionGroupContext);\n  if (groupContext !== null && (id === undefined || id === null || id === '')) {\n    throw new Error(\n      'List.Accordion is used inside a List.AccordionGroup without specifying an id prop.'\n    );\n  }\n  const isExpanded = groupContext\n    ? groupContext.expandedId === id\n    : expandedInternal;\n\n  const { titleColor, descriptionColor, titleTextColor, rippleColor } =\n    getAccordionColors({\n      theme,\n      isExpanded,\n      customRippleColor,\n    });\n\n  const handlePress =\n    groupContext && id !== undefined\n      ? () => groupContext.onAccordionPress(id)\n      : handlePressAction;\n  return (\n    <View>\n      <View style={{ backgroundColor: theme?.colors?.background }}>\n        <TouchableRipple\n          style={[theme.isV3 ? styles.containerV3 : styles.container, style]}\n          onPress={handlePress}\n          onLongPress={onLongPress}\n          delayLongPress={delayLongPress}\n          rippleColor={rippleColor}\n          accessibilityRole=\"button\"\n          accessibilityState={{ expanded: isExpanded }}\n          accessibilityLabel={accessibilityLabel}\n          testID={testID}\n          theme={theme}\n          background={background}\n          borderless\n        >\n          <View\n            style={[theme.isV3 ? styles.rowV3 : styles.row, containerStyle]}\n            pointerEvents={pointerEvents}\n          >\n            {left\n              ? left({\n                  color: isExpanded ? theme.colors?.primary : descriptionColor,\n                  style: getLeftStyles(alignToTop, description, theme.isV3),\n                })\n              : null}\n            <View\n              style={[\n                theme.isV3 ? styles.itemV3 : styles.item,\n                styles.content,\n                contentStyle,\n              ]}\n            >\n              <Text\n                selectable={false}\n                numberOfLines={titleNumberOfLines}\n                style={[\n                  styles.title,\n                  {\n                    color: titleTextColor,\n                  },\n                  titleStyle,\n                ]}\n                maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n              >\n                {title}\n              </Text>\n              {description ? (\n                <Text\n                  selectable={false}\n                  numberOfLines={descriptionNumberOfLines}\n                  style={[\n                    styles.description,\n                    {\n                      color: descriptionColor,\n                    },\n                    descriptionStyle,\n                  ]}\n                  onTextLayout={onDescriptionTextLayout}\n                  maxFontSizeMultiplier={descriptionMaxFontSizeMultiplier}\n                >\n                  {description}\n                </Text>\n              ) : null}\n            </View>\n            <View\n              style={[styles.item, description ? styles.multiline : undefined]}\n            >\n              {right ? (\n                right({\n                  isExpanded: isExpanded,\n                })\n              ) : (\n                <MaterialCommunityIcon\n                  name={isExpanded ? 'chevron-up' : 'chevron-down'}\n                  color={theme.isV3 ? descriptionColor : titleColor}\n                  size={24}\n                  direction={I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'}\n                />\n              )}\n            </View>\n          </View>\n        </TouchableRipple>\n      </View>\n\n      {isExpanded\n        ? React.Children.map(children, (child) => {\n            if (\n              left &&\n              React.isValidElement(child) &&\n              !child.props.left &&\n              !child.props.right\n            ) {\n              return React.cloneElement(child as React.ReactElement<any>, {\n                style: [\n                  theme.isV3 ? styles.childV3 : styles.child,\n                  child.props.style,\n                ],\n                theme,\n              });\n            }\n\n            return child;\n          })\n        : null}\n    </View>\n  );\n};\n\nListAccordion.displayName = 'List.Accordion';\n\nconst styles = StyleSheet.create({\n  container: {\n    padding: 8,\n  },\n  containerV3: {\n    paddingVertical: 8,\n    paddingRight: 24,\n  },\n  row: {\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n  rowV3: {\n    flexDirection: 'row',\n    marginVertical: 6,\n  },\n  multiline: {\n    height: 40,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  title: {\n    fontSize: 16,\n  },\n  description: {\n    fontSize: 14,\n  },\n  item: {\n    marginVertical: 6,\n    paddingLeft: 8,\n  },\n  itemV3: {\n    paddingLeft: 16,\n  },\n  child: {\n    paddingLeft: 64,\n  },\n  childV3: {\n    paddingLeft: 40,\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center',\n  },\n});\n\nexport default ListAccordion;\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAgB9B,SAASC,yBAAyB;AAElC,SAASC,kBAAkB,EAAEC,aAAa;AAC1C,SAASC,gBAAgB;AAEzB,OAAOC,qBAAqB;AAC5B,OAAOC,eAAe;AACtB,OAAOC,IAAI;AAmJX,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAA,EA0BT;EAAA,IAAAC,aAAA,EAAAC,cAAA;EAAA,IAzBXC,IAAI,GAyBEH,IAAA,CAzBNG,IAAI;IACJC,KAAK,GAwBCJ,IAAA,CAxBNI,KAAK;IACLC,KAAK,GAuBCL,IAAA,CAvBNK,KAAK;IACLC,WAAW,GAsBLN,IAAA,CAtBNM,WAAW;IACXC,QAAQ,GAqBFP,IAAA,CArBNO,QAAQ;IACDC,cAAc,GAoBfR,IAAA,CApBNS,KAAK;IACLC,UAAU,GAmBJV,IAAA,CAnBNU,UAAU;IACVC,gBAAgB,GAkBVX,IAAA,CAlBNW,gBAAgB;IAAAC,qBAAA,GAkBVZ,IAAA,CAjBNa,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;IAAAE,qBAAA,GAiBhBd,IAAA,CAhBNe,wBAAwB;IAAxBA,wBAAwB,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;IACfE,iBAAiB,GAexBhB,IAAA,CAfNiB,WAAW;IACXC,KAAK,GAcClB,IAAA,CAdNkB,KAAK;IACLC,cAAc,GAaRnB,IAAA,CAbNmB,cAAc;IACdC,YAAY,GAYNpB,IAAA,CAZNoB,YAAY;IACZC,EAAE,GAWIrB,IAAA,CAXNqB,EAAE;IACFC,MAAM,GAUAtB,IAAA,CAVNsB,MAAM;IACNC,UAAU,GASJvB,IAAA,CATNuB,UAAU;IACVC,OAAO,GAQDxB,IAAA,CARNwB,OAAO;IACPC,WAAW,GAOLzB,IAAA,CAPNyB,WAAW;IACXC,cAAc,GAMR1B,IAAA,CANN0B,cAAc;IACJC,YAAY,GAKhB3B,IAAA,CALN4B,QAAQ;IACRC,kBAAkB,GAIZ7B,IAAA,CAJN6B,kBAAkB;IAAAC,kBAAA,GAIZ9B,IAAA,CAHN+B,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,MAAM,GAAAA,kBAAA;IACtBE,0BAA0B,GAEpBhC,IAAA,CAFNgC,0BAA0B;IAC1BC,gCAAA,GACMjC,IAAA,CADNiC,gCAAA;EAEA,IAAMxB,KAAK,GAAGd,gBAAgB,CAACa,cAAc,CAAC;EAC9C,IAAA0B,eAAA,GAAgC9C,KAAK,CAAC+C,QAAQ,CAC5CR,YAAY,IAAI,KAClB,CAAC;IAAAS,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAFMN,QAAQ,GAAAQ,gBAAA;IAAEE,WAAW,GAAAF,gBAAA;EAG5B,IAAAG,gBAAA,GAAoCnD,KAAK,CAAC+C,QAAQ,CAAC,KAAK,CAAC;IAAAK,gBAAA,GAAAH,cAAA,CAAAE,gBAAA;IAAlDE,UAAU,GAAAD,gBAAA;IAAEE,aAAa,GAAAF,gBAAA;EAEhC,IAAMG,uBAAuB,GAC3B,SADIA,uBAAuBA,CAC3BC,KAAgD,EAC7C;IACH,IAAI,CAACnC,KAAK,CAACoC,IAAI,EAAE;MACf;IACF;IACA,IAAQC,WAAA,GAAgBF,KAAK,CAArBE,WAAA;IACRJ,aAAa,CAACI,WAAW,CAACC,KAAK,CAACC,MAAM,IAAI,CAAC,CAAC;EAC9C,CAAC;EAED,IAAMC,iBAAiB,GAAI,SAArBA,iBAAiBA,CAAIC,CAAwB,EAAK;IACtD1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG0B,CAAC,CAAC;IAEZ,IAAIvB,YAAY,KAAKwB,SAAS,EAAE;MAG9Bb,WAAW,CAAE,UAAAV,QAAQ;QAAA,OAAK,CAACA,QAAQ;MAAA,EAAC;IACtC;EACF,CAAC;EAED,IAAMwB,gBAAgB,GAAGzB,YAAY,KAAKwB,SAAS,GAAGxB,YAAY,GAAGC,QAAQ;EAE7E,IAAMyB,YAAY,GAAGjE,KAAK,CAACkE,UAAU,CAAC9D,yBAAyB,CAAC;EAChE,IAAI6D,YAAY,KAAK,IAAI,KAAKhC,EAAE,KAAK8B,SAAS,IAAI9B,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,EAAE,CAAC,EAAE;IAC3E,MAAM,IAAIkC,KAAK,CACb,oFACF,CAAC;EACH;EACA,IAAMC,UAAU,GAAGH,YAAY,GAC3BA,YAAY,CAACI,UAAU,KAAKpC,EAAE,GAC9B+B,gBAAgB;EAEpB,IAAAM,mBAAA,GACEjE,kBAAkB,CAAC;MACjBgB,KAAK,EAALA,KAAK;MACL+C,UAAU,EAAVA,UAAU;MACVxC,iBAAA,EAAAA;IACF,CAAC,CAAC;IALI2C,UAAU,GAAAD,mBAAA,CAAVC,UAAU;IAAEC,gBAAgB,GAAAF,mBAAA,CAAhBE,gBAAgB;IAAEC,cAAc,GAAAH,mBAAA,CAAdG,cAAc;IAAE5C,WAAA,GAAAyC,mBAAA,CAAAzC,WAAA;EAOtD,IAAM6C,WAAW,GACfT,YAAY,IAAIhC,EAAE,KAAK8B,SAAS,GAC5B;IAAA,OAAME,YAAY,CAACU,gBAAgB,CAAC1C,EAAE,CAAC;EAAA,IACvC4B,iBAAiB;EACvB,OACE7D,KAAA,CAAA4E,aAAA,CAACzE,IAAI,QACHH,KAAA,CAAA4E,aAAA,CAACzE,IAAI;IAAC2B,KAAK,EAAE;MAAE+C,eAAe,EAAExD,KAAK,aAALA,KAAK,gBAAAR,aAAA,GAALQ,KAAK,CAAEyD,MAAM,cAAAjE,aAAA,uBAAbA,aAAA,CAAesB;IAAW;EAAE,GAC1DnC,KAAA,CAAA4E,aAAA,CAACnE,eAAe;IACdqB,KAAK,EAAE,CAACT,KAAK,CAACoC,IAAI,GAAGsB,MAAM,CAACC,WAAW,GAAGD,MAAM,CAACE,SAAS,EAAEnD,KAAK,CAAE;IACnEM,OAAO,EAAEsC,WAAY;IACrBrC,WAAW,EAAEA,WAAY;IACzBC,cAAc,EAAEA,cAAe;IAC/BT,WAAW,EAAEA,WAAY;IACzBqD,iBAAiB,EAAC,QAAQ;IAC1BC,kBAAkB,EAAE;MAAE3C,QAAQ,EAAE4B;IAAW,CAAE;IAC7C3B,kBAAkB,EAAEA,kBAAmB;IACvCP,MAAM,EAAEA,MAAO;IACfb,KAAK,EAAEA,KAAM;IACbc,UAAU,EAAEA,UAAW;IACvBiD,UAAU;EAAA,GAEVpF,KAAA,CAAA4E,aAAA,CAACzE,IAAI;IACH2B,KAAK,EAAE,CAACT,KAAK,CAACoC,IAAI,GAAGsB,MAAM,CAACM,KAAK,GAAGN,MAAM,CAACO,GAAG,EAAEvD,cAAc,CAAE;IAChEY,aAAa,EAAEA;EAAc,GAE5B5B,IAAI,GACDA,IAAI,CAAC;IACHwE,KAAK,EAAEnB,UAAU,IAAAtD,cAAA,GAAGO,KAAK,CAACyD,MAAM,cAAAhE,cAAA,uBAAZA,cAAA,CAAc0E,OAAO,GAAGhB,gBAAgB;IAC5D1C,KAAK,EAAExB,aAAa,CAAC+C,UAAU,EAAEnC,WAAW,EAAEG,KAAK,CAACoC,IAAI;EAC1D,CAAC,CAAC,GACF,IAAI,EACRzD,KAAA,CAAA4E,aAAA,CAACzE,IAAI;IACH2B,KAAK,EAAE,CACLT,KAAK,CAACoC,IAAI,GAAGsB,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACW,IAAI,EACxCX,MAAM,CAACY,OAAO,EACd3D,YAAY;EACZ,GAEFhC,KAAA,CAAA4E,aAAA,CAAClE,IAAI;IACHkF,UAAU,EAAE,KAAM;IAClBC,aAAa,EAAEpE,kBAAmB;IAClCK,KAAK,EAAE,CACLiD,MAAM,CAAC9D,KAAK,EACZ;MACEsE,KAAK,EAAEd;IACT,CAAC,EACDnD,UAAU,CACV;IACFwE,qBAAqB,EAAElD;EAA2B,GAEjD3B,KACG,CAAC,EACNC,WAAW,GACVlB,KAAA,CAAA4E,aAAA,CAAClE,IAAI;IACHkF,UAAU,EAAE,KAAM;IAClBC,aAAa,EAAElE,wBAAyB;IACxCG,KAAK,EAAE,CACLiD,MAAM,CAAC7D,WAAW,EAClB;MACEqE,KAAK,EAAEf;IACT,CAAC,EACDjD,gBAAgB,CAChB;IACFwE,YAAY,EAAExC,uBAAwB;IACtCuC,qBAAqB,EAAEjD;EAAiC,GAEvD3B,WACG,CAAC,GACL,IACA,CAAC,EACPlB,KAAA,CAAA4E,aAAA,CAACzE,IAAI;IACH2B,KAAK,EAAE,CAACiD,MAAM,CAACW,IAAI,EAAExE,WAAW,GAAG6D,MAAM,CAACiB,SAAS,GAAGjC,SAAS;EAAE,GAEhE/C,KAAK,GACJA,KAAK,CAAC;IACJoD,UAAU,EAAEA;EACd,CAAC,CAAC,GAEFpE,KAAA,CAAA4E,aAAA,CAACpE,qBAAqB;IACpByF,IAAI,EAAE7B,UAAU,GAAG,YAAY,GAAG,cAAe;IACjDmB,KAAK,EAAElE,KAAK,CAACoC,IAAI,GAAGe,gBAAgB,GAAGD,UAAW;IAClD2B,IAAI,EAAE,EAAG;IACTC,SAAS,EAAElG,WAAW,CAACmG,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG;EAAM,CAC7D,CAEC,CACF,CACS,CACb,CAAC,EAENjC,UAAU,GACPpE,KAAK,CAACsG,QAAQ,CAACC,GAAG,CAACpF,QAAQ,EAAG,UAAAqF,KAAK,EAAK;IACtC,IACEzF,IAAI,IACJf,KAAK,CAACyG,cAAc,CAACD,KAAK,CAAC,IAC3B,CAACA,KAAK,CAACE,KAAK,CAAC3F,IAAI,IACjB,CAACyF,KAAK,CAACE,KAAK,CAAC1F,KAAK,EAClB;MACA,OAAOhB,KAAK,CAAC2G,YAAY,CAACH,KAAK,EAA6B;QAC1D1E,KAAK,EAAE,CACLT,KAAK,CAACoC,IAAI,GAAGsB,MAAM,CAAC6B,OAAO,GAAG7B,MAAM,CAACyB,KAAK,EAC1CA,KAAK,CAACE,KAAK,CAAC5E,KAAK,CAClB;QACDT,KAAA,EAAAA;MACF,CAAC,CAAC;IACJ;IAEA,OAAOmF,KAAK;EACd,CAAC,CAAC,GACF,IACA,CAAC;AAEX,CAAC;AAED7F,aAAa,CAACkG,WAAW,GAAG,gBAAgB;AAE5C,IAAM9B,MAAM,GAAG7E,UAAU,CAAC4G,MAAM,CAAC;EAC/B7B,SAAS,EAAE;IACT8B,OAAO,EAAE;EACX,CAAC;EACD/B,WAAW,EAAE;IACXgC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD3B,GAAG,EAAE;IACH4B,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EACD9B,KAAK,EAAE;IACL6B,aAAa,EAAE,KAAK;IACpBE,cAAc,EAAE;EAClB,CAAC;EACDpB,SAAS,EAAE;IACTqB,MAAM,EAAE,EAAE;IACVF,UAAU,EAAE,QAAQ;IACpBG,cAAc,EAAE;EAClB,CAAC;EACDrG,KAAK,EAAE;IACLsG,QAAQ,EAAE;EACZ,CAAC;EACDrG,WAAW,EAAE;IACXqG,QAAQ,EAAE;EACZ,CAAC;EACD7B,IAAI,EAAE;IACJ0B,cAAc,EAAE,CAAC;IACjBI,WAAW,EAAE;EACf,CAAC;EACD/B,MAAM,EAAE;IACN+B,WAAW,EAAE;EACf,CAAC;EACDhB,KAAK,EAAE;IACLgB,WAAW,EAAE;EACf,CAAC;EACDZ,OAAO,EAAE;IACPY,WAAW,EAAE;EACf,CAAC;EACD7B,OAAO,EAAE;IACP8B,IAAI,EAAE,CAAC;IACPH,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAe3G,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}