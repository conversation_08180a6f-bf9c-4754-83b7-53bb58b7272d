{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nexport var ListAccordionGroupContext = React.createContext(null);\nvar ListAccordionGroup = function ListAccordionGroup(_ref) {\n  var expandedIdProp = _ref.expandedId,\n    onAccordionPress = _ref.onAccordionPress,\n    children = _ref.children;\n  var _React$useState = React.useState(undefined),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    expandedId = _React$useState2[0],\n    setExpandedId = _React$useState2[1];\n  var onAccordionPressDefault = function onAccordionPressDefault(newExpandedId) {\n    setExpandedId(function (currentExpandedId) {\n      return currentExpandedId === newExpandedId ? undefined : newExpandedId;\n    });\n  };\n  return React.createElement(ListAccordionGroupContext.Provider, {\n    value: {\n      expandedId: expandedIdProp || expandedId,\n      onAccordionPress: onAccordionPress || onAccordionPressDefault\n    }\n  }, children);\n};\nListAccordionGroup.displayName = 'List.AccordionGroup';\nexport default ListAccordionGroup;", "map": {"version": 3, "names": ["React", "ListAccordionGroupContext", "createContext", "ListAccordionGroup", "_ref", "expandedIdProp", "expandedId", "onAccordionPress", "children", "_React$useState", "useState", "undefined", "_React$useState2", "_slicedToArray", "setExpandedId", "onAccordion<PERSON><PERSON><PERSON><PERSON><PERSON>", "newExpandedId", "currentExpandedId", "createElement", "Provider", "value", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/List/ListAccordionGroup.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport type Props = {\n  /**\n   * Function to execute on selection change.\n   */\n  onAccordionPress?: (expandedId: string | number) => void;\n  /**\n   * Id of the currently expanded list accordion\n   */\n  expandedId?: string | number;\n  /**\n   * React elements containing list accordions\n   */\n  children: React.ReactNode;\n};\n\nexport type ListAccordionGroupContextType = {\n  expandedId: string | number | undefined;\n  onAccordionPress: (expandedId: string | number) => void;\n} | null;\n\nexport const ListAccordionGroupContext =\n  React.createContext<ListAccordionGroupContextType>(null);\n\n/**\n * List.AccordionGroup allows to control a group of List Accordions. `id` prop for List.Accordion is required in order for group to work.\n * List.AccordionGroup can be a controlled or uncontrolled component. The example shows the uncontrolled version.\n * At most one Accordion can be expanded at a given time.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View, Text } from 'react-native';\n * import { List } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <List.AccordionGroup>\n *     <List.Accordion title=\"Accordion 1\" id=\"1\">\n *       <List.Item title=\"Item 1\" />\n *     </List.Accordion>\n *     <List.Accordion title=\"Accordion 2\" id=\"2\">\n *       <List.Item title=\"Item 2\" />\n *     </List.Accordion>\n *     <View>\n *       <Text>\n *         List.Accordion can be wrapped because implementation uses React.Context.\n *       </Text>\n *       <List.Accordion title=\"Accordion 3\" id=\"3\">\n *         <List.Item title=\"Item 3\" />\n *       </List.Accordion>\n *     </View>\n *   </List.AccordionGroup>\n * );\n *\n * export default MyComponent;\n *```\n */\nconst ListAccordionGroup = ({\n  expandedId: expandedIdProp,\n  onAccordionPress,\n  children,\n}: Props) => {\n  const [expandedId, setExpandedId] = React.useState<\n    string | number | undefined\n  >(undefined);\n\n  const onAccordionPressDefault = (newExpandedId: string | number) => {\n    setExpandedId((currentExpandedId) =>\n      currentExpandedId === newExpandedId ? undefined : newExpandedId\n    );\n  };\n\n  return (\n    <ListAccordionGroupContext.Provider\n      value={{\n        expandedId: expandedIdProp || expandedId, // component can be controlled or uncontrolled\n        onAccordionPress: onAccordionPress || onAccordionPressDefault,\n      }}\n    >\n      {children}\n    </ListAccordionGroupContext.Provider>\n  );\n};\n\nListAccordionGroup.displayName = 'List.AccordionGroup';\n\nexport default ListAccordionGroup;\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAsB9B,OAAO,IAAMC,yBAAyB,GACpCD,KAAK,CAACE,aAAa,CAAgC,IAAI,CAAC;AAmC1D,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGC,IAAA,EAId;EAAA,IAHCC,cAAc,GAGpBD,IAAA,CAHNE,UAAU;IACVC,gBAAgB,GAEVH,IAAA,CAFNG,gBAAgB;IAChBC,QAAA,GACMJ,IAAA,CADNI,QAAA;EAEA,IAAAC,eAAA,GAAoCT,KAAK,CAACU,QAAQ,CAEhDC,SAAS,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;IAFLH,UAAU,GAAAM,gBAAA;IAAEE,aAAa,GAAAF,gBAAA;EAIhC,IAAMG,uBAAuB,GAAI,SAA3BA,uBAAuBA,CAAIC,aAA8B,EAAK;IAClEF,aAAa,CAAE,UAAAG,iBAAiB;MAAA,OAC9BA,iBAAiB,KAAKD,aAAa,GAAGL,SAAS,GAAGK,aACpD;IAAA,EAAC;EACH,CAAC;EAED,OACEhB,KAAA,CAAAkB,aAAA,CAACjB,yBAAyB,CAACkB,QAAQ;IACjCC,KAAK,EAAE;MACLd,UAAU,EAAED,cAAc,IAAIC,UAAU;MACxCC,gBAAgB,EAAEA,gBAAgB,IAAIQ;IACxC;EAAE,GAEDP,QACiC,CAAC;AAEzC,CAAC;AAEDL,kBAAkB,CAACkB,WAAW,GAAG,qBAAqB;AAEtD,eAAelB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}