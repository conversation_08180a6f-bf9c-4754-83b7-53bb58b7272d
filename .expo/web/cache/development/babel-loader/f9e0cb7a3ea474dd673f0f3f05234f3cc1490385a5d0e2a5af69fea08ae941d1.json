{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"color\", \"subtitle\", \"subtitleStyle\", \"onPress\", \"disabled\", \"style\", \"titleRef\", \"titleStyle\", \"title\", \"titleMaxFontSizeMultiplier\", \"mode\", \"theme\", \"testID\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { modeTextVariant } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { white } from \"../../styles/themes/v2/colors\";\nimport Text from \"../Typography/Text\";\nvar AppbarContent = function AppbarContent(_ref) {\n  var titleColor = _ref.color,\n    subtitle = _ref.subtitle,\n    subtitleStyle = _ref.subtitleStyle,\n    onPress = _ref.onPress,\n    disabled = _ref.disabled,\n    style = _ref.style,\n    titleRef = _ref.titleRef,\n    titleStyle = _ref.titleStyle,\n    title = _ref.title,\n    titleMaxFontSizeMultiplier = _ref.titleMaxFontSizeMultiplier,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'small' : _ref$mode,\n    themeOverrides = _ref.theme,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'appbar-content' : _ref$testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3,\n    colors = theme.colors;\n  var titleTextColor = titleColor ? titleColor : isV3 ? colors.onSurface : white;\n  var subtitleColor = color(titleTextColor).alpha(0.7).rgb().string();\n  var modeContainerStyles = {\n    small: styles.v3DefaultContainer,\n    medium: styles.v3MediumContainer,\n    large: styles.v3LargeContainer,\n    'center-aligned': styles.v3DefaultContainer\n  };\n  var variant = modeTextVariant[mode];\n  var contentWrapperProps = _objectSpread({\n    pointerEvents: 'box-none',\n    style: [styles.container, isV3 && modeContainerStyles[mode], style],\n    testID: testID\n  }, rest);\n  var content = React.createElement(React.Fragment, null, typeof title === 'string' ? React.createElement(Text, _extends({}, isV3 && {\n    variant: variant\n  }, {\n    ref: titleRef,\n    style: [_objectSpread({\n      color: titleTextColor\n    }, isV3 ? theme.fonts[variant] : Platform.OS === 'ios' ? theme.fonts.regular : theme.fonts.medium), !isV3 && styles.title, titleStyle],\n    numberOfLines: 1,\n    accessible: true,\n    accessibilityRole: onPress ? 'none' : Platform.OS === 'web' ? 'heading' : 'header',\n    accessibilityTraits: \"header\",\n    testID: `${testID}-title-text`,\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }), title) : title, !isV3 && subtitle ? React.createElement(Text, {\n    style: [styles.subtitle, {\n      color: subtitleColor\n    }, subtitleStyle],\n    numberOfLines: 1\n  }, subtitle) : null);\n  if (onPress) {\n    return (React.createElement(Pressable, _extends({\n        accessibilityRole: touchableRole,\n        accessibilityTraits: touchableRole,\n        accessibilityComponentType: \"button\",\n        accessbilityState: disabled ? 'disabled' : null,\n        onPress: onPress,\n        disabled: disabled\n      }, contentWrapperProps), content)\n    );\n  }\n  return React.createElement(View, contentWrapperProps, content);\n};\nAppbarContent.displayName = 'Appbar.Content';\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    paddingHorizontal: 12\n  },\n  v3DefaultContainer: {\n    paddingHorizontal: 0\n  },\n  v3MediumContainer: {\n    paddingHorizontal: 0,\n    justifyContent: 'flex-end',\n    paddingBottom: 24\n  },\n  v3LargeContainer: {\n    paddingHorizontal: 0,\n    paddingTop: 36,\n    justifyContent: 'flex-end',\n    paddingBottom: 28\n  },\n  title: {\n    fontSize: Platform.OS === 'ios' ? 17 : 20\n  },\n  subtitle: {\n    fontSize: Platform.OS === 'ios' ? 11 : 14\n  }\n});\nvar touchableRole = 'button';\nexport default AppbarContent;\nexport { AppbarContent };", "map": {"version": 3, "names": ["React", "Platform", "StyleSheet", "Pressable", "View", "color", "modeTextVariant", "useInternalTheme", "white", "Text", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "titleColor", "subtitle", "subtitleStyle", "onPress", "disabled", "style", "titleRef", "titleStyle", "title", "titleMaxFontSizeMultiplier", "_ref$mode", "mode", "themeOverrides", "theme", "_ref$testID", "testID", "rest", "_objectWithoutProperties", "_excluded", "isV3", "colors", "titleTextColor", "onSurface", "subtitleColor", "alpha", "rgb", "string", "modeContainerStyles", "small", "styles", "v3DefaultContainer", "medium", "v3MediumContainer", "large", "v3LargeContainer", "variant", "contentWrapperProps", "_objectSpread", "pointerEvents", "container", "content", "createElement", "Fragment", "_extends", "ref", "fonts", "OS", "regular", "numberOfLines", "accessible", "accessibilityRole", "accessibilityTraits", "maxFontSizeMultiplier", "touchableRole", "accessibilityComponentType", "accessbilityState", "displayName", "create", "flex", "paddingHorizontal", "justifyContent", "paddingBottom", "paddingTop", "fontSize"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Appbar/AppbarContent.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  AccessibilityRole,\n  GestureResponderEvent,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  Pressable,\n  View,\n  ViewStyle,\n  ViewProps,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { modeTextVariant } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport { white } from '../../styles/themes/v2/colors';\nimport type { $RemoveChildren, MD3TypescaleKey, ThemeProp } from '../../types';\nimport Text, { TextRef } from '../Typography/Text';\n\ntype TitleString = {\n  title: string;\n  titleStyle?: StyleProp<TextStyle>;\n};\n\ntype TitleElement = { title: React.ReactNode; titleStyle?: never };\n\nexport type Props = $RemoveChildren<typeof View> & {\n  // For `title` and `titleStyle` props their types are duplicated due to the generation of documentation.\n  // Appropriate type for them are either `TitleString` or `TitleElement`, depends on `title` type.\n  /**\n   * Text or component for the title.\n   */\n  title: React.ReactNode;\n  /**\n   * Style for the title, if `title` is a string.\n   */\n  titleStyle?: StyleProp<TextStyle>;\n  /**\n   * Reference for the title.\n   */\n  titleRef?: React.RefObject<TextRef>;\n  /**\n   * @deprecated Deprecated in v5.x\n   * Text for the subtitle.\n   */\n  subtitle?: React.ReactNode;\n  /**\n   * @deprecated Deprecated in v5.x\n   * Style for the subtitle.\n   */\n  subtitleStyle?: StyleProp<TextStyle>;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * If true, disable all interactions for this component.\n   */\n  disabled?: boolean;\n  /**\n   * Custom color for the text.\n   */\n  color?: string;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  /**\n   * @internal\n   */\n  mode?: 'small' | 'medium' | 'large' | 'center-aligned';\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n} & (TitleString | TitleElement);\n\n/**\n * A component used to display a title and optional subtitle in an appbar.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Appbar } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *     <Appbar.Header>\n *        <Appbar.Content title=\"Title\" />\n *     </Appbar.Header>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst AppbarContent = ({\n  color: titleColor,\n  subtitle,\n  subtitleStyle,\n  onPress,\n  disabled,\n  style,\n  titleRef,\n  titleStyle,\n  title,\n  titleMaxFontSizeMultiplier,\n  mode = 'small',\n  theme: themeOverrides,\n  testID = 'appbar-content',\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { isV3, colors } = theme;\n\n  const titleTextColor = titleColor\n    ? titleColor\n    : isV3\n    ? colors.onSurface\n    : white;\n\n  const subtitleColor = color(titleTextColor).alpha(0.7).rgb().string();\n\n  const modeContainerStyles = {\n    small: styles.v3DefaultContainer,\n    medium: styles.v3MediumContainer,\n    large: styles.v3LargeContainer,\n    'center-aligned': styles.v3DefaultContainer,\n  };\n\n  const variant = modeTextVariant[mode] as MD3TypescaleKey;\n\n  const contentWrapperProps = {\n    pointerEvents: 'box-none' as ViewProps['pointerEvents'],\n    style: [styles.container, isV3 && modeContainerStyles[mode], style],\n    testID,\n    ...rest,\n  };\n\n  const content = (\n    <>\n      {typeof title === 'string' ? (\n        <Text\n          {...(isV3 && { variant })}\n          ref={titleRef}\n          style={[\n            {\n              color: titleTextColor,\n              ...(isV3\n                ? theme.fonts[variant]\n                : Platform.OS === 'ios'\n                ? theme.fonts.regular\n                : theme.fonts.medium),\n            },\n            !isV3 && styles.title,\n            titleStyle,\n          ]}\n          numberOfLines={1}\n          accessible\n          accessibilityRole={\n            onPress\n              ? 'none'\n              : Platform.OS === 'web'\n              ? ('heading' as 'header')\n              : 'header'\n          }\n          // @ts-expect-error We keep old a11y props for backwards compat with old RN versions\n          accessibilityTraits=\"header\"\n          testID={`${testID}-title-text`}\n          maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n        >\n          {title}\n        </Text>\n      ) : (\n        title\n      )}\n      {!isV3 && subtitle ? (\n        <Text\n          style={[styles.subtitle, { color: subtitleColor }, subtitleStyle]}\n          numberOfLines={1}\n        >\n          {subtitle}\n        </Text>\n      ) : null}\n    </>\n  );\n\n  if (onPress) {\n    return (\n      // eslint-disable-next-line react-native-a11y/has-accessibility-props\n      <Pressable\n        accessibilityRole={touchableRole}\n        // @ts-expect-error We keep old a11y props for backwards compat with old RN versions\n        accessibilityTraits={touchableRole}\n        accessibilityComponentType=\"button\"\n        accessbilityState={disabled ? 'disabled' : null}\n        onPress={onPress}\n        disabled={disabled}\n        {...contentWrapperProps}\n      >\n        {content}\n      </Pressable>\n    );\n  }\n\n  return <View {...contentWrapperProps}>{content}</View>;\n};\n\nAppbarContent.displayName = 'Appbar.Content';\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    paddingHorizontal: 12,\n  },\n  v3DefaultContainer: {\n    paddingHorizontal: 0,\n  },\n  v3MediumContainer: {\n    paddingHorizontal: 0,\n    justifyContent: 'flex-end',\n    paddingBottom: 24,\n  },\n  v3LargeContainer: {\n    paddingHorizontal: 0,\n    paddingTop: 36,\n    justifyContent: 'flex-end',\n    paddingBottom: 28,\n  },\n  title: {\n    fontSize: Platform.OS === 'ios' ? 17 : 20,\n  },\n  subtitle: {\n    fontSize: Platform.OS === 'ios' ? 11 : 14,\n  },\n});\n\nconst touchableRole: AccessibilityRole = 'button';\n\nexport default AppbarContent;\n\n// @component-docs ignore-next-line\nexport { AppbarContent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,IAAA;AAc9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,eAAe;AACxB,SAASC,gBAAgB;AACzB,SAASC,KAAK;AAEd,OAAOC,IAAI;AAkFX,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAA,EAeT;EAAA,IAdJC,UAAU,GAcXD,IAAA,CAdNN,KAAK;IACLQ,QAAQ,GAaFF,IAAA,CAbNE,QAAQ;IACRC,aAAa,GAYPH,IAAA,CAZNG,aAAa;IACbC,OAAO,GAWDJ,IAAA,CAXNI,OAAO;IACPC,QAAQ,GAUFL,IAAA,CAVNK,QAAQ;IACRC,KAAK,GASCN,IAAA,CATNM,KAAK;IACLC,QAAQ,GAQFP,IAAA,CARNO,QAAQ;IACRC,UAAU,GAOJR,IAAA,CAPNQ,UAAU;IACVC,KAAK,GAMCT,IAAA,CANNS,KAAK;IACLC,0BAA0B,GAKpBV,IAAA,CALNU,0BAA0B;IAAAC,SAAA,GAKpBX,IAAA,CAJNY,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,OAAO,GAAAA,SAAA;IACPE,cAAc,GAGfb,IAAA,CAHNc,KAAK;IAAAC,WAAA,GAGCf,IAAA,CAFNgB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,gBAAgB,GAAAA,WAAA;IACtBE,IAAA,GAAAC,wBAAA,CACGlB,IAAA,EAAAmB,SAAA;EACN,IAAML,KAAK,GAAGlB,gBAAgB,CAACiB,cAAc,CAAC;EAC9C,IAAQO,IAAI,GAAaN,KAAK,CAAtBM,IAAI;IAAEC,MAAA,GAAWP,KAAK,CAAhBO,MAAA;EAEd,IAAMC,cAAc,GAAGrB,UAAU,GAC7BA,UAAU,GACVmB,IAAI,GACJC,MAAM,CAACE,SAAS,GAChB1B,KAAK;EAET,IAAM2B,aAAa,GAAG9B,KAAK,CAAC4B,cAAc,CAAC,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAErE,IAAMC,mBAAmB,GAAG;IAC1BC,KAAK,EAAEC,MAAM,CAACC,kBAAkB;IAChCC,MAAM,EAAEF,MAAM,CAACG,iBAAiB;IAChCC,KAAK,EAAEJ,MAAM,CAACK,gBAAgB;IAC9B,gBAAgB,EAAEL,MAAM,CAACC;EAC3B,CAAC;EAED,IAAMK,OAAO,GAAGzC,eAAe,CAACiB,IAAI,CAAoB;EAExD,IAAMyB,mBAAmB,GAAAC,aAAA;IACvBC,aAAa,EAAE,UAAwC;IACvDjC,KAAK,EAAE,CAACwB,MAAM,CAACU,SAAS,EAAEpB,IAAI,IAAIQ,mBAAmB,CAAChB,IAAI,CAAC,EAAEN,KAAK,CAAC;IACnEU,MAAM,EAANA;EAAM,GACHC,IAAA,CACJ;EAED,IAAMwB,OAAO,GACXpD,KAAA,CAAAqD,aAAA,CAAArD,KAAA,CAAAsD,QAAA,QACG,OAAOlC,KAAK,KAAK,QAAQ,GACxBpB,KAAA,CAAAqD,aAAA,CAAC5C,IAAI,EAAA8C,QAAA,KACExB,IAAI,IAAI;IAAEgB,OAAA,EAAAA;EAAQ,CAAC;IACxBS,GAAG,EAAEtC,QAAS;IACdD,KAAK,EAAE,CAAAgC,aAAA;MAEH5C,KAAK,EAAE4B;IAAc,GACjBF,IAAI,GACJN,KAAK,CAACgC,KAAK,CAACV,OAAO,CAAC,GACpB9C,QAAQ,CAACyD,EAAE,KAAK,KAAK,GACrBjC,KAAK,CAACgC,KAAK,CAACE,OAAO,GACnBlC,KAAK,CAACgC,KAAK,CAACd,MAAM,GAExB,CAACZ,IAAI,IAAIU,MAAM,CAACrB,KAAK,EACrBD,UAAU,CACV;IACFyC,aAAa,EAAE,CAAE;IACjBC,UAAU;IACVC,iBAAiB,EACf/C,OAAO,GACH,MAAM,GACNd,QAAQ,CAACyD,EAAE,KAAK,KAAK,GACpB,SAAS,GACV;IAGNK,mBAAmB,EAAC,QAAQ;IAC5BpC,MAAM,EAAG,GAAEA,MAAO,aAAa;IAC/BqC,qBAAqB,EAAE3C;EAA2B,IAEjDD,KACG,CAAC,GAEPA,KACD,EACA,CAACW,IAAI,IAAIlB,QAAQ,GAChBb,KAAA,CAAAqD,aAAA,CAAC5C,IAAI;IACHQ,KAAK,EAAE,CAACwB,MAAM,CAAC5B,QAAQ,EAAE;MAAER,KAAK,EAAE8B;IAAc,CAAC,EAAErB,aAAa,CAAE;IAClE8C,aAAa,EAAE;EAAE,GAEhB/C,QACG,CAAC,GACL,IACJ,CACH;EAED,IAAIE,OAAO,EAAE;IACX,QAEEf,KAAA,CAAAqD,aAAA,CAAClD,SAAS,EAAAoD,QAAA;QACRO,iBAAiB,EAAEG,aAAA;QAEnBF,mBAAmB,EAAEE,aAAc;QACnCC,0BAA0B,EAAC,QAAQ;QACnCC,iBAAiB,EAAEnD,QAAQ,GAAG,UAAU,GAAG,IAAK;QAChDD,OAAO,EAAEA,OAAQ;QACjBC,QAAQ,EAAEA;MAAS,GACfgC,mBAAmB,GAEtBI,OACQ;IAAA;EAEf;EAEA,OAAOpD,KAAA,CAAAqD,aAAA,CAACjD,IAAI,EAAK4C,mBAAmB,EAAGI,OAAc,CAAC;AACxD,CAAC;AAED1C,aAAa,CAAC0D,WAAW,GAAG,gBAAgB;AAE5C,IAAM3B,MAAM,GAAGvC,UAAU,CAACmE,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTmB,IAAI,EAAE,CAAC;IACPC,iBAAiB,EAAE;EACrB,CAAC;EACD7B,kBAAkB,EAAE;IAClB6B,iBAAiB,EAAE;EACrB,CAAC;EACD3B,iBAAiB,EAAE;IACjB2B,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE;EACjB,CAAC;EACD3B,gBAAgB,EAAE;IAChByB,iBAAiB,EAAE,CAAC;IACpBG,UAAU,EAAE,EAAE;IACdF,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE;EACjB,CAAC;EACDrD,KAAK,EAAE;IACLuD,QAAQ,EAAE1E,QAAQ,CAACyD,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG;EACzC,CAAC;EACD7C,QAAQ,EAAE;IACR8D,QAAQ,EAAE1E,QAAQ,CAACyD,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG;EACzC;AACF,CAAC,CAAC;AAEF,IAAMO,aAAgC,GAAG,QAAQ;AAEjD,eAAevD,aAAa;AAG5B,SAASA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}