{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { getHeaderTitle, Header, SafeAreaProviderCompat, Screen } from '@react-navigation/elements';\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { SafeAreaInsetsContext } from 'react-native-safe-area-context';\nimport BottomTabBarHeightCallbackContext from \"../utils/BottomTabBarHeightCallbackContext\";\nimport BottomTabBarHeightContext from \"../utils/BottomTabBarHeightContext\";\nimport BottomTabBar, { getTabBarHeight } from \"./BottomTabBar\";\nimport { MaybeScreen, MaybeScreenContainer } from \"./ScreenFallback\";\nexport default function BottomTabView(props) {\n  var _props$tabBar = props.tabBar,\n    tabBar = _props$tabBar === void 0 ? function (props) {\n      return React.createElement(BottomTabBar, props);\n    } : _props$tabBar,\n    state = props.state,\n    navigation = props.navigation,\n    descriptors = props.descriptors,\n    safeAreaInsets = props.safeAreaInsets,\n    _props$detachInactive = props.detachInactiveScreens,\n    detachInactiveScreens = _props$detachInactive === void 0 ? Platform.OS === 'web' || Platform.OS === 'android' || Platform.OS === 'ios' : _props$detachInactive,\n    sceneContainerStyle = props.sceneContainerStyle;\n  var focusedRouteKey = state.routes[state.index].key;\n  var _React$useState = React.useState([focusedRouteKey]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    loaded = _React$useState2[0],\n    setLoaded = _React$useState2[1];\n  if (!loaded.includes(focusedRouteKey)) {\n    setLoaded([].concat(_toConsumableArray(loaded), [focusedRouteKey]));\n  }\n  var dimensions = SafeAreaProviderCompat.initialMetrics.frame;\n  var _React$useState3 = React.useState(function () {\n      return getTabBarHeight({\n        state: state,\n        descriptors: descriptors,\n        dimensions: dimensions,\n        layout: {\n          width: dimensions.width,\n          height: 0\n        },\n        insets: _objectSpread(_objectSpread({}, SafeAreaProviderCompat.initialMetrics.insets), props.safeAreaInsets),\n        style: descriptors[state.routes[state.index].key].options.tabBarStyle\n      });\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    tabBarHeight = _React$useState4[0],\n    setTabBarHeight = _React$useState4[1];\n  var renderTabBar = function renderTabBar() {\n    return React.createElement(SafeAreaInsetsContext.Consumer, null, function (insets) {\n      var _ref2, _ref3, _ref4, _ref5, _ref6, _ref7, _ref8, _ref9;\n      return tabBar({\n        state: state,\n        descriptors: descriptors,\n        navigation: navigation,\n        insets: {\n          top: (_ref2 = (_ref3 = safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.top) != null ? _ref3 : insets === null || insets === void 0 ? void 0 : insets.top) != null ? _ref2 : 0,\n          right: (_ref4 = (_ref5 = safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.right) != null ? _ref5 : insets === null || insets === void 0 ? void 0 : insets.right) != null ? _ref4 : 0,\n          bottom: (_ref6 = (_ref7 = safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.bottom) != null ? _ref7 : insets === null || insets === void 0 ? void 0 : insets.bottom) != null ? _ref6 : 0,\n          left: (_ref8 = (_ref9 = safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.left) != null ? _ref9 : insets === null || insets === void 0 ? void 0 : insets.left) != null ? _ref8 : 0\n        }\n      });\n    });\n  };\n  var routes = state.routes;\n  return React.createElement(SafeAreaProviderCompat, null, React.createElement(MaybeScreenContainer, {\n    enabled: detachInactiveScreens,\n    hasTwoStates: true,\n    style: styles.container\n  }, routes.map(function (route, index) {\n    var descriptor = descriptors[route.key];\n    var _descriptor$options = descriptor.options,\n      _descriptor$options$l = _descriptor$options.lazy,\n      lazy = _descriptor$options$l === void 0 ? true : _descriptor$options$l,\n      unmountOnBlur = _descriptor$options.unmountOnBlur;\n    var isFocused = state.index === index;\n    if (unmountOnBlur && !isFocused) {\n      return null;\n    }\n    if (lazy && !loaded.includes(route.key) && !isFocused) {\n      return null;\n    }\n    var _descriptor$options2 = descriptor.options,\n      freezeOnBlur = _descriptor$options2.freezeOnBlur,\n      _descriptor$options2$ = _descriptor$options2.header,\n      header = _descriptor$options2$ === void 0 ? function (_ref) {\n        var layout = _ref.layout,\n          options = _ref.options;\n        return React.createElement(Header, _extends({}, options, {\n          layout: layout,\n          title: getHeaderTitle(options, route.name)\n        }));\n      } : _descriptor$options2$,\n      headerShown = _descriptor$options2.headerShown,\n      headerStatusBarHeight = _descriptor$options2.headerStatusBarHeight,\n      headerTransparent = _descriptor$options2.headerTransparent;\n    return React.createElement(MaybeScreen, {\n      key: route.key,\n      style: [StyleSheet.absoluteFill, {\n        zIndex: isFocused ? 0 : -1\n      }],\n      visible: isFocused,\n      enabled: detachInactiveScreens,\n      freezeOnBlur: freezeOnBlur\n    }, React.createElement(BottomTabBarHeightContext.Provider, {\n      value: tabBarHeight\n    }, React.createElement(Screen, {\n      focused: isFocused,\n      route: descriptor.route,\n      navigation: descriptor.navigation,\n      headerShown: headerShown,\n      headerStatusBarHeight: headerStatusBarHeight,\n      headerTransparent: headerTransparent,\n      header: header({\n        layout: dimensions,\n        route: descriptor.route,\n        navigation: descriptor.navigation,\n        options: descriptor.options\n      }),\n      style: sceneContainerStyle\n    }, descriptor.render())));\n  })), React.createElement(BottomTabBarHeightCallbackContext.Provider, {\n    value: setTabBarHeight\n  }, renderTabBar()));\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    overflow: 'hidden'\n  }\n});", "map": {"version": 3, "names": ["getHeaderTitle", "Header", "SafeAreaProviderCompat", "Screen", "React", "Platform", "StyleSheet", "SafeAreaInsetsContext", "BottomTabBarHeightCallbackContext", "BottomTabBarHeightContext", "BottomTabBar", "getTabBarHeight", "MaybeScreen", "MaybeScreenContainer", "BottomTabView", "props", "_props$tabBar", "tabBar", "createElement", "state", "navigation", "descriptors", "safeAreaInsets", "_props$detachInactive", "detachInactiveScreens", "OS", "sceneContainerStyle", "focusedRouteKey", "routes", "index", "key", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "loaded", "setLoaded", "includes", "concat", "_toConsumableArray", "dimensions", "initialMetrics", "frame", "_React$useState3", "layout", "width", "height", "insets", "_objectSpread", "style", "options", "tabBarStyle", "_React$useState4", "tabBarHeight", "setTabBarHeight", "renderTabBar", "Consumer", "_ref2", "_ref3", "_ref4", "_ref5", "_ref6", "_ref7", "_ref8", "_ref9", "top", "right", "bottom", "left", "enabled", "hasTwoStates", "styles", "container", "map", "route", "descriptor", "_descriptor$options", "_descriptor$options$l", "lazy", "unmountOnBlur", "isFocused", "_descriptor$options2", "freezeOnBlur", "_descriptor$options2$", "header", "_ref", "_extends", "title", "name", "headerShown", "headerStatusBarHeight", "headerTransparent", "absoluteFill", "zIndex", "visible", "Provider", "value", "focused", "render", "create", "flex", "overflow"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/bottom-tabs/src/views/BottomTabView.tsx"], "sourcesContent": ["import {\n  getHeader<PERSON><PERSON><PERSON>,\n  Header,\n  SafeAreaProviderCompat,\n  Screen,\n} from '@react-navigation/elements';\nimport type {\n  ParamListBase,\n  TabNavigationState,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport { Platform, StyleSheet } from 'react-native';\nimport { SafeAreaInsetsContext } from 'react-native-safe-area-context';\n\nimport type {\n  BottomTabBarProps,\n  BottomTabDescriptorMap,\n  BottomTabHeaderProps,\n  BottomTabNavigationConfig,\n  BottomTabNavigationHelpers,\n  BottomTabNavigationProp,\n} from '../types';\nimport BottomTabBarHeightCallbackContext from '../utils/BottomTabBarHeightCallbackContext';\nimport BottomTabBarHeightContext from '../utils/BottomTabBarHeightContext';\nimport BottomTabBar, { getTabBarHeight } from './BottomTabBar';\nimport { MaybeScreen, MaybeScreenContainer } from './ScreenFallback';\n\ntype Props = BottomTabNavigationConfig & {\n  state: TabNavigationState<ParamListBase>;\n  navigation: BottomTabNavigationHelpers;\n  descriptors: BottomTabDescriptorMap;\n};\n\nexport default function BottomTabView(props: Props) {\n  const {\n    tabBar = (props: BottomTabBarProps) => <BottomTabBar {...props} />,\n    state,\n    navigation,\n    descriptors,\n    safeAreaInsets,\n    detachInactiveScreens = Platform.OS === 'web' ||\n      Platform.OS === 'android' ||\n      Platform.OS === 'ios',\n    sceneContainerStyle,\n  } = props;\n\n  const focusedRouteKey = state.routes[state.index].key;\n  const [loaded, setLoaded] = React.useState([focusedRouteKey]);\n\n  if (!loaded.includes(focusedRouteKey)) {\n    setLoaded([...loaded, focusedRouteKey]);\n  }\n\n  const dimensions = SafeAreaProviderCompat.initialMetrics.frame;\n  const [tabBarHeight, setTabBarHeight] = React.useState(() =>\n    getTabBarHeight({\n      state,\n      descriptors,\n      dimensions,\n      layout: { width: dimensions.width, height: 0 },\n      insets: {\n        ...SafeAreaProviderCompat.initialMetrics.insets,\n        ...props.safeAreaInsets,\n      },\n      style: descriptors[state.routes[state.index].key].options.tabBarStyle,\n    })\n  );\n\n  const renderTabBar = () => {\n    return (\n      <SafeAreaInsetsContext.Consumer>\n        {(insets) =>\n          tabBar({\n            state: state,\n            descriptors: descriptors,\n            navigation: navigation,\n            insets: {\n              top: safeAreaInsets?.top ?? insets?.top ?? 0,\n              right: safeAreaInsets?.right ?? insets?.right ?? 0,\n              bottom: safeAreaInsets?.bottom ?? insets?.bottom ?? 0,\n              left: safeAreaInsets?.left ?? insets?.left ?? 0,\n            },\n          })\n        }\n      </SafeAreaInsetsContext.Consumer>\n    );\n  };\n\n  const { routes } = state;\n\n  return (\n    <SafeAreaProviderCompat>\n      <MaybeScreenContainer\n        enabled={detachInactiveScreens}\n        hasTwoStates\n        style={styles.container}\n      >\n        {routes.map((route, index) => {\n          const descriptor = descriptors[route.key];\n          const { lazy = true, unmountOnBlur } = descriptor.options;\n          const isFocused = state.index === index;\n\n          if (unmountOnBlur && !isFocused) {\n            return null;\n          }\n\n          if (lazy && !loaded.includes(route.key) && !isFocused) {\n            // Don't render a lazy screen if we've never navigated to it\n            return null;\n          }\n\n          const {\n            freezeOnBlur,\n            header = ({ layout, options }: BottomTabHeaderProps) => (\n              <Header\n                {...options}\n                layout={layout}\n                title={getHeaderTitle(options, route.name)}\n              />\n            ),\n            headerShown,\n            headerStatusBarHeight,\n            headerTransparent,\n          } = descriptor.options;\n\n          return (\n            <MaybeScreen\n              key={route.key}\n              style={[StyleSheet.absoluteFill, { zIndex: isFocused ? 0 : -1 }]}\n              visible={isFocused}\n              enabled={detachInactiveScreens}\n              freezeOnBlur={freezeOnBlur}\n            >\n              <BottomTabBarHeightContext.Provider value={tabBarHeight}>\n                <Screen\n                  focused={isFocused}\n                  route={descriptor.route}\n                  navigation={descriptor.navigation}\n                  headerShown={headerShown}\n                  headerStatusBarHeight={headerStatusBarHeight}\n                  headerTransparent={headerTransparent}\n                  header={header({\n                    layout: dimensions,\n                    route: descriptor.route,\n                    navigation:\n                      descriptor.navigation as BottomTabNavigationProp<ParamListBase>,\n                    options: descriptor.options,\n                  })}\n                  style={sceneContainerStyle}\n                >\n                  {descriptor.render()}\n                </Screen>\n              </BottomTabBarHeightContext.Provider>\n            </MaybeScreen>\n          );\n        })}\n      </MaybeScreenContainer>\n      <BottomTabBarHeightCallbackContext.Provider value={setTabBarHeight}>\n        {renderTabBar()}\n      </BottomTabBarHeightCallbackContext.Provider>\n    </SafeAreaProviderCompat>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    overflow: 'hidden',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,SACEA,cAAc,EACdC,MAAM,EACNC,sBAAsB,EACtBC,MAAM,QACD,4BAA4B;AAKnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAE9B,SAASC,qBAAqB,QAAQ,gCAAgC;AAUtE,OAAOC,iCAAiC;AACxC,OAAOC,yBAAyB;AAChC,OAAOC,YAAY,IAAIC,eAAe;AACtC,SAASC,WAAW,EAAEC,oBAAoB;AAQ1C,eAAe,SAASC,aAAaA,CAACC,KAAY,EAAE;EAClD,IAAAC,aAAA,GAUID,KAAK,CATPE,MAAM;IAANA,MAAM,GAAAD,aAAA,cAAI,UAAAD,KAAwB;MAAA,OAAKX,KAAA,CAAAc,aAAA,CAACR,YAAY,EAAKK,KAAK,CAAI;IAAA,IAAAC,aAAA;IAClEG,KAAK,GAQHJ,KAAK,CARPI,KAAK;IACLC,UAAU,GAORL,KAAK,CAPPK,UAAU;IACVC,WAAW,GAMTN,KAAK,CANPM,WAAW;IACXC,cAAc,GAKZP,KAAK,CALPO,cAAc;IAAAC,qBAAA,GAKZR,KAAK,CAJPS,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAGlB,QAAQ,CAACoB,EAAE,KAAK,KAAK,IAC3CpB,QAAQ,CAACoB,EAAE,KAAK,SAAS,IACzBpB,QAAQ,CAACoB,EAAE,KAAK,KAAK,GAAAF,qBAAA;IACvBG,mBAAA,GACEX,KAAK,CADPW,mBAAA;EAGF,IAAMC,eAAe,GAAGR,KAAK,CAACS,MAAM,CAACT,KAAK,CAACU,KAAK,CAAC,CAACC,GAAG;EACrD,IAAAC,eAAA,GAA4B3B,KAAK,CAAC4B,QAAQ,CAAC,CAACL,eAAe,CAAC,CAAC;IAAAM,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAtDI,MAAM,GAAAF,gBAAA;IAAEG,SAAS,GAAAH,gBAAA;EAExB,IAAI,CAACE,MAAM,CAACE,QAAQ,CAACV,eAAe,CAAC,EAAE;IACrCS,SAAS,IAAAE,MAAA,CAAAC,kBAAA,CAAKJ,MAAM,IAAER,eAAe,EAAC,CAAC;EACzC;EAEA,IAAMa,UAAU,GAAGtC,sBAAsB,CAACuC,cAAc,CAACC,KAAK;EAC9D,IAAAC,gBAAA,GAAwCvC,KAAK,CAAC4B,QAAQ,CAAC;MAAA,OACrDrB,eAAe,CAAC;QACdQ,KAAK,EAALA,KAAK;QACLE,WAAW,EAAXA,WAAW;QACXmB,UAAU,EAAVA,UAAU;QACVI,MAAM,EAAE;UAAEC,KAAK,EAAEL,UAAU,CAACK,KAAK;UAAEC,MAAM,EAAE;QAAE,CAAC;QAC9CC,MAAM,EAAAC,aAAA,CAAAA,aAAA,KACD9C,sBAAsB,CAACuC,cAAc,CAACM,MAAM,GAC5ChC,KAAK,CAACO,cAAA,CACV;QACD2B,KAAK,EAAE5B,WAAW,CAACF,KAAK,CAACS,MAAM,CAACT,KAAK,CAACU,KAAK,CAAC,CAACC,GAAG,CAAC,CAACoB,OAAO,CAACC;MAC5D,CAAC,CAAC;IAAA,EACH;IAAAC,gBAAA,GAAAlB,cAAA,CAAAS,gBAAA;IAZMU,YAAY,GAAAD,gBAAA;IAAEE,eAAe,GAAAF,gBAAA;EAcpC,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,OACEnD,KAAA,CAAAc,aAAA,CAACX,qBAAqB,CAACiD,QAAQ,QAC3B,UAAAT,MAAM;MAAA,IAAAU,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA;MAAA,OACN/C,MAAM,CAAC;QACLE,KAAK,EAAEA,KAAK;QACZE,WAAW,EAAEA,WAAW;QACxBD,UAAU,EAAEA,UAAU;QACtB2B,MAAM,EAAE;UACNkB,GAAG,GAAAR,KAAA,IAAAC,KAAA,GAAEpC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C,GAAG,YAAAP,KAAA,GAAIX,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkB,GAAG,YAAAR,KAAA,GAAI,CAAC;UAC5CS,KAAK,GAAAP,KAAA,IAAAC,KAAA,GAAEtC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4C,KAAK,YAAAN,KAAA,GAAIb,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEmB,KAAK,YAAAP,KAAA,GAAI,CAAC;UAClDQ,MAAM,GAAAN,KAAA,IAAAC,KAAA,GAAExC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C,MAAM,YAAAL,KAAA,GAAIf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoB,MAAM,YAAAN,KAAA,GAAI,CAAC;UACrDO,IAAI,GAAAL,KAAA,IAAAC,KAAA,GAAE1C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8C,IAAI,YAAAJ,KAAA,GAAIjB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqB,IAAI,YAAAL,KAAA,GAAI;QAChD;MACF,CAAC,CAAC;IAAA,EAE2B;EAErC,CAAC;EAED,IAAQnC,MAAA,GAAWT,KAAK,CAAhBS,MAAA;EAER,OACExB,KAAA,CAAAc,aAAA,CAAChB,sBAAsB,QACrBE,KAAA,CAAAc,aAAA,CAACL,oBAAoB;IACnBwD,OAAO,EAAE7C,qBAAsB;IAC/B8C,YAAY;IACZrB,KAAK,EAAEsB,MAAM,CAACC;EAAU,GAEvB5C,MAAM,CAAC6C,GAAG,CAAC,UAACC,KAAK,EAAE7C,KAAK,EAAK;IAC5B,IAAM8C,UAAU,GAAGtD,WAAW,CAACqD,KAAK,CAAC5C,GAAG,CAAC;IACzC,IAAA8C,mBAAA,GAAuCD,UAAU,CAACzB,OAAO;MAAA2B,qBAAA,GAAAD,mBAAA,CAAjDE,IAAI;MAAJA,IAAI,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;MAAEE,aAAA,GAAAH,mBAAA,CAAAG,aAAA;IACrB,IAAMC,SAAS,GAAG7D,KAAK,CAACU,KAAK,KAAKA,KAAK;IAEvC,IAAIkD,aAAa,IAAI,CAACC,SAAS,EAAE;MAC/B,OAAO,IAAI;IACb;IAEA,IAAIF,IAAI,IAAI,CAAC3C,MAAM,CAACE,QAAQ,CAACqC,KAAK,CAAC5C,GAAG,CAAC,IAAI,CAACkD,SAAS,EAAE;MAErD,OAAO,IAAI;IACb;IAEA,IAAAC,oBAAA,GAYIN,UAAU,CAACzB,OAAO;MAXpBgC,YAAY,GAAAD,oBAAA,CAAZC,YAAY;MAAAC,qBAAA,GAAAF,oBAAA,CACZG,MAAM;MAANA,MAAM,GAAAD,qBAAA,cAAG,UAAAE,IAAA;QAAA,IAAGzC,MAAM,GAAiCyC,IAAA,CAAvCzC,MAAM;UAAEM,OAAA,GAA+BmC,IAAA,CAA/BnC,OAAA;QAA+B,OACjD9C,KAAA,CAAAc,aAAA,CAACjB,MAAM,EAAAqF,QAAA,KACDpC,OAAO;UACXN,MAAM,EAAEA,MAAO;UACf2C,KAAK,EAAEvF,cAAc,CAACkD,OAAO,EAAEwB,KAAK,CAACc,IAAI;QAAE,GAC3C;MAAA,CACH,GAAAL,qBAAA;MACDM,WAAW,GAAAR,oBAAA,CAAXQ,WAAW;MACXC,qBAAqB,GAAAT,oBAAA,CAArBS,qBAAqB;MACrBC,iBAAA,GAAAV,oBAAA,CAAAU,iBAAA;IAGF,OACEvF,KAAA,CAAAc,aAAA,CAACN,WAAW;MACVkB,GAAG,EAAE4C,KAAK,CAAC5C,GAAI;MACfmB,KAAK,EAAE,CAAC3C,UAAU,CAACsF,YAAY,EAAE;QAAEC,MAAM,EAAEb,SAAS,GAAG,CAAC,GAAG,CAAC;MAAE,CAAC,CAAE;MACjEc,OAAO,EAAEd,SAAU;MACnBX,OAAO,EAAE7C,qBAAsB;MAC/B0D,YAAY,EAAEA;IAAa,GAE3B9E,KAAA,CAAAc,aAAA,CAACT,yBAAyB,CAACsF,QAAQ;MAACC,KAAK,EAAE3C;IAAa,GACtDjD,KAAA,CAAAc,aAAA,CAACf,MAAM;MACL8F,OAAO,EAAEjB,SAAU;MACnBN,KAAK,EAAEC,UAAU,CAACD,KAAM;MACxBtD,UAAU,EAAEuD,UAAU,CAACvD,UAAW;MAClCqE,WAAW,EAAEA,WAAY;MACzBC,qBAAqB,EAAEA,qBAAsB;MAC7CC,iBAAiB,EAAEA,iBAAkB;MACrCP,MAAM,EAAEA,MAAM,CAAC;QACbxC,MAAM,EAAEJ,UAAU;QAClBkC,KAAK,EAAEC,UAAU,CAACD,KAAK;QACvBtD,UAAU,EACRuD,UAAU,CAACvD,UAAoD;QACjE8B,OAAO,EAAEyB,UAAU,CAACzB;MACtB,CAAC,CAAE;MACHD,KAAK,EAAEvB;IAAoB,GAE1BiD,UAAU,CAACuB,MAAM,EAAE,CACb,CAC0B,CACzB;EAElB,CAAC,CAAC,CACmB,EACvB9F,KAAA,CAAAc,aAAA,CAACV,iCAAiC,CAACuF,QAAQ;IAACC,KAAK,EAAE1C;EAAgB,GAChEC,YAAY,EAAE,CAC4B,CACtB;AAE7B;AAEA,IAAMgB,MAAM,GAAGjE,UAAU,CAAC6F,MAAM,CAAC;EAC/B3B,SAAS,EAAE;IACT4B,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}