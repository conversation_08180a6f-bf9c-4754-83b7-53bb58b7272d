{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport { nanoid } from 'nanoid/non-secure';\nimport * as React from 'react';\nimport useLatestCallback from 'use-latest-callback';\nimport NavigationHelpersContext from \"./NavigationHelpersContext\";\nimport NavigationRouteContext from \"./NavigationRouteContext\";\nimport PreventRemoveContext from \"./PreventRemoveContext\";\nvar transformPreventedRoutes = function transformPreventedRoutes(preventedRoutesMap) {\n  var preventedRoutesToTransform = _toConsumableArray(preventedRoutesMap.values());\n  var preventedRoutes = preventedRoutesToTransform.reduce(function (acc, _ref) {\n    var _acc$routeKey;\n    var routeKey = _ref.routeKey,\n      preventRemove = _ref.preventRemove;\n    acc[routeKey] = {\n      preventRemove: ((_acc$routeKey = acc[routeKey]) === null || _acc$routeKey === void 0 ? void 0 : _acc$routeKey.preventRemove) || preventRemove\n    };\n    return acc;\n  }, {});\n  return preventedRoutes;\n};\nexport default function PreventRemoveProvider(_ref2) {\n  var children = _ref2.children;\n  var _React$useState = React.useState(function () {\n      return nanoid();\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    parentId = _React$useState2[0];\n  var _React$useState3 = React.useState(new Map()),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    preventedRoutesMap = _React$useState4[0],\n    setPreventedRoutesMap = _React$useState4[1];\n  var navigation = React.useContext(NavigationHelpersContext);\n  var route = React.useContext(NavigationRouteContext);\n  var preventRemoveContextValue = React.useContext(PreventRemoveContext);\n  var setParentPrevented = preventRemoveContextValue === null || preventRemoveContextValue === void 0 ? void 0 : preventRemoveContextValue.setPreventRemove;\n  var setPreventRemove = useLatestCallback(function (id, routeKey, preventRemove) {\n    if (preventRemove && (navigation == null || navigation !== null && navigation !== void 0 && navigation.getState().routes.every(function (route) {\n      return route.key !== routeKey;\n    }))) {\n      throw new Error(`Couldn't find a route with the key ${routeKey}. Is your component inside NavigationContent?`);\n    }\n    setPreventedRoutesMap(function (prevPrevented) {\n      var _prevPrevented$get, _prevPrevented$get2;\n      if (routeKey === ((_prevPrevented$get = prevPrevented.get(id)) === null || _prevPrevented$get === void 0 ? void 0 : _prevPrevented$get.routeKey) && preventRemove === ((_prevPrevented$get2 = prevPrevented.get(id)) === null || _prevPrevented$get2 === void 0 ? void 0 : _prevPrevented$get2.preventRemove)) {\n        return prevPrevented;\n      }\n      var nextPrevented = new Map(prevPrevented);\n      if (preventRemove) {\n        nextPrevented.set(id, {\n          routeKey: routeKey,\n          preventRemove: preventRemove\n        });\n      } else {\n        nextPrevented.delete(id);\n      }\n      return nextPrevented;\n    });\n  });\n  var isPrevented = _toConsumableArray(preventedRoutesMap.values()).some(function (_ref3) {\n    var preventRemove = _ref3.preventRemove;\n    return preventRemove;\n  });\n  React.useEffect(function () {\n    if ((route === null || route === void 0 ? void 0 : route.key) !== undefined && setParentPrevented !== undefined) {\n      setParentPrevented(parentId, route.key, isPrevented);\n      return function () {\n        setParentPrevented(parentId, route.key, false);\n      };\n    }\n    return;\n  }, [parentId, isPrevented, route === null || route === void 0 ? void 0 : route.key, setParentPrevented]);\n  var value = React.useMemo(function () {\n    return {\n      setPreventRemove: setPreventRemove,\n      preventedRoutes: transformPreventedRoutes(preventedRoutesMap)\n    };\n  }, [setPreventRemove, preventedRoutesMap]);\n  return React.createElement(PreventRemoveContext.Provider, {\n    value: value\n  }, children);\n}", "map": {"version": 3, "names": ["nanoid", "React", "useLatestCallback", "NavigationHelpersContext", "NavigationRouteContext", "PreventRemoveContext", "transformPreventedRoutes", "preventedRoutesMap", "preventedRoutesToTransform", "_toConsumableArray", "values", "preventedRoutes", "reduce", "acc", "_ref", "_acc$routeKey", "routeKey", "preventRemove", "PreventRemoveProvider", "_ref2", "children", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "parentId", "_React$useState3", "Map", "_React$useState4", "setPreventedRoutesMap", "navigation", "useContext", "route", "preventRemoveContextValue", "setParentPrevented", "setPreventRemove", "id", "getState", "routes", "every", "key", "Error", "prevPrevented", "_prevPrevented$get", "_prevPrevented$get2", "get", "nextPrevented", "set", "delete", "isPrevented", "some", "_ref3", "useEffect", "undefined", "value", "useMemo", "createElement", "Provider"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/PreventRemoveProvider.tsx"], "sourcesContent": ["import { nanoid } from 'nanoid/non-secure';\nimport * as React from 'react';\nimport useLatestCallback from 'use-latest-callback';\n\nimport NavigationHelpersContext from './NavigationHelpersContext';\nimport NavigationRouteContext from './NavigationRouteContext';\nimport PreventRemoveContext, { PreventedRoutes } from './PreventRemoveContext';\n\ntype Props = {\n  children: React.ReactNode;\n};\n\ntype PreventedRoutesMap = Map<\n  string,\n  {\n    routeKey: string;\n    preventRemove: boolean;\n  }\n>;\n\n/**\n * Util function to transform map of prevented routes to a simpler object.\n */\nconst transformPreventedRoutes = (\n  preventedRoutesMap: PreventedRoutesMap\n): PreventedRoutes => {\n  const preventedRoutesToTransform = [...preventedRoutesMap.values()];\n\n  const preventedRoutes = preventedRoutesToTransform.reduce<PreventedRoutes>(\n    (acc, { routeKey, preventRemove }) => {\n      acc[routeKey] = {\n        preventRemove: acc[routeKey]?.preventRemove || preventRemove,\n      };\n      return acc;\n    },\n    {}\n  );\n\n  return preventedRoutes;\n};\n\n/**\n * Component used for managing which routes have to be prevented from removal in native-stack.\n */\nexport default function PreventRemoveProvider({ children }: Props) {\n  const [parentId] = React.useState(() => nanoid());\n  const [preventedRoutesMap, setPreventedRoutesMap] =\n    React.useState<PreventedRoutesMap>(new Map());\n\n  const navigation = React.useContext(NavigationHelpersContext);\n  const route = React.useContext(NavigationRouteContext);\n\n  const preventRemoveContextValue = React.useContext(PreventRemoveContext);\n  // take `setPreventRemove` from parent context - if exist it means we're in a nested context\n  const setParentPrevented = preventRemoveContextValue?.setPreventRemove;\n\n  const setPreventRemove = useLatestCallback(\n    (id: string, routeKey: string, preventRemove: boolean): void => {\n      if (\n        preventRemove &&\n        (navigation == null ||\n          navigation\n            ?.getState()\n            .routes.every((route) => route.key !== routeKey))\n      ) {\n        throw new Error(\n          `Couldn't find a route with the key ${routeKey}. Is your component inside NavigationContent?`\n        );\n      }\n\n      setPreventedRoutesMap((prevPrevented) => {\n        // values haven't changed - do nothing\n        if (\n          routeKey === prevPrevented.get(id)?.routeKey &&\n          preventRemove === prevPrevented.get(id)?.preventRemove\n        ) {\n          return prevPrevented;\n        }\n\n        const nextPrevented = new Map(prevPrevented);\n\n        if (preventRemove) {\n          nextPrevented.set(id, {\n            routeKey,\n            preventRemove,\n          });\n        } else {\n          nextPrevented.delete(id);\n        }\n\n        return nextPrevented;\n      });\n    }\n  );\n\n  const isPrevented = [...preventedRoutesMap.values()].some(\n    ({ preventRemove }) => preventRemove\n  );\n\n  React.useEffect(() => {\n    if (route?.key !== undefined && setParentPrevented !== undefined) {\n      // when route is defined (and setParentPrevented) it means we're in a nested stack\n      // route.key then will be the route key of parent\n      setParentPrevented(parentId, route.key, isPrevented);\n      return () => {\n        setParentPrevented(parentId, route.key, false);\n      };\n    }\n\n    return;\n  }, [parentId, isPrevented, route?.key, setParentPrevented]);\n\n  const value = React.useMemo(\n    () => ({\n      setPreventRemove,\n      preventedRoutes: transformPreventedRoutes(preventedRoutesMap),\n    }),\n    [setPreventRemove, preventedRoutesMap]\n  );\n\n  return (\n    <PreventRemoveContext.Provider value={value}>\n      {children}\n    </PreventRemoveContext.Provider>\n  );\n}\n"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,OAAOC,wBAAwB;AAC/B,OAAOC,sBAAsB;AAC7B,OAAOC,oBAAoB;AAiB3B,IAAMC,wBAAwB,GAC5B,SADIA,wBAAwBA,CAC5BC,kBAAsC,EAClB;EACpB,IAAMC,0BAA0B,GAAAC,kBAAA,CAAOF,kBAAkB,CAACG,MAAM,EAAE,CAAC;EAEnE,IAAMC,eAAe,GAAGH,0BAA0B,CAACI,MAAM,CACvD,UAACC,GAAG,EAAAC,IAAA,EAAkC;IAAA,IAAAC,aAAA;IAAA,IAA9BC,QAAQ,GAAiBF,IAAA,CAAzBE,QAAQ;MAAEC,aAAA,GAAeH,IAAA,CAAfG,aAAA;IAChBJ,GAAG,CAACG,QAAQ,CAAC,GAAG;MACdC,aAAa,EAAE,EAAAF,aAAA,GAAAF,GAAG,CAACG,QAAQ,CAAC,cAAAD,aAAA,uBAAbA,aAAA,CAAeE,aAAa,KAAIA;IACjD,CAAC;IACD,OAAOJ,GAAG;EACZ,CAAC,EACD,CAAC,CAAC,CACH;EAED,OAAOF,eAAe;AACxB,CAAC;AAKD,eAAe,SAASO,qBAAqBA,CAAAC,KAAA,EAAsB;EAAA,IAAnBC,QAAA,GAAiBD,KAAA,CAAjBC,QAAA;EAC9C,IAAAC,eAAA,GAAmBpB,KAAK,CAACqB,QAAQ,CAAC;MAAA,OAAMtB,MAAM,EAAE;IAAA,EAAC;IAAAuB,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA1CI,QAAQ,GAAAF,gBAAA;EACf,IAAAG,gBAAA,GACEzB,KAAK,CAACqB,QAAQ,CAAqB,IAAIK,GAAG,EAAE,CAAC;IAAAC,gBAAA,GAAAJ,cAAA,CAAAE,gBAAA;IADxCnB,kBAAkB,GAAAqB,gBAAA;IAAEC,qBAAqB,GAAAD,gBAAA;EAGhD,IAAME,UAAU,GAAG7B,KAAK,CAAC8B,UAAU,CAAC5B,wBAAwB,CAAC;EAC7D,IAAM6B,KAAK,GAAG/B,KAAK,CAAC8B,UAAU,CAAC3B,sBAAsB,CAAC;EAEtD,IAAM6B,yBAAyB,GAAGhC,KAAK,CAAC8B,UAAU,CAAC1B,oBAAoB,CAAC;EAExE,IAAM6B,kBAAkB,GAAGD,yBAAyB,aAAzBA,yBAAyB,uBAAzBA,yBAAyB,CAAEE,gBAAgB;EAEtE,IAAMA,gBAAgB,GAAGjC,iBAAiB,CACxC,UAACkC,EAAU,EAAEpB,QAAgB,EAAEC,aAAsB,EAAW;IAC9D,IACEA,aAAa,KACZa,UAAU,IAAI,IAAI,IACjBA,UAAU,aAAVA,UAAU,eAAVA,UAAU,CACNO,QAAQ,EAAE,CACXC,MAAM,CAACC,KAAK,CAAE,UAAAP,KAAK;MAAA,OAAKA,KAAK,CAACQ,GAAG,KAAKxB,QAAQ;IAAA,EAAC,CAAC,EACrD;MACA,MAAM,IAAIyB,KAAK,CACZ,sCAAqCzB,QAAS,+CAA8C,CAC9F;IACH;IAEAa,qBAAqB,CAAE,UAAAa,aAAa,EAAK;MAAA,IAAAC,kBAAA,EAAAC,mBAAA;MAEvC,IACE5B,QAAQ,OAAA2B,kBAAA,GAAKD,aAAa,CAACG,GAAG,CAACT,EAAE,CAAC,cAAAO,kBAAA,uBAArBA,kBAAA,CAAuB3B,QAAQ,KAC5CC,aAAa,OAAA2B,mBAAA,GAAKF,aAAa,CAACG,GAAG,CAACT,EAAE,CAAC,cAAAQ,mBAAA,uBAArBA,mBAAA,CAAuB3B,aAAa,GACtD;QACA,OAAOyB,aAAa;MACtB;MAEA,IAAMI,aAAa,GAAG,IAAInB,GAAG,CAACe,aAAa,CAAC;MAE5C,IAAIzB,aAAa,EAAE;QACjB6B,aAAa,CAACC,GAAG,CAACX,EAAE,EAAE;UACpBpB,QAAQ,EAARA,QAAQ;UACRC,aAAA,EAAAA;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL6B,aAAa,CAACE,MAAM,CAACZ,EAAE,CAAC;MAC1B;MAEA,OAAOU,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC,CACF;EAED,IAAMG,WAAW,GAAGxC,kBAAA,CAAIF,kBAAkB,CAACG,MAAM,EAAE,EAAEwC,IAAI,CACvD,UAAAC,KAAA;IAAA,IAAGlC,aAAA,GAAekC,KAAA,CAAflC,aAAA;IAAe,OAAKA,aAAa;EAAA,EACrC;EAEDhB,KAAK,CAACmD,SAAS,CAAC,YAAM;IACpB,IAAI,CAAApB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,GAAG,MAAKa,SAAS,IAAInB,kBAAkB,KAAKmB,SAAS,EAAE;MAGhEnB,kBAAkB,CAACT,QAAQ,EAAEO,KAAK,CAACQ,GAAG,EAAES,WAAW,CAAC;MACpD,OAAO,YAAM;QACXf,kBAAkB,CAACT,QAAQ,EAAEO,KAAK,CAACQ,GAAG,EAAE,KAAK,CAAC;MAChD,CAAC;IACH;IAEA;EACF,CAAC,EAAE,CAACf,QAAQ,EAAEwB,WAAW,EAAEjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,GAAG,EAAEN,kBAAkB,CAAC,CAAC;EAE3D,IAAMoB,KAAK,GAAGrD,KAAK,CAACsD,OAAO,CACzB;IAAA,OAAO;MACLpB,gBAAgB,EAAhBA,gBAAgB;MAChBxB,eAAe,EAAEL,wBAAwB,CAACC,kBAAkB;IAC9D,CAAC;EAAA,CAAC,EACF,CAAC4B,gBAAgB,EAAE5B,kBAAkB,CAAC,CACvC;EAED,OACEN,KAAA,CAAAuD,aAAA,CAACnD,oBAAoB,CAACoD,QAAQ;IAACH,KAAK,EAAEA;EAAM,GACzClC,QAAQ,CACqB;AAEpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}