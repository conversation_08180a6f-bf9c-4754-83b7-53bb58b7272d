{"ast": null, "code": "export * from \"./SafeAreaContext\";\nexport * from \"./SafeAreaView\";\nexport * from \"./InitialWindow\";\nexport * from \"./SafeArea.types\";", "map": {"version": 3, "names": [], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-safe-area-context/src/index.tsx"], "sourcesContent": ["export * from './SafeAreaContext';\nexport * from './SafeAreaView';\nexport * from './InitialWindow';\nexport * from './SafeArea.types';\n"], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}