{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport AnimatedText from \"../../Typography/AnimatedText\";\nvar LabelBackground = function LabelBackground(_ref) {\n  var labeled = _ref.labeled,\n    labelLayoutWidth = _ref.labelLayoutWidth,\n    labelLayoutHeight = _ref.labelLayoutHeight,\n    placeholderStyle = _ref.placeholderStyle,\n    baseLabelTranslateX = _ref.baseLabelTranslateX,\n    topPosition = _ref.topPosition,\n    backgroundColor = _ref.backgroundColor,\n    roundness = _ref.roundness,\n    labelStyle = _ref.labelStyle,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    testID = _ref.testID;\n  var opacity = labeled.interpolate({\n    inputRange: [0, 0.6],\n    outputRange: [1, 0]\n  });\n  var labelTranslationX = {\n    translateX: labeled.interpolate({\n      inputRange: [0, 1],\n      outputRange: [-baseLabelTranslateX, 0]\n    })\n  };\n  var labelTextScaleY = {\n    scaleY: labeled.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0.2, 1]\n    })\n  };\n  var labelTextTransform = [].concat(_toConsumableArray(labelStyle.transform), [labelTextScaleY]);\n  var isRounded = roundness > 6;\n  var roundedEdgeCover = isRounded ? React.createElement(Animated.View, {\n    key: \"labelBackground-view\",\n    pointerEvents: \"none\",\n    style: [StyleSheet.absoluteFill, styles.view, {\n      backgroundColor: backgroundColor,\n      maxHeight: Math.max(roundness / 3, 2),\n      bottom: Math.max(roundness, 2),\n      transform: [labelTranslationX],\n      opacity: opacity\n    }]\n  }) : null;\n  return [roundedEdgeCover, React.createElement(AnimatedText, {\n    key: \"labelBackground-text\",\n    testID: `${testID}-label-background`,\n    style: [placeholderStyle, labelStyle, styles.outlinedLabel, {\n      top: topPosition + 1,\n      width: labelLayoutWidth - placeholderStyle.paddingHorizontal,\n      height: labelLayoutHeight,\n      backgroundColor: backgroundColor,\n      opacity: opacity,\n      transform: labelTextTransform\n    }],\n    numberOfLines: 1,\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  })];\n};\nexport default LabelBackground;\nvar styles = StyleSheet.create({\n  view: {\n    position: 'absolute',\n    top: 6,\n    left: 10,\n    width: 12\n  },\n  outlinedLabel: {\n    position: 'absolute',\n    left: 8,\n    paddingHorizontal: 0,\n    color: 'transparent'\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "AnimatedText", "LabelBackground", "_ref", "labeled", "labelLayoutWidth", "labelLayoutHeight", "placeholder<PERSON><PERSON><PERSON>", "baseLabelTranslateX", "topPosition", "backgroundColor", "roundness", "labelStyle", "maxFontSizeMultiplier", "testID", "opacity", "interpolate", "inputRange", "outputRange", "labelTranslationX", "translateX", "labelTextScaleY", "scaleY", "labelTextTransform", "concat", "_toConsumableArray", "transform", "isRounded", "roundedEdgeCover", "createElement", "View", "key", "pointerEvents", "style", "absoluteFill", "styles", "view", "maxHeight", "Math", "max", "bottom", "<PERSON><PERSON><PERSON><PERSON>", "top", "width", "paddingHorizontal", "height", "numberOfLines", "create", "position", "left", "color"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/TextInput/Label/LabelBackground.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated, StyleSheet } from 'react-native';\n\nimport AnimatedText from '../../Typography/AnimatedText';\nimport type { LabelBackgroundProps } from '../types';\n\nconst LabelBackground = ({\n  labeled,\n  labelLayoutWidth,\n  labelLayoutHeight,\n  placeholderStyle,\n  baseLabelTranslateX,\n  topPosition,\n  backgroundColor,\n  roundness,\n  labelStyle,\n  maxFontSizeMultiplier,\n  testID,\n}: LabelBackgroundProps) => {\n  const opacity = labeled.interpolate({\n    inputRange: [0, 0.6],\n    outputRange: [1, 0],\n  });\n\n  const labelTranslationX = {\n    translateX: labeled.interpolate({\n      inputRange: [0, 1],\n      outputRange: [-baseLabelTranslateX, 0],\n    }),\n  };\n\n  const labelTextScaleY = {\n    scaleY: labeled.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0.2, 1],\n    }),\n  };\n\n  const labelTextTransform = [...labelStyle.transform, labelTextScaleY];\n\n  const isRounded = roundness > 6;\n  const roundedEdgeCover = isRounded ? (\n    <Animated.View\n      key=\"labelBackground-view\"\n      pointerEvents=\"none\"\n      style={[\n        StyleSheet.absoluteFill,\n        styles.view,\n        {\n          backgroundColor,\n          maxHeight: Math.max(roundness / 3, 2),\n          bottom: Math.max(roundness, 2),\n          transform: [labelTranslationX],\n          opacity,\n        },\n      ]}\n    />\n  ) : null;\n\n  return [\n    roundedEdgeCover,\n    <AnimatedText\n      key=\"labelBackground-text\"\n      testID={`${testID}-label-background`}\n      style={[\n        placeholderStyle,\n        labelStyle,\n        styles.outlinedLabel,\n        {\n          top: topPosition + 1,\n          width: labelLayoutWidth - placeholderStyle.paddingHorizontal,\n          height: labelLayoutHeight,\n          backgroundColor,\n          opacity,\n          transform: labelTextTransform,\n        },\n      ]}\n      numberOfLines={1}\n      maxFontSizeMultiplier={maxFontSizeMultiplier}\n    />,\n  ];\n};\n\nexport default LabelBackground;\n\nconst styles = StyleSheet.create({\n  view: {\n    position: 'absolute',\n    top: 6,\n    left: 10,\n    width: 12,\n  },\n  // eslint-disable-next-line react-native/no-color-literals\n  outlinedLabel: {\n    position: 'absolute',\n    left: 8,\n    paddingHorizontal: 0,\n    color: 'transparent',\n  },\n});\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAG9B,OAAOC,YAAY;AAGnB,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,IAAA,EAYI;EAAA,IAX1BC,OAAO,GAWcD,IAAA,CAXrBC,OAAO;IACPC,gBAAgB,GAUKF,IAAA,CAVrBE,gBAAgB;IAChBC,iBAAiB,GASIH,IAAA,CATrBG,iBAAiB;IACjBC,gBAAgB,GAQKJ,IAAA,CARrBI,gBAAgB;IAChBC,mBAAmB,GAOEL,IAAA,CAPrBK,mBAAmB;IACnBC,WAAW,GAMUN,IAAA,CANrBM,WAAW;IACXC,eAAe,GAKMP,IAAA,CALrBO,eAAe;IACfC,SAAS,GAIYR,IAAA,CAJrBQ,SAAS;IACTC,UAAU,GAGWT,IAAA,CAHrBS,UAAU;IACVC,qBAAqB,GAEAV,IAAA,CAFrBU,qBAAqB;IACrBC,MAAA,GACqBX,IAAA,CADrBW,MAAA;EAEA,IAAMC,OAAO,GAAGX,OAAO,CAACY,WAAW,CAAC;IAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC;EAEF,IAAMC,iBAAiB,GAAG;IACxBC,UAAU,EAAEhB,OAAO,CAACY,WAAW,CAAC;MAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAACV,mBAAmB,EAAE,CAAC;IACvC,CAAC;EACH,CAAC;EAED,IAAMa,eAAe,GAAG;IACtBC,MAAM,EAAElB,OAAO,CAACY,WAAW,CAAC;MAC1BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;EACH,CAAC;EAED,IAAMK,kBAAkB,MAAAC,MAAA,CAAAC,kBAAA,CAAOb,UAAU,CAACc,SAAS,IAAEL,eAAe,EAAC;EAErE,IAAMM,SAAS,GAAGhB,SAAS,GAAG,CAAC;EAC/B,IAAMiB,gBAAgB,GAAGD,SAAS,GAChC7B,KAAA,CAAA+B,aAAA,CAAC9B,QAAQ,CAAC+B,IAAI;IACZC,GAAG,EAAC,sBAAsB;IAC1BC,aAAa,EAAC,MAAM;IACpBC,KAAK,EAAE,CACLjC,UAAU,CAACkC,YAAY,EACvBC,MAAM,CAACC,IAAI,EACX;MACE1B,eAAe,EAAfA,eAAe;MACf2B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC5B,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC;MACrC6B,MAAM,EAAEF,IAAI,CAACC,GAAG,CAAC5B,SAAS,EAAE,CAAC,CAAC;MAC9Be,SAAS,EAAE,CAACP,iBAAiB,CAAC;MAC9BJ,OAAA,EAAAA;IACF,CAAC;EACD,CACH,CAAC,GACA,IAAI;EAER,OAAO,CACLa,gBAAgB,EAChB9B,KAAA,CAAA+B,aAAA,CAAC5B,YAAY;IACX8B,GAAG,EAAC,sBAAsB;IAC1BjB,MAAM,EAAG,GAAEA,MAAO,mBAAmB;IACrCmB,KAAK,EAAE,CACL1B,gBAAgB,EAChBK,UAAU,EACVuB,MAAM,CAACM,aAAa,EACpB;MACEC,GAAG,EAAEjC,WAAW,GAAG,CAAC;MACpBkC,KAAK,EAAEtC,gBAAgB,GAAGE,gBAAgB,CAACqC,iBAAiB;MAC5DC,MAAM,EAAEvC,iBAAiB;MACzBI,eAAe,EAAfA,eAAe;MACfK,OAAO,EAAPA,OAAO;MACPW,SAAS,EAAEH;IACb,CAAC,CACD;IACFuB,aAAa,EAAE,CAAE;IACjBjC,qBAAqB,EAAEA;EAAsB,CAC9C,CAAC,CACH;AACH,CAAC;AAED,eAAeX,eAAe;AAE9B,IAAMiC,MAAM,GAAGnC,UAAU,CAAC+C,MAAM,CAAC;EAC/BX,IAAI,EAAE;IACJY,QAAQ,EAAE,UAAU;IACpBN,GAAG,EAAE,CAAC;IACNO,IAAI,EAAE,EAAE;IACRN,KAAK,EAAE;EACT,CAAC;EAEDF,aAAa,EAAE;IACbO,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPL,iBAAiB,EAAE,CAAC;IACpBM,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}