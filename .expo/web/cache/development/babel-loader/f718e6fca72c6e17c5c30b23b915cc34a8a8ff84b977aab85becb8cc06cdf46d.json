{"ast": null, "code": "import NativeModules from \"react-native-web/dist/exports/NativeModules\";\nvar NativeIconAPI = NativeModules.RNVectorIconsManager || NativeModules.RNVectorIconsModule;\nexport default function ensureNativeModuleAvailable() {\n  if (!NativeIconAPI) {\n    throw new Error('The native RNVectorIcons API is not available, did you properly integrate the module? Please verify your autolinking setup and recompile.');\n  }\n}", "map": {"version": 3, "names": ["NativeIconAPI", "NativeModules", "RNVectorIconsManager", "RNVectorIconsModule", "ensureNativeModuleAvailable", "Error"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/ensure-native-module-available.js"], "sourcesContent": ["import {NativeModules} from \"react-native\";\n\nconst NativeIconAPI =\n  NativeModules.RNVectorIconsManager || NativeModules.RNVectorIconsModule;\n\nexport default function ensureNativeModuleAvailable() {\n  if (!NativeIconAPI) {\n    throw new Error(\n      'The native RNVectorIcons API is not available, did you properly integrate the module? Please verify your autolinking setup and recompile.'\n    );\n  }\n}\n"], "mappings": ";AAEA,IAAMA,aAAa,GACjBC,aAAa,CAACC,oBAAoB,IAAID,aAAa,CAACE,mBAAmB;AAEzE,eAAe,SAASC,2BAA2BA,CAAA,EAAG;EACpD,IAAI,CAACJ,aAAa,EAAE;IAClB,MAAM,IAAIK,KAAK,CACb,2IACF,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}