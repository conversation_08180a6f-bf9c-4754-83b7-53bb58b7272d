{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport invariant from 'invariant';\nimport NativeEventEmitter from \"react-native-web/dist/exports/NativeEventEmitter\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nvar nativeEmitterSubscriptionKey = '@@nativeEmitterSubscription@@';\nexport var LegacyEventEmitter = function () {\n  function LegacyEventEmitter(nativeModule) {\n    _classCallCheck(this, LegacyEventEmitter);\n    this._listenerCount = 0;\n    if (nativeModule.__expo_module_name__) {\n      return nativeModule;\n    }\n    this._nativeModule = nativeModule;\n    this._eventEmitter = new NativeEventEmitter(nativeModule);\n  }\n  return _createClass(LegacyEventEmitter, [{\n    key: \"addListener\",\n    value: function addListener(eventName, listener) {\n      var _this = this;\n      if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.startObserving) {\n        this._nativeModule.startObserving();\n      }\n      this._listenerCount++;\n      var nativeEmitterSubscription = this._eventEmitter.addListener(eventName, listener);\n      var subscription = _defineProperty(_defineProperty({}, nativeEmitterSubscriptionKey, nativeEmitterSubscription), \"remove\", function remove() {\n        _this.removeSubscription(subscription);\n      });\n      return subscription;\n    }\n  }, {\n    key: \"removeAllListeners\",\n    value: function removeAllListeners(eventName) {\n      var removedListenerCount = this._eventEmitter.listenerCount ? this._eventEmitter.listenerCount(eventName) : this._eventEmitter.listeners(eventName).length;\n      this._eventEmitter.removeAllListeners(eventName);\n      this._listenerCount -= removedListenerCount;\n      invariant(this._listenerCount >= 0, `EventEmitter must have a non-negative number of listeners`);\n      if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n        this._nativeModule.stopObserving();\n      }\n    }\n  }, {\n    key: \"removeSubscription\",\n    value: function removeSubscription(subscription) {\n      var state = subscription;\n      var nativeEmitterSubscription = state[nativeEmitterSubscriptionKey];\n      if (!nativeEmitterSubscription) {\n        return;\n      }\n      if ('remove' in nativeEmitterSubscription) {\n        nativeEmitterSubscription.remove == null ? void 0 : nativeEmitterSubscription.remove();\n      }\n      this._listenerCount--;\n      delete state[nativeEmitterSubscriptionKey];\n      subscription.remove = function () {};\n      if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n        this._nativeModule.stopObserving();\n      }\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(eventName) {\n      var _this$_eventEmitter;\n      for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        params[_key - 1] = arguments[_key];\n      }\n      (_this$_eventEmitter = this._eventEmitter).emit.apply(_this$_eventEmitter, [eventName].concat(params));\n    }\n  }]);\n}();", "map": {"version": 3, "names": ["invariant", "NativeEventEmitter", "Platform", "nativeEmitterSubscriptionKey", "LegacyEventEmitter", "nativeModule", "_classCallCheck", "_listenerCount", "__expo_module_name__", "_nativeModule", "_eventEmitter", "_createClass", "key", "value", "addListener", "eventName", "listener", "_this", "OS", "startObserving", "nativeEmitterSubscription", "subscription", "_defineProperty", "remove", "removeSubscription", "removeAllListeners", "removedListenerCount", "listenerCount", "listeners", "length", "stopObserving", "state", "emit", "_this$_eventEmitter", "_len", "arguments", "params", "Array", "_key", "apply", "concat"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/expo-modules-core/src/LegacyEventEmitter.ts"], "sourcesContent": ["import invariant from 'invariant';\nimport { NativeEventEmitter, Platform } from 'react-native';\n\nimport { EventSubscription } from './EventEmitter';\n\nconst nativeEmitterSubscriptionKey = '@@nativeEmitterSubscription@@' as const;\n\ntype SubscriptionState = {\n  // NOTE(@kitten): Since this is legacy/deprecated, we don't need to be exact about types here\n  [Key in typeof nativeEmitterSubscriptionKey]?: {\n    remove?(): void;\n  };\n} & EventSubscription;\n\ntype NativeModule = {\n  __expo_module_name__?: string;\n  startObserving?: () => void;\n  stopObserving?: () => void;\n\n  // Erase these types as they would conflict with the new NativeModule type.\n  // This EventEmitter is deprecated anyway.\n  addListener?: any;\n  removeListeners?: any;\n};\n\n/**\n * @deprecated Deprecated in favor of `EventEmitter`.\n */\nexport class LegacyEventEmitter {\n  _listenerCount = 0;\n\n  // @ts-expect-error\n  _nativeModule: NativeModule;\n\n  // @ts-expect-error\n  _eventEmitter: NativeEventEmitter;\n\n  constructor(nativeModule: NativeModule) {\n    // If the native module is a new module, just return it back as it's already an event emitter.\n    // This is for backwards compatibility until we stop using this legacy class in other packages.\n    if (nativeModule.__expo_module_name__) {\n      // @ts-expect-error\n      return nativeModule;\n    }\n    this._nativeModule = nativeModule;\n    this._eventEmitter = new NativeEventEmitter(nativeModule as any);\n  }\n\n  addListener<T>(eventName: string, listener: (event: T) => void): EventSubscription {\n    if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.startObserving) {\n      this._nativeModule.startObserving();\n    }\n\n    this._listenerCount++;\n    const nativeEmitterSubscription = this._eventEmitter.addListener(eventName, listener);\n    const subscription: SubscriptionState = {\n      [nativeEmitterSubscriptionKey]: nativeEmitterSubscription,\n      remove: () => {\n        this.removeSubscription(subscription);\n      },\n    };\n    return subscription;\n  }\n\n  removeAllListeners(eventName: string): void {\n    // @ts-ignore: the EventEmitter interface has been changed in react-native@0.64.0\n    const removedListenerCount = this._eventEmitter.listenerCount\n      ? // @ts-ignore: this is available since 0.64\n        this._eventEmitter.listenerCount(eventName)\n      : // @ts-ignore: this is available in older versions\n        this._eventEmitter.listeners(eventName).length;\n    this._eventEmitter.removeAllListeners(eventName);\n    this._listenerCount -= removedListenerCount;\n    invariant(\n      this._listenerCount >= 0,\n      `EventEmitter must have a non-negative number of listeners`\n    );\n\n    if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n      this._nativeModule.stopObserving();\n    }\n  }\n\n  removeSubscription(subscription: EventSubscription): void {\n    const state = subscription as SubscriptionState;\n    const nativeEmitterSubscription = state[nativeEmitterSubscriptionKey];\n    if (!nativeEmitterSubscription) {\n      return;\n    }\n\n    if ('remove' in nativeEmitterSubscription) {\n      nativeEmitterSubscription.remove?.();\n    }\n    this._listenerCount--;\n\n    // Ensure that the emitter's internal state remains correct even if `removeSubscription` is\n    // called again with the same subscription\n    delete state[nativeEmitterSubscriptionKey];\n\n    // Release closed-over references to the emitter\n    subscription.remove = () => {};\n\n    if (!this._listenerCount && Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n      this._nativeModule.stopObserving();\n    }\n  }\n\n  emit(eventName: string, ...params: any[]): void {\n    this._eventEmitter.emit(eventName, ...params);\n  }\n}\n"], "mappings": ";;;AAAA,OAAOA,SAAS,MAAM,WAAW;AAAC,OAAAC,kBAAA;AAAA,OAAAC,QAAA;AAKlC,IAAMC,4BAA4B,GAAG,+BAAwC;AAuB7E,WAAaC,kBAAkB;EAS7B,SAAAA,mBAAYC,YAA0B,EAAE;IAAAC,eAAA,OAAAF,kBAAA;IAAA,KARxCG,cAAc,GAAG,CAAC;IAWhB,IAAIF,YAAY,CAACG,oBAAoB,EAAE;MAErC,OAAOH,YAAY;IACrB;IACA,IAAI,CAACI,aAAa,GAAGJ,YAAY;IACjC,IAAI,CAACK,aAAa,GAAG,IAAIT,kBAAkB,CAACI,YAAmB,CAAC;EAClE;EAAC,OAAAM,YAAA,CAAAP,kBAAA;IAAAQ,GAAA;IAAAC,KAAA,EAED,SAAAC,WAAWA,CAAIC,SAAiB,EAAEC,QAA4B,EAAqB;MAAA,IAAAC,KAAA;MACjF,IAAI,CAAC,IAAI,CAACV,cAAc,IAAIL,QAAQ,CAACgB,EAAE,KAAK,KAAK,IAAI,IAAI,CAACT,aAAa,CAACU,cAAc,EAAE;QACtF,IAAI,CAACV,aAAa,CAACU,cAAc,CAAC,CAAC;MACrC;MAEA,IAAI,CAACZ,cAAc,EAAE;MACrB,IAAMa,yBAAyB,GAAG,IAAI,CAACV,aAAa,CAACI,WAAW,CAACC,SAAS,EAAEC,QAAQ,CAAC;MACrF,IAAMK,YAA+B,GAAAC,eAAA,CAAAA,eAAA,KAClCnB,4BAA4B,EAAGiB,yBAAyB,aACjD,SAARG,MAAMA,CAAA,EAAQ;QACZN,KAAI,CAACO,kBAAkB,CAACH,YAAY,CAAC;MACvC,CAAC,CACF;MACD,OAAOA,YAAY;IACrB;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAED,SAAAY,kBAAkBA,CAACV,SAAiB,EAAQ;MAE1C,IAAMW,oBAAoB,GAAG,IAAI,CAAChB,aAAa,CAACiB,aAAa,GAEzD,IAAI,CAACjB,aAAa,CAACiB,aAAa,CAACZ,SAAS,CAAC,GAE3C,IAAI,CAACL,aAAa,CAACkB,SAAS,CAACb,SAAS,CAAC,CAACc,MAAM;MAClD,IAAI,CAACnB,aAAa,CAACe,kBAAkB,CAACV,SAAS,CAAC;MAChD,IAAI,CAACR,cAAc,IAAImB,oBAAoB;MAC3C1B,SAAS,CACP,IAAI,CAACO,cAAc,IAAI,CAAC,EACxB,2DACF,CAAC;MAED,IAAI,CAAC,IAAI,CAACA,cAAc,IAAIL,QAAQ,CAACgB,EAAE,KAAK,KAAK,IAAI,IAAI,CAACT,aAAa,CAACqB,aAAa,EAAE;QACrF,IAAI,CAACrB,aAAa,CAACqB,aAAa,CAAC,CAAC;MACpC;IACF;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAED,SAAAW,kBAAkBA,CAACH,YAA+B,EAAQ;MACxD,IAAMU,KAAK,GAAGV,YAAiC;MAC/C,IAAMD,yBAAyB,GAAGW,KAAK,CAAC5B,4BAA4B,CAAC;MACrE,IAAI,CAACiB,yBAAyB,EAAE;QAC9B;MACF;MAEA,IAAI,QAAQ,IAAIA,yBAAyB,EAAE;QACzCA,yBAAyB,CAACG,MAAM,oBAAhCH,yBAAyB,CAACG,MAAM,CAAG,CAAC;MACtC;MACA,IAAI,CAAChB,cAAc,EAAE;MAIrB,OAAOwB,KAAK,CAAC5B,4BAA4B,CAAC;MAG1CkB,YAAY,CAACE,MAAM,GAAG,YAAM,CAAC,CAAC;MAE9B,IAAI,CAAC,IAAI,CAAChB,cAAc,IAAIL,QAAQ,CAACgB,EAAE,KAAK,KAAK,IAAI,IAAI,CAACT,aAAa,CAACqB,aAAa,EAAE;QACrF,IAAI,CAACrB,aAAa,CAACqB,aAAa,CAAC,CAAC;MACpC;IACF;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAED,SAAAmB,IAAIA,CAACjB,SAAiB,EAA0B;MAAA,IAAAkB,mBAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAN,MAAA,EAArBO,MAAM,OAAAC,KAAA,CAAAH,IAAA,OAAAA,IAAA,WAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAANF,MAAM,CAAAE,IAAA,QAAAH,SAAA,CAAAG,IAAA;MAAA;MAC/B,CAAAL,mBAAA,OAAI,CAACvB,aAAa,EAACsB,IAAI,CAAAO,KAAA,CAAAN,mBAAA,GAAClB,SAAS,EAAAyB,MAAA,CAAKJ,MAAM,EAAC;IAC/C;EAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}