{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"status\", \"label\", \"onPress\", \"onLongPress\", \"labelStyle\", \"theme\", \"testID\", \"mode\", \"position\", \"accessibilityLabel\", \"disabled\", \"labelVariant\", \"labelMaxFontSizeMultiplier\", \"rippleColor\", \"background\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Checkbox from \"./Checkbox\";\nimport CheckboxAndroid from \"./CheckboxAndroid\";\nimport CheckboxIOS from \"./CheckboxIOS\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar CheckboxItem = function CheckboxItem(_ref) {\n  var style = _ref.style,\n    status = _ref.status,\n    label = _ref.label,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    labelStyle = _ref.labelStyle,\n    themeOverrides = _ref.theme,\n    testID = _ref.testID,\n    mode = _ref.mode,\n    _ref$position = _ref.position,\n    position = _ref$position === void 0 ? 'trailing' : _ref$position,\n    _ref$accessibilityLab = _ref.accessibilityLabel,\n    accessibilityLabel = _ref$accessibilityLab === void 0 ? label : _ref$accessibilityLab,\n    disabled = _ref.disabled,\n    _ref$labelVariant = _ref.labelVariant,\n    labelVariant = _ref$labelVariant === void 0 ? 'bodyLarge' : _ref$labelVariant,\n    _ref$labelMaxFontSize = _ref.labelMaxFontSizeMultiplier,\n    labelMaxFontSizeMultiplier = _ref$labelMaxFontSize === void 0 ? 1.5 : _ref$labelMaxFontSize,\n    rippleColor = _ref.rippleColor,\n    background = _ref.background,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var checkboxProps = _objectSpread(_objectSpread({}, props), {}, {\n    status: status,\n    theme: theme,\n    disabled: disabled\n  });\n  var isLeading = position === 'leading';\n  var checkbox;\n  if (mode === 'android') {\n    checkbox = React.createElement(CheckboxAndroid, checkboxProps);\n  } else if (mode === 'ios') {\n    checkbox = React.createElement(CheckboxIOS, checkboxProps);\n  } else {\n    checkbox = React.createElement(Checkbox, checkboxProps);\n  }\n  var textColor = theme.isV3 ? theme.colors.onSurface : theme.colors.text;\n  var disabledTextColor = theme.isV3 ? theme.colors.onSurfaceDisabled : theme.colors.disabled;\n  var textAlign = isLeading ? 'right' : 'left';\n  var computedStyle = {\n    color: disabled ? disabledTextColor : textColor,\n    textAlign: textAlign\n  };\n  return React.createElement(TouchableRipple, {\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"checkbox\",\n    accessibilityState: {\n      checked: status === 'checked',\n      disabled: disabled\n    },\n    onPress: onPress,\n    onLongPress: onLongPress,\n    testID: testID,\n    disabled: disabled,\n    rippleColor: rippleColor,\n    theme: theme,\n    background: background\n  }, React.createElement(View, {\n    style: [styles.container, style],\n    pointerEvents: \"none\",\n    importantForAccessibility: \"no-hide-descendants\"\n  }, isLeading && checkbox, React.createElement(Text, {\n    variant: labelVariant,\n    testID: `${testID}-text`,\n    maxFontSizeMultiplier: labelMaxFontSizeMultiplier,\n    style: [styles.label, !theme.isV3 && styles.font, computedStyle, labelStyle]\n  }, label), !isLeading && checkbox));\n};\nCheckboxItem.displayName = 'Checkbox.Item';\nexport default CheckboxItem;\nexport { CheckboxItem };\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingVertical: 8,\n    paddingHorizontal: 16\n  },\n  label: {\n    flexShrink: 1,\n    flexGrow: 1\n  },\n  font: {\n    fontSize: 16\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "Checkbox", "CheckboxAndroid", "CheckboxIOS", "useInternalTheme", "TouchableRipple", "Text", "CheckboxItem", "_ref", "style", "status", "label", "onPress", "onLongPress", "labelStyle", "themeOverrides", "theme", "testID", "mode", "_ref$position", "position", "_ref$accessibilityLab", "accessibilityLabel", "disabled", "_ref$labelVariant", "labelVariant", "_ref$labelMaxFontSize", "labelMaxFontSizeMultiplier", "rippleColor", "background", "props", "_objectWithoutProperties", "_excluded", "checkboxProps", "_objectSpread", "isLeading", "checkbox", "createElement", "textColor", "isV3", "colors", "onSurface", "text", "disabledTextColor", "onSurfaceDisabled", "textAlign", "computedStyle", "color", "accessibilityRole", "accessibilityState", "checked", "styles", "container", "pointerEvents", "importantForAccessibility", "variant", "maxFontSizeMultiplier", "font", "displayName", "create", "flexDirection", "alignItems", "justifyContent", "paddingVertical", "paddingHorizontal", "flexShrink", "flexGrow", "fontSize"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Checkbox/CheckboxItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  ColorValue,\n  GestureResponderEvent,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport Checkbox from './Checkbox';\nimport CheckboxAndroid from './CheckboxAndroid';\nimport CheckboxIOS from './CheckboxIOS';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp, MD3TypescaleKey } from '../../types';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Status of checkbox.\n   */\n  status: 'checked' | 'unchecked' | 'indeterminate';\n  /**\n   * Whether checkbox is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Label to be displayed on the item.\n   */\n  label: string;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the touchable. This is read by the screen reader when the user taps the touchable.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Custom color for unchecked checkbox.\n   */\n  uncheckedColor?: string;\n  /**\n   * Custom color for checkbox.\n   */\n  color?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Additional styles for container View.\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  /**\n   * Style that is passed to Label element.\n   */\n  labelStyle?: StyleProp<TextStyle>;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Label text variant defines appropriate text styles for type role and its size.\n   * Available variants:\n   *\n   *  Display: `displayLarge`, `displayMedium`, `displaySmall`\n   *\n   *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`\n   *\n   *  Title: `titleLarge`, `titleMedium`, `titleSmall`\n   *\n   *  Label:  `labelLarge`, `labelMedium`, `labelSmall`\n   *\n   *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`\n   */\n  labelVariant?: keyof typeof MD3TypescaleKey;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n  /**\n   * Checkbox control position.\n   */\n  position?: 'leading' | 'trailing';\n  /**\n   * Whether `<Checkbox.Android />` or `<Checkbox.IOS />` should be used.\n   * Left undefined `<Checkbox />` will be used.\n   */\n  mode?: 'android' | 'ios';\n};\n\n/**\n * Checkbox.Item allows you to press the whole row (item) instead of only the Checkbox.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Checkbox } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <View>\n *     <Checkbox.Item label=\"Item\" status=\"checked\" />\n *   </View>\n * );\n *\n * export default MyComponent;\n *```\n */\n\nconst CheckboxItem = ({\n  style,\n  status,\n  label,\n  onPress,\n  onLongPress,\n  labelStyle,\n  theme: themeOverrides,\n  testID,\n  mode,\n  position = 'trailing',\n  accessibilityLabel = label,\n  disabled,\n  labelVariant = 'bodyLarge',\n  labelMaxFontSizeMultiplier = 1.5,\n  rippleColor,\n  background,\n  ...props\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const checkboxProps = { ...props, status, theme, disabled };\n  const isLeading = position === 'leading';\n  let checkbox;\n\n  if (mode === 'android') {\n    checkbox = <CheckboxAndroid {...checkboxProps} />;\n  } else if (mode === 'ios') {\n    checkbox = <CheckboxIOS {...checkboxProps} />;\n  } else {\n    checkbox = <Checkbox {...checkboxProps} />;\n  }\n\n  const textColor = theme.isV3 ? theme.colors.onSurface : theme.colors.text;\n  const disabledTextColor = theme.isV3\n    ? theme.colors.onSurfaceDisabled\n    : theme.colors.disabled;\n  const textAlign = isLeading ? 'right' : 'left';\n\n  const computedStyle = {\n    color: disabled ? disabledTextColor : textColor,\n    textAlign,\n  } as TextStyle;\n\n  return (\n    <TouchableRipple\n      accessibilityLabel={accessibilityLabel}\n      accessibilityRole=\"checkbox\"\n      accessibilityState={{\n        checked: status === 'checked',\n        disabled,\n      }}\n      onPress={onPress}\n      onLongPress={onLongPress}\n      testID={testID}\n      disabled={disabled}\n      rippleColor={rippleColor}\n      theme={theme}\n      background={background}\n    >\n      <View\n        style={[styles.container, style]}\n        pointerEvents=\"none\"\n        importantForAccessibility=\"no-hide-descendants\"\n      >\n        {isLeading && checkbox}\n        <Text\n          variant={labelVariant}\n          testID={`${testID}-text`}\n          maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n          style={[\n            styles.label,\n            !theme.isV3 && styles.font,\n            computedStyle,\n            labelStyle,\n          ]}\n        >\n          {label}\n        </Text>\n        {!isLeading && checkbox}\n      </View>\n    </TouchableRipple>\n  );\n};\n\nCheckboxItem.displayName = 'Checkbox.Item';\n\nexport default CheckboxItem;\n\n// @component-docs ignore-next-line\nexport { CheckboxItem };\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingVertical: 8,\n    paddingHorizontal: 16,\n  },\n  label: {\n    flexShrink: 1,\n    flexGrow: 1,\n  },\n  font: {\n    fontSize: 16,\n  },\n});\n"], "mappings": ";;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAY9B,OAAOC,QAAQ;AACf,OAAOC,eAAe;AACtB,OAAOC,WAAW;AAClB,SAASC,gBAAgB;AAEzB,OAAOC,eAAe;AACtB,OAAOC,IAAI;AA+GX,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAGC,IAAA,EAkBR;EAAA,IAjBXC,KAAK,GAiBCD,IAAA,CAjBNC,KAAK;IACLC,MAAM,GAgBAF,IAAA,CAhBNE,MAAM;IACNC,KAAK,GAeCH,IAAA,CAfNG,KAAK;IACLC,OAAO,GAcDJ,IAAA,CAdNI,OAAO;IACPC,WAAW,GAaLL,IAAA,CAbNK,WAAW;IACXC,UAAU,GAYJN,IAAA,CAZNM,UAAU;IACHC,cAAc,GAWfP,IAAA,CAXNQ,KAAK;IACLC,MAAM,GAUAT,IAAA,CAVNS,MAAM;IACNC,IAAI,GASEV,IAAA,CATNU,IAAI;IAAAC,aAAA,GASEX,IAAA,CARNY,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,UAAU,GAAAA,aAAA;IAAAE,qBAAA,GAQfb,IAAA,CAPNc,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGV,KAAK,GAAAU,qBAAA;IAC1BE,QAAQ,GAMFf,IAAA,CANNe,QAAQ;IAAAC,iBAAA,GAMFhB,IAAA,CALNiB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,WAAW,GAAAA,iBAAA;IAAAE,qBAAA,GAKpBlB,IAAA,CAJNmB,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAG,GAAG,GAAAA,qBAAA;IAChCE,WAAW,GAGLpB,IAAA,CAHNoB,WAAW;IACXC,UAAU,GAEJrB,IAAA,CAFNqB,UAAU;IACPC,KAAA,GAAAC,wBAAA,CACGvB,IAAA,EAAAwB,SAAA;EACN,IAAMhB,KAAK,GAAGZ,gBAAgB,CAACW,cAAc,CAAC;EAC9C,IAAMkB,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAAQJ,KAAK;IAAEpB,MAAM,EAANA,MAAM;IAAEM,KAAK,EAALA,KAAK;IAAEO,QAAA,EAAAA;EAAA,EAAU;EAC3D,IAAMY,SAAS,GAAGf,QAAQ,KAAK,SAAS;EACxC,IAAIgB,QAAQ;EAEZ,IAAIlB,IAAI,KAAK,SAAS,EAAE;IACtBkB,QAAQ,GAAGtC,KAAA,CAAAuC,aAAA,CAACnC,eAAe,EAAK+B,aAAgB,CAAC;EACnD,CAAC,MAAM,IAAIf,IAAI,KAAK,KAAK,EAAE;IACzBkB,QAAQ,GAAGtC,KAAA,CAAAuC,aAAA,CAAClC,WAAW,EAAK8B,aAAgB,CAAC;EAC/C,CAAC,MAAM;IACLG,QAAQ,GAAGtC,KAAA,CAAAuC,aAAA,CAACpC,QAAQ,EAAKgC,aAAgB,CAAC;EAC5C;EAEA,IAAMK,SAAS,GAAGtB,KAAK,CAACuB,IAAI,GAAGvB,KAAK,CAACwB,MAAM,CAACC,SAAS,GAAGzB,KAAK,CAACwB,MAAM,CAACE,IAAI;EACzE,IAAMC,iBAAiB,GAAG3B,KAAK,CAACuB,IAAI,GAChCvB,KAAK,CAACwB,MAAM,CAACI,iBAAiB,GAC9B5B,KAAK,CAACwB,MAAM,CAACjB,QAAQ;EACzB,IAAMsB,SAAS,GAAGV,SAAS,GAAG,OAAO,GAAG,MAAM;EAE9C,IAAMW,aAAa,GAAG;IACpBC,KAAK,EAAExB,QAAQ,GAAGoB,iBAAiB,GAAGL,SAAS;IAC/CO,SAAA,EAAAA;EACF,CAAc;EAEd,OACE/C,KAAA,CAAAuC,aAAA,CAAChC,eAAe;IACdiB,kBAAkB,EAAEA,kBAAmB;IACvC0B,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAClBC,OAAO,EAAExC,MAAM,KAAK,SAAS;MAC7Ba,QAAA,EAAAA;IACF,CAAE;IACFX,OAAO,EAAEA,OAAQ;IACjBC,WAAW,EAAEA,WAAY;IACzBI,MAAM,EAAEA,MAAO;IACfM,QAAQ,EAAEA,QAAS;IACnBK,WAAW,EAAEA,WAAY;IACzBZ,KAAK,EAAEA,KAAM;IACba,UAAU,EAAEA;EAAW,GAEvB/B,KAAA,CAAAuC,aAAA,CAACrC,IAAI;IACHS,KAAK,EAAE,CAAC0C,MAAM,CAACC,SAAS,EAAE3C,KAAK,CAAE;IACjC4C,aAAa,EAAC,MAAM;IACpBC,yBAAyB,EAAC;EAAqB,GAE9CnB,SAAS,IAAIC,QAAQ,EACtBtC,KAAA,CAAAuC,aAAA,CAAC/B,IAAI;IACHiD,OAAO,EAAE9B,YAAa;IACtBR,MAAM,EAAG,GAAEA,MAAO,OAAO;IACzBuC,qBAAqB,EAAE7B,0BAA2B;IAClDlB,KAAK,EAAE,CACL0C,MAAM,CAACxC,KAAK,EACZ,CAACK,KAAK,CAACuB,IAAI,IAAIY,MAAM,CAACM,IAAI,EAC1BX,aAAa,EACbhC,UAAU;EACV,GAEDH,KACG,CAAC,EACN,CAACwB,SAAS,IAAIC,QACX,CACS,CAAC;AAEtB,CAAC;AAED7B,YAAY,CAACmD,WAAW,GAAG,eAAe;AAE1C,eAAenD,YAAY;AAG3B,SAASA,YAAY;AAErB,IAAM4C,MAAM,GAAGpD,UAAU,CAAC4D,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE;EACrB,CAAC;EACDrD,KAAK,EAAE;IACLsD,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDT,IAAI,EAAE;IACJU,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}