{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport invariant from 'fbjs/lib/invariant';\nexport var CellRenderMask = function () {\n  function CellRenderMask(numCells) {\n    _classCallCheck(this, CellRenderMask);\n    invariant(numCells >= 0, 'CellRenderMask must contain a non-negative number os cells');\n    this._numCells = numCells;\n    if (numCells === 0) {\n      this._regions = [];\n    } else {\n      this._regions = [{\n        first: 0,\n        last: numCells - 1,\n        isSpacer: true\n      }];\n    }\n  }\n  return _createClass(CellRenderMask, [{\n    key: \"enumerateRegions\",\n    value: function enumerateRegions() {\n      return this._regions;\n    }\n  }, {\n    key: \"addCells\",\n    value: function addCells(cells) {\n      var _this$_regions;\n      invariant(cells.first >= 0 && cells.first < this._numCells && cells.last >= -1 && cells.last < this._numCells && cells.last >= cells.first - 1, 'CellRenderMask.addCells called with invalid cell range');\n      if (cells.last < cells.first) {\n        return;\n      }\n      var _this$_findRegion = this._findRegion(cells.first),\n        firstIntersect = _this$_findRegion[0],\n        firstIntersectIdx = _this$_findRegion[1];\n      var _this$_findRegion2 = this._findRegion(cells.last),\n        lastIntersect = _this$_findRegion2[0],\n        lastIntersectIdx = _this$_findRegion2[1];\n      if (firstIntersectIdx === lastIntersectIdx && !firstIntersect.isSpacer) {\n        return;\n      }\n      var newLeadRegion = [];\n      var newTailRegion = [];\n      var newMainRegion = _objectSpread(_objectSpread({}, cells), {}, {\n        isSpacer: false\n      });\n      if (firstIntersect.first < newMainRegion.first) {\n        if (firstIntersect.isSpacer) {\n          newLeadRegion.push({\n            first: firstIntersect.first,\n            last: newMainRegion.first - 1,\n            isSpacer: true\n          });\n        } else {\n          newMainRegion.first = firstIntersect.first;\n        }\n      }\n      if (lastIntersect.last > newMainRegion.last) {\n        if (lastIntersect.isSpacer) {\n          newTailRegion.push({\n            first: newMainRegion.last + 1,\n            last: lastIntersect.last,\n            isSpacer: true\n          });\n        } else {\n          newMainRegion.last = lastIntersect.last;\n        }\n      }\n      var replacementRegions = [].concat(newLeadRegion, [newMainRegion], newTailRegion);\n      var numRegionsToDelete = lastIntersectIdx - firstIntersectIdx + 1;\n      (_this$_regions = this._regions).splice.apply(_this$_regions, [firstIntersectIdx, numRegionsToDelete].concat(_toConsumableArray(replacementRegions)));\n    }\n  }, {\n    key: \"numCells\",\n    value: function numCells() {\n      return this._numCells;\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(other) {\n      return this._numCells === other._numCells && this._regions.length === other._regions.length && this._regions.every(function (region, i) {\n        return region.first === other._regions[i].first && region.last === other._regions[i].last && region.isSpacer === other._regions[i].isSpacer;\n      });\n    }\n  }, {\n    key: \"_findRegion\",\n    value: function _findRegion(cellIdx) {\n      var firstIdx = 0;\n      var lastIdx = this._regions.length - 1;\n      while (firstIdx <= lastIdx) {\n        var middleIdx = Math.floor((firstIdx + lastIdx) / 2);\n        var middleRegion = this._regions[middleIdx];\n        if (cellIdx >= middleRegion.first && cellIdx <= middleRegion.last) {\n          return [middleRegion, middleIdx];\n        } else if (cellIdx < middleRegion.first) {\n          lastIdx = middleIdx - 1;\n        } else if (cellIdx > middleRegion.last) {\n          firstIdx = middleIdx + 1;\n        }\n      }\n      invariant(false, \"A region was not found containing cellIdx \" + cellIdx);\n    }\n  }]);\n}();", "map": {"version": 3, "names": ["_objectSpread", "invariant", "CellRenderMask", "num<PERSON>ells", "_classCallCheck", "_numCells", "_regions", "first", "last", "isSpacer", "_createClass", "key", "value", "enumerateRegions", "add<PERSON>ells", "cells", "_this$_regions", "_this$_findRegion", "_findRegion", "firstIntersect", "firstIntersectIdx", "_this$_findRegion2", "lastIntersect", "lastIntersectIdx", "newLeadRegion", "newTailRegion", "newMainRegion", "push", "replacementRegions", "concat", "numRegionsToDelete", "splice", "apply", "_toConsumableArray", "equals", "other", "length", "every", "region", "i", "cellIdx", "firstIdx", "lastIdx", "middleIdx", "Math", "floor", "middleRegion"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/CellRenderMask.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport invariant from 'fbjs/lib/invariant';\nexport class CellRenderMask {\n  constructor(numCells) {\n    invariant(numCells >= 0, 'CellRenderMask must contain a non-negative number os cells');\n    this._numCells = numCells;\n    if (numCells === 0) {\n      this._regions = [];\n    } else {\n      this._regions = [{\n        first: 0,\n        last: numCells - 1,\n        isSpacer: true\n      }];\n    }\n  }\n  enumerateRegions() {\n    return this._regions;\n  }\n  addCells(cells) {\n    invariant(cells.first >= 0 && cells.first < this._numCells && cells.last >= -1 && cells.last < this._numCells && cells.last >= cells.first - 1, 'CellRenderMask.addCells called with invalid cell range');\n\n    // VirtualizedList uses inclusive ranges, where zero-count states are\n    // possible. E.g. [0, -1] for no cells, starting at 0.\n    if (cells.last < cells.first) {\n      return;\n    }\n    var _this$_findRegion = this._findRegion(cells.first),\n      firstIntersect = _this$_findRegion[0],\n      firstIntersectIdx = _this$_findRegion[1];\n    var _this$_findRegion2 = this._findRegion(cells.last),\n      lastIntersect = _this$_findRegion2[0],\n      lastIntersectIdx = _this$_findRegion2[1];\n\n    // Fast-path if the cells to add are already all present in the mask. We\n    // will otherwise need to do some mutation.\n    if (firstIntersectIdx === lastIntersectIdx && !firstIntersect.isSpacer) {\n      return;\n    }\n\n    // We need to replace the existing covered regions with 1-3 new regions\n    // depending whether we need to split spacers out of overlapping regions.\n    var newLeadRegion = [];\n    var newTailRegion = [];\n    var newMainRegion = _objectSpread(_objectSpread({}, cells), {}, {\n      isSpacer: false\n    });\n    if (firstIntersect.first < newMainRegion.first) {\n      if (firstIntersect.isSpacer) {\n        newLeadRegion.push({\n          first: firstIntersect.first,\n          last: newMainRegion.first - 1,\n          isSpacer: true\n        });\n      } else {\n        newMainRegion.first = firstIntersect.first;\n      }\n    }\n    if (lastIntersect.last > newMainRegion.last) {\n      if (lastIntersect.isSpacer) {\n        newTailRegion.push({\n          first: newMainRegion.last + 1,\n          last: lastIntersect.last,\n          isSpacer: true\n        });\n      } else {\n        newMainRegion.last = lastIntersect.last;\n      }\n    }\n    var replacementRegions = [...newLeadRegion, newMainRegion, ...newTailRegion];\n    var numRegionsToDelete = lastIntersectIdx - firstIntersectIdx + 1;\n    this._regions.splice(firstIntersectIdx, numRegionsToDelete, ...replacementRegions);\n  }\n  numCells() {\n    return this._numCells;\n  }\n  equals(other) {\n    return this._numCells === other._numCells && this._regions.length === other._regions.length && this._regions.every((region, i) => region.first === other._regions[i].first && region.last === other._regions[i].last && region.isSpacer === other._regions[i].isSpacer);\n  }\n  _findRegion(cellIdx) {\n    var firstIdx = 0;\n    var lastIdx = this._regions.length - 1;\n    while (firstIdx <= lastIdx) {\n      var middleIdx = Math.floor((firstIdx + lastIdx) / 2);\n      var middleRegion = this._regions[middleIdx];\n      if (cellIdx >= middleRegion.first && cellIdx <= middleRegion.last) {\n        return [middleRegion, middleIdx];\n      } else if (cellIdx < middleRegion.first) {\n        lastIdx = middleIdx - 1;\n      } else if (cellIdx > middleRegion.last) {\n        firstIdx = middleIdx + 1;\n      }\n    }\n    invariant(false, \"A region was not found containing cellIdx \" + cellIdx);\n  }\n}"], "mappings": ";;;AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAWhE,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,WAAaC,cAAc;EACzB,SAAAA,eAAYC,QAAQ,EAAE;IAAAC,eAAA,OAAAF,cAAA;IACpBD,SAAS,CAACE,QAAQ,IAAI,CAAC,EAAE,4DAA4D,CAAC;IACtF,IAAI,CAACE,SAAS,GAAGF,QAAQ;IACzB,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClB,IAAI,CAACG,QAAQ,GAAG,EAAE;IACpB,CAAC,MAAM;MACL,IAAI,CAACA,QAAQ,GAAG,CAAC;QACfC,KAAK,EAAE,CAAC;QACRC,IAAI,EAAEL,QAAQ,GAAG,CAAC;QAClBM,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF;EAAC,OAAAC,YAAA,CAAAR,cAAA;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACP,QAAQ;IACtB;EAAC;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAE,QAAQA,CAACC,KAAK,EAAE;MAAA,IAAAC,cAAA;MACdf,SAAS,CAACc,KAAK,CAACR,KAAK,IAAI,CAAC,IAAIQ,KAAK,CAACR,KAAK,GAAG,IAAI,CAACF,SAAS,IAAIU,KAAK,CAACP,IAAI,IAAI,CAAC,CAAC,IAAIO,KAAK,CAACP,IAAI,GAAG,IAAI,CAACH,SAAS,IAAIU,KAAK,CAACP,IAAI,IAAIO,KAAK,CAACR,KAAK,GAAG,CAAC,EAAE,wDAAwD,CAAC;MAIzM,IAAIQ,KAAK,CAACP,IAAI,GAAGO,KAAK,CAACR,KAAK,EAAE;QAC5B;MACF;MACA,IAAIU,iBAAiB,GAAG,IAAI,CAACC,WAAW,CAACH,KAAK,CAACR,KAAK,CAAC;QACnDY,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;QACrCG,iBAAiB,GAAGH,iBAAiB,CAAC,CAAC,CAAC;MAC1C,IAAII,kBAAkB,GAAG,IAAI,CAACH,WAAW,CAACH,KAAK,CAACP,IAAI,CAAC;QACnDc,aAAa,GAAGD,kBAAkB,CAAC,CAAC,CAAC;QACrCE,gBAAgB,GAAGF,kBAAkB,CAAC,CAAC,CAAC;MAI1C,IAAID,iBAAiB,KAAKG,gBAAgB,IAAI,CAACJ,cAAc,CAACV,QAAQ,EAAE;QACtE;MACF;MAIA,IAAIe,aAAa,GAAG,EAAE;MACtB,IAAIC,aAAa,GAAG,EAAE;MACtB,IAAIC,aAAa,GAAG1B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DN,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAIU,cAAc,CAACZ,KAAK,GAAGmB,aAAa,CAACnB,KAAK,EAAE;QAC9C,IAAIY,cAAc,CAACV,QAAQ,EAAE;UAC3Be,aAAa,CAACG,IAAI,CAAC;YACjBpB,KAAK,EAAEY,cAAc,CAACZ,KAAK;YAC3BC,IAAI,EAAEkB,aAAa,CAACnB,KAAK,GAAG,CAAC;YAC7BE,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLiB,aAAa,CAACnB,KAAK,GAAGY,cAAc,CAACZ,KAAK;QAC5C;MACF;MACA,IAAIe,aAAa,CAACd,IAAI,GAAGkB,aAAa,CAAClB,IAAI,EAAE;QAC3C,IAAIc,aAAa,CAACb,QAAQ,EAAE;UAC1BgB,aAAa,CAACE,IAAI,CAAC;YACjBpB,KAAK,EAAEmB,aAAa,CAAClB,IAAI,GAAG,CAAC;YAC7BA,IAAI,EAAEc,aAAa,CAACd,IAAI;YACxBC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLiB,aAAa,CAAClB,IAAI,GAAGc,aAAa,CAACd,IAAI;QACzC;MACF;MACA,IAAIoB,kBAAkB,MAAAC,MAAA,CAAOL,aAAa,GAAEE,aAAa,GAAKD,aAAa,CAAC;MAC5E,IAAIK,kBAAkB,GAAGP,gBAAgB,GAAGH,iBAAiB,GAAG,CAAC;MACjE,CAAAJ,cAAA,OAAI,CAACV,QAAQ,EAACyB,MAAM,CAAAC,KAAA,CAAAhB,cAAA,GAACI,iBAAiB,EAAEU,kBAAkB,EAAAD,MAAA,CAAAI,kBAAA,CAAKL,kBAAkB,GAAC;IACpF;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EACD,SAAAT,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAACE,SAAS;IACvB;EAAC;IAAAM,GAAA;IAAAC,KAAA,EACD,SAAAsB,MAAMA,CAACC,KAAK,EAAE;MACZ,OAAO,IAAI,CAAC9B,SAAS,KAAK8B,KAAK,CAAC9B,SAAS,IAAI,IAAI,CAACC,QAAQ,CAAC8B,MAAM,KAAKD,KAAK,CAAC7B,QAAQ,CAAC8B,MAAM,IAAI,IAAI,CAAC9B,QAAQ,CAAC+B,KAAK,CAAC,UAACC,MAAM,EAAEC,CAAC;QAAA,OAAKD,MAAM,CAAC/B,KAAK,KAAK4B,KAAK,CAAC7B,QAAQ,CAACiC,CAAC,CAAC,CAAChC,KAAK,IAAI+B,MAAM,CAAC9B,IAAI,KAAK2B,KAAK,CAAC7B,QAAQ,CAACiC,CAAC,CAAC,CAAC/B,IAAI,IAAI8B,MAAM,CAAC7B,QAAQ,KAAK0B,KAAK,CAAC7B,QAAQ,CAACiC,CAAC,CAAC,CAAC9B,QAAQ;MAAA,EAAC;IACzQ;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAAM,WAAWA,CAACsB,OAAO,EAAE;MACnB,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAIC,OAAO,GAAG,IAAI,CAACpC,QAAQ,CAAC8B,MAAM,GAAG,CAAC;MACtC,OAAOK,QAAQ,IAAIC,OAAO,EAAE;QAC1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,QAAQ,GAAGC,OAAO,IAAI,CAAC,CAAC;QACpD,IAAII,YAAY,GAAG,IAAI,CAACxC,QAAQ,CAACqC,SAAS,CAAC;QAC3C,IAAIH,OAAO,IAAIM,YAAY,CAACvC,KAAK,IAAIiC,OAAO,IAAIM,YAAY,CAACtC,IAAI,EAAE;UACjE,OAAO,CAACsC,YAAY,EAAEH,SAAS,CAAC;QAClC,CAAC,MAAM,IAAIH,OAAO,GAAGM,YAAY,CAACvC,KAAK,EAAE;UACvCmC,OAAO,GAAGC,SAAS,GAAG,CAAC;QACzB,CAAC,MAAM,IAAIH,OAAO,GAAGM,YAAY,CAACtC,IAAI,EAAE;UACtCiC,QAAQ,GAAGE,SAAS,GAAG,CAAC;QAC1B;MACF;MACA1C,SAAS,CAAC,KAAK,EAAE,4CAA4C,GAAGuC,OAAO,CAAC;IAC1E;EAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}