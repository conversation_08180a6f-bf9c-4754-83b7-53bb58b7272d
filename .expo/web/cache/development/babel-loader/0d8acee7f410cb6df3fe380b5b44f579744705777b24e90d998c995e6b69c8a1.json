{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport Appearance from \"../Appearance\";\nexport default function useColorScheme() {\n  var _React$useState = React.useState(Appearance.getColorScheme()),\n    colorScheme = _React$useState[0],\n    setColorScheme = _React$useState[1];\n  React.useEffect(function () {\n    function listener(appearance) {\n      setColorScheme(appearance.colorScheme);\n    }\n    var _Appearance$addChange = Appearance.addChangeListener(listener),\n      remove = _Appearance$addChange.remove;\n    return remove;\n  });\n  return colorScheme;\n}", "map": {"version": 3, "names": ["React", "Appearance", "useColorScheme", "_React$useState", "useState", "getColorScheme", "colorScheme", "setColorScheme", "useEffect", "listener", "appearance", "_Appearance$addChange", "addChangeListener", "remove"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/useColorScheme/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport * as React from 'react';\nimport Appearance from '../Appearance';\nexport default function useColorScheme() {\n  var _React$useState = React.useState(Appearance.getColorScheme()),\n    colorScheme = _React$useState[0],\n    setColorScheme = _React$useState[1];\n  React.useEffect(() => {\n    function listener(appearance) {\n      setColorScheme(appearance.colorScheme);\n    }\n    var _Appearance$addChange = Appearance.addChangeListener(listener),\n      remove = _Appearance$addChange.remove;\n    return remove;\n  });\n  return colorScheme;\n}"], "mappings": "AAUA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,IAAIC,eAAe,GAAGH,KAAK,CAACI,QAAQ,CAACH,UAAU,CAACI,cAAc,CAAC,CAAC,CAAC;IAC/DC,WAAW,GAAGH,eAAe,CAAC,CAAC,CAAC;IAChCI,cAAc,GAAGJ,eAAe,CAAC,CAAC,CAAC;EACrCH,KAAK,CAACQ,SAAS,CAAC,YAAM;IACpB,SAASC,QAAQA,CAACC,UAAU,EAAE;MAC5BH,cAAc,CAACG,UAAU,CAACJ,WAAW,CAAC;IACxC;IACA,IAAIK,qBAAqB,GAAGV,UAAU,CAACW,iBAAiB,CAACH,QAAQ,CAAC;MAChEI,MAAM,GAAGF,qBAAqB,CAACE,MAAM;IACvC,OAAOA,MAAM;EACf,CAAC,CAAC;EACF,OAAOP,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}