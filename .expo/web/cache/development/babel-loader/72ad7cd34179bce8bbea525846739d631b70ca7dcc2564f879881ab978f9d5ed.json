{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"style\"];\nimport * as React from 'react';\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nimport canUseDOM from \"../../modules/canUseDom\";\nvar cssFunction = function () {\n  if (canUseDOM && window.CSS && window.CSS.supports && window.CSS.supports('top: constant(safe-area-inset-top)')) {\n    return 'constant';\n  }\n  return 'env';\n}();\nvar SafeAreaView = React.forwardRef(function (props, ref) {\n  var style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  return React.createElement(View, _extends({}, rest, {\n    ref: ref,\n    style: [styles.root, style]\n  }));\n});\nSafeAreaView.displayName = 'SafeAreaView';\nvar styles = StyleSheet.create({\n  root: {\n    paddingTop: cssFunction + \"(safe-area-inset-top)\",\n    paddingRight: cssFunction + \"(safe-area-inset-right)\",\n    paddingBottom: cssFunction + \"(safe-area-inset-bottom)\",\n    paddingLeft: cssFunction + \"(safe-area-inset-left)\"\n  }\n});\nexport default SafeAreaView;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "StyleSheet", "View", "canUseDOM", "cssFunction", "window", "CSS", "supports", "SafeAreaView", "forwardRef", "props", "ref", "style", "rest", "createElement", "styles", "root", "displayName", "create", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/SafeAreaView/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"style\"];\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\nimport canUseDOM from '../../modules/canUseDom';\nvar cssFunction = function () {\n  if (canUseDOM && window.CSS && window.CSS.supports && window.CSS.supports('top: constant(safe-area-inset-top)')) {\n    return 'constant';\n  }\n  return 'env';\n}();\nvar SafeAreaView = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/React.createElement(View, _extends({}, rest, {\n    ref: ref,\n    style: [styles.root, style]\n  }));\n});\nSafeAreaView.displayName = 'SafeAreaView';\nvar styles = StyleSheet.create({\n  root: {\n    paddingTop: cssFunction + \"(safe-area-inset-top)\",\n    paddingRight: cssFunction + \"(safe-area-inset-right)\",\n    paddingBottom: cssFunction + \"(safe-area-inset-bottom)\",\n    paddingLeft: cssFunction + \"(safe-area-inset-left)\"\n  }\n});\nexport default SafeAreaView;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,OAAO,CAAC;AAWzB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,OAAOC,SAAS;AAChB,IAAIC,WAAW,GAAG,YAAY;EAC5B,IAAID,SAAS,IAAIE,MAAM,CAACC,GAAG,IAAID,MAAM,CAACC,GAAG,CAACC,QAAQ,IAAIF,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,oCAAoC,CAAC,EAAE;IAC/G,OAAO,UAAU;EACnB;EACA,OAAO,KAAK;AACd,CAAC,CAAC,CAAC;AACH,IAAIC,YAAY,GAAgBR,KAAK,CAACS,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;EAC/D,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBC,IAAI,GAAGf,6BAA6B,CAACY,KAAK,EAAEX,SAAS,CAAC;EACxD,OAAoBC,KAAK,CAACc,aAAa,CAACZ,IAAI,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEgB,IAAI,EAAE;IAC/DF,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAE,CAACG,MAAM,CAACC,IAAI,EAAEJ,KAAK;EAC5B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFJ,YAAY,CAACS,WAAW,GAAG,cAAc;AACzC,IAAIF,MAAM,GAAGd,UAAU,CAACiB,MAAM,CAAC;EAC7BF,IAAI,EAAE;IACJG,UAAU,EAAEf,WAAW,GAAG,uBAAuB;IACjDgB,YAAY,EAAEhB,WAAW,GAAG,yBAAyB;IACrDiB,aAAa,EAAEjB,WAAW,GAAG,0BAA0B;IACvDkB,WAAW,EAAElB,WAAW,GAAG;EAC7B;AACF,CAAC,CAAC;AACF,eAAeI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}