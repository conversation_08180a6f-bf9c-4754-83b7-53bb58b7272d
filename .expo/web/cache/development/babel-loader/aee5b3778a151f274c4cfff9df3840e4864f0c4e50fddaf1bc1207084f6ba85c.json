{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/elements/src/types.tsx"], "sourcesContent": ["import type {\n  Animated,\n  LayoutChangeEvent,\n  StyleProp,\n  TextStyle,\n  ViewStyle,\n} from 'react-native';\n\nexport type Layout = { width: number; height: number };\n\nexport type HeaderOptions = {\n  /**\n   * String or a function that returns a React Element to be used by the header.\n   * Defaults to screen `title` or route name.\n   *\n   * It receives `allowFontScaling`, `tintColor`, `style` and `children` in the options object as an argument.\n   * The title string is passed in `children`.\n   */\n  headerTitle?: string | ((props: HeaderTitleProps) => React.ReactNode);\n  /**\n   * How to align the the header title.\n   * Defaults to `center` on iOS and `left` on Android.\n   */\n  headerTitleAlign?: 'left' | 'center';\n  /**\n   * Style object for the title component.\n   */\n  headerTitleStyle?: Animated.WithAnimatedValue<StyleProp<TextStyle>>;\n  /**\n   * Style object for the container of the `headerTitle` element.\n   */\n  headerTitleContainerStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Whether header title font should scale to respect Text Size accessibility settings. Defaults to `false`.\n   */\n  headerTitleAllowFontScaling?: boolean;\n  /**\n   * Function which returns a React Element to display on the left side of the header.\n   */\n  headerLeft?: (props: {\n    tintColor?: string;\n    pressColor?: string;\n    pressOpacity?: number;\n    labelVisible?: boolean;\n  }) => React.ReactNode;\n  /**\n   * Whether a label is visible in the left button. Used to add extra padding.\n   */\n  headerLeftLabelVisible?: boolean;\n  /**\n   * Style object for the container of the `headerLeft` element`.\n   */\n  headerLeftContainerStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Function which returns a React Element to display on the right side of the header.\n   */\n  headerRight?: (props: {\n    tintColor?: string;\n    pressColor?: string;\n    pressOpacity?: number;\n  }) => React.ReactNode;\n  /**\n   * Style object for the container of the `headerRight` element.\n   */\n  headerRightContainerStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Color for material ripple (Android >= 5.0 only).\n   */\n  headerPressColor?: string;\n  /**\n   * Color for material ripple (Android >= 5.0 only).\n   */\n  headerPressOpacity?: number;\n  /**\n   * Tint color for the header.\n   */\n  headerTintColor?: string;\n  /**\n   * Function which returns a React Element to render as the background of the header.\n   * This is useful for using backgrounds such as an image, a gradient, blur effect etc.\n   * You can use this with `headerTransparent` to render a blur view, for example, to create a translucent header.\n   */\n  headerBackground?: (props: {\n    style: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  }) => React.ReactNode;\n  /**\n   * Style object for the container of the `headerBackground` element.\n   */\n  headerBackgroundContainerStyle?: Animated.WithAnimatedValue<\n    StyleProp<ViewStyle>\n  >;\n  /**\n   * Defaults to `false`. If `true`, the header will not have a background unless you explicitly provide it with `headerBackground`.\n   * The header will also float over the screen so that it overlaps the content underneath.\n   * This is useful if you want to render a semi-transparent header or a blurred background.\n   */\n  headerTransparent?: boolean;\n  /**\n   * Style object for the header. You can specify a custom background color here, for example.\n   */\n  headerStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Whether to hide the elevation shadow (Android) or the bottom border (iOS) on the header.\n   *\n   * This is a short-hand for the following styles:\n   *\n   * ```js\n   * {\n   *   elevation: 0,\n   *   shadowOpacity: 0,\n   *   borderBottomWidth: 0,\n   * }\n   * ```\n   *\n   * If the above styles are specified in `headerStyle` along with `headerShadowVisible: false`,\n   * then `headerShadowVisible: false` will take precedence.\n   */\n  headerShadowVisible?: boolean;\n  /**\n   * Extra padding to add at the top of header to account for translucent status bar.\n   * By default, it uses the top value from the safe area insets of the device.\n   * Pass 0 or a custom value to disable the default behaviour, and customize the height.\n   */\n  headerStatusBarHeight?: number;\n};\n\nexport type HeaderTitleProps = {\n  /**\n   * The title text of the header.\n   */\n  children: string;\n  /**\n   * Whether title font should scale to respect Text Size accessibility settings.\n   */\n  allowFontScaling?: boolean;\n  /**\n   * Tint color for the header.\n   */\n  tintColor?: string;\n  /**\n   * Callback to trigger when the size of the title element changes.\n   */\n  onLayout?: (e: LayoutChangeEvent) => void;\n  /**\n   * Style object for the title element.\n   */\n  style?: Animated.WithAnimatedValue<StyleProp<TextStyle>>;\n};\n\nexport type HeaderButtonProps = {\n  /**\n   * Tint color for the header button.\n   */\n  tintColor?: string;\n  /**\n   * Color for material ripple (Android >= 5.0 only).\n   */\n  pressColor?: string;\n  /**\n   * Opacity when the button is pressed, used when ripple is not supported.\n   */\n  pressOpacity?: number;\n  /**\n   * Whether it's possible to navigate back in stack.\n   */\n  canGoBack?: boolean;\n};\n\nexport type HeaderBackButtonProps = HeaderButtonProps & {\n  /**\n   * Whether the button is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Callback to call when the button is pressed.\n   */\n  onPress?: () => void;\n  /**\n   * Function which returns a React Element to display custom image in header's back button.\n   */\n  backImage?: (props: { tintColor: string }) => React.ReactNode;\n  /**\n   * Label text for the button. Usually the title of the previous screen.\n   * By default, this is only shown on iOS.\n   */\n  label?: string;\n  /**\n   * Label text to show when there isn't enough space for the full label.\n   */\n  truncatedLabel?: string;\n  /**\n   * Whether the label text is visible.\n   * Defaults to `true` on iOS and `false` on Android.\n   */\n  labelVisible?: boolean;\n  /**\n   * Style object for the label.\n   */\n  labelStyle?: Animated.WithAnimatedValue<StyleProp<TextStyle>>;\n  /**\n   * Whether label font should scale to respect Text Size accessibility settings.\n   */\n  allowFontScaling?: boolean;\n  /**\n   * Callback to trigger when the size of the label changes.\n   */\n  onLabelLayout?: (e: LayoutChangeEvent) => void;\n  /**\n   * Layout of the screen.\n   */\n  screenLayout?: Layout;\n  /**\n   * Layout of the title element in the header.\n   */\n  titleLayout?: Layout;\n  /**\n   * Accessibility label for the button for screen readers.\n   */\n  accessibilityLabel?: string;\n  /**\n   * ID to locate this button in tests.\n   */\n  testID?: string;\n  /**\n   * Style object for the button.\n   */\n  style?: StyleProp<ViewStyle>;\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}