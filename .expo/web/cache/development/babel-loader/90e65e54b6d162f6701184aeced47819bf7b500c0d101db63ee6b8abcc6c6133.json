{"ast": null, "code": "var TYPE_VALUE = 'value';\nvar TYPE_ERROR = 'error';\nexport default function createIconSourceCache() {\n  var cache = new Map();\n  var setValue = function setValue(key, value) {\n    return cache.set(key, {\n      type: TYPE_VALUE,\n      data: value\n    });\n  };\n  var setError = function setError(key, error) {\n    return cache.set(key, {\n      type: TYPE_ERROR,\n      data: error\n    });\n  };\n  var has = function has(key) {\n    return cache.has(key);\n  };\n  var get = function get(key) {\n    if (!cache.has(key)) {\n      return undefined;\n    }\n    var _cache$get = cache.get(key),\n      type = _cache$get.type,\n      data = _cache$get.data;\n    if (type === TYPE_ERROR) {\n      throw data;\n    }\n    return data;\n  };\n  return {\n    setValue: setValue,\n    setError: setError,\n    has: has,\n    get: get\n  };\n}", "map": {"version": 3, "names": ["TYPE_VALUE", "TYPE_ERROR", "createIconSourceCache", "cache", "Map", "setValue", "key", "value", "set", "type", "data", "setError", "error", "has", "get", "undefined", "_cache$get"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/create-icon-source-cache.js"], "sourcesContent": ["const TYPE_VALUE = 'value';\nconst TYPE_ERROR = 'error';\n\nexport default function createIconSourceCache() {\n  const cache = new Map();\n\n  const setValue = (key, value) =>\n    cache.set(key, { type: TYPE_VALUE, data: value });\n\n  const setError = (key, error) =>\n    cache.set(key, { type: TYPE_ERROR, data: error });\n\n  const has = key => cache.has(key);\n\n  const get = key => {\n    if (!cache.has(key)) {\n      return undefined;\n    }\n    const { type, data } = cache.get(key);\n    if (type === TYPE_ERROR) {\n      throw data;\n    }\n    return data;\n  };\n\n  return { setValue, setError, has, get };\n}\n"], "mappings": "AAAA,IAAMA,UAAU,GAAG,OAAO;AAC1B,IAAMC,UAAU,GAAG,OAAO;AAE1B,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAC9C,IAAMC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EAEvB,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,GAAG,EAAEC,KAAK;IAAA,OAC1BJ,KAAK,CAACK,GAAG,CAACF,GAAG,EAAE;MAAEG,IAAI,EAAET,UAAU;MAAEU,IAAI,EAAEH;IAAM,CAAC,CAAC;EAAA;EAEnD,IAAMI,QAAQ,GAAG,SAAXA,QAAQA,CAAIL,GAAG,EAAEM,KAAK;IAAA,OAC1BT,KAAK,CAACK,GAAG,CAACF,GAAG,EAAE;MAAEG,IAAI,EAAER,UAAU;MAAES,IAAI,EAAEE;IAAM,CAAC,CAAC;EAAA;EAEnD,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAGP,GAAG;IAAA,OAAIH,KAAK,CAACU,GAAG,CAACP,GAAG,CAAC;EAAA;EAEjC,IAAMQ,GAAG,GAAG,SAANA,GAAGA,CAAGR,GAAG,EAAI;IACjB,IAAI,CAACH,KAAK,CAACU,GAAG,CAACP,GAAG,CAAC,EAAE;MACnB,OAAOS,SAAS;IAClB;IACA,IAAAC,UAAA,GAAuBb,KAAK,CAACW,GAAG,CAACR,GAAG,CAAC;MAA7BG,IAAI,GAAAO,UAAA,CAAJP,IAAI;MAAEC,IAAI,GAAAM,UAAA,CAAJN,IAAI;IAClB,IAAID,IAAI,KAAKR,UAAU,EAAE;MACvB,MAAMS,IAAI;IACZ;IACA,OAAOA,IAAI;EACb,CAAC;EAED,OAAO;IAAEL,QAAQ,EAARA,QAAQ;IAAEM,QAAQ,EAARA,QAAQ;IAAEE,GAAG,EAAHA,GAAG;IAAEC,GAAG,EAAHA;EAAI,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}