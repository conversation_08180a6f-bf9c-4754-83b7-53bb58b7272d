{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Provider as PaperProvider } from 'react-native-paper';\nimport HomeScreen from \"./app/src/screens/HomeScreen\";\nimport FoodListScreen from \"./app/src/screens/FoodListScreen\";\nimport ProfileScreen from \"./app/src/screens/ProfileScreen\";\nimport { lightTheme } from \"./app/src/theme/theme\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Tab = createBottomTabNavigator();\nvar WebNotice = function WebNotice() {\n  return _jsxs(View, {\n    style: styles.webNotice,\n    children: [_jsx(MaterialIcons, {\n      name: \"info\",\n      size: 20,\n      color: \"#2196F3\"\n    }), _jsx(Text, {\n      style: styles.webNoticeText,\n      children: \"Web version has limited functionality. For full features, use the mobile app.\"\n    })]\n  });\n};\nvar WebHomeScreen = function WebHomeScreen(_ref) {\n  var navigation = _ref.navigation;\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(WebNotice, {}), _jsxs(View, {\n      style: styles.content,\n      children: [_jsx(Text, {\n        style: styles.title,\n        children: \"Zn\\xFCniZ\\xE4hler\"\n      }), _jsx(Text, {\n        style: styles.subtitle,\n        children: \"Smart Nutrition Tracking\"\n      }), _jsxs(View, {\n        style: styles.featureList,\n        children: [_jsxs(View, {\n          style: styles.feature,\n          children: [_jsx(MaterialIcons, {\n            name: \"document-scanner\",\n            size: 24,\n            color: \"#4CAF50\"\n          }), _jsx(Text, {\n            style: styles.featureText,\n            children: \"OCR Label Scanning (Mobile Only)\"\n          })]\n        }), _jsxs(View, {\n          style: styles.feature,\n          children: [_jsx(MaterialIcons, {\n            name: \"qr-code-scanner\",\n            size: 24,\n            color: \"#2196F3\"\n          }), _jsx(Text, {\n            style: styles.featureText,\n            children: \"Barcode Scanning (Mobile Only)\"\n          })]\n        }), _jsxs(View, {\n          style: styles.feature,\n          children: [_jsx(MaterialIcons, {\n            name: \"edit\",\n            size: 24,\n            color: \"#FF9800\"\n          }), _jsx(Text, {\n            style: styles.featureText,\n            children: \"Manual Food Entry\"\n          })]\n        }), _jsxs(View, {\n          style: styles.feature,\n          children: [_jsx(MaterialIcons, {\n            name: \"analytics\",\n            size: 24,\n            color: \"#9C27B0\"\n          }), _jsx(Text, {\n            style: styles.featureText,\n            children: \"Nutrition Analytics\"\n          })]\n        })]\n      }), _jsxs(View, {\n        style: styles.downloadSection,\n        children: [_jsx(Text, {\n          style: styles.downloadTitle,\n          children: \"Get the Full Experience\"\n        }), _jsx(Text, {\n          style: styles.downloadText,\n          children: \"Download the mobile app for OCR scanning, barcode lookup, and offline functionality.\"\n        }), _jsxs(View, {\n          style: styles.downloadButtons,\n          children: [_jsxs(View, {\n            style: styles.downloadButton,\n            children: [_jsx(MaterialIcons, {\n              name: \"android\",\n              size: 24,\n              color: \"#3DDC84\"\n            }), _jsx(Text, {\n              style: styles.downloadButtonText,\n              children: \"Android (Coming Soon)\"\n            })]\n          }), _jsxs(View, {\n            style: styles.downloadButton,\n            children: [_jsx(MaterialIcons, {\n              name: \"apple\",\n              size: 24,\n              color: \"#000\"\n            }), _jsx(Text, {\n              style: styles.downloadButtonText,\n              children: \"iOS (Coming Soon)\"\n            })]\n          })]\n        })]\n      })]\n    })]\n  });\n};\nvar WebFoodScreen = function WebFoodScreen() {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(WebNotice, {}), _jsxs(View, {\n      style: styles.content,\n      children: [_jsx(Text, {\n        style: styles.title,\n        children: \"Food Database\"\n      }), _jsx(Text, {\n        style: styles.subtitle,\n        children: \"Food database features are optimized for mobile devices with local storage.\"\n      }), _jsxs(View, {\n        style: styles.comingSoon,\n        children: [_jsx(MaterialIcons, {\n          name: \"restaurant\",\n          size: 48,\n          color: \"#ccc\"\n        }), _jsx(Text, {\n          style: styles.comingSoonText,\n          children: \"Web food database coming soon!\"\n        })]\n      })]\n    })]\n  });\n};\nvar WebProfileScreen = function WebProfileScreen() {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(WebNotice, {}), _jsxs(View, {\n      style: styles.content,\n      children: [_jsx(Text, {\n        style: styles.title,\n        children: \"Profile & Settings\"\n      }), _jsx(Text, {\n        style: styles.subtitle,\n        children: \"User profiles and settings are stored locally on mobile devices.\"\n      }), _jsxs(View, {\n        style: styles.comingSoon,\n        children: [_jsx(MaterialIcons, {\n          name: \"person\",\n          size: 48,\n          color: \"#ccc\"\n        }), _jsx(Text, {\n          style: styles.comingSoonText,\n          children: \"Web profiles coming soon!\"\n        })]\n      })]\n    })]\n  });\n};\nexport default function App() {\n  return _jsx(PaperProvider, {\n    theme: lightTheme,\n    children: _jsxs(NavigationContainer, {\n      children: [_jsx(StatusBar, {\n        style: \"auto\"\n      }), _jsxs(Tab.Navigator, {\n        screenOptions: function screenOptions(_ref2) {\n          var route = _ref2.route;\n          return {\n            tabBarIcon: function tabBarIcon(_ref3) {\n              var focused = _ref3.focused,\n                color = _ref3.color,\n                size = _ref3.size;\n              var iconName;\n              if (route.name === 'Home') {\n                iconName = 'home';\n              } else if (route.name === 'Food') {\n                iconName = 'restaurant';\n              } else if (route.name === 'Profile') {\n                iconName = 'person';\n              }\n              return _jsx(MaterialIcons, {\n                name: iconName,\n                size: size,\n                color: color\n              });\n            },\n            tabBarActiveTintColor: '#4CAF50',\n            tabBarInactiveTintColor: 'gray',\n            headerStyle: {\n              backgroundColor: '#4CAF50'\n            },\n            headerTintColor: '#fff',\n            headerTitleStyle: {\n              fontWeight: 'bold'\n            }\n          };\n        },\n        children: [_jsx(Tab.Screen, {\n          name: \"Home\",\n          component: WebHomeScreen,\n          options: {\n            title: 'ZnüniZähler'\n          }\n        }), _jsx(Tab.Screen, {\n          name: \"Food\",\n          component: WebFoodScreen,\n          options: {\n            title: 'Food Database'\n          }\n        }), _jsx(Tab.Screen, {\n          name: \"Profile\",\n          component: WebProfileScreen,\n          options: {\n            title: 'Profile'\n          }\n        })]\n      })]\n    })\n  });\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  webNotice: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    backgroundColor: '#e3f2fd',\n    padding: 12,\n    borderBottomWidth: 1,\n    borderBottomColor: '#bbdefb'\n  },\n  webNoticeText: {\n    marginLeft: 8,\n    color: '#1976d2',\n    fontSize: 14\n  },\n  content: {\n    flex: 1,\n    padding: 20,\n    alignItems: 'center'\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    color: '#4CAF50',\n    marginBottom: 8,\n    textAlign: 'center'\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    marginBottom: 30,\n    textAlign: 'center',\n    maxWidth: 400\n  },\n  featureList: {\n    width: '100%',\n    maxWidth: 400,\n    marginBottom: 40\n  },\n  feature: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 15,\n    backgroundColor: '#fff',\n    marginBottom: 10,\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 1\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2\n  },\n  featureText: {\n    marginLeft: 15,\n    fontSize: 16,\n    color: '#333'\n  },\n  downloadSection: {\n    alignItems: 'center',\n    maxWidth: 400\n  },\n  downloadTitle: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 10\n  },\n  downloadText: {\n    fontSize: 14,\n    color: '#666',\n    textAlign: 'center',\n    marginBottom: 20\n  },\n  downloadButtons: {\n    flexDirection: 'row',\n    gap: 15\n  },\n  downloadButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 12,\n    backgroundColor: '#fff',\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 1\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2\n  },\n  downloadButtonText: {\n    marginLeft: 8,\n    fontSize: 14,\n    color: '#333'\n  },\n  comingSoon: {\n    alignItems: 'center',\n    marginTop: 50\n  },\n  comingSoonText: {\n    fontSize: 18,\n    color: '#999',\n    marginTop: 15\n  }\n});", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "Platform", "StatusBar", "NavigationContainer", "createBottomTabNavigator", "MaterialIcons", "Provider", "PaperProvider", "HomeScreen", "FoodListScreen", "ProfileScreen", "lightTheme", "jsx", "_jsx", "jsxs", "_jsxs", "Tab", "WebNotice", "style", "styles", "webNotice", "children", "name", "size", "color", "webNoticeText", "WebHomeScreen", "_ref", "navigation", "container", "content", "title", "subtitle", "featureList", "feature", "featureText", "downloadSection", "downloadTitle", "downloadText", "downloadButtons", "downloadButton", "downloadButtonText", "WebFoodScreen", "comingSoon", "comingSoonText", "WebProfileScreen", "App", "theme", "Navigator", "screenOptions", "_ref2", "route", "tabBarIcon", "_ref3", "focused", "iconName", "tabBarActiveTintColor", "tabBarInactiveTintColor", "headerStyle", "backgroundColor", "headerTintColor", "headerTitleStyle", "fontWeight", "Screen", "component", "options", "create", "flex", "flexDirection", "alignItems", "padding", "borderBottomWidth", "borderBottomColor", "marginLeft", "fontSize", "marginBottom", "textAlign", "max<PERSON><PERSON><PERSON>", "width", "borderRadius", "shadowColor", "shadowOffset", "height", "shadowOpacity", "shadowRadius", "elevation", "gap", "marginTop"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/App.web.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet, Platform } from 'react-native';\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Provider as PaperProvider } from 'react-native-paper';\n\n// Import screens\nimport HomeScreen from './app/src/screens/HomeScreen';\nimport FoodListScreen from './app/src/screens/FoodListScreen';\nimport ProfileScreen from './app/src/screens/ProfileScreen';\n\n// Import theme\nimport { lightTheme } from './app/src/theme/theme';\n\nconst Tab = createBottomTabNavigator();\n\n// Web-specific notice component\nconst WebNotice = () => (\n  <View style={styles.webNotice}>\n    <MaterialIcons name=\"info\" size={20} color=\"#2196F3\" />\n    <Text style={styles.webNoticeText}>\n      Web version has limited functionality. For full features, use the mobile app.\n    </Text>\n  </View>\n);\n\n// Web-compatible Home Screen\nconst WebHomeScreen = ({ navigation }) => (\n  <View style={styles.container}>\n    <WebNotice />\n    <View style={styles.content}>\n      <Text style={styles.title}>ZnüniZähler</Text>\n      <Text style={styles.subtitle}>Smart Nutrition Tracking</Text>\n      \n      <View style={styles.featureList}>\n        <View style={styles.feature}>\n          <MaterialIcons name=\"document-scanner\" size={24} color=\"#4CAF50\" />\n          <Text style={styles.featureText}>OCR Label Scanning (Mobile Only)</Text>\n        </View>\n        \n        <View style={styles.feature}>\n          <MaterialIcons name=\"qr-code-scanner\" size={24} color=\"#2196F3\" />\n          <Text style={styles.featureText}>Barcode Scanning (Mobile Only)</Text>\n        </View>\n        \n        <View style={styles.feature}>\n          <MaterialIcons name=\"edit\" size={24} color=\"#FF9800\" />\n          <Text style={styles.featureText}>Manual Food Entry</Text>\n        </View>\n        \n        <View style={styles.feature}>\n          <MaterialIcons name=\"analytics\" size={24} color=\"#9C27B0\" />\n          <Text style={styles.featureText}>Nutrition Analytics</Text>\n        </View>\n      </View>\n      \n      <View style={styles.downloadSection}>\n        <Text style={styles.downloadTitle}>Get the Full Experience</Text>\n        <Text style={styles.downloadText}>\n          Download the mobile app for OCR scanning, barcode lookup, and offline functionality.\n        </Text>\n        \n        <View style={styles.downloadButtons}>\n          <View style={styles.downloadButton}>\n            <MaterialIcons name=\"android\" size={24} color=\"#3DDC84\" />\n            <Text style={styles.downloadButtonText}>Android (Coming Soon)</Text>\n          </View>\n          \n          <View style={styles.downloadButton}>\n            <MaterialIcons name=\"apple\" size={24} color=\"#000\" />\n            <Text style={styles.downloadButtonText}>iOS (Coming Soon)</Text>\n          </View>\n        </View>\n      </View>\n    </View>\n  </View>\n);\n\n// Web-compatible Food Screen\nconst WebFoodScreen = () => (\n  <View style={styles.container}>\n    <WebNotice />\n    <View style={styles.content}>\n      <Text style={styles.title}>Food Database</Text>\n      <Text style={styles.subtitle}>\n        Food database features are optimized for mobile devices with local storage.\n      </Text>\n      \n      <View style={styles.comingSoon}>\n        <MaterialIcons name=\"restaurant\" size={48} color=\"#ccc\" />\n        <Text style={styles.comingSoonText}>Web food database coming soon!</Text>\n      </View>\n    </View>\n  </View>\n);\n\n// Web-compatible Profile Screen\nconst WebProfileScreen = () => (\n  <View style={styles.container}>\n    <WebNotice />\n    <View style={styles.content}>\n      <Text style={styles.title}>Profile & Settings</Text>\n      <Text style={styles.subtitle}>\n        User profiles and settings are stored locally on mobile devices.\n      </Text>\n      \n      <View style={styles.comingSoon}>\n        <MaterialIcons name=\"person\" size={48} color=\"#ccc\" />\n        <Text style={styles.comingSoonText}>Web profiles coming soon!</Text>\n      </View>\n    </View>\n  </View>\n);\n\nexport default function App() {\n  return (\n    <PaperProvider theme={lightTheme}>\n      <NavigationContainer>\n        <StatusBar style=\"auto\" />\n        <Tab.Navigator\n          screenOptions={({ route }) => ({\n            tabBarIcon: ({ focused, color, size }) => {\n              let iconName;\n\n              if (route.name === 'Home') {\n                iconName = 'home';\n              } else if (route.name === 'Food') {\n                iconName = 'restaurant';\n              } else if (route.name === 'Profile') {\n                iconName = 'person';\n              }\n\n              return <MaterialIcons name={iconName} size={size} color={color} />;\n            },\n            tabBarActiveTintColor: '#4CAF50',\n            tabBarInactiveTintColor: 'gray',\n            headerStyle: {\n              backgroundColor: '#4CAF50',\n            },\n            headerTintColor: '#fff',\n            headerTitleStyle: {\n              fontWeight: 'bold',\n            },\n          })}\n        >\n          <Tab.Screen \n            name=\"Home\" \n            component={WebHomeScreen}\n            options={{ title: 'ZnüniZähler' }}\n          />\n          <Tab.Screen \n            name=\"Food\" \n            component={WebFoodScreen}\n            options={{ title: 'Food Database' }}\n          />\n          <Tab.Screen \n            name=\"Profile\" \n            component={WebProfileScreen}\n            options={{ title: 'Profile' }}\n          />\n        </Tab.Navigator>\n      </NavigationContainer>\n    </PaperProvider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  webNotice: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    backgroundColor: '#e3f2fd',\n    padding: 12,\n    borderBottomWidth: 1,\n    borderBottomColor: '#bbdefb',\n  },\n  webNoticeText: {\n    marginLeft: 8,\n    color: '#1976d2',\n    fontSize: 14,\n  },\n  content: {\n    flex: 1,\n    padding: 20,\n    alignItems: 'center',\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    color: '#4CAF50',\n    marginBottom: 8,\n    textAlign: 'center',\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    marginBottom: 30,\n    textAlign: 'center',\n    maxWidth: 400,\n  },\n  featureList: {\n    width: '100%',\n    maxWidth: 400,\n    marginBottom: 40,\n  },\n  feature: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 15,\n    backgroundColor: '#fff',\n    marginBottom: 10,\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 1 },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2,\n  },\n  featureText: {\n    marginLeft: 15,\n    fontSize: 16,\n    color: '#333',\n  },\n  downloadSection: {\n    alignItems: 'center',\n    maxWidth: 400,\n  },\n  downloadTitle: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 10,\n  },\n  downloadText: {\n    fontSize: 14,\n    color: '#666',\n    textAlign: 'center',\n    marginBottom: 20,\n  },\n  downloadButtons: {\n    flexDirection: 'row',\n    gap: 15,\n  },\n  downloadButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 12,\n    backgroundColor: '#fff',\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 1 },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2,\n  },\n  downloadButtonText: {\n    marginLeft: 8,\n    fontSize: 14,\n    color: '#333',\n  },\n  comingSoon: {\n    alignItems: 'center',\n    marginTop: 50,\n  },\n  comingSoonText: {\n    fontSize: 18,\n    color: '#999',\n    marginTop: 15,\n  },\n});\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAE1B,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQ,IAAIC,aAAa,QAAQ,oBAAoB;AAG9D,OAAOC,UAAU;AACjB,OAAOC,cAAc;AACrB,OAAOC,aAAa;AAGpB,SAASC,UAAU;AAAgC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEnD,IAAMC,GAAG,GAAGZ,wBAAwB,CAAC,CAAC;AAGtC,IAAMa,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OACbF,KAAA,CAACjB,IAAI;IAACoB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BR,IAAA,CAACR,aAAa;MAACiB,IAAI,EAAC,MAAM;MAACC,IAAI,EAAE,EAAG;MAACC,KAAK,EAAC;IAAS,CAAE,CAAC,EACvDX,IAAA,CAACd,IAAI;MAACmB,KAAK,EAAEC,MAAM,CAACM,aAAc;MAAAJ,QAAA,EAAC;IAEnC,CAAM,CAAC;EAAA,CACH,CAAC;AAAA,CACR;AAGD,IAAMK,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA;EAAA,IAAMC,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAAA,OACjCb,KAAA,CAACjB,IAAI;IAACoB,KAAK,EAAEC,MAAM,CAACU,SAAU;IAAAR,QAAA,GAC5BR,IAAA,CAACI,SAAS,IAAE,CAAC,EACbF,KAAA,CAACjB,IAAI;MAACoB,KAAK,EAAEC,MAAM,CAACW,OAAQ;MAAAT,QAAA,GAC1BR,IAAA,CAACd,IAAI;QAACmB,KAAK,EAAEC,MAAM,CAACY,KAAM;QAAAV,QAAA,EAAC;MAAW,CAAM,CAAC,EAC7CR,IAAA,CAACd,IAAI;QAACmB,KAAK,EAAEC,MAAM,CAACa,QAAS;QAAAX,QAAA,EAAC;MAAwB,CAAM,CAAC,EAE7DN,KAAA,CAACjB,IAAI;QAACoB,KAAK,EAAEC,MAAM,CAACc,WAAY;QAAAZ,QAAA,GAC9BN,KAAA,CAACjB,IAAI;UAACoB,KAAK,EAAEC,MAAM,CAACe,OAAQ;UAAAb,QAAA,GAC1BR,IAAA,CAACR,aAAa;YAACiB,IAAI,EAAC,kBAAkB;YAACC,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS,CAAE,CAAC,EACnEX,IAAA,CAACd,IAAI;YAACmB,KAAK,EAAEC,MAAM,CAACgB,WAAY;YAAAd,QAAA,EAAC;UAAgC,CAAM,CAAC;QAAA,CACpE,CAAC,EAEPN,KAAA,CAACjB,IAAI;UAACoB,KAAK,EAAEC,MAAM,CAACe,OAAQ;UAAAb,QAAA,GAC1BR,IAAA,CAACR,aAAa;YAACiB,IAAI,EAAC,iBAAiB;YAACC,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS,CAAE,CAAC,EAClEX,IAAA,CAACd,IAAI;YAACmB,KAAK,EAAEC,MAAM,CAACgB,WAAY;YAAAd,QAAA,EAAC;UAA8B,CAAM,CAAC;QAAA,CAClE,CAAC,EAEPN,KAAA,CAACjB,IAAI;UAACoB,KAAK,EAAEC,MAAM,CAACe,OAAQ;UAAAb,QAAA,GAC1BR,IAAA,CAACR,aAAa;YAACiB,IAAI,EAAC,MAAM;YAACC,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS,CAAE,CAAC,EACvDX,IAAA,CAACd,IAAI;YAACmB,KAAK,EAAEC,MAAM,CAACgB,WAAY;YAAAd,QAAA,EAAC;UAAiB,CAAM,CAAC;QAAA,CACrD,CAAC,EAEPN,KAAA,CAACjB,IAAI;UAACoB,KAAK,EAAEC,MAAM,CAACe,OAAQ;UAAAb,QAAA,GAC1BR,IAAA,CAACR,aAAa;YAACiB,IAAI,EAAC,WAAW;YAACC,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS,CAAE,CAAC,EAC5DX,IAAA,CAACd,IAAI;YAACmB,KAAK,EAAEC,MAAM,CAACgB,WAAY;YAAAd,QAAA,EAAC;UAAmB,CAAM,CAAC;QAAA,CACvD,CAAC;MAAA,CACH,CAAC,EAEPN,KAAA,CAACjB,IAAI;QAACoB,KAAK,EAAEC,MAAM,CAACiB,eAAgB;QAAAf,QAAA,GAClCR,IAAA,CAACd,IAAI;UAACmB,KAAK,EAAEC,MAAM,CAACkB,aAAc;UAAAhB,QAAA,EAAC;QAAuB,CAAM,CAAC,EACjER,IAAA,CAACd,IAAI;UAACmB,KAAK,EAAEC,MAAM,CAACmB,YAAa;UAAAjB,QAAA,EAAC;QAElC,CAAM,CAAC,EAEPN,KAAA,CAACjB,IAAI;UAACoB,KAAK,EAAEC,MAAM,CAACoB,eAAgB;UAAAlB,QAAA,GAClCN,KAAA,CAACjB,IAAI;YAACoB,KAAK,EAAEC,MAAM,CAACqB,cAAe;YAAAnB,QAAA,GACjCR,IAAA,CAACR,aAAa;cAACiB,IAAI,EAAC,SAAS;cAACC,IAAI,EAAE,EAAG;cAACC,KAAK,EAAC;YAAS,CAAE,CAAC,EAC1DX,IAAA,CAACd,IAAI;cAACmB,KAAK,EAAEC,MAAM,CAACsB,kBAAmB;cAAApB,QAAA,EAAC;YAAqB,CAAM,CAAC;UAAA,CAChE,CAAC,EAEPN,KAAA,CAACjB,IAAI;YAACoB,KAAK,EAAEC,MAAM,CAACqB,cAAe;YAAAnB,QAAA,GACjCR,IAAA,CAACR,aAAa;cAACiB,IAAI,EAAC,OAAO;cAACC,IAAI,EAAE,EAAG;cAACC,KAAK,EAAC;YAAM,CAAE,CAAC,EACrDX,IAAA,CAACd,IAAI;cAACmB,KAAK,EAAEC,MAAM,CAACsB,kBAAmB;cAAApB,QAAA,EAAC;YAAiB,CAAM,CAAC;UAAA,CAC5D,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC;IAAA,CACH,CAAC;EAAA,CACH,CAAC;AAAA,CACR;AAGD,IAAMqB,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OACjB3B,KAAA,CAACjB,IAAI;IAACoB,KAAK,EAAEC,MAAM,CAACU,SAAU;IAAAR,QAAA,GAC5BR,IAAA,CAACI,SAAS,IAAE,CAAC,EACbF,KAAA,CAACjB,IAAI;MAACoB,KAAK,EAAEC,MAAM,CAACW,OAAQ;MAAAT,QAAA,GAC1BR,IAAA,CAACd,IAAI;QAACmB,KAAK,EAAEC,MAAM,CAACY,KAAM;QAAAV,QAAA,EAAC;MAAa,CAAM,CAAC,EAC/CR,IAAA,CAACd,IAAI;QAACmB,KAAK,EAAEC,MAAM,CAACa,QAAS;QAAAX,QAAA,EAAC;MAE9B,CAAM,CAAC,EAEPN,KAAA,CAACjB,IAAI;QAACoB,KAAK,EAAEC,MAAM,CAACwB,UAAW;QAAAtB,QAAA,GAC7BR,IAAA,CAACR,aAAa;UAACiB,IAAI,EAAC,YAAY;UAACC,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAM,CAAE,CAAC,EAC1DX,IAAA,CAACd,IAAI;UAACmB,KAAK,EAAEC,MAAM,CAACyB,cAAe;UAAAvB,QAAA,EAAC;QAA8B,CAAM,CAAC;MAAA,CACrE,CAAC;IAAA,CACH,CAAC;EAAA,CACH,CAAC;AAAA,CACR;AAGD,IAAMwB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OACpB9B,KAAA,CAACjB,IAAI;IAACoB,KAAK,EAAEC,MAAM,CAACU,SAAU;IAAAR,QAAA,GAC5BR,IAAA,CAACI,SAAS,IAAE,CAAC,EACbF,KAAA,CAACjB,IAAI;MAACoB,KAAK,EAAEC,MAAM,CAACW,OAAQ;MAAAT,QAAA,GAC1BR,IAAA,CAACd,IAAI;QAACmB,KAAK,EAAEC,MAAM,CAACY,KAAM;QAAAV,QAAA,EAAC;MAAkB,CAAM,CAAC,EACpDR,IAAA,CAACd,IAAI;QAACmB,KAAK,EAAEC,MAAM,CAACa,QAAS;QAAAX,QAAA,EAAC;MAE9B,CAAM,CAAC,EAEPN,KAAA,CAACjB,IAAI;QAACoB,KAAK,EAAEC,MAAM,CAACwB,UAAW;QAAAtB,QAAA,GAC7BR,IAAA,CAACR,aAAa;UAACiB,IAAI,EAAC,QAAQ;UAACC,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAM,CAAE,CAAC,EACtDX,IAAA,CAACd,IAAI;UAACmB,KAAK,EAAEC,MAAM,CAACyB,cAAe;UAAAvB,QAAA,EAAC;QAAyB,CAAM,CAAC;MAAA,CAChE,CAAC;IAAA,CACH,CAAC;EAAA,CACH,CAAC;AAAA,CACR;AAED,eAAe,SAASyB,GAAGA,CAAA,EAAG;EAC5B,OACEjC,IAAA,CAACN,aAAa;IAACwC,KAAK,EAAEpC,UAAW;IAAAU,QAAA,EAC/BN,KAAA,CAACZ,mBAAmB;MAAAkB,QAAA,GAClBR,IAAA,CAACX,SAAS;QAACgB,KAAK,EAAC;MAAM,CAAE,CAAC,EAC1BH,KAAA,CAACC,GAAG,CAACgC,SAAS;QACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAC,KAAA;UAAA,IAAKC,KAAK,GAAAD,KAAA,CAALC,KAAK;UAAA,OAAQ;YAC7BC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA,EAAgC;cAAA,IAA3BC,OAAO,GAAAD,KAAA,CAAPC,OAAO;gBAAE9B,KAAK,GAAA6B,KAAA,CAAL7B,KAAK;gBAAED,IAAI,GAAA8B,KAAA,CAAJ9B,IAAI;cACjC,IAAIgC,QAAQ;cAEZ,IAAIJ,KAAK,CAAC7B,IAAI,KAAK,MAAM,EAAE;gBACzBiC,QAAQ,GAAG,MAAM;cACnB,CAAC,MAAM,IAAIJ,KAAK,CAAC7B,IAAI,KAAK,MAAM,EAAE;gBAChCiC,QAAQ,GAAG,YAAY;cACzB,CAAC,MAAM,IAAIJ,KAAK,CAAC7B,IAAI,KAAK,SAAS,EAAE;gBACnCiC,QAAQ,GAAG,QAAQ;cACrB;cAEA,OAAO1C,IAAA,CAACR,aAAa;gBAACiB,IAAI,EAAEiC,QAAS;gBAAChC,IAAI,EAAEA,IAAK;gBAACC,KAAK,EAAEA;cAAM,CAAE,CAAC;YACpE,CAAC;YACDgC,qBAAqB,EAAE,SAAS;YAChCC,uBAAuB,EAAE,MAAM;YAC/BC,WAAW,EAAE;cACXC,eAAe,EAAE;YACnB,CAAC;YACDC,eAAe,EAAE,MAAM;YACvBC,gBAAgB,EAAE;cAChBC,UAAU,EAAE;YACd;UACF,CAAC;QAAA,CAAE;QAAAzC,QAAA,GAEHR,IAAA,CAACG,GAAG,CAAC+C,MAAM;UACTzC,IAAI,EAAC,MAAM;UACX0C,SAAS,EAAEtC,aAAc;UACzBuC,OAAO,EAAE;YAAElC,KAAK,EAAE;UAAc;QAAE,CACnC,CAAC,EACFlB,IAAA,CAACG,GAAG,CAAC+C,MAAM;UACTzC,IAAI,EAAC,MAAM;UACX0C,SAAS,EAAEtB,aAAc;UACzBuB,OAAO,EAAE;YAAElC,KAAK,EAAE;UAAgB;QAAE,CACrC,CAAC,EACFlB,IAAA,CAACG,GAAG,CAAC+C,MAAM;UACTzC,IAAI,EAAC,SAAS;UACd0C,SAAS,EAAEnB,gBAAiB;UAC5BoB,OAAO,EAAE;YAAElC,KAAK,EAAE;UAAU;QAAE,CAC/B,CAAC;MAAA,CACW,CAAC;IAAA,CACG;EAAC,CACT,CAAC;AAEpB;AAEA,IAAMZ,MAAM,GAAGnB,UAAU,CAACkE,MAAM,CAAC;EAC/BrC,SAAS,EAAE;IACTsC,IAAI,EAAE,CAAC;IACPR,eAAe,EAAE;EACnB,CAAC;EACDvC,SAAS,EAAE;IACTgD,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBV,eAAe,EAAE,SAAS;IAC1BW,OAAO,EAAE,EAAE;IACXC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACD/C,aAAa,EAAE;IACbgD,UAAU,EAAE,CAAC;IACbjD,KAAK,EAAE,SAAS;IAChBkD,QAAQ,EAAE;EACZ,CAAC;EACD5C,OAAO,EAAE;IACPqC,IAAI,EAAE,CAAC;IACPG,OAAO,EAAE,EAAE;IACXD,UAAU,EAAE;EACd,CAAC;EACDtC,KAAK,EAAE;IACL2C,QAAQ,EAAE,EAAE;IACZZ,UAAU,EAAE,MAAM;IAClBtC,KAAK,EAAE,SAAS;IAChBmD,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACD5C,QAAQ,EAAE;IACR0C,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE,MAAM;IACbmD,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;EACZ,CAAC;EACD5C,WAAW,EAAE;IACX6C,KAAK,EAAE,MAAM;IACbD,QAAQ,EAAE,GAAG;IACbF,YAAY,EAAE;EAChB,CAAC;EACDzC,OAAO,EAAE;IACPkC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXX,eAAe,EAAE,MAAM;IACvBgB,YAAY,EAAE,EAAE;IAChBI,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAEH,KAAK,EAAE,CAAC;MAAEI,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDlD,WAAW,EAAE;IACXsC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE;EACT,CAAC;EACDY,eAAe,EAAE;IACfiC,UAAU,EAAE,QAAQ;IACpBQ,QAAQ,EAAE;EACZ,CAAC;EACDxC,aAAa,EAAE;IACbqC,QAAQ,EAAE,EAAE;IACZZ,UAAU,EAAE,MAAM;IAClBtC,KAAK,EAAE,MAAM;IACbmD,YAAY,EAAE;EAChB,CAAC;EACDrC,YAAY,EAAE;IACZoC,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE,MAAM;IACboD,SAAS,EAAE,QAAQ;IACnBD,YAAY,EAAE;EAChB,CAAC;EACDpC,eAAe,EAAE;IACf6B,aAAa,EAAE,KAAK;IACpBkB,GAAG,EAAE;EACP,CAAC;EACD9C,cAAc,EAAE;IACd4B,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXX,eAAe,EAAE,MAAM;IACvBoB,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAEH,KAAK,EAAE,CAAC;MAAEI,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACD5C,kBAAkB,EAAE;IAClBgC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE;EACT,CAAC;EACDmB,UAAU,EAAE;IACV0B,UAAU,EAAE,QAAQ;IACpBkB,SAAS,EAAE;EACb,CAAC;EACD3C,cAAc,EAAE;IACd8B,QAAQ,EAAE,EAAE;IACZlD,KAAK,EAAE,MAAM;IACb+D,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}