{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport * as React from 'react';\nvar PortalConsumer = function (_React$Component) {\n  function PortalConsumer() {\n    _classCallCheck(this, PortalConsumer);\n    return _callSuper(this, PortalConsumer, arguments);\n  }\n  _inherits(PortalConsumer, _React$Component);\n  return _createClass(PortalConsumer, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.checkManager();\n      this.key = this.props.manager.mount(this.props.children);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.checkManager();\n      this.props.manager.update(this.key, this.props.children);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.checkManager();\n      this.props.manager.unmount(this.key);\n    }\n  }, {\n    key: \"checkManager\",\n    value: function checkManager() {\n      if (!this.props.manager) {\n        throw new Error('Looks like you forgot to wrap your root component with `Provider` component from `react-native-paper`.\\n\\n' + \"Please read our getting-started guide and make sure you've followed all the required steps.\\n\\n\" + 'https://callstack.github.io/react-native-paper/docs/guides/getting-started');\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return null;\n    }\n  }]);\n}(React.Component);\nexport { PortalConsumer as default };", "map": {"version": 3, "names": ["React", "PortalConsumer", "_React$Component", "_classCallCheck", "_callSuper", "arguments", "_inherits", "_createClass", "key", "value", "componentDidMount", "checkManager", "props", "manager", "mount", "children", "componentDidUpdate", "update", "componentWillUnmount", "unmount", "Error", "render", "Component", "default"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Portal/PortalConsumer.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { PortalMethods } from './PortalHost';\n\ntype Props = {\n  manager: PortalMethods;\n  children: React.ReactNode;\n};\n\nexport default class PortalConsumer extends React.Component<Props> {\n  componentDidMount() {\n    this.checkManager();\n\n    this.key = this.props.manager.mount(this.props.children);\n  }\n\n  componentDidUpdate() {\n    this.checkManager();\n\n    this.props.manager.update(this.key, this.props.children);\n  }\n\n  componentWillUnmount() {\n    this.checkManager();\n\n    this.props.manager.unmount(this.key);\n  }\n\n  private key: any;\n\n  private checkManager() {\n    if (!this.props.manager) {\n      throw new Error(\n        'Looks like you forgot to wrap your root component with `Provider` component from `react-native-paper`.\\n\\n' +\n          \"Please read our getting-started guide and make sure you've followed all the required steps.\\n\\n\" +\n          'https://callstack.github.io/react-native-paper/docs/guides/getting-started'\n      );\n    }\n  }\n\n  render() {\n    return null;\n  }\n}\n"], "mappings": ";;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,IASTC,cAAc,aAAAC,gBAAA;EAAA,SAAAD,eAAA;IAAAE,eAAA,OAAAF,cAAA;IAAA,OAAAG,UAAA,OAAAH,cAAA,EAAAI,SAAA;EAAA;EAAAC,SAAA,CAAAL,cAAA,EAAAC,gBAAA;EAAA,OAAAK,YAAA,CAAAN,cAAA;IAAAO,GAAA;IAAAC,KAAA,EACjC,SAAAC,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACC,YAAY,CAAC,CAAC;MAEnB,IAAI,CAACH,GAAG,GAAG,IAAI,CAACI,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC,IAAI,CAACF,KAAK,CAACG,QAAQ,CAAC;IAC1D;EAAA;IAAAP,GAAA;IAAAC,KAAA,EAEA,SAAAO,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACL,YAAY,CAAC,CAAC;MAEnB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACI,MAAM,CAAC,IAAI,CAACT,GAAG,EAAE,IAAI,CAACI,KAAK,CAACG,QAAQ,CAAC;IAC1D;EAAA;IAAAP,GAAA;IAAAC,KAAA,EAEA,SAAAS,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACP,YAAY,CAAC,CAAC;MAEnB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACM,OAAO,CAAC,IAAI,CAACX,GAAG,CAAC;IACtC;EAAA;IAAAA,GAAA;IAAAC,KAAA,EAIQ,SAAAE,YAAYA,CAAA,EAAG;MACrB,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,OAAO,EAAE;QACvB,MAAM,IAAIO,KAAK,CACb,4GAA4G,GAC1G,iGAAiG,GACjG,4EACJ,CAAC;MACH;IACF;EAAA;IAAAZ,GAAA;IAAAC,KAAA,EAEA,SAAAY,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI;IACb;EAAA;AAAA,EAjC0CrB,KAAK,CAACsB,SAAS;AAAA,SAAtCrB,cAAc,IAAAsB,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}