{"ast": null, "code": "'use strict';\n\nimport canUseDOM from \"../canUseDom\";\nvar emptyFunction = function emptyFunction() {};\nfunction supportsPassiveEvents() {\n  var supported = false;\n  if (canUseDOM) {\n    try {\n      var options = {};\n      Object.defineProperty(options, 'passive', {\n        get: function get() {\n          supported = true;\n          return false;\n        }\n      });\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    } catch (e) {}\n  }\n  return supported;\n}\nvar canUsePassiveEvents = supportsPassiveEvents();\nfunction getOptions(options) {\n  if (options == null) {\n    return false;\n  }\n  return canUsePassiveEvents ? options : <PERSON><PERSON>an(options.capture);\n}\nfunction isPropagationStopped() {\n  return this.cancelBubble;\n}\nfunction isDefaultPrevented() {\n  return this.defaultPrevented;\n}\nfunction normalizeEvent(event) {\n  event.nativeEvent = event;\n  event.persist = emptyFunction;\n  event.isDefaultPrevented = isDefaultPrevented;\n  event.isPropagationStopped = isPropagationStopped;\n  return event;\n}\nexport function addEventListener(target, type, listener, options) {\n  var opts = getOptions(options);\n  var compatListener = function compatListener(e) {\n    return listener(normalizeEvent(e));\n  };\n  target.addEventListener(type, compatListener, opts);\n  return function removeEventListener() {\n    if (target != null) {\n      target.removeEventListener(type, compatListener, opts);\n    }\n  };\n}", "map": {"version": 3, "names": ["canUseDOM", "emptyFunction", "supportsPassiveEvents", "supported", "options", "Object", "defineProperty", "get", "window", "addEventListener", "removeEventListener", "e", "canUsePassiveEvents", "getOptions", "Boolean", "capture", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "normalizeEvent", "event", "nativeEvent", "persist", "target", "type", "listener", "opts", "compatListener"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-web/dist/modules/addEventListener/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use strict';\n\nimport canUseDOM from '../canUseDom';\nvar emptyFunction = () => {};\nfunction supportsPassiveEvents() {\n  var supported = false;\n  // Check if browser supports event with passive listeners\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n  if (canUseDOM) {\n    try {\n      var options = {};\n      Object.defineProperty(options, 'passive', {\n        get() {\n          supported = true;\n          return false;\n        }\n      });\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    } catch (e) {}\n  }\n  return supported;\n}\nvar canUsePassiveEvents = supportsPassiveEvents();\nfunction getOptions(options) {\n  if (options == null) {\n    return false;\n  }\n  return canUsePassiveEvents ? options : Boolean(options.capture);\n}\n\n/**\n * Shim generic API compatibility with ReactDOM's synthetic events, without needing the\n * large amount of code ReactDOM uses to do this. Ideally we wouldn't use a synthetic\n * event wrapper at all.\n */\nfunction isPropagationStopped() {\n  return this.cancelBubble;\n}\nfunction isDefaultPrevented() {\n  return this.defaultPrevented;\n}\nfunction normalizeEvent(event) {\n  event.nativeEvent = event;\n  event.persist = emptyFunction;\n  event.isDefaultPrevented = isDefaultPrevented;\n  event.isPropagationStopped = isPropagationStopped;\n  return event;\n}\n\n/**\n *\n */\nexport function addEventListener(target, type, listener, options) {\n  var opts = getOptions(options);\n  var compatListener = e => listener(normalizeEvent(e));\n  target.addEventListener(type, compatListener, opts);\n  return function removeEventListener() {\n    if (target != null) {\n      target.removeEventListener(type, compatListener, opts);\n    }\n  };\n}"], "mappings": "AASA,YAAY;;AAEZ,OAAOA,SAAS;AAChB,IAAIC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS,CAAC,CAAC;AAC5B,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,IAAIC,SAAS,GAAG,KAAK;EAGrB,IAAIH,SAAS,EAAE;IACb,IAAI;MACF,IAAII,OAAO,GAAG,CAAC,CAAC;MAChBC,MAAM,CAACC,cAAc,CAACF,OAAO,EAAE,SAAS,EAAE;QACxCG,GAAG,WAAHA,GAAGA,CAAA,EAAG;UACJJ,SAAS,GAAG,IAAI;UAChB,OAAO,KAAK;QACd;MACF,CAAC,CAAC;MACFK,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAEL,OAAO,CAAC;MAC9CI,MAAM,CAACE,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAEN,OAAO,CAAC;IACnD,CAAC,CAAC,OAAOO,CAAC,EAAE,CAAC;EACf;EACA,OAAOR,SAAS;AAClB;AACA,IAAIS,mBAAmB,GAAGV,qBAAqB,CAAC,CAAC;AACjD,SAASW,UAAUA,CAACT,OAAO,EAAE;EAC3B,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,KAAK;EACd;EACA,OAAOQ,mBAAmB,GAAGR,OAAO,GAAGU,OAAO,CAACV,OAAO,CAACW,OAAO,CAAC;AACjE;AAOA,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,OAAO,IAAI,CAACC,YAAY;AAC1B;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,OAAO,IAAI,CAACC,gBAAgB;AAC9B;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7BA,KAAK,CAACC,WAAW,GAAGD,KAAK;EACzBA,KAAK,CAACE,OAAO,GAAGtB,aAAa;EAC7BoB,KAAK,CAACH,kBAAkB,GAAGA,kBAAkB;EAC7CG,KAAK,CAACL,oBAAoB,GAAGA,oBAAoB;EACjD,OAAOK,KAAK;AACd;AAKA,OAAO,SAASZ,gBAAgBA,CAACe,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEtB,OAAO,EAAE;EAChE,IAAIuB,IAAI,GAAGd,UAAU,CAACT,OAAO,CAAC;EAC9B,IAAIwB,cAAc,GAAG,SAAjBA,cAAcA,CAAGjB,CAAC;IAAA,OAAIe,QAAQ,CAACN,cAAc,CAACT,CAAC,CAAC,CAAC;EAAA;EACrDa,MAAM,CAACf,gBAAgB,CAACgB,IAAI,EAAEG,cAAc,EAAED,IAAI,CAAC;EACnD,OAAO,SAASjB,mBAAmBA,CAAA,EAAG;IACpC,IAAIc,MAAM,IAAI,IAAI,EAAE;MAClBA,MAAM,CAACd,mBAAmB,CAACe,IAAI,EAAEG,cAAc,EAAED,IAAI,CAAC;IACxD;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}