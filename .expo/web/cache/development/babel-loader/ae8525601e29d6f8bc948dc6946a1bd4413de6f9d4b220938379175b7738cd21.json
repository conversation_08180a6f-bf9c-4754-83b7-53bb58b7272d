{"ast": null, "code": "var _global$contexts;\nimport * as React from 'react';\nvar contexts = '__react_navigation__elements_contexts';\nglobal[contexts] = (_global$contexts = global[contexts]) != null ? _global$contexts : new Map();\nexport default function getNamedContext(name, initialValue) {\n  var context = global[contexts].get(name);\n  if (context) {\n    return context;\n  }\n  context = React.createContext(initialValue);\n  context.displayName = name;\n  global[contexts].set(name, context);\n  return context;\n}", "map": {"version": 3, "names": ["React", "contexts", "global", "_global$contexts", "Map", "getNamedContext", "name", "initialValue", "context", "get", "createContext", "displayName", "set"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/elements/src/getNamedContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nconst contexts = '__react_navigation__elements_contexts';\n\ndeclare global {\n  var __react_navigation__elements_contexts: Map<string, React.Context<any>>;\n}\n\n// We use a global variable to keep our contexts so that we can reuse same contexts across packages\nglobal[contexts] = global[contexts] ?? new Map<string, React.Context<any>>();\n\nexport default function getNamedContext<T>(\n  name: string,\n  initialValue: T\n): React.Context<T> {\n  let context = global[contexts].get(name);\n\n  if (context) {\n    return context;\n  }\n\n  context = React.createContext<T>(initialValue);\n  context.displayName = name;\n\n  global[contexts].set(name, context);\n\n  return context;\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAMC,QAAQ,GAAG,uCAAuC;AAOxDC,MAAM,CAACD,QAAQ,CAAC,IAAAE,gBAAA,GAAGD,MAAM,CAACD,QAAQ,CAAC,YAAAE,gBAAA,GAAI,IAAIC,GAAG,EAA8B;AAE5E,eAAe,SAASC,eAAeA,CACrCC,IAAY,EACZC,YAAe,EACG;EAClB,IAAIC,OAAO,GAAGN,MAAM,CAACD,QAAQ,CAAC,CAACQ,GAAG,CAACH,IAAI,CAAC;EAExC,IAAIE,OAAO,EAAE;IACX,OAAOA,OAAO;EAChB;EAEAA,OAAO,GAAGR,KAAK,CAACU,aAAa,CAAIH,YAAY,CAAC;EAC9CC,OAAO,CAACG,WAAW,GAAGL,IAAI;EAE1BJ,MAAM,CAACD,QAAQ,CAAC,CAACW,GAAG,CAACN,IAAI,EAAEE,OAAO,CAAC;EAEnC,OAAOA,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}