{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport { initializeDatabase } from \"../services/databaseService\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar DatabaseContext = createContext({\n  isInitialized: false,\n  isLoading: true,\n  error: null\n});\nexport var useDatabase = function useDatabase() {\n  return useContext(DatabaseContext);\n};\nexport var DatabaseProvider = function DatabaseProvider(_ref) {\n  var children = _ref.children;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isInitialized = _useState2[0],\n    setIsInitialized = _useState2[1];\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    isLoading = _useState4[0],\n    setIsLoading = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    error = _useState6[0],\n    setError = _useState6[1];\n  useEffect(function () {\n    var initialize = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        try {\n          setIsLoading(true);\n          yield initializeDatabase();\n          setIsInitialized(true);\n          setError(null);\n        } catch (err) {\n          console.error('Error initializing database:', err);\n          setError(err.message || 'Failed to initialize database');\n          setIsInitialized(false);\n        } finally {\n          setIsLoading(false);\n        }\n      });\n      return function initialize() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    initialize();\n  }, []);\n  var contextValue = {\n    isInitialized: isInitialized,\n    isLoading: isLoading,\n    error: error\n  };\n  return _jsx(DatabaseContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n};", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "initializeDatabase", "jsx", "_jsx", "DatabaseContext", "isInitialized", "isLoading", "error", "useDatabase", "DatabaseProvider", "_ref", "children", "_useState", "_useState2", "_slicedToArray", "setIsInitialized", "_useState3", "_useState4", "setIsLoading", "_useState5", "_useState6", "setError", "initialize", "_ref2", "_asyncToGenerator", "err", "console", "message", "apply", "arguments", "contextValue", "Provider", "value"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/database/DatabaseProvider.js"], "sourcesContent": ["/**\n * Database Provider for ZnüniZähler\n * Provides database context and initialization for the app\n */\n\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport { initializeDatabase } from '../services/databaseService';\n\n// Create database context\nconst DatabaseContext = createContext({\n  isInitialized: false,\n  isLoading: true,\n  error: null\n});\n\n// Custom hook to use database\nexport const useDatabase = () => useContext(DatabaseContext);\n\n/**\n * Database Provider Component\n * @param {Object} props - Component props\n * @returns {JSX.Element} - Database provider component\n */\nexport const DatabaseProvider = ({ children }) => {\n  // State for database initialization\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  \n  // Initialize database\n  useEffect(() => {\n    const initialize = async () => {\n      try {\n        setIsLoading(true);\n        await initializeDatabase();\n        setIsInitialized(true);\n        setError(null);\n      } catch (err) {\n        console.error('Error initializing database:', err);\n        setError(err.message || 'Failed to initialize database');\n        setIsInitialized(false);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    initialize();\n  }, []);\n  \n  // Context value\n  const contextValue = {\n    isInitialized,\n    isLoading,\n    error\n  };\n  \n  return (\n    <DatabaseContext.Provider value={contextValue}>\n      {children}\n    </DatabaseContext.Provider>\n  );\n};\n"], "mappings": ";;AAKA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,kBAAkB;AAAsC,SAAAC,GAAA,IAAAC,IAAA;AAGjE,IAAMC,eAAe,GAAGP,aAAa,CAAC;EACpCQ,aAAa,EAAE,KAAK;EACpBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC,CAAC;AAGF,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAAST,UAAU,CAACK,eAAe,CAAC;AAAA;AAO5D,OAAO,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAEzC,IAAAC,SAAA,GAA0Cd,QAAQ,CAAC,KAAK,CAAC;IAAAe,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAlDP,aAAa,GAAAQ,UAAA;IAAEE,gBAAgB,GAAAF,UAAA;EACtC,IAAAG,UAAA,GAAkClB,QAAQ,CAAC,IAAI,CAAC;IAAAmB,UAAA,GAAAH,cAAA,CAAAE,UAAA;IAAzCV,SAAS,GAAAW,UAAA;IAAEC,YAAY,GAAAD,UAAA;EAC9B,IAAAE,UAAA,GAA0BrB,QAAQ,CAAC,IAAI,CAAC;IAAAsB,UAAA,GAAAN,cAAA,CAAAK,UAAA;IAAjCZ,KAAK,GAAAa,UAAA;IAAEC,QAAQ,GAAAD,UAAA;EAGtBpB,SAAS,CAAC,YAAM;IACd,IAAMsB,UAAU;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAC7B,IAAI;UACFN,YAAY,CAAC,IAAI,CAAC;UAClB,MAAMjB,kBAAkB,CAAC,CAAC;UAC1Bc,gBAAgB,CAAC,IAAI,CAAC;UACtBM,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZC,OAAO,CAACnB,KAAK,CAAC,8BAA8B,EAAEkB,GAAG,CAAC;UAClDJ,QAAQ,CAACI,GAAG,CAACE,OAAO,IAAI,+BAA+B,CAAC;UACxDZ,gBAAgB,CAAC,KAAK,CAAC;QACzB,CAAC,SAAS;UACRG,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAC;MAAA,gBAbKI,UAAUA,CAAA;QAAA,OAAAC,KAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;IAAA,GAaf;IAEDP,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMQ,YAAY,GAAG;IACnBzB,aAAa,EAAbA,aAAa;IACbC,SAAS,EAATA,SAAS;IACTC,KAAK,EAALA;EACF,CAAC;EAED,OACEJ,IAAA,CAACC,eAAe,CAAC2B,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAAnB,QAAA,EAC3CA;EAAQ,CACe,CAAC;AAE/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}