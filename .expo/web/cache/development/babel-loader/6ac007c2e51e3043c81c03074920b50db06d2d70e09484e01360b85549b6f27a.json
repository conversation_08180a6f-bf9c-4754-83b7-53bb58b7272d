{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"DEFAULT_ICON_COLOR\", {\n  enumerable: true,\n  get: function get() {\n    return _createIconSet.DEFAULT_ICON_COLOR;\n  }\n});\nObject.defineProperty(exports, \"DEFAULT_ICON_SIZE\", {\n  enumerable: true,\n  get: function get() {\n    return _createIconSet.DEFAULT_ICON_SIZE;\n  }\n});\nObject.defineProperty(exports, \"createIconSet\", {\n  enumerable: true,\n  get: function get() {\n    return _createIconSet.createIconSet;\n  }\n});\nObject.defineProperty(exports, \"isDynamicLoadingEnabled\", {\n  enumerable: true,\n  get: function get() {\n    return _dynamicLoadingSetting.isDynamicLoadingEnabled;\n  }\n});\nObject.defineProperty(exports, \"isDynamicLoadingSupported\", {\n  enumerable: true,\n  get: function get() {\n    return _dynamicLoadingSetting.isDynamicLoadingSupported;\n  }\n});\nObject.defineProperty(exports, \"setDynamicLoadingEnabled\", {\n  enumerable: true,\n  get: function get() {\n    return _dynamicLoadingSetting.setDynamicLoadingEnabled;\n  }\n});\nObject.defineProperty(exports, \"setDynamicLoadingErrorCallback\", {\n  enumerable: true,\n  get: function get() {\n    return _dynamicLoadingSetting.setDynamicLoadingErrorCallback;\n  }\n});\nvar _createIconSet = require(\"./create-icon-set.js\");\nvar _dynamicLoadingSetting = require(\"./dynamicLoading/dynamic-loading-setting.js\");", "map": {"version": 3, "names": ["_createIconSet", "require", "_dynamicLoadingSetting"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-vector-icons/common/src/index.ts"], "sourcesContent": ["export { createIconSet, DEFAULT_ICON_SIZE, DEFAULT_ICON_COLOR } from './create-icon-set';\nexport type { IconProps, CreateIconSetOptions } from './create-icon-set';\nexport {\n  setDynamicLoadingEnabled,\n  isDynamicLoadingEnabled,\n  isDynamicLoadingSupported,\n  setDynamicLoadingErrorCallback,\n} from './dynamicLoading/dynamic-loading-setting';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,cAAA,GAAAC,OAAA;AAEA,IAAAC,sBAAA,GAAAD,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}