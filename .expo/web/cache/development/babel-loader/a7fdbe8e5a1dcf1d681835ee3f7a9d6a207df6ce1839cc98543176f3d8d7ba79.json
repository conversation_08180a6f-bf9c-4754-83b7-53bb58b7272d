{"ast": null, "code": "import * as React from 'react';\nimport DefaultTheme from \"./DefaultTheme\";\nvar ThemeContext = React.createContext(DefaultTheme);\nThemeContext.displayName = 'ThemeContext';\nexport default ThemeContext;", "map": {"version": 3, "names": ["React", "DefaultTheme", "ThemeContext", "createContext", "displayName"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/native/src/theming/ThemeContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { Theme } from '../types';\nimport DefaultTheme from './DefaultTheme';\n\nconst ThemeContext = React.createContext<Theme>(DefaultTheme);\n\nThemeContext.displayName = 'ThemeContext';\n\nexport default ThemeContext;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,OAAOC,YAAY;AAEnB,IAAMC,YAAY,GAAGF,KAAK,CAACG,aAAa,CAAQF,YAAY,CAAC;AAE7DC,YAAY,CAACE,WAAW,GAAG,cAAc;AAEzC,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}