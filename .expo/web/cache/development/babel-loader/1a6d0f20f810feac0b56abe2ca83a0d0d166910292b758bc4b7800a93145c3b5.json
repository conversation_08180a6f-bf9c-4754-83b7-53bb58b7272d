{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Text from \"../Typography/Text\";\nimport Caption from \"../Typography/v2/Caption\";\nimport Title from \"../Typography/v2/Title\";\nvar LEFT_SIZE = 40;\nvar CardTitle = function CardTitle(_ref) {\n  var title = _ref.title,\n    titleStyle = _ref.titleStyle,\n    _ref$titleNumberOfLin = _ref.titleNumberOfLines,\n    titleNumberOfLines = _ref$titleNumberOfLin === void 0 ? 1 : _ref$titleNumberOfLin,\n    _ref$titleVariant = _ref.titleVariant,\n    titleVariant = _ref$titleVariant === void 0 ? 'bodyLarge' : _ref$titleVariant,\n    titleMaxFontSizeMultiplier = _ref.titleMaxFontSizeMultiplier,\n    subtitle = _ref.subtitle,\n    subtitleStyle = _ref.subtitleStyle,\n    _ref$subtitleNumberOf = _ref.subtitleNumberOfLines,\n    subtitleNumberOfLines = _ref$subtitleNumberOf === void 0 ? 1 : _ref$subtitleNumberOf,\n    _ref$subtitleVariant = _ref.subtitleVariant,\n    subtitleVariant = _ref$subtitleVariant === void 0 ? 'bodyMedium' : _ref$subtitleVariant,\n    subtitleMaxFontSizeMultiplier = _ref.subtitleMaxFontSizeMultiplier,\n    left = _ref.left,\n    leftStyle = _ref.leftStyle,\n    right = _ref.right,\n    rightStyle = _ref.rightStyle,\n    style = _ref.style,\n    themeOverrides = _ref.theme;\n  var theme = useInternalTheme(themeOverrides);\n  var TitleComponent = theme.isV3 ? Text : Title;\n  var SubtitleComponent = theme.isV3 ? Text : Caption;\n  var minHeight = subtitle || left || right ? 72 : 50;\n  var marginBottom = subtitle ? 0 : 2;\n  return React.createElement(View, {\n    style: [styles.container, {\n      minHeight: minHeight\n    }, style]\n  }, left ? React.createElement(View, {\n    style: [styles.left, leftStyle]\n  }, left({\n    size: LEFT_SIZE\n  })) : null, React.createElement(View, {\n    style: [styles.titles]\n  }, title && React.createElement(TitleComponent, {\n    style: [styles.title, {\n      marginBottom: marginBottom\n    }, titleStyle],\n    numberOfLines: titleNumberOfLines,\n    variant: titleVariant,\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }, title), subtitle && React.createElement(SubtitleComponent, {\n    style: [styles.subtitle, subtitleStyle],\n    numberOfLines: subtitleNumberOfLines,\n    variant: subtitleVariant,\n    maxFontSizeMultiplier: subtitleMaxFontSizeMultiplier\n  }, subtitle)), React.createElement(View, {\n    style: rightStyle\n  }, right ? right({\n    size: 24\n  }) : null));\n};\nCardTitle.displayName = 'Card.Title';\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingLeft: 16\n  },\n  left: {\n    justifyContent: 'center',\n    marginRight: 16,\n    height: LEFT_SIZE,\n    width: LEFT_SIZE\n  },\n  titles: {\n    flex: 1,\n    flexDirection: 'column',\n    justifyContent: 'center'\n  },\n  title: {\n    minHeight: 30,\n    paddingRight: 16\n  },\n  subtitle: {\n    minHeight: 20,\n    marginVertical: 0,\n    paddingRight: 16\n  }\n});\nexport default CardTitle;\nexport { CardTitle };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "Text", "Caption", "Title", "LEFT_SIZE", "CardTitle", "_ref", "title", "titleStyle", "_ref$titleNumberOfLin", "titleNumberOfLines", "_ref$titleVariant", "<PERSON><PERSON><PERSON><PERSON>", "titleMaxFontSizeMultiplier", "subtitle", "subtitleStyle", "_ref$subtitleNumberOf", "subtitleNumberOfLines", "_ref$subtitleVariant", "subtitleVariant", "subtitleMaxFontSizeMultiplier", "left", "leftStyle", "right", "rightStyle", "style", "themeOverrides", "theme", "TitleComponent", "isV3", "SubtitleComponent", "minHeight", "marginBottom", "createElement", "styles", "container", "size", "titles", "numberOfLines", "variant", "maxFontSizeMultiplier", "displayName", "create", "flexDirection", "alignItems", "justifyContent", "paddingLeft", "marginRight", "height", "width", "flex", "paddingRight", "marginVertical"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Card/CardTitle.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport type { MD3TypescaleKey, ThemeProp } from '../../types';\nimport Text from '../Typography/Text';\nimport Caption from '../Typography/v2/Caption';\nimport Title from '../Typography/v2/Title';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Text for the title. Note that this will only accept a string or `<Text>`-based node.\n   */\n  title: React.ReactNode;\n  /**\n   * Style for the title.\n   */\n  titleStyle?: StyleProp<TextStyle>;\n  /**\n   * Number of lines for the title.\n   */\n  titleNumberOfLines?: number;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Title text variant defines appropriate text styles for type role and its size.\n   * Available variants:\n   *\n   *  Display: `displayLarge`, `displayMedium`, `displaySmall`\n   *\n   *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`\n   *\n   *  Title: `titleLarge`, `titleMedium`, `titleSmall`\n   *\n   *  Label:  `labelLarge`, `labelMedium`, `labelSmall`\n   *\n   *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`\n   */\n  titleVariant?: keyof typeof MD3TypescaleKey;\n  /**\n   * Text for the subtitle. Note that this will only accept a string or `<Text>`-based node.\n   */\n  subtitle?: React.ReactNode;\n  /**\n   * Style for the subtitle.\n   */\n  subtitleStyle?: StyleProp<TextStyle>;\n  /**\n   * Number of lines for the subtitle.\n   */\n  subtitleNumberOfLines?: number;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Subtitle text variant defines appropriate text styles for type role and its size.\n   * Available variants:\n   *\n   *  Display: `displayLarge`, `displayMedium`, `displaySmall`\n   *\n   *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`\n   *\n   *  Title: `titleLarge`, `titleMedium`, `titleSmall`\n   *\n   *  Label:  `labelLarge`, `labelMedium`, `labelSmall`\n   *\n   *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`\n   */\n  subtitleVariant?: keyof typeof MD3TypescaleKey;\n  /**\n   * Callback which returns a React element to display on the left side.\n   */\n  left?: (props: { size: number }) => React.ReactNode;\n  /**\n   * Style for the left element wrapper.\n   */\n  leftStyle?: StyleProp<ViewStyle>;\n  /**\n   * Callback which returns a React element to display on the right side.\n   */\n  right?: (props: { size: number }) => React.ReactNode;\n  /**\n   * Style for the right element wrapper.\n   */\n  rightStyle?: StyleProp<ViewStyle>;\n  /**\n   * @internal\n   */\n  index?: number;\n  /**\n   * @internal\n   */\n  total?: number;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  /**\n   * Specifies the largest possible scale a subtitle font can reach.\n   */\n  subtitleMaxFontSizeMultiplier?: number;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\nconst LEFT_SIZE = 40;\n\n/**\n * A component to show a title, subtitle and an avatar inside a Card.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Avatar, Card, IconButton } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Card.Title\n *     title=\"Card Title\"\n *     subtitle=\"Card Subtitle\"\n *     left={(props) => <Avatar.Icon {...props} icon=\"folder\" />}\n *     right={(props) => <IconButton {...props} icon=\"dots-vertical\" onPress={() => {}} />}\n *   />\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst CardTitle = ({\n  title,\n  titleStyle,\n  titleNumberOfLines = 1,\n  titleVariant = 'bodyLarge',\n  titleMaxFontSizeMultiplier,\n  subtitle,\n  subtitleStyle,\n  subtitleNumberOfLines = 1,\n  subtitleVariant = 'bodyMedium',\n  subtitleMaxFontSizeMultiplier,\n  left,\n  leftStyle,\n  right,\n  rightStyle,\n  style,\n  theme: themeOverrides,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const TitleComponent = theme.isV3 ? Text : Title;\n  const SubtitleComponent = theme.isV3 ? Text : Caption;\n\n  const minHeight = subtitle || left || right ? 72 : 50;\n  const marginBottom = subtitle ? 0 : 2;\n\n  return (\n    <View style={[styles.container, { minHeight }, style]}>\n      {left ? (\n        <View style={[styles.left, leftStyle]}>\n          {left({\n            size: LEFT_SIZE,\n          })}\n        </View>\n      ) : null}\n\n      <View style={[styles.titles]}>\n        {title && (\n          <TitleComponent\n            style={[styles.title, { marginBottom }, titleStyle]}\n            numberOfLines={titleNumberOfLines}\n            variant={titleVariant}\n            maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n          >\n            {title}\n          </TitleComponent>\n        )}\n        {subtitle && (\n          <SubtitleComponent\n            style={[styles.subtitle, subtitleStyle]}\n            numberOfLines={subtitleNumberOfLines}\n            variant={subtitleVariant}\n            maxFontSizeMultiplier={subtitleMaxFontSizeMultiplier}\n          >\n            {subtitle}\n          </SubtitleComponent>\n        )}\n      </View>\n      <View style={rightStyle}>{right ? right({ size: 24 }) : null}</View>\n    </View>\n  );\n};\n\nCardTitle.displayName = 'Card.Title';\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingLeft: 16,\n  },\n\n  left: {\n    justifyContent: 'center',\n    marginRight: 16,\n    height: LEFT_SIZE,\n    width: LEFT_SIZE,\n  },\n\n  titles: {\n    flex: 1,\n    flexDirection: 'column',\n    justifyContent: 'center',\n  },\n\n  title: {\n    minHeight: 30,\n    paddingRight: 16,\n  },\n\n  subtitle: {\n    minHeight: 20,\n    marginVertical: 0,\n    paddingRight: 16,\n  },\n});\n\nexport default CardTitle;\n\n// @component-docs ignore-next-line\nexport { CardTitle };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAS9B,SAASC,gBAAgB;AAEzB,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,KAAK;AAoGZ,IAAMC,SAAS,GAAG,EAAE;AAsBpB,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAGC,IAAA,EAiBL;EAAA,IAhBXC,KAAK,GAgBCD,IAAA,CAhBNC,KAAK;IACLC,UAAU,GAeJF,IAAA,CAfNE,UAAU;IAAAC,qBAAA,GAeJH,IAAA,CAdNI,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;IAAAE,iBAAA,GAchBL,IAAA,CAbNM,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,WAAW,GAAAA,iBAAA;IAC1BE,0BAA0B,GAYpBP,IAAA,CAZNO,0BAA0B;IAC1BC,QAAQ,GAWFR,IAAA,CAXNQ,QAAQ;IACRC,aAAa,GAUPT,IAAA,CAVNS,aAAa;IAAAC,qBAAA,GAUPV,IAAA,CATNW,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;IAAAE,oBAAA,GASnBZ,IAAA,CARNa,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,YAAY,GAAAA,oBAAA;IAC9BE,6BAA6B,GAOvBd,IAAA,CAPNc,6BAA6B;IAC7BC,IAAI,GAMEf,IAAA,CANNe,IAAI;IACJC,SAAS,GAKHhB,IAAA,CALNgB,SAAS;IACTC,KAAK,GAICjB,IAAA,CAJNiB,KAAK;IACLC,UAAU,GAGJlB,IAAA,CAHNkB,UAAU;IACVC,KAAK,GAECnB,IAAA,CAFNmB,KAAK;IACEC,cAAA,GACDpB,IAAA,CADNqB,KAAK;EAEL,IAAMA,KAAK,GAAG3B,gBAAgB,CAAC0B,cAAc,CAAC;EAC9C,IAAME,cAAc,GAAGD,KAAK,CAACE,IAAI,GAAG5B,IAAI,GAAGE,KAAK;EAChD,IAAM2B,iBAAiB,GAAGH,KAAK,CAACE,IAAI,GAAG5B,IAAI,GAAGC,OAAO;EAErD,IAAM6B,SAAS,GAAGjB,QAAQ,IAAIO,IAAI,IAAIE,KAAK,GAAG,EAAE,GAAG,EAAE;EACrD,IAAMS,YAAY,GAAGlB,QAAQ,GAAG,CAAC,GAAG,CAAC;EAErC,OACEjB,KAAA,CAAAoC,aAAA,CAAClC,IAAI;IAAC0B,KAAK,EAAE,CAACS,MAAM,CAACC,SAAS,EAAE;MAAEJ,SAAA,EAAAA;IAAU,CAAC,EAAEN,KAAK;EAAE,GACnDJ,IAAI,GACHxB,KAAA,CAAAoC,aAAA,CAAClC,IAAI;IAAC0B,KAAK,EAAE,CAACS,MAAM,CAACb,IAAI,EAAEC,SAAS;EAAE,GACnCD,IAAI,CAAC;IACJe,IAAI,EAAEhC;EACR,CAAC,CACG,CAAC,GACL,IAAI,EAERP,KAAA,CAAAoC,aAAA,CAAClC,IAAI;IAAC0B,KAAK,EAAE,CAACS,MAAM,CAACG,MAAM;EAAE,GAC1B9B,KAAK,IACJV,KAAA,CAAAoC,aAAA,CAACL,cAAc;IACbH,KAAK,EAAE,CAACS,MAAM,CAAC3B,KAAK,EAAE;MAAEyB,YAAA,EAAAA;IAAa,CAAC,EAAExB,UAAU,CAAE;IACpD8B,aAAa,EAAE5B,kBAAmB;IAClC6B,OAAO,EAAE3B,YAAa;IACtB4B,qBAAqB,EAAE3B;EAA2B,GAEjDN,KACa,CACjB,EACAO,QAAQ,IACPjB,KAAA,CAAAoC,aAAA,CAACH,iBAAiB;IAChBL,KAAK,EAAE,CAACS,MAAM,CAACpB,QAAQ,EAAEC,aAAa,CAAE;IACxCuB,aAAa,EAAErB,qBAAsB;IACrCsB,OAAO,EAAEpB,eAAgB;IACzBqB,qBAAqB,EAAEpB;EAA8B,GAEpDN,QACgB,CAEjB,CAAC,EACPjB,KAAA,CAAAoC,aAAA,CAAClC,IAAI;IAAC0B,KAAK,EAAED;EAAW,GAAED,KAAK,GAAGA,KAAK,CAAC;IAAEa,IAAI,EAAE;EAAG,CAAC,CAAC,GAAG,IAAW,CAC/D,CAAC;AAEX,CAAC;AAED/B,SAAS,CAACoC,WAAW,GAAG,YAAY;AAEpC,IAAMP,MAAM,GAAGpC,UAAU,CAAC4C,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,WAAW,EAAE;EACf,CAAC;EAEDzB,IAAI,EAAE;IACJwB,cAAc,EAAE,QAAQ;IACxBE,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE5C,SAAS;IACjB6C,KAAK,EAAE7C;EACT,CAAC;EAEDiC,MAAM,EAAE;IACNa,IAAI,EAAE,CAAC;IACPP,aAAa,EAAE,QAAQ;IACvBE,cAAc,EAAE;EAClB,CAAC;EAEDtC,KAAK,EAAE;IACLwB,SAAS,EAAE,EAAE;IACboB,YAAY,EAAE;EAChB,CAAC;EAEDrC,QAAQ,EAAE;IACRiB,SAAS,EAAE,EAAE;IACbqB,cAAc,EAAE,CAAC;IACjBD,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAEF,eAAe9C,SAAS;AAGxB,SAASA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}