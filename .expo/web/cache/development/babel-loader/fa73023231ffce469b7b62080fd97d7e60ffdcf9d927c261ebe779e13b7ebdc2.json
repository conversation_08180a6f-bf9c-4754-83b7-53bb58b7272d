{"ast": null, "code": "import * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useInternalTheme } from \"../../../core/theming\";\nexport var Underline = function Underline(_ref) {\n  var parentState = _ref.parentState,\n    error = _ref.error,\n    colors = _ref.colors,\n    activeColor = _ref.activeColor,\n    underlineColorCustom = _ref.underlineColorCustom,\n    hasActiveOutline = _ref.hasActiveOutline,\n    style = _ref.style,\n    themeOverrides = _ref.theme;\n  var _useInternalTheme = useInternalTheme(themeOverrides),\n    isV3 = _useInternalTheme.isV3;\n  var backgroundColor = parentState.focused ? activeColor : underlineColorCustom;\n  if (error) backgroundColor = colors === null || colors === void 0 ? void 0 : colors.error;\n  var activeScale = isV3 ? 2 : 1;\n  return React.createElement(Animated.View, {\n    testID: \"text-input-underline\",\n    style: [styles.underline, isV3 && styles.md3Underline, {\n      backgroundColor: backgroundColor,\n      transform: [{\n        scaleY: (isV3 ? hasActiveOutline : parentState.focused) ? activeScale : 0.5\n      }]\n    }, style]\n  });\n};\nvar styles = StyleSheet.create({\n  underline: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    bottom: 0,\n    height: 2,\n    zIndex: 1\n  },\n  md3Underline: {\n    height: 1\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "useInternalTheme", "Underline", "_ref", "parentState", "error", "colors", "activeColor", "underlineColorCustom", "hasActiveOutline", "style", "themeOverrides", "theme", "_useInternalTheme", "isV3", "backgroundColor", "focused", "activeScale", "createElement", "View", "testID", "styles", "underline", "md3Underline", "transform", "scaleY", "create", "position", "left", "right", "bottom", "height", "zIndex"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-paper/src/components/TextInput/Addons/Underline.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated, StyleSheet, StyleProp, ViewStyle } from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { useInternalTheme } from '../../../core/theming';\n\ntype UnderlineProps = {\n  parentState: {\n    focused: boolean;\n  };\n  error?: boolean;\n  colors?: {\n    error?: string;\n  };\n  activeColor: string;\n  underlineColorCustom?: string;\n  hasActiveOutline?: boolean;\n  style?: StyleProp<ViewStyle>;\n  theme?: ThemeProp;\n};\n\nexport const Underline = ({\n  parentState,\n  error,\n  colors,\n  activeColor,\n  underlineColorCustom,\n  hasActiveOutline,\n  style,\n  theme: themeOverrides,\n}: UnderlineProps) => {\n  const { isV3 } = useInternalTheme(themeOverrides);\n\n  let backgroundColor = parentState.focused\n    ? activeColor\n    : underlineColorCustom;\n\n  if (error) backgroundColor = colors?.error;\n\n  const activeScale = isV3 ? 2 : 1;\n\n  return (\n    <Animated.View\n      testID=\"text-input-underline\"\n      style={[\n        styles.underline,\n        isV3 && styles.md3Underline,\n        {\n          backgroundColor,\n          // Underlines is thinner when input is not focused\n          transform: [\n            {\n              scaleY: (isV3 ? hasActiveOutline : parentState.focused)\n                ? activeScale\n                : 0.5,\n            },\n          ],\n        },\n        style,\n      ]}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  underline: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    bottom: 0,\n    height: 2,\n    zIndex: 1,\n  },\n  md3Underline: {\n    height: 1,\n  },\n});\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAK9B,SAASC,gBAAgB;AAiBzB,OAAO,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAGC,IAAA,EASH;EAAA,IARpBC,WAAW,GAQID,IAAA,CARfC,WAAW;IACXC,KAAK,GAOUF,IAAA,CAPfE,KAAK;IACLC,MAAM,GAMSH,IAAA,CANfG,MAAM;IACNC,WAAW,GAKIJ,IAAA,CALfI,WAAW;IACXC,oBAAoB,GAILL,IAAA,CAJfK,oBAAoB;IACpBC,gBAAgB,GAGDN,IAAA,CAHfM,gBAAgB;IAChBC,KAAK,GAEUP,IAAA,CAFfO,KAAK;IACEC,cAAA,GACQR,IAAA,CADfS,KAAK;EAEL,IAAAC,iBAAA,GAAiBZ,gBAAgB,CAACU,cAAc,CAAC;IAAzCG,IAAA,GAAAD,iBAAA,CAAAC,IAAA;EAER,IAAIC,eAAe,GAAGX,WAAW,CAACY,OAAO,GACrCT,WAAW,GACXC,oBAAoB;EAExB,IAAIH,KAAK,EAAEU,eAAe,GAAGT,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAED,KAAK;EAE1C,IAAMY,WAAW,GAAGH,IAAI,GAAG,CAAC,GAAG,CAAC;EAEhC,OACEhB,KAAA,CAAAoB,aAAA,CAACnB,QAAQ,CAACoB,IAAI;IACZC,MAAM,EAAC,sBAAsB;IAC7BV,KAAK,EAAE,CACLW,MAAM,CAACC,SAAS,EAChBR,IAAI,IAAIO,MAAM,CAACE,YAAY,EAC3B;MACER,eAAe,EAAfA,eAAe;MAEfS,SAAS,EAAE,CACT;QACEC,MAAM,EAAE,CAACX,IAAI,GAAGL,gBAAgB,GAAGL,WAAW,CAACY,OAAO,IAClDC,WAAW,GACX;MACN,CAAC;IAEL,CAAC,EACDP,KAAK;EACL,CACH,CAAC;AAEN,CAAC;AAED,IAAMW,MAAM,GAAGrB,UAAU,CAAC0B,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IACTK,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;EACDT,YAAY,EAAE;IACZQ,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}