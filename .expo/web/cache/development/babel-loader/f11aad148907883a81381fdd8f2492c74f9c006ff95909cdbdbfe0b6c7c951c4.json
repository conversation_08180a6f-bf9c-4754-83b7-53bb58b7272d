{"ast": null, "code": "import Platform from \"react-native-web/dist/exports/Platform\";\nimport createMultiStyleIconSet from \"./createMultiStyleIconSet\";\nexport var FA5Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand'\n};\nexport function createFA5iconSet(glyphMap) {\n  var metadata = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var fonts = arguments.length > 2 ? arguments[2] : undefined;\n  var pro = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var metadataKeys = Object.keys(metadata);\n  var fontFamily = `FontAwesome5${pro ? 'Pro' : 'Free'}`;\n  function fallbackFamily(glyph) {\n    for (var i = 0; i < metadataKeys.length; i += 1) {\n      var family = metadataKeys[i];\n      if (metadata[family].indexOf(glyph) !== -1) {\n        return family === 'brands' ? 'brand' : family;\n      }\n    }\n    return 'regular';\n  }\n  function glyphValidator(glyph, style) {\n    var family = style === 'brand' ? 'brands' : style;\n    if (metadataKeys.indexOf(family) === -1) return false;\n    return metadata[family].indexOf(glyph) !== -1;\n  }\n  function createFontAwesomeStyle(styleName, fontWeight) {\n    var family = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : fontFamily;\n    var fontFile = fonts[styleName];\n    return {\n      fontFamily: `${family}-${styleName}`,\n      fontFile: fontFile,\n      fontStyle: Platform.select({\n        ios: {\n          fontWeight: fontWeight\n        },\n        default: {}\n      }),\n      glyphMap: glyphMap\n    };\n  }\n  var brandIcons = createFontAwesomeStyle('Brand', '400');\n  var lightIcons = createFontAwesomeStyle('Light', '100');\n  var regularIcons = createFontAwesomeStyle('Regular', '400');\n  var solidIcons = createFontAwesomeStyle('Solid', '700');\n  var Icon = createMultiStyleIconSet({\n    brand: brandIcons,\n    light: lightIcons,\n    regular: regularIcons,\n    solid: solidIcons\n  }, {\n    defaultStyle: 'regular',\n    fallbackFamily: fallbackFamily,\n    glyphValidator: glyphValidator\n  });\n  return Icon;\n}", "map": {"version": 3, "names": ["createMultiStyleIconSet", "FA5Style", "regular", "light", "solid", "brand", "createFA5iconSet", "glyphMap", "metadata", "arguments", "length", "undefined", "fonts", "pro", "metadataKeys", "Object", "keys", "fontFamily", "fallbackFamily", "glyph", "i", "family", "indexOf", "glyphValidator", "style", "createFontAwesomeStyle", "styleName", "fontWeight", "fontFile", "fontStyle", "Platform", "select", "ios", "default", "brandIcons", "lightIcons", "regularIcons", "solidIcons", "Icon", "defaultStyle"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/src/createIconSetFromFontAwesome5.ts"], "sourcesContent": ["import { Platform } from 'react-native';\nimport createMultiStyleIconSet from './createMultiStyleIconSet';\n\nexport const FA5Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand',\n};\n\nexport function createFA5iconSet(glyphMap, metadata = {}, fonts, pro = false) {\n  const metadataKeys = Object.keys(metadata);\n  const fontFamily = `FontAwesome5${pro ? 'Pro' : 'Free'}`;\n\n  function fallbackFamily(glyph) {\n    for (let i = 0; i < metadataKeys.length; i += 1) {\n      const family = metadataKeys[i];\n      if (metadata[family].indexOf(glyph) !== -1) {\n        return family === 'brands' ? 'brand' : family;\n      }\n    }\n\n    return 'regular';\n  }\n\n  function glyphValidator(glyph, style) {\n    const family = style === 'brand' ? 'brands' : style;\n    if (metadataKeys.indexOf(family) === -1) return false;\n    return metadata[family].indexOf(glyph) !== -1;\n  }\n\n  function createFontAwesomeStyle(styleName, fontWeight, family = fontFamily) {\n    let fontFile = fonts[styleName];\n\n    return {\n      fontFamily: `${family}-${styleName}`,\n      fontFile,\n      fontStyle: Platform.select({\n        ios: {\n          fontWeight,\n        },\n        default: {},\n      }),\n      glyphMap,\n    };\n  }\n\n  const brandIcons = createFontAwesomeStyle('Brand', '400');\n  const lightIcons = createFontAwesomeStyle('Light', '100');\n  const regularIcons = createFontAwesomeStyle('Regular', '400');\n  const solidIcons = createFontAwesomeStyle('Solid', '700');\n  const Icon = createMultiStyleIconSet(\n    {\n      brand: brandIcons,\n      light: lightIcons,\n      regular: regularIcons,\n      solid: solidIcons,\n    },\n    {\n      defaultStyle: 'regular',\n      fallbackFamily,\n      glyphValidator,\n    },\n  );\n\n  return Icon;\n}"], "mappings": ";AACA,OAAOA,uBAAuB;AAE9B,OAAO,IAAMC,QAAQ,GAAG;EACtBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;CACR;AAED,OAAM,SAAUC,gBAAgBA,CAACC,QAAQ,EAAmC;EAAA,IAAjCC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,GAAG,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC1E,IAAMK,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACR,QAAQ,CAAC;EAC1C,IAAMS,UAAU,GAAG,eAAeJ,GAAG,GAAG,KAAK,GAAG,MAAM,EAAE;EAExD,SAASK,cAAcA,CAACC,KAAK;IAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,YAAY,CAACJ,MAAM,EAAEU,CAAC,IAAI,CAAC,EAAE;MAC/C,IAAMC,MAAM,GAAGP,YAAY,CAACM,CAAC,CAAC;MAC9B,IAAIZ,QAAQ,CAACa,MAAM,CAAC,CAACC,OAAO,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1C,OAAOE,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAGA,MAAM;;;IAIjD,OAAO,SAAS;EAClB;EAEA,SAASE,cAAcA,CAACJ,KAAK,EAAEK,KAAK;IAClC,IAAMH,MAAM,GAAGG,KAAK,KAAK,OAAO,GAAG,QAAQ,GAAGA,KAAK;IACnD,IAAIV,YAAY,CAACQ,OAAO,CAACD,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;IACrD,OAAOb,QAAQ,CAACa,MAAM,CAAC,CAACC,OAAO,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC;EAC/C;EAEA,SAASM,sBAAsBA,CAACC,SAAS,EAAEC,UAAU,EAAqB;IAAA,IAAnBN,MAAM,GAAAZ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGQ,UAAU;IACxE,IAAIW,QAAQ,GAAGhB,KAAK,CAACc,SAAS,CAAC;IAE/B,OAAO;MACLT,UAAU,EAAE,GAAGI,MAAM,IAAIK,SAAS,EAAE;MACpCE,QAAQ,EAARA,QAAQ;MACRC,SAAS,EAAEC,QAAQ,CAACC,MAAM,CAAC;QACzBC,GAAG,EAAE;UACHL,UAAU,EAAVA;SACD;QACDM,OAAO,EAAE;OACV,CAAC;MACF1B,QAAQ,EAARA;KACD;EACH;EAEA,IAAM2B,UAAU,GAAGT,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;EACzD,IAAMU,UAAU,GAAGV,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;EACzD,IAAMW,YAAY,GAAGX,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;EAC7D,IAAMY,UAAU,GAAGZ,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;EACzD,IAAMa,IAAI,GAAGtC,uBAAuB,CAClC;IACEK,KAAK,EAAE6B,UAAU;IACjB/B,KAAK,EAAEgC,UAAU;IACjBjC,OAAO,EAAEkC,YAAY;IACrBhC,KAAK,EAAEiC;GACR,EACD;IACEE,YAAY,EAAE,SAAS;IACvBrB,cAAc,EAAdA,cAAc;IACdK,cAAc,EAAdA;GACD,CACF;EAED,OAAOe,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}