{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nvar _excluded = [\"style\", \"iconStyle\", \"children\"];\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport React, { PureComponent } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableHighlight from \"react-native-web/dist/exports/TouchableHighlight\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { pick, omit } from \"./object-utils\";\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    justifyContent: 'flex-start',\n    alignItems: 'center',\n    padding: 8\n  },\n  touchable: {\n    overflow: 'hidden'\n  },\n  icon: {\n    marginRight: 10\n  },\n  text: {\n    fontWeight: '600',\n    backgroundColor: 'transparent'\n  }\n});\nvar IOS7_BLUE = '#007AFF';\nvar TEXT_PROP_NAMES = ['ellipsizeMode', 'numberOfLines', 'textBreakStrategy', 'selectable', 'suppressHighlighting', 'allowFontScaling', 'adjustsFontSizeToFit', 'minimumFontScale'];\nvar TOUCHABLE_PROP_NAMES = ['accessible', 'accessibilityLabel', 'accessibilityHint', 'accessibilityComponentType', 'accessibilityRole', 'accessibilityStates', 'accessibilityTraits', 'onFocus', 'onBlur', 'disabled', 'onPress', 'onPressIn', 'onPressOut', 'onLayout', 'onLongPress', 'nativeID', 'testID', 'delayPressIn', 'delayPressOut', 'delayLongPress', 'activeOpacity', 'underlayColor', 'selectionColor', 'onShowUnderlay', 'onHideUnderlay', 'hasTVPreferredFocus', 'tvParallaxProperties'];\nexport default function createIconButtonComponent(Icon) {\n  var _IconButton;\n  return _IconButton = function (_PureComponent) {\n    function IconButton() {\n      _classCallCheck(this, IconButton);\n      return _callSuper(this, IconButton, arguments);\n    }\n    _inherits(IconButton, _PureComponent);\n    return _createClass(IconButton, [{\n      key: \"render\",\n      value: function render() {\n        var _this$props = this.props,\n          style = _this$props.style,\n          iconStyle = _this$props.iconStyle,\n          children = _this$props.children,\n          restProps = _objectWithoutProperties(_this$props, _excluded);\n        var iconProps = pick(restProps, TEXT_PROP_NAMES, 'style', 'name', 'size', 'color');\n        var touchableProps = pick(restProps, TOUCHABLE_PROP_NAMES);\n        var props = omit(restProps, Object.keys(iconProps), Object.keys(touchableProps), 'iconStyle', 'borderRadius', 'backgroundColor');\n        iconProps.style = iconStyle ? [styles.icon, iconStyle] : styles.icon;\n        var colorStyle = pick(this.props, 'color');\n        var blockStyle = pick(this.props, 'backgroundColor', 'borderRadius');\n        return React.createElement(TouchableHighlight, _extends({\n          style: [styles.touchable, blockStyle]\n        }, touchableProps), React.createElement(View, _extends({\n          style: [styles.container, blockStyle, style]\n        }, props), React.createElement(Icon, iconProps), typeof children === 'string' ? React.createElement(Text, {\n          style: [styles.text, colorStyle],\n          selectable: false\n        }, children) : children));\n      }\n    }]);\n  }(PureComponent), _IconButton.defaultProps = {\n    backgroundColor: IOS7_BLUE,\n    borderRadius: 5,\n    color: 'white',\n    size: 20\n  }, _IconButton;\n}", "map": {"version": 3, "names": ["React", "PureComponent", "StyleSheet", "Text", "TouchableHighlight", "View", "pick", "omit", "styles", "create", "container", "flexDirection", "justifyContent", "alignItems", "padding", "touchable", "overflow", "icon", "marginRight", "text", "fontWeight", "backgroundColor", "IOS7_BLUE", "TEXT_PROP_NAMES", "TOUCHABLE_PROP_NAMES", "createIconButtonComponent", "Icon", "_IconButton", "_PureComponent", "IconButton", "_classCallCheck", "_callSuper", "arguments", "_inherits", "_createClass", "key", "value", "render", "_this$props", "props", "style", "iconStyle", "children", "restProps", "_objectWithoutProperties", "_excluded", "iconProps", "touchableProps", "Object", "keys", "colorStyle", "blockStyle", "createElement", "_extends", "selectable", "defaultProps", "borderRadius", "color", "size"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/icon-button.js"], "sourcesContent": ["import React, { PureComponent } from 'react';\nimport { StyleSheet, Text, TouchableHighlight, View } from 'react-native';\nimport { pick, omit } from './object-utils';\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    justifyContent: 'flex-start',\n    alignItems: 'center',\n    padding: 8,\n  },\n  touchable: {\n    overflow: 'hidden',\n  },\n  icon: {\n    marginRight: 10,\n  },\n  text: {\n    fontWeight: '600',\n    backgroundColor: 'transparent',\n  },\n});\n\nconst IOS7_BLUE = '#007AFF';\n\nconst TEXT_PROP_NAMES = [\n  'ellipsizeMode',\n  'numberOfLines',\n  'textBreakStrategy',\n  'selectable',\n  'suppressHighlighting',\n  'allowFontScaling',\n  'adjustsFontSizeToFit',\n  'minimumFontScale',\n];\n\nconst TOUCHABLE_PROP_NAMES = [\n  'accessible',\n  'accessibilityLabel',\n  'accessibilityHint',\n  'accessibilityComponentType',\n  'accessibilityRole',\n  'accessibilityStates',\n  'accessibilityTraits',\n  'onFocus',\n  'onBlur',\n  'disabled',\n  'onPress',\n  'onPressIn',\n  'onPressOut',\n  'onLayout',\n  'onLongPress',\n  'nativeID',\n  'testID',\n  'delayPressIn',\n  'delayPressOut',\n  'delayLongPress',\n  'activeOpacity',\n  'underlayColor',\n  'selectionColor',\n  'onShowUnderlay',\n  'onHideUnderlay',\n  'hasTVPreferredFocus',\n  'tvParallaxProperties',\n];\n\nexport default function createIconButtonComponent(Icon) {\n  return class IconButton extends PureComponent {\n    static defaultProps = {\n      backgroundColor: IOS7_BLUE,\n      borderRadius: 5,\n      color: 'white',\n      size: 20,\n    };\n\n    render() {\n      const { style, iconStyle, children, ...restProps } = this.props;\n\n      const iconProps = pick(\n        restProps,\n        TEXT_PROP_NAMES,\n        'style',\n        'name',\n        'size',\n        'color'\n      );\n      const touchableProps = pick(restProps, TOUCHABLE_PROP_NAMES);\n      const props = omit(\n        restProps,\n        Object.keys(iconProps),\n        Object.keys(touchableProps),\n        'iconStyle',\n        'borderRadius',\n        'backgroundColor'\n      );\n      iconProps.style = iconStyle ? [styles.icon, iconStyle] : styles.icon;\n\n      const colorStyle = pick(this.props, 'color');\n      const blockStyle = pick(this.props, 'backgroundColor', 'borderRadius');\n\n      return (\n        <TouchableHighlight\n          style={[styles.touchable, blockStyle]}\n          {...touchableProps}\n        >\n          <View style={[styles.container, blockStyle, style]} {...props}>\n            <Icon {...iconProps} />\n            {typeof children === 'string' ? (\n              <Text style={[styles.text, colorStyle]} selectable={false}>\n                {children}\n              </Text>\n            ) : (\n              children\n            )}\n          </View>\n        </TouchableHighlight>\n      );\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,kBAAA;AAAA,OAAAC,IAAA;AAE7C,SAASC,IAAI,EAAEC,IAAI;AAEnB,IAAMC,MAAM,GAAGN,UAAU,CAACO,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE;IACJC,WAAW,EAAE;EACf,CAAC;EACDC,IAAI,EAAE;IACJC,UAAU,EAAE,KAAK;IACjBC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AAEF,IAAMC,SAAS,GAAG,SAAS;AAE3B,IAAMC,eAAe,GAAG,CACtB,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,YAAY,EACZ,sBAAsB,EACtB,kBAAkB,EAClB,sBAAsB,EACtB,kBAAkB,CACnB;AAED,IAAMC,oBAAoB,GAAG,CAC3B,YAAY,EACZ,oBAAoB,EACpB,mBAAmB,EACnB,4BAA4B,EAC5B,mBAAmB,EACnB,qBAAqB,EACrB,qBAAqB,EACrB,SAAS,EACT,QAAQ,EACR,UAAU,EACV,SAAS,EACT,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,EACb,UAAU,EACV,QAAQ,EACR,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,qBAAqB,EACrB,sBAAsB,CACvB;AAED,eAAe,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAAA,IAAAC,WAAA;EACtD,OAAAA,WAAA,aAAAC,cAAA;IAAA,SAAAC,WAAA;MAAAC,eAAA,OAAAD,UAAA;MAAA,OAAAE,UAAA,OAAAF,UAAA,EAAAG,SAAA;IAAA;IAAAC,SAAA,CAAAJ,UAAA,EAAAD,cAAA;IAAA,OAAAM,YAAA,CAAAL,UAAA;MAAAM,GAAA;MAAAC,KAAA,EAQE,SAAAC,MAAMA,CAAA,EAAG;QACP,IAAAC,WAAA,GAAqD,IAAI,CAACC,KAAK;UAAvDC,KAAK,GAAAF,WAAA,CAALE,KAAK;UAAEC,SAAS,GAAAH,WAAA,CAATG,SAAS;UAAEC,QAAQ,GAAAJ,WAAA,CAARI,QAAQ;UAAKC,SAAS,GAAAC,wBAAA,CAAAN,WAAA,EAAAO,SAAA;QAEhD,IAAMC,SAAS,GAAGxC,IAAI,CACpBqC,SAAS,EACTpB,eAAe,EACf,OAAO,EACP,MAAM,EACN,MAAM,EACN,OACF,CAAC;QACD,IAAMwB,cAAc,GAAGzC,IAAI,CAACqC,SAAS,EAAEnB,oBAAoB,CAAC;QAC5D,IAAMe,KAAK,GAAGhC,IAAI,CAChBoC,SAAS,EACTK,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,EACtBE,MAAM,CAACC,IAAI,CAACF,cAAc,CAAC,EAC3B,WAAW,EACX,cAAc,EACd,iBACF,CAAC;QACDD,SAAS,CAACN,KAAK,GAAGC,SAAS,GAAG,CAACjC,MAAM,CAACS,IAAI,EAAEwB,SAAS,CAAC,GAAGjC,MAAM,CAACS,IAAI;QAEpE,IAAMiC,UAAU,GAAG5C,IAAI,CAAC,IAAI,CAACiC,KAAK,EAAE,OAAO,CAAC;QAC5C,IAAMY,UAAU,GAAG7C,IAAI,CAAC,IAAI,CAACiC,KAAK,EAAE,iBAAiB,EAAE,cAAc,CAAC;QAEtE,OACEvC,KAAA,CAAAoD,aAAA,CAAChD,kBAAkB,EAAAiD,QAAA;UACjBb,KAAK,EAAE,CAAChC,MAAM,CAACO,SAAS,EAAEoC,UAAU;QAAE,GAClCJ,cAAc,GAElB/C,KAAA,CAAAoD,aAAA,CAAC/C,IAAI,EAAAgD,QAAA;UAACb,KAAK,EAAE,CAAChC,MAAM,CAACE,SAAS,EAAEyC,UAAU,EAAEX,KAAK;QAAE,GAAKD,KAAK,GAC3DvC,KAAA,CAAAoD,aAAA,CAAC1B,IAAI,EAAKoB,SAAY,CAAC,EACtB,OAAOJ,QAAQ,KAAK,QAAQ,GAC3B1C,KAAA,CAAAoD,aAAA,CAACjD,IAAI;UAACqC,KAAK,EAAE,CAAChC,MAAM,CAACW,IAAI,EAAE+B,UAAU,CAAE;UAACI,UAAU,EAAE;QAAM,GACvDZ,QACG,CAAC,GAEPA,QAEE,CACY,CAAC;MAEzB;IAAC;EAAA,EAlD6BzC,aAAa,GAAA0B,WAAA,CACpC4B,YAAY,GAAG;IACpBlC,eAAe,EAAEC,SAAS;IAC1BkC,YAAY,EAAE,CAAC;IACfC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACR,CAAC,EAAA/B,WAAA;AA8CL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}