{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { BarCodeScanner } from 'expo-barcode-scanner';\nimport { Button, ActivityIndicator, Surface } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { getFoodByBarcode, saveFood } from \"../services/databaseService\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar BarcodeScanner = function BarcodeScanner(_ref) {\n  var onScan = _ref.onScan,\n    onClose = _ref.onClose;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    hasPermission = _useState2[0],\n    setHasPermission = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    scanned = _useState4[0],\n    setScanned = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    scanning = _useState6[0],\n    setScanning = _useState6[1];\n  useEffect(function () {\n    var getBarCodeScannerPermissions = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        var _yield$BarCodeScanner = yield BarCodeScanner.requestPermissionsAsync(),\n          status = _yield$BarCodeScanner.status;\n        setHasPermission(status === 'granted');\n      });\n      return function getBarCodeScannerPermissions() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    getBarCodeScannerPermissions();\n  }, []);\n  var handleBarCodeScanned = function () {\n    var _ref4 = _asyncToGenerator(function* (_ref3) {\n      var type = _ref3.type,\n        data = _ref3.data;\n      if (scanned || scanning) return;\n      setScanned(true);\n      setScanning(true);\n      try {\n        var food = yield getFoodByBarcode(data);\n        if (food) {\n          onScan(food);\n        } else {\n          Alert.alert('Food Not Found', `The barcode ${data} was not found in the database. Would you like to add it?`, [{\n            text: 'Cancel',\n            style: 'cancel',\n            onPress: function onPress() {\n              setScanned(false);\n              setScanning(false);\n            }\n          }, {\n            text: 'Add',\n            onPress: function () {\n              var _onPress = _asyncToGenerator(function* () {\n                var newFood = {\n                  name: 'New Food',\n                  description: '',\n                  barcode: data,\n                  brand: '',\n                  serving_size: 100,\n                  serving_unit: 'g',\n                  is_custom: 1,\n                  nutrients: [{\n                    nutrient_id: 'nutrient-calories',\n                    amount: 0\n                  }, {\n                    nutrient_id: 'nutrient-protein',\n                    amount: 0\n                  }, {\n                    nutrient_id: 'nutrient-carbs',\n                    amount: 0\n                  }, {\n                    nutrient_id: 'nutrient-fat',\n                    amount: 0\n                  }]\n                };\n                var savedFood = yield saveFood(newFood);\n                onScan(savedFood);\n              });\n              function onPress() {\n                return _onPress.apply(this, arguments);\n              }\n              return onPress;\n            }()\n          }]);\n        }\n      } catch (error) {\n        console.error('Error scanning barcode:', error);\n        Alert.alert('Error', 'Failed to process barcode. Please try again.');\n        setScanned(false);\n        setScanning(false);\n      }\n    });\n    return function handleBarCodeScanned(_x) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  if (hasPermission === null) {\n    return _jsxs(View, {\n      style: [styles.container, {\n        backgroundColor: theme.colors.background\n      }],\n      children: [_jsx(ActivityIndicator, {\n        size: \"large\",\n        color: theme.colors.primary\n      }), _jsx(Text, {\n        style: [styles.text, {\n          color: theme.colors.text\n        }],\n        children: \"Requesting camera permission...\"\n      })]\n    });\n  }\n  if (hasPermission === false) {\n    return _jsxs(View, {\n      style: [styles.container, {\n        backgroundColor: theme.colors.background\n      }],\n      children: [_jsx(Text, {\n        style: [styles.text, {\n          color: theme.colors.text\n        }],\n        children: \"No access to camera\"\n      }), _jsx(Button, {\n        mode: \"contained\",\n        onPress: onClose,\n        style: styles.button,\n        children: \"Close\"\n      })]\n    });\n  }\n  return _jsxs(View, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: [_jsx(BarCodeScanner, {\n      onBarCodeScanned: scanned ? undefined : handleBarCodeScanned,\n      style: styles.scanner\n    }), _jsxs(Surface, {\n      style: [styles.overlay, {\n        backgroundColor: theme.colors.backdrop\n      }],\n      children: [_jsx(Text, {\n        style: [styles.instructions, {\n          color: theme.colors.text\n        }],\n        children: \"Position the barcode within the frame to scan\"\n      }), scanning && _jsxs(View, {\n        style: styles.loadingContainer,\n        children: [_jsx(ActivityIndicator, {\n          size: \"large\",\n          color: theme.colors.primary\n        }), _jsx(Text, {\n          style: [styles.text, {\n            color: theme.colors.text\n          }],\n          children: \"Processing...\"\n        })]\n      }), _jsx(Button, {\n        mode: \"contained\",\n        onPress: function onPress() {\n          setScanned(false);\n          onClose();\n        },\n        style: styles.button,\n        disabled: scanning,\n        children: \"Cancel\"\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'column',\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  scanner: _objectSpread({}, StyleSheet.absoluteFillObject),\n  overlay: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    padding: 20,\n    borderTopLeftRadius: 20,\n    borderTopRightRadius: 20,\n    alignItems: 'center'\n  },\n  instructions: {\n    fontSize: 16,\n    marginBottom: 20,\n    textAlign: 'center'\n  },\n  button: {\n    marginTop: 20,\n    width: '80%'\n  },\n  loadingContainer: {\n    alignItems: 'center',\n    marginVertical: 20\n  },\n  text: {\n    fontSize: 16,\n    marginTop: 10,\n    textAlign: 'center'\n  }\n});\nexport default BarcodeScanner;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "Text", "<PERSON><PERSON>", "BarCodeScanner", "<PERSON><PERSON>", "ActivityIndicator", "Surface", "useTheme", "getFoodByBarcode", "saveFood", "jsx", "_jsx", "jsxs", "_jsxs", "BarcodeScanner", "_ref", "onScan", "onClose", "_useTheme", "theme", "_useState", "_useState2", "_slicedToArray", "hasPermission", "setHasPermission", "_useState3", "_useState4", "scanned", "setScanned", "_useState5", "_useState6", "scanning", "setScanning", "getBarCodeScannerPermissions", "_ref2", "_asyncToGenerator", "_yield$BarCodeScanner", "requestPermissionsAsync", "status", "apply", "arguments", "handleBarCodeScanned", "_ref4", "_ref3", "type", "data", "food", "alert", "text", "style", "onPress", "_onPress", "newFood", "name", "description", "barcode", "brand", "serving_size", "serving_unit", "is_custom", "nutrients", "nutrient_id", "amount", "savedFood", "error", "console", "_x", "styles", "container", "backgroundColor", "colors", "background", "children", "size", "color", "primary", "mode", "button", "onBarCodeScanned", "undefined", "scanner", "overlay", "backdrop", "instructions", "loadingContainer", "disabled", "create", "flex", "flexDirection", "justifyContent", "alignItems", "_objectSpread", "absoluteFillObject", "position", "bottom", "left", "right", "padding", "borderTopLeftRadius", "borderTopRightRadius", "fontSize", "marginBottom", "textAlign", "marginTop", "width", "marginVertical"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/components/BarcodeScanner.js"], "sourcesContent": ["/**\n * Barcode Scanner Component for ZnüniZähler\n * Provides a camera view to scan food barcodes\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, Text, Alert } from 'react-native';\nimport { BarCodeScanner } from 'expo-barcode-scanner';\nimport { Button, ActivityIndicator, Surface } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { getFoodByBarcode, saveFood } from '../services/databaseService';\n\n/**\n * Barcode Scanner Component\n * @param {Object} props - Component props\n * @param {Function} props.onScan - Callback function when a barcode is scanned\n * @param {Function} props.onClose - Callback function to close the scanner\n * @returns {JSX.Element} - Barcode scanner component\n */\nconst BarcodeScanner = ({ onScan, onClose }) => {\n  const { theme } = useTheme();\n  const [hasPermission, setHasPermission] = useState(null);\n  const [scanned, setScanned] = useState(false);\n  const [scanning, setScanning] = useState(false);\n  \n  // Request camera permission\n  useEffect(() => {\n    const getBarCodeScannerPermissions = async () => {\n      const { status } = await BarCodeScanner.requestPermissionsAsync();\n      setHasPermission(status === 'granted');\n    };\n    \n    getBarCodeScannerPermissions();\n  }, []);\n  \n  // Handle barcode scan\n  const handleBarCodeScanned = async ({ type, data }) => {\n    if (scanned || scanning) return;\n    \n    setScanned(true);\n    setScanning(true);\n    \n    try {\n      // Look up the barcode in the database\n      const food = await getFoodByBarcode(data);\n      \n      if (food) {\n        // Food found in database\n        onScan(food);\n      } else {\n        // Food not found, ask user if they want to add it\n        Alert.alert(\n          'Food Not Found',\n          `The barcode ${data} was not found in the database. Would you like to add it?`,\n          [\n            {\n              text: 'Cancel',\n              style: 'cancel',\n              onPress: () => {\n                setScanned(false);\n                setScanning(false);\n              }\n            },\n            {\n              text: 'Add',\n              onPress: async () => {\n                // Create a new food item with the barcode\n                const newFood = {\n                  name: 'New Food',\n                  description: '',\n                  barcode: data,\n                  brand: '',\n                  serving_size: 100,\n                  serving_unit: 'g',\n                  is_custom: 1,\n                  nutrients: [\n                    { nutrient_id: 'nutrient-calories', amount: 0 },\n                    { nutrient_id: 'nutrient-protein', amount: 0 },\n                    { nutrient_id: 'nutrient-carbs', amount: 0 },\n                    { nutrient_id: 'nutrient-fat', amount: 0 }\n                  ]\n                };\n                \n                const savedFood = await saveFood(newFood);\n                onScan(savedFood);\n              }\n            }\n          ]\n        );\n      }\n    } catch (error) {\n      console.error('Error scanning barcode:', error);\n      Alert.alert('Error', 'Failed to process barcode. Please try again.');\n      setScanned(false);\n      setScanning(false);\n    }\n  };\n  \n  // Handle permission denied\n  if (hasPermission === null) {\n    return (\n      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n        <ActivityIndicator size=\"large\" color={theme.colors.primary} />\n        <Text style={[styles.text, { color: theme.colors.text }]}>Requesting camera permission...</Text>\n      </View>\n    );\n  }\n  \n  if (hasPermission === false) {\n    return (\n      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n        <Text style={[styles.text, { color: theme.colors.text }]}>No access to camera</Text>\n        <Button mode=\"contained\" onPress={onClose} style={styles.button}>\n          Close\n        </Button>\n      </View>\n    );\n  }\n  \n  return (\n    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <BarCodeScanner\n        onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}\n        style={styles.scanner}\n      />\n      \n      <Surface style={[styles.overlay, { backgroundColor: theme.colors.backdrop }]}>\n        <Text style={[styles.instructions, { color: theme.colors.text }]}>\n          Position the barcode within the frame to scan\n        </Text>\n        \n        {scanning && (\n          <View style={styles.loadingContainer}>\n            <ActivityIndicator size=\"large\" color={theme.colors.primary} />\n            <Text style={[styles.text, { color: theme.colors.text }]}>Processing...</Text>\n          </View>\n        )}\n        \n        <Button \n          mode=\"contained\" \n          onPress={() => {\n            setScanned(false);\n            onClose();\n          }}\n          style={styles.button}\n          disabled={scanning}\n        >\n          Cancel\n        </Button>\n      </Surface>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'column',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  scanner: {\n    ...StyleSheet.absoluteFillObject,\n  },\n  overlay: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    padding: 20,\n    borderTopLeftRadius: 20,\n    borderTopRightRadius: 20,\n    alignItems: 'center',\n  },\n  instructions: {\n    fontSize: 16,\n    marginBottom: 20,\n    textAlign: 'center',\n  },\n  button: {\n    marginTop: 20,\n    width: '80%',\n  },\n  loadingContainer: {\n    alignItems: 'center',\n    marginVertical: 20,\n  },\n  text: {\n    fontSize: 16,\n    marginTop: 10,\n    textAlign: 'center',\n  },\n});\n\nexport default BarcodeScanner;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAEnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,OAAO,QAAQ,oBAAoB;AACvE,SAASC,QAAQ;AACjB,SAASC,gBAAgB,EAAEC,QAAQ;AAAsC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AASzE,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,IAAA,EAA4B;EAAA,IAAtBC,MAAM,GAAAD,IAAA,CAANC,MAAM;IAAEC,OAAO,GAAAF,IAAA,CAAPE,OAAO;EACvC,IAAAC,SAAA,GAAkBX,QAAQ,CAAC,CAAC;IAApBY,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,SAAA,GAA0CvB,QAAQ,CAAC,IAAI,CAAC;IAAAwB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAjDG,aAAa,GAAAF,UAAA;IAAEG,gBAAgB,GAAAH,UAAA;EACtC,IAAAI,UAAA,GAA8B5B,QAAQ,CAAC,KAAK,CAAC;IAAA6B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAtCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAgChC,QAAQ,CAAC,KAAK,CAAC;IAAAiC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAxCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAG5BhC,SAAS,CAAC,YAAM;IACd,IAAMmC,4BAA4B;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAC/C,IAAAC,qBAAA,SAAyBjC,cAAc,CAACkC,uBAAuB,CAAC,CAAC;UAAzDC,MAAM,GAAAF,qBAAA,CAANE,MAAM;QACdd,gBAAgB,CAACc,MAAM,KAAK,SAAS,CAAC;MACxC,CAAC;MAAA,gBAHKL,4BAA4BA,CAAA;QAAA,OAAAC,KAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;IAAA,GAGjC;IAEDP,4BAA4B,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMQ,oBAAoB;IAAA,IAAAC,KAAA,GAAAP,iBAAA,CAAG,WAAAQ,KAAA,EAA0B;MAAA,IAAjBC,IAAI,GAAAD,KAAA,CAAJC,IAAI;QAAEC,IAAI,GAAAF,KAAA,CAAJE,IAAI;MAC9C,IAAIlB,OAAO,IAAII,QAAQ,EAAE;MAEzBH,UAAU,CAAC,IAAI,CAAC;MAChBI,WAAW,CAAC,IAAI,CAAC;MAEjB,IAAI;QAEF,IAAMc,IAAI,SAAStC,gBAAgB,CAACqC,IAAI,CAAC;QAEzC,IAAIC,IAAI,EAAE;UAER9B,MAAM,CAAC8B,IAAI,CAAC;QACd,CAAC,MAAM;UAEL5C,KAAK,CAAC6C,KAAK,CACT,gBAAgB,EAChB,eAAeF,IAAI,2DAA2D,EAC9E,CACE;YACEG,IAAI,EAAE,QAAQ;YACdC,KAAK,EAAE,QAAQ;YACfC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cACbtB,UAAU,CAAC,KAAK,CAAC;cACjBI,WAAW,CAAC,KAAK,CAAC;YACpB;UACF,CAAC,EACD;YACEgB,IAAI,EAAE,KAAK;YACXE,OAAO;cAAA,IAAAC,QAAA,GAAAhB,iBAAA,CAAE,aAAY;gBAEnB,IAAMiB,OAAO,GAAG;kBACdC,IAAI,EAAE,UAAU;kBAChBC,WAAW,EAAE,EAAE;kBACfC,OAAO,EAAEV,IAAI;kBACbW,KAAK,EAAE,EAAE;kBACTC,YAAY,EAAE,GAAG;kBACjBC,YAAY,EAAE,GAAG;kBACjBC,SAAS,EAAE,CAAC;kBACZC,SAAS,EAAE,CACT;oBAAEC,WAAW,EAAE,mBAAmB;oBAAEC,MAAM,EAAE;kBAAE,CAAC,EAC/C;oBAAED,WAAW,EAAE,kBAAkB;oBAAEC,MAAM,EAAE;kBAAE,CAAC,EAC9C;oBAAED,WAAW,EAAE,gBAAgB;oBAAEC,MAAM,EAAE;kBAAE,CAAC,EAC5C;oBAAED,WAAW,EAAE,cAAc;oBAAEC,MAAM,EAAE;kBAAE,CAAC;gBAE9C,CAAC;gBAED,IAAMC,SAAS,SAAStD,QAAQ,CAAC2C,OAAO,CAAC;gBACzCpC,MAAM,CAAC+C,SAAS,CAAC;cACnB,CAAC;cAAA,SApBDb,OAAOA,CAAA;gBAAA,OAAAC,QAAA,CAAAZ,KAAA,OAAAC,SAAA;cAAA;cAAA,OAAPU,OAAO;YAAA;UAqBT,CAAC,CAEL,CAAC;QACH;MACF,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C9D,KAAK,CAAC6C,KAAK,CAAC,OAAO,EAAE,8CAA8C,CAAC;QACpEnB,UAAU,CAAC,KAAK,CAAC;QACjBI,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC;IAAA,gBA5DKS,oBAAoBA,CAAAyB,EAAA;MAAA,OAAAxB,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GA4DzB;EAGD,IAAIjB,aAAa,KAAK,IAAI,EAAE;IAC1B,OACEV,KAAA,CAACb,IAAI;MAACiD,KAAK,EAAE,CAACkB,MAAM,CAACC,SAAS,EAAE;QAAEC,eAAe,EAAElD,KAAK,CAACmD,MAAM,CAACC;MAAW,CAAC,CAAE;MAAAC,QAAA,GAC5E7D,IAAA,CAACN,iBAAiB;QAACoE,IAAI,EAAC,OAAO;QAACC,KAAK,EAAEvD,KAAK,CAACmD,MAAM,CAACK;MAAQ,CAAE,CAAC,EAC/DhE,IAAA,CAACV,IAAI;QAACgD,KAAK,EAAE,CAACkB,MAAM,CAACnB,IAAI,EAAE;UAAE0B,KAAK,EAAEvD,KAAK,CAACmD,MAAM,CAACtB;QAAK,CAAC,CAAE;QAAAwB,QAAA,EAAC;MAA+B,CAAM,CAAC;IAAA,CAC5F,CAAC;EAEX;EAEA,IAAIjD,aAAa,KAAK,KAAK,EAAE;IAC3B,OACEV,KAAA,CAACb,IAAI;MAACiD,KAAK,EAAE,CAACkB,MAAM,CAACC,SAAS,EAAE;QAAEC,eAAe,EAAElD,KAAK,CAACmD,MAAM,CAACC;MAAW,CAAC,CAAE;MAAAC,QAAA,GAC5E7D,IAAA,CAACV,IAAI;QAACgD,KAAK,EAAE,CAACkB,MAAM,CAACnB,IAAI,EAAE;UAAE0B,KAAK,EAAEvD,KAAK,CAACmD,MAAM,CAACtB;QAAK,CAAC,CAAE;QAAAwB,QAAA,EAAC;MAAmB,CAAM,CAAC,EACpF7D,IAAA,CAACP,MAAM;QAACwE,IAAI,EAAC,WAAW;QAAC1B,OAAO,EAAEjC,OAAQ;QAACgC,KAAK,EAAEkB,MAAM,CAACU,MAAO;QAAAL,QAAA,EAAC;MAEjE,CAAQ,CAAC;IAAA,CACL,CAAC;EAEX;EAEA,OACE3D,KAAA,CAACb,IAAI;IAACiD,KAAK,EAAE,CAACkB,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAElD,KAAK,CAACmD,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,GAC5E7D,IAAA,CAACR,cAAc;MACb2E,gBAAgB,EAAEnD,OAAO,GAAGoD,SAAS,GAAGtC,oBAAqB;MAC7DQ,KAAK,EAAEkB,MAAM,CAACa;IAAQ,CACvB,CAAC,EAEFnE,KAAA,CAACP,OAAO;MAAC2C,KAAK,EAAE,CAACkB,MAAM,CAACc,OAAO,EAAE;QAAEZ,eAAe,EAAElD,KAAK,CAACmD,MAAM,CAACY;MAAS,CAAC,CAAE;MAAAV,QAAA,GAC3E7D,IAAA,CAACV,IAAI;QAACgD,KAAK,EAAE,CAACkB,MAAM,CAACgB,YAAY,EAAE;UAAET,KAAK,EAAEvD,KAAK,CAACmD,MAAM,CAACtB;QAAK,CAAC,CAAE;QAAAwB,QAAA,EAAC;MAElE,CAAM,CAAC,EAENzC,QAAQ,IACPlB,KAAA,CAACb,IAAI;QAACiD,KAAK,EAAEkB,MAAM,CAACiB,gBAAiB;QAAAZ,QAAA,GACnC7D,IAAA,CAACN,iBAAiB;UAACoE,IAAI,EAAC,OAAO;UAACC,KAAK,EAAEvD,KAAK,CAACmD,MAAM,CAACK;QAAQ,CAAE,CAAC,EAC/DhE,IAAA,CAACV,IAAI;UAACgD,KAAK,EAAE,CAACkB,MAAM,CAACnB,IAAI,EAAE;YAAE0B,KAAK,EAAEvD,KAAK,CAACmD,MAAM,CAACtB;UAAK,CAAC,CAAE;UAAAwB,QAAA,EAAC;QAAa,CAAM,CAAC;MAAA,CAC1E,CACP,EAED7D,IAAA,CAACP,MAAM;QACLwE,IAAI,EAAC,WAAW;QAChB1B,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;UACbtB,UAAU,CAAC,KAAK,CAAC;UACjBX,OAAO,CAAC,CAAC;QACX,CAAE;QACFgC,KAAK,EAAEkB,MAAM,CAACU,MAAO;QACrBQ,QAAQ,EAAEtD,QAAS;QAAAyC,QAAA,EACpB;MAED,CAAQ,CAAC;IAAA,CACF,CAAC;EAAA,CACN,CAAC;AAEX,CAAC;AAED,IAAML,MAAM,GAAGpE,UAAU,CAACuF,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTmB,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDV,OAAO,EAAAW,aAAA,KACF5F,UAAU,CAAC6F,kBAAkB,CACjC;EACDX,OAAO,EAAE;IACPY,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,EAAE;IACXC,mBAAmB,EAAE,EAAE;IACvBC,oBAAoB,EAAE,EAAE;IACxBT,UAAU,EAAE;EACd,CAAC;EACDP,YAAY,EAAE;IACZiB,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDzB,MAAM,EAAE;IACN0B,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE;EACT,CAAC;EACDpB,gBAAgB,EAAE;IAChBM,UAAU,EAAE,QAAQ;IACpBe,cAAc,EAAE;EAClB,CAAC;EACDzD,IAAI,EAAE;IACJoD,QAAQ,EAAE,EAAE;IACZG,SAAS,EAAE,EAAE;IACbD,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAexF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}