{"ast": null, "code": "import AsyncStorage from \"./AsyncStorage\";\nexport { useAsyncStorage } from \"./hooks\";\nexport default AsyncStorage;", "map": {"version": 3, "names": ["AsyncStorage", "useAsyncStorage"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-async-storage/async-storage/lib/module/index.ts"], "sourcesContent": ["import AsyncStorage from './AsyncStorage';\n\nexport { useAsyncStorage } from './hooks';\n\nexport type { AsyncStorageStatic } from './types';\n\nexport default AsyncStorage;\n"], "mappings": "AAAA,OAAOA,YAAP;AAEA,SAASC,eAAT;AAIA,eAAeD,YAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}