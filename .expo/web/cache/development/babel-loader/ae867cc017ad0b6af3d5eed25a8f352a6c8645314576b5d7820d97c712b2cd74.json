{"ast": null, "code": "import StatusBar from \"react-native-web/dist/exports/StatusBar\";\nimport styleToBarStyle from \"./styleToBarStyle\";\nexport default function setStatusBarStyle(style) {\n  StatusBar.setBarStyle(styleToBarStyle(style));\n}", "map": {"version": 3, "names": ["styleToBarStyle", "setStatusBarStyle", "style", "StatusBar", "setBarStyle"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-status-bar/src/setStatusBarStyle.ts"], "sourcesContent": ["import { StatusBar } from 'react-native';\n\nimport { StatusBarStyle } from './StatusBar.types';\nimport styleToBarStyle from './styleToBarStyle';\n\n// @needsAudit\n/**\n * Set the bar style of the status bar.\n * @param style The color of the status bar text.\n */\nexport default function setStatusBarStyle(style: StatusBarStyle) {\n  StatusBar.setBarStyle(styleToBarStyle(style));\n}\n"], "mappings": ";AAGA,OAAOA,eAAe;AAOtB,eAAc,SAAUC,iBAAiBA,CAACC,KAAqB;EAC7DC,SAAS,CAACC,WAAW,CAACJ,eAAe,CAACE,KAAK,CAAC,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}