{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"../StyleSheet\";\nimport TouchableOpacity from \"../TouchableOpacity\";\nimport Text from \"../Text\";\nvar Button = React.forwardRef(function (props, forwardedRef) {\n  var accessibilityLabel = props.accessibilityLabel,\n    color = props.color,\n    disabled = props.disabled,\n    onPress = props.onPress,\n    testID = props.testID,\n    title = props.title;\n  return React.createElement(TouchableOpacity, {\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"button\",\n    disabled: disabled,\n    focusable: !disabled,\n    onPress: onPress,\n    ref: forwardedRef,\n    style: [styles.button, color && {\n      backgroundColor: color\n    }, disabled && styles.buttonDisabled],\n    testID: testID\n  }, React.createElement(Text, {\n    style: [styles.text, disabled && styles.textDisabled]\n  }, title));\n});\nButton.displayName = 'Button';\nvar styles = StyleSheet.create({\n  button: {\n    backgroundColor: '#2196F3',\n    borderRadius: 2\n  },\n  text: {\n    color: '#fff',\n    fontWeight: '500',\n    padding: 8,\n    textAlign: 'center',\n    textTransform: 'uppercase'\n  },\n  buttonDisabled: {\n    backgroundColor: '#dfdfdf'\n  },\n  textDisabled: {\n    color: '#a1a1a1'\n  }\n});\nexport default Button;", "map": {"version": 3, "names": ["React", "StyleSheet", "TouchableOpacity", "Text", "<PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "accessibilityLabel", "color", "disabled", "onPress", "testID", "title", "createElement", "accessibilityRole", "focusable", "ref", "style", "styles", "button", "backgroundColor", "buttonDisabled", "text", "textDisabled", "displayName", "create", "borderRadius", "fontWeight", "padding", "textAlign", "textTransform"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Button/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport StyleSheet from '../StyleSheet';\nimport TouchableOpacity from '../TouchableOpacity';\nimport Text from '../Text';\n//import { warnOnce } from '../../modules/warnOnce';\n\nvar Button = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  // warnOnce('Button', 'Button is deprecated. Please use Pressable.');\n\n  var accessibilityLabel = props.accessibilityLabel,\n    color = props.color,\n    disabled = props.disabled,\n    onPress = props.onPress,\n    testID = props.testID,\n    title = props.title;\n  return /*#__PURE__*/React.createElement(TouchableOpacity, {\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"button\",\n    disabled: disabled,\n    focusable: !disabled,\n    onPress: onPress,\n    ref: forwardedRef,\n    style: [styles.button, color && {\n      backgroundColor: color\n    }, disabled && styles.buttonDisabled],\n    testID: testID\n  }, /*#__PURE__*/React.createElement(Text, {\n    style: [styles.text, disabled && styles.textDisabled]\n  }, title));\n});\nButton.displayName = 'Button';\nvar styles = StyleSheet.create({\n  button: {\n    backgroundColor: '#2196F3',\n    borderRadius: 2\n  },\n  text: {\n    color: '#fff',\n    fontWeight: '500',\n    padding: 8,\n    textAlign: 'center',\n    textTransform: 'uppercase'\n  },\n  buttonDisabled: {\n    backgroundColor: '#dfdfdf'\n  },\n  textDisabled: {\n    color: '#a1a1a1'\n  }\n});\nexport default Button;"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,gBAAgB;AACvB,OAAOC,IAAI;AAGX,IAAIC,MAAM,GAAgBJ,KAAK,CAACK,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAGlE,IAAIC,kBAAkB,GAAGF,KAAK,CAACE,kBAAkB;IAC/CC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,OAAO,GAAGL,KAAK,CAACK,OAAO;IACvBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,KAAK,GAAGP,KAAK,CAACO,KAAK;EACrB,OAAoBb,KAAK,CAACc,aAAa,CAACZ,gBAAgB,EAAE;IACxDM,kBAAkB,EAAEA,kBAAkB;IACtCO,iBAAiB,EAAE,QAAQ;IAC3BL,QAAQ,EAAEA,QAAQ;IAClBM,SAAS,EAAE,CAACN,QAAQ;IACpBC,OAAO,EAAEA,OAAO;IAChBM,GAAG,EAAEV,YAAY;IACjBW,KAAK,EAAE,CAACC,MAAM,CAACC,MAAM,EAAEX,KAAK,IAAI;MAC9BY,eAAe,EAAEZ;IACnB,CAAC,EAAEC,QAAQ,IAAIS,MAAM,CAACG,cAAc,CAAC;IACrCV,MAAM,EAAEA;EACV,CAAC,EAAeZ,KAAK,CAACc,aAAa,CAACX,IAAI,EAAE;IACxCe,KAAK,EAAE,CAACC,MAAM,CAACI,IAAI,EAAEb,QAAQ,IAAIS,MAAM,CAACK,YAAY;EACtD,CAAC,EAAEX,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFT,MAAM,CAACqB,WAAW,GAAG,QAAQ;AAC7B,IAAIN,MAAM,GAAGlB,UAAU,CAACyB,MAAM,CAAC;EAC7BN,MAAM,EAAE;IACNC,eAAe,EAAE,SAAS;IAC1BM,YAAY,EAAE;EAChB,CAAC;EACDJ,IAAI,EAAE;IACJd,KAAK,EAAE,MAAM;IACbmB,UAAU,EAAE,KAAK;IACjBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE;EACjB,CAAC;EACDT,cAAc,EAAE;IACdD,eAAe,EAAE;EACnB,CAAC;EACDG,YAAY,EAAE;IACZf,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AACF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}