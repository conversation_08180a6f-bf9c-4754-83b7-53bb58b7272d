{"ast": null, "code": "import AsyncStorage from \"./AsyncStorage\";\nexport function useAsyncStorage(key) {\n  return {\n    getItem: function getItem() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return AsyncStorage.getItem.apply(AsyncStorage, [key].concat(args));\n    },\n    setItem: function setItem() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return AsyncStorage.setItem.apply(AsyncStorage, [key].concat(args));\n    },\n    mergeItem: function mergeItem() {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      return AsyncStorage.mergeItem.apply(AsyncStorage, [key].concat(args));\n    },\n    removeItem: function removeItem() {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      return AsyncStorage.removeItem.apply(AsyncStorage, [key].concat(args));\n    }\n  };\n}", "map": {"version": 3, "names": ["AsyncStorage", "useAsyncStorage", "key", "getItem", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "setItem", "_len2", "_key2", "mergeItem", "_len3", "_key3", "removeItem", "_len4", "_key4"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-native-async-storage/async-storage/lib/module/hooks.ts"], "sourcesContent": ["import AsyncStorage from './AsyncStorage';\nimport type { AsyncStorageHook } from './types';\n\nexport function useAsyncStorage(key: string): AsyncStorageHook {\n  return {\n    getItem: (...args) => AsyncStorage.getItem(key, ...args),\n    setItem: (...args) => AsyncStorage.setItem(key, ...args),\n    mergeItem: (...args) => AsyncStorage.mergeItem(key, ...args),\n    removeItem: (...args) => AsyncStorage.removeItem(key, ...args),\n  };\n}\n"], "mappings": "AAAA,OAAOA,YAAP;AAGA,OAAO,SAASC,eAATA,CAAyBC,GAAzB,EAAwD;EAC7D,OAAO;IACLC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAE;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAIC,IAAJ,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAIF,IAAJ,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAAA,OAAaT,YAAY,CAACG,OAAb,CAAAO,KAAA,CAAAV,YAAY,GAASE,GAArB,EAAAS,MAAA,CAA6BJ,IAA7B,EAAb;IAAA,CADJ;IAELK,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAE;MAAA,SAAAC,KAAA,GAAAR,SAAA,CAAAC,MAAA,EAAIC,IAAJ,OAAAC,KAAA,CAAAK,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAIP,IAAJ,CAAAO,KAAA,IAAAT,SAAA,CAAAS,KAAA;MAAA;MAAA,OAAad,YAAY,CAACY,OAAb,CAAAF,KAAA,CAAAV,YAAY,GAASE,GAArB,EAAAS,MAAA,CAA6BJ,IAA7B,EAAb;IAAA,CAFJ;IAGLQ,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAE;MAAA,SAAAC,KAAA,GAAAX,SAAA,CAAAC,MAAA,EAAIC,IAAJ,OAAAC,KAAA,CAAAQ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAIV,IAAJ,CAAAU,KAAA,IAAAZ,SAAA,CAAAY,KAAA;MAAA;MAAA,OAAajB,YAAY,CAACe,SAAb,CAAAL,KAAA,CAAAV,YAAY,GAAWE,GAAvB,EAAAS,MAAA,CAA+BJ,IAA/B,EAAb;IAAA,CAHN;IAILW,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAE;MAAA,SAAAC,KAAA,GAAAd,SAAA,CAAAC,MAAA,EAAIC,IAAJ,OAAAC,KAAA,CAAAW,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAIb,IAAJ,CAAAa,KAAA,IAAAf,SAAA,CAAAe,KAAA;MAAA;MAAA,OAAapB,YAAY,CAACkB,UAAb,CAAAR,KAAA,CAAAV,YAAY,GAAYE,GAAxB,EAAAS,MAAA,CAAgCJ,IAAhC,EAAb;IAAA;EAJP,CAAP;AAMD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}