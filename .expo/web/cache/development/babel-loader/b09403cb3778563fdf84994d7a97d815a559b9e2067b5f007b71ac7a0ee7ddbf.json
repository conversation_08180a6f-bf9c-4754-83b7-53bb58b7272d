{"ast": null, "code": "import dismissKeyboard from \"../../modules/dismissKeyboard\";\nvar Keyboard = {\n  isVisible: function isVisible() {\n    return false;\n  },\n  addListener: function addListener() {\n    return {\n      remove: function remove() {}\n    };\n  },\n  dismiss: function dismiss() {\n    dismissKeyboard();\n  },\n  removeAllListeners: function removeAllListeners() {},\n  removeListener: function removeListener() {}\n};\nexport default Keyboard;", "map": {"version": 3, "names": ["dismissKeyboard", "Keyboard", "isVisible", "addListener", "remove", "dismiss", "removeAllListeners", "removeListener"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/Keyboard/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport dismissKeyboard from '../../modules/dismissKeyboard';\n\n// in the future we can use https://github.com/w3c/virtual-keyboard\nvar Keyboard = {\n  isVisible() {\n    return false;\n  },\n  addListener() {\n    return {\n      remove: () => {}\n    };\n  },\n  dismiss() {\n    dismissKeyboard();\n  },\n  removeAllListeners() {},\n  removeListener() {}\n};\nexport default Keyboard;"], "mappings": "AAUA,OAAOA,eAAe;AAGtB,IAAIC,QAAQ,GAAG;EACbC,SAAS,WAATA,SAASA,CAAA,EAAG;IACV,OAAO,KAAK;EACd,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZ,OAAO;MACLC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ,CAAC;IACjB,CAAC;EACH,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACRL,eAAe,CAAC,CAAC;EACnB,CAAC;EACDM,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG,CAAC,CAAC;EACvBC,cAAc,WAAdA,cAAcA,CAAA,EAAG,CAAC;AACpB,CAAC;AACD,eAAeN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}