{"ast": null, "code": "import * as React from 'react';\nimport useNavigation from \"./useNavigation\";\nexport default function useFocusEffect(effect) {\n  var navigation = useNavigation();\n  if (arguments[1] !== undefined) {\n    var message = \"You passed a second argument to 'useFocusEffect', but it only accepts one argument. \" + \"If you want to pass a dependency array, you can use 'React.useCallback':\\n\\n\" + 'useFocusEffect(\\n' + '  React.useCallback(() => {\\n' + '    // Your code here\\n' + '  }, [depA, depB])\\n' + ');\\n\\n' + 'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n    console.error(message);\n  }\n  React.useEffect(function () {\n    var isFocused = false;\n    var cleanup;\n    var callback = function callback() {\n      var destroy = effect();\n      if (destroy === undefined || typeof destroy === 'function') {\n        return destroy;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        var _message = 'An effect function must not return anything besides a function, which is used for clean-up.';\n        if (destroy === null) {\n          _message += \" You returned 'null'. If your effect does not require clean-up, return 'undefined' (or nothing).\";\n        } else if (typeof destroy.then === 'function') {\n          _message += \"\\n\\nIt looks like you wrote 'useFocusEffect(async () => ...)' or returned a Promise. \" + 'Instead, write the async function inside your effect ' + 'and call it immediately:\\n\\n' + 'useFocusEffect(\\n' + '  React.useCallback(() => {\\n' + '    async function fetchData() {\\n' + '      // You can await here\\n' + '      const response = await MyAPI.getData(someId);\\n' + '      // ...\\n' + '    }\\n\\n' + '    fetchData();\\n' + '  }, [someId])\\n' + ');\\n\\n' + 'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n        } else {\n          _message += ` You returned '${JSON.stringify(destroy)}'.`;\n        }\n        console.error(_message);\n      }\n    };\n    if (navigation.isFocused()) {\n      cleanup = callback();\n      isFocused = true;\n    }\n    var unsubscribeFocus = navigation.addListener('focus', function () {\n      if (isFocused) {\n        return;\n      }\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n      cleanup = callback();\n      isFocused = true;\n    });\n    var unsubscribeBlur = navigation.addListener('blur', function () {\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n      cleanup = undefined;\n      isFocused = false;\n    });\n    return function () {\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n      unsubscribeFocus();\n      unsubscribeBlur();\n    };\n  }, [effect, navigation]);\n}", "map": {"version": 3, "names": ["React", "useNavigation", "useFocusEffect", "effect", "navigation", "arguments", "undefined", "message", "console", "error", "useEffect", "isFocused", "cleanup", "callback", "destroy", "process", "env", "NODE_ENV", "then", "JSON", "stringify", "unsubscribeFocus", "addListener", "unsubscribeBlur"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/@react-navigation/core/src/useFocusEffect.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport useNavigation from './useNavigation';\n\ntype EffectCallback = () => undefined | void | (() => void);\n\n/**\n * Hook to run an effect in a focused screen, similar to `React.useEffect`.\n * This can be used to perform side-effects such as fetching data or subscribing to events.\n * The passed callback should be wrapped in `React.useCallback` to avoid running the effect too often.\n *\n * @param callback Memoized callback containing the effect, should optionally return a cleanup function.\n */\nexport default function useFocusEffect(effect: EffectCallback) {\n  const navigation = useNavigation();\n\n  if (arguments[1] !== undefined) {\n    const message =\n      \"You passed a second argument to 'useFocusEffect', but it only accepts one argument. \" +\n      \"If you want to pass a dependency array, you can use 'React.useCallback':\\n\\n\" +\n      'useFocusEffect(\\n' +\n      '  React.useCallback(() => {\\n' +\n      '    // Your code here\\n' +\n      '  }, [depA, depB])\\n' +\n      ');\\n\\n' +\n      'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n\n    console.error(message);\n  }\n\n  React.useEffect(() => {\n    let isFocused = false;\n    let cleanup: undefined | void | (() => void);\n\n    const callback = () => {\n      const destroy = effect();\n\n      if (destroy === undefined || typeof destroy === 'function') {\n        return destroy;\n      }\n\n      if (process.env.NODE_ENV !== 'production') {\n        let message =\n          'An effect function must not return anything besides a function, which is used for clean-up.';\n\n        if (destroy === null) {\n          message +=\n            \" You returned 'null'. If your effect does not require clean-up, return 'undefined' (or nothing).\";\n        } else if (typeof (destroy as any).then === 'function') {\n          message +=\n            \"\\n\\nIt looks like you wrote 'useFocusEffect(async () => ...)' or returned a Promise. \" +\n            'Instead, write the async function inside your effect ' +\n            'and call it immediately:\\n\\n' +\n            'useFocusEffect(\\n' +\n            '  React.useCallback(() => {\\n' +\n            '    async function fetchData() {\\n' +\n            '      // You can await here\\n' +\n            '      const response = await MyAPI.getData(someId);\\n' +\n            '      // ...\\n' +\n            '    }\\n\\n' +\n            '    fetchData();\\n' +\n            '  }, [someId])\\n' +\n            ');\\n\\n' +\n            'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n        } else {\n          message += ` You returned '${JSON.stringify(destroy)}'.`;\n        }\n\n        console.error(message);\n      }\n    };\n\n    // We need to run the effect on intial render/dep changes if the screen is focused\n    if (navigation.isFocused()) {\n      cleanup = callback();\n      isFocused = true;\n    }\n\n    const unsubscribeFocus = navigation.addListener('focus', () => {\n      // If callback was already called for focus, avoid calling it again\n      // The focus event may also fire on intial render, so we guard against runing the effect twice\n      if (isFocused) {\n        return;\n      }\n\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n\n      cleanup = callback();\n      isFocused = true;\n    });\n\n    const unsubscribeBlur = navigation.addListener('blur', () => {\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n\n      cleanup = undefined;\n      isFocused = false;\n    });\n\n    return () => {\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n\n      unsubscribeFocus();\n      unsubscribeBlur();\n    };\n  }, [effect, navigation]);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,aAAa;AAWpB,eAAe,SAASC,cAAcA,CAACC,MAAsB,EAAE;EAC7D,IAAMC,UAAU,GAAGH,aAAa,EAAE;EAElC,IAAII,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IAC9B,IAAMC,OAAO,GACX,sFAAsF,GACtF,8EAA8E,GAC9E,mBAAmB,GACnB,+BAA+B,GAC/B,yBAAyB,GACzB,sBAAsB,GACtB,QAAQ,GACR,oEAAoE;IAEtEC,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;EACxB;EAEAP,KAAK,CAACU,SAAS,CAAC,YAAM;IACpB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,OAAwC;IAE5C,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAMC,OAAO,GAAGX,MAAM,EAAE;MAExB,IAAIW,OAAO,KAAKR,SAAS,IAAI,OAAOQ,OAAO,KAAK,UAAU,EAAE;QAC1D,OAAOA,OAAO;MAChB;MAEA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIV,QAAO,GACT,6FAA6F;QAE/F,IAAIO,OAAO,KAAK,IAAI,EAAE;UACpBP,QAAO,IACL,kGAAkG;QACtG,CAAC,MAAM,IAAI,OAAQO,OAAO,CAASI,IAAI,KAAK,UAAU,EAAE;UACtDX,QAAO,IACL,uFAAuF,GACvF,uDAAuD,GACvD,8BAA8B,GAC9B,mBAAmB,GACnB,+BAA+B,GAC/B,oCAAoC,GACpC,+BAA+B,GAC/B,uDAAuD,GACvD,gBAAgB,GAChB,WAAW,GACX,oBAAoB,GACpB,kBAAkB,GAClB,QAAQ,GACR,oEAAoE;QACxE,CAAC,MAAM;UACLA,QAAO,IAAK,kBAAiBY,IAAI,CAACC,SAAS,CAACN,OAAO,CAAE,IAAG;QAC1D;QAEAN,OAAO,CAACC,KAAK,CAACF,QAAO,CAAC;MACxB;IACF,CAAC;IAGD,IAAIH,UAAU,CAACO,SAAS,EAAE,EAAE;MAC1BC,OAAO,GAAGC,QAAQ,EAAE;MACpBF,SAAS,GAAG,IAAI;IAClB;IAEA,IAAMU,gBAAgB,GAAGjB,UAAU,CAACkB,WAAW,CAAC,OAAO,EAAE,YAAM;MAG7D,IAAIX,SAAS,EAAE;QACb;MACF;MAEA,IAAIC,OAAO,KAAKN,SAAS,EAAE;QACzBM,OAAO,EAAE;MACX;MAEAA,OAAO,GAAGC,QAAQ,EAAE;MACpBF,SAAS,GAAG,IAAI;IAClB,CAAC,CAAC;IAEF,IAAMY,eAAe,GAAGnB,UAAU,CAACkB,WAAW,CAAC,MAAM,EAAE,YAAM;MAC3D,IAAIV,OAAO,KAAKN,SAAS,EAAE;QACzBM,OAAO,EAAE;MACX;MAEAA,OAAO,GAAGN,SAAS;MACnBK,SAAS,GAAG,KAAK;IACnB,CAAC,CAAC;IAEF,OAAO,YAAM;MACX,IAAIC,OAAO,KAAKN,SAAS,EAAE;QACzBM,OAAO,EAAE;MACX;MAEAS,gBAAgB,EAAE;MAClBE,eAAe,EAAE;IACnB,CAAC;EACH,CAAC,EAAE,CAACpB,MAAM,EAAEC,UAAU,CAAC,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}