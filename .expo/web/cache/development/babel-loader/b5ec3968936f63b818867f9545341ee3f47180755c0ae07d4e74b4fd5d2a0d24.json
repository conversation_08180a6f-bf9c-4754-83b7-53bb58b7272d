{"ast": null, "code": "import StatusBar from \"react-native-web/dist/exports/StatusBar\";\nexport default function setStatusBarBackgroundColor(backgroundColor, animated) {\n  StatusBar.setBackgroundColor(backgroundColor, animated);\n}", "map": {"version": 3, "names": ["setStatusBarBackgroundColor", "backgroundColor", "animated", "StatusBar", "setBackgroundColor"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/expo-status-bar/src/setStatusBarBackgroundColor.ts"], "sourcesContent": ["import { StatusBar } from 'react-native';\n\n// @needsAudit\n/**\n * Set the background color of the status bar.\n * @param backgroundColor The background color of the status bar.\n * @param animated `true` to animate the background color change, `false` to change immediately.\n * @platform android\n */\nexport default function setStatusBarBackgroundColor(backgroundColor: string, animated: boolean) {\n  StatusBar.setBackgroundColor(backgroundColor, animated);\n}\n"], "mappings": ";AASA,eAAc,SAAUA,2BAA2BA,CAACC,eAAuB,EAAEC,QAAiB;EAC5FC,SAAS,CAACC,kBAAkB,CAACH,eAAe,EAAEC,QAAQ,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}