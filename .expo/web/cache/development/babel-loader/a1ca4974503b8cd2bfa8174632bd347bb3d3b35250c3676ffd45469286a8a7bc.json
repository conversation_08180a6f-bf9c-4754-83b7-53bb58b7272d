{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"index\", \"total\", \"siblings\", \"style\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nvar CardContent = function CardContent(_ref) {\n  var index = _ref.index,\n    total = _ref.total,\n    siblings = _ref.siblings,\n    style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var cover = 'withInternalTheme(CardCover)';\n  var title = 'withInternalTheme(CardTitle)';\n  var contentStyle, prev, next;\n  if (typeof index === 'number' && siblings) {\n    prev = siblings[index - 1];\n    next = siblings[index + 1];\n  }\n  if (prev === cover && next === cover || prev === title && next === title || total === 1) {\n    contentStyle = styles.only;\n  } else if (index === 0) {\n    if (next === cover || next === title) {\n      contentStyle = styles.only;\n    } else {\n      contentStyle = styles.first;\n    }\n  } else if (typeof total === 'number' && index === total - 1) {\n    if (prev === cover || prev === title) {\n      contentStyle = styles.only;\n    } else {\n      contentStyle = styles.last;\n    }\n  } else if (prev === cover || prev === title) {\n    contentStyle = styles.first;\n  } else if (next === cover || next === title) {\n    contentStyle = styles.last;\n  }\n  return React.createElement(View, _extends({}, rest, {\n    style: [styles.container, contentStyle, style]\n  }));\n};\nCardContent.displayName = 'Card.Content';\nvar styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: 16\n  },\n  first: {\n    paddingTop: 16\n  },\n  last: {\n    paddingBottom: 16\n  },\n  only: {\n    paddingVertical: 16\n  }\n});\nexport default CardContent;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "index", "total", "siblings", "style", "rest", "_objectWithoutProperties", "_excluded", "cover", "title", "contentStyle", "prev", "next", "styles", "only", "first", "last", "createElement", "_extends", "container", "displayName", "create", "paddingHorizontal", "paddingTop", "paddingBottom", "paddingVertical"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Card/CardContent.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleSheet, StyleProp, View, ViewStyle } from 'react-native';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Items inside the `Card.Content`.\n   */\n  children: React.ReactNode;\n  /**\n   * @internal\n   */\n  index?: number;\n  /**\n   * @internal\n   */\n  total?: number;\n  /**\n   * @internal\n   */\n  siblings?: Array<string>;\n  style?: StyleProp<ViewStyle>;\n};\n\n/**\n * A component to show content inside a Card.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Card, Text } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Card>\n *     <Card.Content>\n *       <Text variant=\"titleLarge\">Card title</Text>\n *       <Text variant=\"bodyMedium\">Card content</Text>\n *     </Card.Content>\n *   </Card>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst CardContent = ({ index, total, siblings, style, ...rest }: Props) => {\n  const cover = 'withInternalTheme(CardCover)';\n  const title = 'withInternalTheme(CardTitle)';\n\n  let contentStyle, prev, next;\n\n  if (typeof index === 'number' && siblings) {\n    prev = siblings[index - 1];\n    next = siblings[index + 1];\n  }\n\n  if (\n    (prev === cover && next === cover) ||\n    (prev === title && next === title) ||\n    total === 1\n  ) {\n    contentStyle = styles.only;\n  } else if (index === 0) {\n    if (next === cover || next === title) {\n      contentStyle = styles.only;\n    } else {\n      contentStyle = styles.first;\n    }\n  } else if (typeof total === 'number' && index === total - 1) {\n    if (prev === cover || prev === title) {\n      contentStyle = styles.only;\n    } else {\n      contentStyle = styles.last;\n    }\n  } else if (prev === cover || prev === title) {\n    contentStyle = styles.first;\n  } else if (next === cover || next === title) {\n    contentStyle = styles.last;\n  }\n\n  return <View {...rest} style={[styles.container, contentStyle, style]} />;\n};\n\nCardContent.displayName = 'Card.Content';\n\nconst styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: 16,\n  },\n  first: {\n    paddingTop: 16,\n  },\n  last: {\n    paddingBottom: 16,\n  },\n  only: {\n    paddingVertical: 16,\n  },\n});\n\nexport default CardContent;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AA2C9B,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAGC,IAAA,EAAuD;EAAA,IAApDC,KAAK,GAA0CD,IAAA,CAA/CC,KAAK;IAAEC,KAAK,GAAmCF,IAAA,CAAxCE,KAAK;IAAEC,QAAQ,GAAyBH,IAAA,CAAjCG,QAAQ;IAAEC,KAAK,GAAkBJ,IAAA,CAAvBI,KAAK;IAAKC,IAAA,GAAAC,wBAAA,CAAaN,IAAA,EAAAO,SAAA;EACpE,IAAMC,KAAK,GAAG,8BAA8B;EAC5C,IAAMC,KAAK,GAAG,8BAA8B;EAE5C,IAAIC,YAAY,EAAEC,IAAI,EAAEC,IAAI;EAE5B,IAAI,OAAOX,KAAK,KAAK,QAAQ,IAAIE,QAAQ,EAAE;IACzCQ,IAAI,GAAGR,QAAQ,CAACF,KAAK,GAAG,CAAC,CAAC;IAC1BW,IAAI,GAAGT,QAAQ,CAACF,KAAK,GAAG,CAAC,CAAC;EAC5B;EAEA,IACGU,IAAI,KAAKH,KAAK,IAAII,IAAI,KAAKJ,KAAK,IAChCG,IAAI,KAAKF,KAAK,IAAIG,IAAI,KAAKH,KAAM,IAClCP,KAAK,KAAK,CAAC,EACX;IACAQ,YAAY,GAAGG,MAAM,CAACC,IAAI;EAC5B,CAAC,MAAM,IAAIb,KAAK,KAAK,CAAC,EAAE;IACtB,IAAIW,IAAI,KAAKJ,KAAK,IAAII,IAAI,KAAKH,KAAK,EAAE;MACpCC,YAAY,GAAGG,MAAM,CAACC,IAAI;IAC5B,CAAC,MAAM;MACLJ,YAAY,GAAGG,MAAM,CAACE,KAAK;IAC7B;EACF,CAAC,MAAM,IAAI,OAAOb,KAAK,KAAK,QAAQ,IAAID,KAAK,KAAKC,KAAK,GAAG,CAAC,EAAE;IAC3D,IAAIS,IAAI,KAAKH,KAAK,IAAIG,IAAI,KAAKF,KAAK,EAAE;MACpCC,YAAY,GAAGG,MAAM,CAACC,IAAI;IAC5B,CAAC,MAAM;MACLJ,YAAY,GAAGG,MAAM,CAACG,IAAI;IAC5B;EACF,CAAC,MAAM,IAAIL,IAAI,KAAKH,KAAK,IAAIG,IAAI,KAAKF,KAAK,EAAE;IAC3CC,YAAY,GAAGG,MAAM,CAACE,KAAK;EAC7B,CAAC,MAAM,IAAIH,IAAI,KAAKJ,KAAK,IAAII,IAAI,KAAKH,KAAK,EAAE;IAC3CC,YAAY,GAAGG,MAAM,CAACG,IAAI;EAC5B;EAEA,OAAOpB,KAAA,CAAAqB,aAAA,CAACnB,IAAI,EAAAoB,QAAA,KAAKb,IAAI;IAAED,KAAK,EAAE,CAACS,MAAM,CAACM,SAAS,EAAET,YAAY,EAAEN,KAAK;EAAE,EAAE,CAAC;AAC3E,CAAC;AAEDL,WAAW,CAACqB,WAAW,GAAG,cAAc;AAExC,IAAMP,MAAM,GAAGhB,UAAU,CAACwB,MAAM,CAAC;EAC/BF,SAAS,EAAE;IACTG,iBAAiB,EAAE;EACrB,CAAC;EACDP,KAAK,EAAE;IACLQ,UAAU,EAAE;EACd,CAAC;EACDP,IAAI,EAAE;IACJQ,aAAa,EAAE;EACjB,CAAC;EACDV,IAAI,EAAE;IACJW,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AAEF,eAAe1B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}