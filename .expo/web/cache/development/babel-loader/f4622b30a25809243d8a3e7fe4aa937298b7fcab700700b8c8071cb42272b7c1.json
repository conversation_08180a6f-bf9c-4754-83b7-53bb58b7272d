{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nexport default function useLayout() {\n  var _React$useState = React.useState({\n      height: 0,\n      width: 0,\n      measured: false\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    layout = _React$useState2[0],\n    setLayout = _React$useState2[1];\n  var onLayout = React.useCallback(function (e) {\n    var _e$nativeEvent$layout = e.nativeEvent.layout,\n      height = _e$nativeEvent$layout.height,\n      width = _e$nativeEvent$layout.width;\n    if (height === layout.height && width === layout.width) {\n      return;\n    }\n    setLayout({\n      height: height,\n      width: width,\n      measured: true\n    });\n  }, [layout.height, layout.width]);\n  return [layout, onLayout];\n}", "map": {"version": 3, "names": ["React", "useLayout", "_React$useState", "useState", "height", "width", "measured", "_React$useState2", "_slicedToArray", "layout", "setLayout", "onLayout", "useCallback", "e", "_e$nativeEvent$layout", "nativeEvent"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/utils/useLayout.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type { LayoutChangeEvent } from 'react-native';\n\nexport default function useLayout() {\n  const [layout, setLayout] = React.useState<{\n    height: number;\n    width: number;\n    measured: boolean;\n  }>({ height: 0, width: 0, measured: false });\n\n  const onLayout = React.useCallback(\n    (e: LayoutChangeEvent) => {\n      const { height, width } = e.nativeEvent.layout;\n\n      if (height === layout.height && width === layout.width) {\n        return;\n      }\n\n      setLayout({\n        height,\n        width,\n        measured: true,\n      });\n    },\n    [layout.height, layout.width]\n  );\n\n  return [layout, onLayout] as const;\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,IAAAC,eAAA,GAA4BF,KAAK,CAACG,QAAQ,CAIvC;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAN,eAAA;IAJrCO,MAAM,GAAAF,gBAAA;IAAEG,SAAS,GAAAH,gBAAA;EAMxB,IAAMI,QAAQ,GAAGX,KAAK,CAACY,WAAW,CAC/B,UAAAC,CAAoB,EAAK;IACxB,IAAAC,qBAAA,GAA0BD,CAAC,CAACE,WAAW,CAACN,MAAM;MAAtCL,MAAM,GAAAU,qBAAA,CAANV,MAAM;MAAEC,KAAA,GAAAS,qBAAA,CAAAT,KAAA;IAEhB,IAAID,MAAM,KAAKK,MAAM,CAACL,MAAM,IAAIC,KAAK,KAAKI,MAAM,CAACJ,KAAK,EAAE;MACtD;IACF;IAEAK,SAAS,CAAC;MACRN,MAAM,EAANA,MAAM;MACNC,KAAK,EAALA,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EACD,CAACG,MAAM,CAACL,MAAM,EAAEK,MAAM,CAACJ,KAAK,CAC9B,CAAC;EAED,OAAO,CAACI,MAAM,EAAEE,QAAQ,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}