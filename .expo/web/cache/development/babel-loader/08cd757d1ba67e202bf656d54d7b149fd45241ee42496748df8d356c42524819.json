{"ast": null, "code": "var cssKeywords = require('color-name');\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n  if (cssKeywords.hasOwnProperty(key)) {\n    reverseKeywords[cssKeywords[key]] = key;\n  }\n}\nvar convert = module.exports = {\n  rgb: {\n    channels: 3,\n    labels: 'rgb'\n  },\n  hsl: {\n    channels: 3,\n    labels: 'hsl'\n  },\n  hsv: {\n    channels: 3,\n    labels: 'hsv'\n  },\n  hwb: {\n    channels: 3,\n    labels: 'hwb'\n  },\n  cmyk: {\n    channels: 4,\n    labels: 'cmyk'\n  },\n  xyz: {\n    channels: 3,\n    labels: 'xyz'\n  },\n  lab: {\n    channels: 3,\n    labels: 'lab'\n  },\n  lch: {\n    channels: 3,\n    labels: 'lch'\n  },\n  hex: {\n    channels: 1,\n    labels: ['hex']\n  },\n  keyword: {\n    channels: 1,\n    labels: ['keyword']\n  },\n  ansi16: {\n    channels: 1,\n    labels: ['ansi16']\n  },\n  ansi256: {\n    channels: 1,\n    labels: ['ansi256']\n  },\n  hcg: {\n    channels: 3,\n    labels: ['h', 'c', 'g']\n  },\n  apple: {\n    channels: 3,\n    labels: ['r16', 'g16', 'b16']\n  },\n  gray: {\n    channels: 1,\n    labels: ['gray']\n  }\n};\nfor (var model in convert) {\n  if (convert.hasOwnProperty(model)) {\n    if (!('channels' in convert[model])) {\n      throw new Error('missing channels property: ' + model);\n    }\n    if (!('labels' in convert[model])) {\n      throw new Error('missing channel labels property: ' + model);\n    }\n    if (convert[model].labels.length !== convert[model].channels) {\n      throw new Error('channel and label counts mismatch: ' + model);\n    }\n    var channels = convert[model].channels;\n    var labels = convert[model].labels;\n    delete convert[model].channels;\n    delete convert[model].labels;\n    Object.defineProperty(convert[model], 'channels', {\n      value: channels\n    });\n    Object.defineProperty(convert[model], 'labels', {\n      value: labels\n    });\n  }\n}\nconvert.rgb.hsl = function (rgb) {\n  var r = rgb[0] / 255;\n  var g = rgb[1] / 255;\n  var b = rgb[2] / 255;\n  var min = Math.min(r, g, b);\n  var max = Math.max(r, g, b);\n  var delta = max - min;\n  var h;\n  var s;\n  var l;\n  if (max === min) {\n    h = 0;\n  } else if (r === max) {\n    h = (g - b) / delta;\n  } else if (g === max) {\n    h = 2 + (b - r) / delta;\n  } else if (b === max) {\n    h = 4 + (r - g) / delta;\n  }\n  h = Math.min(h * 60, 360);\n  if (h < 0) {\n    h += 360;\n  }\n  l = (min + max) / 2;\n  if (max === min) {\n    s = 0;\n  } else if (l <= 0.5) {\n    s = delta / (max + min);\n  } else {\n    s = delta / (2 - max - min);\n  }\n  return [h, s * 100, l * 100];\n};\nconvert.rgb.hsv = function (rgb) {\n  var rdif;\n  var gdif;\n  var bdif;\n  var h;\n  var s;\n  var r = rgb[0] / 255;\n  var g = rgb[1] / 255;\n  var b = rgb[2] / 255;\n  var v = Math.max(r, g, b);\n  var diff = v - Math.min(r, g, b);\n  var diffc = function diffc(c) {\n    return (v - c) / 6 / diff + 1 / 2;\n  };\n  if (diff === 0) {\n    h = s = 0;\n  } else {\n    s = diff / v;\n    rdif = diffc(r);\n    gdif = diffc(g);\n    bdif = diffc(b);\n    if (r === v) {\n      h = bdif - gdif;\n    } else if (g === v) {\n      h = 1 / 3 + rdif - bdif;\n    } else if (b === v) {\n      h = 2 / 3 + gdif - rdif;\n    }\n    if (h < 0) {\n      h += 1;\n    } else if (h > 1) {\n      h -= 1;\n    }\n  }\n  return [h * 360, s * 100, v * 100];\n};\nconvert.rgb.hwb = function (rgb) {\n  var r = rgb[0];\n  var g = rgb[1];\n  var b = rgb[2];\n  var h = convert.rgb.hsl(rgb)[0];\n  var w = 1 / 255 * Math.min(r, Math.min(g, b));\n  b = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n  return [h, w * 100, b * 100];\n};\nconvert.rgb.cmyk = function (rgb) {\n  var r = rgb[0] / 255;\n  var g = rgb[1] / 255;\n  var b = rgb[2] / 255;\n  var c;\n  var m;\n  var y;\n  var k;\n  k = Math.min(1 - r, 1 - g, 1 - b);\n  c = (1 - r - k) / (1 - k) || 0;\n  m = (1 - g - k) / (1 - k) || 0;\n  y = (1 - b - k) / (1 - k) || 0;\n  return [c * 100, m * 100, y * 100, k * 100];\n};\nfunction comparativeDistance(x, y) {\n  return Math.pow(x[0] - y[0], 2) + Math.pow(x[1] - y[1], 2) + Math.pow(x[2] - y[2], 2);\n}\nconvert.rgb.keyword = function (rgb) {\n  var reversed = reverseKeywords[rgb];\n  if (reversed) {\n    return reversed;\n  }\n  var currentClosestDistance = Infinity;\n  var currentClosestKeyword;\n  for (var keyword in cssKeywords) {\n    if (cssKeywords.hasOwnProperty(keyword)) {\n      var value = cssKeywords[keyword];\n      var distance = comparativeDistance(rgb, value);\n      if (distance < currentClosestDistance) {\n        currentClosestDistance = distance;\n        currentClosestKeyword = keyword;\n      }\n    }\n  }\n  return currentClosestKeyword;\n};\nconvert.keyword.rgb = function (keyword) {\n  return cssKeywords[keyword];\n};\nconvert.rgb.xyz = function (rgb) {\n  var r = rgb[0] / 255;\n  var g = rgb[1] / 255;\n  var b = rgb[2] / 255;\n  r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;\n  g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;\n  b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;\n  var x = r * 0.4124 + g * 0.3576 + b * 0.1805;\n  var y = r * 0.2126 + g * 0.7152 + b * 0.0722;\n  var z = r * 0.0193 + g * 0.1192 + b * 0.9505;\n  return [x * 100, y * 100, z * 100];\n};\nconvert.rgb.lab = function (rgb) {\n  var xyz = convert.rgb.xyz(rgb);\n  var x = xyz[0];\n  var y = xyz[1];\n  var z = xyz[2];\n  var l;\n  var a;\n  var b;\n  x /= 95.047;\n  y /= 100;\n  z /= 108.883;\n  x = x > 0.008856 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;\n  y = y > 0.008856 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;\n  z = z > 0.008856 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;\n  l = 116 * y - 16;\n  a = 500 * (x - y);\n  b = 200 * (y - z);\n  return [l, a, b];\n};\nconvert.hsl.rgb = function (hsl) {\n  var h = hsl[0] / 360;\n  var s = hsl[1] / 100;\n  var l = hsl[2] / 100;\n  var t1;\n  var t2;\n  var t3;\n  var rgb;\n  var val;\n  if (s === 0) {\n    val = l * 255;\n    return [val, val, val];\n  }\n  if (l < 0.5) {\n    t2 = l * (1 + s);\n  } else {\n    t2 = l + s - l * s;\n  }\n  t1 = 2 * l - t2;\n  rgb = [0, 0, 0];\n  for (var i = 0; i < 3; i++) {\n    t3 = h + 1 / 3 * -(i - 1);\n    if (t3 < 0) {\n      t3++;\n    }\n    if (t3 > 1) {\n      t3--;\n    }\n    if (6 * t3 < 1) {\n      val = t1 + (t2 - t1) * 6 * t3;\n    } else if (2 * t3 < 1) {\n      val = t2;\n    } else if (3 * t3 < 2) {\n      val = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n    } else {\n      val = t1;\n    }\n    rgb[i] = val * 255;\n  }\n  return rgb;\n};\nconvert.hsl.hsv = function (hsl) {\n  var h = hsl[0];\n  var s = hsl[1] / 100;\n  var l = hsl[2] / 100;\n  var smin = s;\n  var lmin = Math.max(l, 0.01);\n  var sv;\n  var v;\n  l *= 2;\n  s *= l <= 1 ? l : 2 - l;\n  smin *= lmin <= 1 ? lmin : 2 - lmin;\n  v = (l + s) / 2;\n  sv = l === 0 ? 2 * smin / (lmin + smin) : 2 * s / (l + s);\n  return [h, sv * 100, v * 100];\n};\nconvert.hsv.rgb = function (hsv) {\n  var h = hsv[0] / 60;\n  var s = hsv[1] / 100;\n  var v = hsv[2] / 100;\n  var hi = Math.floor(h) % 6;\n  var f = h - Math.floor(h);\n  var p = 255 * v * (1 - s);\n  var q = 255 * v * (1 - s * f);\n  var t = 255 * v * (1 - s * (1 - f));\n  v *= 255;\n  switch (hi) {\n    case 0:\n      return [v, t, p];\n    case 1:\n      return [q, v, p];\n    case 2:\n      return [p, v, t];\n    case 3:\n      return [p, q, v];\n    case 4:\n      return [t, p, v];\n    case 5:\n      return [v, p, q];\n  }\n};\nconvert.hsv.hsl = function (hsv) {\n  var h = hsv[0];\n  var s = hsv[1] / 100;\n  var v = hsv[2] / 100;\n  var vmin = Math.max(v, 0.01);\n  var lmin;\n  var sl;\n  var l;\n  l = (2 - s) * v;\n  lmin = (2 - s) * vmin;\n  sl = s * vmin;\n  sl /= lmin <= 1 ? lmin : 2 - lmin;\n  sl = sl || 0;\n  l /= 2;\n  return [h, sl * 100, l * 100];\n};\nconvert.hwb.rgb = function (hwb) {\n  var h = hwb[0] / 360;\n  var wh = hwb[1] / 100;\n  var bl = hwb[2] / 100;\n  var ratio = wh + bl;\n  var i;\n  var v;\n  var f;\n  var n;\n  if (ratio > 1) {\n    wh /= ratio;\n    bl /= ratio;\n  }\n  i = Math.floor(6 * h);\n  v = 1 - bl;\n  f = 6 * h - i;\n  if ((i & 0x01) !== 0) {\n    f = 1 - f;\n  }\n  n = wh + f * (v - wh);\n  var r;\n  var g;\n  var b;\n  switch (i) {\n    default:\n    case 6:\n    case 0:\n      r = v;\n      g = n;\n      b = wh;\n      break;\n    case 1:\n      r = n;\n      g = v;\n      b = wh;\n      break;\n    case 2:\n      r = wh;\n      g = v;\n      b = n;\n      break;\n    case 3:\n      r = wh;\n      g = n;\n      b = v;\n      break;\n    case 4:\n      r = n;\n      g = wh;\n      b = v;\n      break;\n    case 5:\n      r = v;\n      g = wh;\n      b = n;\n      break;\n  }\n  return [r * 255, g * 255, b * 255];\n};\nconvert.cmyk.rgb = function (cmyk) {\n  var c = cmyk[0] / 100;\n  var m = cmyk[1] / 100;\n  var y = cmyk[2] / 100;\n  var k = cmyk[3] / 100;\n  var r;\n  var g;\n  var b;\n  r = 1 - Math.min(1, c * (1 - k) + k);\n  g = 1 - Math.min(1, m * (1 - k) + k);\n  b = 1 - Math.min(1, y * (1 - k) + k);\n  return [r * 255, g * 255, b * 255];\n};\nconvert.xyz.rgb = function (xyz) {\n  var x = xyz[0] / 100;\n  var y = xyz[1] / 100;\n  var z = xyz[2] / 100;\n  var r;\n  var g;\n  var b;\n  r = x * 3.2406 + y * -1.5372 + z * -0.4986;\n  g = x * -0.9689 + y * 1.8758 + z * 0.0415;\n  b = x * 0.0557 + y * -0.2040 + z * 1.0570;\n  r = r > 0.0031308 ? 1.055 * Math.pow(r, 1.0 / 2.4) - 0.055 : r * 12.92;\n  g = g > 0.0031308 ? 1.055 * Math.pow(g, 1.0 / 2.4) - 0.055 : g * 12.92;\n  b = b > 0.0031308 ? 1.055 * Math.pow(b, 1.0 / 2.4) - 0.055 : b * 12.92;\n  r = Math.min(Math.max(0, r), 1);\n  g = Math.min(Math.max(0, g), 1);\n  b = Math.min(Math.max(0, b), 1);\n  return [r * 255, g * 255, b * 255];\n};\nconvert.xyz.lab = function (xyz) {\n  var x = xyz[0];\n  var y = xyz[1];\n  var z = xyz[2];\n  var l;\n  var a;\n  var b;\n  x /= 95.047;\n  y /= 100;\n  z /= 108.883;\n  x = x > 0.008856 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;\n  y = y > 0.008856 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;\n  z = z > 0.008856 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;\n  l = 116 * y - 16;\n  a = 500 * (x - y);\n  b = 200 * (y - z);\n  return [l, a, b];\n};\nconvert.lab.xyz = function (lab) {\n  var l = lab[0];\n  var a = lab[1];\n  var b = lab[2];\n  var x;\n  var y;\n  var z;\n  y = (l + 16) / 116;\n  x = a / 500 + y;\n  z = y - b / 200;\n  var y2 = Math.pow(y, 3);\n  var x2 = Math.pow(x, 3);\n  var z2 = Math.pow(z, 3);\n  y = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n  x = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n  z = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n  x *= 95.047;\n  y *= 100;\n  z *= 108.883;\n  return [x, y, z];\n};\nconvert.lab.lch = function (lab) {\n  var l = lab[0];\n  var a = lab[1];\n  var b = lab[2];\n  var hr;\n  var h;\n  var c;\n  hr = Math.atan2(b, a);\n  h = hr * 360 / 2 / Math.PI;\n  if (h < 0) {\n    h += 360;\n  }\n  c = Math.sqrt(a * a + b * b);\n  return [l, c, h];\n};\nconvert.lch.lab = function (lch) {\n  var l = lch[0];\n  var c = lch[1];\n  var h = lch[2];\n  var a;\n  var b;\n  var hr;\n  hr = h / 360 * 2 * Math.PI;\n  a = c * Math.cos(hr);\n  b = c * Math.sin(hr);\n  return [l, a, b];\n};\nconvert.rgb.ansi16 = function (args) {\n  var r = args[0];\n  var g = args[1];\n  var b = args[2];\n  var value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2];\n  value = Math.round(value / 50);\n  if (value === 0) {\n    return 30;\n  }\n  var ansi = 30 + (Math.round(b / 255) << 2 | Math.round(g / 255) << 1 | Math.round(r / 255));\n  if (value === 2) {\n    ansi += 60;\n  }\n  return ansi;\n};\nconvert.hsv.ansi16 = function (args) {\n  return convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\nconvert.rgb.ansi256 = function (args) {\n  var r = args[0];\n  var g = args[1];\n  var b = args[2];\n  if (r === g && g === b) {\n    if (r < 8) {\n      return 16;\n    }\n    if (r > 248) {\n      return 231;\n    }\n    return Math.round((r - 8) / 247 * 24) + 232;\n  }\n  var ansi = 16 + 36 * Math.round(r / 255 * 5) + 6 * Math.round(g / 255 * 5) + Math.round(b / 255 * 5);\n  return ansi;\n};\nconvert.ansi16.rgb = function (args) {\n  var color = args % 10;\n  if (color === 0 || color === 7) {\n    if (args > 50) {\n      color += 3.5;\n    }\n    color = color / 10.5 * 255;\n    return [color, color, color];\n  }\n  var mult = (~~(args > 50) + 1) * 0.5;\n  var r = (color & 1) * mult * 255;\n  var g = (color >> 1 & 1) * mult * 255;\n  var b = (color >> 2 & 1) * mult * 255;\n  return [r, g, b];\n};\nconvert.ansi256.rgb = function (args) {\n  if (args >= 232) {\n    var c = (args - 232) * 10 + 8;\n    return [c, c, c];\n  }\n  args -= 16;\n  var rem;\n  var r = Math.floor(args / 36) / 5 * 255;\n  var g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n  var b = rem % 6 / 5 * 255;\n  return [r, g, b];\n};\nconvert.rgb.hex = function (args) {\n  var integer = ((Math.round(args[0]) & 0xFF) << 16) + ((Math.round(args[1]) & 0xFF) << 8) + (Math.round(args[2]) & 0xFF);\n  var string = integer.toString(16).toUpperCase();\n  return '000000'.substring(string.length) + string;\n};\nconvert.hex.rgb = function (args) {\n  var match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n  if (!match) {\n    return [0, 0, 0];\n  }\n  var colorString = match[0];\n  if (match[0].length === 3) {\n    colorString = colorString.split('').map(function (char) {\n      return char + char;\n    }).join('');\n  }\n  var integer = parseInt(colorString, 16);\n  var r = integer >> 16 & 0xFF;\n  var g = integer >> 8 & 0xFF;\n  var b = integer & 0xFF;\n  return [r, g, b];\n};\nconvert.rgb.hcg = function (rgb) {\n  var r = rgb[0] / 255;\n  var g = rgb[1] / 255;\n  var b = rgb[2] / 255;\n  var max = Math.max(Math.max(r, g), b);\n  var min = Math.min(Math.min(r, g), b);\n  var chroma = max - min;\n  var grayscale;\n  var hue;\n  if (chroma < 1) {\n    grayscale = min / (1 - chroma);\n  } else {\n    grayscale = 0;\n  }\n  if (chroma <= 0) {\n    hue = 0;\n  } else if (max === r) {\n    hue = (g - b) / chroma % 6;\n  } else if (max === g) {\n    hue = 2 + (b - r) / chroma;\n  } else {\n    hue = 4 + (r - g) / chroma + 4;\n  }\n  hue /= 6;\n  hue %= 1;\n  return [hue * 360, chroma * 100, grayscale * 100];\n};\nconvert.hsl.hcg = function (hsl) {\n  var s = hsl[1] / 100;\n  var l = hsl[2] / 100;\n  var c = 1;\n  var f = 0;\n  if (l < 0.5) {\n    c = 2.0 * s * l;\n  } else {\n    c = 2.0 * s * (1.0 - l);\n  }\n  if (c < 1.0) {\n    f = (l - 0.5 * c) / (1.0 - c);\n  }\n  return [hsl[0], c * 100, f * 100];\n};\nconvert.hsv.hcg = function (hsv) {\n  var s = hsv[1] / 100;\n  var v = hsv[2] / 100;\n  var c = s * v;\n  var f = 0;\n  if (c < 1.0) {\n    f = (v - c) / (1 - c);\n  }\n  return [hsv[0], c * 100, f * 100];\n};\nconvert.hcg.rgb = function (hcg) {\n  var h = hcg[0] / 360;\n  var c = hcg[1] / 100;\n  var g = hcg[2] / 100;\n  if (c === 0.0) {\n    return [g * 255, g * 255, g * 255];\n  }\n  var pure = [0, 0, 0];\n  var hi = h % 1 * 6;\n  var v = hi % 1;\n  var w = 1 - v;\n  var mg = 0;\n  switch (Math.floor(hi)) {\n    case 0:\n      pure[0] = 1;\n      pure[1] = v;\n      pure[2] = 0;\n      break;\n    case 1:\n      pure[0] = w;\n      pure[1] = 1;\n      pure[2] = 0;\n      break;\n    case 2:\n      pure[0] = 0;\n      pure[1] = 1;\n      pure[2] = v;\n      break;\n    case 3:\n      pure[0] = 0;\n      pure[1] = w;\n      pure[2] = 1;\n      break;\n    case 4:\n      pure[0] = v;\n      pure[1] = 0;\n      pure[2] = 1;\n      break;\n    default:\n      pure[0] = 1;\n      pure[1] = 0;\n      pure[2] = w;\n  }\n  mg = (1.0 - c) * g;\n  return [(c * pure[0] + mg) * 255, (c * pure[1] + mg) * 255, (c * pure[2] + mg) * 255];\n};\nconvert.hcg.hsv = function (hcg) {\n  var c = hcg[1] / 100;\n  var g = hcg[2] / 100;\n  var v = c + g * (1.0 - c);\n  var f = 0;\n  if (v > 0.0) {\n    f = c / v;\n  }\n  return [hcg[0], f * 100, v * 100];\n};\nconvert.hcg.hsl = function (hcg) {\n  var c = hcg[1] / 100;\n  var g = hcg[2] / 100;\n  var l = g * (1.0 - c) + 0.5 * c;\n  var s = 0;\n  if (l > 0.0 && l < 0.5) {\n    s = c / (2 * l);\n  } else if (l >= 0.5 && l < 1.0) {\n    s = c / (2 * (1 - l));\n  }\n  return [hcg[0], s * 100, l * 100];\n};\nconvert.hcg.hwb = function (hcg) {\n  var c = hcg[1] / 100;\n  var g = hcg[2] / 100;\n  var v = c + g * (1.0 - c);\n  return [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\nconvert.hwb.hcg = function (hwb) {\n  var w = hwb[1] / 100;\n  var b = hwb[2] / 100;\n  var v = 1 - b;\n  var c = v - w;\n  var g = 0;\n  if (c < 1) {\n    g = (v - c) / (1 - c);\n  }\n  return [hwb[0], c * 100, g * 100];\n};\nconvert.apple.rgb = function (apple) {\n  return [apple[0] / 65535 * 255, apple[1] / 65535 * 255, apple[2] / 65535 * 255];\n};\nconvert.rgb.apple = function (rgb) {\n  return [rgb[0] / 255 * 65535, rgb[1] / 255 * 65535, rgb[2] / 255 * 65535];\n};\nconvert.gray.rgb = function (args) {\n  return [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n  return [0, 0, args[0]];\n};\nconvert.gray.hwb = function (gray) {\n  return [0, 100, gray[0]];\n};\nconvert.gray.cmyk = function (gray) {\n  return [0, 0, 0, gray[0]];\n};\nconvert.gray.lab = function (gray) {\n  return [gray[0], 0, 0];\n};\nconvert.gray.hex = function (gray) {\n  var val = Math.round(gray[0] / 100 * 255) & 0xFF;\n  var integer = (val << 16) + (val << 8) + val;\n  var string = integer.toString(16).toUpperCase();\n  return '000000'.substring(string.length) + string;\n};\nconvert.rgb.gray = function (rgb) {\n  var val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n  return [val / 255 * 100];\n};", "map": {"version": 3, "names": ["cssKeywords", "require", "reverseKeywords", "key", "hasOwnProperty", "convert", "module", "exports", "rgb", "channels", "labels", "hsl", "hsv", "hwb", "cmyk", "xyz", "lab", "lch", "hex", "keyword", "ansi16", "ansi256", "hcg", "apple", "gray", "model", "Error", "length", "Object", "defineProperty", "value", "r", "g", "b", "min", "Math", "max", "delta", "h", "s", "l", "rdif", "gdif", "bdif", "v", "diff", "diffc", "c", "w", "m", "y", "k", "comparativeDistance", "x", "pow", "reversed", "currentClosestDistance", "Infinity", "currentClosestKeyword", "distance", "z", "a", "t1", "t2", "t3", "val", "i", "smin", "lmin", "sv", "hi", "floor", "f", "p", "q", "t", "vmin", "sl", "wh", "bl", "ratio", "n", "y2", "x2", "z2", "hr", "atan2", "PI", "sqrt", "cos", "sin", "args", "arguments", "round", "ansi", "color", "mult", "rem", "integer", "string", "toString", "toUpperCase", "substring", "match", "colorString", "split", "map", "char", "join", "parseInt", "chroma", "grayscale", "hue", "pure", "mg"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/node_modules/color-convert/conversions.js"], "sourcesContent": ["/* MIT license */\nvar cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n"], "mappings": "AACA,IAAIA,WAAW,GAAGC,OAAO,CAAC,YAAY,CAAC;AAMvC,IAAIC,eAAe,GAAG,CAAC,CAAC;AACxB,KAAK,IAAIC,GAAG,IAAIH,WAAW,EAAE;EAC5B,IAAIA,WAAW,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;IACpCD,eAAe,CAACF,WAAW,CAACG,GAAG,CAAC,CAAC,GAAGA,GAAG;EACxC;AACD;AAEA,IAAIE,OAAO,GAAGC,MAAM,CAACC,OAAO,GAAG;EAC9BC,GAAG,EAAE;IAACC,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC;EACjCC,GAAG,EAAE;IAACF,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC;EACjCE,GAAG,EAAE;IAACH,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC;EACjCG,GAAG,EAAE;IAACJ,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC;EACjCI,IAAI,EAAE;IAACL,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAM,CAAC;EACnCK,GAAG,EAAE;IAACN,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC;EACjCM,GAAG,EAAE;IAACP,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC;EACjCO,GAAG,EAAE;IAACR,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC;EACjCQ,GAAG,EAAE;IAACT,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC,KAAK;EAAC,CAAC;EACnCS,OAAO,EAAE;IAACV,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC,SAAS;EAAC,CAAC;EAC3CU,MAAM,EAAE;IAACX,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC,QAAQ;EAAC,CAAC;EACzCW,OAAO,EAAE;IAACZ,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC,SAAS;EAAC,CAAC;EAC3CY,GAAG,EAAE;IAACb,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;EAAC,CAAC;EAC3Ca,KAAK,EAAE;IAACd,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK;EAAC,CAAC;EACnDc,IAAI,EAAE;IAACf,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC,MAAM;EAAC;AACrC,CAAC;AAGD,KAAK,IAAIe,KAAK,IAAIpB,OAAO,EAAE;EAC1B,IAAIA,OAAO,CAACD,cAAc,CAACqB,KAAK,CAAC,EAAE;IAClC,IAAI,EAAE,UAAU,IAAIpB,OAAO,CAACoB,KAAK,CAAC,CAAC,EAAE;MACpC,MAAM,IAAIC,KAAK,CAAC,6BAA6B,GAAGD,KAAK,CAAC;IACvD;IAEA,IAAI,EAAE,QAAQ,IAAIpB,OAAO,CAACoB,KAAK,CAAC,CAAC,EAAE;MAClC,MAAM,IAAIC,KAAK,CAAC,mCAAmC,GAAGD,KAAK,CAAC;IAC7D;IAEA,IAAIpB,OAAO,CAACoB,KAAK,CAAC,CAACf,MAAM,CAACiB,MAAM,KAAKtB,OAAO,CAACoB,KAAK,CAAC,CAAChB,QAAQ,EAAE;MAC7D,MAAM,IAAIiB,KAAK,CAAC,qCAAqC,GAAGD,KAAK,CAAC;IAC/D;IAEA,IAAIhB,QAAQ,GAAGJ,OAAO,CAACoB,KAAK,CAAC,CAAChB,QAAQ;IACtC,IAAIC,MAAM,GAAGL,OAAO,CAACoB,KAAK,CAAC,CAACf,MAAM;IAClC,OAAOL,OAAO,CAACoB,KAAK,CAAC,CAAChB,QAAQ;IAC9B,OAAOJ,OAAO,CAACoB,KAAK,CAAC,CAACf,MAAM;IAC5BkB,MAAM,CAACC,cAAc,CAACxB,OAAO,CAACoB,KAAK,CAAC,EAAE,UAAU,EAAE;MAACK,KAAK,EAAErB;IAAQ,CAAC,CAAC;IACpEmB,MAAM,CAACC,cAAc,CAACxB,OAAO,CAACoB,KAAK,CAAC,EAAE,QAAQ,EAAE;MAACK,KAAK,EAAEpB;IAAM,CAAC,CAAC;EACjE;AACD;AAEAL,OAAO,CAACG,GAAG,CAACG,GAAG,GAAG,UAAUH,GAAG,EAAE;EAChC,IAAIuB,CAAC,GAAGvB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI0B,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACH,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC3B,IAAIG,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC3B,IAAII,KAAK,GAAGD,GAAG,GAAGF,GAAG;EACrB,IAAII,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EAEL,IAAIJ,GAAG,KAAKF,GAAG,EAAE;IAChBI,CAAC,GAAG,CAAC;EACN,CAAC,MAAM,IAAIP,CAAC,KAAKK,GAAG,EAAE;IACrBE,CAAC,GAAG,CAACN,CAAC,GAAGC,CAAC,IAAII,KAAK;EACpB,CAAC,MAAM,IAAIL,CAAC,KAAKI,GAAG,EAAE;IACrBE,CAAC,GAAG,CAAC,GAAG,CAACL,CAAC,GAAGF,CAAC,IAAIM,KAAK;EACxB,CAAC,MAAM,IAAIJ,CAAC,KAAKG,GAAG,EAAE;IACrBE,CAAC,GAAG,CAAC,GAAG,CAACP,CAAC,GAAGC,CAAC,IAAIK,KAAK;EACxB;EAEAC,CAAC,GAAGH,IAAI,CAACD,GAAG,CAACI,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC;EAEzB,IAAIA,CAAC,GAAG,CAAC,EAAE;IACVA,CAAC,IAAI,GAAG;EACT;EAEAE,CAAC,GAAG,CAACN,GAAG,GAAGE,GAAG,IAAI,CAAC;EAEnB,IAAIA,GAAG,KAAKF,GAAG,EAAE;IAChBK,CAAC,GAAG,CAAC;EACN,CAAC,MAAM,IAAIC,CAAC,IAAI,GAAG,EAAE;IACpBD,CAAC,GAAGF,KAAK,IAAID,GAAG,GAAGF,GAAG,CAAC;EACxB,CAAC,MAAM;IACNK,CAAC,GAAGF,KAAK,IAAI,CAAC,GAAGD,GAAG,GAAGF,GAAG,CAAC;EAC5B;EAEA,OAAO,CAACI,CAAC,EAAEC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AAC7B,CAAC;AAEDnC,OAAO,CAACG,GAAG,CAACI,GAAG,GAAG,UAAUJ,GAAG,EAAE;EAChC,IAAIiC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIL,CAAC;EACL,IAAIC,CAAC;EAEL,IAAIR,CAAC,GAAGvB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIoC,CAAC,GAAGT,IAAI,CAACC,GAAG,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACzB,IAAIY,IAAI,GAAGD,CAAC,GAAGT,IAAI,CAACD,GAAG,CAACH,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAChC,IAAIa,KAAK,GAAG,SAARA,KAAKA,CAAaC,CAAC,EAAE;IACxB,OAAO,CAACH,CAAC,GAAGG,CAAC,IAAI,CAAC,GAAGF,IAAI,GAAG,CAAC,GAAG,CAAC;EAClC,CAAC;EAED,IAAIA,IAAI,KAAK,CAAC,EAAE;IACfP,CAAC,GAAGC,CAAC,GAAG,CAAC;EACV,CAAC,MAAM;IACNA,CAAC,GAAGM,IAAI,GAAGD,CAAC;IACZH,IAAI,GAAGK,KAAK,CAACf,CAAC,CAAC;IACfW,IAAI,GAAGI,KAAK,CAACd,CAAC,CAAC;IACfW,IAAI,GAAGG,KAAK,CAACb,CAAC,CAAC;IAEf,IAAIF,CAAC,KAAKa,CAAC,EAAE;MACZN,CAAC,GAAGK,IAAI,GAAGD,IAAI;IAChB,CAAC,MAAM,IAAIV,CAAC,KAAKY,CAAC,EAAE;MACnBN,CAAC,GAAI,CAAC,GAAG,CAAC,GAAIG,IAAI,GAAGE,IAAI;IAC1B,CAAC,MAAM,IAAIV,CAAC,KAAKW,CAAC,EAAE;MACnBN,CAAC,GAAI,CAAC,GAAG,CAAC,GAAII,IAAI,GAAGD,IAAI;IAC1B;IACA,IAAIH,CAAC,GAAG,CAAC,EAAE;MACVA,CAAC,IAAI,CAAC;IACP,CAAC,MAAM,IAAIA,CAAC,GAAG,CAAC,EAAE;MACjBA,CAAC,IAAI,CAAC;IACP;EACD;EAEA,OAAO,CACNA,CAAC,GAAG,GAAG,EACPC,CAAC,GAAG,GAAG,EACPK,CAAC,GAAG,GAAG,CACP;AACF,CAAC;AAEDvC,OAAO,CAACG,GAAG,CAACK,GAAG,GAAG,UAAUL,GAAG,EAAE;EAChC,IAAIuB,CAAC,GAAGvB,GAAG,CAAC,CAAC,CAAC;EACd,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC;EACd,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC;EACd,IAAI8B,CAAC,GAAGjC,OAAO,CAACG,GAAG,CAACG,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAIwC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGb,IAAI,CAACD,GAAG,CAACH,CAAC,EAAEI,IAAI,CAACD,GAAG,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC;EAE7CA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACC,GAAG,CAACL,CAAC,EAAEI,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAEC,CAAC,CAAC,CAAC;EAE7C,OAAO,CAACK,CAAC,EAAEU,CAAC,GAAG,GAAG,EAAEf,CAAC,GAAG,GAAG,CAAC;AAC7B,CAAC;AAED5B,OAAO,CAACG,GAAG,CAACM,IAAI,GAAG,UAAUN,GAAG,EAAE;EACjC,IAAIuB,CAAC,GAAGvB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIuC,CAAC;EACL,IAAIE,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EAELA,CAAC,GAAGhB,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGH,CAAC,EAAE,CAAC,GAAGC,CAAC,EAAE,CAAC,GAAGC,CAAC,CAAC;EACjCc,CAAC,GAAG,CAAC,CAAC,GAAGhB,CAAC,GAAGoB,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC;EAC9BF,CAAC,GAAG,CAAC,CAAC,GAAGjB,CAAC,GAAGmB,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC;EAC9BD,CAAC,GAAG,CAAC,CAAC,GAAGjB,CAAC,GAAGkB,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC;EAE9B,OAAO,CAACJ,CAAC,GAAG,GAAG,EAAEE,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AAC5C,CAAC;AAKD,SAASC,mBAAmBA,CAACC,CAAC,EAAEH,CAAC,EAAE;EAClC,OACCf,IAAI,CAACmB,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GACxBf,IAAI,CAACmB,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GACxBf,IAAI,CAACmB,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAE1B;AAEA7C,OAAO,CAACG,GAAG,CAACW,OAAO,GAAG,UAAUX,GAAG,EAAE;EACpC,IAAI+C,QAAQ,GAAGrD,eAAe,CAACM,GAAG,CAAC;EACnC,IAAI+C,QAAQ,EAAE;IACb,OAAOA,QAAQ;EAChB;EAEA,IAAIC,sBAAsB,GAAGC,QAAQ;EACrC,IAAIC,qBAAqB;EAEzB,KAAK,IAAIvC,OAAO,IAAInB,WAAW,EAAE;IAChC,IAAIA,WAAW,CAACI,cAAc,CAACe,OAAO,CAAC,EAAE;MACxC,IAAIW,KAAK,GAAG9B,WAAW,CAACmB,OAAO,CAAC;MAGhC,IAAIwC,QAAQ,GAAGP,mBAAmB,CAAC5C,GAAG,EAAEsB,KAAK,CAAC;MAG9C,IAAI6B,QAAQ,GAAGH,sBAAsB,EAAE;QACtCA,sBAAsB,GAAGG,QAAQ;QACjCD,qBAAqB,GAAGvC,OAAO;MAChC;IACD;EACD;EAEA,OAAOuC,qBAAqB;AAC7B,CAAC;AAEDrD,OAAO,CAACc,OAAO,CAACX,GAAG,GAAG,UAAUW,OAAO,EAAE;EACxC,OAAOnB,WAAW,CAACmB,OAAO,CAAC;AAC5B,CAAC;AAEDd,OAAO,CAACG,GAAG,CAACO,GAAG,GAAG,UAAUP,GAAG,EAAE;EAChC,IAAIuB,CAAC,GAAGvB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EAGpBuB,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAGI,IAAI,CAACmB,GAAG,CAAE,CAACvB,CAAC,GAAG,KAAK,IAAI,KAAK,EAAG,GAAG,CAAC,GAAIA,CAAC,GAAG,KAAM;EACpEC,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAGG,IAAI,CAACmB,GAAG,CAAE,CAACtB,CAAC,GAAG,KAAK,IAAI,KAAK,EAAG,GAAG,CAAC,GAAIA,CAAC,GAAG,KAAM;EACpEC,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAGE,IAAI,CAACmB,GAAG,CAAE,CAACrB,CAAC,GAAG,KAAK,IAAI,KAAK,EAAG,GAAG,CAAC,GAAIA,CAAC,GAAG,KAAM;EAEpE,IAAIoB,CAAC,GAAItB,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,MAAO,GAAIC,CAAC,GAAG,MAAO;EAClD,IAAIiB,CAAC,GAAInB,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,MAAO,GAAIC,CAAC,GAAG,MAAO;EAClD,IAAI2B,CAAC,GAAI7B,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,MAAO,GAAIC,CAAC,GAAG,MAAO;EAElD,OAAO,CAACoB,CAAC,GAAG,GAAG,EAAEH,CAAC,GAAG,GAAG,EAAEU,CAAC,GAAG,GAAG,CAAC;AACnC,CAAC;AAEDvD,OAAO,CAACG,GAAG,CAACQ,GAAG,GAAG,UAAUR,GAAG,EAAE;EAChC,IAAIO,GAAG,GAAGV,OAAO,CAACG,GAAG,CAACO,GAAG,CAACP,GAAG,CAAC;EAC9B,IAAI6C,CAAC,GAAGtC,GAAG,CAAC,CAAC,CAAC;EACd,IAAImC,CAAC,GAAGnC,GAAG,CAAC,CAAC,CAAC;EACd,IAAI6C,CAAC,GAAG7C,GAAG,CAAC,CAAC,CAAC;EACd,IAAIyB,CAAC;EACL,IAAIqB,CAAC;EACL,IAAI5B,CAAC;EAELoB,CAAC,IAAI,MAAM;EACXH,CAAC,IAAI,GAAG;EACRU,CAAC,IAAI,OAAO;EAEZP,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAGlB,IAAI,CAACmB,GAAG,CAACD,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAI,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAChEH,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAGf,IAAI,CAACmB,GAAG,CAACJ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAI,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAChEU,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAGzB,IAAI,CAACmB,GAAG,CAACM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAI,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAEhEpB,CAAC,GAAI,GAAG,GAAGU,CAAC,GAAI,EAAE;EAClBW,CAAC,GAAG,GAAG,IAAIR,CAAC,GAAGH,CAAC,CAAC;EACjBjB,CAAC,GAAG,GAAG,IAAIiB,CAAC,GAAGU,CAAC,CAAC;EAEjB,OAAO,CAACpB,CAAC,EAAEqB,CAAC,EAAE5B,CAAC,CAAC;AACjB,CAAC;AAED5B,OAAO,CAACM,GAAG,CAACH,GAAG,GAAG,UAAUG,GAAG,EAAE;EAChC,IAAI2B,CAAC,GAAG3B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI4B,CAAC,GAAG5B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI6B,CAAC,GAAG7B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAImD,EAAE;EACN,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,IAAIxD,GAAG;EACP,IAAIyD,GAAG;EAEP,IAAI1B,CAAC,KAAK,CAAC,EAAE;IACZ0B,GAAG,GAAGzB,CAAC,GAAG,GAAG;IACb,OAAO,CAACyB,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC;EACvB;EAEA,IAAIzB,CAAC,GAAG,GAAG,EAAE;IACZuB,EAAE,GAAGvB,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC;EACjB,CAAC,MAAM;IACNwB,EAAE,GAAGvB,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGD,CAAC;EACnB;EAEAuB,EAAE,GAAG,CAAC,GAAGtB,CAAC,GAAGuB,EAAE;EAEfvD,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACf,KAAK,IAAI0D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3BF,EAAE,GAAG1B,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE4B,CAAC,GAAG,CAAC,CAAC;IACzB,IAAIF,EAAE,GAAG,CAAC,EAAE;MACXA,EAAE,EAAE;IACL;IACA,IAAIA,EAAE,GAAG,CAAC,EAAE;MACXA,EAAE,EAAE;IACL;IAEA,IAAI,CAAC,GAAGA,EAAE,GAAG,CAAC,EAAE;MACfC,GAAG,GAAGH,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,CAAC,GAAGE,EAAE;IAC9B,CAAC,MAAM,IAAI,CAAC,GAAGA,EAAE,GAAG,CAAC,EAAE;MACtBC,GAAG,GAAGF,EAAE;IACT,CAAC,MAAM,IAAI,CAAC,GAAGC,EAAE,GAAG,CAAC,EAAE;MACtBC,GAAG,GAAGH,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGE,EAAE,CAAC,GAAG,CAAC;IACxC,CAAC,MAAM;MACNC,GAAG,GAAGH,EAAE;IACT;IAEAtD,GAAG,CAAC0D,CAAC,CAAC,GAAGD,GAAG,GAAG,GAAG;EACnB;EAEA,OAAOzD,GAAG;AACX,CAAC;AAEDH,OAAO,CAACM,GAAG,CAACC,GAAG,GAAG,UAAUD,GAAG,EAAE;EAChC,IAAI2B,CAAC,GAAG3B,GAAG,CAAC,CAAC,CAAC;EACd,IAAI4B,CAAC,GAAG5B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI6B,CAAC,GAAG7B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIwD,IAAI,GAAG5B,CAAC;EACZ,IAAI6B,IAAI,GAAGjC,IAAI,CAACC,GAAG,CAACI,CAAC,EAAE,IAAI,CAAC;EAC5B,IAAI6B,EAAE;EACN,IAAIzB,CAAC;EAELJ,CAAC,IAAI,CAAC;EACND,CAAC,IAAKC,CAAC,IAAI,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAGA,CAAC;EACzB2B,IAAI,IAAIC,IAAI,IAAI,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI;EACnCxB,CAAC,GAAG,CAACJ,CAAC,GAAGD,CAAC,IAAI,CAAC;EACf8B,EAAE,GAAG7B,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG2B,IAAI,IAAKC,IAAI,GAAGD,IAAI,CAAC,GAAI,CAAC,GAAG5B,CAAC,IAAKC,CAAC,GAAGD,CAAC,CAAC;EAE7D,OAAO,CAACD,CAAC,EAAE+B,EAAE,GAAG,GAAG,EAAEzB,CAAC,GAAG,GAAG,CAAC;AAC9B,CAAC;AAEDvC,OAAO,CAACO,GAAG,CAACJ,GAAG,GAAG,UAAUI,GAAG,EAAE;EAChC,IAAI0B,CAAC,GAAG1B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;EACnB,IAAI2B,CAAC,GAAG3B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIgC,CAAC,GAAGhC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI0D,EAAE,GAAGnC,IAAI,CAACoC,KAAK,CAACjC,CAAC,CAAC,GAAG,CAAC;EAE1B,IAAIkC,CAAC,GAAGlC,CAAC,GAAGH,IAAI,CAACoC,KAAK,CAACjC,CAAC,CAAC;EACzB,IAAImC,CAAC,GAAG,GAAG,GAAG7B,CAAC,IAAI,CAAC,GAAGL,CAAC,CAAC;EACzB,IAAImC,CAAC,GAAG,GAAG,GAAG9B,CAAC,IAAI,CAAC,GAAIL,CAAC,GAAGiC,CAAE,CAAC;EAC/B,IAAIG,CAAC,GAAG,GAAG,GAAG/B,CAAC,IAAI,CAAC,GAAIL,CAAC,IAAI,CAAC,GAAGiC,CAAC,CAAE,CAAC;EACrC5B,CAAC,IAAI,GAAG;EAER,QAAQ0B,EAAE;IACT,KAAK,CAAC;MACL,OAAO,CAAC1B,CAAC,EAAE+B,CAAC,EAAEF,CAAC,CAAC;IACjB,KAAK,CAAC;MACL,OAAO,CAACC,CAAC,EAAE9B,CAAC,EAAE6B,CAAC,CAAC;IACjB,KAAK,CAAC;MACL,OAAO,CAACA,CAAC,EAAE7B,CAAC,EAAE+B,CAAC,CAAC;IACjB,KAAK,CAAC;MACL,OAAO,CAACF,CAAC,EAAEC,CAAC,EAAE9B,CAAC,CAAC;IACjB,KAAK,CAAC;MACL,OAAO,CAAC+B,CAAC,EAAEF,CAAC,EAAE7B,CAAC,CAAC;IACjB,KAAK,CAAC;MACL,OAAO,CAACA,CAAC,EAAE6B,CAAC,EAAEC,CAAC,CAAC;EAClB;AACD,CAAC;AAEDrE,OAAO,CAACO,GAAG,CAACD,GAAG,GAAG,UAAUC,GAAG,EAAE;EAChC,IAAI0B,CAAC,GAAG1B,GAAG,CAAC,CAAC,CAAC;EACd,IAAI2B,CAAC,GAAG3B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIgC,CAAC,GAAGhC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIgE,IAAI,GAAGzC,IAAI,CAACC,GAAG,CAACQ,CAAC,EAAE,IAAI,CAAC;EAC5B,IAAIwB,IAAI;EACR,IAAIS,EAAE;EACN,IAAIrC,CAAC;EAELA,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,IAAIK,CAAC;EACfwB,IAAI,GAAG,CAAC,CAAC,GAAG7B,CAAC,IAAIqC,IAAI;EACrBC,EAAE,GAAGtC,CAAC,GAAGqC,IAAI;EACbC,EAAE,IAAKT,IAAI,IAAI,CAAC,GAAIA,IAAI,GAAG,CAAC,GAAGA,IAAI;EACnCS,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZrC,CAAC,IAAI,CAAC;EAEN,OAAO,CAACF,CAAC,EAAEuC,EAAE,GAAG,GAAG,EAAErC,CAAC,GAAG,GAAG,CAAC;AAC9B,CAAC;AAGDnC,OAAO,CAACQ,GAAG,CAACL,GAAG,GAAG,UAAUK,GAAG,EAAE;EAChC,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIiE,EAAE,GAAGjE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAIkE,EAAE,GAAGlE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAImE,KAAK,GAAGF,EAAE,GAAGC,EAAE;EACnB,IAAIb,CAAC;EACL,IAAItB,CAAC;EACL,IAAI4B,CAAC;EACL,IAAIS,CAAC;EAGL,IAAID,KAAK,GAAG,CAAC,EAAE;IACdF,EAAE,IAAIE,KAAK;IACXD,EAAE,IAAIC,KAAK;EACZ;EAEAd,CAAC,GAAG/B,IAAI,CAACoC,KAAK,CAAC,CAAC,GAAGjC,CAAC,CAAC;EACrBM,CAAC,GAAG,CAAC,GAAGmC,EAAE;EACVP,CAAC,GAAG,CAAC,GAAGlC,CAAC,GAAG4B,CAAC;EAEb,IAAI,CAACA,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;IACrBM,CAAC,GAAG,CAAC,GAAGA,CAAC;EACV;EAEAS,CAAC,GAAGH,EAAE,GAAGN,CAAC,IAAI5B,CAAC,GAAGkC,EAAE,CAAC;EAErB,IAAI/C,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EACL,QAAQiC,CAAC;IACR;IACA,KAAK,CAAC;IACN,KAAK,CAAC;MAAEnC,CAAC,GAAGa,CAAC;MAAEZ,CAAC,GAAGiD,CAAC;MAAEhD,CAAC,GAAG6C,EAAE;MAAE;IAC9B,KAAK,CAAC;MAAE/C,CAAC,GAAGkD,CAAC;MAAEjD,CAAC,GAAGY,CAAC;MAAEX,CAAC,GAAG6C,EAAE;MAAE;IAC9B,KAAK,CAAC;MAAE/C,CAAC,GAAG+C,EAAE;MAAE9C,CAAC,GAAGY,CAAC;MAAEX,CAAC,GAAGgD,CAAC;MAAE;IAC9B,KAAK,CAAC;MAAElD,CAAC,GAAG+C,EAAE;MAAE9C,CAAC,GAAGiD,CAAC;MAAEhD,CAAC,GAAGW,CAAC;MAAE;IAC9B,KAAK,CAAC;MAAEb,CAAC,GAAGkD,CAAC;MAAEjD,CAAC,GAAG8C,EAAE;MAAE7C,CAAC,GAAGW,CAAC;MAAE;IAC9B,KAAK,CAAC;MAAEb,CAAC,GAAGa,CAAC;MAAEZ,CAAC,GAAG8C,EAAE;MAAE7C,CAAC,GAAGgD,CAAC;MAAE;EAC/B;EAEA,OAAO,CAAClD,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AACnC,CAAC;AAED5B,OAAO,CAACS,IAAI,CAACN,GAAG,GAAG,UAAUM,IAAI,EAAE;EAClC,IAAIiC,CAAC,GAAGjC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAImC,CAAC,GAAGnC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAIoC,CAAC,GAAGpC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAIqC,CAAC,GAAGrC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;EACrB,IAAIiB,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EAELF,CAAC,GAAG,CAAC,GAAGI,IAAI,CAACD,GAAG,CAAC,CAAC,EAAEa,CAAC,IAAI,CAAC,GAAGI,CAAC,CAAC,GAAGA,CAAC,CAAC;EACpCnB,CAAC,GAAG,CAAC,GAAGG,IAAI,CAACD,GAAG,CAAC,CAAC,EAAEe,CAAC,IAAI,CAAC,GAAGE,CAAC,CAAC,GAAGA,CAAC,CAAC;EACpClB,CAAC,GAAG,CAAC,GAAGE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAEgB,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC,GAAGA,CAAC,CAAC;EAEpC,OAAO,CAACpB,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AACnC,CAAC;AAED5B,OAAO,CAACU,GAAG,CAACP,GAAG,GAAG,UAAUO,GAAG,EAAE;EAChC,IAAIsC,CAAC,GAAGtC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAImC,CAAC,GAAGnC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI6C,CAAC,GAAG7C,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIgB,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EAELF,CAAC,GAAIsB,CAAC,GAAG,MAAM,GAAKH,CAAC,GAAG,CAAC,MAAO,GAAIU,CAAC,GAAG,CAAC,MAAO;EAChD5B,CAAC,GAAIqB,CAAC,GAAG,CAAC,MAAM,GAAKH,CAAC,GAAG,MAAO,GAAIU,CAAC,GAAG,MAAO;EAC/C3B,CAAC,GAAIoB,CAAC,GAAG,MAAM,GAAKH,CAAC,GAAG,CAAC,MAAO,GAAIU,CAAC,GAAG,MAAO;EAG/C7B,CAAC,GAAGA,CAAC,GAAG,SAAS,GACZ,KAAK,GAAGI,IAAI,CAACmB,GAAG,CAACvB,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAI,KAAK,GACzCA,CAAC,GAAG,KAAK;EAEZC,CAAC,GAAGA,CAAC,GAAG,SAAS,GACZ,KAAK,GAAGG,IAAI,CAACmB,GAAG,CAACtB,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAI,KAAK,GACzCA,CAAC,GAAG,KAAK;EAEZC,CAAC,GAAGA,CAAC,GAAG,SAAS,GACZ,KAAK,GAAGE,IAAI,CAACmB,GAAG,CAACrB,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAI,KAAK,GACzCA,CAAC,GAAG,KAAK;EAEZF,CAAC,GAAGI,IAAI,CAACD,GAAG,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/BC,CAAC,GAAGG,IAAI,CAACD,GAAG,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/BC,CAAC,GAAGE,IAAI,CAACD,GAAG,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,CAAC,CAAC,EAAE,CAAC,CAAC;EAE/B,OAAO,CAACF,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AACnC,CAAC;AAED5B,OAAO,CAACU,GAAG,CAACC,GAAG,GAAG,UAAUD,GAAG,EAAE;EAChC,IAAIsC,CAAC,GAAGtC,GAAG,CAAC,CAAC,CAAC;EACd,IAAImC,CAAC,GAAGnC,GAAG,CAAC,CAAC,CAAC;EACd,IAAI6C,CAAC,GAAG7C,GAAG,CAAC,CAAC,CAAC;EACd,IAAIyB,CAAC;EACL,IAAIqB,CAAC;EACL,IAAI5B,CAAC;EAELoB,CAAC,IAAI,MAAM;EACXH,CAAC,IAAI,GAAG;EACRU,CAAC,IAAI,OAAO;EAEZP,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAGlB,IAAI,CAACmB,GAAG,CAACD,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAI,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAChEH,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAGf,IAAI,CAACmB,GAAG,CAACJ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAI,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAChEU,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAGzB,IAAI,CAACmB,GAAG,CAACM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAI,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAEhEpB,CAAC,GAAI,GAAG,GAAGU,CAAC,GAAI,EAAE;EAClBW,CAAC,GAAG,GAAG,IAAIR,CAAC,GAAGH,CAAC,CAAC;EACjBjB,CAAC,GAAG,GAAG,IAAIiB,CAAC,GAAGU,CAAC,CAAC;EAEjB,OAAO,CAACpB,CAAC,EAAEqB,CAAC,EAAE5B,CAAC,CAAC;AACjB,CAAC;AAED5B,OAAO,CAACW,GAAG,CAACD,GAAG,GAAG,UAAUC,GAAG,EAAE;EAChC,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC;EACd,IAAI6C,CAAC,GAAG7C,GAAG,CAAC,CAAC,CAAC;EACd,IAAIiB,CAAC,GAAGjB,GAAG,CAAC,CAAC,CAAC;EACd,IAAIqC,CAAC;EACL,IAAIH,CAAC;EACL,IAAIU,CAAC;EAELV,CAAC,GAAG,CAACV,CAAC,GAAG,EAAE,IAAI,GAAG;EAClBa,CAAC,GAAGQ,CAAC,GAAG,GAAG,GAAGX,CAAC;EACfU,CAAC,GAAGV,CAAC,GAAGjB,CAAC,GAAG,GAAG;EAEf,IAAIiD,EAAE,GAAG/C,IAAI,CAACmB,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC;EACvB,IAAIiC,EAAE,GAAGhD,IAAI,CAACmB,GAAG,CAACD,CAAC,EAAE,CAAC,CAAC;EACvB,IAAI+B,EAAE,GAAGjD,IAAI,CAACmB,GAAG,CAACM,CAAC,EAAE,CAAC,CAAC;EACvBV,CAAC,GAAGgC,EAAE,GAAG,QAAQ,GAAGA,EAAE,GAAG,CAAChC,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK;EAC/CG,CAAC,GAAG8B,EAAE,GAAG,QAAQ,GAAGA,EAAE,GAAG,CAAC9B,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK;EAC/CO,CAAC,GAAGwB,EAAE,GAAG,QAAQ,GAAGA,EAAE,GAAG,CAACxB,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK;EAE/CP,CAAC,IAAI,MAAM;EACXH,CAAC,IAAI,GAAG;EACRU,CAAC,IAAI,OAAO;EAEZ,OAAO,CAACP,CAAC,EAAEH,CAAC,EAAEU,CAAC,CAAC;AACjB,CAAC;AAEDvD,OAAO,CAACW,GAAG,CAACC,GAAG,GAAG,UAAUD,GAAG,EAAE;EAChC,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC;EACd,IAAI6C,CAAC,GAAG7C,GAAG,CAAC,CAAC,CAAC;EACd,IAAIiB,CAAC,GAAGjB,GAAG,CAAC,CAAC,CAAC;EACd,IAAIqE,EAAE;EACN,IAAI/C,CAAC;EACL,IAAIS,CAAC;EAELsC,EAAE,GAAGlD,IAAI,CAACmD,KAAK,CAACrD,CAAC,EAAE4B,CAAC,CAAC;EACrBvB,CAAC,GAAG+C,EAAE,GAAG,GAAG,GAAG,CAAC,GAAGlD,IAAI,CAACoD,EAAE;EAE1B,IAAIjD,CAAC,GAAG,CAAC,EAAE;IACVA,CAAC,IAAI,GAAG;EACT;EAEAS,CAAC,GAAGZ,IAAI,CAACqD,IAAI,CAAC3B,CAAC,GAAGA,CAAC,GAAG5B,CAAC,GAAGA,CAAC,CAAC;EAE5B,OAAO,CAACO,CAAC,EAAEO,CAAC,EAAET,CAAC,CAAC;AACjB,CAAC;AAEDjC,OAAO,CAACY,GAAG,CAACD,GAAG,GAAG,UAAUC,GAAG,EAAE;EAChC,IAAIuB,CAAC,GAAGvB,GAAG,CAAC,CAAC,CAAC;EACd,IAAI8B,CAAC,GAAG9B,GAAG,CAAC,CAAC,CAAC;EACd,IAAIqB,CAAC,GAAGrB,GAAG,CAAC,CAAC,CAAC;EACd,IAAI4C,CAAC;EACL,IAAI5B,CAAC;EACL,IAAIoD,EAAE;EAENA,EAAE,GAAG/C,CAAC,GAAG,GAAG,GAAG,CAAC,GAAGH,IAAI,CAACoD,EAAE;EAC1B1B,CAAC,GAAGd,CAAC,GAAGZ,IAAI,CAACsD,GAAG,CAACJ,EAAE,CAAC;EACpBpD,CAAC,GAAGc,CAAC,GAAGZ,IAAI,CAACuD,GAAG,CAACL,EAAE,CAAC;EAEpB,OAAO,CAAC7C,CAAC,EAAEqB,CAAC,EAAE5B,CAAC,CAAC;AACjB,CAAC;AAED5B,OAAO,CAACG,GAAG,CAACY,MAAM,GAAG,UAAUuE,IAAI,EAAE;EACpC,IAAI5D,CAAC,GAAG4D,IAAI,CAAC,CAAC,CAAC;EACf,IAAI3D,CAAC,GAAG2D,IAAI,CAAC,CAAC,CAAC;EACf,IAAI1D,CAAC,GAAG0D,IAAI,CAAC,CAAC,CAAC;EACf,IAAI7D,KAAK,GAAG,CAAC,IAAI8D,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGvF,OAAO,CAACG,GAAG,CAACI,GAAG,CAAC+E,IAAI,CAAC,CAAC,CAAC,CAAC;EAEpE7D,KAAK,GAAGK,IAAI,CAAC0D,KAAK,CAAC/D,KAAK,GAAG,EAAE,CAAC;EAE9B,IAAIA,KAAK,KAAK,CAAC,EAAE;IAChB,OAAO,EAAE;EACV;EAEA,IAAIgE,IAAI,GAAG,EAAE,IACR3D,IAAI,CAAC0D,KAAK,CAAC5D,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GACzBE,IAAI,CAAC0D,KAAK,CAAC7D,CAAC,GAAG,GAAG,CAAC,IAAI,CAAE,GAC1BG,IAAI,CAAC0D,KAAK,CAAC9D,CAAC,GAAG,GAAG,CAAC,CAAC;EAEvB,IAAID,KAAK,KAAK,CAAC,EAAE;IAChBgE,IAAI,IAAI,EAAE;EACX;EAEA,OAAOA,IAAI;AACZ,CAAC;AAEDzF,OAAO,CAACO,GAAG,CAACQ,MAAM,GAAG,UAAUuE,IAAI,EAAE;EAGpC,OAAOtF,OAAO,CAACG,GAAG,CAACY,MAAM,CAACf,OAAO,CAACO,GAAG,CAACJ,GAAG,CAACmF,IAAI,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC;AAEDtF,OAAO,CAACG,GAAG,CAACa,OAAO,GAAG,UAAUsE,IAAI,EAAE;EACrC,IAAI5D,CAAC,GAAG4D,IAAI,CAAC,CAAC,CAAC;EACf,IAAI3D,CAAC,GAAG2D,IAAI,CAAC,CAAC,CAAC;EACf,IAAI1D,CAAC,GAAG0D,IAAI,CAAC,CAAC,CAAC;EAIf,IAAI5D,CAAC,KAAKC,CAAC,IAAIA,CAAC,KAAKC,CAAC,EAAE;IACvB,IAAIF,CAAC,GAAG,CAAC,EAAE;MACV,OAAO,EAAE;IACV;IAEA,IAAIA,CAAC,GAAG,GAAG,EAAE;MACZ,OAAO,GAAG;IACX;IAEA,OAAOI,IAAI,CAAC0D,KAAK,CAAE,CAAC9D,CAAC,GAAG,CAAC,IAAI,GAAG,GAAI,EAAE,CAAC,GAAG,GAAG;EAC9C;EAEA,IAAI+D,IAAI,GAAG,EAAE,GACT,EAAE,GAAG3D,IAAI,CAAC0D,KAAK,CAAC9D,CAAC,GAAG,GAAG,GAAG,CAAC,CAAE,GAC7B,CAAC,GAAGI,IAAI,CAAC0D,KAAK,CAAC7D,CAAC,GAAG,GAAG,GAAG,CAAC,CAAE,GAC7BG,IAAI,CAAC0D,KAAK,CAAC5D,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;EAE1B,OAAO6D,IAAI;AACZ,CAAC;AAEDzF,OAAO,CAACe,MAAM,CAACZ,GAAG,GAAG,UAAUmF,IAAI,EAAE;EACpC,IAAII,KAAK,GAAGJ,IAAI,GAAG,EAAE;EAGrB,IAAII,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC,EAAE;IAC/B,IAAIJ,IAAI,GAAG,EAAE,EAAE;MACdI,KAAK,IAAI,GAAG;IACb;IAEAA,KAAK,GAAGA,KAAK,GAAG,IAAI,GAAG,GAAG;IAE1B,OAAO,CAACA,KAAK,EAAEA,KAAK,EAAEA,KAAK,CAAC;EAC7B;EAEA,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAEL,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG;EACpC,IAAI5D,CAAC,GAAI,CAACgE,KAAK,GAAG,CAAC,IAAIC,IAAI,GAAI,GAAG;EAClC,IAAIhE,CAAC,GAAI,CAAE+D,KAAK,IAAI,CAAC,GAAI,CAAC,IAAIC,IAAI,GAAI,GAAG;EACzC,IAAI/D,CAAC,GAAI,CAAE8D,KAAK,IAAI,CAAC,GAAI,CAAC,IAAIC,IAAI,GAAI,GAAG;EAEzC,OAAO,CAACjE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACjB,CAAC;AAED5B,OAAO,CAACgB,OAAO,CAACb,GAAG,GAAG,UAAUmF,IAAI,EAAE;EAErC,IAAIA,IAAI,IAAI,GAAG,EAAE;IAChB,IAAI5C,CAAC,GAAG,CAAC4C,IAAI,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC;IAC7B,OAAO,CAAC5C,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;EACjB;EAEA4C,IAAI,IAAI,EAAE;EAEV,IAAIM,GAAG;EACP,IAAIlE,CAAC,GAAGI,IAAI,CAACoC,KAAK,CAACoB,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG;EACvC,IAAI3D,CAAC,GAAGG,IAAI,CAACoC,KAAK,CAAC,CAAC0B,GAAG,GAAGN,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG;EACnD,IAAI1D,CAAC,GAAIgE,GAAG,GAAG,CAAC,GAAI,CAAC,GAAG,GAAG;EAE3B,OAAO,CAAClE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACjB,CAAC;AAED5B,OAAO,CAACG,GAAG,CAACU,GAAG,GAAG,UAAUyE,IAAI,EAAE;EACjC,IAAIO,OAAO,GAAG,CAAC,CAAC/D,IAAI,CAAC0D,KAAK,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,KAC7C,CAACxD,IAAI,CAAC0D,KAAK,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAClCxD,IAAI,CAAC0D,KAAK,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAE/B,IAAIQ,MAAM,GAAGD,OAAO,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAC/C,OAAO,QAAQ,CAACC,SAAS,CAACH,MAAM,CAACxE,MAAM,CAAC,GAAGwE,MAAM;AAClD,CAAC;AAED9F,OAAO,CAACa,GAAG,CAACV,GAAG,GAAG,UAAUmF,IAAI,EAAE;EACjC,IAAIY,KAAK,GAAGZ,IAAI,CAACS,QAAQ,CAAC,EAAE,CAAC,CAACG,KAAK,CAAC,0BAA0B,CAAC;EAC/D,IAAI,CAACA,KAAK,EAAE;IACX,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjB;EAEA,IAAIC,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC;EAE1B,IAAIA,KAAK,CAAC,CAAC,CAAC,CAAC5E,MAAM,KAAK,CAAC,EAAE;IAC1B6E,WAAW,GAAGA,WAAW,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;MACvD,OAAOA,IAAI,GAAGA,IAAI;IACnB,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACZ;EAEA,IAAIV,OAAO,GAAGW,QAAQ,CAACL,WAAW,EAAE,EAAE,CAAC;EACvC,IAAIzE,CAAC,GAAImE,OAAO,IAAI,EAAE,GAAI,IAAI;EAC9B,IAAIlE,CAAC,GAAIkE,OAAO,IAAI,CAAC,GAAI,IAAI;EAC7B,IAAIjE,CAAC,GAAGiE,OAAO,GAAG,IAAI;EAEtB,OAAO,CAACnE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACjB,CAAC;AAED5B,OAAO,CAACG,GAAG,CAACc,GAAG,GAAG,UAAUd,GAAG,EAAE;EAChC,IAAIuB,CAAC,GAAGvB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIwB,CAAC,GAAGxB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI4B,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACD,IAAI,CAACC,GAAG,CAACL,CAAC,EAAEC,CAAC,CAAC,EAAEC,CAAC,CAAC;EACrC,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACC,IAAI,CAACD,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC,EAAEC,CAAC,CAAC;EACrC,IAAI6E,MAAM,GAAI1E,GAAG,GAAGF,GAAI;EACxB,IAAI6E,SAAS;EACb,IAAIC,GAAG;EAEP,IAAIF,MAAM,GAAG,CAAC,EAAE;IACfC,SAAS,GAAG7E,GAAG,IAAI,CAAC,GAAG4E,MAAM,CAAC;EAC/B,CAAC,MAAM;IACNC,SAAS,GAAG,CAAC;EACd;EAEA,IAAID,MAAM,IAAI,CAAC,EAAE;IAChBE,GAAG,GAAG,CAAC;EACR,CAAC,MACD,IAAI5E,GAAG,KAAKL,CAAC,EAAE;IACdiF,GAAG,GAAI,CAAChF,CAAC,GAAGC,CAAC,IAAI6E,MAAM,GAAI,CAAC;EAC7B,CAAC,MACD,IAAI1E,GAAG,KAAKJ,CAAC,EAAE;IACdgF,GAAG,GAAG,CAAC,GAAG,CAAC/E,CAAC,GAAGF,CAAC,IAAI+E,MAAM;EAC3B,CAAC,MAAM;IACNE,GAAG,GAAG,CAAC,GAAG,CAACjF,CAAC,GAAGC,CAAC,IAAI8E,MAAM,GAAG,CAAC;EAC/B;EAEAE,GAAG,IAAI,CAAC;EACRA,GAAG,IAAI,CAAC;EAER,OAAO,CAACA,GAAG,GAAG,GAAG,EAAEF,MAAM,GAAG,GAAG,EAAEC,SAAS,GAAG,GAAG,CAAC;AAClD,CAAC;AAED1G,OAAO,CAACM,GAAG,CAACW,GAAG,GAAG,UAAUX,GAAG,EAAE;EAChC,IAAI4B,CAAC,GAAG5B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI6B,CAAC,GAAG7B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIoC,CAAC,GAAG,CAAC;EACT,IAAIyB,CAAC,GAAG,CAAC;EAET,IAAIhC,CAAC,GAAG,GAAG,EAAE;IACZO,CAAC,GAAG,GAAG,GAAGR,CAAC,GAAGC,CAAC;EAChB,CAAC,MAAM;IACNO,CAAC,GAAG,GAAG,GAAGR,CAAC,IAAI,GAAG,GAAGC,CAAC,CAAC;EACxB;EAEA,IAAIO,CAAC,GAAG,GAAG,EAAE;IACZyB,CAAC,GAAG,CAAChC,CAAC,GAAG,GAAG,GAAGO,CAAC,KAAK,GAAG,GAAGA,CAAC,CAAC;EAC9B;EAEA,OAAO,CAACpC,GAAG,CAAC,CAAC,CAAC,EAAEoC,CAAC,GAAG,GAAG,EAAEyB,CAAC,GAAG,GAAG,CAAC;AAClC,CAAC;AAEDnE,OAAO,CAACO,GAAG,CAACU,GAAG,GAAG,UAAUV,GAAG,EAAE;EAChC,IAAI2B,CAAC,GAAG3B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIgC,CAAC,GAAGhC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EAEpB,IAAImC,CAAC,GAAGR,CAAC,GAAGK,CAAC;EACb,IAAI4B,CAAC,GAAG,CAAC;EAET,IAAIzB,CAAC,GAAG,GAAG,EAAE;IACZyB,CAAC,GAAG,CAAC5B,CAAC,GAAGG,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;EACtB;EAEA,OAAO,CAACnC,GAAG,CAAC,CAAC,CAAC,EAAEmC,CAAC,GAAG,GAAG,EAAEyB,CAAC,GAAG,GAAG,CAAC;AAClC,CAAC;AAEDnE,OAAO,CAACiB,GAAG,CAACd,GAAG,GAAG,UAAUc,GAAG,EAAE;EAChC,IAAIgB,CAAC,GAAGhB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIU,CAAC,GAAGV,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EAEpB,IAAIyB,CAAC,KAAK,GAAG,EAAE;IACd,OAAO,CAACf,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,CAAC;EACnC;EAEA,IAAIiF,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpB,IAAI3C,EAAE,GAAIhC,CAAC,GAAG,CAAC,GAAI,CAAC;EACpB,IAAIM,CAAC,GAAG0B,EAAE,GAAG,CAAC;EACd,IAAItB,CAAC,GAAG,CAAC,GAAGJ,CAAC;EACb,IAAIsE,EAAE,GAAG,CAAC;EAEV,QAAQ/E,IAAI,CAACoC,KAAK,CAACD,EAAE,CAAC;IACrB,KAAK,CAAC;MACL2C,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAGrE,CAAC;MAAEqE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAE;IACxC,KAAK,CAAC;MACLA,IAAI,CAAC,CAAC,CAAC,GAAGjE,CAAC;MAAEiE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAE;IACxC,KAAK,CAAC;MACLA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAGrE,CAAC;MAAE;IACxC,KAAK,CAAC;MACLqE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAGjE,CAAC;MAAEiE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAE;IACxC,KAAK,CAAC;MACLA,IAAI,CAAC,CAAC,CAAC,GAAGrE,CAAC;MAAEqE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAE;IACxC;MACCA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MAAEA,IAAI,CAAC,CAAC,CAAC,GAAGjE,CAAC;EACvC;EAEAkE,EAAE,GAAG,CAAC,GAAG,GAAGnE,CAAC,IAAIf,CAAC;EAElB,OAAO,CACN,CAACe,CAAC,GAAGkE,IAAI,CAAC,CAAC,CAAC,GAAGC,EAAE,IAAI,GAAG,EACxB,CAACnE,CAAC,GAAGkE,IAAI,CAAC,CAAC,CAAC,GAAGC,EAAE,IAAI,GAAG,EACxB,CAACnE,CAAC,GAAGkE,IAAI,CAAC,CAAC,CAAC,GAAGC,EAAE,IAAI,GAAG,CACxB;AACF,CAAC;AAED7G,OAAO,CAACiB,GAAG,CAACV,GAAG,GAAG,UAAUU,GAAG,EAAE;EAChC,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIU,CAAC,GAAGV,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EAEpB,IAAIsB,CAAC,GAAGG,CAAC,GAAGf,CAAC,IAAI,GAAG,GAAGe,CAAC,CAAC;EACzB,IAAIyB,CAAC,GAAG,CAAC;EAET,IAAI5B,CAAC,GAAG,GAAG,EAAE;IACZ4B,CAAC,GAAGzB,CAAC,GAAGH,CAAC;EACV;EAEA,OAAO,CAACtB,GAAG,CAAC,CAAC,CAAC,EAAEkD,CAAC,GAAG,GAAG,EAAE5B,CAAC,GAAG,GAAG,CAAC;AAClC,CAAC;AAEDvC,OAAO,CAACiB,GAAG,CAACX,GAAG,GAAG,UAAUW,GAAG,EAAE;EAChC,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIU,CAAC,GAAGV,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EAEpB,IAAIkB,CAAC,GAAGR,CAAC,IAAI,GAAG,GAAGe,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC;EAC/B,IAAIR,CAAC,GAAG,CAAC;EAET,IAAIC,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,EAAE;IACvBD,CAAC,GAAGQ,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC;EAChB,CAAC,MACD,IAAIA,CAAC,IAAI,GAAG,IAAIA,CAAC,GAAG,GAAG,EAAE;IACxBD,CAAC,GAAGQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,CAAC;EACtB;EAEA,OAAO,CAAClB,GAAG,CAAC,CAAC,CAAC,EAAEiB,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AAClC,CAAC;AAEDnC,OAAO,CAACiB,GAAG,CAACT,GAAG,GAAG,UAAUS,GAAG,EAAE;EAChC,IAAIyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIU,CAAC,GAAGV,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIsB,CAAC,GAAGG,CAAC,GAAGf,CAAC,IAAI,GAAG,GAAGe,CAAC,CAAC;EACzB,OAAO,CAACzB,GAAG,CAAC,CAAC,CAAC,EAAE,CAACsB,CAAC,GAAGG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,GAAGH,CAAC,IAAI,GAAG,CAAC;AAC9C,CAAC;AAEDvC,OAAO,CAACQ,GAAG,CAACS,GAAG,GAAG,UAAUT,GAAG,EAAE;EAChC,IAAImC,CAAC,GAAGnC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAIoB,CAAC,GAAGpB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI+B,CAAC,GAAG,CAAC,GAAGX,CAAC;EACb,IAAIc,CAAC,GAAGH,CAAC,GAAGI,CAAC;EACb,IAAIhB,CAAC,GAAG,CAAC;EAET,IAAIe,CAAC,GAAG,CAAC,EAAE;IACVf,CAAC,GAAG,CAACY,CAAC,GAAGG,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;EACtB;EAEA,OAAO,CAAClC,GAAG,CAAC,CAAC,CAAC,EAAEkC,CAAC,GAAG,GAAG,EAAEf,CAAC,GAAG,GAAG,CAAC;AAClC,CAAC;AAED3B,OAAO,CAACkB,KAAK,CAACf,GAAG,GAAG,UAAUe,KAAK,EAAE;EACpC,OAAO,CAAEA,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAI,GAAG,EAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAI,GAAG,EAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAI,GAAG,CAAC;AACtF,CAAC;AAEDlB,OAAO,CAACG,GAAG,CAACe,KAAK,GAAG,UAAUf,GAAG,EAAE;EAClC,OAAO,CAAEA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,KAAK,EAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,KAAK,EAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,KAAK,CAAC;AAChF,CAAC;AAEDH,OAAO,CAACmB,IAAI,CAAChB,GAAG,GAAG,UAAUmF,IAAI,EAAE;EAClC,OAAO,CAACA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACvE,CAAC;AAEDtF,OAAO,CAACmB,IAAI,CAACb,GAAG,GAAGN,OAAO,CAACmB,IAAI,CAACZ,GAAG,GAAG,UAAU+E,IAAI,EAAE;EACrD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;AAEDtF,OAAO,CAACmB,IAAI,CAACX,GAAG,GAAG,UAAUW,IAAI,EAAE;EAClC,OAAO,CAAC,CAAC,EAAE,GAAG,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAEDnB,OAAO,CAACmB,IAAI,CAACV,IAAI,GAAG,UAAUU,IAAI,EAAE;EACnC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC;AAEDnB,OAAO,CAACmB,IAAI,CAACR,GAAG,GAAG,UAAUQ,IAAI,EAAE;EAClC,OAAO,CAACA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC;AAEDnB,OAAO,CAACmB,IAAI,CAACN,GAAG,GAAG,UAAUM,IAAI,EAAE;EAClC,IAAIyC,GAAG,GAAG9B,IAAI,CAAC0D,KAAK,CAACrE,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI;EAChD,IAAI0E,OAAO,GAAG,CAACjC,GAAG,IAAI,EAAE,KAAKA,GAAG,IAAI,CAAC,CAAC,GAAGA,GAAG;EAE5C,IAAIkC,MAAM,GAAGD,OAAO,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAC/C,OAAO,QAAQ,CAACC,SAAS,CAACH,MAAM,CAACxE,MAAM,CAAC,GAAGwE,MAAM;AAClD,CAAC;AAED9F,OAAO,CAACG,GAAG,CAACgB,IAAI,GAAG,UAAUhB,GAAG,EAAE;EACjC,IAAIyD,GAAG,GAAG,CAACzD,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;EACxC,OAAO,CAACyD,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}