{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport dbManager from \"../database/DatabaseManager\";\nimport { userDataAccess, foodDataAccess, consumptionDataAccess, nutrientDataAccess, ingredientDataAccess, mealTypeDataAccess } from \"../database/DataAccess\";\nimport { v4 as uuidv4 } from 'uuid';\nimport foodDatabaseImporter from \"../utils/FoodDatabaseImporter\";\nexport var initializeDatabase = function () {\n  var _ref = _asyncToGenerator(function* () {\n    try {\n      yield dbManager.init();\n      console.log('Database initialized successfully');\n    } catch (error) {\n      console.error('Error initializing database:', error);\n      throw error;\n    }\n  });\n  return function initializeDatabase() {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport var getDatabaseStats = function () {\n  var _ref2 = _asyncToGenerator(function* () {\n    try {\n      return yield dbManager.getDatabaseStats();\n    } catch (error) {\n      console.error('Error getting database stats:', error);\n      throw error;\n    }\n  });\n  return function getDatabaseStats() {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport var resetDatabase = function () {\n  var _ref3 = _asyncToGenerator(function* () {\n    try {\n      yield dbManager.resetDatabase();\n      console.log('Database reset successfully');\n    } catch (error) {\n      console.error('Error resetting database:', error);\n      throw error;\n    }\n  });\n  return function resetDatabase() {\n    return _ref3.apply(this, arguments);\n  };\n}();\nexport var backupDatabase = function () {\n  var _ref4 = _asyncToGenerator(function* () {\n    try {\n      return yield dbManager.backupDatabase();\n    } catch (error) {\n      console.error('Error backing up database:', error);\n      throw error;\n    }\n  });\n  return function backupDatabase() {\n    return _ref4.apply(this, arguments);\n  };\n}();\nexport var getCurrentUser = function () {\n  var _ref5 = _asyncToGenerator(function* () {\n    try {\n      var user = yield userDataAccess.getCurrentUser();\n      if (!user) {\n        user = yield userDataAccess.create({\n          id: uuidv4(),\n          name: 'Default User',\n          email: '<EMAIL>',\n          dark_mode_enabled: 1,\n          preferred_language: 'en',\n          measurement_unit: 'metric'\n        });\n      }\n      return user;\n    } catch (error) {\n      console.error('Error getting current user:', error);\n      throw error;\n    }\n  });\n  return function getCurrentUser() {\n    return _ref5.apply(this, arguments);\n  };\n}();\nexport var updateUserSettings = function () {\n  var _ref6 = _asyncToGenerator(function* (userId, settings) {\n    try {\n      var updateData = {};\n      if (settings.name !== undefined) {\n        updateData.name = settings.name;\n      }\n      if (settings.email !== undefined) {\n        updateData.email = settings.email;\n      }\n      if (settings.darkModeEnabled !== undefined) {\n        updateData.dark_mode_enabled = settings.darkModeEnabled ? 1 : 0;\n      }\n      if (settings.preferredLanguage !== undefined) {\n        updateData.preferred_language = settings.preferredLanguage;\n      }\n      if (settings.measurementUnit !== undefined) {\n        updateData.measurement_unit = settings.measurementUnit;\n      }\n      if (Object.keys(updateData).length > 0) {\n        yield userDataAccess.update(userId, updateData);\n      }\n      for (var _ref7 of Object.entries(settings)) {\n        var _ref8 = _slicedToArray(_ref7, 2);\n        var key = _ref8[0];\n        var value = _ref8[1];\n        if (!['name', 'email', 'darkModeEnabled', 'preferredLanguage', 'measurementUnit'].includes(key)) {\n          yield userDataAccess.saveSetting(userId, key, value);\n        }\n      }\n      return userDataAccess.getById(userId);\n    } catch (error) {\n      console.error('Error updating user settings:', error);\n      throw error;\n    }\n  });\n  return function updateUserSettings(_x, _x2) {\n    return _ref6.apply(this, arguments);\n  };\n}();\nexport var getFoodByBarcode = function () {\n  var _ref9 = _asyncToGenerator(function* (barcode) {\n    try {\n      var food = yield foodDataAccess.getByBarcode(barcode);\n      if (food) {\n        food.nutrients = yield foodDataAccess.getNutrients(food.id);\n        food.ingredients = yield foodDataAccess.getIngredients(food.id);\n      }\n      return food;\n    } catch (error) {\n      console.error('Error getting food by barcode:', error);\n      throw error;\n    }\n  });\n  return function getFoodByBarcode(_x3) {\n    return _ref9.apply(this, arguments);\n  };\n}();\nexport var searchFoods = function () {\n  var _ref0 = _asyncToGenerator(function* (query) {\n    var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;\n    try {\n      return yield foodDataAccess.search('name', query, {\n        limit: limit\n      });\n    } catch (error) {\n      console.error('Error searching foods:', error);\n      throw error;\n    }\n  });\n  return function searchFoods(_x4) {\n    return _ref0.apply(this, arguments);\n  };\n}();\nexport var saveFood = function () {\n  var _ref1 = _asyncToGenerator(function* (food) {\n    try {\n      var savedFood;\n      if (food.id) {\n        savedFood = yield foodDataAccess.update(food.id, food);\n      } else {\n        food.id = uuidv4();\n        savedFood = yield foodDataAccess.create(food);\n      }\n      if (food.nutrients && food.nutrients.length > 0) {\n        for (var nutrient of food.nutrients) {\n          yield foodDataAccess.saveNutrient(savedFood.id, nutrient.nutrient_id, nutrient.amount);\n        }\n      }\n      savedFood.nutrients = yield foodDataAccess.getNutrients(savedFood.id);\n      savedFood.ingredients = yield foodDataAccess.getIngredients(savedFood.id);\n      return savedFood;\n    } catch (error) {\n      console.error('Error saving food:', error);\n      throw error;\n    }\n  });\n  return function saveFood(_x5) {\n    return _ref1.apply(this, arguments);\n  };\n}();\nexport var getConsumptionsByDate = function () {\n  var _ref10 = _asyncToGenerator(function* (userId, date) {\n    try {\n      return yield consumptionDataAccess.getByDate(userId, date);\n    } catch (error) {\n      console.error('Error getting consumptions by date:', error);\n      throw error;\n    }\n  });\n  return function getConsumptionsByDate(_x6, _x7) {\n    return _ref10.apply(this, arguments);\n  };\n}();\nexport var saveConsumption = function () {\n  var _ref11 = _asyncToGenerator(function* (consumption) {\n    try {\n      var savedConsumption;\n      if (consumption.id) {\n        savedConsumption = yield consumptionDataAccess.update(consumption.id, consumption);\n      } else {\n        consumption.id = uuidv4();\n        savedConsumption = yield consumptionDataAccess.create(consumption);\n      }\n      return savedConsumption;\n    } catch (error) {\n      console.error('Error saving consumption:', error);\n      throw error;\n    }\n  });\n  return function saveConsumption(_x8) {\n    return _ref11.apply(this, arguments);\n  };\n}();\nexport var addConsumptionItem = function () {\n  var _ref12 = _asyncToGenerator(function* (consumptionId, foodId, quantity) {\n    var unit = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'g';\n    try {\n      return yield consumptionDataAccess.addItem(consumptionId, foodId, quantity, unit);\n    } catch (error) {\n      console.error('Error adding consumption item:', error);\n      throw error;\n    }\n  });\n  return function addConsumptionItem(_x9, _x0, _x1) {\n    return _ref12.apply(this, arguments);\n  };\n}();\nexport var getDailyNutritionSummary = function () {\n  var _ref13 = _asyncToGenerator(function* (userId, date) {\n    try {\n      return yield consumptionDataAccess.getDailyNutritionSummary(userId, date);\n    } catch (error) {\n      console.error('Error getting daily nutrition summary:', error);\n      throw error;\n    }\n  });\n  return function getDailyNutritionSummary(_x10, _x11) {\n    return _ref13.apply(this, arguments);\n  };\n}();\nexport var getAllNutrients = function () {\n  var _ref14 = _asyncToGenerator(function* () {\n    try {\n      return yield nutrientDataAccess.getAll();\n    } catch (error) {\n      console.error('Error getting all nutrients:', error);\n      throw error;\n    }\n  });\n  return function getAllNutrients() {\n    return _ref14.apply(this, arguments);\n  };\n}();\nexport var getMacronutrients = function () {\n  var _ref15 = _asyncToGenerator(function* () {\n    try {\n      return yield nutrientDataAccess.getMacronutrients();\n    } catch (error) {\n      console.error('Error getting macronutrients:', error);\n      throw error;\n    }\n  });\n  return function getMacronutrients() {\n    return _ref15.apply(this, arguments);\n  };\n}();\nexport var getMealTypes = function () {\n  var _ref16 = _asyncToGenerator(function* () {\n    try {\n      return yield mealTypeDataAccess.getAllOrdered();\n    } catch (error) {\n      console.error('Error getting meal types:', error);\n      throw error;\n    }\n  });\n  return function getMealTypes() {\n    return _ref16.apply(this, arguments);\n  };\n}();\nexport var getAllergens = function () {\n  var _ref17 = _asyncToGenerator(function* () {\n    try {\n      return yield ingredientDataAccess.getAllergens();\n    } catch (error) {\n      console.error('Error getting allergens:', error);\n      throw error;\n    }\n  });\n  return function getAllergens() {\n    return _ref17.apply(this, arguments);\n  };\n}();\nexport var getFavorites = function () {\n  var _ref18 = _asyncToGenerator(function* () {\n    try {\n      return yield foodDataAccess.getFavorites();\n    } catch (error) {\n      console.error('Error getting favorite foods:', error);\n      throw error;\n    }\n  });\n  return function getFavorites() {\n    return _ref18.apply(this, arguments);\n  };\n}();\nexport var getCustomFoods = function () {\n  var _ref19 = _asyncToGenerator(function* () {\n    try {\n      return yield foodDataAccess.getCustomFoods();\n    } catch (error) {\n      console.error('Error getting custom foods:', error);\n      throw error;\n    }\n  });\n  return function getCustomFoods() {\n    return _ref19.apply(this, arguments);\n  };\n}();\nexport var getNutrients = function () {\n  var _ref20 = _asyncToGenerator(function* () {\n    try {\n      return yield nutrientDataAccess.getAll();\n    } catch (error) {\n      console.error('Error getting nutrients:', error);\n      throw error;\n    }\n  });\n  return function getNutrients() {\n    return _ref20.apply(this, arguments);\n  };\n}();\nexport var isFoodDatabaseImported = function () {\n  var _ref21 = _asyncToGenerator(function* (source) {\n    try {\n      return yield dbManager.isFoodDatabaseImported(source);\n    } catch (error) {\n      console.error(`Error checking if ${source} database is imported:`, error);\n      return false;\n    }\n  });\n  return function isFoodDatabaseImported(_x12) {\n    return _ref21.apply(this, arguments);\n  };\n}();\nexport var importFoodDatabase = function () {\n  var _ref22 = _asyncToGenerator(function* (databaseName) {\n    var progressCallback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    try {\n      return yield foodDatabaseImporter.importFoodDatabase(databaseName, progressCallback);\n    } catch (error) {\n      console.error(`Error importing ${databaseName} database:`, error);\n      throw error;\n    }\n  });\n  return function importFoodDatabase(_x13) {\n    return _ref22.apply(this, arguments);\n  };\n}();\nexport var getFoodsBySource = function () {\n  var _ref23 = _asyncToGenerator(function* (source) {\n    var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;\n    var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    try {\n      return yield foodDataAccess.getBySource(source, limit, offset);\n    } catch (error) {\n      console.error(`Error getting foods by source ${source}:`, error);\n      throw error;\n    }\n  });\n  return function getFoodsBySource(_x14) {\n    return _ref23.apply(this, arguments);\n  };\n}();\nexport default {\n  initializeDatabase: initializeDatabase,\n  getDatabaseStats: getDatabaseStats,\n  resetDatabase: resetDatabase,\n  backupDatabase: backupDatabase,\n  getCurrentUser: getCurrentUser,\n  updateUserSettings: updateUserSettings,\n  getFoodByBarcode: getFoodByBarcode,\n  searchFoods: searchFoods,\n  saveFood: saveFood,\n  getConsumptionsByDate: getConsumptionsByDate,\n  saveConsumption: saveConsumption,\n  addConsumptionItem: addConsumptionItem,\n  getDailyNutritionSummary: getDailyNutritionSummary,\n  getAllNutrients: getAllNutrients,\n  getMacronutrients: getMacronutrients,\n  getMealTypes: getMealTypes,\n  getAllergens: getAllergens,\n  getFavorites: getFavorites,\n  getCustomFoods: getCustomFoods,\n  getNutrients: getNutrients,\n  isFoodDatabaseImported: isFoodDatabaseImported,\n  importFoodDatabase: importFoodDatabase,\n  getFoodsBySource: getFoodsBySource\n};", "map": {"version": 3, "names": ["db<PERSON><PERSON><PERSON>", "userDataAccess", "foodDataAccess", "consumptionDataAccess", "nutrientDataAccess", "ingredientDataAccess", "mealTypeDataAccess", "v4", "uuidv4", "foodDatabaseImporter", "initializeDatabase", "_ref", "_asyncToGenerator", "init", "console", "log", "error", "apply", "arguments", "getDatabaseStats", "_ref2", "resetDatabase", "_ref3", "backupDatabase", "_ref4", "getCurrentUser", "_ref5", "user", "create", "id", "name", "email", "dark_mode_enabled", "preferred_language", "measurement_unit", "updateUserSettings", "_ref6", "userId", "settings", "updateData", "undefined", "darkModeEnabled", "preferredLanguage", "measurementUnit", "Object", "keys", "length", "update", "_ref7", "entries", "_ref8", "_slicedToArray", "key", "value", "includes", "saveSetting", "getById", "_x", "_x2", "getFoodByBarcode", "_ref9", "barcode", "food", "getByBarcode", "nutrients", "getNutrients", "ingredients", "getIngredients", "_x3", "searchFoods", "_ref0", "query", "limit", "search", "_x4", "saveFood", "_ref1", "savedFood", "nutrient", "saveNutrient", "nutrient_id", "amount", "_x5", "getConsumptionsByDate", "_ref10", "date", "getByDate", "_x6", "_x7", "saveConsumption", "_ref11", "consumption", "savedConsumption", "_x8", "addConsumptionItem", "_ref12", "consumptionId", "foodId", "quantity", "unit", "addItem", "_x9", "_x0", "_x1", "getDailyNutritionSummary", "_ref13", "_x10", "_x11", "getAllNutrients", "_ref14", "getAll", "getMacronutrients", "_ref15", "getMealTypes", "_ref16", "getAllO<PERSON>red", "getAllergens", "_ref17", "getFavorites", "_ref18", "getCustomFoods", "_ref19", "_ref20", "isFoodDatabaseImported", "_ref21", "source", "_x12", "importFoodDatabase", "_ref22", "databaseName", "progressCallback", "_x13", "getFoodsBySource", "_ref23", "offset", "getBySource", "_x14"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/services/databaseService.js"], "sourcesContent": ["/**\n * Database Service for ZnüniZähler\n * Provides high-level database operations for the application\n */\n\nimport dbManager from '../database/DatabaseManager';\nimport {\n  userDataAccess,\n  foodDataAccess,\n  consumptionDataAccess,\n  nutrientDataAccess,\n  ingredientDataAccess,\n  mealTypeDataAccess\n} from '../database/DataAccess';\nimport { v4 as uuidv4 } from 'uuid';\nimport foodDatabaseImporter from '../utils/FoodDatabaseImporter';\n\n/**\n * Initialize the database\n * @returns {Promise<void>}\n */\nexport const initializeDatabase = async () => {\n  try {\n    await dbManager.init();\n    console.log('Database initialized successfully');\n  } catch (error) {\n    console.error('Error initializing database:', error);\n    throw error;\n  }\n};\n\n/**\n * Get database statistics\n * @returns {Promise<Object>} - Database statistics\n */\nexport const getDatabaseStats = async () => {\n  try {\n    return await dbManager.getDatabaseStats();\n  } catch (error) {\n    console.error('Error getting database stats:', error);\n    throw error;\n  }\n};\n\n/**\n * Reset the database (use with caution)\n * @returns {Promise<void>}\n */\nexport const resetDatabase = async () => {\n  try {\n    await dbManager.resetDatabase();\n    console.log('Database reset successfully');\n  } catch (error) {\n    console.error('Error resetting database:', error);\n    throw error;\n  }\n};\n\n/**\n * Backup the database\n * @returns {Promise<string>} - Backup file path\n */\nexport const backupDatabase = async () => {\n  try {\n    return await dbManager.backupDatabase();\n  } catch (error) {\n    console.error('Error backing up database:', error);\n    throw error;\n  }\n};\n\n/**\n * Get or create the current user\n * @returns {Promise<Object>} - User object\n */\nexport const getCurrentUser = async () => {\n  try {\n    let user = await userDataAccess.getCurrentUser();\n\n    if (!user) {\n      // Create default user\n      user = await userDataAccess.create({\n        id: uuidv4(),\n        name: 'Default User',\n        email: '<EMAIL>',\n        dark_mode_enabled: 1,\n        preferred_language: 'en',\n        measurement_unit: 'metric'\n      });\n    }\n\n    return user;\n  } catch (error) {\n    console.error('Error getting current user:', error);\n    throw error;\n  }\n};\n\n/**\n * Update user settings\n * @param {string} userId - User ID\n * @param {Object} settings - Settings to update\n * @returns {Promise<Object>} - Updated user\n */\nexport const updateUserSettings = async (userId, settings) => {\n  try {\n    const updateData = {};\n\n    // Handle core user properties\n    if (settings.name !== undefined) {\n      updateData.name = settings.name;\n    }\n\n    if (settings.email !== undefined) {\n      updateData.email = settings.email;\n    }\n\n    if (settings.darkModeEnabled !== undefined) {\n      updateData.dark_mode_enabled = settings.darkModeEnabled ? 1 : 0;\n    }\n\n    if (settings.preferredLanguage !== undefined) {\n      updateData.preferred_language = settings.preferredLanguage;\n    }\n\n    if (settings.measurementUnit !== undefined) {\n      updateData.measurement_unit = settings.measurementUnit;\n    }\n\n    // Update user if there are core properties to update\n    if (Object.keys(updateData).length > 0) {\n      await userDataAccess.update(userId, updateData);\n    }\n\n    // Handle custom settings\n    for (const [key, value] of Object.entries(settings)) {\n      if (!['name', 'email', 'darkModeEnabled', 'preferredLanguage', 'measurementUnit'].includes(key)) {\n        await userDataAccess.saveSetting(userId, key, value);\n      }\n    }\n\n    return userDataAccess.getById(userId);\n  } catch (error) {\n    console.error('Error updating user settings:', error);\n    throw error;\n  }\n};\n\n/**\n * Get food by barcode\n * @param {string} barcode - Barcode\n * @returns {Promise<Object|null>} - Food object or null\n */\nexport const getFoodByBarcode = async (barcode) => {\n  try {\n    const food = await foodDataAccess.getByBarcode(barcode);\n\n    if (food) {\n      // Get nutrients and ingredients\n      food.nutrients = await foodDataAccess.getNutrients(food.id);\n      food.ingredients = await foodDataAccess.getIngredients(food.id);\n    }\n\n    return food;\n  } catch (error) {\n    console.error('Error getting food by barcode:', error);\n    throw error;\n  }\n};\n\n/**\n * Search foods by name\n * @param {string} query - Search query\n * @param {number} limit - Maximum number of results\n * @returns {Promise<Array>} - Array of food objects\n */\nexport const searchFoods = async (query, limit = 20) => {\n  try {\n    return await foodDataAccess.search('name', query, { limit });\n  } catch (error) {\n    console.error('Error searching foods:', error);\n    throw error;\n  }\n};\n\n/**\n * Save food\n * @param {Object} food - Food data\n * @returns {Promise<Object>} - Saved food\n */\nexport const saveFood = async (food) => {\n  try {\n    let savedFood;\n\n    if (food.id) {\n      // Update existing food\n      savedFood = await foodDataAccess.update(food.id, food);\n    } else {\n      // Create new food\n      food.id = uuidv4();\n      savedFood = await foodDataAccess.create(food);\n    }\n\n    // Save nutrients if provided\n    if (food.nutrients && food.nutrients.length > 0) {\n      for (const nutrient of food.nutrients) {\n        await foodDataAccess.saveNutrient(savedFood.id, nutrient.nutrient_id, nutrient.amount);\n      }\n    }\n\n    // Get complete food with nutrients and ingredients\n    savedFood.nutrients = await foodDataAccess.getNutrients(savedFood.id);\n    savedFood.ingredients = await foodDataAccess.getIngredients(savedFood.id);\n\n    return savedFood;\n  } catch (error) {\n    console.error('Error saving food:', error);\n    throw error;\n  }\n};\n\n/**\n * Get consumptions by date\n * @param {string} userId - User ID\n * @param {string} date - Date (YYYY-MM-DD)\n * @returns {Promise<Array>} - Array of consumption objects\n */\nexport const getConsumptionsByDate = async (userId, date) => {\n  try {\n    return await consumptionDataAccess.getByDate(userId, date);\n  } catch (error) {\n    console.error('Error getting consumptions by date:', error);\n    throw error;\n  }\n};\n\n/**\n * Save consumption\n * @param {Object} consumption - Consumption data\n * @returns {Promise<Object>} - Saved consumption\n */\nexport const saveConsumption = async (consumption) => {\n  try {\n    let savedConsumption;\n\n    if (consumption.id) {\n      // Update existing consumption\n      savedConsumption = await consumptionDataAccess.update(consumption.id, consumption);\n    } else {\n      // Create new consumption\n      consumption.id = uuidv4();\n      savedConsumption = await consumptionDataAccess.create(consumption);\n    }\n\n    return savedConsumption;\n  } catch (error) {\n    console.error('Error saving consumption:', error);\n    throw error;\n  }\n};\n\n/**\n * Add consumption item\n * @param {string} consumptionId - Consumption ID\n * @param {string} foodId - Food ID\n * @param {number} quantity - Quantity\n * @param {string} unit - Unit\n * @returns {Promise<Object>} - Saved consumption item\n */\nexport const addConsumptionItem = async (consumptionId, foodId, quantity, unit = 'g') => {\n  try {\n    return await consumptionDataAccess.addItem(consumptionId, foodId, quantity, unit);\n  } catch (error) {\n    console.error('Error adding consumption item:', error);\n    throw error;\n  }\n};\n\n/**\n * Get daily nutrition summary\n * @param {string} userId - User ID\n * @param {string} date - Date (YYYY-MM-DD)\n * @returns {Promise<Object>} - Nutrition summary\n */\nexport const getDailyNutritionSummary = async (userId, date) => {\n  try {\n    return await consumptionDataAccess.getDailyNutritionSummary(userId, date);\n  } catch (error) {\n    console.error('Error getting daily nutrition summary:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all nutrients\n * @returns {Promise<Array>} - Array of nutrients\n */\nexport const getAllNutrients = async () => {\n  try {\n    return await nutrientDataAccess.getAll();\n  } catch (error) {\n    console.error('Error getting all nutrients:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all macronutrients\n * @returns {Promise<Array>} - Array of macronutrients\n */\nexport const getMacronutrients = async () => {\n  try {\n    return await nutrientDataAccess.getMacronutrients();\n  } catch (error) {\n    console.error('Error getting macronutrients:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all meal types\n * @returns {Promise<Array>} - Array of meal types\n */\nexport const getMealTypes = async () => {\n  try {\n    return await mealTypeDataAccess.getAllOrdered();\n  } catch (error) {\n    console.error('Error getting meal types:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all allergens\n * @returns {Promise<Array>} - Array of allergens\n */\nexport const getAllergens = async () => {\n  try {\n    return await ingredientDataAccess.getAllergens();\n  } catch (error) {\n    console.error('Error getting allergens:', error);\n    throw error;\n  }\n};\n\n/**\n * Get favorite foods\n * @returns {Promise<Array>} - Array of favorite foods\n */\nexport const getFavorites = async () => {\n  try {\n    return await foodDataAccess.getFavorites();\n  } catch (error) {\n    console.error('Error getting favorite foods:', error);\n    throw error;\n  }\n};\n\n/**\n * Get custom foods\n * @returns {Promise<Array>} - Array of custom foods\n */\nexport const getCustomFoods = async () => {\n  try {\n    return await foodDataAccess.getCustomFoods();\n  } catch (error) {\n    console.error('Error getting custom foods:', error);\n    throw error;\n  }\n};\n\n/**\n * Get nutrients\n * @returns {Promise<Array>} - Array of nutrients\n */\nexport const getNutrients = async () => {\n  try {\n    return await nutrientDataAccess.getAll();\n  } catch (error) {\n    console.error('Error getting nutrients:', error);\n    throw error;\n  }\n};\n\n/**\n * Check if a food database is imported\n * @param {string} source - Database source (e.g., 'USDA', 'FoodB')\n * @returns {Promise<boolean>} - Whether the database is imported\n */\nexport const isFoodDatabaseImported = async (source) => {\n  try {\n    return await dbManager.isFoodDatabaseImported(source);\n  } catch (error) {\n    console.error(`Error checking if ${source} database is imported:`, error);\n    return false;\n  }\n};\n\n/**\n * Import a food database\n * @param {string} databaseName - Name of the database file (without extension)\n * @param {Function} progressCallback - Callback function for progress updates\n * @returns {Promise<Object>} - Import statistics\n */\nexport const importFoodDatabase = async (databaseName, progressCallback = null) => {\n  try {\n    return await foodDatabaseImporter.importFoodDatabase(databaseName, progressCallback);\n  } catch (error) {\n    console.error(`Error importing ${databaseName} database:`, error);\n    throw error;\n  }\n};\n\n/**\n * Get foods by source\n * @param {string} source - Database source (e.g., 'USDA', 'FoodB')\n * @param {number} limit - Maximum number of results\n * @param {number} offset - Offset for pagination\n * @returns {Promise<Array>} - Array of food objects\n */\nexport const getFoodsBySource = async (source, limit = 20, offset = 0) => {\n  try {\n    return await foodDataAccess.getBySource(source, limit, offset);\n  } catch (error) {\n    console.error(`Error getting foods by source ${source}:`, error);\n    throw error;\n  }\n};\n\nexport default {\n  initializeDatabase,\n  getDatabaseStats,\n  resetDatabase,\n  backupDatabase,\n  getCurrentUser,\n  updateUserSettings,\n  getFoodByBarcode,\n  searchFoods,\n  saveFood,\n  getConsumptionsByDate,\n  saveConsumption,\n  addConsumptionItem,\n  getDailyNutritionSummary,\n  getAllNutrients,\n  getMacronutrients,\n  getMealTypes,\n  getAllergens,\n  getFavorites,\n  getCustomFoods,\n  getNutrients,\n  isFoodDatabaseImported,\n  importFoodDatabase,\n  getFoodsBySource\n};\n"], "mappings": ";;AAKA,OAAOA,SAAS;AAChB,SACEC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB;AAEpB,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,OAAOC,oBAAoB;AAM3B,OAAO,IAAMC,kBAAkB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;IAC5C,IAAI;MACF,MAAMZ,SAAS,CAACa,IAAI,CAAC,CAAC;MACtBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBARYN,kBAAkBA,CAAA;IAAA,OAAAC,IAAA,CAAAM,KAAA,OAAAC,SAAA;EAAA;AAAA,GAQ9B;AAMD,OAAO,IAAMC,gBAAgB;EAAA,IAAAC,KAAA,GAAAR,iBAAA,CAAG,aAAY;IAC1C,IAAI;MACF,aAAaZ,SAAS,CAACmB,gBAAgB,CAAC,CAAC;IAC3C,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYG,gBAAgBA,CAAA;IAAA,OAAAC,KAAA,CAAAH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO5B;AAMD,OAAO,IAAMG,aAAa;EAAA,IAAAC,KAAA,GAAAV,iBAAA,CAAG,aAAY;IACvC,IAAI;MACF,MAAMZ,SAAS,CAACqB,aAAa,CAAC,CAAC;MAC/BP,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBARYK,aAAaA,CAAA;IAAA,OAAAC,KAAA,CAAAL,KAAA,OAAAC,SAAA;EAAA;AAAA,GAQzB;AAMD,OAAO,IAAMK,cAAc;EAAA,IAAAC,KAAA,GAAAZ,iBAAA,CAAG,aAAY;IACxC,IAAI;MACF,aAAaZ,SAAS,CAACuB,cAAc,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYO,cAAcA,CAAA;IAAA,OAAAC,KAAA,CAAAP,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO1B;AAMD,OAAO,IAAMO,cAAc;EAAA,IAAAC,KAAA,GAAAd,iBAAA,CAAG,aAAY;IACxC,IAAI;MACF,IAAIe,IAAI,SAAS1B,cAAc,CAACwB,cAAc,CAAC,CAAC;MAEhD,IAAI,CAACE,IAAI,EAAE;QAETA,IAAI,SAAS1B,cAAc,CAAC2B,MAAM,CAAC;UACjCC,EAAE,EAAErB,MAAM,CAAC,CAAC;UACZsB,IAAI,EAAE,cAAc;UACpBC,KAAK,EAAE,kBAAkB;UACzBC,iBAAiB,EAAE,CAAC;UACpBC,kBAAkB,EAAE,IAAI;UACxBC,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ;MAEA,OAAOP,IAAI;IACb,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBArBYS,cAAcA,CAAA;IAAA,OAAAC,KAAA,CAAAT,KAAA,OAAAC,SAAA;EAAA;AAAA,GAqB1B;AAQD,OAAO,IAAMiB,kBAAkB;EAAA,IAAAC,KAAA,GAAAxB,iBAAA,CAAG,WAAOyB,MAAM,EAAEC,QAAQ,EAAK;IAC5D,IAAI;MACF,IAAMC,UAAU,GAAG,CAAC,CAAC;MAGrB,IAAID,QAAQ,CAACR,IAAI,KAAKU,SAAS,EAAE;QAC/BD,UAAU,CAACT,IAAI,GAAGQ,QAAQ,CAACR,IAAI;MACjC;MAEA,IAAIQ,QAAQ,CAACP,KAAK,KAAKS,SAAS,EAAE;QAChCD,UAAU,CAACR,KAAK,GAAGO,QAAQ,CAACP,KAAK;MACnC;MAEA,IAAIO,QAAQ,CAACG,eAAe,KAAKD,SAAS,EAAE;QAC1CD,UAAU,CAACP,iBAAiB,GAAGM,QAAQ,CAACG,eAAe,GAAG,CAAC,GAAG,CAAC;MACjE;MAEA,IAAIH,QAAQ,CAACI,iBAAiB,KAAKF,SAAS,EAAE;QAC5CD,UAAU,CAACN,kBAAkB,GAAGK,QAAQ,CAACI,iBAAiB;MAC5D;MAEA,IAAIJ,QAAQ,CAACK,eAAe,KAAKH,SAAS,EAAE;QAC1CD,UAAU,CAACL,gBAAgB,GAAGI,QAAQ,CAACK,eAAe;MACxD;MAGA,IAAIC,MAAM,CAACC,IAAI,CAACN,UAAU,CAAC,CAACO,MAAM,GAAG,CAAC,EAAE;QACtC,MAAM7C,cAAc,CAAC8C,MAAM,CAACV,MAAM,EAAEE,UAAU,CAAC;MACjD;MAGA,SAAAS,KAAA,IAA2BJ,MAAM,CAACK,OAAO,CAACX,QAAQ,CAAC,EAAE;QAAA,IAAAY,KAAA,GAAAC,cAAA,CAAAH,KAAA;QAAA,IAAzCI,GAAG,GAAAF,KAAA;QAAA,IAAEG,KAAK,GAAAH,KAAA;QACpB,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAACI,QAAQ,CAACF,GAAG,CAAC,EAAE;UAC/F,MAAMnD,cAAc,CAACsD,WAAW,CAAClB,MAAM,EAAEe,GAAG,EAAEC,KAAK,CAAC;QACtD;MACF;MAEA,OAAOpD,cAAc,CAACuD,OAAO,CAACnB,MAAM,CAAC;IACvC,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBA1CYmB,kBAAkBA,CAAAsB,EAAA,EAAAC,GAAA;IAAA,OAAAtB,KAAA,CAAAnB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA0C9B;AAOD,OAAO,IAAMyC,gBAAgB;EAAA,IAAAC,KAAA,GAAAhD,iBAAA,CAAG,WAAOiD,OAAO,EAAK;IACjD,IAAI;MACF,IAAMC,IAAI,SAAS5D,cAAc,CAAC6D,YAAY,CAACF,OAAO,CAAC;MAEvD,IAAIC,IAAI,EAAE;QAERA,IAAI,CAACE,SAAS,SAAS9D,cAAc,CAAC+D,YAAY,CAACH,IAAI,CAACjC,EAAE,CAAC;QAC3DiC,IAAI,CAACI,WAAW,SAAShE,cAAc,CAACiE,cAAc,CAACL,IAAI,CAACjC,EAAE,CAAC;MACjE;MAEA,OAAOiC,IAAI;IACb,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAfY2C,gBAAgBA,CAAAS,GAAA;IAAA,OAAAR,KAAA,CAAA3C,KAAA,OAAAC,SAAA;EAAA;AAAA,GAe5B;AAQD,OAAO,IAAMmD,WAAW;EAAA,IAAAC,KAAA,GAAA1D,iBAAA,CAAG,WAAO2D,KAAK,EAAiB;IAAA,IAAfC,KAAK,GAAAtD,SAAA,CAAA4B,MAAA,QAAA5B,SAAA,QAAAsB,SAAA,GAAAtB,SAAA,MAAG,EAAE;IACjD,IAAI;MACF,aAAahB,cAAc,CAACuE,MAAM,CAAC,MAAM,EAAEF,KAAK,EAAE;QAAEC,KAAK,EAALA;MAAM,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYqD,WAAWA,CAAAK,GAAA;IAAA,OAAAJ,KAAA,CAAArD,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOvB;AAOD,OAAO,IAAMyD,QAAQ;EAAA,IAAAC,KAAA,GAAAhE,iBAAA,CAAG,WAAOkD,IAAI,EAAK;IACtC,IAAI;MACF,IAAIe,SAAS;MAEb,IAAIf,IAAI,CAACjC,EAAE,EAAE;QAEXgD,SAAS,SAAS3E,cAAc,CAAC6C,MAAM,CAACe,IAAI,CAACjC,EAAE,EAAEiC,IAAI,CAAC;MACxD,CAAC,MAAM;QAELA,IAAI,CAACjC,EAAE,GAAGrB,MAAM,CAAC,CAAC;QAClBqE,SAAS,SAAS3E,cAAc,CAAC0B,MAAM,CAACkC,IAAI,CAAC;MAC/C;MAGA,IAAIA,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACE,SAAS,CAAClB,MAAM,GAAG,CAAC,EAAE;QAC/C,KAAK,IAAMgC,QAAQ,IAAIhB,IAAI,CAACE,SAAS,EAAE;UACrC,MAAM9D,cAAc,CAAC6E,YAAY,CAACF,SAAS,CAAChD,EAAE,EAAEiD,QAAQ,CAACE,WAAW,EAAEF,QAAQ,CAACG,MAAM,CAAC;QACxF;MACF;MAGAJ,SAAS,CAACb,SAAS,SAAS9D,cAAc,CAAC+D,YAAY,CAACY,SAAS,CAAChD,EAAE,CAAC;MACrEgD,SAAS,CAACX,WAAW,SAAShE,cAAc,CAACiE,cAAc,CAACU,SAAS,CAAChD,EAAE,CAAC;MAEzE,OAAOgD,SAAS;IAClB,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBA7BY2D,QAAQA,CAAAO,GAAA;IAAA,OAAAN,KAAA,CAAA3D,KAAA,OAAAC,SAAA;EAAA;AAAA,GA6BpB;AAQD,OAAO,IAAMiE,qBAAqB;EAAA,IAAAC,MAAA,GAAAxE,iBAAA,CAAG,WAAOyB,MAAM,EAAEgD,IAAI,EAAK;IAC3D,IAAI;MACF,aAAalF,qBAAqB,CAACmF,SAAS,CAACjD,MAAM,EAAEgD,IAAI,CAAC;IAC5D,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYmE,qBAAqBA,CAAAI,GAAA,EAAAC,GAAA;IAAA,OAAAJ,MAAA,CAAAnE,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOjC;AAOD,OAAO,IAAMuE,eAAe;EAAA,IAAAC,MAAA,GAAA9E,iBAAA,CAAG,WAAO+E,WAAW,EAAK;IACpD,IAAI;MACF,IAAIC,gBAAgB;MAEpB,IAAID,WAAW,CAAC9D,EAAE,EAAE;QAElB+D,gBAAgB,SAASzF,qBAAqB,CAAC4C,MAAM,CAAC4C,WAAW,CAAC9D,EAAE,EAAE8D,WAAW,CAAC;MACpF,CAAC,MAAM;QAELA,WAAW,CAAC9D,EAAE,GAAGrB,MAAM,CAAC,CAAC;QACzBoF,gBAAgB,SAASzF,qBAAqB,CAACyB,MAAM,CAAC+D,WAAW,CAAC;MACpE;MAEA,OAAOC,gBAAgB;IACzB,CAAC,CAAC,OAAO5E,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAlBYyE,eAAeA,CAAAI,GAAA;IAAA,OAAAH,MAAA,CAAAzE,KAAA,OAAAC,SAAA;EAAA;AAAA,GAkB3B;AAUD,OAAO,IAAM4E,kBAAkB;EAAA,IAAAC,MAAA,GAAAnF,iBAAA,CAAG,WAAOoF,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAiB;IAAA,IAAfC,IAAI,GAAAjF,SAAA,CAAA4B,MAAA,QAAA5B,SAAA,QAAAsB,SAAA,GAAAtB,SAAA,MAAG,GAAG;IAClF,IAAI;MACF,aAAaf,qBAAqB,CAACiG,OAAO,CAACJ,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,CAAC;IACnF,CAAC,CAAC,OAAOnF,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPY8E,kBAAkBA,CAAAO,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAR,MAAA,CAAA9E,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO9B;AAQD,OAAO,IAAMsF,wBAAwB;EAAA,IAAAC,MAAA,GAAA7F,iBAAA,CAAG,WAAOyB,MAAM,EAAEgD,IAAI,EAAK;IAC9D,IAAI;MACF,aAAalF,qBAAqB,CAACqG,wBAAwB,CAACnE,MAAM,EAAEgD,IAAI,CAAC;IAC3E,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYwF,wBAAwBA,CAAAE,IAAA,EAAAC,IAAA;IAAA,OAAAF,MAAA,CAAAxF,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOpC;AAMD,OAAO,IAAM0F,eAAe;EAAA,IAAAC,MAAA,GAAAjG,iBAAA,CAAG,aAAY;IACzC,IAAI;MACF,aAAaR,kBAAkB,CAAC0G,MAAM,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAO9F,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPY4F,eAAeA,CAAA;IAAA,OAAAC,MAAA,CAAA5F,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO3B;AAMD,OAAO,IAAM6F,iBAAiB;EAAA,IAAAC,MAAA,GAAApG,iBAAA,CAAG,aAAY;IAC3C,IAAI;MACF,aAAaR,kBAAkB,CAAC2G,iBAAiB,CAAC,CAAC;IACrD,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPY+F,iBAAiBA,CAAA;IAAA,OAAAC,MAAA,CAAA/F,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO7B;AAMD,OAAO,IAAM+F,YAAY;EAAA,IAAAC,MAAA,GAAAtG,iBAAA,CAAG,aAAY;IACtC,IAAI;MACF,aAAaN,kBAAkB,CAAC6G,aAAa,CAAC,CAAC;IACjD,CAAC,CAAC,OAAOnG,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYiG,YAAYA,CAAA;IAAA,OAAAC,MAAA,CAAAjG,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOxB;AAMD,OAAO,IAAMkG,YAAY;EAAA,IAAAC,MAAA,GAAAzG,iBAAA,CAAG,aAAY;IACtC,IAAI;MACF,aAAaP,oBAAoB,CAAC+G,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOpG,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYoG,YAAYA,CAAA;IAAA,OAAAC,MAAA,CAAApG,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOxB;AAMD,OAAO,IAAMoG,YAAY;EAAA,IAAAC,MAAA,GAAA3G,iBAAA,CAAG,aAAY;IACtC,IAAI;MACF,aAAaV,cAAc,CAACoH,YAAY,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYsG,YAAYA,CAAA;IAAA,OAAAC,MAAA,CAAAtG,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOxB;AAMD,OAAO,IAAMsG,cAAc;EAAA,IAAAC,MAAA,GAAA7G,iBAAA,CAAG,aAAY;IACxC,IAAI;MACF,aAAaV,cAAc,CAACsH,cAAc,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOxG,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYwG,cAAcA,CAAA;IAAA,OAAAC,MAAA,CAAAxG,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO1B;AAMD,OAAO,IAAM+C,YAAY;EAAA,IAAAyD,MAAA,GAAA9G,iBAAA,CAAG,aAAY;IACtC,IAAI;MACF,aAAaR,kBAAkB,CAAC0G,MAAM,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAO9F,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYiD,YAAYA,CAAA;IAAA,OAAAyD,MAAA,CAAAzG,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOxB;AAOD,OAAO,IAAMyG,sBAAsB;EAAA,IAAAC,MAAA,GAAAhH,iBAAA,CAAG,WAAOiH,MAAM,EAAK;IACtD,IAAI;MACF,aAAa7H,SAAS,CAAC2H,sBAAsB,CAACE,MAAM,CAAC;IACvD,CAAC,CAAC,OAAO7G,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,qBAAqB6G,MAAM,wBAAwB,EAAE7G,KAAK,CAAC;MACzE,OAAO,KAAK;IACd;EACF,CAAC;EAAA,gBAPY2G,sBAAsBA,CAAAG,IAAA;IAAA,OAAAF,MAAA,CAAA3G,KAAA,OAAAC,SAAA;EAAA;AAAA,GAOlC;AAQD,OAAO,IAAM6G,kBAAkB;EAAA,IAAAC,MAAA,GAAApH,iBAAA,CAAG,WAAOqH,YAAY,EAA8B;IAAA,IAA5BC,gBAAgB,GAAAhH,SAAA,CAAA4B,MAAA,QAAA5B,SAAA,QAAAsB,SAAA,GAAAtB,SAAA,MAAG,IAAI;IAC5E,IAAI;MACF,aAAaT,oBAAoB,CAACsH,kBAAkB,CAACE,YAAY,EAAEC,gBAAgB,CAAC;IACtF,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mBAAmBiH,YAAY,YAAY,EAAEjH,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPY+G,kBAAkBA,CAAAI,IAAA;IAAA,OAAAH,MAAA,CAAA/G,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO9B;AASD,OAAO,IAAMkH,gBAAgB;EAAA,IAAAC,MAAA,GAAAzH,iBAAA,CAAG,WAAOiH,MAAM,EAA6B;IAAA,IAA3BrD,KAAK,GAAAtD,SAAA,CAAA4B,MAAA,QAAA5B,SAAA,QAAAsB,SAAA,GAAAtB,SAAA,MAAG,EAAE;IAAA,IAAEoH,MAAM,GAAApH,SAAA,CAAA4B,MAAA,QAAA5B,SAAA,QAAAsB,SAAA,GAAAtB,SAAA,MAAG,CAAC;IACnE,IAAI;MACF,aAAahB,cAAc,CAACqI,WAAW,CAACV,MAAM,EAAErD,KAAK,EAAE8D,MAAM,CAAC;IAChE,CAAC,CAAC,OAAOtH,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC6G,MAAM,GAAG,EAAE7G,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBAPYoH,gBAAgBA,CAAAI,IAAA;IAAA,OAAAH,MAAA,CAAApH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAO5B;AAED,eAAe;EACbR,kBAAkB,EAAlBA,kBAAkB;EAClBS,gBAAgB,EAAhBA,gBAAgB;EAChBE,aAAa,EAAbA,aAAa;EACbE,cAAc,EAAdA,cAAc;EACdE,cAAc,EAAdA,cAAc;EACdU,kBAAkB,EAAlBA,kBAAkB;EAClBwB,gBAAgB,EAAhBA,gBAAgB;EAChBU,WAAW,EAAXA,WAAW;EACXM,QAAQ,EAARA,QAAQ;EACRQ,qBAAqB,EAArBA,qBAAqB;EACrBM,eAAe,EAAfA,eAAe;EACfK,kBAAkB,EAAlBA,kBAAkB;EAClBU,wBAAwB,EAAxBA,wBAAwB;EACxBI,eAAe,EAAfA,eAAe;EACfG,iBAAiB,EAAjBA,iBAAiB;EACjBE,YAAY,EAAZA,YAAY;EACZG,YAAY,EAAZA,YAAY;EACZE,YAAY,EAAZA,YAAY;EACZE,cAAc,EAAdA,cAAc;EACdvD,YAAY,EAAZA,YAAY;EACZ0D,sBAAsB,EAAtBA,sBAAsB;EACtBI,kBAAkB,EAAlBA,kBAAkB;EAClBK,gBAAgB,EAAhBA;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}