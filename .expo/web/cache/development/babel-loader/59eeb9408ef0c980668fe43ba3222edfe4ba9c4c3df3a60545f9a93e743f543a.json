{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Card, Text, Button, List, ActivityIndicator, Dialog, Portal } from 'react-native-paper';\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport { isSyncNeeded, syncData, getAvailableBackups, restoreFromBackup, deleteBackup } from \"../services/syncService\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SyncManager = function SyncManager() {\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isSyncing = _useState2[0],\n    setIsSyncing = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    lastSync = _useState4[0],\n    setLastSync = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    syncNeeded = _useState6[0],\n    setSyncNeeded = _useState6[1];\n  var _useState7 = useState([]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    backups = _useState8[0],\n    setBackups = _useState8[1];\n  var _useState9 = useState(false),\n    _useState0 = _slicedToArray(_useState9, 2),\n    showBackups = _useState0[0],\n    setShowBackups = _useState0[1];\n  var _useState1 = useState(null),\n    _useState10 = _slicedToArray(_useState1, 2),\n    selectedBackup = _useState10[0],\n    setSelectedBackup = _useState10[1];\n  var _useState11 = useState(false),\n    _useState12 = _slicedToArray(_useState11, 2),\n    showRestoreDialog = _useState12[0],\n    setShowRestoreDialog = _useState12[1];\n  useEffect(function () {\n    var checkSyncStatus = function () {\n      var _ref = _asyncToGenerator(function* () {\n        try {\n          var needsSync = yield isSyncNeeded();\n          setSyncNeeded(needsSync);\n        } catch (error) {\n          console.error('Error checking sync status:', error);\n        }\n      });\n      return function checkSyncStatus() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    checkSyncStatus();\n  }, []);\n  useEffect(function () {\n    var loadBackups = function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        try {\n          var availableBackups = yield getAvailableBackups();\n          setBackups(availableBackups);\n        } catch (error) {\n          console.error('Error loading backups:', error);\n        }\n      });\n      return function loadBackups() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    if (showBackups) {\n      loadBackups();\n    }\n  }, [showBackups]);\n  var handleSync = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      try {\n        setIsSyncing(true);\n        var result = yield syncData();\n        setLastSync(new Date(result.timestamp));\n        setSyncNeeded(false);\n        Alert.alert('Success', result.message);\n      } catch (error) {\n        console.error('Error syncing data:', error);\n        Alert.alert('Error', 'Failed to sync data');\n      } finally {\n        setIsSyncing(false);\n      }\n    });\n    return function handleSync() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var handleRestore = function () {\n    var _ref4 = _asyncToGenerator(function* () {\n      if (!selectedBackup) return;\n      try {\n        setIsSyncing(true);\n        setShowRestoreDialog(false);\n        yield restoreFromBackup(selectedBackup.path);\n        Alert.alert('Success', 'Database restored successfully');\n      } catch (error) {\n        console.error('Error restoring database:', error);\n        Alert.alert('Error', 'Failed to restore database');\n      } finally {\n        setIsSyncing(false);\n        setSelectedBackup(null);\n      }\n    });\n    return function handleRestore() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var handleDeleteBackup = function () {\n    var _ref5 = _asyncToGenerator(function* (backup) {\n      try {\n        yield deleteBackup(backup.path);\n        var availableBackups = yield getAvailableBackups();\n        setBackups(availableBackups);\n        Alert.alert('Success', 'Backup deleted successfully');\n      } catch (error) {\n        console.error('Error deleting backup:', error);\n        Alert.alert('Error', 'Failed to delete backup');\n      }\n    });\n    return function handleDeleteBackup(_x) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  var formatDate = function formatDate(date) {\n    if (!date) return 'Never';\n    return date.toLocaleString();\n  };\n  var formatFileSize = function formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';\n    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';\n  };\n  var renderBackupItem = function renderBackupItem(_ref6) {\n    var item = _ref6.item;\n    return _jsx(List.Item, {\n      title: formatDate(item.timestamp),\n      description: `Size: ${formatFileSize(item.size)}`,\n      left: function left(props) {\n        return _jsx(List.Icon, _objectSpread(_objectSpread({}, props), {}, {\n          icon: \"database\"\n        }));\n      },\n      right: function right(props) {\n        return _jsxs(View, {\n          style: styles.backupActions,\n          children: [_jsx(Button, {\n            onPress: function onPress() {\n              setSelectedBackup(item);\n              setShowRestoreDialog(true);\n            },\n            children: \"Restore\"\n          }), _jsx(Button, {\n            onPress: function onPress() {\n              return handleDeleteBackup(item);\n            },\n            children: \"Delete\"\n          })]\n        });\n      }\n    });\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsxs(Card, {\n      style: styles.card,\n      children: [_jsx(Card.Title, {\n        title: \"Data Synchronization\"\n      }), _jsxs(Card.Content, {\n        children: [_jsxs(Text, {\n          style: styles.syncStatus,\n          children: [\"Last sync: \", formatDate(lastSync)]\n        }), _jsxs(Text, {\n          style: styles.syncStatus,\n          children: [\"Sync status: \", syncNeeded ? 'Sync needed' : 'Up to date']\n        }), _jsxs(View, {\n          style: styles.buttonContainer,\n          children: [_jsx(Button, {\n            mode: \"contained\",\n            onPress: handleSync,\n            loading: isSyncing,\n            disabled: isSyncing,\n            style: styles.button,\n            children: isSyncing ? 'Syncing...' : 'Sync Now'\n          }), _jsx(Button, {\n            mode: \"outlined\",\n            onPress: function onPress() {\n              return setShowBackups(!showBackups);\n            },\n            style: styles.button,\n            children: showBackups ? 'Hide Backups' : 'Show Backups'\n          })]\n        }), showBackups && _jsxs(View, {\n          style: styles.backupsContainer,\n          children: [_jsx(Text, {\n            style: styles.backupsTitle,\n            children: \"Available Backups\"\n          }), backups.length === 0 ? _jsx(Text, {\n            style: styles.noBackupsText,\n            children: \"No backups available\"\n          }) : _jsx(FlatList, {\n            data: backups,\n            renderItem: renderBackupItem,\n            keyExtractor: function keyExtractor(item) {\n              return item.filename;\n            },\n            style: styles.backupsList\n          })]\n        })]\n      })]\n    }), _jsx(Portal, {\n      children: _jsxs(Dialog, {\n        visible: showRestoreDialog,\n        onDismiss: function onDismiss() {\n          return setShowRestoreDialog(false);\n        },\n        children: [_jsx(Dialog.Title, {\n          children: \"Confirm Restore\"\n        }), _jsxs(Dialog.Content, {\n          children: [_jsx(Text, {\n            children: \"Are you sure you want to restore the database from backup? This will replace your current data with the backup data.\"\n          }), selectedBackup && _jsxs(Text, {\n            style: styles.backupInfo,\n            children: [\"Backup date: \", formatDate(selectedBackup.timestamp)]\n          })]\n        }), _jsxs(Dialog.Actions, {\n          children: [_jsx(Button, {\n            onPress: function onPress() {\n              return setShowRestoreDialog(false);\n            },\n            children: \"Cancel\"\n          }), _jsx(Button, {\n            onPress: handleRestore,\n            children: \"Restore\"\n          })]\n        })]\n      })\n    }), isSyncing && _jsxs(View, {\n      style: styles.loadingOverlay,\n      children: [_jsx(ActivityIndicator, {\n        size: \"large\",\n        color: theme.colors.primary\n      }), _jsx(Text, {\n        style: styles.loadingText,\n        children: \"Processing...\"\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  card: {\n    margin: 16,\n    elevation: 2\n  },\n  syncStatus: {\n    marginBottom: 8\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 16\n  },\n  button: {\n    width: '48%'\n  },\n  backupsContainer: {\n    marginTop: 24\n  },\n  backupsTitle: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginBottom: 8\n  },\n  noBackupsText: {\n    fontStyle: 'italic',\n    marginTop: 8\n  },\n  backupsList: {\n    maxHeight: 300\n  },\n  backupActions: {\n    flexDirection: 'row'\n  },\n  backupInfo: {\n    marginTop: 8,\n    fontStyle: 'italic'\n  },\n  loadingOverlay: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    justifyContent: 'center',\n    alignItems: 'center'\n  }),\n  loadingText: {\n    color: 'white',\n    marginTop: 16\n  }\n});\nexport default SyncManager;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StyleSheet", "View", "FlatList", "<PERSON><PERSON>", "Card", "Text", "<PERSON><PERSON>", "List", "ActivityIndicator", "Dialog", "Portal", "useTheme", "isSyncNeeded", "syncData", "getAvailableBackups", "restoreFromBackup", "deleteBackup", "jsx", "_jsx", "jsxs", "_jsxs", "SyncManager", "_useTheme", "theme", "_useState", "_useState2", "_slicedToArray", "isSyncing", "setIsSyncing", "_useState3", "_useState4", "lastSync", "setLastSync", "_useState5", "_useState6", "syncNeeded", "setSyncNeeded", "_useState7", "_useState8", "backups", "setBackups", "_useState9", "_useState0", "showBackups", "setShowBackups", "_useState1", "_useState10", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBackup", "_useState11", "_useState12", "showRestoreDialog", "setShowRestoreDialog", "checkSyncStatus", "_ref", "_asyncToGenerator", "needsSync", "error", "console", "apply", "arguments", "loadBackups", "_ref2", "availableBackups", "handleSync", "_ref3", "result", "Date", "timestamp", "alert", "message", "handleRestore", "_ref4", "path", "handleDeleteBackup", "_ref5", "backup", "_x", "formatDate", "date", "toLocaleString", "formatFileSize", "bytes", "toFixed", "renderBackupItem", "_ref6", "item", "<PERSON><PERSON>", "title", "description", "size", "left", "props", "Icon", "_objectSpread", "icon", "right", "style", "styles", "backupActions", "children", "onPress", "container", "card", "Title", "Content", "syncStatus", "buttonContainer", "mode", "loading", "disabled", "button", "backups<PERSON><PERSON><PERSON>", "backups<PERSON><PERSON><PERSON>", "length", "noBackupsText", "data", "renderItem", "keyExtractor", "filename", "backupsList", "visible", "on<PERSON><PERSON><PERSON>", "backupInfo", "Actions", "loadingOverlay", "color", "colors", "primary", "loadingText", "create", "flex", "margin", "elevation", "marginBottom", "flexDirection", "justifyContent", "marginTop", "width", "fontSize", "fontWeight", "fontStyle", "maxHeight", "absoluteFillObject", "backgroundColor", "alignItems"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/components/SyncManager.js"], "sourcesContent": ["/**\n * Sync Manager Component for ZnüniZähler\n * Manages data synchronization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { StyleSheet, View, FlatList, Alert } from 'react-native';\nimport { Card, Text, Button, List, ActivityIndicator, Dialog, Portal } from 'react-native-paper';\nimport { useTheme } from '../theme/ThemeProvider';\nimport { isSyncNeeded, syncData, getAvailableBackups, restoreFromBackup, deleteBackup } from '../services/syncService';\n\n/**\n * Sync Manager Component\n * @param {Object} props - Component props\n * @returns {JSX.Element} - Sync manager component\n */\nconst SyncManager = () => {\n  const { theme } = useTheme();\n  const [isSyncing, setIsSyncing] = useState(false);\n  const [lastSync, setLastSync] = useState(null);\n  const [syncNeeded, setSyncNeeded] = useState(false);\n  const [backups, setBackups] = useState([]);\n  const [showBackups, setShowBackups] = useState(false);\n  const [selectedBackup, setSelectedBackup] = useState(null);\n  const [showRestoreDialog, setShowRestoreDialog] = useState(false);\n  \n  // Check sync status\n  useEffect(() => {\n    const checkSyncStatus = async () => {\n      try {\n        const needsSync = await isSyncNeeded();\n        setSyncNeeded(needsSync);\n      } catch (error) {\n        console.error('Error checking sync status:', error);\n      }\n    };\n    \n    checkSyncStatus();\n  }, []);\n  \n  // Load backups\n  useEffect(() => {\n    const loadBackups = async () => {\n      try {\n        const availableBackups = await getAvailableBackups();\n        setBackups(availableBackups);\n      } catch (error) {\n        console.error('Error loading backups:', error);\n      }\n    };\n    \n    if (showBackups) {\n      loadBackups();\n    }\n  }, [showBackups]);\n  \n  // Handle sync\n  const handleSync = async () => {\n    try {\n      setIsSyncing(true);\n      \n      const result = await syncData();\n      \n      setLastSync(new Date(result.timestamp));\n      setSyncNeeded(false);\n      \n      Alert.alert('Success', result.message);\n    } catch (error) {\n      console.error('Error syncing data:', error);\n      Alert.alert('Error', 'Failed to sync data');\n    } finally {\n      setIsSyncing(false);\n    }\n  };\n  \n  // Handle restore\n  const handleRestore = async () => {\n    if (!selectedBackup) return;\n    \n    try {\n      setIsSyncing(true);\n      setShowRestoreDialog(false);\n      \n      await restoreFromBackup(selectedBackup.path);\n      \n      Alert.alert('Success', 'Database restored successfully');\n    } catch (error) {\n      console.error('Error restoring database:', error);\n      Alert.alert('Error', 'Failed to restore database');\n    } finally {\n      setIsSyncing(false);\n      setSelectedBackup(null);\n    }\n  };\n  \n  // Handle delete backup\n  const handleDeleteBackup = async (backup) => {\n    try {\n      await deleteBackup(backup.path);\n      \n      // Refresh backups list\n      const availableBackups = await getAvailableBackups();\n      setBackups(availableBackups);\n      \n      Alert.alert('Success', 'Backup deleted successfully');\n    } catch (error) {\n      console.error('Error deleting backup:', error);\n      Alert.alert('Error', 'Failed to delete backup');\n    }\n  };\n  \n  // Format date\n  const formatDate = (date) => {\n    if (!date) return 'Never';\n    \n    return date.toLocaleString();\n  };\n  \n  // Format file size\n  const formatFileSize = (bytes) => {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';\n    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';\n  };\n  \n  // Render backup item\n  const renderBackupItem = ({ item }) => (\n    <List.Item\n      title={formatDate(item.timestamp)}\n      description={`Size: ${formatFileSize(item.size)}`}\n      left={props => <List.Icon {...props} icon=\"database\" />}\n      right={props => (\n        <View style={styles.backupActions}>\n          <Button\n            onPress={() => {\n              setSelectedBackup(item);\n              setShowRestoreDialog(true);\n            }}\n          >\n            Restore\n          </Button>\n          <Button\n            onPress={() => handleDeleteBackup(item)}\n          >\n            Delete\n          </Button>\n        </View>\n      )}\n    />\n  );\n  \n  return (\n    <View style={styles.container}>\n      <Card style={styles.card}>\n        <Card.Title title=\"Data Synchronization\" />\n        <Card.Content>\n          <Text style={styles.syncStatus}>\n            Last sync: {formatDate(lastSync)}\n          </Text>\n          \n          <Text style={styles.syncStatus}>\n            Sync status: {syncNeeded ? 'Sync needed' : 'Up to date'}\n          </Text>\n          \n          <View style={styles.buttonContainer}>\n            <Button\n              mode=\"contained\"\n              onPress={handleSync}\n              loading={isSyncing}\n              disabled={isSyncing}\n              style={styles.button}\n            >\n              {isSyncing ? 'Syncing...' : 'Sync Now'}\n            </Button>\n            \n            <Button\n              mode=\"outlined\"\n              onPress={() => setShowBackups(!showBackups)}\n              style={styles.button}\n            >\n              {showBackups ? 'Hide Backups' : 'Show Backups'}\n            </Button>\n          </View>\n          \n          {showBackups && (\n            <View style={styles.backupsContainer}>\n              <Text style={styles.backupsTitle}>Available Backups</Text>\n              \n              {backups.length === 0 ? (\n                <Text style={styles.noBackupsText}>No backups available</Text>\n              ) : (\n                <FlatList\n                  data={backups}\n                  renderItem={renderBackupItem}\n                  keyExtractor={(item) => item.filename}\n                  style={styles.backupsList}\n                />\n              )}\n            </View>\n          )}\n        </Card.Content>\n      </Card>\n      \n      <Portal>\n        <Dialog\n          visible={showRestoreDialog}\n          onDismiss={() => setShowRestoreDialog(false)}\n        >\n          <Dialog.Title>Confirm Restore</Dialog.Title>\n          <Dialog.Content>\n            <Text>\n              Are you sure you want to restore the database from backup?\n              This will replace your current data with the backup data.\n            </Text>\n            {selectedBackup && (\n              <Text style={styles.backupInfo}>\n                Backup date: {formatDate(selectedBackup.timestamp)}\n              </Text>\n            )}\n          </Dialog.Content>\n          <Dialog.Actions>\n            <Button onPress={() => setShowRestoreDialog(false)}>Cancel</Button>\n            <Button onPress={handleRestore}>Restore</Button>\n          </Dialog.Actions>\n        </Dialog>\n      </Portal>\n      \n      {isSyncing && (\n        <View style={styles.loadingOverlay}>\n          <ActivityIndicator size=\"large\" color={theme.colors.primary} />\n          <Text style={styles.loadingText}>Processing...</Text>\n        </View>\n      )}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  card: {\n    margin: 16,\n    elevation: 2,\n  },\n  syncStatus: {\n    marginBottom: 8,\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginTop: 16,\n  },\n  button: {\n    width: '48%',\n  },\n  backupsContainer: {\n    marginTop: 24,\n  },\n  backupsTitle: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginBottom: 8,\n  },\n  noBackupsText: {\n    fontStyle: 'italic',\n    marginTop: 8,\n  },\n  backupsList: {\n    maxHeight: 300,\n  },\n  backupActions: {\n    flexDirection: 'row',\n  },\n  backupInfo: {\n    marginTop: 8,\n    fontStyle: 'italic',\n  },\n  loadingOverlay: {\n    ...StyleSheet.absoluteFillObject,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  loadingText: {\n    color: 'white',\n    marginTop: 16,\n  },\n});\n\nexport default SyncManager;\n"], "mappings": ";;;;;AAKA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,KAAA;AAEnD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,MAAM,QAAQ,oBAAoB;AAChG,SAASC,QAAQ;AACjB,SAASC,YAAY,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,YAAY;AAAkC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAOvH,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EACxB,IAAAC,SAAA,GAAkBX,QAAQ,CAAC,CAAC;IAApBY,KAAK,GAAAD,SAAA,CAALC,KAAK;EACb,IAAAC,SAAA,GAAkC1B,QAAQ,CAAC,KAAK,CAAC;IAAA2B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA1CG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAgC/B,QAAQ,CAAC,IAAI,CAAC;IAAAgC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAvCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAAoCnC,QAAQ,CAAC,KAAK,CAAC;IAAAoC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA5CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAA8BvC,QAAQ,CAAC,EAAE,CAAC;IAAAwC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAAnCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAsC3C,QAAQ,CAAC,KAAK,CAAC;IAAA4C,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAA9CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAClC,IAAAG,UAAA,GAA4C/C,QAAQ,CAAC,IAAI,CAAC;IAAAgD,WAAA,GAAApB,cAAA,CAAAmB,UAAA;IAAnDE,cAAc,GAAAD,WAAA;IAAEE,iBAAiB,GAAAF,WAAA;EACxC,IAAAG,WAAA,GAAkDnD,QAAQ,CAAC,KAAK,CAAC;IAAAoD,WAAA,GAAAxB,cAAA,CAAAuB,WAAA;IAA1DE,iBAAiB,GAAAD,WAAA;IAAEE,oBAAoB,GAAAF,WAAA;EAG9CnD,SAAS,CAAC,YAAM;IACd,IAAMsD,eAAe;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;QAClC,IAAI;UACF,IAAMC,SAAS,SAAS5C,YAAY,CAAC,CAAC;UACtCwB,aAAa,CAACoB,SAAS,CAAC;QAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF,CAAC;MAAA,gBAPKJ,eAAeA,CAAA;QAAA,OAAAC,IAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;IAAA,GAOpB;IAEDP,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAGNtD,SAAS,CAAC,YAAM;IACd,IAAM8D,WAAW;MAAA,IAAAC,KAAA,GAAAP,iBAAA,CAAG,aAAY;QAC9B,IAAI;UACF,IAAMQ,gBAAgB,SAASjD,mBAAmB,CAAC,CAAC;UACpD0B,UAAU,CAACuB,gBAAgB,CAAC;QAC9B,CAAC,CAAC,OAAON,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MAAA,gBAPKI,WAAWA,CAAA;QAAA,OAAAC,KAAA,CAAAH,KAAA,OAAAC,SAAA;MAAA;IAAA,GAOhB;IAED,IAAIjB,WAAW,EAAE;MACfkB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAClB,WAAW,CAAC,CAAC;EAGjB,IAAMqB,UAAU;IAAA,IAAAC,KAAA,GAAAV,iBAAA,CAAG,aAAY;MAC7B,IAAI;QACF3B,YAAY,CAAC,IAAI,CAAC;QAElB,IAAMsC,MAAM,SAASrD,QAAQ,CAAC,CAAC;QAE/BmB,WAAW,CAAC,IAAImC,IAAI,CAACD,MAAM,CAACE,SAAS,CAAC,CAAC;QACvChC,aAAa,CAAC,KAAK,CAAC;QAEpBjC,KAAK,CAACkE,KAAK,CAAC,SAAS,EAAEH,MAAM,CAACI,OAAO,CAAC;MACxC,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CtD,KAAK,CAACkE,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC;MAC7C,CAAC,SAAS;QACRzC,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBAhBKoC,UAAUA,CAAA;MAAA,OAAAC,KAAA,CAAAN,KAAA,OAAAC,SAAA;IAAA;EAAA,GAgBf;EAGD,IAAMW,aAAa;IAAA,IAAAC,KAAA,GAAAjB,iBAAA,CAAG,aAAY;MAChC,IAAI,CAACR,cAAc,EAAE;MAErB,IAAI;QACFnB,YAAY,CAAC,IAAI,CAAC;QAClBwB,oBAAoB,CAAC,KAAK,CAAC;QAE3B,MAAMrC,iBAAiB,CAACgC,cAAc,CAAC0B,IAAI,CAAC;QAE5CtE,KAAK,CAACkE,KAAK,CAAC,SAAS,EAAE,gCAAgC,CAAC;MAC1D,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDtD,KAAK,CAACkE,KAAK,CAAC,OAAO,EAAE,4BAA4B,CAAC;MACpD,CAAC,SAAS;QACRzC,YAAY,CAAC,KAAK,CAAC;QACnBoB,iBAAiB,CAAC,IAAI,CAAC;MACzB;IACF,CAAC;IAAA,gBAjBKuB,aAAaA,CAAA;MAAA,OAAAC,KAAA,CAAAb,KAAA,OAAAC,SAAA;IAAA;EAAA,GAiBlB;EAGD,IAAMc,kBAAkB;IAAA,IAAAC,KAAA,GAAApB,iBAAA,CAAG,WAAOqB,MAAM,EAAK;MAC3C,IAAI;QACF,MAAM5D,YAAY,CAAC4D,MAAM,CAACH,IAAI,CAAC;QAG/B,IAAMV,gBAAgB,SAASjD,mBAAmB,CAAC,CAAC;QACpD0B,UAAU,CAACuB,gBAAgB,CAAC;QAE5B5D,KAAK,CAACkE,KAAK,CAAC,SAAS,EAAE,6BAA6B,CAAC;MACvD,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CtD,KAAK,CAACkE,KAAK,CAAC,OAAO,EAAE,yBAAyB,CAAC;MACjD;IACF,CAAC;IAAA,gBAbKK,kBAAkBA,CAAAG,EAAA;MAAA,OAAAF,KAAA,CAAAhB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAavB;EAGD,IAAMkB,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IAEzB,OAAOA,IAAI,CAACC,cAAc,CAAC,CAAC;EAC9B,CAAC;EAGD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAK,EAAK;IAChC,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;IACjE,OAAO,CAACD,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;EACnD,CAAC;EAGD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,KAAA;IAAA,IAAMC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IAAA,OAC9BpE,IAAA,CAACX,IAAI,CAACgF,IAAI;MACRC,KAAK,EAAEV,UAAU,CAACQ,IAAI,CAAClB,SAAS,CAAE;MAClCqB,WAAW,EAAE,SAASR,cAAc,CAACK,IAAI,CAACI,IAAI,CAAC,EAAG;MAClDC,IAAI,EAAE,SAANA,IAAIA,CAAEC,KAAK;QAAA,OAAI1E,IAAA,CAACX,IAAI,CAACsF,IAAI,EAAAC,aAAA,CAAAA,aAAA,KAAKF,KAAK;UAAEG,IAAI,EAAC;QAAU,EAAE,CAAC;MAAA,CAAC;MACxDC,KAAK,EAAE,SAAPA,KAAKA,CAAEJ,KAAK;QAAA,OACVxE,KAAA,CAACnB,IAAI;UAACgG,KAAK,EAAEC,MAAM,CAACC,aAAc;UAAAC,QAAA,GAChClF,IAAA,CAACZ,MAAM;YACL+F,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cACbrD,iBAAiB,CAACsC,IAAI,CAAC;cACvBlC,oBAAoB,CAAC,IAAI,CAAC;YAC5B,CAAE;YAAAgD,QAAA,EACH;UAED,CAAQ,CAAC,EACTlF,IAAA,CAACZ,MAAM;YACL+F,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ3B,kBAAkB,CAACY,IAAI,CAAC;YAAA,CAAC;YAAAc,QAAA,EACzC;UAED,CAAQ,CAAC;QAAA,CACL,CAAC;MAAA;IACP,CACH,CAAC;EAAA,CACH;EAED,OACEhF,KAAA,CAACnB,IAAI;IAACgG,KAAK,EAAEC,MAAM,CAACI,SAAU;IAAAF,QAAA,GAC5BhF,KAAA,CAAChB,IAAI;MAAC6F,KAAK,EAAEC,MAAM,CAACK,IAAK;MAAAH,QAAA,GACvBlF,IAAA,CAACd,IAAI,CAACoG,KAAK;QAAChB,KAAK,EAAC;MAAsB,CAAE,CAAC,EAC3CpE,KAAA,CAAChB,IAAI,CAACqG,OAAO;QAAAL,QAAA,GACXhF,KAAA,CAACf,IAAI;UAAC4F,KAAK,EAAEC,MAAM,CAACQ,UAAW;UAAAN,QAAA,GAAC,aACnB,EAACtB,UAAU,CAAC/C,QAAQ,CAAC;QAAA,CAC5B,CAAC,EAEPX,KAAA,CAACf,IAAI;UAAC4F,KAAK,EAAEC,MAAM,CAACQ,UAAW;UAAAN,QAAA,GAAC,eACjB,EAACjE,UAAU,GAAG,aAAa,GAAG,YAAY;QAAA,CACnD,CAAC,EAEPf,KAAA,CAACnB,IAAI;UAACgG,KAAK,EAAEC,MAAM,CAACS,eAAgB;UAAAP,QAAA,GAClClF,IAAA,CAACZ,MAAM;YACLsG,IAAI,EAAC,WAAW;YAChBP,OAAO,EAAErC,UAAW;YACpB6C,OAAO,EAAElF,SAAU;YACnBmF,QAAQ,EAAEnF,SAAU;YACpBsE,KAAK,EAAEC,MAAM,CAACa,MAAO;YAAAX,QAAA,EAEpBzE,SAAS,GAAG,YAAY,GAAG;UAAU,CAChC,CAAC,EAETT,IAAA,CAACZ,MAAM;YACLsG,IAAI,EAAC,UAAU;YACfP,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQzD,cAAc,CAAC,CAACD,WAAW,CAAC;YAAA,CAAC;YAC5CsD,KAAK,EAAEC,MAAM,CAACa,MAAO;YAAAX,QAAA,EAEpBzD,WAAW,GAAG,cAAc,GAAG;UAAc,CACxC,CAAC;QAAA,CACL,CAAC,EAENA,WAAW,IACVvB,KAAA,CAACnB,IAAI;UAACgG,KAAK,EAAEC,MAAM,CAACc,gBAAiB;UAAAZ,QAAA,GACnClF,IAAA,CAACb,IAAI;YAAC4F,KAAK,EAAEC,MAAM,CAACe,YAAa;YAAAb,QAAA,EAAC;UAAiB,CAAM,CAAC,EAEzD7D,OAAO,CAAC2E,MAAM,KAAK,CAAC,GACnBhG,IAAA,CAACb,IAAI;YAAC4F,KAAK,EAAEC,MAAM,CAACiB,aAAc;YAAAf,QAAA,EAAC;UAAoB,CAAM,CAAC,GAE9DlF,IAAA,CAAChB,QAAQ;YACPkH,IAAI,EAAE7E,OAAQ;YACd8E,UAAU,EAAEjC,gBAAiB;YAC7BkC,YAAY,EAAE,SAAdA,YAAYA,CAAGhC,IAAI;cAAA,OAAKA,IAAI,CAACiC,QAAQ;YAAA,CAAC;YACtCtB,KAAK,EAAEC,MAAM,CAACsB;UAAY,CAC3B,CACF;QAAA,CACG,CACP;MAAA,CACW,CAAC;IAAA,CACX,CAAC,EAEPtG,IAAA,CAACR,MAAM;MAAA0F,QAAA,EACLhF,KAAA,CAACX,MAAM;QACLgH,OAAO,EAAEtE,iBAAkB;QAC3BuE,SAAS,EAAE,SAAXA,SAASA,CAAA;UAAA,OAAQtE,oBAAoB,CAAC,KAAK,CAAC;QAAA,CAAC;QAAAgD,QAAA,GAE7ClF,IAAA,CAACT,MAAM,CAAC+F,KAAK;UAAAJ,QAAA,EAAC;QAAe,CAAc,CAAC,EAC5ChF,KAAA,CAACX,MAAM,CAACgG,OAAO;UAAAL,QAAA,GACblF,IAAA,CAACb,IAAI;YAAA+F,QAAA,EAAC;UAGN,CAAM,CAAC,EACNrD,cAAc,IACb3B,KAAA,CAACf,IAAI;YAAC4F,KAAK,EAAEC,MAAM,CAACyB,UAAW;YAAAvB,QAAA,GAAC,eACjB,EAACtB,UAAU,CAAC/B,cAAc,CAACqB,SAAS,CAAC;UAAA,CAC9C,CACP;QAAA,CACa,CAAC,EACjBhD,KAAA,CAACX,MAAM,CAACmH,OAAO;UAAAxB,QAAA,GACblF,IAAA,CAACZ,MAAM;YAAC+F,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQjD,oBAAoB,CAAC,KAAK,CAAC;YAAA,CAAC;YAAAgD,QAAA,EAAC;UAAM,CAAQ,CAAC,EACnElF,IAAA,CAACZ,MAAM;YAAC+F,OAAO,EAAE9B,aAAc;YAAA6B,QAAA,EAAC;UAAO,CAAQ,CAAC;QAAA,CAClC,CAAC;MAAA,CACX;IAAC,CACH,CAAC,EAERzE,SAAS,IACRP,KAAA,CAACnB,IAAI;MAACgG,KAAK,EAAEC,MAAM,CAAC2B,cAAe;MAAAzB,QAAA,GACjClF,IAAA,CAACV,iBAAiB;QAACkF,IAAI,EAAC,OAAO;QAACoC,KAAK,EAAEvG,KAAK,CAACwG,MAAM,CAACC;MAAQ,CAAE,CAAC,EAC/D9G,IAAA,CAACb,IAAI;QAAC4F,KAAK,EAAEC,MAAM,CAAC+B,WAAY;QAAA7B,QAAA,EAAC;MAAa,CAAM,CAAC;IAAA,CACjD,CACP;EAAA,CACG,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGlG,UAAU,CAACkI,MAAM,CAAC;EAC/B5B,SAAS,EAAE;IACT6B,IAAI,EAAE;EACR,CAAC;EACD5B,IAAI,EAAE;IACJ6B,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC;EACD3B,UAAU,EAAE;IACV4B,YAAY,EAAE;EAChB,CAAC;EACD3B,eAAe,EAAE;IACf4B,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,SAAS,EAAE;EACb,CAAC;EACD1B,MAAM,EAAE;IACN2B,KAAK,EAAE;EACT,CAAC;EACD1B,gBAAgB,EAAE;IAChByB,SAAS,EAAE;EACb,CAAC;EACDxB,YAAY,EAAE;IACZ0B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBN,YAAY,EAAE;EAChB,CAAC;EACDnB,aAAa,EAAE;IACb0B,SAAS,EAAE,QAAQ;IACnBJ,SAAS,EAAE;EACb,CAAC;EACDjB,WAAW,EAAE;IACXsB,SAAS,EAAE;EACb,CAAC;EACD3C,aAAa,EAAE;IACboC,aAAa,EAAE;EACjB,CAAC;EACDZ,UAAU,EAAE;IACVc,SAAS,EAAE,CAAC;IACZI,SAAS,EAAE;EACb,CAAC;EACDhB,cAAc,EAAA/B,aAAA,CAAAA,aAAA,KACT9F,UAAU,CAAC+I,kBAAkB;IAChCC,eAAe,EAAE,oBAAoB;IACrCR,cAAc,EAAE,QAAQ;IACxBS,UAAU,EAAE;EAAQ,EACrB;EACDhB,WAAW,EAAE;IACXH,KAAK,EAAE,OAAO;IACdW,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAepH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}