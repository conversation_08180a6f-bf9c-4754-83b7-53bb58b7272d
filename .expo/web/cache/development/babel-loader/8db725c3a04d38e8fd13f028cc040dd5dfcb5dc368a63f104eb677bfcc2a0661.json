{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"color\", \"indeterminate\", \"progress\", \"trackColor\", \"style\"];\nimport * as React from 'react';\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nvar ProgressBar = React.forwardRef(function (props, ref) {\n  var _props$color = props.color,\n    color = _props$color === void 0 ? '#1976D2' : _props$color,\n    _props$indeterminate = props.indeterminate,\n    indeterminate = _props$indeterminate === void 0 ? false : _props$indeterminate,\n    _props$progress = props.progress,\n    progress = _props$progress === void 0 ? 0 : _props$progress,\n    _props$trackColor = props.trackColor,\n    trackColor = _props$trackColor === void 0 ? 'transparent' : _props$trackColor,\n    style = props.style,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var percentageProgress = progress * 100;\n  var width = indeterminate ? '25%' : percentageProgress + \"%\";\n  return React.createElement(View, _extends({}, other, {\n    \"aria-valuemax\": 100,\n    \"aria-valuemin\": 0,\n    \"aria-valuenow\": indeterminate ? null : percentageProgress,\n    ref: ref,\n    role: \"progressbar\",\n    style: [styles.track, style, {\n      backgroundColor: trackColor\n    }]\n  }), React.createElement(View, {\n    style: [{\n      backgroundColor: color,\n      width: width\n    }, styles.progress, indeterminate && styles.animation]\n  }));\n});\nProgressBar.displayName = 'ProgressBar';\nvar styles = StyleSheet.create({\n  track: {\n    forcedColorAdjust: 'none',\n    height: 5,\n    overflow: 'hidden',\n    userSelect: 'none',\n    zIndex: 0\n  },\n  progress: {\n    forcedColorAdjust: 'none',\n    height: '100%',\n    zIndex: -1\n  },\n  animation: {\n    animationDuration: '1s',\n    animationKeyframes: [{\n      '0%': {\n        transform: 'translateX(-100%)'\n      },\n      '100%': {\n        transform: 'translateX(400%)'\n      }\n    }],\n    animationTimingFunction: 'linear',\n    animationIterationCount: 'infinite'\n  }\n});\nexport default ProgressBar;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "StyleSheet", "View", "ProgressBar", "forwardRef", "props", "ref", "_props$color", "color", "_props$indeterminate", "indeterminate", "_props$progress", "progress", "_props$trackColor", "trackColor", "style", "other", "percentageProgress", "width", "createElement", "role", "styles", "track", "backgroundColor", "animation", "displayName", "create", "forcedColorAdjust", "height", "overflow", "userSelect", "zIndex", "animationDuration", "animationKeyframes", "transform", "animationTimingFunction", "animationIterationCount"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-web/dist/exports/ProgressBar/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"color\", \"indeterminate\", \"progress\", \"trackColor\", \"style\"];\nimport * as React from 'react';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\nvar ProgressBar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _props$color = props.color,\n    color = _props$color === void 0 ? '#1976D2' : _props$color,\n    _props$indeterminate = props.indeterminate,\n    indeterminate = _props$indeterminate === void 0 ? false : _props$indeterminate,\n    _props$progress = props.progress,\n    progress = _props$progress === void 0 ? 0 : _props$progress,\n    _props$trackColor = props.trackColor,\n    trackColor = _props$trackColor === void 0 ? 'transparent' : _props$trackColor,\n    style = props.style,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var percentageProgress = progress * 100;\n  var width = indeterminate ? '25%' : percentageProgress + \"%\";\n  return /*#__PURE__*/React.createElement(View, _extends({}, other, {\n    \"aria-valuemax\": 100,\n    \"aria-valuemin\": 0,\n    \"aria-valuenow\": indeterminate ? null : percentageProgress,\n    ref: ref,\n    role: \"progressbar\",\n    style: [styles.track, style, {\n      backgroundColor: trackColor\n    }]\n  }), /*#__PURE__*/React.createElement(View, {\n    style: [{\n      backgroundColor: color,\n      width\n    }, styles.progress, indeterminate && styles.animation]\n  }));\n});\nProgressBar.displayName = 'ProgressBar';\nvar styles = StyleSheet.create({\n  track: {\n    forcedColorAdjust: 'none',\n    height: 5,\n    overflow: 'hidden',\n    userSelect: 'none',\n    zIndex: 0\n  },\n  progress: {\n    forcedColorAdjust: 'none',\n    height: '100%',\n    zIndex: -1\n  },\n  animation: {\n    animationDuration: '1s',\n    animationKeyframes: [{\n      '0%': {\n        transform: 'translateX(-100%)'\n      },\n      '100%': {\n        transform: 'translateX(400%)'\n      }\n    }],\n    animationTimingFunction: 'linear',\n    animationIterationCount: 'infinite'\n  }\n});\nexport default ProgressBar;"], "mappings": "AASA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,IAAIC,WAAW,GAAgBH,KAAK,CAACI,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;EAC9D,IAAIC,YAAY,GAAGF,KAAK,CAACG,KAAK;IAC5BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,YAAY;IAC1DE,oBAAoB,GAAGJ,KAAK,CAACK,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,oBAAoB;IAC9EE,eAAe,GAAGN,KAAK,CAACO,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IAC3DE,iBAAiB,GAAGR,KAAK,CAACS,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,iBAAiB;IAC7EE,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,KAAK,GAAGlB,6BAA6B,CAACO,KAAK,EAAEN,SAAS,CAAC;EACzD,IAAIkB,kBAAkB,GAAGL,QAAQ,GAAG,GAAG;EACvC,IAAIM,KAAK,GAAGR,aAAa,GAAG,KAAK,GAAGO,kBAAkB,GAAG,GAAG;EAC5D,OAAoBjB,KAAK,CAACmB,aAAa,CAACjB,IAAI,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEmB,KAAK,EAAE;IAChE,eAAe,EAAE,GAAG;IACpB,eAAe,EAAE,CAAC;IAClB,eAAe,EAAEN,aAAa,GAAG,IAAI,GAAGO,kBAAkB;IAC1DX,GAAG,EAAEA,GAAG;IACRc,IAAI,EAAE,aAAa;IACnBL,KAAK,EAAE,CAACM,MAAM,CAACC,KAAK,EAAEP,KAAK,EAAE;MAC3BQ,eAAe,EAAET;IACnB,CAAC;EACH,CAAC,CAAC,EAAed,KAAK,CAACmB,aAAa,CAACjB,IAAI,EAAE;IACzCa,KAAK,EAAE,CAAC;MACNQ,eAAe,EAAEf,KAAK;MACtBU,KAAK,EAALA;IACF,CAAC,EAAEG,MAAM,CAACT,QAAQ,EAAEF,aAAa,IAAIW,MAAM,CAACG,SAAS;EACvD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFrB,WAAW,CAACsB,WAAW,GAAG,aAAa;AACvC,IAAIJ,MAAM,GAAGpB,UAAU,CAACyB,MAAM,CAAC;EAC7BJ,KAAK,EAAE;IACLK,iBAAiB,EAAE,MAAM;IACzBC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE;EACV,CAAC;EACDnB,QAAQ,EAAE;IACRe,iBAAiB,EAAE,MAAM;IACzBC,MAAM,EAAE,MAAM;IACdG,MAAM,EAAE,CAAC;EACX,CAAC;EACDP,SAAS,EAAE;IACTQ,iBAAiB,EAAE,IAAI;IACvBC,kBAAkB,EAAE,CAAC;MACnB,IAAI,EAAE;QACJC,SAAS,EAAE;MACb,CAAC;MACD,MAAM,EAAE;QACNA,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IACFC,uBAAuB,EAAE,QAAQ;IACjCC,uBAAuB,EAAE;EAC3B;AACF,CAAC,CAAC;AACF,eAAejC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}