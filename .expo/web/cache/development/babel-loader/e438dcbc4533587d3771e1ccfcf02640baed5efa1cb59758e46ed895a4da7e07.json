{"ast": null, "code": "'use client';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"contentContainerStyle\", \"horizontal\", \"onContentSizeChange\", \"refreshControl\", \"stickyHeaderIndices\", \"pagingEnabled\", \"forwardedRef\", \"keyboardDismissMode\", \"onScroll\", \"centerContent\"];\nimport Dimensions from \"../Dimensions\";\nimport dismissKeyboard from \"../../modules/dismissKeyboard\";\nimport invariant from 'fbjs/lib/invariant';\nimport mergeRefs from \"../../modules/mergeRefs\";\nimport Platform from \"../Platform\";\nimport ScrollViewBase from \"./ScrollViewBase\";\nimport StyleSheet from \"../StyleSheet\";\nimport TextInputState from \"../../modules/TextInputState\";\nimport UIManager from \"../UIManager\";\nimport View from \"../View\";\nimport React from 'react';\nimport warning from 'fbjs/lib/warning';\nvar emptyObject = {};\nvar IS_ANIMATING_TOUCH_START_THRESHOLD_MS = 16;\nvar ScrollView = function (_React$Component) {\n  function ScrollView() {\n    var _this;\n    _classCallCheck(this, ScrollView);\n    _this = _callSuper(this, ScrollView, arguments);\n    _this._scrollNodeRef = null;\n    _this._innerViewRef = null;\n    _this.isTouching = false;\n    _this.lastMomentumScrollBeginTime = 0;\n    _this.lastMomentumScrollEndTime = 0;\n    _this.observedScrollSinceBecomingResponder = false;\n    _this.becameResponderWhileAnimating = false;\n    _this.scrollResponderHandleScrollShouldSetResponder = function () {\n      return _this.isTouching;\n    };\n    _this.scrollResponderHandleStartShouldSetResponderCapture = function (e) {\n      return _this.scrollResponderIsAnimating();\n    };\n    _this.scrollResponderHandleTerminationRequest = function () {\n      return !_this.observedScrollSinceBecomingResponder;\n    };\n    _this.scrollResponderHandleTouchEnd = function (e) {\n      var nativeEvent = e.nativeEvent;\n      _this.isTouching = nativeEvent.touches.length !== 0;\n      _this.props.onTouchEnd && _this.props.onTouchEnd(e);\n    };\n    _this.scrollResponderHandleResponderRelease = function (e) {\n      _this.props.onResponderRelease && _this.props.onResponderRelease(e);\n      var currentlyFocusedTextInput = TextInputState.currentlyFocusedField();\n      if (!_this.props.keyboardShouldPersistTaps && currentlyFocusedTextInput != null && e.target !== currentlyFocusedTextInput && !_this.observedScrollSinceBecomingResponder && !_this.becameResponderWhileAnimating) {\n        _this.props.onScrollResponderKeyboardDismissed && _this.props.onScrollResponderKeyboardDismissed(e);\n        TextInputState.blurTextInput(currentlyFocusedTextInput);\n      }\n    };\n    _this.scrollResponderHandleScroll = function (e) {\n      _this.observedScrollSinceBecomingResponder = true;\n      _this.props.onScroll && _this.props.onScroll(e);\n    };\n    _this.scrollResponderHandleResponderGrant = function (e) {\n      _this.observedScrollSinceBecomingResponder = false;\n      _this.props.onResponderGrant && _this.props.onResponderGrant(e);\n      _this.becameResponderWhileAnimating = _this.scrollResponderIsAnimating();\n    };\n    _this.scrollResponderHandleScrollBeginDrag = function (e) {\n      _this.props.onScrollBeginDrag && _this.props.onScrollBeginDrag(e);\n    };\n    _this.scrollResponderHandleScrollEndDrag = function (e) {\n      _this.props.onScrollEndDrag && _this.props.onScrollEndDrag(e);\n    };\n    _this.scrollResponderHandleMomentumScrollBegin = function (e) {\n      _this.lastMomentumScrollBeginTime = Date.now();\n      _this.props.onMomentumScrollBegin && _this.props.onMomentumScrollBegin(e);\n    };\n    _this.scrollResponderHandleMomentumScrollEnd = function (e) {\n      _this.lastMomentumScrollEndTime = Date.now();\n      _this.props.onMomentumScrollEnd && _this.props.onMomentumScrollEnd(e);\n    };\n    _this.scrollResponderHandleTouchStart = function (e) {\n      _this.isTouching = true;\n      _this.props.onTouchStart && _this.props.onTouchStart(e);\n    };\n    _this.scrollResponderHandleTouchMove = function (e) {\n      _this.props.onTouchMove && _this.props.onTouchMove(e);\n    };\n    _this.scrollResponderIsAnimating = function () {\n      var now = Date.now();\n      var timeSinceLastMomentumScrollEnd = now - _this.lastMomentumScrollEndTime;\n      var isAnimating = timeSinceLastMomentumScrollEnd < IS_ANIMATING_TOUCH_START_THRESHOLD_MS || _this.lastMomentumScrollEndTime < _this.lastMomentumScrollBeginTime;\n      return isAnimating;\n    };\n    _this.scrollResponderScrollTo = function (x, y, animated) {\n      if (typeof x === 'number') {\n        console.warn('`scrollResponderScrollTo(x, y, animated)` is deprecated. Use `scrollResponderScrollTo({x: 5, y: 5, animated: true})` instead.');\n      } else {\n        var _ref = x || emptyObject;\n        x = _ref.x;\n        y = _ref.y;\n        animated = _ref.animated;\n      }\n      var node = _this.getScrollableNode();\n      var left = x || 0;\n      var top = y || 0;\n      if (node != null) {\n        if (typeof node.scroll === 'function') {\n          node.scroll({\n            top: top,\n            left: left,\n            behavior: !animated ? 'auto' : 'smooth'\n          });\n        } else {\n          node.scrollLeft = left;\n          node.scrollTop = top;\n        }\n      }\n    };\n    _this.scrollResponderZoomTo = function (rect, animated) {\n      if (Platform.OS !== 'ios') {\n        invariant('zoomToRect is not implemented');\n      }\n    };\n    _this.scrollResponderScrollNativeHandleToKeyboard = function (nodeHandle, additionalOffset, preventNegativeScrollOffset) {\n      _this.additionalScrollOffset = additionalOffset || 0;\n      _this.preventNegativeScrollOffset = !!preventNegativeScrollOffset;\n      UIManager.measureLayout(nodeHandle, _this.getInnerViewNode(), _this.scrollResponderTextInputFocusError, _this.scrollResponderInputMeasureAndScrollToKeyboard);\n    };\n    _this.scrollResponderInputMeasureAndScrollToKeyboard = function (left, top, width, height) {\n      var keyboardScreenY = Dimensions.get('window').height;\n      if (_this.keyboardWillOpenTo) {\n        keyboardScreenY = _this.keyboardWillOpenTo.endCoordinates.screenY;\n      }\n      var scrollOffsetY = top - keyboardScreenY + height + _this.additionalScrollOffset;\n      if (_this.preventNegativeScrollOffset) {\n        scrollOffsetY = Math.max(0, scrollOffsetY);\n      }\n      _this.scrollResponderScrollTo({\n        x: 0,\n        y: scrollOffsetY,\n        animated: true\n      });\n      _this.additionalOffset = 0;\n      _this.preventNegativeScrollOffset = false;\n    };\n    _this.scrollResponderKeyboardWillShow = function (e) {\n      _this.keyboardWillOpenTo = e;\n      _this.props.onKeyboardWillShow && _this.props.onKeyboardWillShow(e);\n    };\n    _this.scrollResponderKeyboardWillHide = function (e) {\n      _this.keyboardWillOpenTo = null;\n      _this.props.onKeyboardWillHide && _this.props.onKeyboardWillHide(e);\n    };\n    _this.scrollResponderKeyboardDidShow = function (e) {\n      if (e) {\n        _this.keyboardWillOpenTo = e;\n      }\n      _this.props.onKeyboardDidShow && _this.props.onKeyboardDidShow(e);\n    };\n    _this.scrollResponderKeyboardDidHide = function (e) {\n      _this.keyboardWillOpenTo = null;\n      _this.props.onKeyboardDidHide && _this.props.onKeyboardDidHide(e);\n    };\n    _this.flashScrollIndicators = function () {\n      _this.scrollResponderFlashScrollIndicators();\n    };\n    _this.getScrollResponder = function () {\n      return _this;\n    };\n    _this.getScrollableNode = function () {\n      return _this._scrollNodeRef;\n    };\n    _this.getInnerViewRef = function () {\n      return _this._innerViewRef;\n    };\n    _this.getInnerViewNode = function () {\n      return _this._innerViewRef;\n    };\n    _this.getNativeScrollRef = function () {\n      return _this._scrollNodeRef;\n    };\n    _this.scrollTo = function (y, x, animated) {\n      if (typeof y === 'number') {\n        console.warn('`scrollTo(y, x, animated)` is deprecated. Use `scrollTo({x: 5, y: 5, animated: true})` instead.');\n      } else {\n        var _ref2 = y || emptyObject;\n        x = _ref2.x;\n        y = _ref2.y;\n        animated = _ref2.animated;\n      }\n      _this.scrollResponderScrollTo({\n        x: x || 0,\n        y: y || 0,\n        animated: animated !== false\n      });\n    };\n    _this.scrollToEnd = function (options) {\n      var animated = (options && options.animated) !== false;\n      var horizontal = _this.props.horizontal;\n      var scrollResponderNode = _this.getScrollableNode();\n      var x = horizontal ? scrollResponderNode.scrollWidth : 0;\n      var y = horizontal ? 0 : scrollResponderNode.scrollHeight;\n      _this.scrollResponderScrollTo({\n        x: x,\n        y: y,\n        animated: animated\n      });\n    };\n    _this._handleContentOnLayout = function (e) {\n      var _e$nativeEvent$layout = e.nativeEvent.layout,\n        width = _e$nativeEvent$layout.width,\n        height = _e$nativeEvent$layout.height;\n      _this.props.onContentSizeChange(width, height);\n    };\n    _this._handleScroll = function (e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (_this.props.onScroll && _this.props.scrollEventThrottle == null) {\n          console.log('You specified `onScroll` on a <ScrollView> but not ' + '`scrollEventThrottle`. You will only receive one event. ' + 'Using `16` you get all the events but be aware that it may ' + \"cause frame drops, use a bigger number if you don't need as \" + 'much precision.');\n        }\n      }\n      if (_this.props.keyboardDismissMode === 'on-drag') {\n        dismissKeyboard();\n      }\n      _this.scrollResponderHandleScroll(e);\n    };\n    _this._setInnerViewRef = function (node) {\n      _this._innerViewRef = node;\n    };\n    _this._setScrollNodeRef = function (node) {\n      _this._scrollNodeRef = node;\n      if (node != null) {\n        node.getScrollResponder = _this.getScrollResponder;\n        node.getInnerViewNode = _this.getInnerViewNode;\n        node.getInnerViewRef = _this.getInnerViewRef;\n        node.getNativeScrollRef = _this.getNativeScrollRef;\n        node.getScrollableNode = _this.getScrollableNode;\n        node.scrollTo = _this.scrollTo;\n        node.scrollToEnd = _this.scrollToEnd;\n        node.flashScrollIndicators = _this.flashScrollIndicators;\n        node.scrollResponderZoomTo = _this.scrollResponderZoomTo;\n        node.scrollResponderScrollNativeHandleToKeyboard = _this.scrollResponderScrollNativeHandleToKeyboard;\n      }\n      var ref = mergeRefs(_this.props.forwardedRef);\n      ref(node);\n    };\n    return _this;\n  }\n  _inherits(ScrollView, _React$Component);\n  return _createClass(ScrollView, [{\n    key: \"scrollResponderHandleStartShouldSetResponder\",\n    value: function scrollResponderHandleStartShouldSetResponder() {\n      return false;\n    }\n  }, {\n    key: \"scrollResponderHandleResponderReject\",\n    value: function scrollResponderHandleResponderReject() {\n      warning(false, \"ScrollView doesn't take rejection well - scrolls anyway\");\n    }\n  }, {\n    key: \"scrollResponderFlashScrollIndicators\",\n    value: function scrollResponderFlashScrollIndicators() {}\n  }, {\n    key: \"scrollResponderTextInputFocusError\",\n    value: function scrollResponderTextInputFocusError(e) {\n      console.error('Error measuring text field: ', e);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        contentContainerStyle = _this$props.contentContainerStyle,\n        horizontal = _this$props.horizontal,\n        onContentSizeChange = _this$props.onContentSizeChange,\n        refreshControl = _this$props.refreshControl,\n        stickyHeaderIndices = _this$props.stickyHeaderIndices,\n        pagingEnabled = _this$props.pagingEnabled,\n        forwardedRef = _this$props.forwardedRef,\n        keyboardDismissMode = _this$props.keyboardDismissMode,\n        onScroll = _this$props.onScroll,\n        centerContent = _this$props.centerContent,\n        other = _objectWithoutPropertiesLoose(_this$props, _excluded);\n      if (process.env.NODE_ENV !== 'production' && this.props.style) {\n        var style = StyleSheet.flatten(this.props.style);\n        var childLayoutProps = ['alignItems', 'justifyContent'].filter(function (prop) {\n          return style && style[prop] !== undefined;\n        });\n        invariant(childLayoutProps.length === 0, \"ScrollView child layout (\" + JSON.stringify(childLayoutProps) + \") \" + 'must be applied through the contentContainerStyle prop.');\n      }\n      var contentSizeChangeProps = {};\n      if (onContentSizeChange) {\n        contentSizeChangeProps = {\n          onLayout: this._handleContentOnLayout\n        };\n      }\n      var hasStickyHeaderIndices = !horizontal && Array.isArray(stickyHeaderIndices);\n      var children = hasStickyHeaderIndices || pagingEnabled ? React.Children.map(this.props.children, function (child, i) {\n        var isSticky = hasStickyHeaderIndices && stickyHeaderIndices.indexOf(i) > -1;\n        if (child != null && (isSticky || pagingEnabled)) {\n          return React.createElement(View, {\n            style: [isSticky && styles.stickyHeader, pagingEnabled && styles.pagingEnabledChild]\n          }, child);\n        } else {\n          return child;\n        }\n      }) : this.props.children;\n      var contentContainer = React.createElement(View, _extends({}, contentSizeChangeProps, {\n        children: children,\n        collapsable: false,\n        ref: this._setInnerViewRef,\n        style: [horizontal && styles.contentContainerHorizontal, centerContent && styles.contentContainerCenterContent, contentContainerStyle]\n      }));\n      var baseStyle = horizontal ? styles.baseHorizontal : styles.baseVertical;\n      var pagingEnabledStyle = horizontal ? styles.pagingEnabledHorizontal : styles.pagingEnabledVertical;\n      var props = _objectSpread(_objectSpread({}, other), {}, {\n        style: [baseStyle, pagingEnabled && pagingEnabledStyle, this.props.style],\n        onTouchStart: this.scrollResponderHandleTouchStart,\n        onTouchMove: this.scrollResponderHandleTouchMove,\n        onTouchEnd: this.scrollResponderHandleTouchEnd,\n        onScrollBeginDrag: this.scrollResponderHandleScrollBeginDrag,\n        onScrollEndDrag: this.scrollResponderHandleScrollEndDrag,\n        onMomentumScrollBegin: this.scrollResponderHandleMomentumScrollBegin,\n        onMomentumScrollEnd: this.scrollResponderHandleMomentumScrollEnd,\n        onStartShouldSetResponder: this.scrollResponderHandleStartShouldSetResponder,\n        onStartShouldSetResponderCapture: this.scrollResponderHandleStartShouldSetResponderCapture,\n        onScrollShouldSetResponder: this.scrollResponderHandleScrollShouldSetResponder,\n        onScroll: this._handleScroll,\n        onResponderGrant: this.scrollResponderHandleResponderGrant,\n        onResponderTerminationRequest: this.scrollResponderHandleTerminationRequest,\n        onResponderTerminate: this.scrollResponderHandleTerminate,\n        onResponderRelease: this.scrollResponderHandleResponderRelease,\n        onResponderReject: this.scrollResponderHandleResponderReject\n      });\n      var ScrollViewClass = ScrollViewBase;\n      invariant(ScrollViewClass !== undefined, 'ScrollViewClass must not be undefined');\n      var scrollView = React.createElement(ScrollViewClass, _extends({}, props, {\n        ref: this._setScrollNodeRef\n      }), contentContainer);\n      if (refreshControl) {\n        return React.cloneElement(refreshControl, {\n          style: props.style\n        }, scrollView);\n      }\n      return scrollView;\n    }\n  }]);\n}(React.Component);\nvar commonStyle = {\n  flexGrow: 1,\n  flexShrink: 1,\n  transform: 'translateZ(0)',\n  WebkitOverflowScrolling: 'touch'\n};\nvar styles = StyleSheet.create({\n  baseVertical: _objectSpread(_objectSpread({}, commonStyle), {}, {\n    flexDirection: 'column',\n    overflowX: 'hidden',\n    overflowY: 'auto'\n  }),\n  baseHorizontal: _objectSpread(_objectSpread({}, commonStyle), {}, {\n    flexDirection: 'row',\n    overflowX: 'auto',\n    overflowY: 'hidden'\n  }),\n  contentContainerHorizontal: {\n    flexDirection: 'row'\n  },\n  contentContainerCenterContent: {\n    justifyContent: 'center',\n    flexGrow: 1\n  },\n  stickyHeader: {\n    position: 'sticky',\n    top: 0,\n    zIndex: 10\n  },\n  pagingEnabledHorizontal: {\n    scrollSnapType: 'x mandatory'\n  },\n  pagingEnabledVertical: {\n    scrollSnapType: 'y mandatory'\n  },\n  pagingEnabledChild: {\n    scrollSnapAlign: 'start'\n  }\n});\nvar ForwardedScrollView = React.forwardRef(function (props, forwardedRef) {\n  return React.createElement(ScrollView, _extends({}, props, {\n    forwardedRef: forwardedRef\n  }));\n});\nForwardedScrollView.displayName = 'ScrollView';\nexport default ForwardedScrollView;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_objectSpread", "_extends", "_objectWithoutPropertiesLoose", "_excluded", "Dimensions", "dismissKeyboard", "invariant", "mergeRefs", "Platform", "ScrollViewBase", "StyleSheet", "TextInputState", "UIManager", "View", "React", "warning", "emptyObject", "IS_ANIMATING_TOUCH_START_THRESHOLD_MS", "ScrollView", "_React$Component", "_this", "arguments", "_scrollNodeRef", "_innerViewRef", "isTouching", "lastMomentumScrollBeginTime", "lastMomentumScrollEndTime", "observedScrollSinceBecomingResponder", "becameResponderWhileAnimating", "scrollResponderHandleScrollShouldSetResponder", "scrollResponderHandleStartShouldSetResponderCapture", "scrollResponderIsAnimating", "scrollResponderHandleTerminationRequest", "scrollResponderHandleTouchEnd", "nativeEvent", "touches", "length", "props", "onTouchEnd", "scrollResponderHandleResponderRelease", "onResponderRelease", "currentlyFocusedTextInput", "currentlyFocusedField", "keyboardShouldPersistTaps", "target", "onScrollResponderKeyboardDismissed", "blurTextInput", "scrollResponderHandleScroll", "onScroll", "scrollResponderHandleResponderGrant", "onResponderGrant", "scrollResponderHandleScrollBeginDrag", "onScrollBeginDrag", "scrollResponderHandleScrollEndDrag", "onScrollEndDrag", "scrollResponderHandleMomentumScrollBegin", "Date", "now", "onMomentumScrollBegin", "scrollResponderHandleMomentumScrollEnd", "onMomentumScrollEnd", "scrollResponderHandleTouchStart", "onTouchStart", "scrollResponderHandleTouchMove", "onTouchMove", "timeSinceLastMomentumScrollEnd", "isAnimating", "scrollResponderScrollTo", "x", "y", "animated", "console", "warn", "_ref", "node", "getScrollableNode", "left", "top", "scroll", "behavior", "scrollLeft", "scrollTop", "scrollResponderZoomTo", "rect", "OS", "scrollResponderScrollNativeHandleToKeyboard", "nodeHandle", "additionalOffset", "preventNegativeScrollOffset", "additionalScrollOffset", "measureLayout", "getInnerViewNode", "scrollResponderTextInputFocusError", "scrollResponderInputMeasureAndScrollToKeyboard", "width", "height", "keyboardScreenY", "get", "keyboardWillOpenTo", "endCoordinates", "screenY", "scrollOffsetY", "Math", "max", "scrollResponderKeyboardWillShow", "onKeyboardWillShow", "scrollResponderKeyboardWillHide", "onKeyboardWillHide", "scrollResponderKeyboardDidShow", "onKeyboardDidShow", "scrollResponderKeyboardDidHide", "onKeyboardDidHide", "flashScrollIndicators", "scrollResponderFlashScrollIndicators", "getScrollResponder", "getInnerViewRef", "getNativeScrollRef", "scrollTo", "_ref2", "scrollToEnd", "options", "horizontal", "scrollResponderNode", "scrollWidth", "scrollHeight", "_handleContentOnLayout", "_e$nativeEvent$layout", "layout", "onContentSizeChange", "_handleScroll", "process", "env", "NODE_ENV", "scrollEventThrottle", "log", "keyboardDismissMode", "_setInnerViewRef", "_setScrollNodeRef", "ref", "forwardedRef", "key", "value", "scrollResponderHandleStartShouldSetResponder", "scrollResponderHandleResponderReject", "error", "render", "_this$props", "contentContainerStyle", "refreshControl", "stickyHeaderIndices", "pagingEnabled", "centerContent", "other", "style", "flatten", "childLayoutProps", "filter", "prop", "undefined", "JSON", "stringify", "contentSizeChangeProps", "onLayout", "hasStickyHeaderIndices", "Array", "isArray", "children", "Children", "map", "child", "i", "isSticky", "indexOf", "createElement", "styles", "<PERSON><PERSON><PERSON><PERSON>", "paging<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contentContainer", "collapsable", "contentContainerHorizontal", "contentContainerCenterContent", "baseStyle", "baseHorizontal", "baseVertical", "pagingEnabledStyle", "pagingEnabledHorizontal", "pagingEnabledVertical", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "onScrollShouldSetResponder", "onResponderTerminationRequest", "onResponderTerminate", "scrollResponderHandleTerminate", "onResponderReject", "ScrollViewClass", "scrollView", "cloneElement", "Component", "commonStyle", "flexGrow", "flexShrink", "transform", "WebkitOverflowScrolling", "create", "flexDirection", "overflowX", "overflowY", "justifyContent", "position", "zIndex", "scrollSnapType", "scrollSnapAlign", "ForwardedScrollView", "forwardRef", "displayName"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/node_modules/react-native-web/dist/exports/ScrollView/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"contentContainerStyle\", \"horizontal\", \"onContentSizeChange\", \"refreshControl\", \"stickyHeaderIndices\", \"pagingEnabled\", \"forwardedRef\", \"keyboardDismissMode\", \"onScroll\", \"centerContent\"];\nimport Dimensions from '../Dimensions';\nimport dismissKeyboard from '../../modules/dismissKeyboard';\nimport invariant from 'fbjs/lib/invariant';\nimport mergeRefs from '../../modules/mergeRefs';\nimport Platform from '../Platform';\nimport ScrollViewBase from './ScrollViewBase';\nimport StyleSheet from '../StyleSheet';\nimport TextInputState from '../../modules/TextInputState';\nimport UIManager from '../UIManager';\nimport View from '../View';\nimport React from 'react';\nimport warning from 'fbjs/lib/warning';\nvar emptyObject = {};\nvar IS_ANIMATING_TOUCH_START_THRESHOLD_MS = 16;\nclass ScrollView extends React.Component {\n  constructor() {\n    super(...arguments);\n    this._scrollNodeRef = null;\n    this._innerViewRef = null;\n    this.isTouching = false;\n    this.lastMomentumScrollBeginTime = 0;\n    this.lastMomentumScrollEndTime = 0;\n    this.observedScrollSinceBecomingResponder = false;\n    this.becameResponderWhileAnimating = false;\n    this.scrollResponderHandleScrollShouldSetResponder = () => {\n      return this.isTouching;\n    };\n    this.scrollResponderHandleStartShouldSetResponderCapture = e => {\n      // First see if we want to eat taps while the keyboard is up\n      // var currentlyFocusedTextInput = TextInputState.currentlyFocusedField();\n      // if (!this.props.keyboardShouldPersistTaps &&\n      //   currentlyFocusedTextInput != null &&\n      //   e.target !== currentlyFocusedTextInput) {\n      //   return true;\n      // }\n      return this.scrollResponderIsAnimating();\n    };\n    this.scrollResponderHandleTerminationRequest = () => {\n      return !this.observedScrollSinceBecomingResponder;\n    };\n    this.scrollResponderHandleTouchEnd = e => {\n      var nativeEvent = e.nativeEvent;\n      this.isTouching = nativeEvent.touches.length !== 0;\n      this.props.onTouchEnd && this.props.onTouchEnd(e);\n    };\n    this.scrollResponderHandleResponderRelease = e => {\n      this.props.onResponderRelease && this.props.onResponderRelease(e);\n\n      // By default scroll views will unfocus a textField\n      // if another touch occurs outside of it\n      var currentlyFocusedTextInput = TextInputState.currentlyFocusedField();\n      if (!this.props.keyboardShouldPersistTaps && currentlyFocusedTextInput != null && e.target !== currentlyFocusedTextInput && !this.observedScrollSinceBecomingResponder && !this.becameResponderWhileAnimating) {\n        this.props.onScrollResponderKeyboardDismissed && this.props.onScrollResponderKeyboardDismissed(e);\n        TextInputState.blurTextInput(currentlyFocusedTextInput);\n      }\n    };\n    this.scrollResponderHandleScroll = e => {\n      this.observedScrollSinceBecomingResponder = true;\n      this.props.onScroll && this.props.onScroll(e);\n    };\n    this.scrollResponderHandleResponderGrant = e => {\n      this.observedScrollSinceBecomingResponder = false;\n      this.props.onResponderGrant && this.props.onResponderGrant(e);\n      this.becameResponderWhileAnimating = this.scrollResponderIsAnimating();\n    };\n    this.scrollResponderHandleScrollBeginDrag = e => {\n      this.props.onScrollBeginDrag && this.props.onScrollBeginDrag(e);\n    };\n    this.scrollResponderHandleScrollEndDrag = e => {\n      this.props.onScrollEndDrag && this.props.onScrollEndDrag(e);\n    };\n    this.scrollResponderHandleMomentumScrollBegin = e => {\n      this.lastMomentumScrollBeginTime = Date.now();\n      this.props.onMomentumScrollBegin && this.props.onMomentumScrollBegin(e);\n    };\n    this.scrollResponderHandleMomentumScrollEnd = e => {\n      this.lastMomentumScrollEndTime = Date.now();\n      this.props.onMomentumScrollEnd && this.props.onMomentumScrollEnd(e);\n    };\n    this.scrollResponderHandleTouchStart = e => {\n      this.isTouching = true;\n      this.props.onTouchStart && this.props.onTouchStart(e);\n    };\n    this.scrollResponderHandleTouchMove = e => {\n      this.props.onTouchMove && this.props.onTouchMove(e);\n    };\n    this.scrollResponderIsAnimating = () => {\n      var now = Date.now();\n      var timeSinceLastMomentumScrollEnd = now - this.lastMomentumScrollEndTime;\n      var isAnimating = timeSinceLastMomentumScrollEnd < IS_ANIMATING_TOUCH_START_THRESHOLD_MS || this.lastMomentumScrollEndTime < this.lastMomentumScrollBeginTime;\n      return isAnimating;\n    };\n    this.scrollResponderScrollTo = (x, y, animated) => {\n      if (typeof x === 'number') {\n        console.warn('`scrollResponderScrollTo(x, y, animated)` is deprecated. Use `scrollResponderScrollTo({x: 5, y: 5, animated: true})` instead.');\n      } else {\n        var _ref = x || emptyObject;\n        x = _ref.x;\n        y = _ref.y;\n        animated = _ref.animated;\n      }\n      var node = this.getScrollableNode();\n      var left = x || 0;\n      var top = y || 0;\n      if (node != null) {\n        if (typeof node.scroll === 'function') {\n          node.scroll({\n            top,\n            left,\n            behavior: !animated ? 'auto' : 'smooth'\n          });\n        } else {\n          node.scrollLeft = left;\n          node.scrollTop = top;\n        }\n      }\n    };\n    this.scrollResponderZoomTo = (rect, animated) => {\n      if (Platform.OS !== 'ios') {\n        invariant('zoomToRect is not implemented');\n      }\n    };\n    this.scrollResponderScrollNativeHandleToKeyboard = (nodeHandle, additionalOffset, preventNegativeScrollOffset) => {\n      this.additionalScrollOffset = additionalOffset || 0;\n      this.preventNegativeScrollOffset = !!preventNegativeScrollOffset;\n      UIManager.measureLayout(nodeHandle, this.getInnerViewNode(), this.scrollResponderTextInputFocusError, this.scrollResponderInputMeasureAndScrollToKeyboard);\n    };\n    this.scrollResponderInputMeasureAndScrollToKeyboard = (left, top, width, height) => {\n      var keyboardScreenY = Dimensions.get('window').height;\n      if (this.keyboardWillOpenTo) {\n        keyboardScreenY = this.keyboardWillOpenTo.endCoordinates.screenY;\n      }\n      var scrollOffsetY = top - keyboardScreenY + height + this.additionalScrollOffset;\n\n      // By default, this can scroll with negative offset, pulling the content\n      // down so that the target component's bottom meets the keyboard's top.\n      // If requested otherwise, cap the offset at 0 minimum to avoid content\n      // shifting down.\n      if (this.preventNegativeScrollOffset) {\n        scrollOffsetY = Math.max(0, scrollOffsetY);\n      }\n      this.scrollResponderScrollTo({\n        x: 0,\n        y: scrollOffsetY,\n        animated: true\n      });\n      this.additionalOffset = 0;\n      this.preventNegativeScrollOffset = false;\n    };\n    this.scrollResponderKeyboardWillShow = e => {\n      this.keyboardWillOpenTo = e;\n      this.props.onKeyboardWillShow && this.props.onKeyboardWillShow(e);\n    };\n    this.scrollResponderKeyboardWillHide = e => {\n      this.keyboardWillOpenTo = null;\n      this.props.onKeyboardWillHide && this.props.onKeyboardWillHide(e);\n    };\n    this.scrollResponderKeyboardDidShow = e => {\n      // TODO(7693961): The event for DidShow is not available on iOS yet.\n      // Use the one from WillShow and do not assign.\n      if (e) {\n        this.keyboardWillOpenTo = e;\n      }\n      this.props.onKeyboardDidShow && this.props.onKeyboardDidShow(e);\n    };\n    this.scrollResponderKeyboardDidHide = e => {\n      this.keyboardWillOpenTo = null;\n      this.props.onKeyboardDidHide && this.props.onKeyboardDidHide(e);\n    };\n    this.flashScrollIndicators = () => {\n      this.scrollResponderFlashScrollIndicators();\n    };\n    this.getScrollResponder = () => {\n      return this;\n    };\n    this.getScrollableNode = () => {\n      return this._scrollNodeRef;\n    };\n    this.getInnerViewRef = () => {\n      return this._innerViewRef;\n    };\n    this.getInnerViewNode = () => {\n      return this._innerViewRef;\n    };\n    this.getNativeScrollRef = () => {\n      return this._scrollNodeRef;\n    };\n    this.scrollTo = (y, x, animated) => {\n      if (typeof y === 'number') {\n        console.warn('`scrollTo(y, x, animated)` is deprecated. Use `scrollTo({x: 5, y: 5, animated: true})` instead.');\n      } else {\n        var _ref2 = y || emptyObject;\n        x = _ref2.x;\n        y = _ref2.y;\n        animated = _ref2.animated;\n      }\n      this.scrollResponderScrollTo({\n        x: x || 0,\n        y: y || 0,\n        animated: animated !== false\n      });\n    };\n    this.scrollToEnd = options => {\n      // Default to true\n      var animated = (options && options.animated) !== false;\n      var horizontal = this.props.horizontal;\n      var scrollResponderNode = this.getScrollableNode();\n      var x = horizontal ? scrollResponderNode.scrollWidth : 0;\n      var y = horizontal ? 0 : scrollResponderNode.scrollHeight;\n      this.scrollResponderScrollTo({\n        x,\n        y,\n        animated\n      });\n    };\n    this._handleContentOnLayout = e => {\n      var _e$nativeEvent$layout = e.nativeEvent.layout,\n        width = _e$nativeEvent$layout.width,\n        height = _e$nativeEvent$layout.height;\n      this.props.onContentSizeChange(width, height);\n    };\n    this._handleScroll = e => {\n      if (process.env.NODE_ENV !== 'production') {\n        if (this.props.onScroll && this.props.scrollEventThrottle == null) {\n          console.log('You specified `onScroll` on a <ScrollView> but not ' + '`scrollEventThrottle`. You will only receive one event. ' + 'Using `16` you get all the events but be aware that it may ' + \"cause frame drops, use a bigger number if you don't need as \" + 'much precision.');\n        }\n      }\n      if (this.props.keyboardDismissMode === 'on-drag') {\n        dismissKeyboard();\n      }\n      this.scrollResponderHandleScroll(e);\n    };\n    this._setInnerViewRef = node => {\n      this._innerViewRef = node;\n    };\n    this._setScrollNodeRef = node => {\n      this._scrollNodeRef = node;\n      // ScrollView needs to add more methods to the hostNode in addition to those\n      // added by `usePlatformMethods`. This is temporarily until an API like\n      // `ScrollView.scrollTo(hostNode, { x, y })` is added to React Native.\n      if (node != null) {\n        node.getScrollResponder = this.getScrollResponder;\n        node.getInnerViewNode = this.getInnerViewNode;\n        node.getInnerViewRef = this.getInnerViewRef;\n        node.getNativeScrollRef = this.getNativeScrollRef;\n        node.getScrollableNode = this.getScrollableNode;\n        node.scrollTo = this.scrollTo;\n        node.scrollToEnd = this.scrollToEnd;\n        node.flashScrollIndicators = this.flashScrollIndicators;\n        node.scrollResponderZoomTo = this.scrollResponderZoomTo;\n        node.scrollResponderScrollNativeHandleToKeyboard = this.scrollResponderScrollNativeHandleToKeyboard;\n      }\n      var ref = mergeRefs(this.props.forwardedRef);\n      ref(node);\n    };\n  }\n  /**\n   * ------------------------------------------------------\n   * START SCROLLRESPONDER\n   * ------------------------------------------------------\n   */\n  // Reset to false every time becomes responder. This is used to:\n  // - Determine if the scroll view has been scrolled and therefore should\n  // refuse to give up its responder lock.\n  // - Determine if releasing should dismiss the keyboard when we are in\n  // tap-to-dismiss mode (!this.props.keyboardShouldPersistTaps).\n  /**\n   * Invoke this from an `onScroll` event.\n   */\n  /**\n   * Merely touch starting is not sufficient for a scroll view to become the\n   * responder. Being the \"responder\" means that the very next touch move/end\n   * event will result in an action/movement.\n   *\n   * Invoke this from an `onStartShouldSetResponder` event.\n   *\n   * `onStartShouldSetResponder` is used when the next move/end will trigger\n   * some UI movement/action, but when you want to yield priority to views\n   * nested inside of the view.\n   *\n   * There may be some cases where scroll views actually should return `true`\n   * from `onStartShouldSetResponder`: Any time we are detecting a standard tap\n   * that gives priority to nested views.\n   *\n   * - If a single tap on the scroll view triggers an action such as\n   *   recentering a map style view yet wants to give priority to interaction\n   *   views inside (such as dropped pins or labels), then we would return true\n   *   from this method when there is a single touch.\n   *\n   * - Similar to the previous case, if a two finger \"tap\" should trigger a\n   *   zoom, we would check the `touches` count, and if `>= 2`, we would return\n   *   true.\n   *\n   */\n  scrollResponderHandleStartShouldSetResponder() {\n    return false;\n  }\n\n  /**\n   * There are times when the scroll view wants to become the responder\n   * (meaning respond to the next immediate `touchStart/touchEnd`), in a way\n   * that *doesn't* give priority to nested views (hence the capture phase):\n   *\n   * - Currently animating.\n   * - Tapping anywhere that is not the focused input, while the keyboard is\n   *   up (which should dismiss the keyboard).\n   *\n   * Invoke this from an `onStartShouldSetResponderCapture` event.\n   */\n\n  /**\n   * Invoke this from an `onResponderReject` event.\n   *\n   * Some other element is not yielding its role as responder. Normally, we'd\n   * just disable the `UIScrollView`, but a touch has already began on it, the\n   * `UIScrollView` will not accept being disabled after that. The easiest\n   * solution for now is to accept the limitation of disallowing this\n   * altogether. To improve this, find a way to disable the `UIScrollView` after\n   * a touch has already started.\n   */\n  scrollResponderHandleResponderReject() {\n    warning(false, \"ScrollView doesn't take rejection well - scrolls anyway\");\n  }\n\n  /**\n   * We will allow the scroll view to give up its lock iff it acquired the lock\n   * during an animation. This is a very useful default that happens to satisfy\n   * many common user experiences.\n   *\n   * - Stop a scroll on the left edge, then turn that into an outer view's\n   *   backswipe.\n   * - Stop a scroll mid-bounce at the top, continue pulling to have the outer\n   *   view dismiss.\n   * - However, without catching the scroll view mid-bounce (while it is\n   *   motionless), if you drag far enough for the scroll view to become\n   *   responder (and therefore drag the scroll view a bit), any backswipe\n   *   navigation of a swipe gesture higher in the view hierarchy, should be\n   *   rejected.\n   */\n\n  /**\n   * Invoke this from an `onTouchEnd` event.\n   *\n   * @param {SyntheticEvent} e Event.\n   */\n\n  /**\n   * Invoke this from an `onResponderRelease` event.\n   */\n\n  /**\n   * Invoke this from an `onResponderGrant` event.\n   */\n\n  /**\n   * Unfortunately, `onScrollBeginDrag` also fires when *stopping* the scroll\n   * animation, and there's not an easy way to distinguish a drag vs. stopping\n   * momentum.\n   *\n   * Invoke this from an `onScrollBeginDrag` event.\n   */\n\n  /**\n   * Invoke this from an `onScrollEndDrag` event.\n   */\n\n  /**\n   * Invoke this from an `onMomentumScrollBegin` event.\n   */\n\n  /**\n   * Invoke this from an `onMomentumScrollEnd` event.\n   */\n\n  /**\n   * Invoke this from an `onTouchStart` event.\n   *\n   * Since we know that the `SimpleEventPlugin` occurs later in the plugin\n   * order, after `ResponderEventPlugin`, we can detect that we were *not*\n   * permitted to be the responder (presumably because a contained view became\n   * responder). The `onResponderReject` won't fire in that case - it only\n   * fires when a *current* responder rejects our request.\n   *\n   * @param {SyntheticEvent} e Touch Start event.\n   */\n\n  /**\n   * Invoke this from an `onTouchMove` event.\n   *\n   * Since we know that the `SimpleEventPlugin` occurs later in the plugin\n   * order, after `ResponderEventPlugin`, we can detect that we were *not*\n   * permitted to be the responder (presumably because a contained view became\n   * responder). The `onResponderReject` won't fire in that case - it only\n   * fires when a *current* responder rejects our request.\n   *\n   * @param {SyntheticEvent} e Touch Start event.\n   */\n\n  /**\n   * A helper function for this class that lets us quickly determine if the\n   * view is currently animating. This is particularly useful to know when\n   * a touch has just started or ended.\n   */\n\n  /**\n   * A helper function to scroll to a specific point in the scrollview.\n   * This is currently used to help focus on child textviews, but can also\n   * be used to quickly scroll to any element we want to focus. Syntax:\n   *\n   * scrollResponderScrollTo(options: {x: number = 0; y: number = 0; animated: boolean = true})\n   *\n   * Note: The weird argument signature is due to the fact that, for historical reasons,\n   * the function also accepts separate arguments as as alternative to the options object.\n   * This is deprecated due to ambiguity (y before x), and SHOULD NOT BE USED.\n   */\n\n  /**\n   * A helper function to zoom to a specific rect in the scrollview. The argument has the shape\n   * {x: number; y: number; width: number; height: number; animated: boolean = true}\n   *\n   * @platform ios\n   */\n\n  /**\n   * Displays the scroll indicators momentarily.\n   */\n  scrollResponderFlashScrollIndicators() {}\n\n  /**\n   * This method should be used as the callback to onFocus in a TextInputs'\n   * parent view. Note that any module using this mixin needs to return\n   * the parent view's ref in getScrollViewRef() in order to use this method.\n   * @param {any} nodeHandle The TextInput node handle\n   * @param {number} additionalOffset The scroll view's top \"contentInset\".\n   *        Default is 0.\n   * @param {bool} preventNegativeScrolling Whether to allow pulling the content\n   *        down to make it meet the keyboard's top. Default is false.\n   */\n\n  /**\n   * The calculations performed here assume the scroll view takes up the entire\n   * screen - even if has some content inset. We then measure the offsets of the\n   * keyboard, and compensate both for the scroll view's \"contentInset\".\n   *\n   * @param {number} left Position of input w.r.t. table view.\n   * @param {number} top Position of input w.r.t. table view.\n   * @param {number} width Width of the text input.\n   * @param {number} height Height of the text input.\n   */\n\n  scrollResponderTextInputFocusError(e) {\n    console.error('Error measuring text field: ', e);\n  }\n\n  /**\n   * Warning, this may be called several times for a single keyboard opening.\n   * It's best to store the information in this method and then take any action\n   * at a later point (either in `keyboardDidShow` or other).\n   *\n   * Here's the order that events occur in:\n   * - focus\n   * - willShow {startCoordinates, endCoordinates} several times\n   * - didShow several times\n   * - blur\n   * - willHide {startCoordinates, endCoordinates} several times\n   * - didHide several times\n   *\n   * The `ScrollResponder` providesModule callbacks for each of these events.\n   * Even though any user could have easily listened to keyboard events\n   * themselves, using these `props` callbacks ensures that ordering of events\n   * is consistent - and not dependent on the order that the keyboard events are\n   * subscribed to. This matters when telling the scroll view to scroll to where\n   * the keyboard is headed - the scroll responder better have been notified of\n   * the keyboard destination before being instructed to scroll to where the\n   * keyboard will be. Stick to the `ScrollResponder` callbacks, and everything\n   * will work.\n   *\n   * WARNING: These callbacks will fire even if a keyboard is displayed in a\n   * different navigation pane. Filter out the events to determine if they are\n   * relevant to you. (For example, only if you receive these callbacks after\n   * you had explicitly focused a node etc).\n   */\n\n  /**\n   * ------------------------------------------------------\n   * END SCROLLRESPONDER\n   * ------------------------------------------------------\n   */\n\n  /**\n   * Returns a reference to the underlying scroll responder, which supports\n   * operations like `scrollTo`. All ScrollView-like components should\n   * implement this method so that they can be composed while providing access\n   * to the underlying scroll responder's methods.\n   */\n\n  /**\n   * Scrolls to a given x, y offset, either immediately or with a smooth animation.\n   * Syntax:\n   *\n   * scrollTo(options: {x: number = 0; y: number = 0; animated: boolean = true})\n   *\n   * Note: The weird argument signature is due to the fact that, for historical reasons,\n   * the function also accepts separate arguments as as alternative to the options object.\n   * This is deprecated due to ambiguity (y before x), and SHOULD NOT BE USED.\n   */\n\n  /**\n   * If this is a vertical ScrollView scrolls to the bottom.\n   * If this is a horizontal ScrollView scrolls to the right.\n   *\n   * Use `scrollToEnd({ animated: true })` for smooth animated scrolling,\n   * `scrollToEnd({ animated: false })` for immediate scrolling.\n   * If no options are passed, `animated` defaults to true.\n   */\n\n  render() {\n    var _this$props = this.props,\n      contentContainerStyle = _this$props.contentContainerStyle,\n      horizontal = _this$props.horizontal,\n      onContentSizeChange = _this$props.onContentSizeChange,\n      refreshControl = _this$props.refreshControl,\n      stickyHeaderIndices = _this$props.stickyHeaderIndices,\n      pagingEnabled = _this$props.pagingEnabled,\n      forwardedRef = _this$props.forwardedRef,\n      keyboardDismissMode = _this$props.keyboardDismissMode,\n      onScroll = _this$props.onScroll,\n      centerContent = _this$props.centerContent,\n      other = _objectWithoutPropertiesLoose(_this$props, _excluded);\n    if (process.env.NODE_ENV !== 'production' && this.props.style) {\n      var style = StyleSheet.flatten(this.props.style);\n      var childLayoutProps = ['alignItems', 'justifyContent'].filter(prop => style && style[prop] !== undefined);\n      invariant(childLayoutProps.length === 0, \"ScrollView child layout (\" + JSON.stringify(childLayoutProps) + \") \" + 'must be applied through the contentContainerStyle prop.');\n    }\n    var contentSizeChangeProps = {};\n    if (onContentSizeChange) {\n      contentSizeChangeProps = {\n        onLayout: this._handleContentOnLayout\n      };\n    }\n    var hasStickyHeaderIndices = !horizontal && Array.isArray(stickyHeaderIndices);\n    var children = hasStickyHeaderIndices || pagingEnabled ? React.Children.map(this.props.children, (child, i) => {\n      var isSticky = hasStickyHeaderIndices && stickyHeaderIndices.indexOf(i) > -1;\n      if (child != null && (isSticky || pagingEnabled)) {\n        return /*#__PURE__*/React.createElement(View, {\n          style: [isSticky && styles.stickyHeader, pagingEnabled && styles.pagingEnabledChild]\n        }, child);\n      } else {\n        return child;\n      }\n    }) : this.props.children;\n    var contentContainer = /*#__PURE__*/React.createElement(View, _extends({}, contentSizeChangeProps, {\n      children: children,\n      collapsable: false,\n      ref: this._setInnerViewRef,\n      style: [horizontal && styles.contentContainerHorizontal, centerContent && styles.contentContainerCenterContent, contentContainerStyle]\n    }));\n    var baseStyle = horizontal ? styles.baseHorizontal : styles.baseVertical;\n    var pagingEnabledStyle = horizontal ? styles.pagingEnabledHorizontal : styles.pagingEnabledVertical;\n    var props = _objectSpread(_objectSpread({}, other), {}, {\n      style: [baseStyle, pagingEnabled && pagingEnabledStyle, this.props.style],\n      onTouchStart: this.scrollResponderHandleTouchStart,\n      onTouchMove: this.scrollResponderHandleTouchMove,\n      onTouchEnd: this.scrollResponderHandleTouchEnd,\n      onScrollBeginDrag: this.scrollResponderHandleScrollBeginDrag,\n      onScrollEndDrag: this.scrollResponderHandleScrollEndDrag,\n      onMomentumScrollBegin: this.scrollResponderHandleMomentumScrollBegin,\n      onMomentumScrollEnd: this.scrollResponderHandleMomentumScrollEnd,\n      onStartShouldSetResponder: this.scrollResponderHandleStartShouldSetResponder,\n      onStartShouldSetResponderCapture: this.scrollResponderHandleStartShouldSetResponderCapture,\n      onScrollShouldSetResponder: this.scrollResponderHandleScrollShouldSetResponder,\n      onScroll: this._handleScroll,\n      onResponderGrant: this.scrollResponderHandleResponderGrant,\n      onResponderTerminationRequest: this.scrollResponderHandleTerminationRequest,\n      onResponderTerminate: this.scrollResponderHandleTerminate,\n      onResponderRelease: this.scrollResponderHandleResponderRelease,\n      onResponderReject: this.scrollResponderHandleResponderReject\n    });\n    var ScrollViewClass = ScrollViewBase;\n    invariant(ScrollViewClass !== undefined, 'ScrollViewClass must not be undefined');\n    var scrollView = /*#__PURE__*/React.createElement(ScrollViewClass, _extends({}, props, {\n      ref: this._setScrollNodeRef\n    }), contentContainer);\n    if (refreshControl) {\n      return /*#__PURE__*/React.cloneElement(refreshControl, {\n        style: props.style\n      }, scrollView);\n    }\n    return scrollView;\n  }\n}\nvar commonStyle = {\n  flexGrow: 1,\n  flexShrink: 1,\n  // Enable hardware compositing in modern browsers.\n  // Creates a new layer with its own backing surface that can significantly\n  // improve scroll performance.\n  transform: 'translateZ(0)',\n  // iOS native scrolling\n  WebkitOverflowScrolling: 'touch'\n};\nvar styles = StyleSheet.create({\n  baseVertical: _objectSpread(_objectSpread({}, commonStyle), {}, {\n    flexDirection: 'column',\n    overflowX: 'hidden',\n    overflowY: 'auto'\n  }),\n  baseHorizontal: _objectSpread(_objectSpread({}, commonStyle), {}, {\n    flexDirection: 'row',\n    overflowX: 'auto',\n    overflowY: 'hidden'\n  }),\n  contentContainerHorizontal: {\n    flexDirection: 'row'\n  },\n  contentContainerCenterContent: {\n    justifyContent: 'center',\n    flexGrow: 1\n  },\n  stickyHeader: {\n    position: 'sticky',\n    top: 0,\n    zIndex: 10\n  },\n  pagingEnabledHorizontal: {\n    scrollSnapType: 'x mandatory'\n  },\n  pagingEnabledVertical: {\n    scrollSnapType: 'y mandatory'\n  },\n  pagingEnabledChild: {\n    scrollSnapAlign: 'start'\n  }\n});\nvar ForwardedScrollView = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  return /*#__PURE__*/React.createElement(ScrollView, _extends({}, props, {\n    forwardedRef: forwardedRef\n  }));\n});\nForwardedScrollView.displayName = 'ScrollView';\nexport default ForwardedScrollView;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAJ,eAAA,CAAAI,CAAA,GAAAL,0BAAA,CAAAI,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAL,eAAA,CAAAG,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAEb,OAAOY,aAAa,MAAM,sCAAsC;AAChE,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,uBAAuB,EAAE,YAAY,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,eAAe,EAAE,cAAc,EAAE,qBAAqB,EAAE,UAAU,EAAE,eAAe,CAAC;AAC5M,OAAOC,UAAU;AACjB,OAAOC,eAAe;AACtB,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,SAAS;AAChB,OAAOC,QAAQ;AACf,OAAOC,cAAc;AACrB,OAAOC,UAAU;AACjB,OAAOC,cAAc;AACrB,OAAOC,SAAS;AAChB,OAAOC,IAAI;AACX,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,kBAAkB;AACtC,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,qCAAqC,GAAG,EAAE;AAAC,IACzCC,UAAU,aAAAC,gBAAA;EACd,SAAAD,WAAA,EAAc;IAAA,IAAAE,KAAA;IAAAtC,eAAA,OAAAoC,UAAA;IACZE,KAAA,GAAAjC,UAAA,OAAA+B,UAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,cAAc,GAAG,IAAI;IAC1BF,KAAA,CAAKG,aAAa,GAAG,IAAI;IACzBH,KAAA,CAAKI,UAAU,GAAG,KAAK;IACvBJ,KAAA,CAAKK,2BAA2B,GAAG,CAAC;IACpCL,KAAA,CAAKM,yBAAyB,GAAG,CAAC;IAClCN,KAAA,CAAKO,oCAAoC,GAAG,KAAK;IACjDP,KAAA,CAAKQ,6BAA6B,GAAG,KAAK;IAC1CR,KAAA,CAAKS,6CAA6C,GAAG,YAAM;MACzD,OAAOT,KAAA,CAAKI,UAAU;IACxB,CAAC;IACDJ,KAAA,CAAKU,mDAAmD,GAAG,UAAAxC,CAAC,EAAI;MAQ9D,OAAO8B,KAAA,CAAKW,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IACDX,KAAA,CAAKY,uCAAuC,GAAG,YAAM;MACnD,OAAO,CAACZ,KAAA,CAAKO,oCAAoC;IACnD,CAAC;IACDP,KAAA,CAAKa,6BAA6B,GAAG,UAAA3C,CAAC,EAAI;MACxC,IAAI4C,WAAW,GAAG5C,CAAC,CAAC4C,WAAW;MAC/Bd,KAAA,CAAKI,UAAU,GAAGU,WAAW,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC;MAClDhB,KAAA,CAAKiB,KAAK,CAACC,UAAU,IAAIlB,KAAA,CAAKiB,KAAK,CAACC,UAAU,CAAChD,CAAC,CAAC;IACnD,CAAC;IACD8B,KAAA,CAAKmB,qCAAqC,GAAG,UAAAjD,CAAC,EAAI;MAChD8B,KAAA,CAAKiB,KAAK,CAACG,kBAAkB,IAAIpB,KAAA,CAAKiB,KAAK,CAACG,kBAAkB,CAAClD,CAAC,CAAC;MAIjE,IAAImD,yBAAyB,GAAG9B,cAAc,CAAC+B,qBAAqB,CAAC,CAAC;MACtE,IAAI,CAACtB,KAAA,CAAKiB,KAAK,CAACM,yBAAyB,IAAIF,yBAAyB,IAAI,IAAI,IAAInD,CAAC,CAACsD,MAAM,KAAKH,yBAAyB,IAAI,CAACrB,KAAA,CAAKO,oCAAoC,IAAI,CAACP,KAAA,CAAKQ,6BAA6B,EAAE;QAC7MR,KAAA,CAAKiB,KAAK,CAACQ,kCAAkC,IAAIzB,KAAA,CAAKiB,KAAK,CAACQ,kCAAkC,CAACvD,CAAC,CAAC;QACjGqB,cAAc,CAACmC,aAAa,CAACL,yBAAyB,CAAC;MACzD;IACF,CAAC;IACDrB,KAAA,CAAK2B,2BAA2B,GAAG,UAAAzD,CAAC,EAAI;MACtC8B,KAAA,CAAKO,oCAAoC,GAAG,IAAI;MAChDP,KAAA,CAAKiB,KAAK,CAACW,QAAQ,IAAI5B,KAAA,CAAKiB,KAAK,CAACW,QAAQ,CAAC1D,CAAC,CAAC;IAC/C,CAAC;IACD8B,KAAA,CAAK6B,mCAAmC,GAAG,UAAA3D,CAAC,EAAI;MAC9C8B,KAAA,CAAKO,oCAAoC,GAAG,KAAK;MACjDP,KAAA,CAAKiB,KAAK,CAACa,gBAAgB,IAAI9B,KAAA,CAAKiB,KAAK,CAACa,gBAAgB,CAAC5D,CAAC,CAAC;MAC7D8B,KAAA,CAAKQ,6BAA6B,GAAGR,KAAA,CAAKW,0BAA0B,CAAC,CAAC;IACxE,CAAC;IACDX,KAAA,CAAK+B,oCAAoC,GAAG,UAAA7D,CAAC,EAAI;MAC/C8B,KAAA,CAAKiB,KAAK,CAACe,iBAAiB,IAAIhC,KAAA,CAAKiB,KAAK,CAACe,iBAAiB,CAAC9D,CAAC,CAAC;IACjE,CAAC;IACD8B,KAAA,CAAKiC,kCAAkC,GAAG,UAAA/D,CAAC,EAAI;MAC7C8B,KAAA,CAAKiB,KAAK,CAACiB,eAAe,IAAIlC,KAAA,CAAKiB,KAAK,CAACiB,eAAe,CAAChE,CAAC,CAAC;IAC7D,CAAC;IACD8B,KAAA,CAAKmC,wCAAwC,GAAG,UAAAjE,CAAC,EAAI;MACnD8B,KAAA,CAAKK,2BAA2B,GAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC7CrC,KAAA,CAAKiB,KAAK,CAACqB,qBAAqB,IAAItC,KAAA,CAAKiB,KAAK,CAACqB,qBAAqB,CAACpE,CAAC,CAAC;IACzE,CAAC;IACD8B,KAAA,CAAKuC,sCAAsC,GAAG,UAAArE,CAAC,EAAI;MACjD8B,KAAA,CAAKM,yBAAyB,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3CrC,KAAA,CAAKiB,KAAK,CAACuB,mBAAmB,IAAIxC,KAAA,CAAKiB,KAAK,CAACuB,mBAAmB,CAACtE,CAAC,CAAC;IACrE,CAAC;IACD8B,KAAA,CAAKyC,+BAA+B,GAAG,UAAAvE,CAAC,EAAI;MAC1C8B,KAAA,CAAKI,UAAU,GAAG,IAAI;MACtBJ,KAAA,CAAKiB,KAAK,CAACyB,YAAY,IAAI1C,KAAA,CAAKiB,KAAK,CAACyB,YAAY,CAACxE,CAAC,CAAC;IACvD,CAAC;IACD8B,KAAA,CAAK2C,8BAA8B,GAAG,UAAAzE,CAAC,EAAI;MACzC8B,KAAA,CAAKiB,KAAK,CAAC2B,WAAW,IAAI5C,KAAA,CAAKiB,KAAK,CAAC2B,WAAW,CAAC1E,CAAC,CAAC;IACrD,CAAC;IACD8B,KAAA,CAAKW,0BAA0B,GAAG,YAAM;MACtC,IAAI0B,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIQ,8BAA8B,GAAGR,GAAG,GAAGrC,KAAA,CAAKM,yBAAyB;MACzE,IAAIwC,WAAW,GAAGD,8BAA8B,GAAGhD,qCAAqC,IAAIG,KAAA,CAAKM,yBAAyB,GAAGN,KAAA,CAAKK,2BAA2B;MAC7J,OAAOyC,WAAW;IACpB,CAAC;IACD9C,KAAA,CAAK+C,uBAAuB,GAAG,UAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAK;MACjD,IAAI,OAAOF,CAAC,KAAK,QAAQ,EAAE;QACzBG,OAAO,CAACC,IAAI,CAAC,+HAA+H,CAAC;MAC/I,CAAC,MAAM;QACL,IAAIC,IAAI,GAAGL,CAAC,IAAIpD,WAAW;QAC3BoD,CAAC,GAAGK,IAAI,CAACL,CAAC;QACVC,CAAC,GAAGI,IAAI,CAACJ,CAAC;QACVC,QAAQ,GAAGG,IAAI,CAACH,QAAQ;MAC1B;MACA,IAAII,IAAI,GAAGtD,KAAA,CAAKuD,iBAAiB,CAAC,CAAC;MACnC,IAAIC,IAAI,GAAGR,CAAC,IAAI,CAAC;MACjB,IAAIS,GAAG,GAAGR,CAAC,IAAI,CAAC;MAChB,IAAIK,IAAI,IAAI,IAAI,EAAE;QAChB,IAAI,OAAOA,IAAI,CAACI,MAAM,KAAK,UAAU,EAAE;UACrCJ,IAAI,CAACI,MAAM,CAAC;YACVD,GAAG,EAAHA,GAAG;YACHD,IAAI,EAAJA,IAAI;YACJG,QAAQ,EAAE,CAACT,QAAQ,GAAG,MAAM,GAAG;UACjC,CAAC,CAAC;QACJ,CAAC,MAAM;UACLI,IAAI,CAACM,UAAU,GAAGJ,IAAI;UACtBF,IAAI,CAACO,SAAS,GAAGJ,GAAG;QACtB;MACF;IACF,CAAC;IACDzD,KAAA,CAAK8D,qBAAqB,GAAG,UAACC,IAAI,EAAEb,QAAQ,EAAK;MAC/C,IAAI9D,QAAQ,CAAC4E,EAAE,KAAK,KAAK,EAAE;QACzB9E,SAAS,CAAC,+BAA+B,CAAC;MAC5C;IACF,CAAC;IACDc,KAAA,CAAKiE,2CAA2C,GAAG,UAACC,UAAU,EAAEC,gBAAgB,EAAEC,2BAA2B,EAAK;MAChHpE,KAAA,CAAKqE,sBAAsB,GAAGF,gBAAgB,IAAI,CAAC;MACnDnE,KAAA,CAAKoE,2BAA2B,GAAG,CAAC,CAACA,2BAA2B;MAChE5E,SAAS,CAAC8E,aAAa,CAACJ,UAAU,EAAElE,KAAA,CAAKuE,gBAAgB,CAAC,CAAC,EAAEvE,KAAA,CAAKwE,kCAAkC,EAAExE,KAAA,CAAKyE,8CAA8C,CAAC;IAC5J,CAAC;IACDzE,KAAA,CAAKyE,8CAA8C,GAAG,UAACjB,IAAI,EAAEC,GAAG,EAAEiB,KAAK,EAAEC,MAAM,EAAK;MAClF,IAAIC,eAAe,GAAG5F,UAAU,CAAC6F,GAAG,CAAC,QAAQ,CAAC,CAACF,MAAM;MACrD,IAAI3E,KAAA,CAAK8E,kBAAkB,EAAE;QAC3BF,eAAe,GAAG5E,KAAA,CAAK8E,kBAAkB,CAACC,cAAc,CAACC,OAAO;MAClE;MACA,IAAIC,aAAa,GAAGxB,GAAG,GAAGmB,eAAe,GAAGD,MAAM,GAAG3E,KAAA,CAAKqE,sBAAsB;MAMhF,IAAIrE,KAAA,CAAKoE,2BAA2B,EAAE;QACpCa,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,aAAa,CAAC;MAC5C;MACAjF,KAAA,CAAK+C,uBAAuB,CAAC;QAC3BC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAEgC,aAAa;QAChB/B,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFlD,KAAA,CAAKmE,gBAAgB,GAAG,CAAC;MACzBnE,KAAA,CAAKoE,2BAA2B,GAAG,KAAK;IAC1C,CAAC;IACDpE,KAAA,CAAKoF,+BAA+B,GAAG,UAAAlH,CAAC,EAAI;MAC1C8B,KAAA,CAAK8E,kBAAkB,GAAG5G,CAAC;MAC3B8B,KAAA,CAAKiB,KAAK,CAACoE,kBAAkB,IAAIrF,KAAA,CAAKiB,KAAK,CAACoE,kBAAkB,CAACnH,CAAC,CAAC;IACnE,CAAC;IACD8B,KAAA,CAAKsF,+BAA+B,GAAG,UAAApH,CAAC,EAAI;MAC1C8B,KAAA,CAAK8E,kBAAkB,GAAG,IAAI;MAC9B9E,KAAA,CAAKiB,KAAK,CAACsE,kBAAkB,IAAIvF,KAAA,CAAKiB,KAAK,CAACsE,kBAAkB,CAACrH,CAAC,CAAC;IACnE,CAAC;IACD8B,KAAA,CAAKwF,8BAA8B,GAAG,UAAAtH,CAAC,EAAI;MAGzC,IAAIA,CAAC,EAAE;QACL8B,KAAA,CAAK8E,kBAAkB,GAAG5G,CAAC;MAC7B;MACA8B,KAAA,CAAKiB,KAAK,CAACwE,iBAAiB,IAAIzF,KAAA,CAAKiB,KAAK,CAACwE,iBAAiB,CAACvH,CAAC,CAAC;IACjE,CAAC;IACD8B,KAAA,CAAK0F,8BAA8B,GAAG,UAAAxH,CAAC,EAAI;MACzC8B,KAAA,CAAK8E,kBAAkB,GAAG,IAAI;MAC9B9E,KAAA,CAAKiB,KAAK,CAAC0E,iBAAiB,IAAI3F,KAAA,CAAKiB,KAAK,CAAC0E,iBAAiB,CAACzH,CAAC,CAAC;IACjE,CAAC;IACD8B,KAAA,CAAK4F,qBAAqB,GAAG,YAAM;MACjC5F,KAAA,CAAK6F,oCAAoC,CAAC,CAAC;IAC7C,CAAC;IACD7F,KAAA,CAAK8F,kBAAkB,GAAG,YAAM;MAC9B,OAAA9F,KAAA;IACF,CAAC;IACDA,KAAA,CAAKuD,iBAAiB,GAAG,YAAM;MAC7B,OAAOvD,KAAA,CAAKE,cAAc;IAC5B,CAAC;IACDF,KAAA,CAAK+F,eAAe,GAAG,YAAM;MAC3B,OAAO/F,KAAA,CAAKG,aAAa;IAC3B,CAAC;IACDH,KAAA,CAAKuE,gBAAgB,GAAG,YAAM;MAC5B,OAAOvE,KAAA,CAAKG,aAAa;IAC3B,CAAC;IACDH,KAAA,CAAKgG,kBAAkB,GAAG,YAAM;MAC9B,OAAOhG,KAAA,CAAKE,cAAc;IAC5B,CAAC;IACDF,KAAA,CAAKiG,QAAQ,GAAG,UAAChD,CAAC,EAAED,CAAC,EAAEE,QAAQ,EAAK;MAClC,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;QACzBE,OAAO,CAACC,IAAI,CAAC,iGAAiG,CAAC;MACjH,CAAC,MAAM;QACL,IAAI8C,KAAK,GAAGjD,CAAC,IAAIrD,WAAW;QAC5BoD,CAAC,GAAGkD,KAAK,CAAClD,CAAC;QACXC,CAAC,GAAGiD,KAAK,CAACjD,CAAC;QACXC,QAAQ,GAAGgD,KAAK,CAAChD,QAAQ;MAC3B;MACAlD,KAAA,CAAK+C,uBAAuB,CAAC;QAC3BC,CAAC,EAAEA,CAAC,IAAI,CAAC;QACTC,CAAC,EAAEA,CAAC,IAAI,CAAC;QACTC,QAAQ,EAAEA,QAAQ,KAAK;MACzB,CAAC,CAAC;IACJ,CAAC;IACDlD,KAAA,CAAKmG,WAAW,GAAG,UAAAC,OAAO,EAAI;MAE5B,IAAIlD,QAAQ,GAAG,CAACkD,OAAO,IAAIA,OAAO,CAAClD,QAAQ,MAAM,KAAK;MACtD,IAAImD,UAAU,GAAGrG,KAAA,CAAKiB,KAAK,CAACoF,UAAU;MACtC,IAAIC,mBAAmB,GAAGtG,KAAA,CAAKuD,iBAAiB,CAAC,CAAC;MAClD,IAAIP,CAAC,GAAGqD,UAAU,GAAGC,mBAAmB,CAACC,WAAW,GAAG,CAAC;MACxD,IAAItD,CAAC,GAAGoD,UAAU,GAAG,CAAC,GAAGC,mBAAmB,CAACE,YAAY;MACzDxG,KAAA,CAAK+C,uBAAuB,CAAC;QAC3BC,CAAC,EAADA,CAAC;QACDC,CAAC,EAADA,CAAC;QACDC,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ,CAAC;IACDlD,KAAA,CAAKyG,sBAAsB,GAAG,UAAAvI,CAAC,EAAI;MACjC,IAAIwI,qBAAqB,GAAGxI,CAAC,CAAC4C,WAAW,CAAC6F,MAAM;QAC9CjC,KAAK,GAAGgC,qBAAqB,CAAChC,KAAK;QACnCC,MAAM,GAAG+B,qBAAqB,CAAC/B,MAAM;MACvC3E,KAAA,CAAKiB,KAAK,CAAC2F,mBAAmB,CAAClC,KAAK,EAAEC,MAAM,CAAC;IAC/C,CAAC;IACD3E,KAAA,CAAK6G,aAAa,GAAG,UAAA3I,CAAC,EAAI;MACxB,IAAI4I,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIhH,KAAA,CAAKiB,KAAK,CAACW,QAAQ,IAAI5B,KAAA,CAAKiB,KAAK,CAACgG,mBAAmB,IAAI,IAAI,EAAE;UACjE9D,OAAO,CAAC+D,GAAG,CAAC,qDAAqD,GAAG,0DAA0D,GAAG,6DAA6D,GAAG,8DAA8D,GAAG,iBAAiB,CAAC;QACtR;MACF;MACA,IAAIlH,KAAA,CAAKiB,KAAK,CAACkG,mBAAmB,KAAK,SAAS,EAAE;QAChDlI,eAAe,CAAC,CAAC;MACnB;MACAe,KAAA,CAAK2B,2BAA2B,CAACzD,CAAC,CAAC;IACrC,CAAC;IACD8B,KAAA,CAAKoH,gBAAgB,GAAG,UAAA9D,IAAI,EAAI;MAC9BtD,KAAA,CAAKG,aAAa,GAAGmD,IAAI;IAC3B,CAAC;IACDtD,KAAA,CAAKqH,iBAAiB,GAAG,UAAA/D,IAAI,EAAI;MAC/BtD,KAAA,CAAKE,cAAc,GAAGoD,IAAI;MAI1B,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,CAACwC,kBAAkB,GAAG9F,KAAA,CAAK8F,kBAAkB;QACjDxC,IAAI,CAACiB,gBAAgB,GAAGvE,KAAA,CAAKuE,gBAAgB;QAC7CjB,IAAI,CAACyC,eAAe,GAAG/F,KAAA,CAAK+F,eAAe;QAC3CzC,IAAI,CAAC0C,kBAAkB,GAAGhG,KAAA,CAAKgG,kBAAkB;QACjD1C,IAAI,CAACC,iBAAiB,GAAGvD,KAAA,CAAKuD,iBAAiB;QAC/CD,IAAI,CAAC2C,QAAQ,GAAGjG,KAAA,CAAKiG,QAAQ;QAC7B3C,IAAI,CAAC6C,WAAW,GAAGnG,KAAA,CAAKmG,WAAW;QACnC7C,IAAI,CAACsC,qBAAqB,GAAG5F,KAAA,CAAK4F,qBAAqB;QACvDtC,IAAI,CAACQ,qBAAqB,GAAG9D,KAAA,CAAK8D,qBAAqB;QACvDR,IAAI,CAACW,2CAA2C,GAAGjE,KAAA,CAAKiE,2CAA2C;MACrG;MACA,IAAIqD,GAAG,GAAGnI,SAAS,CAACa,KAAA,CAAKiB,KAAK,CAACsG,YAAY,CAAC;MAC5CD,GAAG,CAAChE,IAAI,CAAC;IACX,CAAC;IAAC,OAAAtD,KAAA;EACJ;EAAClC,SAAA,CAAAgC,UAAA,EAAAC,gBAAA;EAAA,OAAApC,YAAA,CAAAmC,UAAA;IAAA0H,GAAA;IAAAC,KAAA,EAuCD,SAAAC,4CAA4CA,CAAA,EAAG;MAC7C,OAAO,KAAK;IACd;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAwBD,SAAAE,oCAAoCA,CAAA,EAAG;MACrChI,OAAO,CAAC,KAAK,EAAE,yDAAyD,CAAC;IAC3E;EAAC;IAAA6H,GAAA;IAAAC,KAAA,EAwGD,SAAA5B,oCAAoCA,CAAA,EAAG,CAAC;EAAC;IAAA2B,GAAA;IAAAC,KAAA,EAwBzC,SAAAjD,kCAAkCA,CAACtG,CAAC,EAAE;MACpCiF,OAAO,CAACyE,KAAK,CAAC,8BAA8B,EAAE1J,CAAC,CAAC;IAClD;EAAC;IAAAsJ,GAAA;IAAAC,KAAA,EAgED,SAAAI,MAAMA,CAAA,EAAG;MACP,IAAIC,WAAW,GAAG,IAAI,CAAC7G,KAAK;QAC1B8G,qBAAqB,GAAGD,WAAW,CAACC,qBAAqB;QACzD1B,UAAU,GAAGyB,WAAW,CAACzB,UAAU;QACnCO,mBAAmB,GAAGkB,WAAW,CAAClB,mBAAmB;QACrDoB,cAAc,GAAGF,WAAW,CAACE,cAAc;QAC3CC,mBAAmB,GAAGH,WAAW,CAACG,mBAAmB;QACrDC,aAAa,GAAGJ,WAAW,CAACI,aAAa;QACzCX,YAAY,GAAGO,WAAW,CAACP,YAAY;QACvCJ,mBAAmB,GAAGW,WAAW,CAACX,mBAAmB;QACrDvF,QAAQ,GAAGkG,WAAW,CAAClG,QAAQ;QAC/BuG,aAAa,GAAGL,WAAW,CAACK,aAAa;QACzCC,KAAK,GAAGtJ,6BAA6B,CAACgJ,WAAW,EAAE/I,SAAS,CAAC;MAC/D,IAAI+H,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,IAAI,CAAC/F,KAAK,CAACoH,KAAK,EAAE;QAC7D,IAAIA,KAAK,GAAG/I,UAAU,CAACgJ,OAAO,CAAC,IAAI,CAACrH,KAAK,CAACoH,KAAK,CAAC;QAChD,IAAIE,gBAAgB,GAAG,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAACC,MAAM,CAAC,UAAAC,IAAI;UAAA,OAAIJ,KAAK,IAAIA,KAAK,CAACI,IAAI,CAAC,KAAKC,SAAS;QAAA,EAAC;QAC1GxJ,SAAS,CAACqJ,gBAAgB,CAACvH,MAAM,KAAK,CAAC,EAAE,2BAA2B,GAAG2H,IAAI,CAACC,SAAS,CAACL,gBAAgB,CAAC,GAAG,IAAI,GAAG,yDAAyD,CAAC;MAC7K;MACA,IAAIM,sBAAsB,GAAG,CAAC,CAAC;MAC/B,IAAIjC,mBAAmB,EAAE;QACvBiC,sBAAsB,GAAG;UACvBC,QAAQ,EAAE,IAAI,CAACrC;QACjB,CAAC;MACH;MACA,IAAIsC,sBAAsB,GAAG,CAAC1C,UAAU,IAAI2C,KAAK,CAACC,OAAO,CAAChB,mBAAmB,CAAC;MAC9E,IAAIiB,QAAQ,GAAGH,sBAAsB,IAAIb,aAAa,GAAGxI,KAAK,CAACyJ,QAAQ,CAACC,GAAG,CAAC,IAAI,CAACnI,KAAK,CAACiI,QAAQ,EAAE,UAACG,KAAK,EAAEC,CAAC,EAAK;QAC7G,IAAIC,QAAQ,GAAGR,sBAAsB,IAAId,mBAAmB,CAACuB,OAAO,CAACF,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5E,IAAID,KAAK,IAAI,IAAI,KAAKE,QAAQ,IAAIrB,aAAa,CAAC,EAAE;UAChD,OAAoBxI,KAAK,CAAC+J,aAAa,CAAChK,IAAI,EAAE;YAC5C4I,KAAK,EAAE,CAACkB,QAAQ,IAAIG,MAAM,CAACC,YAAY,EAAEzB,aAAa,IAAIwB,MAAM,CAACE,kBAAkB;UACrF,CAAC,EAAEP,KAAK,CAAC;QACX,CAAC,MAAM;UACL,OAAOA,KAAK;QACd;MACF,CAAC,CAAC,GAAG,IAAI,CAACpI,KAAK,CAACiI,QAAQ;MACxB,IAAIW,gBAAgB,GAAgBnK,KAAK,CAAC+J,aAAa,CAAChK,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEgK,sBAAsB,EAAE;QACjGK,QAAQ,EAAEA,QAAQ;QAClBY,WAAW,EAAE,KAAK;QAClBxC,GAAG,EAAE,IAAI,CAACF,gBAAgB;QAC1BiB,KAAK,EAAE,CAAChC,UAAU,IAAIqD,MAAM,CAACK,0BAA0B,EAAE5B,aAAa,IAAIuB,MAAM,CAACM,6BAA6B,EAAEjC,qBAAqB;MACvI,CAAC,CAAC,CAAC;MACH,IAAIkC,SAAS,GAAG5D,UAAU,GAAGqD,MAAM,CAACQ,cAAc,GAAGR,MAAM,CAACS,YAAY;MACxE,IAAIC,kBAAkB,GAAG/D,UAAU,GAAGqD,MAAM,CAACW,uBAAuB,GAAGX,MAAM,CAACY,qBAAqB;MACnG,IAAIrJ,KAAK,GAAGrC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwJ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACtDC,KAAK,EAAE,CAAC4B,SAAS,EAAE/B,aAAa,IAAIkC,kBAAkB,EAAE,IAAI,CAACnJ,KAAK,CAACoH,KAAK,CAAC;QACzE3F,YAAY,EAAE,IAAI,CAACD,+BAA+B;QAClDG,WAAW,EAAE,IAAI,CAACD,8BAA8B;QAChDzB,UAAU,EAAE,IAAI,CAACL,6BAA6B;QAC9CmB,iBAAiB,EAAE,IAAI,CAACD,oCAAoC;QAC5DG,eAAe,EAAE,IAAI,CAACD,kCAAkC;QACxDK,qBAAqB,EAAE,IAAI,CAACH,wCAAwC;QACpEK,mBAAmB,EAAE,IAAI,CAACD,sCAAsC;QAChEgI,yBAAyB,EAAE,IAAI,CAAC7C,4CAA4C;QAC5E8C,gCAAgC,EAAE,IAAI,CAAC9J,mDAAmD;QAC1F+J,0BAA0B,EAAE,IAAI,CAAChK,6CAA6C;QAC9EmB,QAAQ,EAAE,IAAI,CAACiF,aAAa;QAC5B/E,gBAAgB,EAAE,IAAI,CAACD,mCAAmC;QAC1D6I,6BAA6B,EAAE,IAAI,CAAC9J,uCAAuC;QAC3E+J,oBAAoB,EAAE,IAAI,CAACC,8BAA8B;QACzDxJ,kBAAkB,EAAE,IAAI,CAACD,qCAAqC;QAC9D0J,iBAAiB,EAAE,IAAI,CAAClD;MAC1B,CAAC,CAAC;MACF,IAAImD,eAAe,GAAGzL,cAAc;MACpCH,SAAS,CAAC4L,eAAe,KAAKpC,SAAS,EAAE,uCAAuC,CAAC;MACjF,IAAIqC,UAAU,GAAgBrL,KAAK,CAAC+J,aAAa,CAACqB,eAAe,EAAEjM,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;QACrFqG,GAAG,EAAE,IAAI,CAACD;MACZ,CAAC,CAAC,EAAEwC,gBAAgB,CAAC;MACrB,IAAI7B,cAAc,EAAE;QAClB,OAAoBtI,KAAK,CAACsL,YAAY,CAAChD,cAAc,EAAE;UACrDK,KAAK,EAAEpH,KAAK,CAACoH;QACf,CAAC,EAAE0C,UAAU,CAAC;MAChB;MACA,OAAOA,UAAU;IACnB;EAAC;AAAA,EA/jBsBrL,KAAK,CAACuL,SAAS;AAikBxC,IAAIC,WAAW,GAAG;EAChBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE,CAAC;EAIbC,SAAS,EAAE,eAAe;EAE1BC,uBAAuB,EAAE;AAC3B,CAAC;AACD,IAAI5B,MAAM,GAAGpK,UAAU,CAACiM,MAAM,CAAC;EAC7BpB,YAAY,EAAEvL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsM,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;IAC9DM,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE;EACb,CAAC,CAAC;EACFxB,cAAc,EAAEtL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsM,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;IAChEM,aAAa,EAAE,KAAK;IACpBC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF3B,0BAA0B,EAAE;IAC1ByB,aAAa,EAAE;EACjB,CAAC;EACDxB,6BAA6B,EAAE;IAC7B2B,cAAc,EAAE,QAAQ;IACxBR,QAAQ,EAAE;EACZ,CAAC;EACDxB,YAAY,EAAE;IACZiC,QAAQ,EAAE,QAAQ;IAClBnI,GAAG,EAAE,CAAC;IACNoI,MAAM,EAAE;EACV,CAAC;EACDxB,uBAAuB,EAAE;IACvByB,cAAc,EAAE;EAClB,CAAC;EACDxB,qBAAqB,EAAE;IACrBwB,cAAc,EAAE;EAClB,CAAC;EACDlC,kBAAkB,EAAE;IAClBmC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AACF,IAAIC,mBAAmB,GAAgBtM,KAAK,CAACuM,UAAU,CAAC,UAAChL,KAAK,EAAEsG,YAAY,EAAK;EAC/E,OAAoB7H,KAAK,CAAC+J,aAAa,CAAC3J,UAAU,EAAEjB,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;IACtEsG,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFyE,mBAAmB,CAACE,WAAW,GAAG,YAAY;AAC9C,eAAeF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}