{"ast": null, "code": "function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport StyledText from \"./StyledText\";\nvar Subheading = function Subheading(props) {\n  return React.createElement(StyledText, _extends({}, props, {\n    alpha: 0.87,\n    family: \"regular\",\n    style: [styles.text, props.style]\n  }));\n};\nexport default Subheading;\nvar styles = StyleSheet.create({\n  text: {\n    fontSize: 16,\n    lineHeight: 24,\n    marginVertical: 2,\n    letterSpacing: 0.5\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "StyledText", "Subheading", "props", "createElement", "_extends", "alpha", "family", "style", "styles", "text", "create", "fontSize", "lineHeight", "marginVertical", "letterSpacing"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-paper/src/components/Typography/v2/Subheading.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Text, TextStyle, StyleSheet, StyleProp } from 'react-native';\n\nimport StyledText from './StyledText';\n\nexport type Props = React.ComponentProps<typeof Text> & {\n  style?: StyleProp<TextStyle>;\n  children: React.ReactNode;\n};\n\n// @component-group Typography\n\n/**\n * @deprecated Deprecated in v5.x - use `<Text variant=\"titleMedium\" />` instead.\n * Typography component for showing a subheading.\n *\n * <div class=\"screenshots\">\n *   <img src=\"screenshots/subheading.png\" />\n * </div>\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Subheading } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Subheading>Subheading</Subheading>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Subheading = (props: Props) => (\n  <StyledText\n    {...props}\n    alpha={0.87}\n    family=\"regular\"\n    style={[styles.text, props.style]}\n  />\n);\n\nexport default Subheading;\n\nconst styles = StyleSheet.create({\n  text: {\n    fontSize: 16,\n    lineHeight: 24,\n    marginVertical: 2,\n    letterSpacing: 0.5,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAG9B,OAAOC,UAAU;AA6BjB,IAAMC,UAAU,GAAI,SAAdA,UAAUA,CAAIC,KAAY;EAAA,OAC9BJ,KAAA,CAAAK,aAAA,CAACH,UAAU,EAAAI,QAAA,KACLF,KAAK;IACTG,KAAK,EAAE,IAAK;IACZC,MAAM,EAAC,SAAS;IAChBC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAEP,KAAK,CAACK,KAAK;EAAE,EACnC,CACF;AAAA;AAED,eAAeN,UAAU;AAEzB,IAAMO,MAAM,GAAGT,UAAU,CAACW,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}