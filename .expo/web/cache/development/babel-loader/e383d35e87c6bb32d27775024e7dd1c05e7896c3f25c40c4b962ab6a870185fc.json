{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport SegmentedButtonItem from \"./SegmentedButtonItem\";\nimport { getDisabledSegmentedButtonStyle } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar SegmentedButtons = function SegmentedButtons(_ref) {\n  var value = _ref.value,\n    onValueChange = _ref.onValueChange,\n    buttons = _ref.buttons,\n    multiSelect = _ref.multiSelect,\n    density = _ref.density,\n    style = _ref.style,\n    themeOverrides = _ref.theme;\n  var theme = useInternalTheme(themeOverrides);\n  return React.createElement(View, {\n    style: [styles.row, style]\n  }, buttons.map(function (item, i) {\n    var disabledChildStyle = getDisabledSegmentedButtonStyle({\n      theme: theme,\n      buttons: buttons,\n      index: i\n    });\n    var segment = i === 0 ? 'first' : i === buttons.length - 1 ? 'last' : undefined;\n    var checked = multiSelect && Array.isArray(value) ? value.includes(item.value) : value === item.value;\n    var onPress = function onPress(e) {\n      var _item$onPress;\n      (_item$onPress = item.onPress) === null || _item$onPress === void 0 ? void 0 : _item$onPress.call(item, e);\n      var nextValue = multiSelect && Array.isArray(value) ? checked ? value.filter(function (val) {\n        return item.value !== val;\n      }) : [].concat(_toConsumableArray(value), [item.value]) : item.value;\n      onValueChange(nextValue);\n    };\n    return React.createElement(SegmentedButtonItem, _extends({}, item, {\n      key: i,\n      checked: checked,\n      segment: segment,\n      density: density,\n      onPress: onPress,\n      style: [item.style, disabledChildStyle],\n      labelStyle: item.labelStyle,\n      theme: theme\n    }));\n  }));\n};\nvar styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row'\n  }\n});\nexport default SegmentedButtons;\nexport { SegmentedButtons };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "SegmentedButtonItem", "getDisabledSegmentedButtonStyle", "useInternalTheme", "SegmentedButtons", "_ref", "value", "onValueChange", "buttons", "multiSelect", "density", "style", "themeOverrides", "theme", "createElement", "styles", "row", "map", "item", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "segment", "length", "undefined", "checked", "Array", "isArray", "includes", "onPress", "e", "_item$onPress", "call", "nextValue", "filter", "val", "concat", "_toConsumableArray", "_extends", "key", "labelStyle", "create", "flexDirection"], "sources": ["/workspaces/codespaces-blank/NutritionTracker/app/node_modules/react-native-paper/src/components/SegmentedButtons/SegmentedButtons.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  GestureResponderEvent,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport SegmentedButtonItem from './SegmentedButtonItem';\nimport { getDisabledSegmentedButtonStyle } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { IconSource } from '../Icon';\n\ntype ConditionalValue<T extends string = string> =\n  | {\n      /**\n       * Array of the currently selected segmented button values.\n       */\n      value: T[];\n      /**\n       * Support multiple selected options.\n       */\n      multiSelect: true;\n      /**\n       * Function to execute on selection change\n       */\n      onValueChange: (value: T[]) => void;\n    }\n  | {\n      /**\n       * Value of the currently selected segmented button.\n       */\n      value: T;\n      /**\n       * Support multiple selected options.\n       */\n      multiSelect?: false;\n      /**\n       * Function to execute on selection change\n       */\n      onValueChange: (value: T) => void;\n    };\n\nexport type Props<T extends string = string> = {\n  /**\n   * Buttons to display as options in toggle button.\n   * Button should contain the following properties:\n   * - `value`: value of button (required)\n   * - `icon`: icon to display for the item\n   * - `disabled`: whether the button is disabled\n   * - `accessibilityLabel`: acccessibility label for the button. This is read by the screen reader when the user taps the button.\n   * - `checkedColor`: custom color for checked Text and Icon\n   * - `uncheckedColor`: custom color for unchecked Text and Icon\n   * - `onPress`: callback that is called when button is pressed\n   * - `label`: label text of the button\n   * - `showSelectedCheck`: show optional check icon to indicate selected state\n   * - `style`: pass additional styles for the button\n   * - `testID`: testID to be used on tests\n   */\n  buttons: {\n    value: T;\n    icon?: IconSource;\n    disabled?: boolean;\n    accessibilityLabel?: string;\n    checkedColor?: string;\n    uncheckedColor?: string;\n    onPress?: (event: GestureResponderEvent) => void;\n    label?: string;\n    showSelectedCheck?: boolean;\n    style?: StyleProp<ViewStyle>;\n    labelStyle?: StyleProp<TextStyle>;\n    testID?: string;\n  }[];\n  /**\n   * Density is applied to the height, to allow usage in denser UIs\n   */\n  density?: 'regular' | 'small' | 'medium' | 'high';\n  style?: StyleProp<ViewStyle>;\n  theme?: ThemeProp;\n} & ConditionalValue<T>;\n\n/**\n * Segmented buttons can be used to select options, switch views or sort elements.</br>\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { SafeAreaView, StyleSheet } from 'react-native';\n * import { SegmentedButtons } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [value, setValue] = React.useState('');\n *\n *   return (\n *     <SafeAreaView style={styles.container}>\n *       <SegmentedButtons\n *         value={value}\n *         onValueChange={setValue}\n *         buttons={[\n *           {\n *             value: 'walk',\n *             label: 'Walking',\n *           },\n *           {\n *             value: 'train',\n *             label: 'Transit',\n *           },\n *           { value: 'drive', label: 'Driving' },\n *         ]}\n *       />\n *     </SafeAreaView>\n *   );\n * };\n *\n * const styles = StyleSheet.create({\n *   container: {\n *     flex: 1,\n *     alignItems: 'center',\n *   },\n * });\n *\n * export default MyComponent;\n *```\n */\nconst SegmentedButtons = <T extends string = string>({\n  value,\n  onValueChange,\n  buttons,\n  multiSelect,\n  density,\n  style,\n  theme: themeOverrides,\n}: Props<T>) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  return (\n    <View style={[styles.row, style]}>\n      {buttons.map((item, i) => {\n        const disabledChildStyle = getDisabledSegmentedButtonStyle({\n          theme,\n          buttons,\n          index: i,\n        });\n        const segment =\n          i === 0 ? 'first' : i === buttons.length - 1 ? 'last' : undefined;\n\n        const checked =\n          multiSelect && Array.isArray(value)\n            ? value.includes(item.value)\n            : value === item.value;\n\n        const onPress = (e: GestureResponderEvent) => {\n          item.onPress?.(e);\n\n          const nextValue =\n            multiSelect && Array.isArray(value)\n              ? checked\n                ? value.filter((val) => item.value !== val)\n                : [...value, item.value]\n              : item.value;\n\n          // @ts-expect-error: TS doesn't preserve types after destructuring, so the type isn't inferred correctly\n          onValueChange(nextValue);\n        };\n\n        return (\n          <SegmentedButtonItem\n            {...item}\n            key={i}\n            checked={checked}\n            segment={segment}\n            density={density}\n            onPress={onPress}\n            style={[item.style, disabledChildStyle]}\n            labelStyle={item.labelStyle}\n            theme={theme}\n          />\n        );\n      })}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row',\n  },\n});\n\nexport default SegmentedButtons;\n\n// @component-docs ignore-next-line\nexport { SegmentedButtons as SegmentedButtons };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAY9B,OAAOC,mBAAmB;AAC1B,SAASC,+BAA+B;AACxC,SAASC,gBAAgB;AAkHzB,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAGC,IAAA,EAQT;EAAA,IAPdC,KAAK,GAOID,IAAA,CAPTC,KAAK;IACLC,aAAa,GAMJF,IAAA,CANTE,aAAa;IACbC,OAAO,GAKEH,IAAA,CALTG,OAAO;IACPC,WAAW,GAIFJ,IAAA,CAJTI,WAAW;IACXC,OAAO,GAGEL,IAAA,CAHTK,OAAO;IACPC,KAAK,GAEIN,IAAA,CAFTM,KAAK;IACEC,cAAA,GACEP,IAAA,CADTQ,KAAK;EAEL,IAAMA,KAAK,GAAGV,gBAAgB,CAACS,cAAc,CAAC;EAE9C,OACEd,KAAA,CAAAgB,aAAA,CAACd,IAAI;IAACW,KAAK,EAAE,CAACI,MAAM,CAACC,GAAG,EAAEL,KAAK;EAAE,GAC9BH,OAAO,CAACS,GAAG,CAAC,UAACC,IAAI,EAAEC,CAAC,EAAK;IACxB,IAAMC,kBAAkB,GAAGlB,+BAA+B,CAAC;MACzDW,KAAK,EAALA,KAAK;MACLL,OAAO,EAAPA,OAAO;MACPa,KAAK,EAAEF;IACT,CAAC,CAAC;IACF,IAAMG,OAAO,GACXH,CAAC,KAAK,CAAC,GAAG,OAAO,GAAGA,CAAC,KAAKX,OAAO,CAACe,MAAM,GAAG,CAAC,GAAG,MAAM,GAAGC,SAAS;IAEnE,IAAMC,OAAO,GACXhB,WAAW,IAAIiB,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,GAC/BA,KAAK,CAACsB,QAAQ,CAACV,IAAI,CAACZ,KAAK,CAAC,GAC1BA,KAAK,KAAKY,IAAI,CAACZ,KAAK;IAE1B,IAAMuB,OAAO,GAAI,SAAXA,OAAOA,CAAIC,CAAwB,EAAK;MAAA,IAAAC,aAAA;MAC5C,CAAAA,aAAA,GAAAb,IAAI,CAACW,OAAO,cAAAE,aAAA,uBAAZA,aAAA,CAAAC,IAAA,CAAAd,IAAI,EAAWY,CAAC,CAAC;MAEjB,IAAMG,SAAS,GACbxB,WAAW,IAAIiB,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,GAC/BmB,OAAO,GACLnB,KAAK,CAAC4B,MAAM,CAAE,UAAAC,GAAG;QAAA,OAAKjB,IAAI,CAACZ,KAAK,KAAK6B,GAAG;MAAA,EAAC,MAAAC,MAAA,CAAAC,kBAAA,CACrC/B,KAAK,IAAEY,IAAI,CAACZ,KAAK,EAAC,GACxBY,IAAI,CAACZ,KAAK;MAGhBC,aAAa,CAAC0B,SAAS,CAAC;IAC1B,CAAC;IAED,OACEnC,KAAA,CAAAgB,aAAA,CAACb,mBAAmB,EAAAqC,QAAA,KACdpB,IAAI;MACRqB,GAAG,EAAEpB,CAAE;MACPM,OAAO,EAAEA,OAAQ;MACjBH,OAAO,EAAEA,OAAQ;MACjBZ,OAAO,EAAEA,OAAQ;MACjBmB,OAAO,EAAEA,OAAQ;MACjBlB,KAAK,EAAE,CAACO,IAAI,CAACP,KAAK,EAAES,kBAAkB,CAAE;MACxCoB,UAAU,EAAEtB,IAAI,CAACsB,UAAW;MAC5B3B,KAAK,EAAEA;IAAM,EACd,CAAC;EAEN,CAAC,CACG,CAAC;AAEX,CAAC;AAED,IAAME,MAAM,GAAGhB,UAAU,CAAC0C,MAAM,CAAC;EAC/BzB,GAAG,EAAE;IACH0B,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAEF,eAAetC,gBAAgB;AAG/B,SAASA,gBAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}