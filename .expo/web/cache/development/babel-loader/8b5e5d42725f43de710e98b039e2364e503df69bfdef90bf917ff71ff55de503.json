{"ast": null, "code": "function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport StyledText from \"./StyledText\";\nvar Paragraph = function Paragraph(props) {\n  return React.createElement(StyledText, _extends({}, props, {\n    alpha: 0.87,\n    family: \"regular\",\n    style: [styles.text, props.style]\n  }));\n};\nexport default Paragraph;\nvar styles = StyleSheet.create({\n  text: {\n    fontSize: 14,\n    lineHeight: 20,\n    marginVertical: 2,\n    letterSpacing: 0.25\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "StyledText", "Paragraph", "props", "createElement", "_extends", "alpha", "family", "style", "styles", "text", "create", "fontSize", "lineHeight", "marginVertical", "letterSpacing"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/Typography/v2/Paragraph.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { TextProps, StyleSheet } from 'react-native';\n\nimport StyledText from './StyledText';\n\nexport type Props = TextProps & {\n  children: React.ReactNode;\n};\n\n// @component-group Typography\n\n/**\n * @deprecated Deprecated in v5.x - use `<Text variant=\"bodyMedium\" />` instead.\n * Typography component for showing a paragraph.\n *\n * <div class=\"screenshots\">\n *   <img src=\"screenshots/paragraph.png\" />\n * </div>\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Paragraph } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Paragraph>Paragraph</Paragraph>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Paragraph = (props: Props) => (\n  <StyledText\n    {...props}\n    alpha={0.87}\n    family=\"regular\"\n    style={[styles.text, props.style]}\n  />\n);\n\nexport default Paragraph;\n\nconst styles = StyleSheet.create({\n  text: {\n    fontSize: 14,\n    lineHeight: 20,\n    marginVertical: 2,\n    letterSpacing: 0.25,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAG9B,OAAOC,UAAU;AA4BjB,IAAMC,SAAS,GAAI,SAAbA,SAASA,CAAIC,KAAY;EAAA,OAC7BJ,KAAA,CAAAK,aAAA,CAACH,UAAU,EAAAI,QAAA,KACLF,KAAK;IACTG,KAAK,EAAE,IAAK;IACZC,MAAM,EAAC,SAAS;IAChBC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAEP,KAAK,CAACK,KAAK;EAAE,EACnC,CACF;AAAA;AAED,eAAeN,SAAS;AAExB,IAAMO,MAAM,GAAGT,UAAU,CAACW,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}