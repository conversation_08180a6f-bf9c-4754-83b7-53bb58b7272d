{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport RadioButton from \"./RadioButton\";\nimport RadioButtonAndroid from \"./RadioButtonAndroid\";\nimport { RadioButtonContext } from \"./RadioButtonGroup\";\nimport RadioButtonIOS from \"./RadioButtonIOS\";\nimport { handlePress, isChecked } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar RadioButtonItem = function RadioButtonItem(_ref) {\n  var value = _ref.value,\n    label = _ref.label,\n    style = _ref.style,\n    labelStyle = _ref.labelStyle,\n    _onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    disabled = _ref.disabled,\n    color = _ref.color,\n    uncheckedColor = _ref.uncheckedColor,\n    rippleColor = _ref.rippleColor,\n    status = _ref.status,\n    themeOverrides = _ref.theme,\n    background = _ref.background,\n    _ref$accessibilityLab = _ref.accessibilityLabel,\n    accessibilityLabel = _ref$accessibilityLab === void 0 ? label : _ref$accessibilityLab,\n    testID = _ref.testID,\n    mode = _ref.mode,\n    _ref$position = _ref.position,\n    position = _ref$position === void 0 ? 'trailing' : _ref$position,\n    _ref$labelVariant = _ref.labelVariant,\n    labelVariant = _ref$labelVariant === void 0 ? 'bodyLarge' : _ref$labelVariant,\n    labelMaxFontSizeMultiplier = _ref.labelMaxFontSizeMultiplier;\n  var theme = useInternalTheme(themeOverrides);\n  var radioButtonProps = {\n    value: value,\n    disabled: disabled,\n    status: status,\n    color: color,\n    theme: theme,\n    uncheckedColor: uncheckedColor\n  };\n  var isLeading = position === 'leading';\n  var radioButton;\n  if (mode === 'android') {\n    radioButton = React.createElement(RadioButtonAndroid, radioButtonProps);\n  } else if (mode === 'ios') {\n    radioButton = React.createElement(RadioButtonIOS, radioButtonProps);\n  } else {\n    radioButton = React.createElement(RadioButton, radioButtonProps);\n  }\n  var textColor = theme.isV3 ? theme.colors.onSurface : theme.colors.text;\n  var disabledTextColor = theme.isV3 ? theme.colors.onSurfaceDisabled : theme.colors.disabled;\n  var textAlign = isLeading ? 'right' : 'left';\n  var computedStyle = {\n    color: disabled ? disabledTextColor : textColor,\n    textAlign: textAlign\n  };\n  return React.createElement(RadioButtonContext.Consumer, null, function (context) {\n    var checked = isChecked({\n      contextValue: context === null || context === void 0 ? void 0 : context.value,\n      status: status,\n      value: value\n    }) === 'checked';\n    return React.createElement(TouchableRipple, {\n      onPress: function onPress(event) {\n        return handlePress({\n          onPress: _onPress,\n          onValueChange: context === null || context === void 0 ? void 0 : context.onValueChange,\n          value: value,\n          event: event\n        });\n      },\n      onLongPress: onLongPress,\n      accessibilityLabel: accessibilityLabel,\n      accessibilityRole: \"radio\",\n      accessibilityState: {\n        checked: checked,\n        disabled: disabled\n      },\n      testID: testID,\n      disabled: disabled,\n      background: background,\n      theme: theme,\n      rippleColor: rippleColor\n    }, React.createElement(View, {\n      style: [styles.container, style],\n      pointerEvents: \"none\"\n    }, isLeading && radioButton, React.createElement(Text, {\n      variant: labelVariant,\n      style: [styles.label, !theme.isV3 && styles.font, computedStyle, labelStyle],\n      maxFontSizeMultiplier: labelMaxFontSizeMultiplier\n    }, label), !isLeading && radioButton));\n  });\n};\nRadioButtonItem.displayName = 'RadioButton.Item';\nexport default RadioButtonItem;\nexport { RadioButtonItem };\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingVertical: 8,\n    paddingHorizontal: 16\n  },\n  label: {\n    flexShrink: 1,\n    flexGrow: 1\n  },\n  font: {\n    fontSize: 16\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "RadioButton", "RadioButtonAndroid", "RadioButtonContext", "RadioButtonIOS", "handlePress", "isChecked", "useInternalTheme", "TouchableRipple", "Text", "RadioButtonItem", "_ref", "value", "label", "style", "labelStyle", "onPress", "onLongPress", "disabled", "color", "uncheckedColor", "rippleColor", "status", "themeOverrides", "theme", "background", "_ref$accessibilityLab", "accessibilityLabel", "testID", "mode", "_ref$position", "position", "_ref$labelVariant", "labelVariant", "labelMaxFontSizeMultiplier", "radioButtonProps", "isLeading", "radioButton", "createElement", "textColor", "isV3", "colors", "onSurface", "text", "disabledTextColor", "onSurfaceDisabled", "textAlign", "computedStyle", "Consumer", "context", "checked", "contextValue", "event", "onValueChange", "accessibilityRole", "accessibilityState", "styles", "container", "pointerEvents", "variant", "font", "maxFontSizeMultiplier", "displayName", "create", "flexDirection", "alignItems", "justifyContent", "paddingVertical", "paddingHorizontal", "flexShrink", "flexGrow", "fontSize"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/RadioButton/RadioButtonItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  ColorValue,\n  GestureResponderEvent,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport RadioButton from './RadioButton';\nimport RadioButtonAndroid from './RadioButtonAndroid';\nimport { RadioButtonContext, RadioButtonContextType } from './RadioButtonGroup';\nimport RadioButtonIOS from './RadioButtonIOS';\nimport { handlePress, isChecked } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp, MD3TypescaleKey } from '../../types';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Value of the radio button.\n   */\n  value: string;\n  /**\n   * Label to be displayed on the item.\n   */\n  label: string;\n  /**\n   * Whether radio is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Accessibility label for the touchable. This is read by the screen reader when the user taps the touchable.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Custom color for unchecked radio.\n   */\n  uncheckedColor?: string;\n  /**\n   * Custom color for radio.\n   */\n  color?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Status of radio button.\n   */\n  status?: 'checked' | 'unchecked';\n  /**\n   * Additional styles for container View.\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to Label element.\n   */\n  labelStyle?: StyleProp<TextStyle>;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Label text variant defines appropriate text styles for type role and its size.\n   * Available variants:\n   *\n   *  Display: `displayLarge`, `displayMedium`, `displaySmall`\n   *\n   *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`\n   *\n   *  Title: `titleLarge`, `titleMedium`, `titleSmall`\n   *\n   *  Label:  `labelLarge`, `labelMedium`, `labelSmall`\n   *\n   *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`\n   */\n  labelVariant?: keyof typeof MD3TypescaleKey;\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n  /**\n   * Whether `<RadioButton.Android />` or `<RadioButton.IOS />` should be used.\n   * Left undefined `<RadioButton />` will be used.\n   */\n  mode?: 'android' | 'ios';\n  /**\n   * Radio button control position.\n   */\n  position?: 'leading' | 'trailing';\n};\n\n/**\n * RadioButton.Item allows you to press the whole row (item) instead of only the RadioButton.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { RadioButton } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [value, setValue] = React.useState('first');\n *\n *   return (\n *     <RadioButton.Group onValueChange={value => setValue(value)} value={value}>\n *       <RadioButton.Item label=\"First item\" value=\"first\" />\n *       <RadioButton.Item label=\"Second item\" value=\"second\" />\n *     </RadioButton.Group>\n *   );\n * };\n *\n * export default MyComponent;\n *```\n */\nconst RadioButtonItem = ({\n  value,\n  label,\n  style,\n  labelStyle,\n  onPress,\n  onLongPress,\n  disabled,\n  color,\n  uncheckedColor,\n  rippleColor,\n  status,\n  theme: themeOverrides,\n  background,\n  accessibilityLabel = label,\n  testID,\n  mode,\n  position = 'trailing',\n  labelVariant = 'bodyLarge',\n  labelMaxFontSizeMultiplier,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const radioButtonProps = {\n    value,\n    disabled,\n    status,\n    color,\n    theme,\n    uncheckedColor,\n  };\n  const isLeading = position === 'leading';\n  let radioButton: any;\n\n  if (mode === 'android') {\n    radioButton = <RadioButtonAndroid {...radioButtonProps} />;\n  } else if (mode === 'ios') {\n    radioButton = <RadioButtonIOS {...radioButtonProps} />;\n  } else {\n    radioButton = <RadioButton {...radioButtonProps} />;\n  }\n\n  const textColor = theme.isV3 ? theme.colors.onSurface : theme.colors.text;\n  const disabledTextColor = theme.isV3\n    ? theme.colors.onSurfaceDisabled\n    : theme.colors.disabled;\n  const textAlign = isLeading ? 'right' : 'left';\n\n  const computedStyle = {\n    color: disabled ? disabledTextColor : textColor,\n    textAlign,\n  } as TextStyle;\n\n  return (\n    <RadioButtonContext.Consumer>\n      {(context?: RadioButtonContextType) => {\n        const checked =\n          isChecked({\n            contextValue: context?.value,\n            status,\n            value,\n          }) === 'checked';\n        return (\n          <TouchableRipple\n            onPress={(event) =>\n              handlePress({\n                onPress: onPress,\n                onValueChange: context?.onValueChange,\n                value,\n                event,\n              })\n            }\n            onLongPress={onLongPress}\n            accessibilityLabel={accessibilityLabel}\n            accessibilityRole=\"radio\"\n            accessibilityState={{\n              checked,\n              disabled,\n            }}\n            testID={testID}\n            disabled={disabled}\n            background={background}\n            theme={theme}\n            rippleColor={rippleColor}\n          >\n            <View style={[styles.container, style]} pointerEvents=\"none\">\n              {isLeading && radioButton}\n              <Text\n                variant={labelVariant}\n                style={[\n                  styles.label,\n                  !theme.isV3 && styles.font,\n                  computedStyle,\n                  labelStyle,\n                ]}\n                maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n              >\n                {label}\n              </Text>\n              {!isLeading && radioButton}\n            </View>\n          </TouchableRipple>\n        );\n      }}\n    </RadioButtonContext.Consumer>\n  );\n};\n\nRadioButtonItem.displayName = 'RadioButton.Item';\n\nexport default RadioButtonItem;\n\n// @component-docs ignore-next-line\nexport { RadioButtonItem };\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingVertical: 8,\n    paddingHorizontal: 16,\n  },\n  label: {\n    flexShrink: 1,\n    flexGrow: 1,\n  },\n  font: {\n    fontSize: 16,\n  },\n});\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAY9B,OAAOC,WAAW;AAClB,OAAOC,kBAAkB;AACzB,SAASC,kBAAkB;AAC3B,OAAOC,cAAc;AACrB,SAASC,WAAW,EAAEC,SAAS;AAC/B,SAASC,gBAAgB;AAEzB,OAAOC,eAAe;AACtB,OAAOC,IAAI;AAsHX,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,IAAA,EAoBX;EAAA,IAnBXC,KAAK,GAmBCD,IAAA,CAnBNC,KAAK;IACLC,KAAK,GAkBCF,IAAA,CAlBNE,KAAK;IACLC,KAAK,GAiBCH,IAAA,CAjBNG,KAAK;IACLC,UAAU,GAgBJJ,IAAA,CAhBNI,UAAU;IACVC,QAAO,GAeDL,IAAA,CAfNK,OAAO;IACPC,WAAW,GAcLN,IAAA,CAdNM,WAAW;IACXC,QAAQ,GAaFP,IAAA,CAbNO,QAAQ;IACRC,KAAK,GAYCR,IAAA,CAZNQ,KAAK;IACLC,cAAc,GAWRT,IAAA,CAXNS,cAAc;IACdC,WAAW,GAULV,IAAA,CAVNU,WAAW;IACXC,MAAM,GASAX,IAAA,CATNW,MAAM;IACCC,cAAc,GAQfZ,IAAA,CARNa,KAAK;IACLC,UAAU,GAOJd,IAAA,CAPNc,UAAU;IAAAC,qBAAA,GAOJf,IAAA,CANNgB,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGb,KAAK,GAAAa,qBAAA;IAC1BE,MAAM,GAKAjB,IAAA,CALNiB,MAAM;IACNC,IAAI,GAIElB,IAAA,CAJNkB,IAAI;IAAAC,aAAA,GAIEnB,IAAA,CAHNoB,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,UAAU,GAAAA,aAAA;IAAAE,iBAAA,GAGfrB,IAAA,CAFNsB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,WAAW,GAAAA,iBAAA;IAC1BE,0BAAA,GACMvB,IAAA,CADNuB,0BAAA;EAEA,IAAMV,KAAK,GAAGjB,gBAAgB,CAACgB,cAAc,CAAC;EAC9C,IAAMY,gBAAgB,GAAG;IACvBvB,KAAK,EAALA,KAAK;IACLM,QAAQ,EAARA,QAAQ;IACRI,MAAM,EAANA,MAAM;IACNH,KAAK,EAALA,KAAK;IACLK,KAAK,EAALA,KAAK;IACLJ,cAAA,EAAAA;EACF,CAAC;EACD,IAAMgB,SAAS,GAAGL,QAAQ,KAAK,SAAS;EACxC,IAAIM,WAAgB;EAEpB,IAAIR,IAAI,KAAK,SAAS,EAAE;IACtBQ,WAAW,GAAGvC,KAAA,CAAAwC,aAAA,CAACpC,kBAAkB,EAAKiC,gBAAmB,CAAC;EAC5D,CAAC,MAAM,IAAIN,IAAI,KAAK,KAAK,EAAE;IACzBQ,WAAW,GAAGvC,KAAA,CAAAwC,aAAA,CAAClC,cAAc,EAAK+B,gBAAmB,CAAC;EACxD,CAAC,MAAM;IACLE,WAAW,GAAGvC,KAAA,CAAAwC,aAAA,CAACrC,WAAW,EAAKkC,gBAAmB,CAAC;EACrD;EAEA,IAAMI,SAAS,GAAGf,KAAK,CAACgB,IAAI,GAAGhB,KAAK,CAACiB,MAAM,CAACC,SAAS,GAAGlB,KAAK,CAACiB,MAAM,CAACE,IAAI;EACzE,IAAMC,iBAAiB,GAAGpB,KAAK,CAACgB,IAAI,GAChChB,KAAK,CAACiB,MAAM,CAACI,iBAAiB,GAC9BrB,KAAK,CAACiB,MAAM,CAACvB,QAAQ;EACzB,IAAM4B,SAAS,GAAGV,SAAS,GAAG,OAAO,GAAG,MAAM;EAE9C,IAAMW,aAAa,GAAG;IACpB5B,KAAK,EAAED,QAAQ,GAAG0B,iBAAiB,GAAGL,SAAS;IAC/CO,SAAA,EAAAA;EACF,CAAc;EAEd,OACEhD,KAAA,CAAAwC,aAAA,CAACnC,kBAAkB,CAAC6C,QAAQ,QACxB,UAAAC,OAAgC,EAAK;IACrC,IAAMC,OAAO,GACX5C,SAAS,CAAC;MACR6C,YAAY,EAAEF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAErC,KAAK;MAC5BU,MAAM,EAANA,MAAM;MACNV,KAAA,EAAAA;IACF,CAAC,CAAC,KAAK,SAAS;IAClB,OACEd,KAAA,CAAAwC,aAAA,CAAC9B,eAAe;MACdQ,OAAO,EAAG,SAAVA,OAAOA,CAAGoC,KAAK;QAAA,OACb/C,WAAW,CAAC;UACVW,OAAO,EAAEA,QAAO;UAChBqC,aAAa,EAAEJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,aAAa;UACrCzC,KAAK,EAALA,KAAK;UACLwC,KAAA,EAAAA;QACF,CAAC,CACF;MAAA;MACDnC,WAAW,EAAEA,WAAY;MACzBU,kBAAkB,EAAEA,kBAAmB;MACvC2B,iBAAiB,EAAC,OAAO;MACzBC,kBAAkB,EAAE;QAClBL,OAAO,EAAPA,OAAO;QACPhC,QAAA,EAAAA;MACF,CAAE;MACFU,MAAM,EAAEA,MAAO;MACfV,QAAQ,EAAEA,QAAS;MACnBO,UAAU,EAAEA,UAAW;MACvBD,KAAK,EAAEA,KAAM;MACbH,WAAW,EAAEA;IAAY,GAEzBvB,KAAA,CAAAwC,aAAA,CAACtC,IAAI;MAACc,KAAK,EAAE,CAAC0C,MAAM,CAACC,SAAS,EAAE3C,KAAK,CAAE;MAAC4C,aAAa,EAAC;IAAM,GACzDtB,SAAS,IAAIC,WAAW,EACzBvC,KAAA,CAAAwC,aAAA,CAAC7B,IAAI;MACHkD,OAAO,EAAE1B,YAAa;MACtBnB,KAAK,EAAE,CACL0C,MAAM,CAAC3C,KAAK,EACZ,CAACW,KAAK,CAACgB,IAAI,IAAIgB,MAAM,CAACI,IAAI,EAC1Bb,aAAa,EACbhC,UAAU,CACV;MACF8C,qBAAqB,EAAE3B;IAA2B,GAEjDrB,KACG,CAAC,EACN,CAACuB,SAAS,IAAIC,WACX,CACS,CAAC;EAEtB,CAC2B,CAAC;AAElC,CAAC;AAED3B,eAAe,CAACoD,WAAW,GAAG,kBAAkB;AAEhD,eAAepD,eAAe;AAG9B,SAASA,eAAe;AAExB,IAAM8C,MAAM,GAAGzD,UAAU,CAACgE,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE;EACrB,CAAC;EACDvD,KAAK,EAAE;IACLwD,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDV,IAAI,EAAE;IACJW,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}