{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport escape from 'escape-string-regexp';\nimport * as queryString from 'query-string';\nimport findFocusedRoute from \"./findFocusedRoute\";\nimport validatePathConfig from \"./validatePathConfig\";\nexport default function getStateFromPath(path, options) {\n  var _ref;\n  if (options) {\n    validatePathConfig(options);\n  }\n  var initialRoutes = [];\n  if (options !== null && options !== void 0 && options.initialRouteName) {\n    initialRoutes.push({\n      initialRouteName: options.initialRouteName,\n      parentScreens: []\n    });\n  }\n  var screens = options === null || options === void 0 ? void 0 : options.screens;\n  var remaining = path.replace(/\\/+/g, '/').replace(/^\\//, '').replace(/\\?.*$/, '');\n  remaining = remaining.endsWith('/') ? remaining : `${remaining}/`;\n  if (screens === undefined) {\n    var _routes = remaining.split('/').filter(Boolean).map(function (segment) {\n      var name = decodeURIComponent(segment);\n      return {\n        name: name\n      };\n    });\n    if (_routes.length) {\n      return createNestedStateObject(path, _routes, initialRoutes);\n    }\n    return undefined;\n  }\n  var configs = (_ref = []).concat.apply(_ref, _toConsumableArray(Object.keys(screens).map(function (key) {\n    return _createNormalizedConfigs(key, screens, [], initialRoutes, []);\n  }))).sort(function (a, b) {\n    if (a.pattern === b.pattern) {\n      return b.routeNames.join('>').localeCompare(a.routeNames.join('>'));\n    }\n    if (a.pattern.startsWith(b.pattern)) {\n      return -1;\n    }\n    if (b.pattern.startsWith(a.pattern)) {\n      return 1;\n    }\n    var aParts = a.pattern.split('/');\n    var bParts = b.pattern.split('/');\n    for (var i = 0; i < Math.max(aParts.length, bParts.length); i++) {\n      if (aParts[i] == null) {\n        return 1;\n      }\n      if (bParts[i] == null) {\n        return -1;\n      }\n      var aWildCard = aParts[i] === '*' || aParts[i].startsWith(':');\n      var bWildCard = bParts[i] === '*' || bParts[i].startsWith(':');\n      if (aWildCard && bWildCard) {\n        continue;\n      }\n      if (aWildCard) {\n        return 1;\n      }\n      if (bWildCard) {\n        return -1;\n      }\n    }\n    return bParts.length - aParts.length;\n  });\n  configs.reduce(function (acc, config) {\n    if (acc[config.pattern]) {\n      var a = acc[config.pattern].routeNames;\n      var b = config.routeNames;\n      var intersects = a.length > b.length ? b.every(function (it, i) {\n        return a[i] === it;\n      }) : a.every(function (it, i) {\n        return b[i] === it;\n      });\n      if (!intersects) {\n        throw new Error(`Found conflicting screens with the same pattern. The pattern '${config.pattern}' resolves to both '${a.join(' > ')}' and '${b.join(' > ')}'. Patterns must be unique and cannot resolve to more than one screen.`);\n      }\n    }\n    return Object.assign(acc, _defineProperty({}, config.pattern, config));\n  }, {});\n  if (remaining === '/') {\n    var match = configs.find(function (config) {\n      return config.path === '' && config.routeNames.every(function (name) {\n        var _configs$find;\n        return !((_configs$find = configs.find(function (c) {\n          return c.screen === name;\n        })) !== null && _configs$find !== void 0 && _configs$find.path);\n      });\n    });\n    if (match) {\n      return createNestedStateObject(path, match.routeNames.map(function (name) {\n        return {\n          name: name\n        };\n      }), initialRoutes, configs);\n    }\n    return undefined;\n  }\n  var result;\n  var current;\n  var _matchAgainstConfigs = matchAgainstConfigs(remaining, configs.map(function (c) {\n      return _objectSpread(_objectSpread({}, c), {}, {\n        regex: c.regex ? new RegExp(c.regex.source + '$') : undefined\n      });\n    })),\n    routes = _matchAgainstConfigs.routes,\n    remainingPath = _matchAgainstConfigs.remainingPath;\n  if (routes !== undefined) {\n    current = createNestedStateObject(path, routes, initialRoutes, configs);\n    remaining = remainingPath;\n    result = current;\n  }\n  if (current == null || result == null) {\n    return undefined;\n  }\n  return result;\n}\nvar joinPaths = function joinPaths() {\n  var _ref2;\n  for (var _len = arguments.length, paths = new Array(_len), _key = 0; _key < _len; _key++) {\n    paths[_key] = arguments[_key];\n  }\n  return (_ref2 = []).concat.apply(_ref2, _toConsumableArray(paths.map(function (p) {\n    return p.split('/');\n  }))).filter(Boolean).join('/');\n};\nvar matchAgainstConfigs = function matchAgainstConfigs(remaining, configs) {\n  var routes;\n  var remainingPath = remaining;\n  var _loop = function _loop(config) {\n      if (!config.regex) {\n        return 0;\n      }\n      var match = remainingPath.match(config.regex);\n      if (match) {\n        var matchResult = (_config$pattern = config.pattern) === null || _config$pattern === void 0 ? void 0 : _config$pattern.split('/').reduce(function (acc, p, index) {\n          if (!p.startsWith(':')) {\n            return acc;\n          }\n          acc.pos += 1;\n          var decodedParamSegment = decodeURIComponent(match[(acc.pos + 1) * 2].replace(/\\/$/, ''));\n          Object.assign(acc.matchedParams, _defineProperty({}, p, Object.assign(acc.matchedParams[p] || {}, _defineProperty({}, index, decodedParamSegment))));\n          return acc;\n        }, {\n          pos: -1,\n          matchedParams: {}\n        });\n        var matchedParams = matchResult.matchedParams || {};\n        routes = config.routeNames.map(function (name) {\n          var _routeConfig$pattern$;\n          var routeConfig = configs.find(function (c) {\n            return c.screen === name && config.pattern.startsWith(c.pattern);\n          });\n          var normalizedPath = routeConfig === null || routeConfig === void 0 ? void 0 : routeConfig.path.split('/').filter(Boolean).join('/');\n          var numInitialSegments = routeConfig === null || routeConfig === void 0 ? void 0 : (_routeConfig$pattern$ = routeConfig.pattern.replace(new RegExp(`${escape(normalizedPath)}$`), '')) === null || _routeConfig$pattern$ === void 0 ? void 0 : _routeConfig$pattern$.split('/').length;\n          var params = normalizedPath === null || normalizedPath === void 0 ? void 0 : normalizedPath.split('/').reduce(function (acc, p, index) {\n            var _matchedParams$p;\n            if (!p.startsWith(':')) {\n              return acc;\n            }\n            var offset = numInitialSegments ? numInitialSegments - 1 : 0;\n            var value = (_matchedParams$p = matchedParams[p]) === null || _matchedParams$p === void 0 ? void 0 : _matchedParams$p[index + offset];\n            if (value) {\n              var _routeConfig$parse;\n              var key = p.replace(/^:/, '').replace(/\\?$/, '');\n              acc[key] = routeConfig !== null && routeConfig !== void 0 && (_routeConfig$parse = routeConfig.parse) !== null && _routeConfig$parse !== void 0 && _routeConfig$parse[key] ? routeConfig.parse[key](value) : value;\n            }\n            return acc;\n          }, {});\n          if (params && Object.keys(params).length) {\n            return {\n              name: name,\n              params: params\n            };\n          }\n          return {\n            name: name\n          };\n        });\n        remainingPath = remainingPath.replace(match[1], '');\n        return 1;\n      }\n    },\n    _config$pattern,\n    _ret;\n  for (var config of configs) {\n    _ret = _loop(config);\n    if (_ret === 0) continue;\n    if (_ret === 1) break;\n  }\n  return {\n    routes: routes,\n    remainingPath: remainingPath\n  };\n};\nvar _createNormalizedConfigs = function createNormalizedConfigs(screen, routeConfig) {\n  var routeNames = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  var initials = arguments.length > 3 ? arguments[3] : undefined;\n  var parentScreens = arguments.length > 4 ? arguments[4] : undefined;\n  var parentPattern = arguments.length > 5 ? arguments[5] : undefined;\n  var configs = [];\n  routeNames.push(screen);\n  parentScreens.push(screen);\n  var config = routeConfig[screen];\n  if (typeof config === 'string') {\n    var pattern = parentPattern ? joinPaths(parentPattern, config) : config;\n    configs.push(createConfigItem(screen, routeNames, pattern, config));\n  } else if (typeof config === 'object') {\n    var _pattern;\n    if (typeof config.path === 'string') {\n      if (config.exact && config.path === undefined) {\n        throw new Error(\"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\");\n      }\n      _pattern = config.exact !== true ? joinPaths(parentPattern || '', config.path || '') : config.path || '';\n      configs.push(createConfigItem(screen, routeNames, _pattern, config.path, config.parse));\n    }\n    if (config.screens) {\n      if (config.initialRouteName) {\n        initials.push({\n          initialRouteName: config.initialRouteName,\n          parentScreens: parentScreens\n        });\n      }\n      Object.keys(config.screens).forEach(function (nestedConfig) {\n        var _pattern2;\n        var result = _createNormalizedConfigs(nestedConfig, config.screens, routeNames, initials, _toConsumableArray(parentScreens), (_pattern2 = _pattern) != null ? _pattern2 : parentPattern);\n        configs.push.apply(configs, _toConsumableArray(result));\n      });\n    }\n  }\n  routeNames.pop();\n  return configs;\n};\nvar createConfigItem = function createConfigItem(screen, routeNames, pattern, path, parse) {\n  pattern = pattern.split('/').filter(Boolean).join('/');\n  var regex = pattern ? new RegExp(`^(${pattern.split('/').map(function (it) {\n    if (it.startsWith(':')) {\n      return `(([^/]+\\\\/)${it.endsWith('?') ? '?' : ''})`;\n    }\n    return `${it === '*' ? '.*' : escape(it)}\\\\/`;\n  }).join('')})`) : undefined;\n  return {\n    screen: screen,\n    regex: regex,\n    pattern: pattern,\n    path: path,\n    routeNames: _toConsumableArray(routeNames),\n    parse: parse\n  };\n};\nvar findParseConfigForRoute = function findParseConfigForRoute(routeName, flatConfig) {\n  for (var config of flatConfig) {\n    if (routeName === config.routeNames[config.routeNames.length - 1]) {\n      return config.parse;\n    }\n  }\n  return undefined;\n};\nvar findInitialRoute = function findInitialRoute(routeName, parentScreens, initialRoutes) {\n  for (var config of initialRoutes) {\n    if (parentScreens.length === config.parentScreens.length) {\n      var sameParents = true;\n      for (var i = 0; i < parentScreens.length; i++) {\n        if (parentScreens[i].localeCompare(config.parentScreens[i]) !== 0) {\n          sameParents = false;\n          break;\n        }\n      }\n      if (sameParents) {\n        return routeName !== config.initialRouteName ? config.initialRouteName : undefined;\n      }\n    }\n  }\n  return undefined;\n};\nvar createStateObject = function createStateObject(initialRoute, route, isEmpty) {\n  if (isEmpty) {\n    if (initialRoute) {\n      return {\n        index: 1,\n        routes: [{\n          name: initialRoute\n        }, route]\n      };\n    } else {\n      return {\n        routes: [route]\n      };\n    }\n  } else {\n    if (initialRoute) {\n      return {\n        index: 1,\n        routes: [{\n          name: initialRoute\n        }, _objectSpread(_objectSpread({}, route), {}, {\n          state: {\n            routes: []\n          }\n        })]\n      };\n    } else {\n      return {\n        routes: [_objectSpread(_objectSpread({}, route), {}, {\n          state: {\n            routes: []\n          }\n        })]\n      };\n    }\n  }\n};\nvar createNestedStateObject = function createNestedStateObject(path, routes, initialRoutes, flatConfig) {\n  var state;\n  var route = routes.shift();\n  var parentScreens = [];\n  var initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n  parentScreens.push(route.name);\n  state = createStateObject(initialRoute, route, routes.length === 0);\n  if (routes.length > 0) {\n    var nestedState = state;\n    while (route = routes.shift()) {\n      initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n      var nestedStateIndex = nestedState.index || nestedState.routes.length - 1;\n      nestedState.routes[nestedStateIndex].state = createStateObject(initialRoute, route, routes.length === 0);\n      if (routes.length > 0) {\n        nestedState = nestedState.routes[nestedStateIndex].state;\n      }\n      parentScreens.push(route.name);\n    }\n  }\n  route = findFocusedRoute(state);\n  route.path = path;\n  var params = parseQueryParams(path, flatConfig ? findParseConfigForRoute(route.name, flatConfig) : undefined);\n  if (params) {\n    route.params = _objectSpread(_objectSpread({}, route.params), params);\n  }\n  return state;\n};\nvar parseQueryParams = function parseQueryParams(path, parseConfig) {\n  var query = path.split('?')[1];\n  var params = queryString.parse(query);\n  if (parseConfig) {\n    Object.keys(params).forEach(function (name) {\n      if (Object.hasOwnProperty.call(parseConfig, name) && typeof params[name] === 'string') {\n        params[name] = parseConfig[name](params[name]);\n      }\n    });\n  }\n  return Object.keys(params).length ? params : undefined;\n};", "map": {"version": 3, "names": ["escape", "queryString", "findFocusedRoute", "validatePathConfig", "getStateFromPath", "path", "options", "_ref", "initialRoutes", "initialRouteName", "push", "parentScreens", "screens", "remaining", "replace", "endsWith", "undefined", "routes", "split", "filter", "Boolean", "map", "segment", "name", "decodeURIComponent", "length", "createNestedStateObject", "configs", "concat", "apply", "_toConsumableArray", "Object", "keys", "key", "createNormalizedConfigs", "sort", "a", "b", "pattern", "routeNames", "join", "localeCompare", "startsWith", "aParts", "b<PERSON><PERSON>s", "i", "Math", "max", "aWildCard", "bWildCard", "reduce", "acc", "config", "intersects", "every", "it", "Error", "assign", "_defineProperty", "match", "find", "_configs$find", "c", "screen", "result", "current", "_matchAgainstConfigs", "matchAgainstConfigs", "_objectSpread", "regex", "RegExp", "source", "remainingPath", "joinPaths", "_ref2", "_len", "arguments", "paths", "Array", "_key", "p", "_loop", "matchResult", "_config$pattern", "index", "pos", "decodedParamSegment", "matchedParams", "_routeConfig$pattern$", "routeConfig", "normalizedPath", "numInitialSegments", "params", "_matchedParams$p", "offset", "value", "_routeConfig$parse", "parse", "_ret", "initials", "parentPattern", "createConfigItem", "exact", "for<PERSON>ach", "nestedConfig", "_pattern2", "pop", "findParseConfigForRoute", "routeName", "flatConfig", "findInitialRoute", "sameParents", "createStateObject", "initialRoute", "route", "isEmpty", "state", "shift", "nestedState", "nestedStateIndex", "parseQueryParams", "parseConfig", "query", "hasOwnProperty", "call"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/@react-navigation/core/src/getStateFromPath.tsx"], "sourcesContent": ["import type {\n  InitialState,\n  NavigationState,\n  PartialState,\n} from '@react-navigation/routers';\nimport escape from 'escape-string-regexp';\nimport * as queryString from 'query-string';\n\nimport findFocusedRoute from './findFocusedRoute';\nimport type { PathConfigMap } from './types';\nimport validatePathConfig from './validatePathConfig';\n\ntype Options<ParamList extends {}> = {\n  initialRouteName?: string;\n  screens: PathConfigMap<ParamList>;\n};\n\ntype ParseConfig = Record<string, (value: string) => any>;\n\ntype RouteConfig = {\n  screen: string;\n  regex?: RegExp;\n  path: string;\n  pattern: string;\n  routeNames: string[];\n  parse?: ParseConfig;\n};\n\ntype InitialRouteConfig = {\n  initialRouteName: string;\n  parentScreens: string[];\n};\n\ntype ResultState = PartialState<NavigationState> & {\n  state?: ResultState;\n};\n\ntype ParsedRoute = {\n  name: string;\n  path?: string;\n  params?: Record<string, any> | undefined;\n};\n\n/**\n * Utility to parse a path string to initial state object accepted by the container.\n * This is useful for deep linking when we need to handle the incoming URL.\n *\n * @example\n * ```js\n * getStateFromPath(\n *   '/chat/jane/42',\n *   {\n *     screens: {\n *       Chat: {\n *         path: 'chat/:author/:id',\n *         parse: { id: Number }\n *       }\n *     }\n *   }\n * )\n * ```\n * @param path Path string to parse and convert, e.g. /foo/bar?count=42.\n * @param options Extra options to fine-tune how to parse the path.\n */\nexport default function getStateFromPath<ParamList extends {}>(\n  path: string,\n  options?: Options<ParamList>\n): ResultState | undefined {\n  if (options) {\n    validatePathConfig(options);\n  }\n\n  let initialRoutes: InitialRouteConfig[] = [];\n\n  if (options?.initialRouteName) {\n    initialRoutes.push({\n      initialRouteName: options.initialRouteName,\n      parentScreens: [],\n    });\n  }\n\n  const screens = options?.screens;\n\n  let remaining = path\n    .replace(/\\/+/g, '/') // Replace multiple slash (//) with single ones\n    .replace(/^\\//, '') // Remove extra leading slash\n    .replace(/\\?.*$/, ''); // Remove query params which we will handle later\n\n  // Make sure there is a trailing slash\n  remaining = remaining.endsWith('/') ? remaining : `${remaining}/`;\n\n  if (screens === undefined) {\n    // When no config is specified, use the path segments as route names\n    const routes = remaining\n      .split('/')\n      .filter(Boolean)\n      .map((segment) => {\n        const name = decodeURIComponent(segment);\n        return { name };\n      });\n\n    if (routes.length) {\n      return createNestedStateObject(path, routes, initialRoutes);\n    }\n\n    return undefined;\n  }\n\n  // Create a normalized configs array which will be easier to use\n  const configs = ([] as RouteConfig[])\n    .concat(\n      ...Object.keys(screens).map((key) =>\n        createNormalizedConfigs(\n          key,\n          screens as PathConfigMap<object>,\n          [],\n          initialRoutes,\n          []\n        )\n      )\n    )\n    .sort((a, b) => {\n      // Sort config so that:\n      // - the most exhaustive ones are always at the beginning\n      // - patterns with wildcard are always at the end\n\n      // If 2 patterns are same, move the one with less route names up\n      // This is an error state, so it's only useful for consistent error messages\n      if (a.pattern === b.pattern) {\n        return b.routeNames.join('>').localeCompare(a.routeNames.join('>'));\n      }\n\n      // If one of the patterns starts with the other, it's more exhaustive\n      // So move it up\n      if (a.pattern.startsWith(b.pattern)) {\n        return -1;\n      }\n\n      if (b.pattern.startsWith(a.pattern)) {\n        return 1;\n      }\n\n      const aParts = a.pattern.split('/');\n      const bParts = b.pattern.split('/');\n\n      for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {\n        // if b is longer, b get higher priority\n        if (aParts[i] == null) {\n          return 1;\n        }\n        // if a is longer, a get higher priority\n        if (bParts[i] == null) {\n          return -1;\n        }\n        const aWildCard = aParts[i] === '*' || aParts[i].startsWith(':');\n        const bWildCard = bParts[i] === '*' || bParts[i].startsWith(':');\n        // if both are wildcard we compare next component\n        if (aWildCard && bWildCard) {\n          continue;\n        }\n        // if only a is wild card, b get higher priority\n        if (aWildCard) {\n          return 1;\n        }\n        // if only b is wild card, a get higher priority\n        if (bWildCard) {\n          return -1;\n        }\n      }\n      return bParts.length - aParts.length;\n    });\n\n  // Check for duplicate patterns in the config\n  configs.reduce<Record<string, RouteConfig>>((acc, config) => {\n    if (acc[config.pattern]) {\n      const a = acc[config.pattern].routeNames;\n      const b = config.routeNames;\n\n      // It's not a problem if the path string omitted from a inner most screen\n      // For example, it's ok if a path resolves to `A > B > C` or `A > B`\n      const intersects =\n        a.length > b.length\n          ? b.every((it, i) => a[i] === it)\n          : a.every((it, i) => b[i] === it);\n\n      if (!intersects) {\n        throw new Error(\n          `Found conflicting screens with the same pattern. The pattern '${\n            config.pattern\n          }' resolves to both '${a.join(' > ')}' and '${b.join(\n            ' > '\n          )}'. Patterns must be unique and cannot resolve to more than one screen.`\n        );\n      }\n    }\n\n    return Object.assign(acc, {\n      [config.pattern]: config,\n    });\n  }, {});\n\n  if (remaining === '/') {\n    // We need to add special handling of empty path so navigation to empty path also works\n    // When handling empty path, we should only look at the root level config\n    const match = configs.find(\n      (config) =>\n        config.path === '' &&\n        config.routeNames.every(\n          // Make sure that none of the parent configs have a non-empty path defined\n          (name) => !configs.find((c) => c.screen === name)?.path\n        )\n    );\n\n    if (match) {\n      return createNestedStateObject(\n        path,\n        match.routeNames.map((name) => ({ name })),\n        initialRoutes,\n        configs\n      );\n    }\n\n    return undefined;\n  }\n\n  let result: PartialState<NavigationState> | undefined;\n  let current: PartialState<NavigationState> | undefined;\n\n  // We match the whole path against the regex instead of segments\n  // This makes sure matches such as wildcard will catch any unmatched routes, even if nested\n  const { routes, remainingPath } = matchAgainstConfigs(\n    remaining,\n    configs.map((c) => ({\n      ...c,\n      // Add `$` to the regex to make sure it matches till end of the path and not just beginning\n      regex: c.regex ? new RegExp(c.regex.source + '$') : undefined,\n    }))\n  );\n\n  if (routes !== undefined) {\n    // This will always be empty if full path matched\n    current = createNestedStateObject(path, routes, initialRoutes, configs);\n    remaining = remainingPath;\n    result = current;\n  }\n\n  if (current == null || result == null) {\n    return undefined;\n  }\n\n  return result;\n}\n\nconst joinPaths = (...paths: string[]): string =>\n  ([] as string[])\n    .concat(...paths.map((p) => p.split('/')))\n    .filter(Boolean)\n    .join('/');\n\nconst matchAgainstConfigs = (remaining: string, configs: RouteConfig[]) => {\n  let routes: ParsedRoute[] | undefined;\n  let remainingPath = remaining;\n\n  // Go through all configs, and see if the next path segment matches our regex\n  for (const config of configs) {\n    if (!config.regex) {\n      continue;\n    }\n\n    const match = remainingPath.match(config.regex);\n\n    // If our regex matches, we need to extract params from the path\n    if (match) {\n      const matchResult = config.pattern?.split('/').reduce<{\n        pos: number; // Position of the current path param segment in the path (e.g in pattern `a/:b/:c`, `:a` is 0 and `:b` is 1)\n        matchedParams: Record<string, Record<string, string>>; // The extracted params\n      }>(\n        (acc, p, index) => {\n          if (!p.startsWith(':')) {\n            return acc;\n          }\n\n          // Path parameter so increment position for the segment\n          acc.pos += 1;\n\n          const decodedParamSegment = decodeURIComponent(\n            // The param segments appear every second item starting from 2 in the regex match result\n            match![(acc.pos + 1) * 2]\n              // Remove trailing slash\n              .replace(/\\/$/, '')\n          );\n\n          Object.assign(acc.matchedParams, {\n            [p]: Object.assign(acc.matchedParams[p] || {}, {\n              [index]: decodedParamSegment,\n            }),\n          });\n\n          return acc;\n        },\n        { pos: -1, matchedParams: {} }\n      );\n\n      const matchedParams = matchResult.matchedParams || {};\n\n      routes = config.routeNames.map((name) => {\n        const routeConfig = configs.find((c) => {\n          // Check matching name AND pattern in case same screen is used at different levels in config\n          return c.screen === name && config.pattern.startsWith(c.pattern);\n        });\n\n        // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n        const normalizedPath = routeConfig?.path\n          .split('/')\n          .filter(Boolean)\n          .join('/');\n\n        // Get the number of segments in the initial pattern\n        const numInitialSegments = routeConfig?.pattern\n          // Extract the prefix from the pattern by removing the ending path pattern (e.g pattern=`a/b/c/d` and normalizedPath=`c/d` becomes `a/b`)\n          .replace(new RegExp(`${escape(normalizedPath!)}$`), '')\n          ?.split('/').length;\n\n        const params = normalizedPath\n          ?.split('/')\n          .reduce<Record<string, unknown>>((acc, p, index) => {\n            if (!p.startsWith(':')) {\n              return acc;\n            }\n\n            // Get the real index of the path parameter in the matched path\n            // by offsetting by the number of segments in the initial pattern\n            const offset = numInitialSegments ? numInitialSegments - 1 : 0;\n            const value = matchedParams[p]?.[index + offset];\n\n            if (value) {\n              const key = p.replace(/^:/, '').replace(/\\?$/, '');\n              acc[key] = routeConfig?.parse?.[key]\n                ? routeConfig.parse[key](value)\n                : value;\n            }\n\n            return acc;\n          }, {});\n\n        if (params && Object.keys(params).length) {\n          return { name, params };\n        }\n\n        return { name };\n      });\n\n      remainingPath = remainingPath.replace(match[1], '');\n\n      break;\n    }\n  }\n\n  return { routes, remainingPath };\n};\n\nconst createNormalizedConfigs = (\n  screen: string,\n  routeConfig: PathConfigMap<object>,\n  routeNames: string[] = [],\n  initials: InitialRouteConfig[],\n  parentScreens: string[],\n  parentPattern?: string\n): RouteConfig[] => {\n  const configs: RouteConfig[] = [];\n\n  routeNames.push(screen);\n\n  parentScreens.push(screen);\n\n  // @ts-expect-error: we can't strongly typecheck this for now\n  const config = routeConfig[screen];\n\n  if (typeof config === 'string') {\n    // If a string is specified as the value of the key(e.g. Foo: '/path'), use it as the pattern\n    const pattern = parentPattern ? joinPaths(parentPattern, config) : config;\n\n    configs.push(createConfigItem(screen, routeNames, pattern, config));\n  } else if (typeof config === 'object') {\n    let pattern: string | undefined;\n\n    // if an object is specified as the value (e.g. Foo: { ... }),\n    // it can have `path` property and\n    // it could have `screens` prop which has nested configs\n    if (typeof config.path === 'string') {\n      if (config.exact && config.path === undefined) {\n        throw new Error(\n          \"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\"\n        );\n      }\n\n      pattern =\n        config.exact !== true\n          ? joinPaths(parentPattern || '', config.path || '')\n          : config.path || '';\n\n      configs.push(\n        createConfigItem(\n          screen,\n          routeNames,\n          pattern!,\n          config.path,\n          config.parse\n        )\n      );\n    }\n\n    if (config.screens) {\n      // property `initialRouteName` without `screens` has no purpose\n      if (config.initialRouteName) {\n        initials.push({\n          initialRouteName: config.initialRouteName,\n          parentScreens,\n        });\n      }\n\n      Object.keys(config.screens).forEach((nestedConfig) => {\n        const result = createNormalizedConfigs(\n          nestedConfig,\n          config.screens as PathConfigMap<object>,\n          routeNames,\n          initials,\n          [...parentScreens],\n          pattern ?? parentPattern\n        );\n\n        configs.push(...result);\n      });\n    }\n  }\n\n  routeNames.pop();\n\n  return configs;\n};\n\nconst createConfigItem = (\n  screen: string,\n  routeNames: string[],\n  pattern: string,\n  path: string,\n  parse?: ParseConfig\n): RouteConfig => {\n  // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n  pattern = pattern.split('/').filter(Boolean).join('/');\n\n  const regex = pattern\n    ? new RegExp(\n        `^(${pattern\n          .split('/')\n          .map((it) => {\n            if (it.startsWith(':')) {\n              return `(([^/]+\\\\/)${it.endsWith('?') ? '?' : ''})`;\n            }\n\n            return `${it === '*' ? '.*' : escape(it)}\\\\/`;\n          })\n          .join('')})`\n      )\n    : undefined;\n\n  return {\n    screen,\n    regex,\n    pattern,\n    path,\n    // The routeNames array is mutated, so copy it to keep the current state\n    routeNames: [...routeNames],\n    parse,\n  };\n};\n\nconst findParseConfigForRoute = (\n  routeName: string,\n  flatConfig: RouteConfig[]\n): ParseConfig | undefined => {\n  for (const config of flatConfig) {\n    if (routeName === config.routeNames[config.routeNames.length - 1]) {\n      return config.parse;\n    }\n  }\n\n  return undefined;\n};\n\n// Try to find an initial route connected with the one passed\nconst findInitialRoute = (\n  routeName: string,\n  parentScreens: string[],\n  initialRoutes: InitialRouteConfig[]\n): string | undefined => {\n  for (const config of initialRoutes) {\n    if (parentScreens.length === config.parentScreens.length) {\n      let sameParents = true;\n      for (let i = 0; i < parentScreens.length; i++) {\n        if (parentScreens[i].localeCompare(config.parentScreens[i]) !== 0) {\n          sameParents = false;\n          break;\n        }\n      }\n      if (sameParents) {\n        return routeName !== config.initialRouteName\n          ? config.initialRouteName\n          : undefined;\n      }\n    }\n  }\n  return undefined;\n};\n\n// returns state object with values depending on whether\n// it is the end of state and if there is initialRoute for this level\nconst createStateObject = (\n  initialRoute: string | undefined,\n  route: ParsedRoute,\n  isEmpty: boolean\n): InitialState => {\n  if (isEmpty) {\n    if (initialRoute) {\n      return {\n        index: 1,\n        routes: [{ name: initialRoute }, route],\n      };\n    } else {\n      return {\n        routes: [route],\n      };\n    }\n  } else {\n    if (initialRoute) {\n      return {\n        index: 1,\n        routes: [{ name: initialRoute }, { ...route, state: { routes: [] } }],\n      };\n    } else {\n      return {\n        routes: [{ ...route, state: { routes: [] } }],\n      };\n    }\n  }\n};\n\nconst createNestedStateObject = (\n  path: string,\n  routes: ParsedRoute[],\n  initialRoutes: InitialRouteConfig[],\n  flatConfig?: RouteConfig[]\n) => {\n  let state: InitialState;\n  let route = routes.shift() as ParsedRoute;\n  const parentScreens: string[] = [];\n\n  let initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n\n  parentScreens.push(route.name);\n\n  state = createStateObject(initialRoute, route, routes.length === 0);\n\n  if (routes.length > 0) {\n    let nestedState = state;\n\n    while ((route = routes.shift() as ParsedRoute)) {\n      initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n\n      const nestedStateIndex =\n        nestedState.index || nestedState.routes.length - 1;\n\n      nestedState.routes[nestedStateIndex].state = createStateObject(\n        initialRoute,\n        route,\n        routes.length === 0\n      );\n\n      if (routes.length > 0) {\n        nestedState = nestedState.routes[nestedStateIndex]\n          .state as InitialState;\n      }\n\n      parentScreens.push(route.name);\n    }\n  }\n\n  route = findFocusedRoute(state) as ParsedRoute;\n  route.path = path;\n\n  const params = parseQueryParams(\n    path,\n    flatConfig ? findParseConfigForRoute(route.name, flatConfig) : undefined\n  );\n\n  if (params) {\n    route.params = { ...route.params, ...params };\n  }\n\n  return state;\n};\n\nconst parseQueryParams = (\n  path: string,\n  parseConfig?: Record<string, (value: string) => any>\n) => {\n  const query = path.split('?')[1];\n  const params = queryString.parse(query);\n\n  if (parseConfig) {\n    Object.keys(params).forEach((name) => {\n      if (\n        Object.hasOwnProperty.call(parseConfig, name) &&\n        typeof params[name] === 'string'\n      ) {\n        params[name] = parseConfig[name](params[name] as string);\n      }\n    });\n  }\n\n  return Object.keys(params).length ? params : undefined;\n};\n"], "mappings": ";;;;AAKA,OAAOA,MAAM,MAAM,sBAAsB;AACzC,OAAO,KAAKC,WAAW,MAAM,cAAc;AAE3C,OAAOC,gBAAgB;AAEvB,OAAOC,kBAAkB;AAsDzB,eAAe,SAASC,gBAAgBA,CACtCC,IAAY,EACZC,OAA4B,EACH;EAAA,IAAAC,IAAA;EACzB,IAAID,OAAO,EAAE;IACXH,kBAAkB,CAACG,OAAO,CAAC;EAC7B;EAEA,IAAIE,aAAmC,GAAG,EAAE;EAE5C,IAAIF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEG,gBAAgB,EAAE;IAC7BD,aAAa,CAACE,IAAI,CAAC;MACjBD,gBAAgB,EAAEH,OAAO,CAACG,gBAAgB;MAC1CE,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EAEA,IAAMC,OAAO,GAAGN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,OAAO;EAEhC,IAAIC,SAAS,GAAGR,IAAI,CACjBS,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;EAGvBD,SAAS,GAAGA,SAAS,CAACE,QAAQ,CAAC,GAAG,CAAC,GAAGF,SAAS,GAAI,GAAEA,SAAU,GAAE;EAEjE,IAAID,OAAO,KAAKI,SAAS,EAAE;IAEzB,IAAMC,OAAM,GAAGJ,SAAS,CACrBK,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAACC,OAAO,CAAC,CACfC,GAAG,CAAE,UAAAC,OAAO,EAAK;MAChB,IAAMC,IAAI,GAAGC,kBAAkB,CAACF,OAAO,CAAC;MACxC,OAAO;QAAEC,IAAA,EAAAA;MAAK,CAAC;IACjB,CAAC,CAAC;IAEJ,IAAIN,OAAM,CAACQ,MAAM,EAAE;MACjB,OAAOC,uBAAuB,CAACrB,IAAI,EAAEY,OAAM,EAAET,aAAa,CAAC;IAC7D;IAEA,OAAOQ,SAAS;EAClB;EAGA,IAAMW,OAAO,GAAI,CAAApB,IAAA,KAAE,EAChBqB,MAAM,CAAAC,KAAA,CAAAtB,IAAA,EAAAuB,kBAAA,CACFC,MAAM,CAACC,IAAI,CAACpB,OAAO,CAAC,CAACS,GAAG,CAAE,UAAAY,GAAG;IAAA,OAC9BC,wBAAuB,CACrBD,GAAG,EACHrB,OAAO,EACP,EAAE,EACFJ,aAAa,EACb,EAAE,CACH;EAAA,EACF,EACF,CACA2B,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IAOd,IAAID,CAAC,CAACE,OAAO,KAAKD,CAAC,CAACC,OAAO,EAAE;MAC3B,OAAOD,CAAC,CAACE,UAAU,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,aAAa,CAACL,CAAC,CAACG,UAAU,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;IACrE;IAIA,IAAIJ,CAAC,CAACE,OAAO,CAACI,UAAU,CAACL,CAAC,CAACC,OAAO,CAAC,EAAE;MACnC,OAAO,CAAC,CAAC;IACX;IAEA,IAAID,CAAC,CAACC,OAAO,CAACI,UAAU,CAACN,CAAC,CAACE,OAAO,CAAC,EAAE;MACnC,OAAO,CAAC;IACV;IAEA,IAAMK,MAAM,GAAGP,CAAC,CAACE,OAAO,CAACpB,KAAK,CAAC,GAAG,CAAC;IACnC,IAAM0B,MAAM,GAAGP,CAAC,CAACC,OAAO,CAACpB,KAAK,CAAC,GAAG,CAAC;IAEnC,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAAClB,MAAM,EAAEmB,MAAM,CAACnB,MAAM,CAAC,EAAEoB,CAAC,EAAE,EAAE;MAE/D,IAAIF,MAAM,CAACE,CAAC,CAAC,IAAI,IAAI,EAAE;QACrB,OAAO,CAAC;MACV;MAEA,IAAID,MAAM,CAACC,CAAC,CAAC,IAAI,IAAI,EAAE;QACrB,OAAO,CAAC,CAAC;MACX;MACA,IAAMG,SAAS,GAAGL,MAAM,CAACE,CAAC,CAAC,KAAK,GAAG,IAAIF,MAAM,CAACE,CAAC,CAAC,CAACH,UAAU,CAAC,GAAG,CAAC;MAChE,IAAMO,SAAS,GAAGL,MAAM,CAACC,CAAC,CAAC,KAAK,GAAG,IAAID,MAAM,CAACC,CAAC,CAAC,CAACH,UAAU,CAAC,GAAG,CAAC;MAEhE,IAAIM,SAAS,IAAIC,SAAS,EAAE;QAC1B;MACF;MAEA,IAAID,SAAS,EAAE;QACb,OAAO,CAAC;MACV;MAEA,IAAIC,SAAS,EAAE;QACb,OAAO,CAAC,CAAC;MACX;IACF;IACA,OAAOL,MAAM,CAACnB,MAAM,GAAGkB,MAAM,CAAClB,MAAM;EACtC,CAAC,CAAC;EAGJE,OAAO,CAACuB,MAAM,CAA8B,UAACC,GAAG,EAAEC,MAAM,EAAK;IAC3D,IAAID,GAAG,CAACC,MAAM,CAACd,OAAO,CAAC,EAAE;MACvB,IAAMF,CAAC,GAAGe,GAAG,CAACC,MAAM,CAACd,OAAO,CAAC,CAACC,UAAU;MACxC,IAAMF,CAAC,GAAGe,MAAM,CAACb,UAAU;MAI3B,IAAMc,UAAU,GACdjB,CAAC,CAACX,MAAM,GAAGY,CAAC,CAACZ,MAAM,GACfY,CAAC,CAACiB,KAAK,CAAC,UAACC,EAAE,EAAEV,CAAC;QAAA,OAAKT,CAAC,CAACS,CAAC,CAAC,KAAKU,EAAE;MAAA,EAAC,GAC/BnB,CAAC,CAACkB,KAAK,CAAC,UAACC,EAAE,EAAEV,CAAC;QAAA,OAAKR,CAAC,CAACQ,CAAC,CAAC,KAAKU,EAAE;MAAA,EAAC;MAErC,IAAI,CAACF,UAAU,EAAE;QACf,MAAM,IAAIG,KAAK,CACZ,iEACCJ,MAAM,CAACd,OACR,uBAAsBF,CAAC,CAACI,IAAI,CAAC,KAAK,CAAE,UAASH,CAAC,CAACG,IAAI,CAClD,KAAK,CACL,wEAAuE,CAC1E;MACH;IACF;IAEA,OAAOT,MAAM,CAAC0B,MAAM,CAACN,GAAG,EAAAO,eAAA,KACrBN,MAAM,CAACd,OAAO,EAAGc,MAAA,CACnB,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,IAAIvC,SAAS,KAAK,GAAG,EAAE;IAGrB,IAAM8C,KAAK,GAAGhC,OAAO,CAACiC,IAAI,CACvB,UAAAR,MAAM;MAAA,OACLA,MAAM,CAAC/C,IAAI,KAAK,EAAE,IAClB+C,MAAM,CAACb,UAAU,CAACe,KAAK,CAEpB,UAAA/B,IAAI;QAAA,IAAAsC,aAAA;QAAA,OAAK,GAAAA,aAAA,GAAClC,OAAO,CAACiC,IAAI,CAAE,UAAAE,CAAC;UAAA,OAAKA,CAAC,CAACC,MAAM,KAAKxC,IAAI;QAAA,EAAC,cAAAsC,aAAA,eAAtCA,aAAA,CAAwCxD,IAAI;MAAA,EACxD;IAAA,EACJ;IAED,IAAIsD,KAAK,EAAE;MACT,OAAOjC,uBAAuB,CAC5BrB,IAAI,EACJsD,KAAK,CAACpB,UAAU,CAAClB,GAAG,CAAE,UAAAE,IAAI;QAAA,OAAM;UAAEA,IAAA,EAAAA;QAAK,CAAC;MAAA,CAAC,CAAC,EAC1Cf,aAAa,EACbmB,OAAO,CACR;IACH;IAEA,OAAOX,SAAS;EAClB;EAEA,IAAIgD,MAAiD;EACrD,IAAIC,OAAkD;EAItD,IAAAC,oBAAA,GAAkCC,mBAAmB,CACnDtD,SAAS,EACTc,OAAO,CAACN,GAAG,CAAE,UAAAyC,CAAC;MAAA,OAAAM,aAAA,CAAAA,aAAA,KACTN,CAAC;QAEJO,KAAK,EAAEP,CAAC,CAACO,KAAK,GAAG,IAAIC,MAAM,CAACR,CAAC,CAACO,KAAK,CAACE,MAAM,GAAG,GAAG,CAAC,GAAGvD;MAAA;IAAA,CACpD,CAAC,CACJ;IAPOC,MAAM,GAAAiD,oBAAA,CAANjD,MAAM;IAAEuD,aAAA,GAAAN,oBAAA,CAAAM,aAAA;EAShB,IAAIvD,MAAM,KAAKD,SAAS,EAAE;IAExBiD,OAAO,GAAGvC,uBAAuB,CAACrB,IAAI,EAAEY,MAAM,EAAET,aAAa,EAAEmB,OAAO,CAAC;IACvEd,SAAS,GAAG2D,aAAa;IACzBR,MAAM,GAAGC,OAAO;EAClB;EAEA,IAAIA,OAAO,IAAI,IAAI,IAAID,MAAM,IAAI,IAAI,EAAE;IACrC,OAAOhD,SAAS;EAClB;EAEA,OAAOgD,MAAM;AACf;AAEA,IAAMS,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAG;EAAA,IAAAC,KAAA;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAnD,MAAA,EAAIoD,KAAK,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;IAALF,KAAK,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;EAAA;EAAA,OACxB,CAAAL,KAAA,KAAE,EACA9C,MAAM,CAAAC,KAAA,CAAA6C,KAAA,EAAA5C,kBAAA,CAAI+C,KAAK,CAACxD,GAAG,CAAE,UAAA2D,CAAC;IAAA,OAAKA,CAAC,CAAC9D,KAAK,CAAC,GAAG,CAAC;EAAA,EAAC,EAAC,CACzCC,MAAM,CAACC,OAAO,CAAC,CACfoB,IAAI,CAAC,GAAG,CAAC;AAAA;AAEd,IAAM2B,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAItD,SAAiB,EAAEc,OAAsB,EAAK;EACzE,IAAIV,MAAiC;EACrC,IAAIuD,aAAa,GAAG3D,SAAS;EAAA,IAAAoE,KAAA,YAAAA,MAAA7B,MAAA,EAGC;MAC5B,IAAI,CAACA,MAAM,CAACiB,KAAK,EAAE;QAAA;MAEnB;MAEA,IAAMV,KAAK,GAAGa,aAAa,CAACb,KAAK,CAACP,MAAM,CAACiB,KAAK,CAAC;MAG/C,IAAIV,KAAK,EAAE;QACT,IAAMuB,WAAW,IAAAC,eAAA,GAAG/B,MAAM,CAACd,OAAO,cAAA6C,eAAA,uBAAdA,eAAA,CAAgBjE,KAAK,CAAC,GAAG,CAAC,CAACgC,MAAM,CAInD,UAACC,GAAG,EAAE6B,CAAC,EAAEI,KAAK,EAAK;UACjB,IAAI,CAACJ,CAAC,CAACtC,UAAU,CAAC,GAAG,CAAC,EAAE;YACtB,OAAOS,GAAG;UACZ;UAGAA,GAAG,CAACkC,GAAG,IAAI,CAAC;UAEZ,IAAMC,mBAAmB,GAAG9D,kBAAkB,CAE5CmC,KAAK,CAAE,CAACR,GAAG,CAACkC,GAAG,GAAG,CAAC,IAAI,CAAC,EAErBvE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CACtB;UAEDiB,MAAM,CAAC0B,MAAM,CAACN,GAAG,CAACoC,aAAa,EAAA7B,eAAA,KAC5BsB,CAAC,EAAGjD,MAAM,CAAC0B,MAAM,CAACN,GAAG,CAACoC,aAAa,CAACP,CAAC,CAAC,IAAI,CAAC,CAAC,EAAAtB,eAAA,KAC1C0B,KAAK,EAAGE,mBAAA,CACV,EACF,CAAC;UAEF,OAAOnC,GAAG;QACZ,CAAC,EACD;UAAEkC,GAAG,EAAE,CAAC,CAAC;UAAEE,aAAa,EAAE,CAAC;QAAE,CAAC,CAC/B;QAED,IAAMA,aAAa,GAAGL,WAAW,CAACK,aAAa,IAAI,CAAC,CAAC;QAErDtE,MAAM,GAAGmC,MAAM,CAACb,UAAU,CAAClB,GAAG,CAAE,UAAAE,IAAI,EAAK;UAAA,IAAAiE,qBAAA;UACvC,IAAMC,WAAW,GAAG9D,OAAO,CAACiC,IAAI,CAAE,UAAAE,CAAC,EAAK;YAEtC,OAAOA,CAAC,CAACC,MAAM,KAAKxC,IAAI,IAAI6B,MAAM,CAACd,OAAO,CAACI,UAAU,CAACoB,CAAC,CAACxB,OAAO,CAAC;UAClE,CAAC,CAAC;UAGF,IAAMoD,cAAc,GAAGD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEpF,IAAI,CACrCa,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAACC,OAAO,CAAC,CACfoB,IAAI,CAAC,GAAG,CAAC;UAGZ,IAAMmD,kBAAkB,GAAGF,WAAW,aAAXA,WAAW,wBAAAD,qBAAA,GAAXC,WAAW,CAAEnD,OAAA,CAErCxB,OAAO,CAAC,IAAIwD,MAAM,CAAE,GAAEtE,MAAM,CAAC0F,cAAc,CAAG,GAAE,CAAC,EAAE,EAAE,CAAC,cAAAF,qBAAA,uBAF9BA,qBAAA,CAGvBtE,KAAK,CAAC,GAAG,CAAC,CAACO,MAAM;UAErB,IAAMmE,MAAM,GAAGF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CACzBxE,KAAK,CAAC,GAAG,CAAC,CACXgC,MAAM,CAA0B,UAACC,GAAG,EAAE6B,CAAC,EAAEI,KAAK,EAAK;YAAA,IAAAS,gBAAA;YAClD,IAAI,CAACb,CAAC,CAACtC,UAAU,CAAC,GAAG,CAAC,EAAE;cACtB,OAAOS,GAAG;YACZ;YAIA,IAAM2C,MAAM,GAAGH,kBAAkB,GAAGA,kBAAkB,GAAG,CAAC,GAAG,CAAC;YAC9D,IAAMI,KAAK,IAAAF,gBAAA,GAAGN,aAAa,CAACP,CAAC,CAAC,cAAAa,gBAAA,uBAAhBA,gBAAA,CAAmBT,KAAK,GAAGU,MAAM,CAAC;YAEhD,IAAIC,KAAK,EAAE;cAAA,IAAAC,kBAAA;cACT,IAAM/D,GAAG,GAAG+C,CAAC,CAAClE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;cAClDqC,GAAG,CAAClB,GAAG,CAAC,GAAGwD,WAAW,aAAXA,WAAW,gBAAAO,kBAAA,GAAXP,WAAW,CAAEQ,KAAK,cAAAD,kBAAA,eAAlBA,kBAAA,CAAqB/D,GAAG,CAAC,GAChCwD,WAAW,CAACQ,KAAK,CAAChE,GAAG,CAAC,CAAC8D,KAAK,CAAC,GAC7BA,KAAK;YACX;YAEA,OAAO5C,GAAG;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC;UAER,IAAIyC,MAAM,IAAI7D,MAAM,CAACC,IAAI,CAAC4D,MAAM,CAAC,CAACnE,MAAM,EAAE;YACxC,OAAO;cAAEF,IAAI,EAAJA,IAAI;cAAEqE,MAAA,EAAAA;YAAO,CAAC;UACzB;UAEA,OAAO;YAAErE,IAAA,EAAAA;UAAK,CAAC;QACjB,CAAC,CAAC;QAEFiD,aAAa,GAAGA,aAAa,CAAC1D,OAAO,CAAC6C,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAAA;MAGrD;IACF;IAAAwB,eAAA;IAAAe,IAAA;EA5FA,KAAK,IAAM9C,MAAM,IAAIzB,OAAO;IAAAuE,IAAA,GAAAjB,KAAA,CAAA7B,MAAA;IAAA,IAAA8C,IAAA,QAExB;IAAA,IAAAA,IAAA,QAwFA;EAAA;EAIJ,OAAO;IAAEjF,MAAM,EAANA,MAAM;IAAEuD,aAAA,EAAAA;EAAc,CAAC;AAClC,CAAC;AAED,IAAMtC,wBAAuB,GAAG,SAA1BA,uBAAuBA,CAC3B6B,MAAc,EACd0B,WAAkC,EAKhB;EAAA,IAJlBlD,UAAoB,GAAAqC,SAAA,CAAAnD,MAAA,QAAAmD,SAAA,QAAA5D,SAAA,GAAA4D,SAAA,MAAG,EAAE;EAAA,IACzBuB,QAA8B,GAAAvB,SAAA,CAAAnD,MAAA,OAAAmD,SAAA,MAAA5D,SAAA;EAAA,IAC9BL,aAAuB,GAAAiE,SAAA,CAAAnD,MAAA,OAAAmD,SAAA,MAAA5D,SAAA;EAAA,IACvBoF,aAAsB,GAAAxB,SAAA,CAAAnD,MAAA,OAAAmD,SAAA,MAAA5D,SAAA;EAEtB,IAAMW,OAAsB,GAAG,EAAE;EAEjCY,UAAU,CAAC7B,IAAI,CAACqD,MAAM,CAAC;EAEvBpD,aAAa,CAACD,IAAI,CAACqD,MAAM,CAAC;EAG1B,IAAMX,MAAM,GAAGqC,WAAW,CAAC1B,MAAM,CAAC;EAElC,IAAI,OAAOX,MAAM,KAAK,QAAQ,EAAE;IAE9B,IAAMd,OAAO,GAAG8D,aAAa,GAAG3B,SAAS,CAAC2B,aAAa,EAAEhD,MAAM,CAAC,GAAGA,MAAM;IAEzEzB,OAAO,CAACjB,IAAI,CAAC2F,gBAAgB,CAACtC,MAAM,EAAExB,UAAU,EAAED,OAAO,EAAEc,MAAM,CAAC,CAAC;EACrE,CAAC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IACrC,IAAId,QAA2B;IAK/B,IAAI,OAAOc,MAAM,CAAC/C,IAAI,KAAK,QAAQ,EAAE;MACnC,IAAI+C,MAAM,CAACkD,KAAK,IAAIlD,MAAM,CAAC/C,IAAI,KAAKW,SAAS,EAAE;QAC7C,MAAM,IAAIwC,KAAK,CACb,sJAAsJ,CACvJ;MACH;MAEAlB,QAAO,GACLc,MAAM,CAACkD,KAAK,KAAK,IAAI,GACjB7B,SAAS,CAAC2B,aAAa,IAAI,EAAE,EAAEhD,MAAM,CAAC/C,IAAI,IAAI,EAAE,CAAC,GACjD+C,MAAM,CAAC/C,IAAI,IAAI,EAAE;MAEvBsB,OAAO,CAACjB,IAAI,CACV2F,gBAAgB,CACdtC,MAAM,EACNxB,UAAU,EACVD,QAAO,EACPc,MAAM,CAAC/C,IAAI,EACX+C,MAAM,CAAC6C,KAAK,CACb,CACF;IACH;IAEA,IAAI7C,MAAM,CAACxC,OAAO,EAAE;MAElB,IAAIwC,MAAM,CAAC3C,gBAAgB,EAAE;QAC3B0F,QAAQ,CAACzF,IAAI,CAAC;UACZD,gBAAgB,EAAE2C,MAAM,CAAC3C,gBAAgB;UACzCE,aAAA,EAAAA;QACF,CAAC,CAAC;MACJ;MAEAoB,MAAM,CAACC,IAAI,CAACoB,MAAM,CAACxC,OAAO,CAAC,CAAC2F,OAAO,CAAE,UAAAC,YAAY,EAAK;QAAA,IAAAC,SAAA;QACpD,IAAMzC,MAAM,GAAG9B,wBAAuB,CACpCsE,YAAY,EACZpD,MAAM,CAACxC,OAAO,EACd2B,UAAU,EACV4D,QAAQ,EAAArE,kBAAA,CACJnB,aAAa,IAAA8F,SAAA,GACjBnE,QAAO,YAAAmE,SAAA,GAAIL,aAAa,CACzB;QAEDzE,OAAO,CAACjB,IAAI,CAAAmB,KAAA,CAAZF,OAAO,EAAAG,kBAAA,CAASkC,MAAM,EAAC;MACzB,CAAC,CAAC;IACJ;EACF;EAEAzB,UAAU,CAACmE,GAAG,EAAE;EAEhB,OAAO/E,OAAO;AAChB,CAAC;AAED,IAAM0E,gBAAgB,GAAG,SAAnBA,gBAAgBA,CACpBtC,MAAc,EACdxB,UAAoB,EACpBD,OAAe,EACfjC,IAAY,EACZ4F,KAAmB,EACH;EAEhB3D,OAAO,GAAGA,OAAO,CAACpB,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACoB,IAAI,CAAC,GAAG,CAAC;EAEtD,IAAM6B,KAAK,GAAG/B,OAAO,GACjB,IAAIgC,MAAM,CACP,KAAIhC,OAAO,CACTpB,KAAK,CAAC,GAAG,CAAC,CACVG,GAAG,CAAE,UAAAkC,EAAE,EAAK;IACX,IAAIA,EAAE,CAACb,UAAU,CAAC,GAAG,CAAC,EAAE;MACtB,OAAQ,cAAaa,EAAE,CAACxC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG,GAAE;IACrD;IAEA,OAAQ,GAAEwC,EAAE,KAAK,GAAG,GAAG,IAAI,GAAGvD,MAAM,CAACuD,EAAE,CAAE,KAAI;EAC/C,CAAC,CAAC,CACDf,IAAI,CAAC,EAAE,CAAE,GAAE,CACf,GACDxB,SAAS;EAEb,OAAO;IACL+C,MAAM,EAANA,MAAM;IACNM,KAAK,EAALA,KAAK;IACL/B,OAAO,EAAPA,OAAO;IACPjC,IAAI,EAAJA,IAAI;IAEJkC,UAAU,EAAAT,kBAAA,CAAMS,UAAU,CAAC;IAC3B0D,KAAA,EAAAA;EACF,CAAC;AACH,CAAC;AAED,IAAMU,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAC3BC,SAAiB,EACjBC,UAAyB,EACG;EAC5B,KAAK,IAAMzD,MAAM,IAAIyD,UAAU,EAAE;IAC/B,IAAID,SAAS,KAAKxD,MAAM,CAACb,UAAU,CAACa,MAAM,CAACb,UAAU,CAACd,MAAM,GAAG,CAAC,CAAC,EAAE;MACjE,OAAO2B,MAAM,CAAC6C,KAAK;IACrB;EACF;EAEA,OAAOjF,SAAS;AAClB,CAAC;AAGD,IAAM8F,gBAAgB,GAAG,SAAnBA,gBAAgBA,CACpBF,SAAiB,EACjBjG,aAAuB,EACvBH,aAAmC,EACZ;EACvB,KAAK,IAAM4C,MAAM,IAAI5C,aAAa,EAAE;IAClC,IAAIG,aAAa,CAACc,MAAM,KAAK2B,MAAM,CAACzC,aAAa,CAACc,MAAM,EAAE;MACxD,IAAIsF,WAAW,GAAG,IAAI;MACtB,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlC,aAAa,CAACc,MAAM,EAAEoB,CAAC,EAAE,EAAE;QAC7C,IAAIlC,aAAa,CAACkC,CAAC,CAAC,CAACJ,aAAa,CAACW,MAAM,CAACzC,aAAa,CAACkC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UACjEkE,WAAW,GAAG,KAAK;UACnB;QACF;MACF;MACA,IAAIA,WAAW,EAAE;QACf,OAAOH,SAAS,KAAKxD,MAAM,CAAC3C,gBAAgB,GACxC2C,MAAM,CAAC3C,gBAAgB,GACvBO,SAAS;MACf;IACF;EACF;EACA,OAAOA,SAAS;AAClB,CAAC;AAID,IAAMgG,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,YAAgC,EAChCC,KAAkB,EAClBC,OAAgB,EACC;EACjB,IAAIA,OAAO,EAAE;IACX,IAAIF,YAAY,EAAE;MAChB,OAAO;QACL7B,KAAK,EAAE,CAAC;QACRnE,MAAM,EAAE,CAAC;UAAEM,IAAI,EAAE0F;QAAa,CAAC,EAAEC,KAAK;MACxC,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLjG,MAAM,EAAE,CAACiG,KAAK;MAChB,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAID,YAAY,EAAE;MAChB,OAAO;QACL7B,KAAK,EAAE,CAAC;QACRnE,MAAM,EAAE,CAAC;UAAEM,IAAI,EAAE0F;QAAa,CAAC,EAAA7C,aAAA,CAAAA,aAAA,KAAO8C,KAAK;UAAEE,KAAK,EAAE;YAAEnG,MAAM,EAAE;UAAG;QAAA;MACnE,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLA,MAAM,EAAE,CAAAmD,aAAA,CAAAA,aAAA,KAAM8C,KAAK;UAAEE,KAAK,EAAE;YAAEnG,MAAM,EAAE;UAAG;QAAA;MAC3C,CAAC;IACH;EACF;AACF,CAAC;AAED,IAAMS,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAC3BrB,IAAY,EACZY,MAAqB,EACrBT,aAAmC,EACnCqG,UAA0B,EACvB;EACH,IAAIO,KAAmB;EACvB,IAAIF,KAAK,GAAGjG,MAAM,CAACoG,KAAK,EAAiB;EACzC,IAAM1G,aAAuB,GAAG,EAAE;EAElC,IAAIsG,YAAY,GAAGH,gBAAgB,CAACI,KAAK,CAAC3F,IAAI,EAAEZ,aAAa,EAAEH,aAAa,CAAC;EAE7EG,aAAa,CAACD,IAAI,CAACwG,KAAK,CAAC3F,IAAI,CAAC;EAE9B6F,KAAK,GAAGJ,iBAAiB,CAACC,YAAY,EAAEC,KAAK,EAAEjG,MAAM,CAACQ,MAAM,KAAK,CAAC,CAAC;EAEnE,IAAIR,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;IACrB,IAAI6F,WAAW,GAAGF,KAAK;IAEvB,OAAQF,KAAK,GAAGjG,MAAM,CAACoG,KAAK,EAAiB,EAAG;MAC9CJ,YAAY,GAAGH,gBAAgB,CAACI,KAAK,CAAC3F,IAAI,EAAEZ,aAAa,EAAEH,aAAa,CAAC;MAEzE,IAAM+G,gBAAgB,GACpBD,WAAW,CAAClC,KAAK,IAAIkC,WAAW,CAACrG,MAAM,CAACQ,MAAM,GAAG,CAAC;MAEpD6F,WAAW,CAACrG,MAAM,CAACsG,gBAAgB,CAAC,CAACH,KAAK,GAAGJ,iBAAiB,CAC5DC,YAAY,EACZC,KAAK,EACLjG,MAAM,CAACQ,MAAM,KAAK,CAAC,CACpB;MAED,IAAIR,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;QACrB6F,WAAW,GAAGA,WAAW,CAACrG,MAAM,CAACsG,gBAAgB,CAAC,CAC/CH,KAAqB;MAC1B;MAEAzG,aAAa,CAACD,IAAI,CAACwG,KAAK,CAAC3F,IAAI,CAAC;IAChC;EACF;EAEA2F,KAAK,GAAGhH,gBAAgB,CAACkH,KAAK,CAAgB;EAC9CF,KAAK,CAAC7G,IAAI,GAAGA,IAAI;EAEjB,IAAMuF,MAAM,GAAG4B,gBAAgB,CAC7BnH,IAAI,EACJwG,UAAU,GAAGF,uBAAuB,CAACO,KAAK,CAAC3F,IAAI,EAAEsF,UAAU,CAAC,GAAG7F,SAAS,CACzE;EAED,IAAI4E,MAAM,EAAE;IACVsB,KAAK,CAACtB,MAAM,GAAAxB,aAAA,CAAAA,aAAA,KAAQ8C,KAAK,CAACtB,MAAM,GAAKA,MAAA,CAAQ;EAC/C;EAEA,OAAOwB,KAAK;AACd,CAAC;AAED,IAAMI,gBAAgB,GAAG,SAAnBA,gBAAgBA,CACpBnH,IAAY,EACZoH,WAAoD,EACjD;EACH,IAAMC,KAAK,GAAGrH,IAAI,CAACa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC,IAAM0E,MAAM,GAAG3F,WAAW,CAACgG,KAAK,CAACyB,KAAK,CAAC;EAEvC,IAAID,WAAW,EAAE;IACf1F,MAAM,CAACC,IAAI,CAAC4D,MAAM,CAAC,CAACW,OAAO,CAAE,UAAAhF,IAAI,EAAK;MACpC,IACEQ,MAAM,CAAC4F,cAAc,CAACC,IAAI,CAACH,WAAW,EAAElG,IAAI,CAAC,IAC7C,OAAOqE,MAAM,CAACrE,IAAI,CAAC,KAAK,QAAQ,EAChC;QACAqE,MAAM,CAACrE,IAAI,CAAC,GAAGkG,WAAW,CAAClG,IAAI,CAAC,CAACqE,MAAM,CAACrE,IAAI,CAAC,CAAW;MAC1D;IACF,CAAC,CAAC;EACJ;EAEA,OAAOQ,MAAM,CAACC,IAAI,CAAC4D,MAAM,CAAC,CAACnE,MAAM,GAAGmE,MAAM,GAAG5E,SAAS;AACxD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}