{"ast": null, "code": "import React, { useState } from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useTheme } from \"../theme/ThemeProvider\";\nimport FoodList from \"../components/FoodList\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar FoodScreen = function FoodScreen(_ref) {\n  var navigation = _ref.navigation;\n  var _useTheme = useTheme(),\n    theme = _useTheme.theme;\n  var handleSelectFood = function handleSelectFood(food) {\n    navigation.navigate('FoodDetail', {\n      food: food\n    });\n  };\n  var handleAddFood = function handleAddFood() {\n    navigation.navigate('FoodDetail', {\n      isNew: true\n    });\n  };\n  var handleEditFood = function handleEditFood(food) {\n    navigation.navigate('FoodDetail', {\n      food: food,\n      isEditing: true\n    });\n  };\n  return _jsx(View, {\n    style: [styles.container, {\n      backgroundColor: theme.colors.background\n    }],\n    children: _jsx(FoodList, {\n      onSelectFood: handleSelectFood,\n      onAddFood: handleAddFood,\n      onEditFood: handleEditFood\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  }\n});\nexport default FoodScreen;", "map": {"version": 3, "names": ["React", "useState", "StyleSheet", "View", "useTheme", "FoodList", "jsx", "_jsx", "FoodScreen", "_ref", "navigation", "_useTheme", "theme", "handleSelectFood", "food", "navigate", "handleAddFood", "isNew", "handleEditFood", "isEditing", "style", "styles", "container", "backgroundColor", "colors", "background", "children", "onSelectFood", "onAddFood", "onEditFood", "create", "flex"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/app/src/screens/FoodScreen.js"], "sourcesContent": ["/**\n * Food Screen for Znü<PERSON><PERSON>ähler\n * Displays a list of foods and allows searching and filtering\n */\n\nimport React, { useState } from 'react';\nimport { StyleSheet, View } from 'react-native';\nimport { useTheme } from '../theme/ThemeProvider';\nimport FoodList from '../components/FoodList';\n\n/**\n * Food Screen Component\n * @param {Object} props - Component props\n * @param {Object} props.navigation - Navigation object\n * @returns {JSX.Element} - Food screen component\n */\nconst FoodScreen = ({ navigation }) => {\n  const { theme } = useTheme();\n  \n  // Handle select food\n  const handleSelectFood = (food) => {\n    navigation.navigate('FoodDetail', { food });\n  };\n  \n  // Handle add food\n  const handleAddFood = () => {\n    navigation.navigate('FoodDetail', { isNew: true });\n  };\n  \n  // Handle edit food\n  const handleEditFood = (food) => {\n    navigation.navigate('FoodDetail', { food, isEditing: true });\n  };\n  \n  return (\n    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <FoodList\n        onSelectFood={handleSelectFood}\n        onAddFood={handleAddFood}\n        onEditFood={handleEditFood}\n      />\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n\nexport default FoodScreen;\n"], "mappings": "AAKA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAExC,SAASC,QAAQ;AACjB,OAAOC,QAAQ;AAA+B,SAAAC,GAAA,IAAAC,IAAA;AAQ9C,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAAC,IAAA,EAAuB;EAAA,IAAjBC,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAC9B,IAAAC,SAAA,GAAkBP,QAAQ,CAAC,CAAC;IAApBQ,KAAK,GAAAD,SAAA,CAALC,KAAK;EAGb,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;IACjCJ,UAAU,CAACK,QAAQ,CAAC,YAAY,EAAE;MAAED,IAAI,EAAJA;IAAK,CAAC,CAAC;EAC7C,CAAC;EAGD,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BN,UAAU,CAACK,QAAQ,CAAC,YAAY,EAAE;MAAEE,KAAK,EAAE;IAAK,CAAC,CAAC;EACpD,CAAC;EAGD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIJ,IAAI,EAAK;IAC/BJ,UAAU,CAACK,QAAQ,CAAC,YAAY,EAAE;MAAED,IAAI,EAAJA,IAAI;MAAEK,SAAS,EAAE;IAAK,CAAC,CAAC;EAC9D,CAAC;EAED,OACEZ,IAAA,CAACJ,IAAI;IAACiB,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAEX,KAAK,CAACY,MAAM,CAACC;IAAW,CAAC,CAAE;IAAAC,QAAA,EAC5EnB,IAAA,CAACF,QAAQ;MACPsB,YAAY,EAAEd,gBAAiB;MAC/Be,SAAS,EAAEZ,aAAc;MACzBa,UAAU,EAAEX;IAAe,CAC5B;EAAC,CACE,CAAC;AAEX,CAAC;AAED,IAAMG,MAAM,GAAGnB,UAAU,CAAC4B,MAAM,CAAC;EAC/BR,SAAS,EAAE;IACTS,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,eAAevB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}