{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"mode\", \"dense\", \"disabled\", \"error\", \"multiline\", \"editable\", \"contentStyle\", \"render\", \"theme\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport NativeTextInput from \"react-native-web/dist/exports/TextInput\";\nimport TextInputAffix from \"./Adornment/TextInputAffix\";\nimport TextInputIcon from \"./Adornment/TextInputIcon\";\nimport TextInputFlat from \"./TextInputFlat\";\nimport TextInputOutlined from \"./TextInputOutlined\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nimport { roundLayoutSize } from \"../../utils/roundLayoutSize\";\nvar BLUR_ANIMATION_DURATION = 180;\nvar FOCUS_ANIMATION_DURATION = 150;\nvar DefaultRenderer = function DefaultRenderer(props) {\n  return React.createElement(NativeTextInput, props);\n};\nvar TextInput = forwardRef(function (_ref, ref) {\n  var _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'flat' : _ref$mode,\n    _ref$dense = _ref.dense,\n    dense = _ref$dense === void 0 ? false : _ref$dense,\n    _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    _ref$error = _ref.error,\n    errorProp = _ref$error === void 0 ? false : _ref$error,\n    _ref$multiline = _ref.multiline,\n    multiline = _ref$multiline === void 0 ? false : _ref$multiline,\n    _ref$editable = _ref.editable,\n    editable = _ref$editable === void 0 ? true : _ref$editable,\n    contentStyle = _ref.contentStyle,\n    _ref$render = _ref.render,\n    render = _ref$render === void 0 ? DefaultRenderer : _ref$render,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isControlled = rest.value !== undefined;\n  var validInputValue = isControlled ? rest.value : rest.defaultValue;\n  var _React$useRef = React.useRef(new Animated.Value(validInputValue ? 0 : 1)),\n    labeled = _React$useRef.current;\n  var _React$useRef2 = React.useRef(new Animated.Value(errorProp ? 1 : 0)),\n    error = _React$useRef2.current;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    displayPlaceholder = _React$useState4[0],\n    setDisplayPlaceholder = _React$useState4[1];\n  var _React$useState5 = React.useState(validInputValue),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    uncontrolledValue = _React$useState6[0],\n    setUncontrolledValue = _React$useState6[1];\n  var value = isControlled ? rest.value : uncontrolledValue;\n  var _React$useState7 = React.useState({\n      width: 33\n    }),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    labelTextLayout = _React$useState8[0],\n    setLabelTextLayout = _React$useState8[1];\n  var _React$useState9 = React.useState({\n      width: 65\n    }),\n    _React$useState0 = _slicedToArray(_React$useState9, 2),\n    inputContainerLayout = _React$useState0[0],\n    setInputContainerLayout = _React$useState0[1];\n  var _React$useState1 = React.useState({\n      measured: false,\n      width: 0,\n      height: 0\n    }),\n    _React$useState10 = _slicedToArray(_React$useState1, 2),\n    labelLayout = _React$useState10[0],\n    setLabelLayout = _React$useState10[1];\n  var _React$useState11 = React.useState({\n      width: null,\n      height: null\n    }),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    leftLayout = _React$useState12[0],\n    setLeftLayout = _React$useState12[1];\n  var _React$useState13 = React.useState({\n      width: null,\n      height: null\n    }),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    rightLayout = _React$useState14[0],\n    setRightLayout = _React$useState14[1];\n  var timer = React.useRef();\n  var root = React.useRef();\n  var scale = theme.animation.scale;\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        var _root$current;\n        return (_root$current = root.current) === null || _root$current === void 0 ? void 0 : _root$current.focus();\n      },\n      clear: function clear() {\n        var _root$current2;\n        return (_root$current2 = root.current) === null || _root$current2 === void 0 ? void 0 : _root$current2.clear();\n      },\n      setNativeProps: function setNativeProps(args) {\n        var _root$current3;\n        return (_root$current3 = root.current) === null || _root$current3 === void 0 ? void 0 : _root$current3.setNativeProps(args);\n      },\n      isFocused: function isFocused() {\n        var _root$current4;\n        return ((_root$current4 = root.current) === null || _root$current4 === void 0 ? void 0 : _root$current4.isFocused()) || false;\n      },\n      blur: function blur() {\n        var _root$current5;\n        return (_root$current5 = root.current) === null || _root$current5 === void 0 ? void 0 : _root$current5.blur();\n      },\n      forceFocus: function forceFocus() {\n        var _root$current6;\n        return (_root$current6 = root.current) === null || _root$current6 === void 0 ? void 0 : _root$current6.focus();\n      },\n      setSelection: function setSelection(start, end) {\n        var _root$current7;\n        return (_root$current7 = root.current) === null || _root$current7 === void 0 ? void 0 : _root$current7.setSelection(start, end);\n      }\n    };\n  });\n  React.useEffect(function () {\n    if (errorProp) {\n      Animated.timing(error, {\n        toValue: 1,\n        duration: FOCUS_ANIMATION_DURATION * scale,\n        useNativeDriver: true\n      }).start();\n    } else {\n      {\n        Animated.timing(error, {\n          toValue: 0,\n          duration: BLUR_ANIMATION_DURATION * scale,\n          useNativeDriver: true\n        }).start();\n      }\n    }\n  }, [errorProp, scale, error]);\n  React.useEffect(function () {\n    if (focused || !rest.label) {\n      if (rest.placeholder) {\n        timer.current = setTimeout(function () {\n          return setDisplayPlaceholder(true);\n        }, 50);\n      }\n    } else {\n      setDisplayPlaceholder(false);\n    }\n    return function () {\n      if (timer.current) {\n        clearTimeout(timer.current);\n      }\n    };\n  }, [focused, rest.label, rest.placeholder]);\n  React.useEffect(function () {\n    labeled.stopAnimation();\n    if (value || focused) {\n      Animated.timing(labeled, {\n        toValue: 0,\n        duration: BLUR_ANIMATION_DURATION * scale,\n        useNativeDriver: true\n      }).start();\n    } else {\n      Animated.timing(labeled, {\n        toValue: 1,\n        duration: FOCUS_ANIMATION_DURATION * scale,\n        useNativeDriver: true\n      }).start();\n    }\n  }, [focused, value, labeled, scale]);\n  var onLeftAffixLayoutChange = React.useCallback(function (event) {\n    var height = roundLayoutSize(event.nativeEvent.layout.height);\n    var width = roundLayoutSize(event.nativeEvent.layout.width);\n    if (width !== leftLayout.width || height !== leftLayout.height) {\n      setLeftLayout({\n        width: width,\n        height: height\n      });\n    }\n  }, [leftLayout.height, leftLayout.width]);\n  var onRightAffixLayoutChange = React.useCallback(function (event) {\n    var width = roundLayoutSize(event.nativeEvent.layout.width);\n    var height = roundLayoutSize(event.nativeEvent.layout.height);\n    if (width !== rightLayout.width || height !== rightLayout.height) {\n      setRightLayout({\n        width: width,\n        height: height\n      });\n    }\n  }, [rightLayout.height, rightLayout.width]);\n  var handleFocus = function handleFocus(args) {\n    var _rest$onFocus;\n    if (disabled || !editable) {\n      return;\n    }\n    setFocused(true);\n    (_rest$onFocus = rest.onFocus) === null || _rest$onFocus === void 0 ? void 0 : _rest$onFocus.call(rest, args);\n  };\n  var handleBlur = function handleBlur(args) {\n    var _rest$onBlur;\n    if (!editable) {\n      return;\n    }\n    setFocused(false);\n    (_rest$onBlur = rest.onBlur) === null || _rest$onBlur === void 0 ? void 0 : _rest$onBlur.call(rest, args);\n  };\n  var handleChangeText = function handleChangeText(value) {\n    var _rest$onChangeText;\n    if (!editable || disabled) {\n      return;\n    }\n    if (!isControlled) {\n      setUncontrolledValue(value);\n    }\n    (_rest$onChangeText = rest.onChangeText) === null || _rest$onChangeText === void 0 ? void 0 : _rest$onChangeText.call(rest, value);\n  };\n  var handleLayoutAnimatedText = React.useCallback(function (e) {\n    var width = roundLayoutSize(e.nativeEvent.layout.width);\n    var height = roundLayoutSize(e.nativeEvent.layout.height);\n    if (width !== labelLayout.width || height !== labelLayout.height) {\n      setLabelLayout({\n        width: width,\n        height: height,\n        measured: true\n      });\n    }\n  }, [labelLayout.height, labelLayout.width]);\n  var handleLabelTextLayout = React.useCallback(function (_ref2) {\n    var nativeEvent = _ref2.nativeEvent;\n    setLabelTextLayout({\n      width: nativeEvent.lines.reduce(function (acc, line) {\n        return acc + Math.ceil(line.width);\n      }, 0)\n    });\n  }, []);\n  var handleInputContainerLayout = React.useCallback(function (_ref3) {\n    var layout = _ref3.nativeEvent.layout;\n    setInputContainerLayout({\n      width: layout.width\n    });\n  }, []);\n  var forceFocus = React.useCallback(function () {\n    var _root$current8;\n    return (_root$current8 = root.current) === null || _root$current8 === void 0 ? void 0 : _root$current8.focus();\n  }, []);\n  var _rest$maxFontSizeMult = rest.maxFontSizeMultiplier,\n    maxFontSizeMultiplier = _rest$maxFontSizeMult === void 0 ? 1.5 : _rest$maxFontSizeMult;\n  var scaledLabel = !!(value || focused);\n  if (mode === 'outlined') {\n    return React.createElement(TextInputOutlined, _extends({\n      dense: dense,\n      disabled: disabled,\n      error: errorProp,\n      multiline: multiline,\n      editable: editable,\n      render: render\n    }, rest, {\n      theme: theme,\n      value: value,\n      parentState: {\n        labeled: labeled,\n        error: error,\n        focused: focused,\n        displayPlaceholder: displayPlaceholder,\n        value: value,\n        labelTextLayout: labelTextLayout,\n        labelLayout: labelLayout,\n        leftLayout: leftLayout,\n        rightLayout: rightLayout,\n        inputContainerLayout: inputContainerLayout\n      },\n      innerRef: function innerRef(ref) {\n        root.current = ref;\n      },\n      onFocus: handleFocus,\n      forceFocus: forceFocus,\n      onBlur: handleBlur,\n      onChangeText: handleChangeText,\n      onLayoutAnimatedText: handleLayoutAnimatedText,\n      onInputLayout: handleInputContainerLayout,\n      onLabelTextLayout: handleLabelTextLayout,\n      onLeftAffixLayoutChange: onLeftAffixLayoutChange,\n      onRightAffixLayoutChange: onRightAffixLayoutChange,\n      maxFontSizeMultiplier: maxFontSizeMultiplier,\n      contentStyle: contentStyle,\n      scaledLabel: scaledLabel\n    }));\n  }\n  return React.createElement(TextInputFlat, _extends({\n    dense: dense,\n    disabled: disabled,\n    error: errorProp,\n    multiline: multiline,\n    editable: editable,\n    render: render\n  }, rest, {\n    theme: theme,\n    value: value,\n    parentState: {\n      labeled: labeled,\n      error: error,\n      focused: focused,\n      displayPlaceholder: displayPlaceholder,\n      value: value,\n      labelTextLayout: labelTextLayout,\n      labelLayout: labelLayout,\n      leftLayout: leftLayout,\n      rightLayout: rightLayout,\n      inputContainerLayout: inputContainerLayout\n    },\n    innerRef: function innerRef(ref) {\n      root.current = ref;\n    },\n    onFocus: handleFocus,\n    forceFocus: forceFocus,\n    onBlur: handleBlur,\n    onInputLayout: handleInputContainerLayout,\n    onChangeText: handleChangeText,\n    onLayoutAnimatedText: handleLayoutAnimatedText,\n    onLabelTextLayout: handleLabelTextLayout,\n    onLeftAffixLayoutChange: onLeftAffixLayoutChange,\n    onRightAffixLayoutChange: onRightAffixLayoutChange,\n    maxFontSizeMultiplier: maxFontSizeMultiplier,\n    contentStyle: contentStyle,\n    scaledLabel: scaledLabel\n  }));\n});\nTextInput.Icon = TextInputIcon;\nTextInput.Affix = TextInputAffix;\nexport default TextInput;", "map": {"version": 3, "names": ["React", "Animated", "NativeTextInput", "TextInputAffix", "TextInputIcon", "TextInputFlat", "TextInputOutlined", "useInternalTheme", "forwardRef", "roundLayoutSize", "BLUR_ANIMATION_DURATION", "FOCUS_ANIMATION_DURATION", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "createElement", "TextInput", "_ref", "ref", "_ref$mode", "mode", "_ref$dense", "dense", "_ref$disabled", "disabled", "_ref$error", "error", "errorProp", "_ref$multiline", "multiline", "_ref$editable", "editable", "contentStyle", "_ref$render", "render", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "isControlled", "value", "undefined", "validInputValue", "defaultValue", "_React$useRef", "useRef", "Value", "labeled", "current", "_React$useRef2", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "focused", "setFocused", "_React$useState3", "_React$useState4", "displayPlaceholder", "setDisplayPlaceholder", "_React$useState5", "_React$useState6", "uncontrolledValue", "setUncontrolledValue", "_React$useState7", "width", "_React$useState8", "labelTextLayout", "setLabelTextLayout", "_React$useState9", "_React$useState0", "inputContainerLayout", "setInputContainerLayout", "_React$useState1", "measured", "height", "_React$useState10", "labelLayout", "setLabelLayout", "_React$useState11", "_React$useState12", "leftLayout", "setLeftLayout", "_React$useState13", "_React$useState14", "rightLayout", "setRightLayout", "timer", "root", "scale", "animation", "useImperativeHandle", "focus", "_root$current", "clear", "_root$current2", "setNativeProps", "args", "_root$current3", "isFocused", "_root$current4", "blur", "_root$current5", "forceFocus", "_root$current6", "setSelection", "start", "end", "_root$current7", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "label", "placeholder", "setTimeout", "clearTimeout", "stopAnimation", "onLeftAffixLayoutChange", "useCallback", "event", "nativeEvent", "layout", "onRightAffixLayoutChange", "handleFocus", "_rest$onFocus", "onFocus", "call", "handleBlur", "_rest$onBlur", "onBlur", "handleChangeText", "_rest$onChangeText", "onChangeText", "handleLayoutAnimatedText", "e", "handleLabelTextLayout", "_ref2", "lines", "reduce", "acc", "line", "Math", "ceil", "handleInputContainerLayout", "_ref3", "_root$current8", "_rest$maxFontSizeMult", "maxFontSizeMultiplier", "scaledLabel", "_extends", "parentState", "innerRef", "onLayoutAnimatedText", "onInputLayout", "onLabelTextLayout", "Icon", "Affix"], "sources": ["/workspaces/<PERSON><PERSON><PERSON>-<PERSON>/node_modules/react-native-paper/src/components/TextInput/TextInput.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  LayoutChangeEvent,\n  StyleProp,\n  TextInput as NativeTextInput,\n  TextStyle,\n  ViewStyle,\n  NativeSyntheticEvent,\n  TextLayoutEventData,\n} from 'react-native';\n\nimport TextInputAffix, {\n  Props as TextInputAffixProps,\n} from './Adornment/TextInputAffix';\nimport TextInputIcon, {\n  Props as TextInputIconProps,\n} from './Adornment/TextInputIcon';\nimport TextInputFlat from './TextInputFlat';\nimport TextInputOutlined from './TextInputOutlined';\nimport type { RenderProps, TextInputLabelProp } from './types';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\nimport { roundLayoutSize } from '../../utils/roundLayoutSize';\n\nconst BLUR_ANIMATION_DURATION = 180;\nconst FOCUS_ANIMATION_DURATION = 150;\n\nexport type Props = React.ComponentPropsWithRef<typeof NativeTextInput> & {\n  /**\n   * Mode of the TextInput.\n   * - `flat` - flat input with an underline.\n   * - `outlined` - input with an outline.\n   *\n   * In `outlined` mode, the background color of the label is derived from `colors?.background` in theme or the `backgroundColor` style.\n   * This component render TextInputOutlined or TextInputFlat based on that props\n   */\n  mode?: 'flat' | 'outlined';\n  left?: React.ReactNode;\n  right?: React.ReactNode;\n  /**\n   * If true, user won't be able to interact with the component.\n   */\n  disabled?: boolean;\n  /**\n   * The text or component to use for the floating label.\n   */\n  label?: TextInputLabelProp;\n  /**\n   * Placeholder for the input.\n   */\n  placeholder?: string;\n  /**\n   * Whether to style the TextInput with error style.\n   */\n  error?: boolean;\n  /**\n   * Callback that is called when the text input's text changes. Changed text is passed as an argument to the callback handler.\n   */\n  onChangeText?: Function;\n  /**\n   * Selection color of the input. On iOS, it sets both the selection color and cursor color.\n   * On Android, it sets only the selection color.\n   */\n  selectionColor?: string;\n  /**\n   * @platform Android only\n   * Cursor (or \"caret\") color of the input on Android.\n   * This property has no effect on iOS.\n   */\n  cursorColor?: string;\n  /**\n   * Inactive underline color of the input.\n   */\n  underlineColor?: string;\n  /**\n   * Active underline color of the input.\n   */\n  activeUnderlineColor?: string;\n  /**\n   * Inactive outline color of the input.\n   */\n  outlineColor?: string;\n  /**\n   * Active outline color of the input.\n   */\n  activeOutlineColor?: string;\n  /**\n   * Color of the text in the input.\n   */\n  textColor?: string;\n  /**\n   * Sets min height with densed layout. For `TextInput` in `flat` mode\n   * height is `64dp` or in dense layout - `52dp` with label or `40dp` without label.\n   * For `TextInput` in `outlined` mode\n   * height is `56dp` or in dense layout - `40dp` regardless of label.\n   * When you apply `height` prop in style the `dense` prop affects only `paddingVertical` inside `TextInput`\n   */\n  dense?: boolean;\n  /**\n   * Whether the input can have multiple lines.\n   */\n  multiline?: boolean;\n  /**\n   * @platform Android only\n   * The number of lines to show in the input (Android only).\n   */\n  numberOfLines?: number;\n  /**\n   * Callback that is called when the text input is focused.\n   */\n  onFocus?: (args: any) => void;\n  /**\n   * Callback that is called when the text input is blurred.\n   */\n  onBlur?: (args: any) => void;\n  /**\n   *\n   * Callback to render a custom input component such as `react-native-text-input-mask`\n   * instead of the default `TextInput` component from `react-native`.\n   *\n   * Example:\n   * ```js\n   * <TextInput\n   *   label=\"Phone number\"\n   *   render={props =>\n   *     <TextInputMask\n   *       {...props}\n   *       mask=\"+[00] [000] [000] [000]\"\n   *     />\n   *   }\n   * />\n   * ```\n   */\n  render?: (props: RenderProps) => React.ReactNode;\n  /**\n   * Value of the text input.\n   */\n  value?: string;\n  /**\n   * Pass `fontSize` prop to modify the font size inside `TextInput`.\n   * Pass `height` prop to set `TextInput` height. When `height` is passed,\n   * `dense` prop will affect only input's `paddingVertical`.\n   * Pass `paddingHorizontal` to modify horizontal padding.\n   * This can be used to get MD Guidelines v1 TextInput look.\n   */\n  style?: StyleProp<TextStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n  /**\n   * Pass custom style directly to the input itself.\n   * Overrides input style\n   * Example: `paddingLeft`, `backgroundColor`\n   */\n  contentStyle?: StyleProp<TextStyle>;\n  /**\n   * Pass style to override the default style of outlined wrapper.\n   * Overrides style when mode is set to `outlined`\n   * Example: `borderRadius`, `borderColor`\n   */\n  outlineStyle?: StyleProp<ViewStyle>;\n  /**\n   * Pass style to override the default style of underlined wrapper.\n   * Overrides style when mode is set to `flat`\n   * Example: `borderRadius`, `borderColor`\n   */\n  underlineStyle?: StyleProp<ViewStyle>;\n};\n\ninterface CompoundedComponent\n  extends React.ForwardRefExoticComponent<\n    Props & React.RefAttributes<TextInputHandles>\n  > {\n  Icon: React.FunctionComponent<TextInputIconProps>;\n  Affix: React.FunctionComponent<Partial<TextInputAffixProps>>;\n}\n\ntype TextInputHandles = Pick<\n  NativeTextInput,\n  'focus' | 'clear' | 'blur' | 'isFocused' | 'setNativeProps' | 'setSelection'\n>;\n\nconst DefaultRenderer = (props: RenderProps) => <NativeTextInput {...props} />;\n\n/**\n * A component to allow users to input text.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { TextInput } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [text, setText] = React.useState(\"\");\n *\n *   return (\n *     <TextInput\n *       label=\"Email\"\n *       value={text}\n *       onChangeText={text => setText(text)}\n *     />\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n *\n * @extends TextInput props https://reactnative.dev/docs/textinput#props\n */\nconst TextInput = forwardRef<TextInputHandles, Props>(\n  (\n    {\n      mode = 'flat',\n      dense = false,\n      disabled = false,\n      error: errorProp = false,\n      multiline = false,\n      editable = true,\n      contentStyle,\n      render = DefaultRenderer,\n      theme: themeOverrides,\n      ...rest\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(themeOverrides);\n    const isControlled = rest.value !== undefined;\n    const validInputValue = isControlled ? rest.value : rest.defaultValue;\n\n    const { current: labeled } = React.useRef<Animated.Value>(\n      new Animated.Value(validInputValue ? 0 : 1)\n    );\n    const { current: error } = React.useRef<Animated.Value>(\n      new Animated.Value(errorProp ? 1 : 0)\n    );\n    const [focused, setFocused] = React.useState<boolean>(false);\n    const [displayPlaceholder, setDisplayPlaceholder] =\n      React.useState<boolean>(false);\n    const [uncontrolledValue, setUncontrolledValue] = React.useState<\n      string | undefined\n    >(validInputValue);\n    // Use value from props instead of local state when input is controlled\n    const value = isControlled ? rest.value : uncontrolledValue;\n\n    const [labelTextLayout, setLabelTextLayout] = React.useState({\n      width: 33,\n    });\n\n    const [inputContainerLayout, setInputContainerLayout] = React.useState({\n      width: 65,\n    });\n\n    const [labelLayout, setLabelLayout] = React.useState<{\n      measured: boolean;\n      width: number;\n      height: number;\n    }>({\n      measured: false,\n      width: 0,\n      height: 0,\n    });\n    const [leftLayout, setLeftLayout] = React.useState<{\n      height: number | null;\n      width: number | null;\n    }>({\n      width: null,\n      height: null,\n    });\n    const [rightLayout, setRightLayout] = React.useState<{\n      height: number | null;\n      width: number | null;\n    }>({\n      width: null,\n      height: null,\n    });\n\n    const timer = React.useRef<NodeJS.Timeout | undefined>();\n\n    const root = React.useRef<NativeTextInput | undefined | null>();\n\n    const { scale } = theme.animation;\n\n    React.useImperativeHandle(ref, () => ({\n      focus: () => root.current?.focus(),\n      clear: () => root.current?.clear(),\n      setNativeProps: (args: Object) => root.current?.setNativeProps(args),\n      isFocused: () => root.current?.isFocused() || false,\n      blur: () => root.current?.blur(),\n      forceFocus: () => root.current?.focus(),\n      setSelection: (start: number, end: number) =>\n        root.current?.setSelection(start, end),\n    }));\n\n    React.useEffect(() => {\n      // When the input has an error, we wiggle the label and apply error styles\n      if (errorProp) {\n        // show error\n        Animated.timing(error, {\n          toValue: 1,\n          duration: FOCUS_ANIMATION_DURATION * scale,\n          // To prevent this - https://github.com/callstack/react-native-paper/issues/941\n          useNativeDriver: true,\n        }).start();\n      } else {\n        // hide error\n        {\n          Animated.timing(error, {\n            toValue: 0,\n            duration: BLUR_ANIMATION_DURATION * scale,\n            // To prevent this - https://github.com/callstack/react-native-paper/issues/941\n            useNativeDriver: true,\n          }).start();\n        }\n      }\n    }, [errorProp, scale, error]);\n\n    React.useEffect(() => {\n      // Show placeholder text only if the input is focused, or there's no label\n      // We don't show placeholder if there's a label because the label acts as placeholder\n      // When focused, the label moves up, so we can show a placeholder\n      if (focused || !rest.label) {\n        // If the user wants to use the contextMenu, when changing the placeholder, the contextMenu is closed\n        // This is a workaround to mitigate this behavior in scenarios where the placeholder is not specified.\n        if (rest.placeholder) {\n          // Display placeholder in a delay to offset the label animation\n          // If we show it immediately, they'll overlap and look ugly\n          timer.current = setTimeout(\n            () => setDisplayPlaceholder(true),\n            50\n          ) as unknown as NodeJS.Timeout;\n        }\n      } else {\n        // hidePlaceholder\n        setDisplayPlaceholder(false);\n      }\n\n      return () => {\n        if (timer.current) {\n          clearTimeout(timer.current);\n        }\n      };\n    }, [focused, rest.label, rest.placeholder]);\n\n    React.useEffect(() => {\n      labeled.stopAnimation();\n      // The label should be minimized if the text input is focused, or has text\n      // In minimized mode, the label moves up and becomes small\n      // workaround for animated regression for react native > 0.61\n      // https://github.com/callstack/react-native-paper/pull/1440\n      if (value || focused) {\n        // minimize label\n        Animated.timing(labeled, {\n          toValue: 0,\n          duration: BLUR_ANIMATION_DURATION * scale,\n          // To prevent this - https://github.com/callstack/react-native-paper/issues/941\n          useNativeDriver: true,\n        }).start();\n      } else {\n        // restore label\n        Animated.timing(labeled, {\n          toValue: 1,\n          duration: FOCUS_ANIMATION_DURATION * scale,\n          // To prevent this - https://github.com/callstack/react-native-paper/issues/941\n          useNativeDriver: true,\n        }).start();\n      }\n    }, [focused, value, labeled, scale]);\n\n    const onLeftAffixLayoutChange = React.useCallback(\n      (event: LayoutChangeEvent) => {\n        const height = roundLayoutSize(event.nativeEvent.layout.height);\n        const width = roundLayoutSize(event.nativeEvent.layout.width);\n\n        if (width !== leftLayout.width || height !== leftLayout.height) {\n          setLeftLayout({\n            width,\n            height,\n          });\n        }\n      },\n      [leftLayout.height, leftLayout.width]\n    );\n\n    const onRightAffixLayoutChange = React.useCallback(\n      (event: LayoutChangeEvent) => {\n        const width = roundLayoutSize(event.nativeEvent.layout.width);\n        const height = roundLayoutSize(event.nativeEvent.layout.height);\n\n        if (width !== rightLayout.width || height !== rightLayout.height) {\n          setRightLayout({\n            width,\n            height,\n          });\n        }\n      },\n      [rightLayout.height, rightLayout.width]\n    );\n\n    const handleFocus = (args: any) => {\n      if (disabled || !editable) {\n        return;\n      }\n\n      setFocused(true);\n\n      rest.onFocus?.(args);\n    };\n\n    const handleBlur = (args: Object) => {\n      if (!editable) {\n        return;\n      }\n\n      setFocused(false);\n      rest.onBlur?.(args);\n    };\n\n    const handleChangeText = (value: string) => {\n      if (!editable || disabled) {\n        return;\n      }\n\n      if (!isControlled) {\n        // Keep track of value in local state when input is not controlled\n        setUncontrolledValue(value);\n      }\n      rest.onChangeText?.(value);\n    };\n\n    const handleLayoutAnimatedText = React.useCallback(\n      (e: LayoutChangeEvent) => {\n        const width = roundLayoutSize(e.nativeEvent.layout.width);\n        const height = roundLayoutSize(e.nativeEvent.layout.height);\n\n        if (width !== labelLayout.width || height !== labelLayout.height) {\n          setLabelLayout({\n            width,\n            height,\n            measured: true,\n          });\n        }\n      },\n      [labelLayout.height, labelLayout.width]\n    );\n\n    const handleLabelTextLayout = React.useCallback(\n      ({ nativeEvent }: NativeSyntheticEvent<TextLayoutEventData>) => {\n        setLabelTextLayout({\n          width: nativeEvent.lines.reduce(\n            (acc, line) => acc + Math.ceil(line.width),\n            0\n          ),\n        });\n      },\n      []\n    );\n\n    const handleInputContainerLayout = React.useCallback(\n      ({ nativeEvent: { layout } }: LayoutChangeEvent) => {\n        setInputContainerLayout({\n          width: layout.width,\n        });\n      },\n      []\n    );\n\n    const forceFocus = React.useCallback(() => root.current?.focus(), []);\n\n    const { maxFontSizeMultiplier = 1.5 } = rest;\n\n    const scaledLabel = !!(value || focused);\n\n    if (mode === 'outlined') {\n      return (\n        <TextInputOutlined\n          dense={dense}\n          disabled={disabled}\n          error={errorProp}\n          multiline={multiline}\n          editable={editable}\n          render={render}\n          {...rest}\n          theme={theme}\n          value={value}\n          parentState={{\n            labeled,\n            error,\n            focused,\n            displayPlaceholder,\n            value,\n            labelTextLayout,\n            labelLayout,\n            leftLayout,\n            rightLayout,\n            inputContainerLayout,\n          }}\n          innerRef={(ref) => {\n            root.current = ref;\n          }}\n          onFocus={handleFocus}\n          forceFocus={forceFocus}\n          onBlur={handleBlur}\n          onChangeText={handleChangeText}\n          onLayoutAnimatedText={handleLayoutAnimatedText}\n          onInputLayout={handleInputContainerLayout}\n          onLabelTextLayout={handleLabelTextLayout}\n          onLeftAffixLayoutChange={onLeftAffixLayoutChange}\n          onRightAffixLayoutChange={onRightAffixLayoutChange}\n          maxFontSizeMultiplier={maxFontSizeMultiplier}\n          contentStyle={contentStyle}\n          scaledLabel={scaledLabel}\n        />\n      );\n    }\n\n    return (\n      <TextInputFlat\n        dense={dense}\n        disabled={disabled}\n        error={errorProp}\n        multiline={multiline}\n        editable={editable}\n        render={render}\n        {...rest}\n        theme={theme}\n        value={value}\n        parentState={{\n          labeled,\n          error,\n          focused,\n          displayPlaceholder,\n          value,\n          labelTextLayout,\n          labelLayout,\n          leftLayout,\n          rightLayout,\n          inputContainerLayout,\n        }}\n        innerRef={(ref) => {\n          root.current = ref;\n        }}\n        onFocus={handleFocus}\n        forceFocus={forceFocus}\n        onBlur={handleBlur}\n        onInputLayout={handleInputContainerLayout}\n        onChangeText={handleChangeText}\n        onLayoutAnimatedText={handleLayoutAnimatedText}\n        onLabelTextLayout={handleLabelTextLayout}\n        onLeftAffixLayoutChange={onLeftAffixLayoutChange}\n        onRightAffixLayoutChange={onRightAffixLayoutChange}\n        maxFontSizeMultiplier={maxFontSizeMultiplier}\n        contentStyle={contentStyle}\n        scaledLabel={scaledLabel}\n      />\n    );\n  }\n) as CompoundedComponent;\n// @component ./Adornment/TextInputIcon.tsx\nTextInput.Icon = TextInputIcon;\n\n// @component ./Adornment/TextInputAffix.tsx\n// @ts-ignore Types of property 'theme' are incompatible.\nTextInput.Affix = TextInputAffix;\n\nexport default TextInput;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,eAAA;AAY9B,OAAOC,cAAc;AAGrB,OAAOC,aAAa;AAGpB,OAAOC,aAAa;AACpB,OAAOC,iBAAiB;AAExB,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AACnB,SAASC,eAAe;AAExB,IAAMC,uBAAuB,GAAG,GAAG;AACnC,IAAMC,wBAAwB,GAAG,GAAG;AAkKpC,IAAMC,eAAe,GAAI,SAAnBA,eAAeA,CAAIC,KAAkB;EAAA,OAAKb,KAAA,CAAAc,aAAA,CAACZ,eAAe,EAAKW,KAAQ,CAAC;AAAA;AA2B9E,IAAME,SAAS,GAAGP,UAAU,CAC1B,UAAAQ,IAAA,EAaEC,GAAG,EACA;EAAA,IAAAC,SAAA,GAFKF,IAAA,CAVNG,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,MAAM,GAAAA,SAAA;IAAAE,UAAA,GAUPJ,IAAA,CATNK,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,KAAK,GAAAA,UAAA;IAAAE,aAAA,GASPN,IAAA,CARNO,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,UAAA,GAQVR,IAAA,CAPNS,KAAK;IAAEC,SAAS,GAAAF,UAAA,cAAG,KAAK,GAAAA,UAAA;IAAAG,cAAA,GAOlBX,IAAA,CANNY,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IAAAE,aAAA,GAMXb,IAAA,CALNc,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACfE,YAAY,GAINf,IAAA,CAJNe,YAAY;IAAAC,WAAA,GAINhB,IAAA,CAHNiB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAGpB,eAAe,GAAAoB,WAAA;IACjBE,cAAc,GAEflB,IAAA,CAFNmB,KAAK;IACFC,IAAA,GAAAC,wBAAA,CACGrB,IAAA,EAAAsB,SAAA;EAGR,IAAMH,KAAK,GAAG5B,gBAAgB,CAAC2B,cAAc,CAAC;EAC9C,IAAMK,YAAY,GAAGH,IAAI,CAACI,KAAK,KAAKC,SAAS;EAC7C,IAAMC,eAAe,GAAGH,YAAY,GAAGH,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACO,YAAY;EAErE,IAAAC,aAAA,GAA6B5C,KAAK,CAAC6C,MAAM,CACvC,IAAI5C,QAAQ,CAAC6C,KAAK,CAACJ,eAAe,GAAG,CAAC,GAAG,CAAC,CAC5C,CAAC;IAFgBK,OAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAAC,cAAA,GAA2BjD,KAAK,CAAC6C,MAAM,CACrC,IAAI5C,QAAQ,CAAC6C,KAAK,CAACpB,SAAS,GAAG,CAAC,GAAG,CAAC,CACtC,CAAC;IAFgBD,KAAA,GAAAwB,cAAA,CAATD,OAAO;EAGf,IAAAE,eAAA,GAA8BlD,KAAK,CAACmD,QAAQ,CAAU,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAArDI,OAAO,GAAAF,gBAAA;IAAEG,UAAU,GAAAH,gBAAA;EAC1B,IAAAI,gBAAA,GACExD,KAAK,CAACmD,QAAQ,CAAU,KAAK,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IADzBE,kBAAkB,GAAAD,gBAAA;IAAEE,qBAAqB,GAAAF,gBAAA;EAEhD,IAAAG,gBAAA,GAAkD5D,KAAK,CAACmD,QAAQ,CAE9DT,eAAe,CAAC;IAAAmB,gBAAA,GAAAR,cAAA,CAAAO,gBAAA;IAFXE,iBAAiB,GAAAD,gBAAA;IAAEE,oBAAoB,GAAAF,gBAAA;EAI9C,IAAMrB,KAAK,GAAGD,YAAY,GAAGH,IAAI,CAACI,KAAK,GAAGsB,iBAAiB;EAE3D,IAAAE,gBAAA,GAA8ChE,KAAK,CAACmD,QAAQ,CAAC;MAC3Dc,KAAK,EAAE;IACT,CAAC,CAAC;IAAAC,gBAAA,GAAAb,cAAA,CAAAW,gBAAA;IAFKG,eAAe,GAAAD,gBAAA;IAAEE,kBAAkB,GAAAF,gBAAA;EAI1C,IAAAG,gBAAA,GAAwDrE,KAAK,CAACmD,QAAQ,CAAC;MACrEc,KAAK,EAAE;IACT,CAAC,CAAC;IAAAK,gBAAA,GAAAjB,cAAA,CAAAgB,gBAAA;IAFKE,oBAAoB,GAAAD,gBAAA;IAAEE,uBAAuB,GAAAF,gBAAA;EAIpD,IAAAG,gBAAA,GAAsCzE,KAAK,CAACmD,QAAQ,CAIjD;MACDuB,QAAQ,EAAE,KAAK;MACfT,KAAK,EAAE,CAAC;MACRU,MAAM,EAAE;IACV,CAAC,CAAC;IAAAC,iBAAA,GAAAvB,cAAA,CAAAoB,gBAAA;IARKI,WAAW,GAAAD,iBAAA;IAAEE,cAAc,GAAAF,iBAAA;EASlC,IAAAG,iBAAA,GAAoC/E,KAAK,CAACmD,QAAQ,CAG/C;MACDc,KAAK,EAAE,IAAI;MACXU,MAAM,EAAE;IACV,CAAC,CAAC;IAAAK,iBAAA,GAAA3B,cAAA,CAAA0B,iBAAA;IANKE,UAAU,GAAAD,iBAAA;IAAEE,aAAa,GAAAF,iBAAA;EAOhC,IAAAG,iBAAA,GAAsCnF,KAAK,CAACmD,QAAQ,CAGjD;MACDc,KAAK,EAAE,IAAI;MACXU,MAAM,EAAE;IACV,CAAC,CAAC;IAAAS,iBAAA,GAAA/B,cAAA,CAAA8B,iBAAA;IANKE,WAAW,GAAAD,iBAAA;IAAEE,cAAc,GAAAF,iBAAA;EAQlC,IAAMG,KAAK,GAAGvF,KAAK,CAAC6C,MAAM,CAA6B,CAAC;EAExD,IAAM2C,IAAI,GAAGxF,KAAK,CAAC6C,MAAM,CAAqC,CAAC;EAE/D,IAAQ4C,KAAA,GAAUtD,KAAK,CAACuD,SAAS,CAAzBD,KAAA;EAERzF,KAAK,CAAC2F,mBAAmB,CAAC1E,GAAG,EAAE;IAAA,OAAO;MACpC2E,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAE;QAAA,IAAAC,aAAA;QAAA,QAAAA,aAAA,GAAML,IAAI,CAACxC,OAAO,cAAA6C,aAAA,uBAAZA,aAAA,CAAcD,KAAK,CAAC,CAAC;MAAA;MAClCE,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAE;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GAAMP,IAAI,CAACxC,OAAO,cAAA+C,cAAA,uBAAZA,cAAA,CAAcD,KAAK,CAAC,CAAC;MAAA;MAClCE,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,IAAY;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GAAKV,IAAI,CAACxC,OAAO,cAAAkD,cAAA,uBAAZA,cAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;MAAA;MACpEE,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAE;QAAA,IAAAC,cAAA;QAAA,OAAM,EAAAA,cAAA,GAAAZ,IAAI,CAACxC,OAAO,cAAAoD,cAAA,uBAAZA,cAAA,CAAcD,SAAS,CAAC,CAAC,KAAI,KAAK;MAAA;MACnDE,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAE;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GAAMd,IAAI,CAACxC,OAAO,cAAAsD,cAAA,uBAAZA,cAAA,CAAcD,IAAI,CAAC,CAAC;MAAA;MAChCE,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAE;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GAAMhB,IAAI,CAACxC,OAAO,cAAAwD,cAAA,uBAAZA,cAAA,CAAcZ,KAAK,CAAC,CAAC;MAAA;MACvCa,YAAY,EAAE,SAAdA,YAAYA,CAAGC,KAAa,EAAEC,GAAW;QAAA,IAAAC,cAAA;QAAA,QAAAA,cAAA,GACvCpB,IAAI,CAACxC,OAAO,cAAA4D,cAAA,uBAAZA,cAAA,CAAcH,YAAY,CAACC,KAAK,EAAEC,GAAG,CAAC;MAAA;IAC1C,CAAC;EAAA,CAAC,CAAC;EAEH3G,KAAK,CAAC6G,SAAS,CAAC,YAAM;IAEpB,IAAInF,SAAS,EAAE;MAEbzB,QAAQ,CAAC6G,MAAM,CAACrF,KAAK,EAAE;QACrBsF,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAErG,wBAAwB,GAAG8E,KAAK;QAE1CwB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MAEL;QACEzG,QAAQ,CAAC6G,MAAM,CAACrF,KAAK,EAAE;UACrBsF,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAEtG,uBAAuB,GAAG+E,KAAK;UAEzCwB,eAAe,EAAE;QACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;MACZ;IACF;EACF,CAAC,EAAE,CAAChF,SAAS,EAAE+D,KAAK,EAAEhE,KAAK,CAAC,CAAC;EAE7BzB,KAAK,CAAC6G,SAAS,CAAC,YAAM;IAIpB,IAAIvD,OAAO,IAAI,CAAClB,IAAI,CAAC8E,KAAK,EAAE;MAG1B,IAAI9E,IAAI,CAAC+E,WAAW,EAAE;QAGpB5B,KAAK,CAACvC,OAAO,GAAGoE,UAAU,CACxB;UAAA,OAAMzD,qBAAqB,CAAC,IAAI,CAAC;QAAA,GACjC,EACF,CAA8B;MAChC;IACF,CAAC,MAAM;MAELA,qBAAqB,CAAC,KAAK,CAAC;IAC9B;IAEA,OAAO,YAAM;MACX,IAAI4B,KAAK,CAACvC,OAAO,EAAE;QACjBqE,YAAY,CAAC9B,KAAK,CAACvC,OAAO,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACM,OAAO,EAAElB,IAAI,CAAC8E,KAAK,EAAE9E,IAAI,CAAC+E,WAAW,CAAC,CAAC;EAE3CnH,KAAK,CAAC6G,SAAS,CAAC,YAAM;IACpB9D,OAAO,CAACuE,aAAa,CAAC,CAAC;IAKvB,IAAI9E,KAAK,IAAIc,OAAO,EAAE;MAEpBrD,QAAQ,CAAC6G,MAAM,CAAC/D,OAAO,EAAE;QACvBgE,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAEtG,uBAAuB,GAAG+E,KAAK;QAEzCwB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MAELzG,QAAQ,CAAC6G,MAAM,CAAC/D,OAAO,EAAE;QACvBgE,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAErG,wBAAwB,GAAG8E,KAAK;QAE1CwB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACpD,OAAO,EAAEd,KAAK,EAAEO,OAAO,EAAE0C,KAAK,CAAC,CAAC;EAEpC,IAAM8B,uBAAuB,GAAGvH,KAAK,CAACwH,WAAW,CAC9C,UAAAC,KAAwB,EAAK;IAC5B,IAAM9C,MAAM,GAAGlE,eAAe,CAACgH,KAAK,CAACC,WAAW,CAACC,MAAM,CAAChD,MAAM,CAAC;IAC/D,IAAMV,KAAK,GAAGxD,eAAe,CAACgH,KAAK,CAACC,WAAW,CAACC,MAAM,CAAC1D,KAAK,CAAC;IAE7D,IAAIA,KAAK,KAAKgB,UAAU,CAAChB,KAAK,IAAIU,MAAM,KAAKM,UAAU,CAACN,MAAM,EAAE;MAC9DO,aAAa,CAAC;QACZjB,KAAK,EAALA,KAAK;QACLU,MAAA,EAAAA;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACM,UAAU,CAACN,MAAM,EAAEM,UAAU,CAAChB,KAAK,CACtC,CAAC;EAED,IAAM2D,wBAAwB,GAAG5H,KAAK,CAACwH,WAAW,CAC/C,UAAAC,KAAwB,EAAK;IAC5B,IAAMxD,KAAK,GAAGxD,eAAe,CAACgH,KAAK,CAACC,WAAW,CAACC,MAAM,CAAC1D,KAAK,CAAC;IAC7D,IAAMU,MAAM,GAAGlE,eAAe,CAACgH,KAAK,CAACC,WAAW,CAACC,MAAM,CAAChD,MAAM,CAAC;IAE/D,IAAIV,KAAK,KAAKoB,WAAW,CAACpB,KAAK,IAAIU,MAAM,KAAKU,WAAW,CAACV,MAAM,EAAE;MAChEW,cAAc,CAAC;QACbrB,KAAK,EAALA,KAAK;QACLU,MAAA,EAAAA;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACU,WAAW,CAACV,MAAM,EAAEU,WAAW,CAACpB,KAAK,CACxC,CAAC;EAED,IAAM4D,WAAW,GAAI,SAAfA,WAAWA,CAAI5B,IAAS,EAAK;IAAA,IAAA6B,aAAA;IACjC,IAAIvG,QAAQ,IAAI,CAACO,QAAQ,EAAE;MACzB;IACF;IAEAyB,UAAU,CAAC,IAAI,CAAC;IAEhB,CAAAuE,aAAA,GAAA1F,IAAI,CAAC2F,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAAE,IAAA,CAAA5F,IAAI,EAAW6D,IAAI,CAAC;EACtB,CAAC;EAED,IAAMgC,UAAU,GAAI,SAAdA,UAAUA,CAAIhC,IAAY,EAAK;IAAA,IAAAiC,YAAA;IACnC,IAAI,CAACpG,QAAQ,EAAE;MACb;IACF;IAEAyB,UAAU,CAAC,KAAK,CAAC;IACjB,CAAA2E,YAAA,GAAA9F,IAAI,CAAC+F,MAAM,cAAAD,YAAA,uBAAXA,YAAA,CAAAF,IAAA,CAAA5F,IAAI,EAAU6D,IAAI,CAAC;EACrB,CAAC;EAED,IAAMmC,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAI5F,KAAa,EAAK;IAAA,IAAA6F,kBAAA;IAC1C,IAAI,CAACvG,QAAQ,IAAIP,QAAQ,EAAE;MACzB;IACF;IAEA,IAAI,CAACgB,YAAY,EAAE;MAEjBwB,oBAAoB,CAACvB,KAAK,CAAC;IAC7B;IACA,CAAA6F,kBAAA,GAAAjG,IAAI,CAACkG,YAAY,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAAL,IAAA,CAAA5F,IAAI,EAAgBI,KAAK,CAAC;EAC5B,CAAC;EAED,IAAM+F,wBAAwB,GAAGvI,KAAK,CAACwH,WAAW,CAC/C,UAAAgB,CAAoB,EAAK;IACxB,IAAMvE,KAAK,GAAGxD,eAAe,CAAC+H,CAAC,CAACd,WAAW,CAACC,MAAM,CAAC1D,KAAK,CAAC;IACzD,IAAMU,MAAM,GAAGlE,eAAe,CAAC+H,CAAC,CAACd,WAAW,CAACC,MAAM,CAAChD,MAAM,CAAC;IAE3D,IAAIV,KAAK,KAAKY,WAAW,CAACZ,KAAK,IAAIU,MAAM,KAAKE,WAAW,CAACF,MAAM,EAAE;MAChEG,cAAc,CAAC;QACbb,KAAK,EAALA,KAAK;QACLU,MAAM,EAANA,MAAM;QACND,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACG,WAAW,CAACF,MAAM,EAAEE,WAAW,CAACZ,KAAK,CACxC,CAAC;EAED,IAAMwE,qBAAqB,GAAGzI,KAAK,CAACwH,WAAW,CAC7C,UAAAkB,KAAA,EAAgE;IAAA,IAA7DhB,WAAA,GAAwDgB,KAAA,CAAxDhB,WAAA;IACDtD,kBAAkB,CAAC;MACjBH,KAAK,EAAEyD,WAAW,CAACiB,KAAK,CAACC,MAAM,CAC7B,UAACC,GAAG,EAAEC,IAAI;QAAA,OAAKD,GAAG,GAAGE,IAAI,CAACC,IAAI,CAACF,IAAI,CAAC7E,KAAK,CAAC;MAAA,GAC1C,CACF;IACF,CAAC,CAAC;EACJ,CAAC,EACD,EACF,CAAC;EAED,IAAMgF,0BAA0B,GAAGjJ,KAAK,CAACwH,WAAW,CAClD,UAAA0B,KAAA,EAAoD;IAAA,IAAlCvB,MAAA,GAA6BuB,KAAA,CAA5CxB,WAAW,CAAIC,MAAA;IAChBnD,uBAAuB,CAAC;MACtBP,KAAK,EAAE0D,MAAM,CAAC1D;IAChB,CAAC,CAAC;EACJ,CAAC,EACD,EACF,CAAC;EAED,IAAMsC,UAAU,GAAGvG,KAAK,CAACwH,WAAW,CAAC;IAAA,IAAA2B,cAAA;IAAA,QAAAA,cAAA,GAAM3D,IAAI,CAACxC,OAAO,cAAAmG,cAAA,uBAAZA,cAAA,CAAcvD,KAAK,CAAC,CAAC;EAAA,GAAE,EAAE,CAAC;EAErE,IAAAwD,qBAAA,GAAwChH,IAAI,CAApCiH,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,MAAAA,qBAAA;EAEhC,IAAME,WAAW,GAAG,CAAC,EAAE9G,KAAK,IAAIc,OAAO,CAAC;EAExC,IAAInC,IAAI,KAAK,UAAU,EAAE;IACvB,OACEnB,KAAA,CAAAc,aAAA,CAACR,iBAAiB,EAAAiJ,QAAA;MAChBlI,KAAK,EAAEA,KAAM;MACbE,QAAQ,EAAEA,QAAS;MACnBE,KAAK,EAAEC,SAAU;MACjBE,SAAS,EAAEA,SAAU;MACrBE,QAAQ,EAAEA,QAAS;MACnBG,MAAM,EAAEA;IAAO,GACXG,IAAI;MACRD,KAAK,EAAEA,KAAM;MACbK,KAAK,EAAEA,KAAM;MACbgH,WAAW,EAAE;QACXzG,OAAO,EAAPA,OAAO;QACPtB,KAAK,EAALA,KAAK;QACL6B,OAAO,EAAPA,OAAO;QACPI,kBAAkB,EAAlBA,kBAAkB;QAClBlB,KAAK,EAALA,KAAK;QACL2B,eAAe,EAAfA,eAAe;QACfU,WAAW,EAAXA,WAAW;QACXI,UAAU,EAAVA,UAAU;QACVI,WAAW,EAAXA,WAAW;QACXd,oBAAA,EAAAA;MACF,CAAE;MACFkF,QAAQ,EAAG,SAAXA,QAAQA,CAAGxI,GAAG,EAAK;QACjBuE,IAAI,CAACxC,OAAO,GAAG/B,GAAG;MACpB,CAAE;MACF8G,OAAO,EAAEF,WAAY;MACrBtB,UAAU,EAAEA,UAAW;MACvB4B,MAAM,EAAEF,UAAW;MACnBK,YAAY,EAAEF,gBAAiB;MAC/BsB,oBAAoB,EAAEnB,wBAAyB;MAC/CoB,aAAa,EAAEV,0BAA2B;MAC1CW,iBAAiB,EAAEnB,qBAAsB;MACzClB,uBAAuB,EAAEA,uBAAwB;MACjDK,wBAAwB,EAAEA,wBAAyB;MACnDyB,qBAAqB,EAAEA,qBAAsB;MAC7CtH,YAAY,EAAEA,YAAa;MAC3BuH,WAAW,EAAEA;IAAY,EAC1B,CAAC;EAEN;EAEA,OACEtJ,KAAA,CAAAc,aAAA,CAACT,aAAa,EAAAkJ,QAAA;IACZlI,KAAK,EAAEA,KAAM;IACbE,QAAQ,EAAEA,QAAS;IACnBE,KAAK,EAAEC,SAAU;IACjBE,SAAS,EAAEA,SAAU;IACrBE,QAAQ,EAAEA,QAAS;IACnBG,MAAM,EAAEA;EAAO,GACXG,IAAI;IACRD,KAAK,EAAEA,KAAM;IACbK,KAAK,EAAEA,KAAM;IACbgH,WAAW,EAAE;MACXzG,OAAO,EAAPA,OAAO;MACPtB,KAAK,EAALA,KAAK;MACL6B,OAAO,EAAPA,OAAO;MACPI,kBAAkB,EAAlBA,kBAAkB;MAClBlB,KAAK,EAALA,KAAK;MACL2B,eAAe,EAAfA,eAAe;MACfU,WAAW,EAAXA,WAAW;MACXI,UAAU,EAAVA,UAAU;MACVI,WAAW,EAAXA,WAAW;MACXd,oBAAA,EAAAA;IACF,CAAE;IACFkF,QAAQ,EAAG,SAAXA,QAAQA,CAAGxI,GAAG,EAAK;MACjBuE,IAAI,CAACxC,OAAO,GAAG/B,GAAG;IACpB,CAAE;IACF8G,OAAO,EAAEF,WAAY;IACrBtB,UAAU,EAAEA,UAAW;IACvB4B,MAAM,EAAEF,UAAW;IACnB0B,aAAa,EAAEV,0BAA2B;IAC1CX,YAAY,EAAEF,gBAAiB;IAC/BsB,oBAAoB,EAAEnB,wBAAyB;IAC/CqB,iBAAiB,EAAEnB,qBAAsB;IACzClB,uBAAuB,EAAEA,uBAAwB;IACjDK,wBAAwB,EAAEA,wBAAyB;IACnDyB,qBAAqB,EAAEA,qBAAsB;IAC7CtH,YAAY,EAAEA,YAAa;IAC3BuH,WAAW,EAAEA;EAAY,EAC1B,CAAC;AAEN,CACF,CAAwB;AAExBvI,SAAS,CAAC8I,IAAI,GAAGzJ,aAAa;AAI9BW,SAAS,CAAC+I,KAAK,GAAG3J,cAAc;AAEhC,eAAeY,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}