#!/usr/bin/env python3
"""
USDA Food Data Import Script

This script downloads the USDA SR Legacy dataset, processes it, and creates a SQLite database.
"""

import os
import sys
import csv
import sqlite3
import urllib.request
import zipfile
import tempfile
import shutil
import json
from pathlib import Path

# Configuration
OUTPUT_DIR = Path("../app/src/assets/databases")
USDA_DB_PATH = OUTPUT_DIR / "usda_foods.sqlite"
TEMP_DIR = Path("./temp")

# USDA dataset URL - SR Legacy dataset from April 2018
USDA_DATASET_URL = "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_sr_legacy_food_csv_2018-04.zip"

# Nutrient mapping - maps USDA nutrient IDs to our internal names
NUTRIENT_MAPPING = {
    1003: {"name": "Protein", "unit": "g", "type": "macro"},
    1004: {"name": "Total Fat", "unit": "g", "type": "macro"},
    1005: {"name": "Carbohydrates", "unit": "g", "type": "macro"},
    1008: {"name": "Energy", "unit": "kcal", "type": "macro"},
    1079: {"name": "Fiber", "unit": "g", "type": "macro"},
    2000: {"name": "Total Sugars", "unit": "g", "type": "macro"},
    1087: {"name": "Calcium", "unit": "mg", "type": "mineral"},
    1089: {"name": "Iron", "unit": "mg", "type": "mineral"},
    1090: {"name": "Magnesium", "unit": "mg", "type": "mineral"},
    1091: {"name": "Phosphorus", "unit": "mg", "type": "mineral"},
    1092: {"name": "Potassium", "unit": "mg", "type": "mineral"},
    1093: {"name": "Sodium", "unit": "mg", "type": "mineral"},
    1095: {"name": "Zinc", "unit": "mg", "type": "mineral"},
    1106: {"name": "Vitamin A", "unit": "µg", "type": "vitamin"},
    1162: {"name": "Vitamin C", "unit": "mg", "type": "vitamin"},
    1175: {"name": "Vitamin B6", "unit": "mg", "type": "vitamin"},
    1178: {"name": "Vitamin B12", "unit": "µg", "type": "vitamin"},
    1180: {"name": "Vitamin D", "unit": "µg", "type": "vitamin"},
    1185: {"name": "Vitamin E", "unit": "mg", "type": "vitamin"},
    1190: {"name": "Folate", "unit": "µg", "type": "vitamin"},
}

# Common allergens to look for in ingredients
COMMON_ALLERGENS = [
    {"name": "Milk", "keywords": ["milk", "dairy", "lactose", "whey", "casein", "butter", "cream", "cheese", "yogurt"]},
    {"name": "Eggs", "keywords": ["egg", "albumin", "lysozyme", "globulin", "ovomucin", "ovalbumin", "ovotransferrin"]},
    {"name": "Fish", "keywords": ["fish", "cod", "salmon", "tuna", "tilapia", "halibut", "anchovy", "bass"]},
    {"name": "Shellfish", "keywords": ["shellfish", "crab", "lobster", "shrimp", "prawn", "crayfish", "clam", "mussel", "oyster", "scallop"]},
    {"name": "Tree Nuts", "keywords": ["almond", "hazelnut", "walnut", "cashew", "pecan", "brazil nut", "pistachio", "macadamia"]},
    {"name": "Peanuts", "keywords": ["peanut", "arachis", "groundnut"]},
    {"name": "Wheat", "keywords": ["wheat", "flour", "bread", "bran", "bulgur", "durum", "gluten", "semolina", "spelt"]},
    {"name": "Soybeans", "keywords": ["soy", "soya", "tofu", "edamame", "miso", "tempeh", "textured vegetable protein", "tvp"]},
    {"name": "Sesame", "keywords": ["sesame", "tahini", "sesamol", "gingelly"]},
    {"name": "Sulfites", "keywords": ["sulfite", "sulphite", "sulfur dioxide", "so2", "e220", "e221", "e222", "e223", "e224", "e225", "e226", "e227", "e228"]},
]

def create_database():
    """Create a new SQLite database file."""
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Delete existing database file if it exists
    if os.path.exists(USDA_DB_PATH):
        os.remove(USDA_DB_PATH)

    # Create new database
    conn = sqlite3.connect(USDA_DB_PATH)
    cursor = conn.cursor()

    # Create tables
    cursor.execute('''
    CREATE TABLE foods (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      brand_name TEXT,
      serving_size REAL DEFAULT 100,
      serving_unit TEXT DEFAULT 'g',
      barcode TEXT,
      source TEXT,
      source_id TEXT
    )
    ''')

    cursor.execute('''
    CREATE TABLE nutrients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      amount REAL NOT NULL,
      unit TEXT NOT NULL,
      type TEXT NOT NULL,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
    ''')

    cursor.execute('''
    CREATE TABLE ingredients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      amount REAL,
      unit TEXT,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
    ''')

    cursor.execute('''
    CREATE TABLE allergens (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
    ''')

    # Create indexes
    cursor.execute('CREATE INDEX idx_foods_name ON foods (name)')
    cursor.execute('CREATE INDEX idx_foods_source ON foods (source)')
    cursor.execute('CREATE INDEX idx_nutrients_food_id ON nutrients (food_id)')
    cursor.execute('CREATE INDEX idx_ingredients_food_id ON ingredients (food_id)')
    cursor.execute('CREATE INDEX idx_allergens_food_id ON allergens (food_id)')

    conn.commit()
    return conn

def download_dataset(url, output_dir):
    """Download the USDA dataset."""
    os.makedirs(output_dir, exist_ok=True)
    zip_path = os.path.join(output_dir, "usda_dataset.zip")

    print(f"Downloading USDA dataset from {url}...")
    urllib.request.urlretrieve(url, zip_path)
    print(f"Dataset downloaded to {zip_path}")

    return zip_path

def extract_dataset(zip_path, output_dir):
    """Extract the USDA dataset."""
    os.makedirs(output_dir, exist_ok=True)

    print(f"Extracting dataset to {output_dir}...")
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(output_dir)
    print("Dataset extracted")

def import_usda_data(conn, data_dir):
    """Import USDA data into the SQLite database."""
    cursor = conn.cursor()

    # Parse food data
    food_file = os.path.join(data_dir, "food.csv")
    food_nutrient_file = os.path.join(data_dir, "food_nutrient.csv")
    nutrient_file = os.path.join(data_dir, "nutrient.csv")

    # Create nutrient lookup
    nutrient_lookup = {}
    with open(nutrient_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            nutrient_lookup[row['id']] = {
                'id': row['id'],
                'name': row['name'],
                'unit': row['unit_name'],
            }

    # Process each food
    food_count = 0
    nutrient_count = 0

    with open(food_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                # Create food object
                food_data = {
                    'name': row['description'],
                    'description': row['description'],
                    'brand_name': '',
                    'serving_size': 100,  # Default to 100g
                    'serving_unit': 'g',
                    'barcode': '',
                    'source': 'USDA SR Legacy',
                    'source_id': row['fdc_id'],
                }

                # Save food to database
                cursor.execute(
                    '''INSERT INTO foods (name, description, brand_name, serving_size, serving_unit, barcode, source, source_id)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
                    (
                        food_data['name'],
                        food_data['description'],
                        food_data['brand_name'],
                        food_data['serving_size'],
                        food_data['serving_unit'],
                        food_data['barcode'],
                        food_data['source'],
                        food_data['source_id']
                    )
                )

                food_id = cursor.lastrowid
                food_count += 1

                # Process nutrients for this food
                with open(food_nutrient_file, 'r', encoding='utf-8') as fn_file:
                    fn_reader = csv.DictReader(fn_file)
                    for fn_row in fn_reader:
                        if fn_row['fdc_id'] == row['fdc_id']:
                            nutrient_id = fn_row['nutrient_id']

                            # Skip if not in our mapping
                            if int(nutrient_id) not in NUTRIENT_MAPPING:
                                continue

                            mapping = NUTRIENT_MAPPING[int(nutrient_id)]

                            nutrient_data = {
                                'food_id': food_id,
                                'name': mapping['name'],
                                'amount': float(fn_row['amount']) if fn_row['amount'] else 0,
                                'unit': mapping['unit'],
                                'type': mapping['type'],
                            }

                            # Save nutrient to database
                            cursor.execute(
                                '''INSERT INTO nutrients (food_id, name, amount, unit, type)
                                   VALUES (?, ?, ?, ?, ?)''',
                                (
                                    nutrient_data['food_id'],
                                    nutrient_data['name'],
                                    nutrient_data['amount'],
                                    nutrient_data['unit'],
                                    nutrient_data['type']
                                )
                            )

                            nutrient_count += 1

                # Commit every 100 foods
                if food_count % 100 == 0:
                    conn.commit()
                    print(f"Imported {food_count} foods, {nutrient_count} nutrients...")

            except Exception as e:
                print(f"Error importing food {row.get('fdc_id', 'unknown')}: {e}")

    # Final commit
    conn.commit()
    print(f"Import complete: {food_count} foods, {nutrient_count} nutrients")

def main():
    """Main function."""
    try:
        # Create database
        conn = create_database()

        # Create temp directory
        os.makedirs(TEMP_DIR, exist_ok=True)

        # Download dataset
        zip_path = download_dataset(USDA_DATASET_URL, TEMP_DIR)

        # Extract dataset
        extract_dir = os.path.join(TEMP_DIR, "extracted")
        extract_dataset(zip_path, extract_dir)

        # Import data
        import_usda_data(conn, extract_dir)

        # Close database
        conn.close()

        # Clean up
        shutil.rmtree(TEMP_DIR)

        print(f"USDA database generated at {USDA_DB_PATH}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
