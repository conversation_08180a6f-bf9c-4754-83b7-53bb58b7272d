#!/usr/bin/env python3
"""
FoodB Data Import Script

This script downloads the FoodB dataset, processes it, and creates a SQLite database.
"""

import os
import sys
import json
import sqlite3
import urllib.request
import zipfile
import tempfile
import shutil
from pathlib import Path

# Configuration
OUTPUT_DIR = Path("../app/src/assets/databases")
FOODB_DB_PATH = OUTPUT_DIR / "foodb_foods.sqlite"
TEMP_DIR = Path("./temp")

# FoodB dataset URL
FOODB_DATASET_URL = "https://foodb.ca/public/system/downloads/foodb_2020_04_07_json.zip"

# Nutrient mapping - maps FoodB nutrient names to our internal names
NUTRIENT_MAPPING = {
    "Protein": {"name": "Protein", "unit": "g", "type": "macro"},
    "Total lipid (fat)": {"name": "Total Fat", "unit": "g", "type": "macro"},
    "Carbohydrate, by difference": {"name": "Carbohydrates", "unit": "g", "type": "macro"},
    "Energy": {"name": "Energy", "unit": "kcal", "type": "macro"},
    "Fiber, total dietary": {"name": "Fiber", "unit": "g", "type": "macro"},
    "Sugars, total": {"name": "Total Sugars", "unit": "g", "type": "macro"},
    "Calcium, Ca": {"name": "Calcium", "unit": "mg", "type": "mineral"},
    "Iron, Fe": {"name": "Iron", "unit": "mg", "type": "mineral"},
    "Magnesium, Mg": {"name": "Magnesium", "unit": "mg", "type": "mineral"},
    "Phosphorus, P": {"name": "Phosphorus", "unit": "mg", "type": "mineral"},
    "Potassium, K": {"name": "Potassium", "unit": "mg", "type": "mineral"},
    "Sodium, Na": {"name": "Sodium", "unit": "mg", "type": "mineral"},
    "Zinc, Zn": {"name": "Zinc", "unit": "mg", "type": "mineral"},
    "Vitamin A, RAE": {"name": "Vitamin A", "unit": "µg", "type": "vitamin"},
    "Vitamin C, total ascorbic acid": {"name": "Vitamin C", "unit": "mg", "type": "vitamin"},
    "Vitamin B-6": {"name": "Vitamin B6", "unit": "mg", "type": "vitamin"},
    "Vitamin B-12": {"name": "Vitamin B12", "unit": "µg", "type": "vitamin"},
    "Vitamin D (D2 + D3)": {"name": "Vitamin D", "unit": "µg", "type": "vitamin"},
    "Vitamin E (alpha-tocopherol)": {"name": "Vitamin E", "unit": "mg", "type": "vitamin"},
    "Folate, total": {"name": "Folate", "unit": "µg", "type": "vitamin"},
}

# Common allergens to look for in ingredients
COMMON_ALLERGENS = [
    {"name": "Milk", "keywords": ["milk", "dairy", "lactose", "whey", "casein", "butter", "cream", "cheese", "yogurt"]},
    {"name": "Eggs", "keywords": ["egg", "albumin", "lysozyme", "globulin", "ovomucin", "ovalbumin", "ovotransferrin"]},
    {"name": "Fish", "keywords": ["fish", "cod", "salmon", "tuna", "tilapia", "halibut", "anchovy", "bass"]},
    {"name": "Shellfish", "keywords": ["shellfish", "crab", "lobster", "shrimp", "prawn", "crayfish", "clam", "mussel", "oyster", "scallop"]},
    {"name": "Tree Nuts", "keywords": ["almond", "hazelnut", "walnut", "cashew", "pecan", "brazil nut", "pistachio", "macadamia"]},
    {"name": "Peanuts", "keywords": ["peanut", "arachis", "groundnut"]},
    {"name": "Wheat", "keywords": ["wheat", "flour", "bread", "bran", "bulgur", "durum", "gluten", "semolina", "spelt"]},
    {"name": "Soybeans", "keywords": ["soy", "soya", "tofu", "edamame", "miso", "tempeh", "textured vegetable protein", "tvp"]},
    {"name": "Sesame", "keywords": ["sesame", "tahini", "sesamol", "gingelly"]},
    {"name": "Sulfites", "keywords": ["sulfite", "sulphite", "sulfur dioxide", "so2", "e220", "e221", "e222", "e223", "e224", "e225", "e226", "e227", "e228"]},
]

def create_database():
    """Create a new SQLite database file."""
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Delete existing database file if it exists
    if os.path.exists(FOODB_DB_PATH):
        os.remove(FOODB_DB_PATH)
    
    # Create new database
    conn = sqlite3.connect(FOODB_DB_PATH)
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute('''
    CREATE TABLE foods (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      brand_name TEXT,
      serving_size REAL DEFAULT 100,
      serving_unit TEXT DEFAULT 'g',
      barcode TEXT,
      source TEXT,
      source_id TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE nutrients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      amount REAL NOT NULL,
      unit TEXT NOT NULL,
      type TEXT NOT NULL,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE ingredients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      amount REAL,
      unit TEXT,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE allergens (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
    ''')
    
    # Create indexes
    cursor.execute('CREATE INDEX idx_foods_name ON foods (name)')
    cursor.execute('CREATE INDEX idx_foods_source ON foods (source)')
    cursor.execute('CREATE INDEX idx_nutrients_food_id ON nutrients (food_id)')
    cursor.execute('CREATE INDEX idx_ingredients_food_id ON ingredients (food_id)')
    cursor.execute('CREATE INDEX idx_allergens_food_id ON allergens (food_id)')
    
    conn.commit()
    return conn

def download_dataset(url, output_dir):
    """Download the FoodB dataset."""
    os.makedirs(output_dir, exist_ok=True)
    zip_path = os.path.join(output_dir, "foodb_dataset.zip")
    
    print(f"Downloading FoodB dataset from {url}...")
    urllib.request.urlretrieve(url, zip_path)
    print(f"Dataset downloaded to {zip_path}")
    
    return zip_path

def extract_dataset(zip_path, output_dir):
    """Extract the FoodB dataset."""
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Extracting dataset to {output_dir}...")
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(output_dir)
    print("Dataset extracted")

def import_foodb_data(conn, data_dir):
    """Import FoodB data into the SQLite database."""
    cursor = conn.cursor()
    
    # Parse food data
    food_file = os.path.join(data_dir, "Food.json")
    nutrient_file = os.path.join(data_dir, "Nutrient.json")
    compound_file = os.path.join(data_dir, "Compound.json")
    
    # Load data
    with open(food_file, 'r', encoding='utf-8') as f:
        foods_data = json.load(f)
    
    with open(nutrient_file, 'r', encoding='utf-8') as f:
        nutrients_data = json.load(f)
    
    with open(compound_file, 'r', encoding='utf-8') as f:
        compounds_data = json.load(f)
    
    # Create lookup maps
    nutrient_map = {}
    for nutrient in nutrients_data:
        nutrient_map[nutrient['id']] = nutrient
    
    compound_map = {}
    for compound in compounds_data:
        compound_map[compound['id']] = compound
    
    # Process each food
    food_count = 0
    nutrient_count = 0
    ingredient_count = 0
    allergen_count = 0
    
    for food in foods_data:
        try:
            # Skip foods without a name
            if not food.get('name'):
                continue
            
            # Create food object
            food_data = {
                'name': food['name'],
                'description': food.get('description', food['name']),
                'brand_name': '',
                'serving_size': 100,  # Default to 100g
                'serving_unit': 'g',
                'barcode': '',
                'source': 'FoodB',
                'source_id': str(food['id']),
            }
            
            # Save food to database
            cursor.execute(
                '''INSERT INTO foods (name, description, brand_name, serving_size, serving_unit, barcode, source, source_id)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
                (
                    food_data['name'],
                    food_data['description'],
                    food_data['brand_name'],
                    food_data['serving_size'],
                    food_data['serving_unit'],
                    food_data['barcode'],
                    food_data['source'],
                    food_data['source_id']
                )
            )
            
            food_id = cursor.lastrowid
            food_count += 1
            
            # Process nutrients for this food
            if 'nutrients' in food and isinstance(food['nutrients'], list):
                for food_nutrient in food['nutrients']:
                    nutrient = nutrient_map.get(food_nutrient.get('nutrient_id'))
                    if not nutrient:
                        continue
                    
                    # Skip if not in our mapping
                    mapped_nutrient = NUTRIENT_MAPPING.get(nutrient.get('name'))
                    if not mapped_nutrient:
                        continue
                    
                    nutrient_data = {
                        'food_id': food_id,
                        'name': mapped_nutrient['name'],
                        'amount': float(food_nutrient.get('amount', 0)) or 0,
                        'unit': mapped_nutrient['unit'],
                        'type': mapped_nutrient['type'],
                    }
                    
                    # Save nutrient to database
                    cursor.execute(
                        '''INSERT INTO nutrients (food_id, name, amount, unit, type)
                           VALUES (?, ?, ?, ?, ?)''',
                        (
                            nutrient_data['food_id'],
                            nutrient_data['name'],
                            nutrient_data['amount'],
                            nutrient_data['unit'],
                            nutrient_data['type']
                        )
                    )
                    
                    nutrient_count += 1
            
            # Process compounds as ingredients
            if 'compounds' in food and isinstance(food['compounds'], list):
                for food_compound in food['compounds']:
                    compound = compound_map.get(food_compound.get('compound_id'))
                    if not compound or not compound.get('name'):
                        continue
                    
                    ingredient_data = {
                        'food_id': food_id,
                        'name': compound['name'],
                        'amount': float(food_compound.get('amount', 0)) or 0,
                        'unit': food_compound.get('unit', 'mg'),
                    }
                    
                    # Save ingredient to database
                    cursor.execute(
                        '''INSERT INTO ingredients (food_id, name, amount, unit)
                           VALUES (?, ?, ?, ?)''',
                        (
                            ingredient_data['food_id'],
                            ingredient_data['name'],
                            ingredient_data['amount'],
                            ingredient_data['unit']
                        )
                    )
                    
                    ingredient_count += 1
                    
                    # Check for allergens
                    for allergen in COMMON_ALLERGENS:
                        has_allergen = any(
                            keyword.lower() in compound['name'].lower()
                            for keyword in allergen['keywords']
                        )
                        
                        if has_allergen:
                            # Check if this allergen is already added for this food
                            cursor.execute(
                                '''SELECT COUNT(*) FROM allergens
                                   WHERE food_id = ? AND name = ?''',
                                (food_id, allergen['name'])
                            )
                            
                            if cursor.fetchone()[0] == 0:
                                cursor.execute(
                                    '''INSERT INTO allergens (food_id, name)
                                       VALUES (?, ?)''',
                                    (food_id, allergen['name'])
                                )
                                
                                allergen_count += 1
            
            # Commit every 100 foods
            if food_count % 100 == 0:
                conn.commit()
                print(f"Imported {food_count} foods, {nutrient_count} nutrients, {ingredient_count} ingredients, {allergen_count} allergens...")
        
        except Exception as e:
            print(f"Error importing food {food.get('id', 'unknown')}: {e}")
    
    # Final commit
    conn.commit()
    print(f"Import complete: {food_count} foods, {nutrient_count} nutrients, {ingredient_count} ingredients, {allergen_count} allergens")

def main():
    """Main function."""
    try:
        # Create database
        conn = create_database()
        
        # Create temp directory
        os.makedirs(TEMP_DIR, exist_ok=True)
        
        # Download dataset
        zip_path = download_dataset(FOODB_DATASET_URL, TEMP_DIR)
        
        # Extract dataset
        extract_dir = os.path.join(TEMP_DIR, "extracted")
        extract_dataset(zip_path, extract_dir)
        
        # Import data
        import_foodb_data(conn, extract_dir)
        
        # Close database
        conn.close()
        
        # Clean up
        shutil.rmtree(TEMP_DIR)
        
        print(f"FoodB database generated at {FOODB_DB_PATH}")
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
