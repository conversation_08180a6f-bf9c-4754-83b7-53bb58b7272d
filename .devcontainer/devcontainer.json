{"name": "Znueni-Zaehler Development", "image": "mcr.microsoft.com/devcontainers/javascript-node:20", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "customizations": {"vscode": {"extensions": ["vscode.typescript-language-features", "vscode.json-language-features", "vscode.emmet", "vscode.git"], "settings": {"editor.formatOnSave": true, "typescript.preferences.includePackageJsonAutoImports": "on", "emmet.includeLanguages": {"javascript": "javascriptreact"}}}}, "forwardPorts": [19000, 19001, 19002, 8081], "portsAttributes": {"19000": {"label": "Expo Dev Tools", "onAutoForward": "notify"}, "19001": {"label": "Expo Packager", "onAutoForward": "silent"}, "19002": {"label": "Expo Tunnel", "onAutoForward": "silent"}, "8081": {"label": "Metro Bundler", "onAutoForward": "silent"}}, "postCreateCommand": "npm install && npx expo install --fix", "remoteUser": "node"}