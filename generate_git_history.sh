#!/bin/bash
# Generate a visualization of the git history

# Get the git log in JSON format
git log --all --decorate --oneline --graph > git_history.txt

# Create an HTML file with the git history
cat > git_history.html << 'EOL'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git History - Znueni-Z<PERSON>hler</title>
    <style>
        body {
            font-family: monospace;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        pre {
            white-space: pre;
            overflow-x: auto;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .main-branch {
            color: #F44D27;
            font-weight: bold;
        }
        .feature-branch {
            color: #4584B6;
            font-weight: bold;
        }
        .head {
            color: #2B9348;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Git History - Znueni-Zaehler</h1>
        <pre id="git-history">
EOL

# Append the git history to the HTML file
cat git_history.txt >> git_history.html

# Close the HTML file
cat >> git_history.html << 'EOL'
        </pre>
    </div>
    <script>
        // Highlight branches and HEAD
        const gitHistory = document.getElementById('git-history');
        let content = gitHistory.innerHTML;
        
        // Highlight main branch
        content = content.replace(/\(origin\/main\)/g, '<span class="main-branch">$&</span>');
        
        // Highlight feature branches
        content = content.replace(/\(origin\/feature\/[^)]+\)/g, '<span class="feature-branch">$&</span>');
        
        // Highlight HEAD
        content = content.replace(/\(HEAD[^)]*\)/g, '<span class="head">$&</span>');
        
        gitHistory.innerHTML = content;
    </script>
</body>
</html>
EOL

echo "Git history visualization generated in git_history.html"
