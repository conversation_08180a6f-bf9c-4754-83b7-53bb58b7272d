#!/bin/bash

# Z<PERSON><PERSON><PERSON><PERSON><PERSON>hler Documentation Deployment Script
# Builds and deploys documentation to various platforms

set -e  # Exit on any error

# Configuration
PROJECT_NAME="Znüni<PERSON>ähler"
DOCS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$DOCS_DIR/_build"
DEPLOY_DIR="$BUILD_DIR/deploy"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
$PROJECT_NAME Documentation Deployment Script

Usage: $0 [OPTIONS] [TARGET]

TARGETS:
    html        Build HTML documentation (default)
    pdf         Build PDF documentation
    epub        Build EPUB documentation
    all         Build all formats
    deploy      Build and deploy to GitHub Pages
    clean       Clean build directory

OPTIONS:
    -h, --help      Show this help message
    -v, --verbose   Enable verbose output
    -c, --clean     Clean before building
    -l, --live      Start live reload server
    --check-links   Check for broken links
    --spell-check   Run spell checker
    --validate      Validate documentation structure

EXAMPLES:
    $0 html                 # Build HTML documentation
    $0 --clean all          # Clean and build all formats
    $0 deploy               # Build and deploy to GitHub Pages
    $0 --live               # Start development server with live reload

EOF
}

# Parse command line arguments
VERBOSE=false
CLEAN_FIRST=false
LIVE_RELOAD=false
CHECK_LINKS=false
SPELL_CHECK=false
VALIDATE=false
TARGET="html"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--clean)
            CLEAN_FIRST=true
            shift
            ;;
        -l|--live)
            LIVE_RELOAD=true
            TARGET="livehtml"
            shift
            ;;
        --check-links)
            CHECK_LINKS=true
            shift
            ;;
        --spell-check)
            SPELL_CHECK=true
            shift
            ;;
        --validate)
            VALIDATE=true
            shift
            ;;
        html|pdf|epub|all|deploy|clean|livehtml)
            TARGET=$1
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Set verbose mode
if [ "$VERBOSE" = true ]; then
    set -x
fi

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check if Python is installed
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check if pip is installed
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 is required but not installed"
        exit 1
    fi
    
    # Check if Sphinx is installed
    if ! python3 -c "import sphinx" &> /dev/null; then
        log_warning "Sphinx not found. Installing documentation dependencies..."
        pip3 install -r "$DOCS_DIR/requirements-docs.txt"
    fi
    
    log_success "Dependencies check completed"
}

# Clean build directory
clean_build() {
    log_info "Cleaning build directory..."
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        log_success "Build directory cleaned"
    else
        log_info "Build directory already clean"
    fi
}

# Validate documentation
validate_docs() {
    log_info "Validating documentation structure..."
    cd "$DOCS_DIR"
    
    if sphinx-build -b dummy . "$BUILD_DIR/dummy" -W; then
        log_success "Documentation structure is valid"
    else
        log_error "Documentation validation failed"
        exit 1
    fi
}

# Check for broken links
check_links() {
    log_info "Checking for broken links..."
    cd "$DOCS_DIR"
    
    if sphinx-build -b linkcheck . "$BUILD_DIR/linkcheck"; then
        log_success "Link check completed"
    else
        log_warning "Some links may be broken. Check $BUILD_DIR/linkcheck/output.txt"
    fi
}

# Run spell checker
spell_check() {
    log_info "Running spell checker..."
    cd "$DOCS_DIR"
    
    if command -v pyspelling &> /dev/null; then
        if pyspelling -c .pyspelling.yml; then
            log_success "Spell check completed"
        else
            log_warning "Spelling errors found"
        fi
    else
        log_warning "pyspelling not installed. Skipping spell check."
    fi
}

# Build HTML documentation
build_html() {
    log_info "Building HTML documentation..."
    cd "$DOCS_DIR"
    
    if sphinx-build -b html . "$BUILD_DIR/html"; then
        log_success "HTML documentation built successfully"
        log_info "Documentation available at: $BUILD_DIR/html/index.html"
    else
        log_error "HTML build failed"
        exit 1
    fi
}

# Build PDF documentation
build_pdf() {
    log_info "Building PDF documentation..."
    cd "$DOCS_DIR"
    
    # First build LaTeX
    if sphinx-build -b latex . "$BUILD_DIR/latex"; then
        log_info "LaTeX files generated"
        
        # Then build PDF
        cd "$BUILD_DIR/latex"
        if make all-pdf; then
            log_success "PDF documentation built successfully"
            log_info "PDF available at: $BUILD_DIR/latex/ZnuniZaehler.pdf"
        else
            log_error "PDF build failed"
            exit 1
        fi
    else
        log_error "LaTeX build failed"
        exit 1
    fi
}

# Build EPUB documentation
build_epub() {
    log_info "Building EPUB documentation..."
    cd "$DOCS_DIR"
    
    if sphinx-build -b epub . "$BUILD_DIR/epub"; then
        log_success "EPUB documentation built successfully"
        log_info "EPUB available at: $BUILD_DIR/epub/ZnuniZaehler.epub"
    else
        log_error "EPUB build failed"
        exit 1
    fi
}

# Build user manual
build_user_manual() {
    log_info "Building user manual PDF..."
    cd "$DOCS_DIR"
    
    # Build user manual as standalone PDF
    if sphinx-build -b latex -D master_doc=user-manual . "$BUILD_DIR/user-manual"; then
        cd "$BUILD_DIR/user-manual"
        if make all-pdf; then
            log_success "User manual PDF built successfully"
            log_info "User manual available at: $BUILD_DIR/user-manual/user-manual.pdf"
        else
            log_error "User manual PDF build failed"
            exit 1
        fi
    else
        log_error "User manual LaTeX build failed"
        exit 1
    fi
}

# Start live reload server
start_live_reload() {
    log_info "Starting live reload server..."
    cd "$DOCS_DIR"
    
    log_info "Documentation will be available at: http://localhost:8000"
    log_info "Press Ctrl+C to stop the server"
    
    sphinx-autobuild . "$BUILD_DIR/html" --host 0.0.0.0 --port 8000
}

# Deploy to GitHub Pages
deploy_github_pages() {
    log_info "Deploying to GitHub Pages..."
    
    # First build HTML
    build_html
    
    # Prepare deployment
    cd "$BUILD_DIR/html"
    
    # Add .nojekyll file to disable Jekyll processing
    touch .nojekyll
    
    # Add CNAME file for custom domain (if configured)
    if [ -n "$DOCS_DOMAIN" ]; then
        echo "$DOCS_DOMAIN" > CNAME
        log_info "Added CNAME for domain: $DOCS_DOMAIN"
    fi
    
    # Initialize git repository
    git init
    git add -A
    git commit -m "Deploy documentation - $(date)"
    
    # Deploy to gh-pages branch
    if [ -n "$GITHUB_REPO" ]; then
        git push -f "$GITHUB_REPO" master:gh-pages
        log_success "Documentation deployed to GitHub Pages"
    else
        log_warning "GITHUB_REPO not set. Manual deployment required."
        log_info "Run: git push -f <your-repo-url> master:gh-pages"
    fi
}

# Generate documentation statistics
generate_stats() {
    log_info "Generating documentation statistics..."
    
    echo "=== Documentation Statistics ==="
    echo "RST files: $(find "$DOCS_DIR" -name "*.rst" | wc -l)"
    echo "Markdown files: $(find "$DOCS_DIR" -name "*.md" | wc -l)"
    echo "Total lines in RST: $(find "$DOCS_DIR" -name "*.rst" -exec wc -l {} + | tail -1 | awk '{print $1}')"
    echo "Total lines in MD: $(find "$DOCS_DIR" -name "*.md" -exec wc -l {} + | tail -1 | awk '{print $1}')"
    echo "Images: $(find "$DOCS_DIR" -name "*.png" -o -name "*.jpg" -o -name "*.svg" | wc -l)"
    echo "Build size: $(du -sh "$BUILD_DIR" 2>/dev/null | cut -f1 || echo "No build")"
}

# Main execution
main() {
    log_info "Starting $PROJECT_NAME documentation build..."
    log_info "Target: $TARGET"
    
    # Change to docs directory
    cd "$DOCS_DIR"
    
    # Check dependencies
    check_dependencies
    
    # Clean if requested
    if [ "$CLEAN_FIRST" = true ]; then
        clean_build
    fi
    
    # Run validation if requested
    if [ "$VALIDATE" = true ]; then
        validate_docs
    fi
    
    # Run spell check if requested
    if [ "$SPELL_CHECK" = true ]; then
        spell_check
    fi
    
    # Execute target
    case $TARGET in
        html)
            build_html
            ;;
        pdf)
            build_pdf
            ;;
        epub)
            build_epub
            ;;
        all)
            build_html
            build_pdf
            build_epub
            build_user_manual
            ;;
        deploy)
            deploy_github_pages
            ;;
        clean)
            clean_build
            ;;
        livehtml)
            start_live_reload
            ;;
        *)
            log_error "Unknown target: $TARGET"
            show_help
            exit 1
            ;;
    esac
    
    # Check links if requested
    if [ "$CHECK_LINKS" = true ]; then
        check_links
    fi
    
    # Generate statistics
    generate_stats
    
    log_success "$PROJECT_NAME documentation build completed!"
}

# Run main function
main "$@"
