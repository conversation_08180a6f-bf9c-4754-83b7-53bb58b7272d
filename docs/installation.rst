Installation Guide
==================

This guide will help you install and set up <PERSON><PERSON><PERSON><PERSON><PERSON>ähler for development or production use.

.. image:: _static/images/installation-banner.png
   :alt: <PERSON>nü<PERSON>Zähler Installation
   :align: center
   :width: 600px

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~~

**For Development:**

- **Node.js**: 18.0 or higher
- **npm**: 8.0 or higher (or yarn 1.22+)
- **Git**: Latest version
- **Expo CLI**: Latest version
- **React Native CLI**: 0.72 or higher

**For iOS Development:**

- **macOS**: 10.15 (Catalina) or higher
- **Xcode**: 14.0 or higher
- **iOS Simulator**: iOS 13.0 or higher
- **CocoaPods**: 1.11 or higher

**For Android Development:**

- **Android Studio**: 2022.1 or higher
- **Android SDK**: API Level 21 (Android 5.0) or higher
- **Java Development Kit**: JDK 11 or higher
- **Android Emulator**: API Level 21 or higher

**Hardware Requirements:**

- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space minimum
- **CPU**: Multi-core processor recommended

Development Setup
-----------------

1. Install Node.js and npm
~~~~~~~~~~~~~~~~~~~~~~~~~~

**macOS (using Homebrew):**

.. code-block:: bash

   # Install Homebrew if not already installed
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # Install Node.js
   brew install node

**Windows:**

1. Download Node.js from `nodejs.org <https://nodejs.org/>`_
2. Run the installer and follow the setup wizard
3. Verify installation in Command Prompt

**Linux (Ubuntu/Debian):**

.. code-block:: bash

   # Update package index
   sudo apt update
   
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

**Verify Installation:**

.. code-block:: bash

   node --version  # Should show v18.0.0 or higher
   npm --version   # Should show 8.0.0 or higher

2. Install Expo CLI
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install Expo CLI globally
   npm install -g @expo/cli
   
   # Verify installation
   expo --version

3. Clone the Repository
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/your-username/znuni-zaehler.git
   
   # Navigate to the project directory
   cd znuni-zaehler/app

4. Install Dependencies
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install npm dependencies
   npm install
   
   # Install Expo dependencies
   npx expo install

5. Environment Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Copy environment template
   cp .env.example .env
   
   # Edit environment variables
   nano .env  # or use your preferred editor

**Required Environment Variables:**

.. code-block:: bash

   # App Configuration
   APP_VARIANT=development
   EAS_PROJECT_ID=your-eas-project-id
   
   # API Keys (optional for basic functionality)
   GOOGLE_VISION_API_KEY=your-google-vision-api-key
   CLARIFAI_API_KEY=your-clarifai-api-key
   
   # Analytics (optional)
   SENTRY_DSN=your-sentry-dsn
   AMPLITUDE_API_KEY=your-amplitude-api-key

6. Start Development Server
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Start Expo development server
   npx expo start
   
   # Or with specific options
   npx expo start --clear  # Clear cache
   npx expo start --tunnel # Use tunnel for external access

iOS Setup
---------

1. Install Xcode
~~~~~~~~~~~~~~~~

1. Download Xcode from the Mac App Store
2. Install Xcode Command Line Tools:

.. code-block:: bash

   xcode-select --install

3. Accept Xcode license:

.. code-block:: bash

   sudo xcodebuild -license accept

2. Install CocoaPods
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install CocoaPods
   sudo gem install cocoapods
   
   # Verify installation
   pod --version

3. iOS Simulator Setup
~~~~~~~~~~~~~~~~~~~~~~

1. Open Xcode
2. Go to **Xcode → Preferences → Components**
3. Download iOS simulators for testing
4. Recommended: iOS 13.0, 14.0, and latest version

4. Run on iOS
~~~~~~~~~~~~~

.. code-block:: bash

   # Start development server
   npx expo start
   
   # Press 'i' to open iOS simulator
   # Or scan QR code with Expo Go app on physical device

Android Setup
-------------

1. Install Android Studio
~~~~~~~~~~~~~~~~~~~~~~~~~

1. Download Android Studio from `developer.android.com <https://developer.android.com/studio>`_
2. Run the installer and follow the setup wizard
3. Install Android SDK and build tools

2. Configure Android SDK
~~~~~~~~~~~~~~~~~~~~~~~~

1. Open Android Studio
2. Go to **Tools → SDK Manager**
3. Install required SDK platforms:
   - Android 5.0 (API Level 21) - minimum
   - Android 13 (API Level 33) - recommended
   - Latest Android version

4. Install SDK build tools and platform tools

3. Set Environment Variables
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**macOS/Linux:**

.. code-block:: bash

   # Add to ~/.bashrc or ~/.zshrc
   export ANDROID_HOME=$HOME/Library/Android/sdk
   export PATH=$PATH:$ANDROID_HOME/emulator
   export PATH=$PATH:$ANDROID_HOME/platform-tools

**Windows:**

1. Open System Properties → Advanced → Environment Variables
2. Add ``ANDROID_HOME`` pointing to SDK location
3. Add SDK tools to PATH

4. Create Android Emulator
~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. Open Android Studio
2. Go to **Tools → AVD Manager**
3. Create new virtual device:
   - Device: Pixel 4 or similar
   - System Image: API Level 21 or higher
   - Configuration: Default settings

5. Run on Android
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Start development server
   npx expo start
   
   # Press 'a' to open Android emulator
   # Or scan QR code with Expo Go app on physical device

Production Build Setup
----------------------

1. Install EAS CLI
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install EAS CLI globally
   npm install -g eas-cli
   
   # Login to Expo account
   eas login

2. Configure EAS Build
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Initialize EAS configuration
   eas build:configure

This creates ``eas.json`` with build configurations.

3. Build for Production
~~~~~~~~~~~~~~~~~~~~~~~

**iOS Build:**

.. code-block:: bash

   # Build for iOS App Store
   eas build --platform ios --profile production

**Android Build:**

.. code-block:: bash

   # Build for Google Play Store
   eas build --platform android --profile production

**Both Platforms:**

.. code-block:: bash

   # Build for both platforms
   eas build --platform all --profile production

API Keys Setup
--------------

Google Vision API
~~~~~~~~~~~~~~~~~

1. Go to `Google Cloud Console <https://console.cloud.google.com/>`_
2. Create new project or select existing one
3. Enable Vision API
4. Create API key in Credentials section
5. Add key to ``.env`` file:

.. code-block:: bash

   GOOGLE_VISION_API_KEY=AIzaSyC4E6t7...

Clarifai API (Optional)
~~~~~~~~~~~~~~~~~~~~~~~

1. Sign up at `Clarifai <https://clarifai.com/>`_
2. Create new application
3. Get API key from application settings
4. Add key to ``.env`` file:

.. code-block:: bash

   CLARIFAI_API_KEY=your-clarifai-key

Sentry (Error Tracking)
~~~~~~~~~~~~~~~~~~~~~~~

1. Sign up at `Sentry <https://sentry.io/>`_
2. Create new project for React Native
3. Get DSN from project settings
4. Add DSN to ``.env`` file:

.. code-block:: bash

   SENTRY_DSN=https://<EMAIL>/project-id

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Metro bundler issues:**

.. code-block:: bash

   # Clear Metro cache
   npx expo start --clear
   
   # Reset Metro cache completely
   npx expo r -c

**Node modules issues:**

.. code-block:: bash

   # Clean install
   rm -rf node_modules package-lock.json
   npm install

**iOS build issues:**

.. code-block:: bash

   # Clean iOS build
   cd ios && rm -rf build && cd ..
   
   # Reinstall pods
   cd ios && pod install && cd ..

**Android build issues:**

.. code-block:: bash

   # Clean Android build
   cd android && ./gradlew clean && cd ..

**Permission issues on macOS:**

.. code-block:: bash

   # Fix npm permissions
   sudo chown -R $(whoami) ~/.npm

Platform-Specific Issues
~~~~~~~~~~~~~~~~~~~~~~~~~

**iOS Simulator not starting:**

1. Quit Xcode and Simulator
2. Reset simulator: **Device → Erase All Content and Settings**
3. Restart development server

**Android emulator slow:**

1. Increase emulator RAM in AVD settings
2. Enable hardware acceleration
3. Use x86_64 system images

**Metro bundler port conflicts:**

.. code-block:: bash

   # Use different port
   npx expo start --port 8082

**Network issues with tunnel:**

.. code-block:: bash

   # Use LAN instead of tunnel
   npx expo start --lan

Verification
------------

Test Installation
~~~~~~~~~~~~~~~~~

1. **Start development server:**

.. code-block:: bash

   npx expo start

2. **Open app on device/simulator**
3. **Verify core features:**
   - App launches successfully
   - Navigation works
   - Camera permissions granted
   - Database initializes

4. **Test API integrations:**
   - OCR scanning (if API key configured)
   - Food recognition (if API key configured)
   - Error tracking (if Sentry configured)

Performance Check
~~~~~~~~~~~~~~~~~

**Development metrics:**
   - App startup: < 3 seconds
   - Screen transitions: < 500ms
   - Camera launch: < 2 seconds
   - Database queries: < 100ms

**Build verification:**
   - iOS build completes without errors
   - Android build completes without errors
   - App size < 50MB
   - No critical warnings

Next Steps
----------

After successful installation:

1. **Read the User Guide:** :doc:`user-guide/index`
2. **Explore API Documentation:** :doc:`api/ocr-service`
3. **Review Architecture:** :doc:`developer/architecture`
4. **Run Tests:** :doc:`developer/testing`
5. **Deploy to Production:** :doc:`developer/deployment`

Support
-------

If you encounter issues during installation:

1. **Check the FAQ:** :doc:`support/faq`
2. **Search existing issues:** GitHub Issues
3. **Create new issue:** Include system info and error logs
4. **Contact support:** :doc:`support/contact`

**System Information Template:**

.. code-block:: text

   OS: [macOS/Windows/Linux version]
   Node.js: [version]
   npm: [version]
   Expo CLI: [version]
   Xcode: [version] (macOS only)
   Android Studio: [version]
   Error: [full error message]
   Steps: [steps to reproduce]
