# Documentation build requirements for Znü<PERSON>Zähler
# Install with: pip install -r requirements-docs.txt

# Core Sphinx
sphinx>=6.0.0
sphinx-rtd-theme>=1.3.0
sphinx-autobuild>=2021.3.14

# Sphinx extensions
myst-parser>=2.0.0
sphinx-copybutton>=0.5.2
sphinx-tabs>=3.4.1
sphinx-design>=0.5.0
sphinxcontrib-mermaid>=0.9.2
sphinxcontrib-plantuml>=0.25
sphinx-togglebutton>=0.3.2

# API documentation
sphinx-js>=3.2.1
sphinx-autodoc-typehints>=1.24.0

# Documentation quality
doc8>=1.1.1
sphinx-lint>=0.6.7
pyspelling>=2.10

# Live reload and development
watchdog>=3.0.0
livereload>=2.6.3

# PDF generation
rst2pdf>=0.101
weasyprint>=59.0

# Internationalization
sphinx-intl>=2.1.0
babel>=2.12.1

# Code highlighting
pygments>=2.15.1

# Markdown support
recommonmark>=0.7.1
markdown>=3.4.3

# Utilities
pyyaml>=6.0
jinja2>=3.1.2
markupsafe>=2.1.3

# Testing documentation
pytest>=7.4.0
pytest-cov>=4.1.0

# Pre-commit hooks for documentation
pre-commit>=3.3.3
