# Food Database Integrations for ZnüniZähler

This document outlines the open source food databases integrated into ZnüniZähler, providing comprehensive nutrition tracking capabilities beyond the existing Open Food Facts and USDA databases.

## Overview

ZnüniZähler now supports **6 major food databases**, making it one of the most comprehensive nutrition tracking apps available:

1. **Open Food Facts** (existing) - Global crowdsourced food database
2. **USDA Food Data Central** (existing) - US government nutrition database  
3. **FoodB Database** (existing) - University of Alberta food composition database
4. **Canadian Nutrient File (CNF)** (new) - Health Canada's official food database
5. **Phenol-Explorer** (new) - Polyphenol and antioxidant database
6. **Future integrations** - Australian AFCD, EuroFIR, and others

## New Database Integrations

### 1. Canadian Nutrient File (CNF)

**Source**: Health Canada  
**Coverage**: ~5,000 foods commonly consumed in Canada  
**Strengths**: Government-verified data, bilingual support (English/French)

#### Features:
- Comprehensive macronutrient and micronutrient data
- Canadian-specific food items and brands
- Bilingual food names and descriptions
- Government quality assurance
- Real-time API access through Canada's Open Data portal

#### Implementation:
- **Importer**: `app/src/utils/importers/CNFImporter.js`
- **API Integration**: `app/src/api/cnfDatabase.js`
- **UI Screen**: `app/src/screens/CNFImportScreen.js`

#### Data Format:
```javascript
{
  name: "Milk, 2% M.F.",
  source: "CNF",
  source_id: "142",
  nutrients: [
    { name: "Protein", amount: 3.3, unit: "g" },
    { name: "Total Fat", amount: 2.0, unit: "g" },
    { name: "Carbohydrate", amount: 4.8, unit: "g" }
  ]
}
```

### 2. Phenol-Explorer Database

**Source**: INRA (French National Institute for Agricultural Research)  
**Coverage**: 500+ polyphenols in 400+ foods  
**Strengths**: Unique antioxidant focus, research-backed data

#### Features:
- Comprehensive polyphenol content data
- Antioxidant tracking capabilities
- Health benefit information
- Polyphenol class categorization (Flavonoids, Phenolic acids, Stilbenes, Lignans)

#### Polyphenol Classes Supported:
1. **Flavonoids**
   - Anthocyanins (color compounds in berries)
   - Flavanols (catechins in tea, cocoa)
   - Flavonols (quercetin in onions, apples)
   - Flavanones (hesperidin in citrus)
   - Isoflavones (genistein in soy)

2. **Phenolic Acids**
   - Hydroxybenzoic acids (gallic acid)
   - Hydroxycinnamic acids (chlorogenic acid in coffee)

3. **Stilbenes**
   - Resveratrol (in grapes, wine)

4. **Lignans**
   - Plant compounds with hormone-like effects

#### Implementation:
- **Importer**: `app/src/utils/importers/PhenolExplorerImporter.js`
- **UI Screen**: `app/src/screens/PhenolExplorerImportScreen.js`

#### Data Format:
```javascript
{
  name: "Red wine",
  source: "Phenol-Explorer",
  polyphenols: [
    { name: "Resveratrol", amount: 1.9, unit: "mg", class: "Stilbenes" },
    { name: "Quercetin", amount: 2.5, unit: "mg", class: "Flavonoids" },
    { name: "Total Polyphenols", amount: 101.9, unit: "mg" }
  ]
}
```

## Technical Implementation

### Database Architecture

The new integrations follow the existing pattern:

```
app/src/
├── api/
│   ├── foodDatabase.js (updated with multi-source search)
│   └── cnfDatabase.js (new CNF API integration)
├── utils/importers/
│   ├── CNFImporter.js (new)
│   └── PhenolExplorerImporter.js (new)
├── screens/
│   ├── CNFImportScreen.js (new)
│   ├── PhenolExplorerImportScreen.js (new)
│   └── FoodImportScreen.js (updated)
└── services/
    └── databaseService.js (supports multiple sources)
```

### Multi-Source Search

The enhanced food search now queries multiple databases simultaneously:

```javascript
import { searchMultipleDatabases } from '../api/foodDatabase';

const results = await searchMultipleDatabases('apple', ['openfoodfacts', 'cnf'], 5);
// Returns results from both Open Food Facts and CNF
```

### Data Source Tracking

All imported foods include source tracking:

```sql
CREATE TABLE foods (
    ...
    source TEXT NOT NULL,           -- 'CNF', 'Phenol-Explorer', 'USDA', etc.
    source_id TEXT,                 -- Original ID from source database
    ...
);
```

## User Benefits

### Enhanced Coverage
- **Regional Foods**: Better coverage of Canadian, European, and other regional foods
- **Specialty Items**: Antioxidant-rich foods, functional foods, traditional foods
- **Data Validation**: Cross-reference nutrition data across multiple authoritative sources

### New Tracking Capabilities
- **Antioxidant Tracking**: Monitor polyphenol intake for health benefits
- **Regional Nutrition**: Access to country-specific food composition data
- **Research-Backed Data**: Government and academic institution verified information

### Improved Accuracy
- **Multiple Sources**: Validate nutrition data across databases
- **Authoritative Data**: Government and research institution sources
- **Reduced "Not Found"**: Comprehensive coverage reduces missing foods

## Usage Instructions

### Importing CNF Data

1. Visit [Health Canada CNF Downloads](https://www.canada.ca/en/health-canada/services/food-nutrition/healthy-eating/nutrient-data/canadian-nutrient-file-2015-download-files.html)
2. Download the CNF 2015 ZIP file
3. In ZnüniZähler, go to Settings > Import Data > Canadian Nutrient File
4. Select the downloaded ZIP file
5. Wait for import to complete

### Importing Phenol-Explorer Data

1. Visit [Phenol-Explorer Downloads](http://phenol-explorer.eu/downloads)
2. Download polyphenol content CSV files
3. In ZnüniZähler, go to Settings > Import Data > Phenol-Explorer
4. Select CSV file or ZIP archive
5. Wait for import to complete

### Using Multi-Source Search

The app automatically searches across all available databases when you:
- Search for foods in the food diary
- Scan barcodes (falls back to database search if not found)
- Browse food categories

## Future Enhancements

### Planned Integrations

1. **Australian Food Composition Database (AFCD)**
   - Coverage: Australian and New Zealand foods
   - Source: Food Standards Australia New Zealand

2. **EuroFIR Databases**
   - Coverage: European national food databases
   - Source: European Food Information Resource Network

3. **National Food Databases**
   - UK Food Composition Database
   - German Food Composition Database (BLS)
   - French CIQUAL Database

### Advanced Features

1. **Smart Data Fusion**
   - Automatically merge data from multiple sources
   - Confidence scoring for nutrition values
   - Conflict resolution algorithms

2. **Real-Time Sync**
   - Automatic updates from API sources
   - Background data refresh
   - Delta updates for efficiency

3. **Personalized Recommendations**
   - Antioxidant intake optimization
   - Regional food suggestions
   - Nutrient gap analysis

## Technical Notes

### Performance Considerations
- Local database storage for offline access
- Efficient indexing for multi-source searches
- Background import processing
- Progressive data loading

### Data Quality
- Source attribution for all nutrition data
- Import validation and error handling
- Duplicate detection across sources
- Data freshness tracking

### Privacy & Compliance
- Local-first data storage
- No personal data sent to external APIs
- GDPR-compliant data handling
- User control over data sources

## Conclusion

The integration of CNF and Phenol-Explorer databases significantly enhances ZnüniZähler's capabilities, providing users with:

- **Comprehensive Coverage**: Access to government-verified nutrition data from multiple countries
- **Unique Features**: First-in-class antioxidant and polyphenol tracking
- **Research-Backed Data**: Academic and government institution sources
- **Enhanced Accuracy**: Cross-validation across multiple authoritative databases

This positions ZnüniZähler as one of the most comprehensive nutrition tracking applications available, with unparalleled access to diverse, high-quality food composition data.
