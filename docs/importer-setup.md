# Food Database Importer Setup Guide

This guide will help you set up the new food database importers for ZnüniZähler.

## 🚀 Quick Setup

### 1. Install Dependencies

Run the following command to install the required packages:

```bash
npm install
```

This will install the newly added dependencies:
- `axios` - For API calls
- `expo-document-picker` - For file selection
- `react-native-zip-archive` - For ZIP file extraction

### 2. Platform-Specific Setup

#### iOS Setup
No additional setup required for iOS. The dependencies are compatible with Expo managed workflow.

#### Android Setup
No additional setup required for Android. The dependencies are compatible with Expo managed workflow.

#### Web Setup
The importers are designed for mobile platforms. Web support may be limited for file operations.

## 📱 Using the Importers

### Canadian Nutrient File (CNF)

1. **Download Data**:
   - Visit [Health Canada CNF Downloads](https://www.canada.ca/en/health-canada/services/food-nutrition/healthy-eating/nutrient-data/canadian-nutrient-file-2015-download-files.html)
   - Download the "Canadian Nutrient File 2015 - All Files" ZIP

2. **Import Process**:
   - Open ZnüniZ<PERSON>hler
   - Navigate to Settings → Import Data → Canadian Nutrient File
   - Tap "Select CNF Dataset"
   - Choose the downloaded ZIP file
   - Wait for import to complete

3. **Expected Results**:
   - ~5,000 Canadian foods imported
   - Comprehensive nutrient data
   - Allergen detection
   - Bilingual food names

### Phenol-Explorer Database

1. **Download Data**:
   - Visit [Phenol-Explorer Downloads](http://phenol-explorer.eu/downloads)
   - Download polyphenol content CSV files
   - Can be individual CSV files or ZIP archives

2. **Import Process**:
   - Open ZnüniZähler
   - Navigate to Settings → Import Data → Phenol-Explorer
   - Tap "Select Phenol Data"
   - Choose CSV file or ZIP archive
   - Wait for import to complete

3. **Expected Results**:
   - 400+ foods with polyphenol data
   - Individual polyphenol compounds
   - Total antioxidant values
   - Health benefit information

## 🔧 Troubleshooting

### Common Issues

#### "Module not found" errors
```bash
# Clear cache and reinstall
npm start -- --clear
npm install
```

#### File picker not working
- Ensure you're testing on a physical device or proper simulator
- Check that file permissions are granted
- Verify the file format is supported (ZIP, CSV)

#### Import fails with "Invalid dataset"
- Verify you downloaded the correct dataset format
- Check that the ZIP file is not corrupted
- Ensure the CSV files have the expected structure

#### Memory issues during import
- Close other apps during import
- Import smaller datasets first
- Restart the app if needed

### Debug Information

Enable debug logging to troubleshoot issues:

1. Go to Settings → Debug Information
2. Enable "Verbose Logging"
3. Attempt the import again
4. Check the debug logs for detailed error information

## 📊 Import Statistics

After a successful import, you'll see statistics like:

### CNF Import
- **Foods**: Number of food items imported
- **Nutrients**: Number of nutrient values added
- **Allergens**: Number of allergen warnings detected
- **Errors**: Number of items that failed to import

### Phenol-Explorer Import
- **Foods**: Number of foods with polyphenol data
- **Polyphenols**: Number of total polyphenol values
- **Compounds**: Number of individual polyphenol compounds
- **Errors**: Number of items that failed to import

## 🔍 Verifying Import Success

### Check Imported Data

1. **Search for Foods**:
   - Use the food search to look for imported items
   - CNF foods will show "Source: CNF"
   - Phenol-Explorer foods will show "Source: Phenol-Explorer"

2. **View Nutrient Data**:
   - Select an imported food
   - Check that nutrient values are populated
   - Verify polyphenol data for Phenol-Explorer imports

3. **Database Statistics**:
   - Go to Settings → Debug Information
   - Check "Database Statistics" for import counts

## 🚨 Known Limitations

### Current Limitations

1. **Simplified Nutrient Storage**: Nutrients are currently logged but not fully integrated into the database schema
2. **No Real-time Sync**: Imported data is static until manually re-imported
3. **Limited Error Recovery**: Failed imports require starting over
4. **Memory Usage**: Large datasets may require significant memory

### Future Improvements

1. **Enhanced Database Schema**: Full nutrient and allergen tables
2. **Incremental Updates**: Update only changed data
3. **Background Processing**: Import in background with progress notifications
4. **Data Validation**: Enhanced validation and error recovery
5. **Automatic Updates**: Periodic updates from API sources

## 📞 Support

### Getting Help

If you encounter issues:

1. Check this troubleshooting guide
2. Review the debug logs
3. Verify your dataset format
4. Try with a smaller test dataset first

### Reporting Issues

When reporting issues, please include:
- Device type and OS version
- Dataset source and size
- Error messages from debug logs
- Steps to reproduce the issue

## 🎯 Next Steps

After successful setup:

1. **Test with Small Datasets**: Start with smaller CSV files
2. **Verify Data Quality**: Check imported foods for accuracy
3. **Explore New Features**: Try the polyphenol tracking capabilities
4. **Provide Feedback**: Report any issues or suggestions

The importers are now ready to use! Enjoy the enhanced nutrition tracking capabilities with comprehensive food databases.
