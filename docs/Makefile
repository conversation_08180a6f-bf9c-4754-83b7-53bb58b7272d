# Makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Custom targets for ZnüniZähler documentation

# Build all documentation formats
all: html pdf epub

# Build HTML documentation
html:
	@echo "Building HTML documentation..."
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "HTML documentation built in $(BUILDDIR)/html/"

# Build PDF documentation
pdf: latex
	@echo "Building PDF documentation..."
	@cd "$(BUILDDIR)/latex" && make all-pdf
	@echo "PDF documentation built in $(BUILDDIR)/latex/"

# Build LaTeX documentation
latex:
	@echo "Building LaTeX documentation..."
	@$(SPHINXBUILD) -b latex "$(SOURCEDIR)" "$(BUILDDIR)/latex" $(SPHINXOPTS) $(O)

# Build EPUB documentation
epub:
	@echo "Building EPUB documentation..."
	@$(SPHINXBUILD) -b epub "$(SOURCEDIR)" "$(BUILDDIR)/epub" $(SPHINXOPTS) $(O)
	@echo "EPUB documentation built in $(BUILDDIR)/epub/"

# Build user manual as standalone PDF
user-manual:
	@echo "Building user manual PDF..."
	@$(SPHINXBUILD) -b latex -D master_doc=user-manual "$(SOURCEDIR)" "$(BUILDDIR)/user-manual" $(SPHINXOPTS) $(O)
	@cd "$(BUILDDIR)/user-manual" && make all-pdf
	@echo "User manual PDF built in $(BUILDDIR)/user-manual/"

# Clean build directory
clean:
	@echo "Cleaning build directory..."
	@rm -rf "$(BUILDDIR)"
	@echo "Build directory cleaned."

# Live reload for development
livehtml:
	@echo "Starting live reload server..."
	@sphinx-autobuild "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)

# Check for broken links
linkcheck:
	@echo "Checking for broken links..."
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)

# Check documentation coverage
coverage:
	@echo "Checking documentation coverage..."
	@$(SPHINXBUILD) -b coverage "$(SOURCEDIR)" "$(BUILDDIR)/coverage" $(SPHINXOPTS) $(O)

# Spell check documentation
spelling:
	@echo "Checking spelling..."
	@$(SPHINXBUILD) -b spelling "$(SOURCEDIR)" "$(BUILDDIR)/spelling" $(SPHINXOPTS) $(O)

# Build documentation for different languages
html-en:
	@$(SPHINXBUILD) -b html -D language=en "$(SOURCEDIR)" "$(BUILDDIR)/html/en" $(SPHINXOPTS) $(O)

html-de:
	@$(SPHINXBUILD) -b html -D language=de "$(SOURCEDIR)" "$(BUILDDIR)/html/de" $(SPHINXOPTS) $(O)

html-fr:
	@$(SPHINXBUILD) -b html -D language=fr "$(SOURCEDIR)" "$(BUILDDIR)/html/fr" $(SPHINXOPTS) $(O)

# Deploy documentation to GitHub Pages
deploy-gh-pages: html
	@echo "Deploying to GitHub Pages..."
	@touch "$(BUILDDIR)/html/.nojekyll"
	@echo "znunizaehler.com" > "$(BUILDDIR)/html/CNAME"
	@cd "$(BUILDDIR)/html" && git init && git add -A && git commit -m "Deploy documentation" && git push -f **************:znunizaehler/znuni-zaehler-docs.git master:gh-pages

# Generate API documentation from source code
api-docs:
	@echo "Generating API documentation..."
	@sphinx-apidoc -o api ../app/src --force --module-first
	@echo "API documentation generated in api/"

# Validate documentation structure
validate:
	@echo "Validating documentation structure..."
	@$(SPHINXBUILD) -b dummy "$(SOURCEDIR)" "$(BUILDDIR)/dummy" -W $(SPHINXOPTS) $(O)
	@echo "Documentation structure validated."

# Generate documentation statistics
stats:
	@echo "Generating documentation statistics..."
	@find . -name "*.rst" -exec wc -l {} + | tail -1
	@find . -name "*.md" -exec wc -l {} + | tail -1
	@echo "Documentation files:"
	@find . -name "*.rst" -o -name "*.md" | wc -l

# Install documentation dependencies
install-deps:
	@echo "Installing documentation dependencies..."
	@pip install -r requirements-docs.txt
	@echo "Dependencies installed."

# Setup development environment
setup-dev: install-deps
	@echo "Setting up development environment..."
	@pre-commit install
	@echo "Development environment ready."

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)
