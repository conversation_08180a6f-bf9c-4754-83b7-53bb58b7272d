# ZnüniZähler Documentation

Welcome to the comprehensive documentation for ZnüniZähler, the advanced nutrition tracking app with AI-powered features.

## 📚 Documentation Overview

This documentation provides complete coverage of ZnüniZähler's features, APIs, and development guidelines.

### 🎯 For Users
- **[User Guide](user-guide/index.rst)** - Complete guide to using the app
- **[User Manual](user-manual.rst)** - Comprehensive manual (PDF available)
- **[Installation Guide](installation.rst)** - Getting started with the app
- **[FAQ](support/faq.rst)** - Frequently asked questions

### 👨‍💻 For Developers
- **[Architecture Overview](developer/architecture.rst)** - System design and structure
- **[API Reference](api/)** - Complete API documentation
- **[Testing Guide](developer/testing.rst)** - Testing strategies and tools
- **[Deployment Guide](developer/deployment.rst)** - Production deployment

### 🔧 For Contributors
- **[Contributing Guide](support/contributing.rst)** - How to contribute
- **[Code Style](developer/code-style.rst)** - Coding standards
- **[Release Process](developer/release-process.rst)** - How releases are made

## 🚀 Quick Start

### Building Documentation Locally

1. **Install Dependencies**
   ```bash
   pip install -r requirements-docs.txt
   ```

2. **Build HTML Documentation**
   ```bash
   make html
   # or
   ./deploy-docs.sh html
   ```

3. **View Documentation**
   ```bash
   open _build/html/index.html
   ```

### Live Development Server

For documentation development with live reload:

```bash
make livehtml
# or
./deploy-docs.sh --live
```

Visit `http://localhost:8000` to see live updates as you edit.

## 📖 Documentation Structure

```
docs/
├── index.rst                 # Main documentation index
├── installation.rst          # Installation guide
├── quick-start.rst           # Quick start guide
├── user-manual.rst           # Complete user manual
├── user-guide/               # User documentation
│   ├── index.rst
│   ├── food-logging.rst
│   ├── ai-features.rst
│   ├── analytics.rst
│   └── settings.rst
├── developer/                # Developer documentation
│   ├── architecture.rst
│   ├── api-reference.rst
│   ├── testing.rst
│   └── deployment.rst
├── api/                      # API documentation
│   ├── ocr-service.rst
│   ├── ai-recommendations.rst
│   ├── food-recognition.rst
│   └── analytics.rst
├── support/                  # Support documentation
│   ├── faq.rst
│   ├── contact.rst
│   └── contributing.rst
├── releases/                 # Release notes
│   ├── v1.0.0.rst
│   └── roadmap.rst
├── _static/                  # Static assets
│   ├── images/
│   ├── css/
│   └── js/
└── _templates/               # Custom templates
```

## 🛠 Build Targets

The documentation supports multiple build targets:

### HTML Documentation
```bash
make html
./deploy-docs.sh html
```
Builds responsive HTML documentation with search functionality.

### PDF Documentation
```bash
make pdf
./deploy-docs.sh pdf
```
Generates a complete PDF version of the documentation.

### EPUB Documentation
```bash
make epub
./deploy-docs.sh epub
```
Creates an EPUB version for e-readers.

### User Manual PDF
```bash
make user-manual
./deploy-docs.sh user-manual
```
Builds a standalone user manual PDF.

### All Formats
```bash
make all
./deploy-docs.sh all
```
Builds all documentation formats.

## 🔍 Quality Assurance

### Link Checking
```bash
make linkcheck
./deploy-docs.sh --check-links
```
Validates all internal and external links.

### Spell Checking
```bash
make spelling
./deploy-docs.sh --spell-check
```
Runs spell checker on all documentation.

### Structure Validation
```bash
make validate
./deploy-docs.sh --validate
```
Validates documentation structure and syntax.

### Coverage Report
```bash
make coverage
```
Generates documentation coverage report.

## 🚀 Deployment

### GitHub Pages
```bash
./deploy-docs.sh deploy
```
Builds and deploys documentation to GitHub Pages.

### Manual Deployment
1. Build documentation: `make html`
2. Copy `_build/html/` to your web server
3. Ensure proper permissions and redirects

### Continuous Integration
The documentation is automatically built and deployed on:
- Pull requests (validation only)
- Main branch pushes (full deployment)
- Release tags (versioned deployment)

## 🎨 Customization

### Theme Configuration
The documentation uses the Sphinx RTD theme with custom styling:

- **Colors**: Customized to match app branding
- **Logo**: ZnüniZähler logo and favicon
- **Fonts**: Optimized for readability
- **Mobile**: Responsive design for all devices

### Custom CSS
Additional styling is in `_static/custom.css`:
- Brand colors and typography
- Custom component styling
- Mobile optimizations
- Print-friendly styles

### Custom JavaScript
Interactive features in `_static/custom.js`:
- Enhanced search functionality
- Code copy buttons
- Image zoom capabilities
- Analytics tracking

## 📝 Writing Guidelines

### Style Guide
- Use clear, concise language
- Write in active voice
- Include code examples for technical content
- Add screenshots for UI instructions
- Use consistent terminology

### Formatting Standards
- **Headers**: Use proper RST hierarchy
- **Code**: Use appropriate syntax highlighting
- **Links**: Use descriptive link text
- **Images**: Include alt text and captions
- **Lists**: Use bullet points for unordered lists

### Content Organization
- Start with overview and objectives
- Provide step-by-step instructions
- Include troubleshooting sections
- Add cross-references to related topics
- End with next steps or related links

## 🔧 Development Tools

### Required Tools
- **Python 3.8+**: For Sphinx and extensions
- **Sphinx 6.0+**: Documentation generator
- **Make**: Build automation
- **Git**: Version control

### Recommended Tools
- **VS Code**: With RST extension
- **ReStructuredText**: Language support
- **Live Server**: For local preview
- **Grammarly**: Grammar and spell checking

### Browser Testing
Test documentation in:
- Chrome/Chromium (latest)
- Firefox (latest)
- Safari (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📊 Analytics and Metrics

### Documentation Metrics
- Page views and popular content
- Search queries and results
- User feedback and ratings
- Download statistics for PDFs

### Performance Monitoring
- Page load times
- Search response times
- Mobile performance
- Accessibility compliance

## 🤝 Contributing to Documentation

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally with `make html`
5. Submit a pull request

### Types of Contributions
- **Content Updates**: Fix errors, add missing information
- **New Sections**: Add documentation for new features
- **Translations**: Help translate to other languages
- **Improvements**: Enhance structure and organization

### Review Process
1. Automated checks (spelling, links, build)
2. Technical review by maintainers
3. User experience review
4. Final approval and merge

## 📞 Support

### Documentation Issues
- **GitHub Issues**: Report bugs or request features
- **Discussions**: Ask questions or suggest improvements
- **Email**: <EMAIL>

### Community
- **Discord**: Join our documentation community
- **Forums**: Participate in discussions
- **Meetups**: Attend documentation sprints

## 📄 License

This documentation is licensed under the same terms as the ZnüniZähler project.

## 🔗 Links

- **Live Documentation**: https://docs.znunizaehler.com
- **GitHub Repository**: https://github.com/znunizaehler/znuni-zaehler
- **App Store**: https://apps.apple.com/app/znunizaehler
- **Google Play**: https://play.google.com/store/apps/details?id=com.znunizaehler.app

---

**Last Updated**: December 2024  
**Documentation Version**: 1.0.0  
**App Version**: 1.0.0
