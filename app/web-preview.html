<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZnüniZähler Web Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #121212;
            color: #FFFFFF;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #1A1A1A;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .subtitle {
            font-size: 16px;
            color: #E0E0E0;
        }
        .section {
            margin-top: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
        }
        .options {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        .option {
            width: 31%;
            background-color: #333333;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        .icon-container {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 8px;
        }
        .scan-icon {
            background-color: #4CAF50;
        }
        .barcode-icon {
            background-color: #2196F3;
        }
        .manual-icon {
            background-color: #FF9800;
        }
        .icon {
            font-size: 24px;
        }
        .option-text {
            font-size: 14px;
        }
        .summary {
            background-color: #262626;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            min-height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .empty-text {
            font-style: italic;
            text-align: center;
            color: #B0B0B0;
        }
        .profile-button {
            background-color: #388E3C;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
            margin: 16px 0;
            border-radius: 8px;
        }
        .profile-icon {
            font-size: 20px;
            margin-right: 8px;
        }
        .profile-text {
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">Welcome to ZnüniZähler</div>
            <div class="subtitle">Track your nutrition with precision</div>
        </div>

        <div class="section">
            <div class="section-title">Capture Food</div>
            <div class="options">
                <div class="option">
                    <div class="icon-container scan-icon">
                        <div class="icon">📷</div>
                    </div>
                    <div class="option-text">Scan Label</div>
                </div>
                <div class="option">
                    <div class="icon-container barcode-icon">
                        <div class="icon">📊</div>
                    </div>
                    <div class="option-text">Scan Barcode</div>
                </div>
                <div class="option">
                    <div class="icon-container manual-icon">
                        <div class="icon">✏️</div>
                    </div>
                    <div class="option-text">Manual Entry</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Today's Summary</div>
            <div class="summary">
                <div class="empty-text">Your daily nutrition summary will appear here</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Recent Entries</div>
            <div class="summary">
                <div class="empty-text">Your recent food entries will appear here</div>
            </div>
        </div>

        <div class="profile-button">
            <div class="profile-icon">👤</div>
            <div class="profile-text">My Profile</div>
        </div>
    </div>
</body>
</html>
