/**
 * OCR Results Screen for ZnüniZähler
 * Display and edit OCR-extracted nutrition information
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Image, Alert } from 'react-native';
import { 
  Card, 
  Text, 
  Button, 
  TextInput, 
  Chip, 
  Divider, 
  Surface,
  IconButton,
  ProgressBar
} from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import { saveFood } from '../services/databaseService';

const OCRResultsScreen = ({ navigation, route }) => {
  const theme = useTheme();
  const { ocrData, imageUri } = route.params;
  
  const [foodData, setFoodData] = useState({
    name: '',
    brand: '',
    serving_size: 100,
    serving_unit: 'g',
    ...ocrData.nutritionFacts,
    ingredients: ocrData.ingredients || [],
    allergens: ocrData.allergens || [],
    additives: ocrData.additives || []
  });
  
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Auto-populate serving info if available
    if (ocrData.servingInfo) {
      setFoodData(prev => ({
        ...prev,
        serving_size: ocrData.servingInfo.size,
        serving_unit: ocrData.servingInfo.unit
      }));
    }
  }, [ocrData]);

  const handleSave = async () => {
    if (!foodData.name.trim()) {
      Alert.alert('Error', 'Please enter a food name');
      return;
    }

    setIsSaving(true);

    try {
      // Prepare food data for database
      const foodToSave = {
        name: foodData.name.trim(),
        brand: foodData.brand.trim(),
        serving_size: foodData.serving_size,
        serving_unit: foodData.serving_unit,
        is_custom: 1,
        source_type: 'ocr',
        nutrients: [
          { nutrient_id: 'nutrient-calories', amount: foodData.calories || 0 },
          { nutrient_id: 'nutrient-protein', amount: foodData.protein || 0 },
          { nutrient_id: 'nutrient-carbs', amount: foodData.carbs || 0 },
          { nutrient_id: 'nutrient-sugar', amount: foodData.sugar || 0 },
          { nutrient_id: 'nutrient-fat', amount: foodData.fat || 0 },
          { nutrient_id: 'nutrient-saturated-fat', amount: foodData.saturatedFat || 0 },
          { nutrient_id: 'nutrient-fiber', amount: foodData.fiber || 0 },
          { nutrient_id: 'nutrient-sodium', amount: foodData.sodium || 0 },
          { nutrient_id: 'nutrient-cholesterol', amount: foodData.cholesterol || 0 }
        ].filter(nutrient => nutrient.amount > 0),
        ingredients: foodData.ingredients,
        allergens: foodData.allergens,
        additives: foodData.additives,
        ocr_confidence: ocrData.confidence
      };

      const savedFood = await saveFood(foodToSave);
      
      Alert.alert(
        'Success',
        'Food item saved successfully!',
        [
          {
            text: 'Add to Log',
            onPress: () => navigation.navigate('AddConsumption', { food: savedFood })
          },
          {
            text: 'Done',
            onPress: () => navigation.navigate('Home')
          }
        ]
      );
    } catch (error) {
      console.error('Error saving food:', error);
      Alert.alert('Error', 'Failed to save food item. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const updateNutrient = (nutrient, value) => {
    setFoodData(prev => ({
      ...prev,
      [nutrient]: parseFloat(value) || 0
    }));
  };

  const addIngredient = (ingredient) => {
    if (ingredient.trim() && !foodData.ingredients.includes(ingredient.trim())) {
      setFoodData(prev => ({
        ...prev,
        ingredients: [...prev.ingredients, ingredient.trim()]
      }));
    }
  };

  const removeIngredient = (index) => {
    setFoodData(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index)
    }));
  };

  const ConfidenceIndicator = ({ confidence }) => (
    <View style={styles.confidenceContainer}>
      <Text style={[styles.confidenceText, { color: theme.colors.text }]}>
        OCR Confidence: {Math.round(confidence * 100)}%
      </Text>
      <ProgressBar 
        progress={confidence} 
        color={confidence > 0.8 ? theme.colors.primary : theme.colors.error}
        style={styles.confidenceBar}
      />
    </View>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.text}
            onPress={() => navigation.goBack()}
          />
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            OCR Results
          </Text>
          <IconButton
            icon={isEditing ? "check" : "pencil"}
            size={24}
            iconColor={theme.colors.primary}
            onPress={() => setIsEditing(!isEditing)}
          />
        </View>
        <ConfidenceIndicator confidence={ocrData.confidence} />
      </Surface>

      {/* Scanned Image */}
      {imageUri && (
        <Card style={styles.imageCard}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Scanned Image
            </Text>
            <Image source={{ uri: imageUri }} style={styles.scannedImage} />
          </Card.Content>
        </Card>
      )}

      {/* Basic Information */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Basic Information
          </Text>
          
          <TextInput
            label="Food Name *"
            value={foodData.name}
            onChangeText={(text) => setFoodData(prev => ({ ...prev, name: text }))}
            mode="outlined"
            style={styles.input}
            editable={isEditing}
          />
          
          <TextInput
            label="Brand"
            value={foodData.brand}
            onChangeText={(text) => setFoodData(prev => ({ ...prev, brand: text }))}
            mode="outlined"
            style={styles.input}
            editable={isEditing}
          />
          
          <View style={styles.servingContainer}>
            <TextInput
              label="Serving Size"
              value={foodData.serving_size.toString()}
              onChangeText={(text) => setFoodData(prev => ({ 
                ...prev, 
                serving_size: parseFloat(text) || 0 
              }))}
              mode="outlined"
              style={[styles.input, styles.servingInput]}
              keyboardType="numeric"
              editable={isEditing}
            />
            <TextInput
              label="Unit"
              value={foodData.serving_unit}
              onChangeText={(text) => setFoodData(prev => ({ ...prev, serving_unit: text }))}
              mode="outlined"
              style={[styles.input, styles.unitInput]}
              editable={isEditing}
            />
          </View>
        </Card.Content>
      </Card>

      {/* Nutrition Facts */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Nutrition Facts (per serving)
          </Text>
          
          {[
            { key: 'calories', label: 'Calories', unit: 'kcal' },
            { key: 'protein', label: 'Protein', unit: 'g' },
            { key: 'carbs', label: 'Carbohydrates', unit: 'g' },
            { key: 'sugar', label: 'Sugar', unit: 'g' },
            { key: 'fat', label: 'Total Fat', unit: 'g' },
            { key: 'saturatedFat', label: 'Saturated Fat', unit: 'g' },
            { key: 'fiber', label: 'Fiber', unit: 'g' },
            { key: 'sodium', label: 'Sodium', unit: 'mg' },
            { key: 'cholesterol', label: 'Cholesterol', unit: 'mg' }
          ].map(({ key, label, unit }) => (
            <View key={key} style={styles.nutrientRow}>
              <Text style={[styles.nutrientLabel, { color: theme.colors.text }]}>
                {label}
              </Text>
              <View style={styles.nutrientInputContainer}>
                <TextInput
                  value={(foodData[key] || 0).toString()}
                  onChangeText={(text) => updateNutrient(key, text)}
                  mode="outlined"
                  style={styles.nutrientInput}
                  keyboardType="numeric"
                  editable={isEditing}
                  dense
                />
                <Text style={[styles.nutrientUnit, { color: theme.colors.text }]}>
                  {unit}
                </Text>
              </View>
            </View>
          ))}
        </Card.Content>
      </Card>

      {/* Ingredients */}
      {foodData.ingredients.length > 0 && (
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Ingredients
            </Text>
            <View style={styles.chipContainer}>
              {foodData.ingredients.map((ingredient, index) => (
                <Chip
                  key={index}
                  mode="outlined"
                  onClose={isEditing ? () => removeIngredient(index) : undefined}
                  style={styles.chip}
                >
                  {ingredient}
                </Chip>
              ))}
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Allergens */}
      {foodData.allergens.length > 0 && (
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Allergens
            </Text>
            <View style={styles.chipContainer}>
              {foodData.allergens.map((allergen, index) => (
                <Chip
                  key={index}
                  mode="flat"
                  icon="alert"
                  style={[styles.chip, { backgroundColor: theme.colors.errorContainer }]}
                  textStyle={{ color: theme.colors.error }}
                >
                  {allergen}
                </Chip>
              ))}
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Additives */}
      {foodData.additives.length > 0 && (
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Food Additives (E-numbers)
            </Text>
            <View style={styles.chipContainer}>
              {foodData.additives.map((additive, index) => (
                <Chip
                  key={index}
                  mode="outlined"
                  icon="chemistry-flask"
                  style={styles.chip}
                >
                  {additive}
                </Chip>
              ))}
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Raw OCR Text */}
      <Card style={styles.card}>
        <Card.Content>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Raw OCR Text
          </Text>
          <Surface style={[styles.rawTextContainer, { backgroundColor: theme.colors.surfaceVariant }]}>
            <Text style={[styles.rawText, { color: theme.colors.onSurfaceVariant }]}>
              {ocrData.rawText}
            </Text>
          </Surface>
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.button}
          disabled={isSaving}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.button}
          loading={isSaving}
          disabled={isSaving}
        >
          Save Food
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingVertical: 8,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confidenceContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  confidenceText: {
    fontSize: 12,
    marginBottom: 4,
  },
  confidenceBar: {
    height: 4,
    borderRadius: 2,
  },
  imageCard: {
    margin: 16,
    marginBottom: 8,
  },
  scannedImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginTop: 8,
  },
  card: {
    margin: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  input: {
    marginBottom: 12,
  },
  servingContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  servingInput: {
    flex: 2,
  },
  unitInput: {
    flex: 1,
  },
  nutrientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  nutrientLabel: {
    flex: 1,
    fontSize: 16,
  },
  nutrientInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  nutrientInput: {
    width: 80,
  },
  nutrientUnit: {
    fontSize: 14,
    minWidth: 30,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 4,
  },
  rawTextContainer: {
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  rawText: {
    fontSize: 12,
    lineHeight: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    gap: 16,
  },
  button: {
    flex: 1,
  },
});

export default OCRResultsScreen;
