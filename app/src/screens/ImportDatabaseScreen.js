/**
 * Import Database Screen for ZnüniZähler
 * Handles importing food databases
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Card, Text, Button, ProgressBar, List, Divider, ActivityIndicator, Checkbox } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import { importFoodDatabase, isFoodDatabaseImported } from '../services/databaseService';

/**
 * Import Database Screen Component
 * @param {Object} props - Component props
 * @param {Object} props.route - Route object
 * @param {Object} props.navigation - Navigation object
 * @returns {JSX.Element} - Import database screen component
 */
const ImportDatabaseScreen = ({ route, navigation }) => {
  const { theme } = useTheme();
  const { source } = route.params || { source: 'USDA' };

  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importStatus, setImportStatus] = useState('');
  const [importStats, setImportStats] = useState(null);
  const [isImported, setIsImported] = useState(false);
  const [error, setError] = useState('');

  // For importing both databases
  const [importBoth, setImportBoth] = useState(source === 'ALL');
  const [usdaImported, setUsdaImported] = useState(false);
  const [foodbImported, setFoodbImported] = useState(false);
  const [selectedDatabases, setSelectedDatabases] = useState({
    usda: true,
    foodb: true
  });

  // Check if databases are already imported
  useEffect(() => {
    const checkImportStatus = async () => {
      try {
        // Check USDA database
        const usdaStatus = await isFoodDatabaseImported('USDA');
        setUsdaImported(usdaStatus);

        // Check FoodB database
        const foodbStatus = await isFoodDatabaseImported('FoodB');
        setFoodbImported(foodbStatus);

        if (importBoth) {
          // For importing both, consider imported if both are imported
          setIsImported(usdaStatus && foodbStatus);
        } else {
          // For single database, check the specific source
          const imported = await isFoodDatabaseImported(source);
          setIsImported(imported);
        }
      } catch (error) {
        console.error('Error checking import status:', error);
      }
    };

    checkImportStatus();
  }, [source, importBoth]);

  // Handle import progress
  const handleImportProgress = (progress) => {
    setImportProgress(progress.progress);
    setImportStatus(progress.message);

    if (progress.stats) {
      setImportStats(progress.stats);
    }

    if (progress.status === 'error') {
      setError(progress.message);
      setIsImporting(false);
    }

    if (progress.status === 'complete') {
      setIsImporting(false);
      setIsImported(true);
    }
  };

  // Handle import button press
  const handleImport = async () => {
    try {
      setIsImporting(true);
      setImportProgress(0);
      setImportStatus('Preparing to import...');
      setError('');
      setImportStats(null);

      // Combined stats for both databases
      const combinedStats = {
        foods: 0,
        nutrients: 0,
        ingredients: 0,
        allergens: 0,
        errors: 0
      };

      if (importBoth) {
        // Import both databases if selected
        if (selectedDatabases.usda) {
          setImportStatus('Importing USDA database...');
          try {
            const usdaStats = await importFoodDatabase('usda_foods', progress => {
              // Scale progress to 0-50% for first database
              setImportProgress(progress.progress * 0.5);
              setImportStatus(progress.message);
            });

            // Add USDA stats to combined stats
            if (usdaStats) {
              Object.keys(combinedStats).forEach(key => {
                combinedStats[key] += usdaStats[key] || 0;
              });
            }
          } catch (error) {
            console.error('Error importing USDA database:', error);
            setError(`Error importing USDA database: ${error.message}`);
            // Continue with FoodB import even if USDA fails
          }
        }

        if (selectedDatabases.foodb) {
          setImportStatus('Importing FoodB database...');
          try {
            const foodbStats = await importFoodDatabase('foodb_foods', progress => {
              // Scale progress to 50-100% for second database
              setImportProgress(0.5 + (progress.progress * 0.5));
              setImportStatus(progress.message);
            });

            // Add FoodB stats to combined stats
            if (foodbStats) {
              Object.keys(combinedStats).forEach(key => {
                combinedStats[key] += foodbStats[key] || 0;
              });
            }
          } catch (error) {
            console.error('Error importing FoodB database:', error);
            setError(`Error importing FoodB database: ${error.message}`);
          }
        }

        // Set combined stats
        setImportStats(combinedStats);
        setImportProgress(1);
        setImportStatus('Import complete!');
        setIsImporting(false);

        // Check import status again
        const usdaStatus = await isFoodDatabaseImported('USDA');
        const foodbStatus = await isFoodDatabaseImported('FoodB');
        setUsdaImported(usdaStatus);
        setFoodbImported(foodbStatus);
        setIsImported(usdaStatus || foodbStatus);
      } else {
        // Import single database
        const databaseName = source === 'USDA' ? 'usda_foods' : 'foodb_foods';
        await importFoodDatabase(databaseName, handleImportProgress);
      }
    } catch (error) {
      console.error('Error importing database:', error);
      setError(`Error: ${error.message}`);
      setIsImporting(false);
    }
  };

  // Get database description
  const getDatabaseDescription = () => {
    if (importBoth || source === 'ALL') {
      return 'Import both the USDA Food Data Central and FoodB databases to access a comprehensive collection of food data. Having both databases will provide you with the most complete nutrition information.';
    } else if (source === 'USDA') {
      return 'The USDA Food Data Central database contains comprehensive nutrition information for thousands of foods, including standard reference foods, branded foods, and foundation foods.';
    } else {
      return 'The FoodB database is a comprehensive resource containing detailed information about food composition, including nutrients, compounds, and ingredients.';
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Card style={styles.card}>
        <Card.Title title={importBoth ? "Import Food Databases" : `Import ${source} Database`} />
        <Card.Content>
          <Text style={styles.description}>
            {getDatabaseDescription()}
          </Text>

          {importBoth && (
            <View style={styles.checkboxContainer}>
              <Text variant="titleMedium" style={styles.checkboxTitle}>
                Select databases to import:
              </Text>

              <View style={styles.checkboxRow}>
                <Checkbox
                  status={selectedDatabases.usda ? 'checked' : 'unchecked'}
                  onPress={() => setSelectedDatabases({
                    ...selectedDatabases,
                    usda: !selectedDatabases.usda
                  })}
                />
                <Text style={styles.checkboxLabel}>
                  USDA Food Database {usdaImported && '(Already imported)'}
                </Text>
              </View>

              <View style={styles.checkboxRow}>
                <Checkbox
                  status={selectedDatabases.foodb ? 'checked' : 'unchecked'}
                  onPress={() => setSelectedDatabases({
                    ...selectedDatabases,
                    foodb: !selectedDatabases.foodb
                  })}
                />
                <Text style={styles.checkboxLabel}>
                  FoodB Database {foodbImported && '(Already imported)'}
                </Text>
              </View>
            </View>
          )}

          {isImported && !isImporting && !importStats ? (
            <View style={styles.importedContainer}>
              <Text style={styles.importedText}>
                {importBoth
                  ? (usdaImported && foodbImported
                    ? "Both databases are already imported."
                    : (usdaImported
                      ? "USDA database is already imported."
                      : "FoodB database is already imported."))
                  : `${source} database is already imported.`
                }
              </Text>
              <Button
                mode="contained"
                onPress={() => navigation.goBack()}
                style={styles.button}
              >
                Go Back
              </Button>
              <Button
                mode="outlined"
                onPress={handleImport}
                style={styles.button}
              >
                {importBoth ? "Reimport Selected Databases" : "Reimport Database"}
              </Button>
            </View>
          ) : (
            <>
              {isImporting ? (
                <View style={styles.progressContainer}>
                  <Text style={styles.progressText}>{importStatus}</Text>
                  <ProgressBar
                    progress={importProgress}
                    color={theme.colors.primary}
                    style={styles.progressBar}
                  />
                  <ActivityIndicator
                    animating={true}
                    color={theme.colors.primary}
                    style={styles.spinner}
                  />
                </View>
              ) : (
                <Button
                  mode="contained"
                  onPress={handleImport}
                  style={styles.importButton}
                  disabled={isImporting || (importBoth && !selectedDatabases.usda && !selectedDatabases.foodb)}
                >
                  {importBoth
                    ? (isImported
                      ? 'Reimport Selected Databases'
                      : 'Import Selected Databases')
                    : (isImported
                      ? 'Reimport Database'
                      : 'Import Database')
                  }
                </Button>
              )}

              {error ? (
                <View style={[styles.errorContainer, { backgroundColor: theme.colors.errorContainer }]}>
                  <Text style={[styles.errorText, { color: theme.colors.error }]}>
                    {error}
                  </Text>
                </View>
              ) : null}
            </>
          )}

          {importStats && (
            <View style={styles.statsContainer}>
              <Text style={styles.statsTitle}>Import Results</Text>

              <List.Section>
                <List.Item
                  title="Foods Imported"
                  description={importStats.foods.toString()}
                  left={props => <List.Icon {...props} icon="food-apple" />}
                />
                <Divider />
                <List.Item
                  title="Nutrients Added"
                  description={importStats.nutrients.toString()}
                  left={props => <List.Icon {...props} icon="molecule" />}
                />
                <Divider />
                <List.Item
                  title="Ingredients Added"
                  description={importStats.ingredients.toString()}
                  left={props => <List.Icon {...props} icon="food-variant" />}
                />
                <Divider />
                <List.Item
                  title="Allergens Detected"
                  description={importStats.allergens.toString()}
                  left={props => <List.Icon {...props} icon="alert-circle" />}
                />
                <Divider />
                <List.Item
                  title="Errors"
                  description={importStats.errors.toString()}
                  left={props => <List.Icon {...props} icon="alert" color={importStats.errors > 0 ? theme.colors.error : theme.colors.text} />}
                />
              </List.Section>

              <Button
                mode="contained"
                onPress={() => navigation.navigate('FoodDatabase')}
                style={styles.doneButton}
              >
                View Foods
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.infoCard}>
        <Card.Title title={importBoth ? "About the Databases" : "About the Database"} />
        <Card.Content>
          {importBoth ? (
            <>
              <Text style={styles.infoText}>
                Importing both databases provides the most comprehensive food data:
              </Text>
              <List.Item
                title="USDA Food Data Central"
                description="Comprehensive nutrition information for thousands of foods"
                left={props => <List.Icon {...props} icon="database" />}
              />
              <List.Item
                title="FoodB Database"
                description="Detailed information about food composition, nutrients, and compounds"
                left={props => <List.Icon {...props} icon="database" />}
              />
              <Text style={styles.infoText}>
                Having both databases will provide you with the most complete nutrition information.
              </Text>
            </>
          ) : source === 'USDA' ? (
            <>
              <Text style={styles.infoText}>
                The USDA Food Data Central database includes:
              </Text>
              <List.Item
                title="Foundation Foods"
                description="Nutrient values derived from analyses, aggregated data, and imputed data"
                left={props => <List.Icon {...props} icon="database" />}
              />
              <List.Item
                title="SR Legacy"
                description="Standard Reference foods from the USDA National Nutrient Database"
                left={props => <List.Icon {...props} icon="database" />}
              />
              <List.Item
                title="Survey Foods (FNDDS)"
                description="Foods reported in the What We Eat in America dietary survey"
                left={props => <List.Icon {...props} icon="database" />}
              />
              <List.Item
                title="Branded Foods"
                description="Foods with brand names and nutrition facts labels"
                left={props => <List.Icon {...props} icon="database" />}
              />
            </>
          ) : (
            <>
              <Text style={styles.infoText}>
                The FoodB database includes:
              </Text>
              <List.Item
                title="Comprehensive Food Data"
                description="Detailed information about food composition, nutrients, and compounds"
                left={props => <List.Icon {...props} icon="database" />}
              />
              <List.Item
                title="Nutrient Information"
                description="Macronutrients, vitamins, minerals, and other nutritional components"
                left={props => <List.Icon {...props} icon="molecule" />}
              />
              <List.Item
                title="Ingredient Details"
                description="Information about food ingredients and their properties"
                left={props => <List.Icon {...props} icon="food-variant" />}
              />
              <List.Item
                title="Allergen Detection"
                description="Identification of common allergens in foods"
                left={props => <List.Icon {...props} icon="alert-circle" />}
              />
            </>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    margin: 16,
    elevation: 2,
  },
  infoCard: {
    margin: 16,
    marginTop: 0,
    elevation: 2,
  },
  description: {
    marginBottom: 16,
  },
  importButton: {
    marginTop: 16,
  },
  progressContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  progressText: {
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    width: '100%',
    borderRadius: 4,
  },
  spinner: {
    marginTop: 16,
  },
  errorContainer: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
  },
  errorText: {
    fontWeight: 'bold',
  },
  statsContainer: {
    marginTop: 24,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  doneButton: {
    marginTop: 16,
  },
  importedContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  importedText: {
    fontSize: 16,
    marginBottom: 16,
  },
  button: {
    marginTop: 8,
    width: '100%',
  },
  infoText: {
    marginBottom: 16,
  },
  checkboxContainer: {
    marginTop: 16,
    marginBottom: 16,
  },
  checkboxTitle: {
    marginBottom: 8,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkboxLabel: {
    marginLeft: 8,
  },
});

export default ImportDatabaseScreen;
