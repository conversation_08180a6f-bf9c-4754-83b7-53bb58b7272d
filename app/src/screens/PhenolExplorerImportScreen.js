/**
 * Phenol-Explorer Import Screen for ZnüniZähler
 * Allows users to import polyphenol data from the Phenol-Explorer database
 */

import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, Alert, Linking } from 'react-native';
import { Card, Text, Button, ProgressBar, List, Divider, Banner, Chip } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import PhenolExplorerImporter from '../utils/importers/PhenolExplorerImporter';

const PhenolExplorerImportScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importStats, setImportStats] = useState(null);
  const [error, setError] = useState('');
  
  // Create importer instance
  const importer = new PhenolExplorerImporter();
  
  // Handle import button press
  const handleImport = async () => {
    try {
      setIsImporting(true);
      setImportProgress(0.1);
      setError('');
      
      // Show information alert
      Alert.alert(
        'Phenol-Explorer Import',
        'You will be prompted to select a Phenol-Explorer dataset file (CSV or ZIP). ' +
        'You can download polyphenol data from the Phenol-Explorer website.\n\n' +
        'This will add antioxidant and polyphenol tracking capabilities to your app.',
        [{ text: 'OK', onPress: startImport }]
      );
    } catch (error) {
      setError(error.message);
      setIsImporting(false);
    }
  };
  
  // Start the import process
  const startImport = async () => {
    try {
      // Start import
      setImportProgress(0.2);
      
      const result = await importer.startImport();
      
      setImportProgress(1);
      setImportStats(result.stats);
      
      if (!result.success) {
        setError(result.error || 'Import failed');
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setIsImporting(false);
    }
  };
  
  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Card style={styles.card}>
        <Card.Title title="Phenol-Explorer Import" />
        <Card.Content>
          <Text style={styles.description}>
            Import polyphenol data from the Phenol-Explorer database to track antioxidants and bioactive compounds in your foods.
          </Text>
          
          <View style={styles.chipContainer}>
            <Chip icon="leaf" style={styles.chip}>Antioxidants</Chip>
            <Chip icon="heart" style={styles.chip}>Heart Health</Chip>
            <Chip icon="brain" style={styles.chip}>Brain Health</Chip>
          </View>
          
          <Button
            mode="contained"
            icon="database-import"
            onPress={handleImport}
            disabled={isImporting}
            style={styles.importButton}
          >
            Select Phenol-Explorer Data
          </Button>
          
          <Button
            mode="outlined"
            icon="web"
            onPress={() => Linking.openURL('http://phenol-explorer.eu/downloads')}
            style={styles.websiteButton}
          >
            Visit Phenol-Explorer Website
          </Button>
          
          {isImporting && (
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>Importing polyphenol data...</Text>
              <ProgressBar progress={importProgress} style={styles.progressBar} />
            </View>
          )}
          
          {error && (
            <Banner
              visible={true}
              actions={[
                {
                  label: 'Dismiss',
                  onPress: () => setError(''),
                },
              ]}
              icon="alert"
              style={styles.errorBanner}
            >
              {error}
            </Banner>
          )}
          
          {importStats && (
            <Card style={styles.statsCard}>
              <Card.Title title="Import Results" />
              <Card.Content>
                <List.Section>
                  <List.Item
                    title="Foods with Polyphenol Data"
                    description={`${importStats.foods} food items`}
                    left={props => <List.Icon {...props} icon="food-apple" />}
                  />
                  <List.Item
                    title="Total Polyphenol Values"
                    description={`${importStats.polyphenols} total values`}
                    left={props => <List.Icon {...props} icon="chart-line" />}
                  />
                  <List.Item
                    title="Individual Compounds"
                    description={`${importStats.compounds} specific polyphenols`}
                    left={props => <List.Icon {...props} icon="molecule" />}
                  />
                  {importStats.errors > 0 && (
                    <List.Item
                      title="Errors"
                      description={`${importStats.errors} items failed to import`}
                      left={props => <List.Icon {...props} icon="alert" />}
                    />
                  )}
                </List.Section>
              </Card.Content>
            </Card>
          )}
        </Card.Content>
      </Card>
      
      <Card style={styles.infoCard}>
        <Card.Title title="About Phenol-Explorer" />
        <Card.Content>
          <Text style={styles.infoText}>
            Phenol-Explorer is the first comprehensive database on polyphenol content in foods. It contains data on over 500 different polyphenols in more than 400 foods.
          </Text>
          
          <Divider style={styles.divider} />
          
          <List.Section>
            <List.Subheader>Polyphenol Classes</List.Subheader>
            <List.Item
              title="Flavonoids"
              description="Anthocyanins, flavanols, flavonols, isoflavones"
              left={props => <List.Icon {...props} icon="flower" />}
            />
            <List.Item
              title="Phenolic Acids"
              description="Hydroxybenzoic and hydroxycinnamic acids"
              left={props => <List.Icon {...props} icon="chemistry" />}
            />
            <List.Item
              title="Stilbenes"
              description="Resveratrol and derivatives"
              left={props => <List.Icon {...props} icon="grape" />}
            />
            <List.Item
              title="Lignans"
              description="Plant compounds with hormone-like effects"
              left={props => <List.Icon {...props} icon="leaf" />}
            />
          </List.Section>
        </Card.Content>
      </Card>
      
      <Card style={styles.benefitsCard}>
        <Card.Title title="Health Benefits" />
        <Card.Content>
          <List.Section>
            <List.Item
              title="Antioxidant Protection"
              description="Protect cells from oxidative stress and free radicals"
              left={props => <List.Icon {...props} icon="shield-check" />}
            />
            <List.Item
              title="Heart Health"
              description="Support cardiovascular function and blood flow"
              left={props => <List.Icon {...props} icon="heart" />}
            />
            <List.Item
              title="Brain Function"
              description="May support cognitive health and neuroprotection"
              left={props => <List.Icon {...props} icon="brain" />}
            />
            <List.Item
              title="Anti-inflammatory"
              description="Help reduce inflammation in the body"
              left={props => <List.Icon {...props} icon="medical-bag" />}
            />
          </List.Section>
        </Card.Content>
      </Card>
      
      <Card style={styles.helpCard}>
        <Card.Title title="How to Import" />
        <Card.Content>
          <List.Section>
            <List.Item
              title="1. Download Data"
              description="Visit Phenol-Explorer and download CSV data files"
              left={props => <List.Icon {...props} icon="download" />}
            />
            <List.Item
              title="2. Select File"
              description="Choose CSV file or ZIP archive containing polyphenol data"
              left={props => <List.Icon {...props} icon="file-document" />}
            />
            <List.Item
              title="3. Process Data"
              description="The app will extract and categorize polyphenol information"
              left={props => <List.Icon {...props} icon="cog" />}
            />
            <List.Item
              title="4. Track Antioxidants"
              description="Start tracking polyphenols and antioxidants in your diet"
              left={props => <List.Icon {...props} icon="chart-timeline-variant" />}
            />
          </List.Section>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  description: {
    marginBottom: 16,
    fontSize: 16,
    lineHeight: 24,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  chip: {
    marginRight: 8,
    marginBottom: 8,
  },
  importButton: {
    marginBottom: 8,
  },
  websiteButton: {
    marginBottom: 16,
  },
  progressContainer: {
    marginTop: 16,
  },
  progressText: {
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBar: {
    height: 8,
  },
  errorBanner: {
    marginTop: 16,
  },
  statsCard: {
    marginTop: 16,
  },
  infoCard: {
    marginBottom: 16,
  },
  infoText: {
    marginBottom: 16,
    fontSize: 14,
    lineHeight: 20,
  },
  divider: {
    marginVertical: 16,
  },
  benefitsCard: {
    marginBottom: 16,
  },
  helpCard: {
    marginBottom: 16,
  },
});

export default PhenolExplorerImportScreen;
