import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useTheme } from '../theme/ThemeContext';
import { getDailyNutritionSummary, getWeeklyNutritionSummary, getMonthlyNutritionSummary, getCurrentUser } from '../services/databaseService';
import { useTranslation } from '../hooks/useTranslation';

const StatisticsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t, getNutrientName, formatNutritionValue, getDayName } = useTranslation();
  const [selectedPeriod, setSelectedPeriod] = useState('week'); // 'week', 'month', 'year'
  const [nutritionData, setNutritionData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState(null);

  const screenWidth = Dimensions.get('window').width;

  useEffect(() => {
    loadUserAndData();
  }, [selectedPeriod]);

  const loadUserAndData = async () => {
    try {
      setIsLoading(true);
      const user = await getCurrentUser();
      if (user) {
        setUserId(user.id);
        await loadNutritionData(user.id);
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadNutritionData = async (userId) => {
    try {
      let data;
      const today = new Date().toISOString().split('T')[0];

      switch (selectedPeriod) {
        case 'week':
          data = await getWeeklyNutritionSummary(userId, today);
          break;
        case 'month':
          data = await getMonthlyNutritionSummary(userId, today);
          break;
        default:
          data = await getDailyNutritionSummary(userId, today);
      }

      setNutritionData(data);
    } catch (error) {
      console.error('Error loading nutrition data:', error);
      // Set mock data for demonstration
      setNutritionData(getMockData());
    }
  };

  const getMockData = () => ({
    calories: 1850,
    protein: 75,
    carbs: 220,
    fat: 65,
    fiber: 28,
    sugar: 45,
    sodium: 1200,
    weeklyData: {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      datasets: [{
        data: [1800, 2100, 1950, 1750, 2200, 1900, 1850]
      }]
    },
    macroDistribution: [
      { name: 'Protein', population: 20, color: '#FF6384', legendFontColor: '#333' },
      { name: 'Carbs', population: 50, color: '#36A2EB', legendFontColor: '#333' },
      { name: 'Fat', population: 30, color: '#FFCE56', legendFontColor: '#333' }
    ]
  });

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {['week', 'month', 'year'].map((period) => (
        <TouchableOpacity
          key={period}
          style={[
            styles.periodButton,
            selectedPeriod === period && styles.periodButtonActive
          ]}
          onPress={() => setSelectedPeriod(period)}
        >
          <Text style={[
            styles.periodButtonText,
            selectedPeriod === period && styles.periodButtonTextActive
          ]}>
            {t(`statistics.${period}`)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderCalorieChart = () => {
    if (!nutritionData?.weeklyData) return null;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>{t('statistics.dailyCalories')}</Text>
        <LineChart
          data={nutritionData.weeklyData}
          width={screenWidth - 40}
          height={220}
          chartConfig={{
            backgroundColor: '#ffffff',
            backgroundGradientFrom: '#ffffff',
            backgroundGradientTo: '#ffffff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
            labelColor: (opacity = 1) => '#333333',
            style: {
              borderRadius: 16
            },
            propsForDots: {
              r: "6",
              strokeWidth: "2",
              stroke: "#4CAF50"
            }
          }}
          bezier
          style={styles.chart}
        />
      </View>
    );
  };

  const renderMacroChart = () => {
    if (!nutritionData?.macroDistribution) return null;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>{t('statistics.macronutrientDistribution')}</Text>
        <PieChart
          data={nutritionData.macroDistribution}
          width={screenWidth - 40}
          height={220}
          chartConfig={{
            color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
          }}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="15"
          absolute
          style={styles.chart}
        />
      </View>
    );
  };

  const renderNutritionSummary = () => {
    if (!nutritionData) return null;

    const nutrients = [
      { name: getNutrientName('calories'), value: nutritionData.calories, unit: 'kcal', icon: 'local-fire-department', color: '#FF6384' },
      { name: getNutrientName('protein'), value: nutritionData.protein, unit: 'g', icon: 'fitness-center', color: '#36A2EB' },
      { name: getNutrientName('carbs'), value: nutritionData.carbs, unit: 'g', icon: 'grain', color: '#FFCE56' },
      { name: getNutrientName('fat'), value: nutritionData.fat, unit: 'g', icon: 'opacity', color: '#4BC0C0' },
      { name: getNutrientName('fiber'), value: nutritionData.fiber, unit: 'g', icon: 'eco', color: '#9966FF' },
      { name: getNutrientName('sugar'), value: nutritionData.sugar, unit: 'g', icon: 'cake', color: '#FF9F40' },
    ];

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.sectionTitle}>{t('statistics.nutritionSummary')}</Text>
        <View style={styles.nutrientGrid}>
          {nutrients.map((nutrient, index) => (
            <View key={index} style={styles.nutrientCard}>
              <MaterialIcons name={nutrient.icon} size={24} color={nutrient.color} />
              <Text style={styles.nutrientValue}>{nutrient.value}</Text>
              <Text style={styles.nutrientUnit}>{nutrient.unit}</Text>
              <Text style={styles.nutrientName}>{nutrient.name}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>{t('statistics.loading')}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{t('statistics.title')}</Text>
        <Text style={styles.headerSubtitle}>{t('statistics.subtitle')}</Text>
      </View>

      {renderPeriodSelector()}
      {renderNutritionSummary()}
      {renderCalorieChart()}
      {renderMacroChart()}
    </ScrollView>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    centered: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    header: {
      padding: 20,
      backgroundColor: '#4CAF50',
    },
    headerTitle: {
      fontSize: 22,
      fontWeight: 'bold',
      color: '#fff',
      marginBottom: 5,
    },
    headerSubtitle: {
      fontSize: 16,
      color: 'rgba(255,255,255,0.8)',
    },
    loadingText: {
      fontSize: 16,
      color: '#666',
      textAlign: 'center',
    },
    periodSelector: {
      flexDirection: 'row',
      backgroundColor: '#fff',
      margin: 15,
      borderRadius: 25,
      padding: 5,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    periodButton: {
      flex: 1,
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderRadius: 20,
      alignItems: 'center',
    },
    periodButtonActive: {
      backgroundColor: '#4CAF50',
    },
    periodButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: '#666',
    },
    periodButtonTextActive: {
      color: '#fff',
    },
    summaryContainer: {
      backgroundColor: '#fff',
      margin: 15,
      borderRadius: 15,
      padding: 20,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#333',
      marginBottom: 15,
    },
    nutrientGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    nutrientCard: {
      width: '48%',
      backgroundColor: '#f8f9fa',
      borderRadius: 12,
      padding: 15,
      alignItems: 'center',
      marginBottom: 10,
    },
    nutrientValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#333',
      marginTop: 8,
    },
    nutrientUnit: {
      fontSize: 12,
      color: '#666',
      marginTop: 2,
    },
    nutrientName: {
      fontSize: 14,
      color: '#333',
      marginTop: 4,
      textAlign: 'center',
    },
    chartContainer: {
      backgroundColor: '#fff',
      margin: 15,
      borderRadius: 15,
      padding: 20,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    chartTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#333',
      marginBottom: 15,
      textAlign: 'center',
    },
    chart: {
      borderRadius: 16,
    },
  });
};

export default StatisticsScreen;
