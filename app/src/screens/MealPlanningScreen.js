/**
 * Meal Planning Screen for ZnüniZähler
 * Advanced meal planning with AI-powered suggestions and recipe management
 */

import React, { useState, useEffect } from 'react';
import { 
  View, 
  ScrollView, 
  StyleSheet, 
  Dimensions,
  Alert 
} from 'react-native';
import { 
  Text, 
  Card, 
  Surface, 
  Button, 
  Chip, 
  IconButton,
  FAB,
  Portal,
  Modal,
  TextInput,
  Divider,
  ActivityIndicator,
  ProgressBar
} from 'react-native-paper';
import { Calendar } from 'react-native-calendars';
import { useTheme } from '../theme/ThemeProvider';
import smartMealPlanningService from '../services/smartMealPlanningService';
import recipeManagementService from '../services/recipeManagementService';
import { trackScreenView, trackUserAction } from '../services/analyticsService';

const { width: screenWidth } = Dimensions.get('window');

const MealPlanningScreen = ({ navigation }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [planningOptions, setPlanningOptions] = useState({
    duration: 7,
    includeSnacks: true,
    cookingTime: 'medium',
    skillLevel: 'intermediate',
    varietyLevel: 'high'
  });
  const [showOptionsModal, setShowOptionsModal] = useState(false);
  const [generatingPlan, setGeneratingPlan] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [showMealModal, setShowMealModal] = useState(false);

  useEffect(() => {
    trackScreenView('Meal Planning');
    initializeMealPlanning();
  }, []);

  const initializeMealPlanning = async () => {
    try {
      setLoading(true);
      
      // Initialize services
      await Promise.all([
        smartMealPlanningService.initialize(),
        recipeManagementService.initialize()
      ]);

      // Load existing meal plan if available
      await loadCurrentMealPlan();
      
    } catch (error) {
      console.error('Error initializing meal planning:', error);
      Alert.alert('Error', 'Failed to initialize meal planning. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentMealPlan = async () => {
    try {
      // Check if there's an active meal plan for the current week
      // This would typically load from database
      // For now, we'll set to null to show the generation interface
      setCurrentPlan(null);
    } catch (error) {
      console.error('Error loading current meal plan:', error);
    }
  };

  const generateNewMealPlan = async () => {
    try {
      setGeneratingPlan(true);
      
      const options = {
        ...planningOptions,
        startDate: new Date(selectedDate),
        dietaryRestrictions: [], // Would come from user preferences
        preferredCuisines: [], // Would come from user preferences
        mealTypes: ['breakfast', 'lunch', 'dinner']
      };

      const result = await smartMealPlanningService.generateMealPlan(options);
      
      if (result.success) {
        setCurrentPlan(result.mealPlan);
        trackUserAction('meal_plan_generated', 'meal_planning', {
          duration: options.duration,
          includeSnacks: options.includeSnacks
        });
        Alert.alert('Success', 'Your meal plan has been generated!');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      Alert.alert('Error', 'Failed to generate meal plan. Please try again.');
    } finally {
      setGeneratingPlan(false);
    }
  };

  const handleMealPress = (meal, date, mealType) => {
    setSelectedMeal({ meal, date, mealType });
    setShowMealModal(true);
  };

  const handleDateSelect = (day) => {
    setSelectedDate(day.dateString);
  };

  const getMarkedDates = () => {
    if (!currentPlan) return {};
    
    const marked = {};
    currentPlan.dailyPlans.forEach(day => {
      marked[day.date] = {
        marked: true,
        dotColor: theme.colors.primary,
        selectedColor: theme.colors.primary
      };
    });
    
    marked[selectedDate] = {
      ...marked[selectedDate],
      selected: true,
      selectedColor: theme.colors.primary
    };
    
    return marked;
  };

  const getSelectedDayPlan = () => {
    if (!currentPlan) return null;
    return currentPlan.dailyPlans.find(day => day.date === selectedDate);
  };

  const PlanningOptionsModal = () => (
    <Portal>
      <Modal
        visible={showOptionsModal}
        onDismiss={() => setShowOptionsModal(false)}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
          Meal Planning Options
        </Text>
        
        <View style={styles.optionGroup}>
          <Text style={[styles.optionLabel, { color: theme.colors.text }]}>
            Plan Duration
          </Text>
          <View style={styles.chipGroup}>
            {[3, 7, 14].map(duration => (
              <Chip
                key={duration}
                mode={planningOptions.duration === duration ? 'flat' : 'outlined'}
                onPress={() => setPlanningOptions(prev => ({ ...prev, duration }))}
                style={styles.optionChip}
              >
                {duration} days
              </Chip>
            ))}
          </View>
        </View>

        <View style={styles.optionGroup}>
          <Text style={[styles.optionLabel, { color: theme.colors.text }]}>
            Cooking Time Preference
          </Text>
          <View style={styles.chipGroup}>
            {['quick', 'medium', 'elaborate'].map(time => (
              <Chip
                key={time}
                mode={planningOptions.cookingTime === time ? 'flat' : 'outlined'}
                onPress={() => setPlanningOptions(prev => ({ ...prev, cookingTime: time }))}
                style={styles.optionChip}
              >
                {time.charAt(0).toUpperCase() + time.slice(1)}
              </Chip>
            ))}
          </View>
        </View>

        <View style={styles.optionGroup}>
          <Text style={[styles.optionLabel, { color: theme.colors.text }]}>
            Variety Level
          </Text>
          <View style={styles.chipGroup}>
            {['low', 'medium', 'high'].map(variety => (
              <Chip
                key={variety}
                mode={planningOptions.varietyLevel === variety ? 'flat' : 'outlined'}
                onPress={() => setPlanningOptions(prev => ({ ...prev, varietyLevel: variety }))}
                style={styles.optionChip}
              >
                {variety.charAt(0).toUpperCase() + variety.slice(1)}
              </Chip>
            ))}
          </View>
        </View>

        <View style={styles.modalActions}>
          <Button
            mode="outlined"
            onPress={() => setShowOptionsModal(false)}
            style={styles.modalButton}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={() => {
              setShowOptionsModal(false);
              generateNewMealPlan();
            }}
            style={styles.modalButton}
          >
            Generate Plan
          </Button>
        </View>
      </Modal>
    </Portal>
  );

  const MealDetailModal = () => (
    <Portal>
      <Modal
        visible={showMealModal}
        onDismiss={() => setShowMealModal(false)}
        contentContainerStyle={[
          styles.mealModalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        {selectedMeal && (
          <>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              {selectedMeal.meal.name}
            </Text>
            
            <Text style={[styles.mealDescription, { color: theme.colors.onSurfaceVariant }]}>
              {selectedMeal.meal.description}
            </Text>

            <Divider style={styles.divider} />

            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Ingredients
            </Text>
            {selectedMeal.meal.ingredients?.map((ingredient, index) => (
              <Text key={index} style={[styles.ingredientText, { color: theme.colors.onSurfaceVariant }]}>
                • {ingredient.amount} {ingredient.unit} {ingredient.name}
              </Text>
            ))}

            <Divider style={styles.divider} />

            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Instructions
            </Text>
            {selectedMeal.meal.instructions?.map((instruction, index) => (
              <Text key={index} style={[styles.instructionText, { color: theme.colors.onSurfaceVariant }]}>
                {index + 1}. {instruction}
              </Text>
            ))}

            {selectedMeal.meal.nutrition && (
              <>
                <Divider style={styles.divider} />
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                  Nutrition (per serving)
                </Text>
                <View style={styles.nutritionGrid}>
                  <Text style={[styles.nutritionText, { color: theme.colors.onSurfaceVariant }]}>
                    Calories: {selectedMeal.meal.nutrition.calories}
                  </Text>
                  <Text style={[styles.nutritionText, { color: theme.colors.onSurfaceVariant }]}>
                    Protein: {selectedMeal.meal.nutrition.protein}g
                  </Text>
                  <Text style={[styles.nutritionText, { color: theme.colors.onSurfaceVariant }]}>
                    Carbs: {selectedMeal.meal.nutrition.carbs}g
                  </Text>
                  <Text style={[styles.nutritionText, { color: theme.colors.onSurfaceVariant }]}>
                    Fat: {selectedMeal.meal.nutrition.fat}g
                  </Text>
                </View>
              </>
            )}

            <View style={styles.mealModalActions}>
              <Button
                mode="outlined"
                onPress={() => setShowMealModal(false)}
                style={styles.modalButton}
              >
                Close
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  setShowMealModal(false);
                  // Navigate to cooking mode or add to shopping list
                }}
                style={styles.modalButton}
              >
                Cook This
              </Button>
            </View>
          </>
        )}
      </Modal>
    </Portal>
  );

  const DayPlanView = ({ dayPlan }) => (
    <Card style={styles.dayPlanCard}>
      <Card.Content>
        <View style={styles.dayHeader}>
          <Text style={[styles.dayTitle, { color: theme.colors.text }]}>
            {dayPlan.dayOfWeek}
          </Text>
          <Text style={[styles.dayDate, { color: theme.colors.onSurfaceVariant }]}>
            {new Date(dayPlan.date).toLocaleDateString()}
          </Text>
        </View>

        {Object.entries(dayPlan.meals).map(([mealType, meal]) => {
          if (Array.isArray(meal)) {
            // Handle snacks array
            return (
              <View key={mealType} style={styles.mealSection}>
                <Text style={[styles.mealTypeTitle, { color: theme.colors.text }]}>
                  {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                </Text>
                {meal.map((snack, index) => (
                  <Surface
                    key={index}
                    style={[styles.mealCard, { backgroundColor: theme.colors.surfaceVariant }]}
                    onTouchEnd={() => handleMealPress(snack, dayPlan.date, mealType)}
                  >
                    <Text style={[styles.mealName, { color: theme.colors.text }]}>
                      {snack.name}
                    </Text>
                    <Text style={[styles.mealTime, { color: theme.colors.onSurfaceVariant }]}>
                      {snack.prepTime} min prep
                    </Text>
                  </Surface>
                ))}
              </View>
            );
          } else {
            // Handle single meal
            return (
              <View key={mealType} style={styles.mealSection}>
                <Text style={[styles.mealTypeTitle, { color: theme.colors.text }]}>
                  {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                </Text>
                <Surface
                  style={[styles.mealCard, { backgroundColor: theme.colors.surfaceVariant }]}
                  onTouchEnd={() => handleMealPress(meal, dayPlan.date, mealType)}
                >
                  <Text style={[styles.mealName, { color: theme.colors.text }]}>
                    {meal.name}
                  </Text>
                  <Text style={[styles.mealTime, { color: theme.colors.onSurfaceVariant }]}>
                    {meal.prepTime + meal.cookTime} min total
                  </Text>
                  <View style={styles.mealTags}>
                    <Chip mode="outlined" compact style={styles.difficultyChip}>
                      {meal.difficulty}
                    </Chip>
                    {meal.tags?.slice(0, 2).map((tag, index) => (
                      <Chip key={index} mode="outlined" compact style={styles.tagChip}>
                        {tag}
                      </Chip>
                    ))}
                  </View>
                </Surface>
              </View>
            );
          }
        })}

        {dayPlan.dailyNutrition && (
          <View style={styles.nutritionSummary}>
            <Text style={[styles.nutritionTitle, { color: theme.colors.text }]}>
              Daily Nutrition
            </Text>
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: theme.colors.onSurfaceVariant }]}>
                Calories: {Math.round(dayPlan.dailyNutrition.calories)}
              </Text>
              <Text style={[styles.nutritionLabel, { color: theme.colors.onSurfaceVariant }]}>
                Protein: {Math.round(dayPlan.dailyNutrition.protein)}g
              </Text>
            </View>
          </View>
        )}
      </Card.Content>
    </Card>
  );

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          Initializing meal planning...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.text}
            onPress={() => navigation.goBack()}
          />
          <View style={styles.headerInfo}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Meal Planning
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              AI-powered meal suggestions
            </Text>
          </View>
          <IconButton
            icon="cog"
            size={24}
            iconColor={theme.colors.text}
            onPress={() => setShowOptionsModal(true)}
          />
        </View>
      </Surface>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {!currentPlan ? (
          // No meal plan - show generation interface
          <View style={styles.emptyState}>
            <Card style={styles.welcomeCard}>
              <Card.Content>
                <Text style={[styles.welcomeTitle, { color: theme.colors.text }]}>
                  Welcome to Smart Meal Planning
                </Text>
                <Text style={[styles.welcomeText, { color: theme.colors.onSurfaceVariant }]}>
                  Let our AI create a personalized meal plan based on your preferences, 
                  dietary needs, and nutrition goals.
                </Text>
                
                <View style={styles.featureList}>
                  <Text style={[styles.featureItem, { color: theme.colors.onSurfaceVariant }]}>
                    🎯 Personalized to your nutrition goals
                  </Text>
                  <Text style={[styles.featureItem, { color: theme.colors.onSurfaceVariant }]}>
                    🛒 Automatic shopping list generation
                  </Text>
                  <Text style={[styles.featureItem, { color: theme.colors.onSurfaceVariant }]}>
                    👨‍🍳 Step-by-step cooking instructions
                  </Text>
                  <Text style={[styles.featureItem, { color: theme.colors.onSurfaceVariant }]}>
                    📊 Complete nutrition analysis
                  </Text>
                </View>

                <Button
                  mode="contained"
                  onPress={() => setShowOptionsModal(true)}
                  style={styles.generateButton}
                  loading={generatingPlan}
                  disabled={generatingPlan}
                >
                  {generatingPlan ? 'Generating Plan...' : 'Create Meal Plan'}
                </Button>
              </Card.Content>
            </Card>
          </View>
        ) : (
          // Show existing meal plan
          <>
            {/* Calendar */}
            <Card style={styles.calendarCard}>
              <Card.Content>
                <Calendar
                  current={selectedDate}
                  onDayPress={handleDateSelect}
                  markedDates={getMarkedDates()}
                  theme={{
                    backgroundColor: theme.colors.surface,
                    calendarBackground: theme.colors.surface,
                    textSectionTitleColor: theme.colors.text,
                    selectedDayBackgroundColor: theme.colors.primary,
                    selectedDayTextColor: theme.colors.onPrimary,
                    todayTextColor: theme.colors.primary,
                    dayTextColor: theme.colors.text,
                    textDisabledColor: theme.colors.onSurfaceVariant,
                    dotColor: theme.colors.primary,
                    selectedDotColor: theme.colors.onPrimary,
                    arrowColor: theme.colors.primary,
                    monthTextColor: theme.colors.text,
                    indicatorColor: theme.colors.primary
                  }}
                />
              </Card.Content>
            </Card>

            {/* Selected Day Plan */}
            {getSelectedDayPlan() && (
              <DayPlanView dayPlan={getSelectedDayPlan()} />
            )}

            {/* Plan Summary */}
            {currentPlan.nutritionSummary && (
              <Card style={styles.summaryCard}>
                <Card.Content>
                  <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                    Plan Summary
                  </Text>
                  <View style={styles.summaryGrid}>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryLabel, { color: theme.colors.onSurfaceVariant }]}>
                        Avg Daily Calories
                      </Text>
                      <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
                        {currentPlan.nutritionSummary.averageDaily.calories}
                      </Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryLabel, { color: theme.colors.onSurfaceVariant }]}>
                        Avg Daily Protein
                      </Text>
                      <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
                        {currentPlan.nutritionSummary.averageDaily.protein}g
                      </Text>
                    </View>
                  </View>
                </Card.Content>
              </Card>
            )}

            {/* Quick Actions */}
            <Card style={styles.actionsCard}>
              <Card.Content>
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                  Quick Actions
                </Text>
                <View style={styles.actionsGrid}>
                  <Button
                    mode="contained-tonal"
                    icon="cart"
                    onPress={() => navigation.navigate('ShoppingList', { 
                      mealPlan: currentPlan 
                    })}
                    style={styles.actionButton}
                  >
                    Shopping List
                  </Button>
                  <Button
                    mode="contained-tonal"
                    icon="chef-hat"
                    onPress={() => navigation.navigate('MealPrep', { 
                      mealPlan: currentPlan 
                    })}
                    style={styles.actionButton}
                  >
                    Meal Prep
                  </Button>
                </View>
              </Card.Content>
            </Card>
          </>
        )}
      </ScrollView>

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => setShowOptionsModal(true)}
        label={currentPlan ? "New Plan" : "Create Plan"}
      />

      {/* Modals */}
      <PlanningOptionsModal />
      <MealDetailModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    paddingTop: 40,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
  },
  content: {
    flex: 1,
  },
  emptyState: {
    padding: 16,
  },
  welcomeCard: {
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  welcomeText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  featureList: {
    marginBottom: 24,
  },
  featureItem: {
    fontSize: 16,
    marginBottom: 8,
    paddingLeft: 8,
  },
  generateButton: {
    marginTop: 8,
  },
  calendarCard: {
    margin: 16,
    marginBottom: 8,
  },
  dayPlanCard: {
    margin: 16,
    marginBottom: 8,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  dayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  dayDate: {
    fontSize: 14,
  },
  mealSection: {
    marginBottom: 16,
  },
  mealTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  mealCard: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  mealTime: {
    fontSize: 14,
    marginBottom: 8,
  },
  mealTags: {
    flexDirection: 'row',
    gap: 4,
  },
  difficultyChip: {
    height: 24,
  },
  tagChip: {
    height: 24,
  },
  nutritionSummary: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  nutritionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionLabel: {
    fontSize: 12,
  },
  summaryCard: {
    margin: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  actionsCard: {
    margin: 16,
    marginBottom: 80,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  mealModalContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 8,
    maxHeight: '90%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  optionGroup: {
    marginBottom: 20,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  chipGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionChip: {
    marginBottom: 4,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  mealModalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  mealDescription: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  ingredientText: {
    fontSize: 14,
    marginBottom: 4,
  },
  instructionText: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  nutritionText: {
    fontSize: 14,
    minWidth: '45%',
  },
});

export default MealPlanningScreen;
