/**
 * Canadian Nutrient File (CNF) Import Screen for ZnüniZähler
 * Allows users to import food data from the Canadian Nutrient File database
 */

import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, Alert, Linking } from 'react-native';
import { Card, Text, Button, ProgressBar, List, Divider, Banner } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import CNFImporter from '../utils/importers/CNFImporter';

const CNFImportScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importStats, setImportStats] = useState(null);
  const [error, setError] = useState('');
  
  // Create importer instance
  const importer = new CNFImporter();
  
  // Handle import button press
  const handleImport = async () => {
    try {
      setIsImporting(true);
      setImportProgress(0.1);
      setError('');
      
      // Show information alert
      Alert.alert(
        'Canadian Nutrient File Import',
        'You will be prompted to select a CNF dataset ZIP file. ' +
        'You can download the dataset from the Health Canada website.\n\n' +
        'The import process may take several minutes depending on the dataset size.',
        [{ text: 'OK', onPress: startImport }]
      );
    } catch (error) {
      setError(error.message);
      setIsImporting(false);
    }
  };
  
  // Start the import process
  const startImport = async () => {
    try {
      // Start import
      setImportProgress(0.2);
      
      const result = await importer.startImport();
      
      setImportProgress(1);
      setImportStats(result.stats);
      
      if (!result.success) {
        setError(result.error || 'Import failed');
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setIsImporting(false);
    }
  };
  
  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Card style={styles.card}>
        <Card.Title title="Canadian Nutrient File Import" />
        <Card.Content>
          <Text style={styles.description}>
            Import food data from the Canadian Nutrient File (CNF) database. The CNF is Canada's standard reference food composition database.
          </Text>
          
          <Button
            mode="contained"
            icon="database-import"
            onPress={handleImport}
            disabled={isImporting}
            style={styles.importButton}
          >
            Select CNF Dataset
          </Button>
          
          <Button
            mode="outlined"
            icon="web"
            onPress={() => Linking.openURL('https://www.canada.ca/en/health-canada/services/food-nutrition/healthy-eating/nutrient-data/canadian-nutrient-file-2015-download-files.html')}
            style={styles.websiteButton}
          >
            Visit Health Canada Website
          </Button>
          
          {isImporting && (
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>Importing CNF data...</Text>
              <ProgressBar progress={importProgress} style={styles.progressBar} />
            </View>
          )}
          
          {error && (
            <Banner
              visible={true}
              actions={[
                {
                  label: 'Dismiss',
                  onPress: () => setError(''),
                },
              ]}
              icon="alert"
              style={styles.errorBanner}
            >
              {error}
            </Banner>
          )}
          
          {importStats && (
            <Card style={styles.statsCard}>
              <Card.Title title="Import Results" />
              <Card.Content>
                <List.Section>
                  <List.Item
                    title="Foods Imported"
                    description={`${importStats.foods} food items`}
                    left={props => <List.Icon {...props} icon="food-apple" />}
                  />
                  <List.Item
                    title="Nutrients Added"
                    description={`${importStats.nutrients} nutrient values`}
                    left={props => <List.Icon {...props} icon="nutrition" />}
                  />
                  <List.Item
                    title="Allergens Detected"
                    description={`${importStats.allergens} allergen warnings`}
                    left={props => <List.Icon {...props} icon="alert-circle" />}
                  />
                  {importStats.errors > 0 && (
                    <List.Item
                      title="Errors"
                      description={`${importStats.errors} items failed to import`}
                      left={props => <List.Icon {...props} icon="alert" />}
                    />
                  )}
                </List.Section>
              </Card.Content>
            </Card>
          )}
        </Card.Content>
      </Card>
      
      <Card style={styles.infoCard}>
        <Card.Title title="About Canadian Nutrient File" />
        <Card.Content>
          <Text style={styles.infoText}>
            The Canadian Nutrient File (CNF) is a comprehensive bilingual food composition database reporting the amount of nutrients in foods commonly consumed in Canada.
          </Text>
          
          <Divider style={styles.divider} />
          
          <List.Section>
            <List.Subheader>Key Features</List.Subheader>
            <List.Item
              title="Government Verified"
              description="Official data from Health Canada"
              left={props => <List.Icon {...props} icon="shield-check" />}
            />
            <List.Item
              title="Canadian Foods"
              description="Foods commonly consumed in Canada"
              left={props => <List.Icon {...props} icon="flag" />}
            />
            <List.Item
              title="Comprehensive Nutrients"
              description="Macronutrients, vitamins, and minerals"
              left={props => <List.Icon {...props} icon="chart-line" />}
            />
            <List.Item
              title="Bilingual Support"
              description="Available in English and French"
              left={props => <List.Icon {...props} icon="translate" />}
            />
          </List.Section>
        </Card.Content>
      </Card>
      
      <Card style={styles.helpCard}>
        <Card.Title title="How to Import" />
        <Card.Content>
          <List.Section>
            <List.Item
              title="1. Download Dataset"
              description="Visit the Health Canada website and download the CNF 2015 ZIP file"
              left={props => <List.Icon {...props} icon="download" />}
            />
            <List.Item
              title="2. Select File"
              description="Tap 'Select CNF Dataset' and choose the downloaded ZIP file"
              left={props => <List.Icon {...props} icon="file-document" />}
            />
            <List.Item
              title="3. Wait for Import"
              description="The import process will automatically extract and process the data"
              left={props => <List.Icon {...props} icon="clock" />}
            />
            <List.Item
              title="4. View Results"
              description="Check the import statistics to see what was added to your database"
              left={props => <List.Icon {...props} icon="chart-bar" />}
            />
          </List.Section>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  description: {
    marginBottom: 16,
    fontSize: 16,
    lineHeight: 24,
  },
  importButton: {
    marginBottom: 8,
  },
  websiteButton: {
    marginBottom: 16,
  },
  progressContainer: {
    marginTop: 16,
  },
  progressText: {
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBar: {
    height: 8,
  },
  errorBanner: {
    marginTop: 16,
  },
  statsCard: {
    marginTop: 16,
  },
  infoCard: {
    marginBottom: 16,
  },
  infoText: {
    marginBottom: 16,
    fontSize: 14,
    lineHeight: 20,
  },
  divider: {
    marginVertical: 16,
  },
  helpCard: {
    marginBottom: 16,
  },
});

export default CNFImportScreen;
