/**
 * Food Database Screen for ZnüniZähler
 * Displays foods from imported databases (USDA and FoodB)
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, FlatList, ActivityIndicator } from 'react-native';
import { Card, Text, Chip, Searchbar, Button, Divider, SegmentedButtons, Banner } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import { isFoodDatabaseImported, getFoodsBySource } from '../services/databaseService';
import FoodItem from '../components/FoodItem';
import { useTranslation } from '../hooks/useTranslation';

/**
 * Food Database Screen Component
 * @param {Object} props - Component props
 * @param {Object} props.navigation - Navigation object
 * @returns {JSX.Element} - Food database screen component
 */
const FoodDatabaseScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [selectedSource, setSelectedSource] = useState('USDA');
  const [isLoading, setIsLoading] = useState(true);
  const [foods, setFoods] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isImported, setIsImported] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const PAGE_SIZE = 20;

  // Check if database is imported and load foods
  useEffect(() => {
    const checkImportStatus = async () => {
      try {
        setIsLoading(true);

        // Check if database is imported
        const imported = await isFoodDatabaseImported(selectedSource);
        setIsImported(imported);

        if (imported) {
          // Load foods
          const foodData = await getFoodsBySource(selectedSource, PAGE_SIZE, 0);
          setFoods(foodData);
          setHasMore(foodData.length === PAGE_SIZE);
        }
      } catch (error) {
        console.error('Error checking import status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkImportStatus();
  }, [selectedSource]);

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  // Handle load more
  const handleLoadMore = async () => {
    if (!hasMore || isLoading) return;

    try {
      setIsLoading(true);

      const nextPage = page + 1;
      const offset = (nextPage - 1) * PAGE_SIZE;
      const newFoods = await getFoodsBySource(selectedSource, PAGE_SIZE, offset);

      if (newFoods.length > 0) {
        setFoods([...foods, ...newFoods]);
        setPage(nextPage);
        setHasMore(newFoods.length === PAGE_SIZE);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading more foods:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);

      // Reset pagination
      setPage(1);

      // Load first page
      const foodData = await getFoodsBySource(selectedSource, PAGE_SIZE, 0);
      setFoods(foodData);
      setHasMore(foodData.length === PAGE_SIZE);
    } catch (error) {
      console.error('Error refreshing foods:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle select food
  const handleSelectFood = (food) => {
    navigation.navigate('FoodDetail', { food });
  };

  // Handle import database
  const handleImportDatabase = () => {
    navigation.navigate('ImportDatabase', { source: selectedSource });
  };

  // Filter foods by search query
  const filteredFoods = searchQuery
    ? foods.filter(food =>
      food.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (food.description && food.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (food.brand_name && food.brand_name.toLowerCase().includes(searchQuery.toLowerCase()))
    )
    : foods;

  // Render food item
  const renderFoodItem = ({ item }) => (
    <FoodItem
      food={item}
      onPress={() => handleSelectFood(item)}
    />
  );

  // Render footer
  const renderFooter = () => {
    if (!isLoading) return null;

    return (
      <View style={styles.footerContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <Text style={styles.footerText}>{t('food.loadingMoreFoods')}</Text>
      </View>
    );
  };

  // Render empty component
  const renderEmpty = () => {
    if (isLoading) return null;

    return (
      <View style={styles.emptyContainer}>
        {isImported ? (
          <>
            <Text style={styles.emptyText}>{t('food.noFoodFound')}</Text>
            {searchQuery ? (
              <Text style={styles.emptySubtext}>{t('food.tryDifferentSearch')}</Text>
            ) : null}
          </>
        ) : (
          <>
            <Text style={styles.emptyText}>{t('database.databaseNotImported', { source: '' })}</Text>
            <Text style={styles.emptySubtext}>{t('database.importToAccess')}</Text>
            <Button
              mode="contained"
              onPress={handleImportDatabase}
              style={styles.importButton}
            >
              {t('database.importDatabase', { source: selectedSource })}
            </Button>
          </>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Banner
        visible={!isImported}
        actions={[
          {
            label: t('database.import'),
            onPress: handleImportDatabase,
          },
        ]}
        icon="database-import"
      >
        {t('database.databaseNotImported', { source: selectedSource })}
      </Banner>

      <SegmentedButtons
        value={selectedSource}
        onValueChange={setSelectedSource}
        buttons={[
          { value: 'USDA', label: t('database.usdaFoods') },
          { value: 'FoodB', label: t('database.foodbFoods') }
        ]}
        style={styles.segmentedButtons}
      />

      <Searchbar
        placeholder={t('food.searchFoods')}
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
      />

      <FlatList
        data={filteredFoods}
        renderItem={renderFoodItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        onRefresh={handleRefresh}
        refreshing={isRefreshing}
        ItemSeparatorComponent={() => <Divider style={styles.divider} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  segmentedButtons: {
    margin: 16,
  },
  searchbar: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  listContent: {
    paddingBottom: 16,
    flexGrow: 1,
  },
  divider: {
    marginVertical: 8,
  },
  footerContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  footerText: {
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptySubtext: {
    textAlign: 'center',
    marginBottom: 16,
  },
  importButton: {
    marginTop: 16,
  },
});

export default FoodDatabaseScreen;
