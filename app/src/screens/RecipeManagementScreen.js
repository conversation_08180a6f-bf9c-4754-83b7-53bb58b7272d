/**
 * Recipe Management Screen for Znü<PERSON>Zähler
 * Comprehensive recipe management with search, creation, and AI enhancements
 */

import React, { useState, useEffect } from 'react';
import { 
  View, 
  ScrollView, 
  StyleSheet, 
  Dimensions,
  Alert,
  FlatList 
} from 'react-native';
import { 
  Text, 
  Card, 
  Surface, 
  Button, 
  Chip, 
  IconButton,
  FAB,
  Portal,
  Modal,
  TextInput,
  Divider,
  ActivityIndicator,
  Searchbar,
  Menu
} from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import recipeManagementService from '../services/recipeManagementService';
import { trackScreenView, trackUserAction } from '../services/analyticsService';

const { width: screenWidth } = Dimensions.get('window');

const RecipeManagementScreen = ({ navigation }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [recipes, setRecipes] = useState([]);
  const [filteredRecipes, setFilteredRecipes] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    cuisine: null,
    difficulty: null,
    maxCookingTime: null,
    dietaryRestrictions: []
  });
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [selectedRecipe, setSelectedRecipe] = useState(null);
  const [showRecipeModal, setShowRecipeModal] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'

  useEffect(() => {
    trackScreenView('Recipe Management');
    initializeRecipeManagement();
  }, []);

  useEffect(() => {
    filterRecipes();
  }, [searchQuery, selectedFilters, recipes]);

  const initializeRecipeManagement = async () => {
    try {
      setLoading(true);
      
      // Initialize recipe service
      await recipeManagementService.initialize();
      
      // Load recipes
      await loadRecipes();
      
    } catch (error) {
      console.error('Error initializing recipe management:', error);
      Alert.alert('Error', 'Failed to load recipes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadRecipes = async () => {
    try {
      const result = await recipeManagementService.searchRecipes({
        limit: 100
      });
      
      if (result.success) {
        setRecipes(result.recipes);
        setFilteredRecipes(result.recipes);
      }
    } catch (error) {
      console.error('Error loading recipes:', error);
    }
  };

  const filterRecipes = async () => {
    try {
      const criteria = {
        query: searchQuery,
        ...selectedFilters,
        limit: 100
      };

      const result = await recipeManagementService.searchRecipes(criteria);
      
      if (result.success) {
        setFilteredRecipes(result.recipes);
      }
    } catch (error) {
      console.error('Error filtering recipes:', error);
    }
  };

  const handleRecipePress = (recipe) => {
    setSelectedRecipe(recipe);
    setShowRecipeModal(true);
  };

  const handleCreateRecipe = () => {
    navigation.navigate('CreateRecipe');
  };

  const handleEditRecipe = (recipe) => {
    navigation.navigate('EditRecipe', { recipe });
  };

  const handleCookRecipe = (recipe) => {
    navigation.navigate('CookingMode', { recipe });
    trackUserAction('recipe_cooking_started', 'recipe_management', {
      recipeId: recipe.id,
      difficulty: recipe.difficulty
    });
  };

  const FilterMenu = () => (
    <Menu
      visible={showFilterMenu}
      onDismiss={() => setShowFilterMenu(false)}
      anchor={
        <IconButton
          icon="filter"
          iconColor={theme.colors.text}
          onPress={() => setShowFilterMenu(true)}
        />
      }
    >
      <Menu.Item
        onPress={() => {
          setSelectedFilters(prev => ({ ...prev, difficulty: 'easy' }));
          setShowFilterMenu(false);
        }}
        title="Easy Recipes"
      />
      <Menu.Item
        onPress={() => {
          setSelectedFilters(prev => ({ ...prev, maxCookingTime: 30 }));
          setShowFilterMenu(false);
        }}
        title="Quick (< 30 min)"
      />
      <Menu.Item
        onPress={() => {
          setSelectedFilters(prev => ({ ...prev, cuisine: 'Mediterranean' }));
          setShowFilterMenu(false);
        }}
        title="Mediterranean"
      />
      <Divider />
      <Menu.Item
        onPress={() => {
          setSelectedFilters({
            cuisine: null,
            difficulty: null,
            maxCookingTime: null,
            dietaryRestrictions: []
          });
          setShowFilterMenu(false);
        }}
        title="Clear Filters"
      />
    </Menu>
  );

  const RecipeCard = ({ recipe, index }) => (
    <Card 
      style={[
        styles.recipeCard,
        viewMode === 'list' && styles.recipeCardList
      ]}
      onPress={() => handleRecipePress(recipe)}
    >
      <Card.Content>
        <View style={styles.recipeHeader}>
          <Text style={[styles.recipeName, { color: theme.colors.text }]}>
            {recipe.name}
          </Text>
          <IconButton
            icon="dots-vertical"
            size={20}
            iconColor={theme.colors.onSurfaceVariant}
            onPress={() => {/* Show recipe options menu */}}
          />
        </View>
        
        {recipe.description && (
          <Text 
            style={[styles.recipeDescription, { color: theme.colors.onSurfaceVariant }]}
            numberOfLines={2}
          >
            {recipe.description}
          </Text>
        )}

        <View style={styles.recipeMetadata}>
          <View style={styles.metadataRow}>
            <Chip mode="outlined" compact style={styles.metadataChip}>
              ⏱️ {recipe.cookingTime + recipe.prepTime} min
            </Chip>
            <Chip mode="outlined" compact style={styles.metadataChip}>
              👨‍🍳 {recipe.difficulty}
            </Chip>
            <Chip mode="outlined" compact style={styles.metadataChip}>
              🍽️ {recipe.servings} servings
            </Chip>
          </View>
          
          {recipe.cuisine && (
            <Chip mode="outlined" compact style={styles.cuisineChip}>
              🌍 {recipe.cuisine}
            </Chip>
          )}
        </View>

        {recipe.nutrition && (
          <View style={styles.nutritionPreview}>
            <Text style={[styles.nutritionText, { color: theme.colors.onSurfaceVariant }]}>
              {recipe.nutrition.calories} cal • {recipe.nutrition.protein}g protein
            </Text>
          </View>
        )}

        <View style={styles.recipeActions}>
          <Button
            mode="contained-tonal"
            compact
            onPress={() => handleCookRecipe(recipe)}
            style={styles.actionButton}
          >
            Cook
          </Button>
          <Button
            mode="outlined"
            compact
            onPress={() => handleEditRecipe(recipe)}
            style={styles.actionButton}
          >
            Edit
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const RecipeDetailModal = () => (
    <Portal>
      <Modal
        visible={showRecipeModal}
        onDismiss={() => setShowRecipeModal(false)}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        {selectedRecipe && (
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
                {selectedRecipe.name}
              </Text>
              <IconButton
                icon="close"
                iconColor={theme.colors.text}
                onPress={() => setShowRecipeModal(false)}
              />
            </View>
            
            {selectedRecipe.description && (
              <Text style={[styles.recipeDescription, { color: theme.colors.onSurfaceVariant }]}>
                {selectedRecipe.description}
              </Text>
            )}

            <View style={styles.recipeInfo}>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Prep Time:
                </Text>
                <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                  {selectedRecipe.prepTime} minutes
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Cook Time:
                </Text>
                <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                  {selectedRecipe.cookingTime} minutes
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Servings:
                </Text>
                <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                  {selectedRecipe.servings}
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Difficulty:
                </Text>
                <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                  {selectedRecipe.difficulty}
                </Text>
              </View>
            </View>

            <Divider style={styles.divider} />

            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Ingredients
            </Text>
            {selectedRecipe.ingredients?.map((ingredient, index) => (
              <Text key={index} style={[styles.ingredientText, { color: theme.colors.onSurfaceVariant }]}>
                • {ingredient.amount} {ingredient.unit} {ingredient.name}
                {ingredient.notes && ` (${ingredient.notes})`}
              </Text>
            ))}

            <Divider style={styles.divider} />

            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Instructions
            </Text>
            {selectedRecipe.instructions?.map((instruction, index) => (
              <View key={index} style={styles.instructionStep}>
                <Text style={[styles.stepNumber, { color: theme.colors.primary }]}>
                  {index + 1}
                </Text>
                <Text style={[styles.instructionText, { color: theme.colors.onSurfaceVariant }]}>
                  {typeof instruction === 'string' ? instruction : instruction.instruction}
                </Text>
              </View>
            ))}

            {selectedRecipe.nutrition && (
              <>
                <Divider style={styles.divider} />
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                  Nutrition (per serving)
                </Text>
                <View style={styles.nutritionGrid}>
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionLabel, { color: theme.colors.onSurfaceVariant }]}>
                      Calories
                    </Text>
                    <Text style={[styles.nutritionValue, { color: theme.colors.text }]}>
                      {selectedRecipe.nutrition.calories}
                    </Text>
                  </View>
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionLabel, { color: theme.colors.onSurfaceVariant }]}>
                      Protein
                    </Text>
                    <Text style={[styles.nutritionValue, { color: theme.colors.text }]}>
                      {selectedRecipe.nutrition.protein}g
                    </Text>
                  </View>
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionLabel, { color: theme.colors.onSurfaceVariant }]}>
                      Carbs
                    </Text>
                    <Text style={[styles.nutritionValue, { color: theme.colors.text }]}>
                      {selectedRecipe.nutrition.carbs}g
                    </Text>
                  </View>
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionLabel, { color: theme.colors.onSurfaceVariant }]}>
                      Fat
                    </Text>
                    <Text style={[styles.nutritionValue, { color: theme.colors.text }]}>
                      {selectedRecipe.nutrition.fat}g
                    </Text>
                  </View>
                </View>
              </>
            )}

            {selectedRecipe.cookingTips && selectedRecipe.cookingTips.length > 0 && (
              <>
                <Divider style={styles.divider} />
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                  Cooking Tips
                </Text>
                {selectedRecipe.cookingTips.map((tip, index) => (
                  <Text key={index} style={[styles.tipText, { color: theme.colors.onSurfaceVariant }]}>
                    💡 {tip}
                  </Text>
                ))}
              </>
            )}

            <View style={styles.modalActions}>
              <Button
                mode="contained"
                onPress={() => {
                  setShowRecipeModal(false);
                  handleCookRecipe(selectedRecipe);
                }}
                style={styles.modalActionButton}
              >
                Start Cooking
              </Button>
              <Button
                mode="outlined"
                onPress={() => {
                  setShowRecipeModal(false);
                  handleEditRecipe(selectedRecipe);
                }}
                style={styles.modalActionButton}
              >
                Edit Recipe
              </Button>
            </View>
          </ScrollView>
        )}
      </Modal>
    </Portal>
  );

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          Loading recipes...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.text}
            onPress={() => navigation.goBack()}
          />
          <View style={styles.headerInfo}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              My Recipes
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              {filteredRecipes.length} recipes
            </Text>
          </View>
          <View style={styles.headerActions}>
            <IconButton
              icon={viewMode === 'grid' ? 'view-list' : 'view-grid'}
              size={24}
              iconColor={theme.colors.text}
              onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            />
            <FilterMenu />
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search recipes..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            iconColor={theme.colors.onSurfaceVariant}
            inputStyle={{ color: theme.colors.text }}
          />
        </View>

        {/* Active Filters */}
        {(selectedFilters.cuisine || selectedFilters.difficulty || selectedFilters.maxCookingTime) && (
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.filtersContainer}
            contentContainerStyle={styles.filtersContent}
          >
            {selectedFilters.cuisine && (
              <Chip
                mode="flat"
                onClose={() => setSelectedFilters(prev => ({ ...prev, cuisine: null }))}
                style={styles.filterChip}
              >
                {selectedFilters.cuisine}
              </Chip>
            )}
            {selectedFilters.difficulty && (
              <Chip
                mode="flat"
                onClose={() => setSelectedFilters(prev => ({ ...prev, difficulty: null }))}
                style={styles.filterChip}
              >
                {selectedFilters.difficulty}
              </Chip>
            )}
            {selectedFilters.maxCookingTime && (
              <Chip
                mode="flat"
                onClose={() => setSelectedFilters(prev => ({ ...prev, maxCookingTime: null }))}
                style={styles.filterChip}
              >
                Under {selectedFilters.maxCookingTime} min
              </Chip>
            )}
          </ScrollView>
        )}
      </Surface>

      {/* Recipe List */}
      {filteredRecipes.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
            {searchQuery || Object.values(selectedFilters).some(f => f) 
              ? 'No recipes found' 
              : 'No recipes yet'
            }
          </Text>
          <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
            {searchQuery || Object.values(selectedFilters).some(f => f)
              ? 'Try adjusting your search or filters'
              : 'Create your first recipe to get started'
            }
          </Text>
          <Button
            mode="contained"
            onPress={handleCreateRecipe}
            style={styles.emptyButton}
          >
            Create Recipe
          </Button>
        </View>
      ) : (
        <FlatList
          data={filteredRecipes}
          renderItem={({ item, index }) => <RecipeCard recipe={item} index={index} />}
          keyExtractor={(item) => item.id}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode} // Force re-render when view mode changes
          contentContainerStyle={styles.recipeList}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleCreateRecipe}
        label="New Recipe"
      />

      {/* Recipe Detail Modal */}
      <RecipeDetailModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    paddingTop: 40,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
  },
  headerActions: {
    flexDirection: 'row',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 0,
  },
  filtersContainer: {
    paddingBottom: 8,
  },
  filtersContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  recipeList: {
    padding: 8,
    paddingBottom: 80,
  },
  recipeCard: {
    flex: 1,
    margin: 8,
    maxWidth: screenWidth / 2 - 24,
  },
  recipeCardList: {
    maxWidth: '100%',
  },
  recipeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  recipeName: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  recipeDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  recipeMetadata: {
    marginBottom: 12,
  },
  metadataRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
    marginBottom: 4,
  },
  metadataChip: {
    height: 24,
  },
  cuisineChip: {
    height: 24,
    alignSelf: 'flex-start',
  },
  nutritionPreview: {
    marginBottom: 12,
  },
  nutritionText: {
    fontSize: 12,
  },
  recipeActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  emptyButton: {
    minWidth: 150,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 8,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  recipeInfo: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  divider: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  ingredientText: {
    fontSize: 14,
    marginBottom: 4,
    lineHeight: 20,
  },
  instructionStep: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
    minWidth: 24,
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  nutritionItem: {
    alignItems: 'center',
    minWidth: '22%',
  },
  nutritionLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  tipText: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  modalActionButton: {
    flex: 1,
  },
});

export default RecipeManagementScreen;
