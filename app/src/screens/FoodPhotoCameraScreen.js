/**
 * Food Photo Camera Screen for ZnüniZähler
 * Camera interface for food identification and portion estimation
 */

import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Text, Alert, Dimensions, Animated } from 'react-native';
import { Camera } from 'expo-camera';
import { Button, ActivityIndicator, Surface, IconButton, Chip, FAB } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import foodImageRecognitionService from '../services/foodImageRecognitionService';
import aiRecommendationService from '../services/aiRecommendationService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const FoodPhotoCameraScreen = ({ navigation, route }) => {
  const theme = useTheme();
  const cameraRef = useRef(null);
  const [hasPermission, setHasPermission] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.back);
  const [detectedFoods, setDetectedFoods] = useState([]);
  const [showDetections, setShowDetections] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [captureMode, setCaptureMode] = useState('single'); // 'single' or 'multiple'

  const { onResult, mealType = 'snack' } = route.params || {};

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  useEffect(() => {
    // Initialize food recognition service
    foodImageRecognitionService.initialize();
  }, []);

  const takePicture = async () => {
    if (!cameraRef.current || isProcessing) return;

    // Animate capture button
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setIsProcessing(true);
    setProcessingProgress(0);

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      await processImage(photo.uri);
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
      setIsProcessing(false);
    }
  };

  const processImage = async (imageUri) => {
    try {
      setProcessingProgress(0.2);

      // Recognize foods in the image
      const recognitionResult = await foodImageRecognitionService.recognizeFoods(imageUri, {
        maxWidth: 512,
        maxHeight: 512,
        quality: 0.8
      });

      setProcessingProgress(0.6);

      if (recognitionResult.success && recognitionResult.foods.length > 0) {
        setDetectedFoods(recognitionResult.foods);
        setShowDetections(true);
        
        // Animate detection results
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();

        setProcessingProgress(0.8);

        // Get AI recommendations for detected foods
        const recommendations = await aiRecommendationService.getMealCompletionSuggestions(
          recognitionResult.foods.map(food => ({
            food_id: food.id,
            quantity: food.portion.estimatedWeight,
            nutrients: food.estimatedNutrition
          }))
        );

        setProcessingProgress(1.0);

        // Navigate to results screen
        setTimeout(() => {
          if (onResult) {
            onResult({
              detectedFoods: recognitionResult.foods,
              recommendations,
              imageUri,
              confidence: recognitionResult.confidence
            });
            navigation.goBack();
          } else {
            navigation.navigate('FoodRecognitionResults', {
              detectedFoods: recognitionResult.foods,
              recommendations,
              imageUri,
              confidence: recognitionResult.confidence,
              mealType
            });
          }
        }, 500);
      } else {
        // No foods detected or low confidence
        Alert.alert(
          'No Food Detected',
          'Could not identify any foods in the image. Try taking another photo with better lighting or a clearer view of the food.',
          [
            { text: 'Retry', onPress: () => setIsProcessing(false) },
            { text: 'Manual Entry', onPress: () => navigation.navigate('ManualEntry') },
            { text: 'Cancel', onPress: () => navigation.goBack() }
          ]
        );
      }
    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert('Error', 'Failed to process image. Please try again.');
    } finally {
      setIsProcessing(false);
      setProcessingProgress(0);
    }
  };

  const toggleFlash = () => {
    setFlashMode(
      flashMode === Camera.Constants.FlashMode.off
        ? Camera.Constants.FlashMode.on
        : Camera.Constants.FlashMode.off
    );
  };

  const toggleCameraType = () => {
    setCameraType(
      cameraType === Camera.Constants.Type.back
        ? Camera.Constants.Type.front
        : Camera.Constants.Type.back
    );
  };

  const retakePhoto = () => {
    setDetectedFoods([]);
    setShowDetections(false);
    setIsProcessing(false);
    
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const confirmDetections = () => {
    if (detectedFoods.length > 0) {
      navigation.navigate('FoodRecognitionResults', {
        detectedFoods,
        imageUri: null,
        confidence: 0.8,
        mealType
      });
    }
  };

  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.text, { color: theme.colors.text }]}>
          Requesting camera permission...
        </Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.text, { color: theme.colors.text }]}>
          Camera permission is required to identify foods.
        </Text>
        <Button 
          mode="contained" 
          onPress={() => navigation.goBack()}
          style={styles.button}
        >
          Go Back
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={cameraRef}
        style={styles.camera}
        type={cameraType}
        flashMode={flashMode}
        autoFocus={Camera.Constants.AutoFocus.on}
      >
        {/* Top Controls */}
        <Surface style={[styles.topControls, { backgroundColor: theme.colors.backdrop }]}>
          <IconButton
            icon="close"
            iconColor={theme.colors.text}
            size={24}
            onPress={() => navigation.goBack()}
          />
          <Text style={[styles.mealTypeText, { color: theme.colors.text }]}>
            {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
          </Text>
          <View style={styles.topControlsRight}>
            <IconButton
              icon={flashMode === Camera.Constants.FlashMode.on ? "flash" : "flash-off"}
              iconColor={theme.colors.text}
              size={24}
              onPress={toggleFlash}
            />
            <IconButton
              icon="camera-flip"
              iconColor={theme.colors.text}
              size={24}
              onPress={toggleCameraType}
            />
          </View>
        </Surface>

        {/* Food Detection Overlay */}
        {showDetections && (
          <Animated.View 
            style={[
              styles.detectionOverlay,
              { opacity: fadeAnim }
            ]}
          >
            <Surface style={[styles.detectionPanel, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.detectionTitle, { color: theme.colors.text }]}>
                Detected Foods:
              </Text>
              {detectedFoods.slice(0, 3).map((food, index) => (
                <Chip
                  key={index}
                  mode="outlined"
                  icon="food-apple"
                  style={styles.foodChip}
                >
                  {food.name} ({food.portion.estimatedWeight}g)
                </Chip>
              ))}
              {detectedFoods.length > 3 && (
                <Text style={[styles.moreText, { color: theme.colors.onSurfaceVariant }]}>
                  +{detectedFoods.length - 3} more foods
                </Text>
              )}
            </Surface>
          </Animated.View>
        )}

        {/* Instructions */}
        {!showDetections && !isProcessing && (
          <Surface style={[styles.instructions, { backgroundColor: theme.colors.backdrop }]}>
            <Text style={[styles.instructionText, { color: theme.colors.text }]}>
              Point camera at your food
            </Text>
            <Chip 
              icon="lightbulb-outline" 
              mode="outlined"
              textStyle={{ color: theme.colors.text }}
              style={{ backgroundColor: 'transparent', borderColor: theme.colors.text }}
            >
              Good lighting improves accuracy
            </Chip>
          </Surface>
        )}

        {/* Processing Indicator */}
        {isProcessing && (
          <Surface style={[styles.processingOverlay, { backgroundColor: theme.colors.backdrop }]}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.processingText, { color: theme.colors.text }]}>
              Analyzing food...
            </Text>
            <View style={styles.progressContainer}>
              <View 
                style={[
                  styles.progressBar,
                  { 
                    width: `${processingProgress * 100}%`,
                    backgroundColor: theme.colors.primary 
                  }
                ]}
              />
            </View>
            <Text style={[styles.progressText, { color: theme.colors.onSurfaceVariant }]}>
              {Math.round(processingProgress * 100)}%
            </Text>
          </Surface>
        )}

        {/* Bottom Controls */}
        <Surface style={[styles.bottomControls, { backgroundColor: theme.colors.backdrop }]}>
          {showDetections ? (
            <View style={styles.detectionControls}>
              <Button
                mode="outlined"
                onPress={retakePhoto}
                style={styles.secondaryButton}
                textColor={theme.colors.text}
              >
                Retake
              </Button>
              
              <Button
                mode="contained"
                onPress={confirmDetections}
                style={styles.primaryButton}
              >
                Confirm ({detectedFoods.length})
              </Button>
            </View>
          ) : (
            <View style={styles.captureContainer}>
              <Button
                mode="text"
                onPress={() => navigation.navigate('OCRCamera')}
                textColor={theme.colors.text}
              >
                Scan Label
              </Button>
              
              <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                <FAB
                  icon="camera"
                  size="large"
                  style={[styles.captureButton, { backgroundColor: theme.colors.primary }]}
                  onPress={takePicture}
                  disabled={isProcessing}
                />
              </Animated.View>
              
              <Button
                mode="text"
                onPress={() => navigation.navigate('ManualEntry')}
                textColor={theme.colors.text}
              >
                Manual Entry
              </Button>
            </View>
          )}
        </Surface>
      </Camera>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  topControls: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
  },
  mealTypeText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  topControlsRight: {
    flexDirection: 'row',
  },
  detectionOverlay: {
    position: 'absolute',
    top: 120,
    left: 16,
    right: 16,
  },
  detectionPanel: {
    padding: 16,
    borderRadius: 8,
    elevation: 4,
  },
  detectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  foodChip: {
    marginBottom: 4,
    marginRight: 8,
  },
  moreText: {
    fontSize: 12,
    fontStyle: 'italic',
    marginTop: 4,
  },
  instructions: {
    position: 'absolute',
    top: 120,
    left: 16,
    right: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  processingOverlay: {
    position: 'absolute',
    top: '40%',
    left: 16,
    right: 16,
    padding: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  processingText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  progressContainer: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    marginTop: 16,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    marginTop: 8,
    fontSize: 12,
  },
  bottomControls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginHorizontal: 16,
    borderRadius: 8,
  },
  captureContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  captureButton: {
    elevation: 4,
  },
  detectionControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 16,
  },
  secondaryButton: {
    flex: 1,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  primaryButton: {
    flex: 1,
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  button: {
    marginTop: 16,
  },
});

export default FoodPhotoCameraScreen;
