import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from '../hooks/useTranslation';

const HomeScreen = ({ navigation }) => {
  const { t, isSwissGerman, getSwissGermanGreeting } = useTranslation();

  // Get appropriate greeting for Swiss German users
  const greeting = isSwissGerman ? getSwissGermanGreeting() : t('home.welcome');

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{greeting}</Text>
        <Text style={styles.headerSubtitle}>{t('home.subtitle')}</Text>
      </View>

      <View style={styles.captureSection}>
        <Text style={styles.sectionTitle}>{t('home.captureFood')}</Text>
        <View style={styles.captureOptions}>
          <TouchableOpacity
            style={styles.captureOption}
            onPress={() => navigation.navigate('Scan')}
          >
            <MaterialIcons name="document-scanner" size={40} color="#4CAF50" />
            <Text style={styles.optionText}>{t('home.scanLabel')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.captureOption}
            onPress={() => navigation.navigate('Barcode')}
          >
            <MaterialIcons name="qr-code-scanner" size={40} color="#2196F3" />
            <Text style={styles.optionText}>{t('home.scanBarcode')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.captureOption}
            onPress={() => navigation.navigate('ManualEntry')}
          >
            <MaterialIcons name="edit" size={40} color="#FF9800" />
            <Text style={styles.optionText}>{t('home.manualEntry')}</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.statsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{t('home.todaysSummary')}</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Statistics')}>
            <Text style={styles.seeAllText}>{t('home.seeAll')}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.statsCard}>
          <Text style={styles.placeholderText}>{t('home.dailySummaryPlaceholder')}</Text>
        </View>
      </View>

      <View style={styles.recentSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{t('home.recentEntries')}</Text>
        </View>

        <View style={styles.recentCard}>
          <Text style={styles.placeholderText}>{t('home.recentEntriesPlaceholder')}</Text>
        </View>
      </View>

      <TouchableOpacity
        style={styles.profileButton}
        onPress={() => navigation.navigate('Profile')}
      >
        <MaterialIcons name="person" size={24} color="#fff" />
        <Text style={styles.profileButtonText}>{t('home.myProfile')}</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e8f5e9',
  },
  captureSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  captureOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  captureOption: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    width: '30%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  optionText: {
    marginTop: 10,
    fontSize: 14,
    textAlign: 'center',
  },
  statsSection: {
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  seeAllText: {
    color: '#4CAF50',
    fontSize: 14,
  },
  statsCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recentSection: {
    padding: 20,
    paddingTop: 0,
  },
  recentCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    color: '#9e9e9e',
    textAlign: 'center',
  },
  profileButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    margin: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  profileButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});

export default HomeScreen;
