/**
 * Shopping List Screen for ZnüniZähler
 * Smart shopping list generation from meal plans and recipes
 */

import React, { useState, useEffect } from 'react';
import { 
  View, 
  ScrollView, 
  StyleSheet, 
  Alert,
  Share 
} from 'react-native';
import { 
  Text, 
  Card, 
  Surface, 
  Button, 
  Chip, 
  IconButton,
  Checkbox,
  Divider,
  ActivityIndicator,
  FAB,
  Portal,
  Modal,
  TextInput
} from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import smartMealPlanningService from '../services/smartMealPlanningService';
import { trackScreenView, trackUserAction } from '../services/analyticsService';

const ShoppingListScreen = ({ navigation, route }) => {
  const theme = useTheme();
  const { mealPlan, recipes } = route.params || {};
  const [loading, setLoading] = useState(true);
  const [shoppingList, setShoppingList] = useState(null);
  const [checkedItems, setCheckedItems] = useState(new Set());
  const [showAddItemModal, setShowAddItemModal] = useState(false);
  const [newItemName, setNewItemName] = useState('');
  const [newItemQuantity, setNewItemQuantity] = useState('');
  const [newItemCategory, setNewItemCategory] = useState('Other');
  const [estimatedCost, setEstimatedCost] = useState(0);
  const [shoppingTips, setShoppingTips] = useState([]);

  useEffect(() => {
    trackScreenView('Shopping List');
    generateShoppingList();
  }, []);

  const generateShoppingList = async () => {
    try {
      setLoading(true);
      
      // Initialize service
      await smartMealPlanningService.initialize();
      
      let result;
      if (mealPlan) {
        // Generate from meal plan
        result = await smartMealPlanningService.generateShoppingList(mealPlan, {
          groupByCategory: true,
          includePantryCheck: true,
          optimizeForStore: true,
          addQuantityBuffer: 0.1,
          includeAlternatives: true
        });
      } else if (recipes) {
        // Generate from individual recipes
        result = await smartMealPlanningService.generateShoppingListFromRecipes(recipes);
      } else {
        throw new Error('No meal plan or recipes provided');
      }

      if (result.success) {
        setShoppingList(result.shoppingList);
        setEstimatedCost(result.shoppingList.estimatedCost || 0);
        setShoppingTips(result.shoppingList.shoppingTips || []);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error generating shopping list:', error);
      Alert.alert('Error', 'Failed to generate shopping list. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleItemCheck = (itemId) => {
    const newCheckedItems = new Set(checkedItems);
    if (newCheckedItems.has(itemId)) {
      newCheckedItems.delete(itemId);
    } else {
      newCheckedItems.add(itemId);
    }
    setCheckedItems(newCheckedItems);
    
    trackUserAction('shopping_item_checked', 'shopping_list', {
      itemId,
      checked: newCheckedItems.has(itemId)
    });
  };

  const handleAddCustomItem = () => {
    if (!newItemName.trim()) {
      Alert.alert('Error', 'Please enter an item name');
      return;
    }

    const newItem = {
      id: `custom_${Date.now()}`,
      name: newItemName.trim(),
      amount: newItemQuantity || '1',
      unit: 'item',
      category: newItemCategory,
      isCustom: true
    };

    // Add to appropriate category
    const updatedList = { ...shoppingList.items };
    if (!updatedList[newItemCategory]) {
      updatedList[newItemCategory] = [];
    }
    updatedList[newItemCategory].push(newItem);

    setShoppingList(prev => ({
      ...prev,
      items: updatedList,
      totalItems: prev.totalItems + 1
    }));

    // Reset form
    setNewItemName('');
    setNewItemQuantity('');
    setNewItemCategory('Other');
    setShowAddItemModal(false);

    trackUserAction('custom_item_added', 'shopping_list', {
      category: newItemCategory
    });
  };

  const handleShareList = async () => {
    try {
      const listText = generateShareableText();
      await Share.share({
        message: listText,
        title: 'Shopping List from ZnüniZähler'
      });
      
      trackUserAction('shopping_list_shared', 'shopping_list');
    } catch (error) {
      console.error('Error sharing shopping list:', error);
    }
  };

  const generateShareableText = () => {
    let text = '🛒 Shopping List from ZnüniZähler\n\n';
    
    Object.entries(shoppingList.items).forEach(([category, items]) => {
      text += `📂 ${category.toUpperCase()}\n`;
      items.forEach(item => {
        const checked = checkedItems.has(item.id) ? '✅' : '☐';
        text += `${checked} ${item.amount} ${item.unit} ${item.name}\n`;
      });
      text += '\n';
    });
    
    if (estimatedCost > 0) {
      text += `💰 Estimated Cost: $${estimatedCost.toFixed(2)}\n\n`;
    }
    
    text += 'Generated by ZnüniZähler - Smart Nutrition Tracking';
    
    return text;
  };

  const getCompletionPercentage = () => {
    if (!shoppingList) return 0;
    const totalItems = shoppingList.totalItems;
    const checkedCount = checkedItems.size;
    return totalItems > 0 ? (checkedCount / totalItems) * 100 : 0;
  };

  const AddItemModal = () => (
    <Portal>
      <Modal
        visible={showAddItemModal}
        onDismiss={() => setShowAddItemModal(false)}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
          Add Custom Item
        </Text>
        
        <TextInput
          label="Item Name"
          value={newItemName}
          onChangeText={setNewItemName}
          mode="outlined"
          style={styles.input}
        />
        
        <TextInput
          label="Quantity (optional)"
          value={newItemQuantity}
          onChangeText={setNewItemQuantity}
          mode="outlined"
          style={styles.input}
          placeholder="e.g., 2, 1 lb, 500g"
        />
        
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Category
        </Text>
        <View style={styles.categoryChips}>
          {['Produce', 'Meat', 'Dairy', 'Pantry', 'Frozen', 'Other'].map(category => (
            <Chip
              key={category}
              mode={newItemCategory === category ? 'flat' : 'outlined'}
              onPress={() => setNewItemCategory(category)}
              style={styles.categoryChip}
            >
              {category}
            </Chip>
          ))}
        </View>
        
        <View style={styles.modalActions}>
          <Button
            mode="outlined"
            onPress={() => setShowAddItemModal(false)}
            style={styles.modalButton}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleAddCustomItem}
            style={styles.modalButton}
          >
            Add Item
          </Button>
        </View>
      </Modal>
    </Portal>
  );

  const CategorySection = ({ category, items }) => (
    <Card style={styles.categoryCard}>
      <Card.Content>
        <View style={styles.categoryHeader}>
          <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
            {getCategoryIcon(category)} {category}
          </Text>
          <Chip mode="outlined" compact>
            {items.length} items
          </Chip>
        </View>
        
        {items.map((item, index) => (
          <View key={item.id || index} style={styles.itemRow}>
            <Checkbox
              status={checkedItems.has(item.id || `${category}_${index}`) ? 'checked' : 'unchecked'}
              onPress={() => handleItemCheck(item.id || `${category}_${index}`)}
              color={theme.colors.primary}
            />
            <View style={styles.itemInfo}>
              <Text 
                style={[
                  styles.itemName, 
                  { 
                    color: checkedItems.has(item.id || `${category}_${index}`) 
                      ? theme.colors.onSurfaceVariant 
                      : theme.colors.text,
                    textDecorationLine: checkedItems.has(item.id || `${category}_${index}`) 
                      ? 'line-through' 
                      : 'none'
                  }
                ]}
              >
                {item.totalAmount || item.amount} {item.unit} {item.name}
              </Text>
              {item.alternatives && item.alternatives.length > 0 && (
                <Text style={[styles.alternatives, { color: theme.colors.onSurfaceVariant }]}>
                  Alternatives: {item.alternatives.slice(0, 2).join(', ')}
                </Text>
              )}
            </View>
            {item.estimatedPrice && (
              <Text style={[styles.itemPrice, { color: theme.colors.onSurfaceVariant }]}>
                ${item.estimatedPrice.toFixed(2)}
              </Text>
            )}
          </View>
        ))}
      </Card.Content>
    </Card>
  );

  const getCategoryIcon = (category) => {
    const icons = {
      'Produce': '🥬',
      'Meat': '🥩',
      'Dairy': '🥛',
      'Pantry': '🏺',
      'Frozen': '🧊',
      'Bakery': '🍞',
      'Beverages': '🥤',
      'Snacks': '🍿',
      'Other': '📦'
    };
    return icons[category] || '📦';
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          Generating shopping list...
        </Text>
      </View>
    );
  }

  if (!shoppingList) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.text }]}>
          Failed to generate shopping list
        </Text>
        <Button mode="contained" onPress={generateShoppingList}>
          Try Again
        </Button>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.text}
            onPress={() => navigation.goBack()}
          />
          <View style={styles.headerInfo}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Shopping List
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              {shoppingList.totalItems} items • {Math.round(getCompletionPercentage())}% complete
            </Text>
          </View>
          <IconButton
            icon="share"
            size={24}
            iconColor={theme.colors.text}
            onPress={handleShareList}
          />
        </View>
      </Surface>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Progress Summary */}
        <Card style={styles.summaryCard}>
          <Card.Content>
            <View style={styles.progressHeader}>
              <Text style={[styles.progressTitle, { color: theme.colors.text }]}>
                Shopping Progress
              </Text>
              <Text style={[styles.progressPercentage, { color: theme.colors.primary }]}>
                {Math.round(getCompletionPercentage())}%
              </Text>
            </View>
            
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    backgroundColor: theme.colors.primary,
                    width: `${getCompletionPercentage()}%`
                  }
                ]} 
              />
            </View>
            
            <View style={styles.summaryStats}>
              <Text style={[styles.statText, { color: theme.colors.onSurfaceVariant }]}>
                {checkedItems.size} of {shoppingList.totalItems} items collected
              </Text>
              {estimatedCost > 0 && (
                <Text style={[styles.statText, { color: theme.colors.onSurfaceVariant }]}>
                  Estimated cost: ${estimatedCost.toFixed(2)}
                </Text>
              )}
            </View>
          </Card.Content>
        </Card>

        {/* Shopping Tips */}
        {shoppingTips.length > 0 && (
          <Card style={styles.tipsCard}>
            <Card.Content>
              <Text style={[styles.tipsTitle, { color: theme.colors.text }]}>
                💡 Shopping Tips
              </Text>
              {shoppingTips.slice(0, 3).map((tip, index) => (
                <Text key={index} style={[styles.tipText, { color: theme.colors.onSurfaceVariant }]}>
                  • {tip}
                </Text>
              ))}
            </Card.Content>
          </Card>
        )}

        {/* Shopping List Categories */}
        {Object.entries(shoppingList.items).map(([category, items]) => (
          <CategorySection key={category} category={category} items={items} />
        ))}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => setShowAddItemModal(true)}
        label="Add Item"
      />

      {/* Add Item Modal */}
      <AddItemModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 18,
    marginBottom: 24,
    textAlign: 'center',
  },
  header: {
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    paddingTop: 40,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
  },
  content: {
    flex: 1,
  },
  summaryCard: {
    margin: 16,
    marginBottom: 8,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressPercentage: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
    marginBottom: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statText: {
    fontSize: 14,
  },
  tipsCard: {
    margin: 16,
    marginBottom: 8,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    marginBottom: 4,
    lineHeight: 20,
  },
  categoryCard: {
    margin: 16,
    marginBottom: 8,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  itemInfo: {
    flex: 1,
    marginLeft: 8,
  },
  itemName: {
    fontSize: 16,
    marginBottom: 2,
  },
  alternatives: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 80,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  categoryChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 20,
  },
  categoryChip: {
    marginBottom: 4,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default ShoppingListScreen;
