/**
 * OCR Camera Screen for Znü<PERSON>Zähler
 * Advanced camera interface for nutrition label scanning
 */

import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Text, Alert, Dimensions } from 'react-native';
import { Camera } from 'expo-camera';
import { Button, ActivityIndicator, Surface, IconButton, Chip } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import ocrService from '../services/ocrService';
import * as ImageManipulator from 'expo-image-manipulator';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const OCRCameraScreen = ({ navigation, route }) => {
  const theme = useTheme();
  const cameraRef = useRef(null);
  const [hasPermission, setHasPermission] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.back);
  const [focusDepth, setFocusDepth] = useState(0);
  const [cropArea, setCropArea] = useState(null);
  const [showCropGuide, setShowCropGuide] = useState(true);

  const { onResult } = route.params || {};

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  useEffect(() => {
    // Initialize OCR service
    ocrService.initialize();
  }, []);

  const takePicture = async () => {
    if (!cameraRef.current || isProcessing) return;

    setIsProcessing(true);

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      await processImage(photo.uri);
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
      setIsProcessing(false);
    }
  };

  const processImage = async (imageUri) => {
    try {
      // Show processing indicator
      setIsProcessing(true);

      // Crop image if crop area is defined
      let processedImageUri = imageUri;
      if (cropArea) {
        const croppedImage = await ImageManipulator.manipulateAsync(
          imageUri,
          [{ crop: cropArea }],
          { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
        );
        processedImageUri = croppedImage.uri;
      }

      // Process with OCR
      const ocrResult = await ocrService.processImage(processedImageUri, {
        resize: { width: 1024, height: 1024 },
        compress: 0.8
      });

      if (ocrResult.success) {
        // Navigate to results screen or call callback
        if (onResult) {
          onResult(ocrResult.data);
          navigation.goBack();
        } else {
          navigation.navigate('OCRResults', { 
            ocrData: ocrResult.data,
            imageUri: processedImageUri 
          });
        }
      } else {
        Alert.alert(
          'OCR Failed',
          ocrResult.error || 'Could not extract text from image. Please try again with better lighting.',
          [
            { text: 'Retry', onPress: () => setIsProcessing(false) },
            { text: 'Cancel', onPress: () => navigation.goBack() }
          ]
        );
      }
    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert('Error', 'Failed to process image. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const toggleFlash = () => {
    setFlashMode(
      flashMode === Camera.Constants.FlashMode.off
        ? Camera.Constants.FlashMode.on
        : Camera.Constants.FlashMode.off
    );
  };

  const toggleCameraType = () => {
    setCameraType(
      cameraType === Camera.Constants.Type.back
        ? Camera.Constants.Type.front
        : Camera.Constants.Type.back
    );
  };

  const setCropGuide = () => {
    // Define crop area for nutrition label (typically rectangular)
    const guideWidth = screenWidth * 0.8;
    const guideHeight = screenHeight * 0.4;
    const guideX = (screenWidth - guideWidth) / 2;
    const guideY = (screenHeight - guideHeight) / 2;

    setCropArea({
      originX: guideX,
      originY: guideY,
      width: guideWidth,
      height: guideHeight
    });
  };

  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.text, { color: theme.colors.text }]}>
          Requesting camera permission...
        </Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.text, { color: theme.colors.text }]}>
          Camera permission is required to scan nutrition labels.
        </Text>
        <Button 
          mode="contained" 
          onPress={() => navigation.goBack()}
          style={styles.button}
        >
          Go Back
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={cameraRef}
        style={styles.camera}
        type={cameraType}
        flashMode={flashMode}
        autoFocus={Camera.Constants.AutoFocus.on}
        focusDepth={focusDepth}
      >
        {/* Crop Guide Overlay */}
        {showCropGuide && (
          <View style={styles.cropGuideContainer}>
            <View style={[styles.cropGuide, { borderColor: theme.colors.primary }]}>
              <Text style={[styles.cropGuideText, { color: theme.colors.primary }]}>
                Position nutrition label here
              </Text>
            </View>
          </View>
        )}

        {/* Top Controls */}
        <Surface style={[styles.topControls, { backgroundColor: theme.colors.backdrop }]}>
          <IconButton
            icon="close"
            iconColor={theme.colors.text}
            size={24}
            onPress={() => navigation.goBack()}
          />
          <View style={styles.topControlsRight}>
            <IconButton
              icon={flashMode === Camera.Constants.FlashMode.on ? "flash" : "flash-off"}
              iconColor={theme.colors.text}
              size={24}
              onPress={toggleFlash}
            />
            <IconButton
              icon="camera-flip"
              iconColor={theme.colors.text}
              size={24}
              onPress={toggleCameraType}
            />
          </View>
        </Surface>

        {/* Instructions */}
        <Surface style={[styles.instructions, { backgroundColor: theme.colors.backdrop }]}>
          <Text style={[styles.instructionText, { color: theme.colors.text }]}>
            Position the nutrition label within the frame
          </Text>
          <Chip 
            icon="information" 
            mode="outlined"
            textStyle={{ color: theme.colors.text }}
            style={{ backgroundColor: 'transparent', borderColor: theme.colors.text }}
          >
            Ensure good lighting for best results
          </Chip>
        </Surface>

        {/* Bottom Controls */}
        <Surface style={[styles.bottomControls, { backgroundColor: theme.colors.backdrop }]}>
          {isProcessing ? (
            <View style={styles.processingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={[styles.processingText, { color: theme.colors.text }]}>
                Processing image...
              </Text>
            </View>
          ) : (
            <View style={styles.captureContainer}>
              <Button
                mode="outlined"
                onPress={() => setShowCropGuide(!showCropGuide)}
                style={styles.secondaryButton}
                textColor={theme.colors.text}
              >
                {showCropGuide ? 'Hide Guide' : 'Show Guide'}
              </Button>
              
              <IconButton
                icon="camera"
                iconColor={theme.colors.primary}
                size={64}
                style={[styles.captureButton, { backgroundColor: theme.colors.surface }]}
                onPress={takePicture}
              />
              
              <Button
                mode="text"
                onPress={() => navigation.navigate('ManualEntry')}
                textColor={theme.colors.text}
              >
                Manual Entry
              </Button>
            </View>
          )}
        </Surface>
      </Camera>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cropGuideContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cropGuide: {
    width: screenWidth * 0.8,
    height: screenHeight * 0.4,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  cropGuideText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  topControls: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
  },
  topControlsRight: {
    flexDirection: 'row',
  },
  instructions: {
    position: 'absolute',
    top: 120,
    left: 16,
    right: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  bottomControls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginHorizontal: 16,
    borderRadius: 8,
  },
  processingContainer: {
    alignItems: 'center',
  },
  processingText: {
    marginTop: 8,
    fontSize: 16,
  },
  captureContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  captureButton: {
    elevation: 4,
  },
  secondaryButton: {
    borderColor: 'rgba(255,255,255,0.5)',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  button: {
    marginTop: 16,
  },
});

export default OCRCameraScreen;
