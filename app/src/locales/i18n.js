/**
 * Internationalization (i18n) Configuration for Znü<PERSON>Zähler
 * Supports English, German, and Swiss German
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, NativeModules } from 'react-native';

// Import translation files
import en from './en.json';
import de from './de.json';
import deCH from './de-CH.json';

// Language detection function
const getDeviceLanguage = () => {
  let locale = 'en';
  
  if (Platform.OS === 'ios') {
    locale = NativeModules.SettingsManager?.settings?.AppleLocale ||
             NativeModules.SettingsManager?.settings?.AppleLanguages?.[0] ||
             'en';
  } else if (Platform.OS === 'android') {
    locale = NativeModules.I18nManager?.localeIdentifier || 'en';
  }
  
  // Normalize locale
  if (locale.includes('_')) {
    locale = locale.replace('_', '-');
  }
  
  // Handle Swiss German specifically
  if (locale.startsWith('de') && (locale.includes('CH') || locale.includes('ch'))) {
    return 'de-CH';
  }
  
  // Handle German
  if (locale.startsWith('de')) {
    return 'de';
  }
  
  // Default to English
  return 'en';
};

// Custom language detector
const languageDetector = {
  type: 'languageDetector',
  async: true,
  detect: async (callback) => {
    try {
      // Try to get saved language preference
      const savedLanguage = await AsyncStorage.getItem('user-language');
      if (savedLanguage) {
        callback(savedLanguage);
        return;
      }
      
      // Fall back to device language
      const deviceLanguage = getDeviceLanguage();
      callback(deviceLanguage);
    } catch (error) {
      console.warn('Error detecting language:', error);
      callback('en');
    }
  },
  init: () => {},
  cacheUserLanguage: async (language) => {
    try {
      await AsyncStorage.setItem('user-language', language);
    } catch (error) {
      console.warn('Error saving language preference:', error);
    }
  }
};

// i18n configuration
i18n
  .use(languageDetector)
  .use(initReactI18next)
  .init({
    // Translation resources
    resources: {
      en: {
        translation: en
      },
      de: {
        translation: de
      },
      'de-CH': {
        translation: deCH
      }
    },
    
    // Fallback language
    fallbackLng: 'en',
    
    // Language detection options
    detection: {
      order: ['asyncStorage', 'navigator'],
      caches: ['asyncStorage']
    },
    
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      format: (value, format, lng) => {
        // Custom formatting for numbers, dates, etc.
        if (format === 'number') {
          return new Intl.NumberFormat(lng).format(value);
        }
        if (format === 'currency') {
          const currency = lng === 'de-CH' ? 'CHF' : 'EUR';
          return new Intl.NumberFormat(lng, {
            style: 'currency',
            currency: currency
          }).format(value);
        }
        if (format === 'date') {
          return new Intl.DateTimeFormat(lng).format(new Date(value));
        }
        if (format === 'time') {
          return new Intl.DateTimeFormat(lng, {
            hour: '2-digit',
            minute: '2-digit'
          }).format(new Date(value));
        }
        return value;
      }
    },
    
    // React options
    react: {
      useSuspense: false
    },
    
    // Debug mode (disable in production)
    debug: __DEV__,
    
    // Namespace options
    defaultNS: 'translation',
    ns: ['translation'],
    
    // Key separator
    keySeparator: '.',
    
    // Nested separator
    nsSeparator: ':',
    
    // Pluralization
    pluralSeparator: '_',
    contextSeparator: '_',
    
    // Missing key handling
    saveMissing: __DEV__,
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      if (__DEV__) {
        console.warn(`Missing translation key: ${key} for language: ${lng}`);
      }
    }
  });

// Helper functions for language management
export const changeLanguage = async (language) => {
  try {
    await i18n.changeLanguage(language);
    await AsyncStorage.setItem('user-language', language);
    return true;
  } catch (error) {
    console.error('Error changing language:', error);
    return false;
  }
};

export const getCurrentLanguage = () => {
  return i18n.language;
};

export const getSupportedLanguages = () => {
  return [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'de', name: 'German', nativeName: 'Deutsch' },
    { code: 'de-CH', name: 'Swiss German', nativeName: 'Schwiizerdütsch' }
  ];
};

export const isRTL = () => {
  // None of our supported languages are RTL, but this can be extended
  return false;
};

// Swiss German specific helpers
export const getSwissGermanGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) {
    return i18n.t('swissSpecific.greetings.grueziMitenand');
  } else if (hour < 18) {
    return i18n.t('swissSpecific.greetings.hoi');
  } else {
    return i18n.t('swissSpecific.greetings.salue');
  }
};

export const getMealTimeSwissGerman = () => {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 9) {
    return i18n.t('swissSpecific.morgenessen');
  } else if (hour >= 9 && hour < 11) {
    return i18n.t('swissSpecific.znueni');
  } else if (hour >= 11 && hour < 14) {
    return i18n.t('swissSpecific.zmittag');
  } else if (hour >= 14 && hour < 17) {
    return i18n.t('swissSpecific.zvieri');
  } else {
    return i18n.t('swissSpecific.znacht');
  }
};

// Format numbers according to locale
export const formatNumber = (number, options = {}) => {
  const locale = getCurrentLanguage();
  return new Intl.NumberFormat(locale, options).format(number);
};

// Format currency according to locale
export const formatCurrency = (amount) => {
  const locale = getCurrentLanguage();
  const currency = locale === 'de-CH' ? 'CHF' : 'EUR';
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount);
};

// Format date according to locale
export const formatDate = (date, options = {}) => {
  const locale = getCurrentLanguage();
  return new Intl.DateTimeFormat(locale, options).format(new Date(date));
};

// Format time according to locale
export const formatTime = (date) => {
  const locale = getCurrentLanguage();
  return new Intl.DateTimeFormat(locale, {
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
};

export default i18n;
