/**
 * Custom Translation Hook for Znü<PERSON>Zähler
 * Provides enhanced translation functionality with Swiss German support
 */

import { useTranslation as useI18nTranslation } from 'react-i18next';
import { 
  getCurrentLanguage, 
  changeLanguage, 
  getSupportedLanguages,
  getSwissGermanGreeting,
  getMealTimeSwissGerman,
  formatNumber,
  formatCurrency,
  formatDate,
  formatTime
} from '../locales/i18n';

/**
 * Enhanced translation hook with additional functionality
 */
export const useTranslation = (namespace = 'translation') => {
  const { t, i18n, ready } = useI18nTranslation(namespace);
  
  // Get current language info
  const currentLanguage = getCurrentLanguage();
  const isSwissGerman = currentLanguage === 'de-CH';
  const isGerman = currentLanguage === 'de' || isSwissGerman;
  const isEnglish = currentLanguage === 'en';
  
  /**
   * Enhanced translation function with Swiss German context
   */
  const translate = (key, options = {}) => {
    // Handle Swiss German specific keys
    if (isSwissGerman && key.startsWith('swissSpecific.')) {
      return t(key, options);
    }
    
    // Handle meal time context for Swiss German
    if (isSwissGerman && key === 'mealTime') {
      return getMealTimeSwissGerman();
    }
    
    // Handle greetings for Swiss German
    if (isSwissGerman && key === 'greeting') {
      return getSwissGermanGreeting();
    }
    
    // Standard translation
    return t(key, options);
  };
  
  /**
   * Get localized nutrient name with proper German grammar
   */
  const getNutrientName = (nutrientKey, isPlural = false) => {
    const baseName = t(`nutrients.${nutrientKey}`);
    
    if (isGerman && isPlural) {
      // Handle German pluralization for nutrients
      switch (nutrientKey) {
        case 'calories':
          return baseName; // Already plural in German
        case 'protein':
          return 'Proteine';
        case 'carbs':
        case 'carbohydrates':
          return baseName; // Already plural
        case 'fat':
          return 'Fette';
        case 'fiber':
          return baseName; // Already plural
        case 'sugar':
          return isSwissGerman ? 'Zucker' : 'Zucker';
        default:
          return baseName;
      }
    }
    
    return baseName;
  };
  
  /**
   * Get localized day name with Swiss German variants
   */
  const getDayName = (dayKey, isShort = false) => {
    const suffix = isShort ? '' : '';
    const key = isShort ? `days.${dayKey}` : `days.${dayKey}`;
    return t(key);
  };
  
  /**
   * Get localized month name
   */
  const getMonthName = (monthKey) => {
    return t(`months.${monthKey}`);
  };
  
  /**
   * Format nutrition value with proper units and locale
   */
  const formatNutritionValue = (value, unit) => {
    if (typeof value !== 'number') return value;
    
    const formattedNumber = formatNumber(value, {
      minimumFractionDigits: 0,
      maximumFractionDigits: unit === 'kcal' ? 0 : 1
    });
    
    const localizedUnit = t(`units.${unit}`) || unit;
    
    // Handle spacing for different locales
    if (isGerman) {
      return `${formattedNumber} ${localizedUnit}`;
    } else {
      return `${formattedNumber}${localizedUnit}`;
    }
  };
  
  /**
   * Get contextual food-related translations
   */
  const getFoodContext = (context) => {
    switch (context) {
      case 'breakfast':
        return isSwissGerman ? t('swissSpecific.morgenessen') : t('meals.breakfast');
      case 'snack_morning':
        return isSwissGerman ? t('swissSpecific.znueni') : t('meals.snack');
      case 'lunch':
        return isSwissGerman ? t('swissSpecific.zmittag') : t('meals.lunch');
      case 'snack_afternoon':
        return isSwissGerman ? t('swissSpecific.zvieri') : t('meals.snack');
      case 'dinner':
        return isSwissGerman ? t('swissSpecific.znacht') : t('meals.dinner');
      default:
        return t(`meals.${context}`);
    }
  };
  
  /**
   * Get polite forms for Swiss German
   */
  const getPoliteForm = (key) => {
    if (isSwissGerman) {
      // Use "Sie" form for Swiss German politeness
      switch (key) {
        case 'welcome':
          return 'Willkomme Sie bim ZnüniZähler';
        case 'please_enter':
          return 'Bitte gäbed Sie ii';
        case 'thank_you':
          return 'Merci vilmal';
        default:
          return t(key);
      }
    }
    return t(key);
  };
  
  /**
   * Handle error messages with cultural context
   */
  const getErrorMessage = (errorKey, context = {}) => {
    const baseMessage = t(`errors.${errorKey}`, context);
    
    if (isSwissGerman) {
      // Add Swiss German politeness markers
      return `${baseMessage} Merci für's Verständnis.`;
    }
    
    return baseMessage;
  };
  
  /**
   * Get language-specific number formatting
   */
  const getLocalizedNumber = (number, options = {}) => {
    return formatNumber(number, options);
  };
  
  /**
   * Get language-specific date formatting
   */
  const getLocalizedDate = (date, options = {}) => {
    return formatDate(date, options);
  };
  
  /**
   * Get language-specific time formatting
   */
  const getLocalizedTime = (date) => {
    return formatTime(date);
  };
  
  /**
   * Change language with validation
   */
  const switchLanguage = async (languageCode) => {
    const supportedLanguages = getSupportedLanguages();
    const isSupported = supportedLanguages.some(lang => lang.code === languageCode);
    
    if (!isSupported) {
      console.warn(`Language ${languageCode} is not supported`);
      return false;
    }
    
    return await changeLanguage(languageCode);
  };
  
  return {
    // Core translation functions
    t: translate,
    i18n,
    ready,
    
    // Language info
    currentLanguage,
    isSwissGerman,
    isGerman,
    isEnglish,
    supportedLanguages: getSupportedLanguages(),
    
    // Enhanced translation functions
    getNutrientName,
    getDayName,
    getMonthName,
    formatNutritionValue,
    getFoodContext,
    getPoliteForm,
    getErrorMessage,
    
    // Formatting functions
    formatNumber: getLocalizedNumber,
    formatCurrency,
    formatDate: getLocalizedDate,
    formatTime: getLocalizedTime,
    
    // Language management
    changeLanguage: switchLanguage,
    
    // Swiss German specific
    getSwissGermanGreeting,
    getMealTimeSwissGerman
  };
};

export default useTranslation;
