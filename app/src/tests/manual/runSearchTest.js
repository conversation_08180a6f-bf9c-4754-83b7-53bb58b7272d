/**
 * Runner for the search test
 * This script sets up the environment and runs the search test
 */

// Mock the necessary modules for Node.js environment
global.Platform = { OS: 'web' };
global.FileSystem = { cacheDirectory: '/tmp/' };
global.SQLite = {
  openDatabase: (name) => ({
    transaction: (callback) => {
      callback({
        executeSql: (sql, params, success) => {
          console.log(`Executing SQL: ${sql}`);
          success(null, {
            rows: {
              length: 2,
              item: (index) => {
                if (sql.includes('COUNT(*)')) {
                  return { count: 10 };
                }
                if (index === 0) {
                  return {
                    id: 'usda1',
                    name: 'Apple',
                    source: 'USDA',
                    description: 'Fresh apple',
                    brand_name: '',
                    serving_size: 100,
                    serving_unit: 'g'
                  };
                }
                return {
                  id: 'foodb1',
                  name: 'Apple Juice',
                  source: 'FoodB',
                  description: 'Apple juice',
                  brand_name: 'Generic',
                  serving_size: 100,
                  serving_unit: 'ml'
                };
              }
            },
            insertId: 1,
            rowsAffected: 1
          });
        }
      });
    },
    _db: { close: () => {} }
  })
};

// Import and run the test
require('./testSearch');
