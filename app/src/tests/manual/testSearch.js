/**
 * Manual Test Script for Database Search Functionality
 *
 * This script tests the search functionality across both databases.
 * Run this script with: node -r esm testSearch.js
 */

// Import from mock service for testing
import { initializeDatabase, searchFoods, isFoodDatabaseImported } from './mockDatabaseService';

// Test the search functionality
async function testSearch() {
  try {
    console.log('Starting database search test...');

    // Initialize the database
    console.log('Initializing database...');
    await initializeDatabase();
    console.log('Database initialized successfully');

    // Check if databases are imported
    console.log('\nChecking if databases are imported:');
    const isUsdaImported = await isFoodDatabaseImported('USDA');
    const isFoodBImported = await isFoodDatabaseImported('FoodB');

    console.log(`USDA database imported: ${isUsdaImported}`);
    console.log(`FoodB database imported: ${isFoodBImported}`);

    if (!isUsdaImported && !isFoodBImported) {
      console.log('No databases are imported. Please import at least one database before testing search.');
      return;
    }

    // Test 1: Search across all databases
    console.log('\nTest 1: Search across all databases');
    const allResults = await searchFoods('apple');
    console.log(`Found ${allResults.length} results for 'apple' across all databases`);

    // Count results by source
    const usdaCount = allResults.filter(food => food.source && food.source.startsWith('USDA')).length;
    const foodbCount = allResults.filter(food => food.source && food.source.startsWith('FoodB')).length;

    console.log(`USDA results: ${usdaCount}`);
    console.log(`FoodB results: ${foodbCount}`);

    // Display first 3 results
    if (allResults.length > 0) {
      console.log('\nSample results:');
      allResults.slice(0, 3).forEach((food, index) => {
        console.log(`${index + 1}. ${food.name} (${food.source})`);
      });
    }

    // Test 2: Search USDA database only
    console.log('\nTest 2: Search USDA database only');
    const usdaResults = await searchFoods('apple', 20, 'USDA');
    console.log(`Found ${usdaResults.length} results for 'apple' in USDA database`);

    // Display first 3 results
    if (usdaResults.length > 0) {
      console.log('\nSample results:');
      usdaResults.slice(0, 3).forEach((food, index) => {
        console.log(`${index + 1}. ${food.name} (${food.source})`);
      });
    }

    // Test 3: Search FoodB database only
    console.log('\nTest 3: Search FoodB database only');
    const foodbResults = await searchFoods('apple', 20, 'FoodB');
    console.log(`Found ${foodbResults.length} results for 'apple' in FoodB database`);

    // Display first 3 results
    if (foodbResults.length > 0) {
      console.log('\nSample results:');
      foodbResults.slice(0, 3).forEach((food, index) => {
        console.log(`${index + 1}. ${food.name} (${food.source})`);
      });
    }

    // Test 4: Search with pagination
    console.log('\nTest 4: Search with pagination');
    const page1 = await searchFoods('a', 5, null, 0);
    const page2 = await searchFoods('a', 5, null, 5);

    console.log(`Page 1: ${page1.length} results`);
    console.log(`Page 2: ${page2.length} results`);

    // Check if pages are different
    const page1Ids = page1.map(food => food.id);
    const page2Ids = page2.map(food => food.id);
    const differentPages = page1Ids.every(id => !page2Ids.includes(id));

    console.log(`Pages contain different results: ${differentPages}`);

    // Test 5: Search with a specific term
    console.log('\nTest 5: Search with a specific term');
    const specificResults = await searchFoods('banana');
    console.log(`Found ${specificResults.length} results for 'banana' across all databases`);

    // Count results by source
    const specificUsdaCount = specificResults.filter(food => food.source && food.source.startsWith('USDA')).length;
    const specificFoodbCount = specificResults.filter(food => food.source && food.source.startsWith('FoodB')).length;

    console.log(`USDA results: ${specificUsdaCount}`);
    console.log(`FoodB results: ${specificFoodbCount}`);

    console.log('\nSearch tests completed successfully!');
  } catch (error) {
    console.error('Error during search test:', error);
  }
}

// Run the test
testSearch();
