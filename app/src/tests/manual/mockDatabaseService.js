/**
 * Mock Database Service for Testing
 * This file provides mock implementations of the database service functions
 */

// Mock data
const mockFoods = {
  'apple': [
    {
      id: 'usda1',
      name: 'Apple',
      source: 'USDA',
      description: 'Fresh apple',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [
        { nutrient_id: 'calories', name: 'Calories', amount: 52, unit: 'kcal' },
        { nutrient_id: 'protein', name: 'Protein', amount: 0.3, unit: 'g' }
      ],
      ingredients: []
    },
    {
      id: 'usda2',
      name: 'Apple Pie',
      source: 'USDA',
      description: 'Apple pie',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [
        { nutrient_id: 'calories', name: 'Calories', amount: 237, unit: 'kcal' },
        { nutrient_id: 'protein', name: 'Protein', amount: 2.4, unit: 'g' }
      ],
      ingredients: []
    },
    {
      id: 'foodb1',
      name: 'Apple Juice',
      source: 'FoodB',
      description: 'Apple juice',
      brand_name: 'Generic',
      serving_size: 100,
      serving_unit: 'ml',
      nutrients: [
        { nutrient_id: 'calories', name: 'Calories', amount: 46, unit: 'kcal' },
        { nutrient_id: 'protein', name: 'Protein', amount: 0.1, unit: 'g' }
      ],
      ingredients: []
    }
  ],
  'banana': [
    {
      id: 'usda3',
      name: 'Banana',
      source: 'USDA',
      description: 'Fresh banana',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [
        { nutrient_id: 'calories', name: 'Calories', amount: 89, unit: 'kcal' },
        { nutrient_id: 'protein', name: 'Protein', amount: 1.1, unit: 'g' }
      ],
      ingredients: []
    },
    {
      id: 'foodb2',
      name: 'Banana Smoothie',
      source: 'FoodB',
      description: 'Banana smoothie',
      brand_name: 'Generic',
      serving_size: 100,
      serving_unit: 'ml',
      nutrients: [
        { nutrient_id: 'calories', name: 'Calories', amount: 95, unit: 'kcal' },
        { nutrient_id: 'protein', name: 'Protein', amount: 1.5, unit: 'g' }
      ],
      ingredients: []
    }
  ],
  'a': [
    {
      id: 'usda1',
      name: 'Apple',
      source: 'USDA',
      description: 'Fresh apple',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [],
      ingredients: []
    },
    {
      id: 'usda2',
      name: 'Apple Pie',
      source: 'USDA',
      description: 'Apple pie',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [],
      ingredients: []
    },
    {
      id: 'usda3',
      name: 'Banana',
      source: 'USDA',
      description: 'Fresh banana',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [],
      ingredients: []
    },
    {
      id: 'usda4',
      name: 'Avocado',
      source: 'USDA',
      description: 'Fresh avocado',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [],
      ingredients: []
    },
    {
      id: 'foodb1',
      name: 'Apple Juice',
      source: 'FoodB',
      description: 'Apple juice',
      brand_name: 'Generic',
      serving_size: 100,
      serving_unit: 'ml',
      nutrients: [],
      ingredients: []
    },
    {
      id: 'foodb2',
      name: 'Banana Smoothie',
      source: 'FoodB',
      description: 'Banana smoothie',
      brand_name: 'Generic',
      serving_size: 100,
      serving_unit: 'ml',
      nutrients: [],
      ingredients: []
    },
    {
      id: 'foodb3',
      name: 'Almond Milk',
      source: 'FoodB',
      description: 'Almond milk',
      brand_name: 'Generic',
      serving_size: 100,
      serving_unit: 'ml',
      nutrients: [],
      ingredients: []
    },
    {
      id: 'foodb4',
      name: 'Apricot',
      source: 'FoodB',
      description: 'Fresh apricot',
      brand_name: '',
      serving_size: 100,
      serving_unit: 'g',
      nutrients: [],
      ingredients: []
    }
  ]
};

// Mock database service functions
export const initializeDatabase = async () => {
  console.log('Mock database initialized');
  return true;
};

export const searchFoods = async (query, limit = 20, source = null, offset = 0) => {
  console.log(`Mock searchFoods called with query: ${query}, limit: ${limit}, source: ${source}, offset: ${offset}`);
  
  // Get the appropriate mock data
  const key = Object.keys(mockFoods).find(k => query.toLowerCase().includes(k)) || 'a';
  let results = [...mockFoods[key]];
  
  // Filter by source if specified
  if (source) {
    results = results.filter(food => food.source.startsWith(source));
  }
  
  // Apply pagination
  results = results.slice(offset, offset + limit);
  
  return results;
};

export const isFoodDatabaseImported = async (source) => {
  console.log(`Mock isFoodDatabaseImported called with source: ${source}`);
  return true; // Both databases are imported
};
