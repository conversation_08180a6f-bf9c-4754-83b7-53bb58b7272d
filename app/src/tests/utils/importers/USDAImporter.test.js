/**
 * Tests for USDA Food Data Central Importer
 */

import USDAImporter from '../../../utils/importers/USDAImporter';
import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import { unzip } from 'react-native-zip-archive';
import {
  saveFood,
  saveNutrient,
  saveIngredient,
  saveAllergen
} from '../../../services/databaseService';

// Mock dependencies
jest.mock('expo-file-system', () => ({
  cacheDirectory: 'file:///mock-cache-directory/',
  documentDirectory: 'file:///mock-document-directory/',
  makeDirectoryAsync: jest.fn(),
  getInfoAsync: jest.fn(),
  copyAsync: jest.fn(),
  deleteAsync: jest.fn(),
  readAsStringAsync: jest.fn(),
  writeAsStringAsync: jest.fn(),
  readDirectoryAsync: jest.fn(),
}));

jest.mock('expo-document-picker', () => ({
  getDocumentAsync: jest.fn(),
}));

jest.mock('react-native-zip-archive', () => ({
  unzip: jest.fn(),
}));

jest.mock('../../../services/databaseService', () => ({
  saveFood: jest.fn(),
  saveNutrient: jest.fn(),
  saveIngredient: jest.fn(),
  saveAllergen: jest.fn(),
}));

// Mock CSV parser
jest.mock('csv-parse/lib/sync', () => jest.fn());

describe('USDAImporter', () => {
  let importer;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create a new importer instance
    importer = new USDAImporter();

    // Mock successful file system operations
    FileSystem.getInfoAsync.mockResolvedValue({ exists: false });
    FileSystem.makeDirectoryAsync.mockResolvedValue(true);
    FileSystem.deleteAsync.mockResolvedValue(true);
    FileSystem.copyAsync.mockResolvedValue(true);
    FileSystem.readDirectoryAsync.mockResolvedValue([]);

    // Mock successful document picker
    DocumentPicker.getDocumentAsync.mockResolvedValue({
      type: 'success',
      uri: 'file:///mock-document.zip',
      name: 'mock-document.zip',
      size: 1000,
    });

    // Mock successful unzip
    unzip.mockResolvedValue('file:///mock-extract-directory/');

    // Mock successful database operations
    saveFood.mockResolvedValue({ id: 'mock-food-id' });
    saveNutrient.mockResolvedValue({ id: 'mock-nutrient-id' });
    saveIngredient.mockResolvedValue({ id: 'mock-ingredient-id' });
    saveAllergen.mockResolvedValue({ id: 'mock-allergen-id' });
  });

  describe('initialize', () => {
    test('should create temp directory if it does not exist', async () => {
      // Arrange
      FileSystem.getInfoAsync.mockResolvedValue({ exists: false });

      // Act
      const result = await importer.initialize();

      // Assert
      expect(FileSystem.getInfoAsync).toHaveBeenCalledWith(importer.tempDir);
      expect(FileSystem.makeDirectoryAsync).toHaveBeenCalledWith(importer.tempDir, { intermediates: true });
      expect(result).toBe(true);
    });

    test('should not create temp directory if it already exists', async () => {
      // Arrange
      FileSystem.getInfoAsync.mockResolvedValue({ exists: true });

      // Act
      const result = await importer.initialize();

      // Assert
      expect(FileSystem.getInfoAsync).toHaveBeenCalledWith(importer.tempDir);
      expect(FileSystem.makeDirectoryAsync).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    test('should handle errors', async () => {
      // Arrange
      FileSystem.getInfoAsync.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.initialize();

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('selectDataset', () => {
    test('should select and copy dataset', async () => {
      // Act
      const result = await importer.selectDataset();

      // Assert
      expect(DocumentPicker.getDocumentAsync).toHaveBeenCalledWith({
        type: ['application/zip', 'application/x-zip-compressed'],
        copyToCacheDirectory: true,
      });
      expect(FileSystem.copyAsync).toHaveBeenCalledWith({
        from: 'file:///mock-document.zip',
        to: importer.tempDir + 'usda_dataset.zip',
      });
      expect(result).toBe(importer.tempDir + 'usda_dataset.zip');
    });

    test('should return null if user cancels', async () => {
      // Arrange
      DocumentPicker.getDocumentAsync.mockResolvedValue({
        type: 'cancel',
      });

      // Act
      const result = await importer.selectDataset();

      // Assert
      expect(result).toBeNull();
      expect(FileSystem.copyAsync).not.toHaveBeenCalled();
    });

    test('should handle errors', async () => {
      // Arrange
      DocumentPicker.getDocumentAsync.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.selectDataset();

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('extractDataset', () => {
    test('should extract dataset', async () => {
      // Act
      const result = await importer.extractDataset(importer.tempDir + 'usda_dataset.zip');

      // Assert
      expect(unzip).toHaveBeenCalledWith(
        importer.tempDir + 'usda_dataset.zip',
        importer.tempDir + 'extracted/'
      );
      expect(result).toBe('file:///mock-extract-directory/');
    });

    test('should handle errors', async () => {
      // Arrange
      unzip.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.extractDataset(importer.tempDir + 'usda_dataset.zip');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('detectDatasetType', () => {
    test('should detect foundation foods dataset', async () => {
      // Arrange
      FileSystem.readDirectoryAsync.mockResolvedValue(['food.csv', 'nutrient.csv', 'input_food.csv']);

      // Act
      const result = await importer.detectDatasetType(importer.tempDir + 'extracted/');

      // Assert
      expect(FileSystem.readDirectoryAsync).toHaveBeenCalledWith(importer.tempDir + 'extracted/');
      expect(result).toBe('foundation');
    });

    test('should detect SR Legacy dataset', async () => {
      // Arrange
      FileSystem.readDirectoryAsync.mockResolvedValue(['food.csv', 'food_nutrient.csv']);

      // Act
      const result = await importer.detectDatasetType(importer.tempDir + 'extracted/');

      // Assert
      expect(result).toBe('sr_legacy');
    });

    test('should detect branded foods dataset', async () => {
      // Arrange
      FileSystem.readDirectoryAsync.mockResolvedValue(['branded_food.csv']);

      // Act
      const result = await importer.detectDatasetType(importer.tempDir + 'extracted/');

      // Assert
      expect(result).toBe('branded');
    });

    test('should detect survey foods dataset', async () => {
      // Arrange
      FileSystem.readDirectoryAsync.mockResolvedValue(['survey_fndds_food.csv']);

      // Act
      const result = await importer.detectDatasetType(importer.tempDir + 'extracted/');

      // Assert
      expect(result).toBe('survey');
    });

    test('should return unknown for unrecognized dataset', async () => {
      // Arrange
      FileSystem.readDirectoryAsync.mockResolvedValue(['unknown.csv']);

      // Act
      const result = await importer.detectDatasetType(importer.tempDir + 'extracted/');

      // Assert
      expect(result).toBe('unknown');
    });

    test('should handle errors', async () => {
      // Arrange
      FileSystem.readDirectoryAsync.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.detectDatasetType(importer.tempDir + 'extracted/');

      // Assert
      expect(result).toBe('unknown');
    });
  });

  describe('parseCSV', () => {
    test('should parse CSV file', async () => {
      // Arrange
      const mockCSVContent = 'id,name\n1,Apple\n2,Banana';
      const mockParsedData = [
        { id: '1', name: 'Apple' },
        { id: '2', name: 'Banana' }
      ];
      FileSystem.readAsStringAsync.mockResolvedValue(mockCSVContent);
      require('csv-parse/lib/sync').mockReturnValue(mockParsedData);

      // Act
      const result = await importer.parseCSV('file:///mock-file.csv');

      // Assert
      expect(FileSystem.readAsStringAsync).toHaveBeenCalledWith('file:///mock-file.csv');
      expect(require('csv-parse/lib/sync')).toHaveBeenCalledWith(mockCSVContent, {
        columns: true,
        skip_empty_lines: true
      });
      expect(result).toEqual(mockParsedData);
    });

    test('should handle errors', async () => {
      // Arrange
      FileSystem.readAsStringAsync.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.parseCSV('file:///mock-file.csv');

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('cleanupTempFiles', () => {
    test('should delete temp files', async () => {
      // Act
      await importer.cleanupTempFiles();

      // Assert
      expect(FileSystem.deleteAsync).toHaveBeenCalledWith(importer.tempDir, { idempotent: true });
      expect(FileSystem.makeDirectoryAsync).toHaveBeenCalledWith(importer.tempDir, { intermediates: true });
    });

    test('should handle errors', async () => {
      // Arrange
      FileSystem.deleteAsync.mockRejectedValue(new Error('Mock error'));

      // Act & Assert
      await expect(importer.cleanupTempFiles()).resolves.not.toThrow();
    });
  });

  describe('importFoundationFoods', () => {
    beforeEach(() => {
      // Mock CSV parsing
      importer.parseCSV = jest.fn();

      // Mock food data
      importer.parseCSV.mockImplementation((filePath) => {
        if (filePath.includes('food.csv')) {
          return Promise.resolve([
            { fdc_id: '1', description: 'Apple' },
            { fdc_id: '2', description: 'Banana' }
          ]);
        } else if (filePath.includes('food_nutrient.csv')) {
          return Promise.resolve([
            { fdc_id: '1', nutrient_id: '203', amount: '0.3' }, // Protein
            { fdc_id: '1', nutrient_id: '204', amount: '0.2' }, // Fat
            { fdc_id: '2', nutrient_id: '203', amount: '1.1' }  // Protein
          ]);
        } else if (filePath.includes('nutrient.csv')) {
          return Promise.resolve([
            { id: '203', name: 'Protein', unit_name: 'g' },
            { id: '204', name: 'Total lipid (fat)', unit_name: 'g' }
          ]);
        } else if (filePath.includes('input_food.csv')) {
          return Promise.resolve([
            { fdc_id: '1', input_food_id: '3', description: 'Apple skin' },
            { fdc_id: '2', input_food_id: '4', description: 'Banana peel' }
          ]);
        }
        return Promise.resolve([]);
      });
    });

    test('should import foundation foods', async () => {
      // Act
      const result = await importer.importFoundationFoods('mock-path/');

      // Assert
      expect(importer.parseCSV).toHaveBeenCalledTimes(4);
      expect(saveFood).toHaveBeenCalledTimes(2);
      expect(saveNutrient).toHaveBeenCalledTimes(3);
      expect(saveIngredient).toHaveBeenCalledTimes(2);
      expect(result).toBe(true);
      expect(importer.importStats.foods).toBe(2);
      expect(importer.importStats.nutrients).toBe(3);
      expect(importer.importStats.ingredients).toBe(2);
    });

    test('should handle errors during food import', async () => {
      // Arrange
      saveFood.mockRejectedValueOnce(new Error('Mock error'));

      // Act
      const result = await importer.importFoundationFoods('mock-path/');

      // Assert
      expect(result).toBe(true); // Overall import still succeeds
      expect(importer.importStats.foods).toBe(1); // Only one food imported
      expect(importer.importStats.errors).toBe(1); // One error recorded
    });

    test('should handle overall import failure', async () => {
      // Arrange
      importer.parseCSV.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.importFoundationFoods('mock-path/');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('importSRLegacyFoods', () => {
    beforeEach(() => {
      // Mock CSV parsing
      importer.parseCSV = jest.fn();

      // Mock food data
      importer.parseCSV.mockImplementation((filePath) => {
        if (filePath.includes('food.csv')) {
          return Promise.resolve([
            { fdc_id: '1', description: 'Apple' },
            { fdc_id: '2', description: 'Banana' }
          ]);
        } else if (filePath.includes('food_nutrient.csv')) {
          return Promise.resolve([
            { fdc_id: '1', nutrient_id: '203', amount: '0.3' }, // Protein
            { fdc_id: '1', nutrient_id: '204', amount: '0.2' }, // Fat
            { fdc_id: '2', nutrient_id: '203', amount: '1.1' }  // Protein
          ]);
        } else if (filePath.includes('nutrient.csv')) {
          return Promise.resolve([
            { id: '203', name: 'Protein', unit_name: 'g' },
            { id: '204', name: 'Total lipid (fat)', unit_name: 'g' }
          ]);
        }
        return Promise.resolve([]);
      });
    });

    test('should import SR Legacy foods', async () => {
      // Act
      const result = await importer.importSRLegacyFoods('mock-path/');

      // Assert
      expect(importer.parseCSV).toHaveBeenCalledTimes(3);
      expect(saveFood).toHaveBeenCalledTimes(2);
      expect(saveNutrient).toHaveBeenCalledTimes(3);
      expect(result).toBe(true);
      expect(importer.importStats.foods).toBe(2);
      expect(importer.importStats.nutrients).toBe(3);
    });

    test('should handle errors during food import', async () => {
      // Arrange
      saveFood.mockRejectedValueOnce(new Error('Mock error'));

      // Act
      const result = await importer.importSRLegacyFoods('mock-path/');

      // Assert
      expect(result).toBe(true); // Overall import still succeeds
      expect(importer.importStats.foods).toBe(1); // Only one food imported
      expect(importer.importStats.errors).toBe(1); // One error recorded
    });

    test('should handle overall import failure', async () => {
      // Arrange
      importer.parseCSV.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.importSRLegacyFoods('mock-path/');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('importBrandedFoods', () => {
    beforeEach(() => {
      // Mock CSV parsing
      importer.parseCSV = jest.fn();

      // Mock food data
      importer.parseCSV.mockImplementation((filePath) => {
        if (filePath.includes('branded_food.csv')) {
          return Promise.resolve([
            {
              fdc_id: '1',
              description: 'Brand A Apple Juice',
              brand_name: 'Brand A',
              ingredients: 'Apple juice, water, sugar',
              serving_size: '240',
              serving_size_unit: 'ml',
              gtin_upc: '1234567890'
            },
            {
              fdc_id: '2',
              description: 'Brand B Banana Chips',
              brand_name: 'Brand B',
              ingredients: 'Banana, oil, salt',
              serving_size: '30',
              serving_size_unit: 'g',
              gtin_upc: '0987654321'
            }
          ]);
        } else if (filePath.includes('food_nutrient.csv')) {
          return Promise.resolve([
            { fdc_id: '1', nutrient_id: '203', amount: '0.3' }, // Protein
            { fdc_id: '1', nutrient_id: '204', amount: '0.2' }, // Fat
            { fdc_id: '2', nutrient_id: '203', amount: '1.1' }  // Protein
          ]);
        } else if (filePath.includes('nutrient.csv')) {
          return Promise.resolve([
            { id: '203', name: 'Protein', unit_name: 'g' },
            { id: '204', name: 'Total lipid (fat)', unit_name: 'g' }
          ]);
        }
        return Promise.resolve([]);
      });
    });

    test('should import branded foods', async () => {
      // Act
      const result = await importer.importBrandedFoods('mock-path/');

      // Assert
      expect(importer.parseCSV).toHaveBeenCalledTimes(3);
      expect(saveFood).toHaveBeenCalledTimes(2);
      expect(saveNutrient).toHaveBeenCalledTimes(3);
      expect(saveIngredient).toHaveBeenCalledWith(expect.objectContaining({ name: 'Apple juice' }));
      expect(result).toBe(true);
      expect(importer.importStats.foods).toBe(2);
      expect(importer.importStats.nutrients).toBe(3);
    });

    test('should handle errors during food import', async () => {
      // Arrange
      saveFood.mockRejectedValueOnce(new Error('Mock error'));

      // Act
      const result = await importer.importBrandedFoods('mock-path/');

      // Assert
      expect(result).toBe(true); // Overall import still succeeds
      expect(importer.importStats.foods).toBe(1); // Only one food imported
      expect(importer.importStats.errors).toBe(1); // One error recorded
    });

    test('should handle overall import failure', async () => {
      // Arrange
      importer.parseCSV.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.importBrandedFoods('mock-path/');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('importSurveyFoods', () => {
    beforeEach(() => {
      // Mock CSV parsing
      importer.parseCSV = jest.fn();

      // Mock food data
      importer.parseCSV.mockImplementation((filePath) => {
        if (filePath.includes('survey_fndds_food.csv')) {
          return Promise.resolve([
            { fdc_id: '1', description: 'Apple, raw' },
            { fdc_id: '2', description: 'Banana, raw' }
          ]);
        } else if (filePath.includes('food_nutrient.csv')) {
          return Promise.resolve([
            { fdc_id: '1', nutrient_id: '203', amount: '0.3' }, // Protein
            { fdc_id: '1', nutrient_id: '204', amount: '0.2' }, // Fat
            { fdc_id: '2', nutrient_id: '203', amount: '1.1' }  // Protein
          ]);
        } else if (filePath.includes('nutrient.csv')) {
          return Promise.resolve([
            { id: '203', name: 'Protein', unit_name: 'g' },
            { id: '204', name: 'Total lipid (fat)', unit_name: 'g' }
          ]);
        }
        return Promise.resolve([]);
      });
    });

    test('should import survey foods', async () => {
      // Act
      const result = await importer.importSurveyFoods('mock-path/');

      // Assert
      expect(importer.parseCSV).toHaveBeenCalledTimes(3);
      expect(saveFood).toHaveBeenCalledTimes(2);
      expect(saveNutrient).toHaveBeenCalledTimes(3);
      expect(result).toBe(true);
      expect(importer.importStats.foods).toBe(2);
      expect(importer.importStats.nutrients).toBe(3);
    });

    test('should handle errors during food import', async () => {
      // Arrange
      saveFood.mockRejectedValueOnce(new Error('Mock error'));

      // Act
      const result = await importer.importSurveyFoods('mock-path/');

      // Assert
      expect(result).toBe(true); // Overall import still succeeds
      expect(importer.importStats.foods).toBe(1); // Only one food imported
      expect(importer.importStats.errors).toBe(1); // One error recorded
    });

    test('should handle overall import failure', async () => {
      // Arrange
      importer.parseCSV.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.importSurveyFoods('mock-path/');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('startImport', () => {
    beforeEach(() => {
      // Mock the import methods
      importer.initialize = jest.fn().mockResolvedValue(true);
      importer.selectDataset = jest.fn().mockResolvedValue('mock-zip-path');
      importer.extractDataset = jest.fn().mockResolvedValue('mock-extract-path');
      importer.detectDatasetType = jest.fn().mockResolvedValue('foundation');
      importer.importFoundationFoods = jest.fn().mockResolvedValue(true);
      importer.importSRLegacyFoods = jest.fn().mockResolvedValue(true);
      importer.importBrandedFoods = jest.fn().mockResolvedValue(true);
      importer.importSurveyFoods = jest.fn().mockResolvedValue(true);
      importer.cleanupTempFiles = jest.fn().mockResolvedValue();
    });

    test('should start import process for foundation foods', async () => {
      // Act
      const result = await importer.startImport();

      // Assert
      expect(importer.initialize).toHaveBeenCalled();
      expect(importer.selectDataset).toHaveBeenCalled();
      expect(importer.extractDataset).toHaveBeenCalledWith('mock-zip-path');
      expect(importer.detectDatasetType).toHaveBeenCalledWith('mock-extract-path');
      expect(importer.importFoundationFoods).toHaveBeenCalledWith('mock-extract-path');
      expect(importer.cleanupTempFiles).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        datasetType: 'foundation',
        stats: importer.importStats
      });
    });

    test('should start import process for SR Legacy foods', async () => {
      // Arrange
      importer.detectDatasetType.mockResolvedValue('sr_legacy');

      // Act
      const result = await importer.startImport();

      // Assert
      expect(importer.importSRLegacyFoods).toHaveBeenCalledWith('mock-extract-path');
      expect(result).toEqual({
        success: true,
        datasetType: 'sr_legacy',
        stats: importer.importStats
      });
    });

    test('should start import process for branded foods', async () => {
      // Arrange
      importer.detectDatasetType.mockResolvedValue('branded');

      // Act
      const result = await importer.startImport();

      // Assert
      expect(importer.importBrandedFoods).toHaveBeenCalledWith('mock-extract-path');
      expect(result).toEqual({
        success: true,
        datasetType: 'branded',
        stats: importer.importStats
      });
    });

    test('should start import process for survey foods', async () => {
      // Arrange
      importer.detectDatasetType.mockResolvedValue('survey');

      // Act
      const result = await importer.startImport();

      // Assert
      expect(importer.importSurveyFoods).toHaveBeenCalledWith('mock-extract-path');
      expect(result).toEqual({
        success: true,
        datasetType: 'survey',
        stats: importer.importStats
      });
    });

    test('should handle initialization failure', async () => {
      // Arrange
      importer.initialize.mockResolvedValue(false);

      // Act
      const result = await importer.startImport();

      // Assert
      expect(result).toEqual({
        success: false,
        error: 'Failed to initialize importer',
        stats: importer.importStats
      });
    });

    test('should handle dataset selection failure', async () => {
      // Arrange
      importer.selectDataset.mockResolvedValue(null);

      // Act
      const result = await importer.startImport();

      // Assert
      expect(result).toEqual({
        success: false,
        error: 'No dataset selected',
        stats: importer.importStats
      });
    });

    test('should handle dataset extraction failure', async () => {
      // Arrange
      importer.extractDataset.mockResolvedValue(null);

      // Act
      const result = await importer.startImport();

      // Assert
      expect(result).toEqual({
        success: false,
        error: 'Failed to extract dataset',
        stats: importer.importStats
      });
    });

    test('should handle unknown dataset type', async () => {
      // Arrange
      importer.detectDatasetType.mockResolvedValue('unknown');

      // Act
      const result = await importer.startImport();

      // Assert
      expect(result).toEqual({
        success: false,
        error: 'Unknown dataset type',
        stats: importer.importStats
      });
    });

    test('should handle general errors', async () => {
      // Arrange
      importer.initialize.mockRejectedValue(new Error('Mock error'));

      // Act
      const result = await importer.startImport();

      // Assert
      expect(result).toEqual({
        success: false,
        error: 'Mock error',
        stats: importer.importStats
      });
    });
  });
});
