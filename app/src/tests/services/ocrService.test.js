/**
 * Tests for OCR Service
 */

import ocrService from '../../services/ocrService';

// Mock dependencies
jest.mock('expo-image-manipulator', () => ({
  manipulateAsync: jest.fn(),
  SaveFormat: {
    JPEG: 'jpeg'
  }
}));

jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios'
  }
}));

describe('OCRService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      const result = await ocrService.initialize();
      expect(result).toBe(true);
      expect(ocrService.isInitialized).toBe(true);
    });
  });

  describe('extractNutritionFacts', () => {
    it('should extract calories from text', () => {
      const text = 'Calories: 250 per serving';
      const result = ocrService.extractNutritionFacts(text);
      
      expect(result.calories).toBe(250);
    });

    it('should extract protein from text', () => {
      const text = 'Protein 15g';
      const result = ocrService.extractNutritionFacts(text);
      
      expect(result.protein).toBe(15);
    });

    it('should extract carbohydrates from text', () => {
      const text = 'Total Carbohydrates 30g';
      const result = ocrService.extractNutritionFacts(text);
      
      expect(result.carbs).toBe(30);
    });

    it('should extract fat from text', () => {
      const text = 'Total Fat 8g';
      const result = ocrService.extractNutritionFacts(text);
      
      expect(result.fat).toBe(8);
    });

    it('should extract multiple nutrients from complex text', () => {
      const text = `
        Nutrition Facts
        Serving Size 1 cup (240ml)
        Calories 150
        Total Fat 3g
        Saturated Fat 1.5g
        Cholesterol 10mg
        Sodium 130mg
        Total Carbohydrates 22g
        Dietary Fiber 0g
        Total Sugars 12g
        Protein 8g
      `;
      
      const result = ocrService.extractNutritionFacts(text);
      
      expect(result.calories).toBe(150);
      expect(result.fat).toBe(3);
      expect(result.saturatedFat).toBe(1.5);
      expect(result.cholesterol).toBe(10);
      expect(result.sodium).toBe(130);
      expect(result.carbs).toBe(22);
      expect(result.fiber).toBe(0);
      expect(result.sugar).toBe(12);
      expect(result.protein).toBe(8);
    });

    it('should handle missing nutrients gracefully', () => {
      const text = 'This text has no nutrition information';
      const result = ocrService.extractNutritionFacts(text);
      
      expect(Object.keys(result)).toHaveLength(0);
    });
  });

  describe('extractIngredients', () => {
    it('should extract ingredients list', () => {
      const text = 'Ingredients: Water, Sugar, Natural Flavors, Citric Acid';
      const result = ocrService.extractIngredients(text);
      
      expect(result).toEqual(['Water', 'Sugar', 'Natural Flavors', 'Citric Acid']);
    });

    it('should handle ingredients with different separators', () => {
      const text = 'Ingredients: Milk; Eggs; Wheat flour; Salt';
      const result = ocrService.extractIngredients(text);
      
      expect(result).toEqual(['Milk', 'Eggs', 'Wheat flour', 'Salt']);
    });

    it('should handle multi-line ingredients', () => {
      const text = `
        Ingredients: Enriched flour (wheat flour, niacin, 
        reduced iron, thiamine mononitrate), sugar, 
        vegetable oil, eggs, baking powder
      `;
      const result = ocrService.extractIngredients(text);
      
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toContain('Enriched flour');
    });

    it('should return empty array when no ingredients found', () => {
      const text = 'No ingredients listed here';
      const result = ocrService.extractIngredients(text);
      
      expect(result).toEqual([]);
    });
  });

  describe('extractAllergens', () => {
    it('should extract allergens from contains statement', () => {
      const text = 'Contains: Milk, Eggs, Wheat, Soy';
      const result = ocrService.extractAllergens(text);
      
      expect(result).toEqual(expect.arrayContaining(['milk', 'eggs', 'wheat']));
    });

    it('should extract allergens from may contain statement', () => {
      const text = 'May contain peanuts and tree nuts';
      const result = ocrService.extractAllergens(text);
      
      expect(result).toEqual(expect.arrayContaining(['peanuts', 'tree nuts']));
    });

    it('should handle case insensitive matching', () => {
      const text = 'CONTAINS: MILK, EGGS, WHEAT';
      const result = ocrService.extractAllergens(text);
      
      expect(result).toEqual(expect.arrayContaining(['milk', 'eggs', 'wheat']));
    });

    it('should return empty array when no allergens found', () => {
      const text = 'No allergen information';
      const result = ocrService.extractAllergens(text);
      
      expect(result).toEqual([]);
    });
  });

  describe('extractAdditives', () => {
    it('should extract E-numbers from text', () => {
      const text = 'Contains E100, E150a, E330, E621';
      const result = ocrService.extractAdditives(text);
      
      expect(result).toEqual(['E100', 'E150a', 'E330', 'E621']);
    });

    it('should handle mixed case E-numbers', () => {
      const text = 'Contains e100, E150A, e330';
      const result = ocrService.extractAdditives(text);
      
      expect(result).toEqual(['e100', 'E150A', 'e330']);
    });

    it('should deduplicate E-numbers', () => {
      const text = 'Contains E100, E150a, E100, E330';
      const result = ocrService.extractAdditives(text);
      
      expect(result).toEqual(['E100', 'E150a', 'E330']);
    });

    it('should return empty array when no E-numbers found', () => {
      const text = 'No additives listed';
      const result = ocrService.extractAdditives(text);
      
      expect(result).toEqual([]);
    });
  });

  describe('extractServingInfo', () => {
    it('should extract serving size and unit', () => {
      const text = 'Serving Size: 100g';
      const result = ocrService.extractServingInfo(text);
      
      expect(result.size).toBe(100);
      expect(result.unit).toBe('g');
    });

    it('should handle different units', () => {
      const text = 'Serving Size: 1 cup (240ml)';
      const result = ocrService.extractServingInfo(text);
      
      expect(result.size).toBe(1);
      expect(result.unit).toBe('cup');
    });

    it('should handle decimal serving sizes', () => {
      const text = 'Serving Size: 2.5 oz';
      const result = ocrService.extractServingInfo(text);
      
      expect(result.size).toBe(2.5);
      expect(result.unit).toBe('oz');
    });

    it('should return default when no serving info found', () => {
      const text = 'No serving information';
      const result = ocrService.extractServingInfo(text);
      
      expect(result.size).toBe(100);
      expect(result.unit).toBe('g');
    });
  });

  describe('calculateConfidence', () => {
    it('should calculate confidence from text annotations', () => {
      const textAnnotations = [
        { confidence: 0.9 },
        { confidence: 0.8 },
        { confidence: 0.95 }
      ];
      
      const result = ocrService.calculateConfidence(textAnnotations);
      
      expect(result).toBeCloseTo(0.883, 2);
    });

    it('should return default confidence when no annotations', () => {
      const result = ocrService.calculateConfidence([]);
      
      expect(result).toBe(0.8);
    });

    it('should handle annotations without confidence', () => {
      const textAnnotations = [
        { text: 'some text' },
        { text: 'more text' }
      ];
      
      const result = ocrService.calculateConfidence(textAnnotations);
      
      expect(result).toBe(0.8);
    });
  });

  describe('postProcessOCR', () => {
    it('should process OCR results and extract all information', async () => {
      const ocrResult = {
        text: `
          Nutrition Facts
          Serving Size: 100g
          Calories 250
          Protein 15g
          Total Fat 8g
          Ingredients: Water, Sugar, Natural Flavors
          Contains: Milk, Eggs
          E100, E330
        `,
        confidence: 0.9
      };
      
      const result = await ocrService.postProcessOCR(ocrResult);
      
      expect(result.rawText).toContain('Nutrition Facts');
      expect(result.nutritionFacts.calories).toBe(250);
      expect(result.nutritionFacts.protein).toBe(15);
      expect(result.nutritionFacts.fat).toBe(8);
      expect(result.ingredients).toEqual(expect.arrayContaining(['Water', 'Sugar', 'Natural Flavors']));
      expect(result.allergens).toEqual(expect.arrayContaining(['milk', 'eggs']));
      expect(result.additives).toEqual(expect.arrayContaining(['E100', 'E330']));
      expect(result.servingInfo.size).toBe(100);
      expect(result.servingInfo.unit).toBe('g');
      expect(result.confidence).toBe(0.9);
    });
  });

  describe('error handling', () => {
    it('should handle processing errors gracefully', async () => {
      const invalidImageUri = 'invalid://uri';
      
      const result = await ocrService.processImage(invalidImageUri);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle empty text input', () => {
      const result = ocrService.extractNutritionFacts('');
      
      expect(Object.keys(result)).toHaveLength(0);
    });

    it('should handle null text input', () => {
      const result = ocrService.extractNutritionFacts(null);
      
      expect(Object.keys(result)).toHaveLength(0);
    });
  });
});
