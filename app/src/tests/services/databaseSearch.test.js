/**
 * Tests for Database Search Functionality
 * Tests searching across multiple databases
 */

import * as databaseService from '../../services/databaseService';
import dbManager from '../../database/DatabaseManager';

// Mock the database manager
jest.mock('../../database/DatabaseManager', () => ({
  executeQuery: jest.fn(),
  isFoodDatabaseImported: jest.fn()
}));

describe('Database Search Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should search foods across all databases', async () => {
    // Mock data for foods from both databases
    const mockFoods = [
      { id: 'usda1', name: 'Apple', source: 'USDA' },
      { id: 'foodb1', name: 'Apple Juice', source: 'FoodB' }
    ];

    // Mock the executeQuery response
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockFoods.length,
        item: (index) => mockFoods[index]
      }
    });

    // Call the searchFoods function with no source filter
    const results = await databaseService.searchFoods('apple');

    // Verify the SQL query was executed with the correct parameters
    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM Food'),
      expect.arrayContaining(['%apple%', 20])
    );

    // Verify the query doesn't include source filtering
    const sqlQuery = dbManager.executeQuery.mock.calls[0][0];
    expect(sqlQuery).not.toContain('AND source LIKE ?');

    // Verify the results include foods from both databases
    expect(results).toHaveLength(2);
    expect(results.some(food => food.source === 'USDA')).toBeTruthy();
    expect(results.some(food => food.source === 'FoodB')).toBeTruthy();
  });

  test('should search foods from a specific database', async () => {
    // Mock data for foods from USDA database only
    const mockFoods = [
      { id: 'usda1', name: 'Apple', source: 'USDA' },
      { id: 'usda2', name: 'Apple Pie', source: 'USDA' }
    ];

    // Mock the executeQuery response
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockFoods.length,
        item: (index) => mockFoods[index]
      }
    });

    // Call the searchFoods function with USDA source filter
    const results = await databaseService.searchFoods('apple', 20, 'USDA');

    // Verify the SQL query was executed with the correct parameters
    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM Food'),
      expect.arrayContaining(['%apple%', 'USDA%', 20])
    );

    // Verify the query includes source filtering
    const sqlQuery = dbManager.executeQuery.mock.calls[0][0];
    expect(sqlQuery).toContain('AND source LIKE ?');

    // Verify the results only include foods from USDA database
    expect(results).toHaveLength(2);
    expect(results.every(food => food.source === 'USDA')).toBeTruthy();
  });

  test('should search foods with pagination', async () => {
    // Mock data for foods
    const mockFoods = [
      { id: 'usda3', name: 'Banana', source: 'USDA' },
      { id: 'foodb2', name: 'Banana Smoothie', source: 'FoodB' }
    ];

    // Mock the executeQuery response
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockFoods.length,
        item: (index) => mockFoods[index]
      }
    });

    // Call the searchFoods function with pagination
    const results = await databaseService.searchFoods('banana', 10, null, 20);

    // Verify the SQL query was executed with the correct parameters
    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('LIMIT ? OFFSET ?'),
      expect.arrayContaining(['%banana%', 10, 20])
    );

    // Verify the results include foods from both databases
    expect(results).toHaveLength(2);
    expect(results.some(food => food.source === 'USDA')).toBeTruthy();
    expect(results.some(food => food.source === 'FoodB')).toBeTruthy();
  });

  test('should check if databases are imported', async () => {
    // Mock the isFoodDatabaseImported responses
    dbManager.isFoodDatabaseImported
      .mockResolvedValueOnce(true)  // USDA is imported
      .mockResolvedValueOnce(true); // FoodB is imported

    // Check if USDA database is imported
    const isUsdaImported = await databaseService.isFoodDatabaseImported('USDA');
    expect(dbManager.isFoodDatabaseImported).toHaveBeenCalledWith('USDA');
    expect(isUsdaImported).toBeTruthy();

    // Check if FoodB database is imported
    const isFoodBImported = await databaseService.isFoodDatabaseImported('FoodB');
    expect(dbManager.isFoodDatabaseImported).toHaveBeenCalledWith('FoodB');
    expect(isFoodBImported).toBeTruthy();
  });

  test('should handle empty search results', async () => {
    // Mock empty search results
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: 0,
        item: () => null
      }
    });

    // Call the searchFoods function with a query that returns no results
    const results = await databaseService.searchFoods('nonexistentfood');

    // Verify the SQL query was executed
    expect(dbManager.executeQuery).toHaveBeenCalled();

    // Verify the results are empty
    expect(results).toHaveLength(0);
  });
});
