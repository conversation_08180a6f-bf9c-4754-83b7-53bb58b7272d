/**
 * Tests for AI Recommendation Service
 */

import aiRecommendationService from '../../services/aiRecommendationService';
import * as databaseService from '../../services/databaseService';

// Mock database service
jest.mock('../../services/databaseService', () => ({
  getUserPreferences: jest.fn(),
  getFoodsByNutrientProfile: jest.fn(),
  getConsumptionHistory: jest.fn(),
  searchFoods: jest.fn()
}));

describe('AIRecommendationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    aiRecommendationService.initialized = false;
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      databaseService.getUserPreferences.mockResolvedValue({
        dietaryRestrictions: ['vegetarian'],
        healthGoals: ['weight_loss']
      });

      databaseService.getConsumptionHistory.mockResolvedValue([]);

      await aiRecommendationService.initialize();

      expect(aiRecommendationService.initialized).toBe(true);
      expect(databaseService.getUserPreferences).toHaveBeenCalled();
    });

    it('should handle initialization errors gracefully', async () => {
      databaseService.getUserPreferences.mockRejectedValue(new Error('Database error'));

      await aiRecommendationService.initialize();

      expect(aiRecommendationService.initialized).toBe(false);
    });
  });

  describe('getFoodRecommendations', () => {
    beforeEach(async () => {
      databaseService.getUserPreferences.mockResolvedValue({
        dietaryRestrictions: [],
        healthGoals: ['weight_loss']
      });
      databaseService.getConsumptionHistory.mockResolvedValue([]);
      await aiRecommendationService.initialize();
    });

    it('should return recommendations for breakfast', async () => {
      const mockFoods = [
        { id: '1', name: 'Oatmeal', category: 'grains', nutrients: { calories: 150, protein: 5 } },
        { id: '2', name: 'Greek Yogurt', category: 'dairy', nutrients: { calories: 100, protein: 15 } }
      ];

      databaseService.getFoodsByNutrientProfile.mockResolvedValue(mockFoods);

      const recommendations = await aiRecommendationService.getFoodRecommendations('breakfast');

      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeGreaterThan(0);
      expect(recommendations[0]).toHaveProperty('rank');
      expect(recommendations[0]).toHaveProperty('confidence');
    });

    it('should return recommendations for lunch', async () => {
      const mockFoods = [
        { id: '3', name: 'Chicken Salad', category: 'protein', nutrients: { calories: 300, protein: 25 } },
        { id: '4', name: 'Quinoa Bowl', category: 'grains', nutrients: { calories: 250, protein: 8 } }
      ];

      databaseService.getFoodsByNutrientProfile.mockResolvedValue(mockFoods);

      const recommendations = await aiRecommendationService.getFoodRecommendations('lunch');

      expect(recommendations).toBeDefined();
      expect(recommendations.length).toBeGreaterThan(0);
    });

    it('should handle empty food database', async () => {
      databaseService.getFoodsByNutrientProfile.mockResolvedValue([]);

      const recommendations = await aiRecommendationService.getFoodRecommendations('dinner');

      expect(recommendations).toEqual([]);
    });

    it('should handle database errors', async () => {
      databaseService.getFoodsByNutrientProfile.mockRejectedValue(new Error('Database error'));

      const recommendations = await aiRecommendationService.getFoodRecommendations('snack');

      expect(recommendations).toEqual([]);
    });
  });

  describe('getNutritionalInsights', () => {
    beforeEach(async () => {
      databaseService.getUserPreferences.mockResolvedValue({});
      databaseService.getConsumptionHistory.mockResolvedValue([]);
      await aiRecommendationService.initialize();
    });

    it('should generate insights for nutrient deficiencies', async () => {
      // Mock analytics service to return low nutrient goals
      jest.doMock('../../services/analyticsService', () => ({
        getConsumptionStats: jest.fn().mockResolvedValue({
          macroDistribution: [
            { name: 'Protein', percentage: 15 }, // Low protein
            { name: 'Carbs', percentage: 60 },   // High carbs
            { name: 'Fat', percentage: 25 }
          ]
        }),
        getNutrientGoals: jest.fn().mockResolvedValue({
          protein: { current: 50, target: 150, percentage: 33 }, // Deficient
          vitamin_c: { current: 20, target: 90, percentage: 22 } // Deficient
        })
      }));

      const insights = await aiRecommendationService.getNutritionalInsights();

      expect(insights).toBeDefined();
      expect(Array.isArray(insights)).toBe(true);
      
      // Should have insights about deficiencies
      const deficiencyInsight = insights.find(insight => insight.type === 'deficiency');
      expect(deficiencyInsight).toBeDefined();
      expect(deficiencyInsight.priority).toBe('high');
    });

    it('should generate macro balance insights', async () => {
      jest.doMock('../../services/analyticsService', () => ({
        getConsumptionStats: jest.fn().mockResolvedValue({
          macroDistribution: [
            { name: 'Protein', percentage: 15 }, // Too low
            { name: 'Carbs', percentage: 65 },   // Too high
            { name: 'Fat', percentage: 20 }      // Too low
          ]
        }),
        getNutrientGoals: jest.fn().mockResolvedValue({})
      }));

      const insights = await aiRecommendationService.getNutritionalInsights();

      const macroInsight = insights.find(insight => insight.type === 'macro_balance');
      expect(macroInsight).toBeDefined();
      expect(macroInsight.priority).toBe('medium');
    });

    it('should handle analytics errors gracefully', async () => {
      jest.doMock('../../services/analyticsService', () => ({
        getConsumptionStats: jest.fn().mockRejectedValue(new Error('Analytics error')),
        getNutrientGoals: jest.fn().mockRejectedValue(new Error('Goals error'))
      }));

      const insights = await aiRecommendationService.getNutritionalInsights();

      expect(insights).toEqual([]);
    });
  });

  describe('getMealCompletionSuggestions', () => {
    it('should suggest foods to complete a meal', async () => {
      const currentMealItems = [
        { food_id: '1', quantity: 100, nutrients: { calories: 200, protein: 10, carbs: 30, fat: 5 } }
      ];

      const suggestions = await aiRecommendationService.getMealCompletionSuggestions(currentMealItems);

      expect(suggestions).toBeDefined();
      expect(Array.isArray(suggestions)).toBe(true);
      
      if (suggestions.length > 0) {
        expect(suggestions[0]).toHaveProperty('reason');
        expect(suggestions[0]).toHaveProperty('confidence');
      }
    });

    it('should handle empty meal items', async () => {
      const suggestions = await aiRecommendationService.getMealCompletionSuggestions([]);

      expect(suggestions).toBeDefined();
      expect(Array.isArray(suggestions)).toBe(true);
    });
  });

  describe('getSubstitutionSuggestions', () => {
    it('should suggest healthier alternatives', async () => {
      const food = {
        id: '1',
        name: 'White Bread',
        nutrients: { calories: 250, protein: 8, carbs: 50, fat: 2, fiber: 2 }
      };

      const suggestions = await aiRecommendationService.getSubstitutionSuggestions(food, 'health');

      expect(suggestions).toBeDefined();
      expect(Array.isArray(suggestions)).toBe(true);
      
      if (suggestions.length > 0) {
        expect(suggestions[0]).toHaveProperty('similarity');
        expect(suggestions[0]).toHaveProperty('improvement');
        expect(suggestions[0]).toHaveProperty('reason');
      }
    });

    it('should suggest alternatives for dietary restrictions', async () => {
      const food = {
        id: '2',
        name: 'Milk',
        nutrients: { calories: 150, protein: 8, carbs: 12, fat: 8 },
        allergens: ['milk']
      };

      const suggestions = await aiRecommendationService.getSubstitutionSuggestions(food, 'allergy');

      expect(suggestions).toBeDefined();
      expect(Array.isArray(suggestions)).toBe(true);
    });
  });

  describe('scoring algorithms', () => {
    it('should calculate nutrition score correctly', () => {
      const food = {
        nutrients: { calories: 200, protein: 20, carbs: 30, fat: 8 }
      };
      const needs = { calories: 200, protein: 15, carbs: 25, fat: 10 };

      const score = aiRecommendationService.calculateNutritionScore(food, needs);

      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(100); // Max possible score
    });

    it('should calculate history score based on frequency', () => {
      const food = { id: '1', name: 'Apple' };
      const history = [
        { food_id: '1', date: '2023-01-01' },
        { food_id: '1', date: '2023-01-02' },
        { food_id: '2', date: '2023-01-03' }
      ];

      const score = aiRecommendationService.calculateHistoryScore(food, history);

      expect(score).toBeGreaterThan(0);
    });

    it('should calculate context score based on time and weather', () => {
      const food = { category: 'soup' };
      const context = { timeOfDay: 'evening', weather: 'cold' };

      const score = aiRecommendationService.calculateContextScore(food, context);

      expect(score).toBeGreaterThan(0);
    });
  });

  describe('helper methods', () => {
    it('should calculate nutritional needs for different meal types', () => {
      const currentNutrition = { calories: 500, protein: 20, carbs: 60, fat: 15 };
      
      const breakfastNeeds = aiRecommendationService.calculateNutritionalNeeds(currentNutrition, 'breakfast');
      const lunchNeeds = aiRecommendationService.calculateNutritionalNeeds(currentNutrition, 'lunch');
      const dinnerNeeds = aiRecommendationService.calculateNutritionalNeeds(currentNutrition, 'dinner');

      expect(breakfastNeeds.calories).toBeGreaterThan(0);
      expect(lunchNeeds.calories).toBeGreaterThan(breakfastNeeds.calories);
      expect(dinnerNeeds.calories).toBeGreaterThan(0);
    });

    it('should deduplicate recommendations', () => {
      const recommendations = [
        { id: '1', name: 'Apple', score: 80 },
        { id: '2', name: 'Banana', score: 75 },
        { id: '1', name: 'Apple', score: 85 }, // Duplicate
        { id: '3', name: 'Orange', score: 70 }
      ];

      const deduplicated = aiRecommendationService.deduplicateRecommendations(recommendations);

      expect(deduplicated).toHaveLength(3);
      expect(deduplicated.map(r => r.id)).toEqual(['1', '2', '3']);
    });

    it('should rank recommendations by score', () => {
      const recommendations = [
        { id: '1', score: 70 },
        { id: '2', score: 85 },
        { id: '3', score: 60 },
        { id: '4', score: 90 }
      ];

      const ranked = aiRecommendationService.rankRecommendations(recommendations);

      expect(ranked[0].score).toBe(90);
      expect(ranked[0].rank).toBe(1);
      expect(ranked[1].score).toBe(85);
      expect(ranked[1].rank).toBe(2);
      expect(ranked.length).toBeLessThanOrEqual(10); // Should limit to top 10
    });
  });
});
