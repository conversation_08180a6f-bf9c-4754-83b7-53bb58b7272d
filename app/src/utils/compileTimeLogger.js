/**
 * Compile-time Error and Warning Logger
 * 
 * This module captures compilation errors and warnings during development
 * and stores them for analysis and debugging purposes.
 */

import { logCompileIssue, log, LOG_LEVELS, LOG_CATEGORIES } from '../services/logService';

// Store for compile-time issues
let compileIssues = [];
let isCapturing = false;

/**
 * Initialize compile-time logging
 */
export const initCompileTimeLogging = () => {
  if (isCapturing) return;
  
  try {
    // Capture console errors and warnings during development
    if (__DEV__) {
      setupConsoleCapture();
      setupErrorCapture();
      setupWarningCapture();
      setupReactErrorCapture();
    }
    
    isCapturing = true;
    console.log('Compile-time logging initialized');
  } catch (error) {
    console.error('Failed to initialize compile-time logging:', error);
  }
};

/**
 * Setup console capture for compilation messages
 */
const setupConsoleCapture = () => {
  const originalError = console.error;
  const originalWarn = console.warn;
  
  console.error = (...args) => {
    captureCompileIssue('error', args);
    originalError.apply(console, args);
  };
  
  console.warn = (...args) => {
    captureCompileIssue('warning', args);
    originalWarn.apply(console, args);
  };
};

/**
 * Setup error capture for unhandled errors
 */
const setupErrorCapture = () => {
  // Capture unhandled promise rejections
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      captureCompileIssue('error', [
        'Unhandled Promise Rejection:',
        event.reason
      ], {
        type: 'unhandled_promise_rejection',
        promise: event.promise,
        reason: event.reason,
      });
    });
    
    // Capture global errors
    window.addEventListener('error', (event) => {
      captureCompileIssue('error', [
        'Global Error:',
        event.message
      ], {
        type: 'global_error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
      });
    });
  }
  
  // React Native error capture
  if (typeof global !== 'undefined' && global.ErrorUtils) {
    const originalHandler = global.ErrorUtils.getGlobalHandler();
    
    global.ErrorUtils.setGlobalHandler((error, isFatal) => {
      captureCompileIssue('error', [
        'React Native Error:',
        error.message
      ], {
        type: 'react_native_error',
        isFatal,
        stack: error.stack,
        name: error.name,
      });
      
      originalHandler(error, isFatal);
    });
  }
};

/**
 * Setup warning capture for React warnings
 */
const setupWarningCapture = () => {
  // Capture React warnings in development
  if (typeof console !== 'undefined' && console.warn) {
    const originalWarn = console.warn;
    
    console.warn = (...args) => {
      const message = args.join(' ');
      
      // Check if it's a React warning
      if (message.includes('Warning:') || message.includes('React')) {
        captureCompileIssue('warning', args, {
          type: 'react_warning',
          component: extractComponentFromWarning(message),
        });
      }
      
      originalWarn.apply(console, args);
    };
  }
};

/**
 * Setup React-specific error capture
 */
const setupReactErrorCapture = () => {
  // This will be enhanced by the ErrorBoundary component
  // but we can capture some React-specific issues here
  
  if (typeof window !== 'undefined' && window.React) {
    // Capture React DevTools errors if available
    try {
      const originalCreateElement = window.React.createElement;
      
      window.React.createElement = (type, props, ...children) => {
        try {
          return originalCreateElement(type, props, ...children);
        } catch (error) {
          captureCompileIssue('error', [
            'React createElement error:',
            error.message
          ], {
            type: 'react_create_element_error',
            componentType: typeof type === 'string' ? type : type?.name || 'Unknown',
            props,
            error: error.message,
          });
          throw error;
        }
      };
    } catch (error) {
      console.warn('Could not setup React createElement capture:', error);
    }
  }
};

/**
 * Capture compile-time issue
 * @param {string} type - 'error' or 'warning'
 * @param {Array} args - Console arguments
 * @param {Object} metadata - Additional metadata
 */
const captureCompileIssue = async (type, args, metadata = {}) => {
  try {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    
    // Filter out noise
    if (shouldIgnoreMessage(message)) {
      return;
    }
    
    const issue = {
      id: `compile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      message,
      timestamp: new Date().toISOString(),
      args,
      metadata: {
        ...metadata,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A',
        url: typeof window !== 'undefined' ? window.location?.href : 'N/A',
        stack: new Error().stack,
      },
    };
    
    // Store locally
    compileIssues.unshift(issue);
    
    // Keep only last 100 issues
    if (compileIssues.length > 100) {
      compileIssues = compileIssues.slice(0, 100);
    }
    
    // Log to main logging system
    await logCompileIssue(type, message, {
      ...metadata,
      compileIssueId: issue.id,
    });
    
  } catch (error) {
    console.error('Error capturing compile issue:', error);
  }
};

/**
 * Check if message should be ignored
 * @param {string} message - Message to check
 * @returns {boolean} - Whether to ignore
 */
const shouldIgnoreMessage = (message) => {
  const ignorePatterns = [
    /^Download the React DevTools/,
    /^You are running a development build/,
    /^This is a development build/,
    /^Warning: React DevTools/,
    /^The above error occurred in the/,
    /^Consider adding an error boundary/,
    /^React will try to recreate this component/,
  ];
  
  return ignorePatterns.some(pattern => pattern.test(message));
};

/**
 * Extract component name from React warning
 * @param {string} message - Warning message
 * @returns {string} - Component name or 'Unknown'
 */
const extractComponentFromWarning = (message) => {
  const componentMatch = message.match(/in (\w+)/);
  return componentMatch ? componentMatch[1] : 'Unknown';
};

/**
 * Get all captured compile issues
 * @param {string} type - Filter by type ('error', 'warning', or null for all)
 * @returns {Array} - Array of compile issues
 */
export const getCompileIssues = (type = null) => {
  if (type) {
    return compileIssues.filter(issue => issue.type === type);
  }
  return [...compileIssues];
};

/**
 * Clear all captured compile issues
 */
export const clearCompileIssues = () => {
  compileIssues = [];
};

/**
 * Get compile issue statistics
 * @returns {Object} - Statistics object
 */
export const getCompileIssueStats = () => {
  const errors = compileIssues.filter(issue => issue.type === 'error');
  const warnings = compileIssues.filter(issue => issue.type === 'warning');
  
  return {
    total: compileIssues.length,
    errors: errors.length,
    warnings: warnings.length,
    lastIssue: compileIssues[0] || null,
    errorRate: compileIssues.length > 0 ? (errors.length / compileIssues.length) * 100 : 0,
  };
};

/**
 * Export compile issues for debugging
 * @returns {string} - JSON string of all issues
 */
export const exportCompileIssues = () => {
  const exportData = {
    timestamp: new Date().toISOString(),
    stats: getCompileIssueStats(),
    issues: compileIssues,
    environment: {
      isDev: __DEV__,
      platform: typeof window !== 'undefined' ? 'web' : 'native',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A',
    },
  };
  
  return JSON.stringify(exportData, null, 2);
};

// Auto-initialize in development
if (__DEV__) {
  initCompileTimeLogging();
}
