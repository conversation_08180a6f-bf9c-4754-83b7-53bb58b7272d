/**
 * Phenol-Explorer Database Importer
 * 
 * This utility imports polyphenol data from the Phenol-Explorer database
 * http://phenol-explorer.eu/downloads
 * 
 * The importer supports the Phenol-Explorer CSV export format
 * Focuses on polyphenol content in foods for antioxidant tracking
 */

import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import { unzip } from 'react-native-zip-archive';
import { saveFood, saveNutrient, saveIngredient, saveAllergen } from '../../services/databaseService';

// Polyphenol classes and their health benefits
const POLYPHENOL_CLASSES = {
  'Flavonoids': {
    subclasses: ['Anthocyanins', 'Flavanols', 'Flavanones', 'Flavones', 'Flavonols', 'Isoflavones'],
    benefits: 'Antioxidant, anti-inflammatory, cardiovascular health'
  },
  'Phenolic acids': {
    subclasses: ['Hydroxybenzoic acids', 'Hydroxycinnamic acids'],
    benefits: 'Antioxidant, neuroprotective'
  },
  'Stilbenes': {
    subclasses: ['Resveratrol derivatives'],
    benefits: 'Cardioprotective, anti-aging'
  },
  'Lignans': {
    subclasses: ['Lignans'],
    benefits: 'Hormone regulation, cancer prevention'
  },
  'Other polyphenols': {
    subclasses: ['Curcuminoids', 'Other'],
    benefits: 'Various health benefits'
  }
};

// Common polyphenol compounds and their properties
const POLYPHENOL_MAPPING = {
  // Flavonoids - Anthocyanins
  'Cyanidin': { class: 'Flavonoids', subclass: 'Anthocyanins', unit: 'mg', color: 'red-purple' },
  'Delphinidin': { class: 'Flavonoids', subclass: 'Anthocyanins', unit: 'mg', color: 'blue-purple' },
  'Malvidin': { class: 'Flavonoids', subclass: 'Anthocyanins', unit: 'mg', color: 'purple' },
  'Pelargonidin': { class: 'Flavonoids', subclass: 'Anthocyanins', unit: 'mg', color: 'orange-red' },
  'Peonidin': { class: 'Flavonoids', subclass: 'Anthocyanins', unit: 'mg', color: 'red' },
  'Petunidin': { class: 'Flavonoids', subclass: 'Anthocyanins', unit: 'mg', color: 'purple' },

  // Flavonoids - Flavanols
  'Catechin': { class: 'Flavonoids', subclass: 'Flavanols', unit: 'mg', benefits: 'antioxidant' },
  'Epicatechin': { class: 'Flavonoids', subclass: 'Flavanols', unit: 'mg', benefits: 'cardiovascular' },
  'Epigallocatechin': { class: 'Flavonoids', subclass: 'Flavanols', unit: 'mg', benefits: 'antioxidant' },
  'Epigallocatechin gallate': { class: 'Flavonoids', subclass: 'Flavanols', unit: 'mg', benefits: 'anti-cancer' },
  'Procyanidin B1': { class: 'Flavonoids', subclass: 'Flavanols', unit: 'mg', benefits: 'antioxidant' },
  'Procyanidin B2': { class: 'Flavonoids', subclass: 'Flavanols', unit: 'mg', benefits: 'antioxidant' },

  // Flavonoids - Flavonols
  'Quercetin': { class: 'Flavonoids', subclass: 'Flavonols', unit: 'mg', benefits: 'anti-inflammatory' },
  'Kaempferol': { class: 'Flavonoids', subclass: 'Flavonols', unit: 'mg', benefits: 'antioxidant' },
  'Myricetin': { class: 'Flavonoids', subclass: 'Flavonols', unit: 'mg', benefits: 'neuroprotective' },
  'Isorhamnetin': { class: 'Flavonoids', subclass: 'Flavonols', unit: 'mg', benefits: 'antioxidant' },

  // Flavonoids - Flavanones
  'Naringenin': { class: 'Flavonoids', subclass: 'Flavanones', unit: 'mg', benefits: 'anti-inflammatory' },
  'Hesperetin': { class: 'Flavonoids', subclass: 'Flavanones', unit: 'mg', benefits: 'cardiovascular' },
  'Eriodictyol': { class: 'Flavonoids', subclass: 'Flavanones', unit: 'mg', benefits: 'antioxidant' },

  // Flavonoids - Isoflavones
  'Genistein': { class: 'Flavonoids', subclass: 'Isoflavones', unit: 'mg', benefits: 'hormone regulation' },
  'Daidzein': { class: 'Flavonoids', subclass: 'Isoflavones', unit: 'mg', benefits: 'hormone regulation' },
  'Glycitein': { class: 'Flavonoids', subclass: 'Isoflavones', unit: 'mg', benefits: 'hormone regulation' },

  // Phenolic acids
  'Gallic acid': { class: 'Phenolic acids', subclass: 'Hydroxybenzoic acids', unit: 'mg', benefits: 'antioxidant' },
  'Protocatechuic acid': { class: 'Phenolic acids', subclass: 'Hydroxybenzoic acids', unit: 'mg', benefits: 'antioxidant' },
  'Vanillic acid': { class: 'Phenolic acids', subclass: 'Hydroxybenzoic acids', unit: 'mg', benefits: 'antioxidant' },
  'Caffeic acid': { class: 'Phenolic acids', subclass: 'Hydroxycinnamic acids', unit: 'mg', benefits: 'neuroprotective' },
  'Chlorogenic acid': { class: 'Phenolic acids', subclass: 'Hydroxycinnamic acids', unit: 'mg', benefits: 'metabolic health' },
  'Ferulic acid': { class: 'Phenolic acids', subclass: 'Hydroxycinnamic acids', unit: 'mg', benefits: 'antioxidant' },

  // Stilbenes
  'Resveratrol': { class: 'Stilbenes', subclass: 'Resveratrol derivatives', unit: 'mg', benefits: 'cardioprotective' },
  'Piceatannol': { class: 'Stilbenes', subclass: 'Resveratrol derivatives', unit: 'mg', benefits: 'anti-inflammatory' },

  // Lignans
  'Secoisolariciresinol': { class: 'Lignans', subclass: 'Lignans', unit: 'mg', benefits: 'hormone regulation' },
  'Matairesinol': { class: 'Lignans', subclass: 'Lignans', unit: 'mg', benefits: 'cancer prevention' },

  // Other polyphenols
  'Curcumin': { class: 'Other polyphenols', subclass: 'Curcuminoids', unit: 'mg', benefits: 'anti-inflammatory' },
  'Ellagic acid': { class: 'Other polyphenols', subclass: 'Other', unit: 'mg', benefits: 'anti-cancer' },
};

/**
 * Phenol-Explorer Data Importer class
 */
class PhenolExplorerImporter {
  constructor() {
    this.tempDir = FileSystem.cacheDirectory + 'phenol_import/';
    this.importStats = {
      foods: 0,
      polyphenols: 0,
      compounds: 0,
      errors: 0,
    };
  }

  /**
   * Initialize the importer
   */
  async initialize() {
    try {
      // Create temp directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(this.tempDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.tempDir, { intermediates: true });
      }

      // Clear any existing files
      await this.cleanupTempFiles();

      return true;
    } catch (error) {
      console.error('Error initializing Phenol-Explorer importer:', error);
      return false;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles() {
    try {
      const files = await FileSystem.readDirectoryAsync(this.tempDir);
      for (const file of files) {
        await FileSystem.deleteAsync(this.tempDir + file, { idempotent: true });
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }

  /**
   * Select Phenol-Explorer dataset file
   * @returns {Promise<string|null>} Path to selected file
   */
  async selectDataset() {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['text/csv', 'application/zip', 'application/x-zip-compressed'],
        copyToCacheDirectory: true,
      });

      if (result.type === 'success') {
        return result.uri;
      }

      return null;
    } catch (error) {
      console.error('Error selecting Phenol-Explorer dataset:', error);
      return null;
    }
  }

  /**
   * Extract dataset if it's a ZIP file
   * @param {string} filePath Path to the file
   * @returns {Promise<string>} Path to extracted directory or original file
   */
  async extractDataset(filePath) {
    try {
      // Check if it's a ZIP file
      if (filePath.toLowerCase().endsWith('.zip')) {
        const extractPath = this.tempDir + 'extracted/';

        // Create extraction directory
        await FileSystem.makeDirectoryAsync(extractPath, { intermediates: true });

        // Extract ZIP file
        await unzip(filePath, extractPath);

        return extractPath;
      } else {
        // It's already a CSV file
        return filePath;
      }
    } catch (error) {
      console.error('Error extracting Phenol-Explorer dataset:', error);
      return null;
    }
  }

  /**
   * Parse CSV file
   * @param {string} filePath Path to CSV file
   * @returns {Promise<Array>} Parsed CSV data
   */
  async parseCSV(filePath) {
    try {
      const content = await FileSystem.readAsStringAsync(filePath);
      const lines = content.split('\n').filter(line => line.trim().length > 0);

      if (lines.length === 0) {
        return [];
      }

      // Parse header
      const header = lines[0].split(',').map(col => col.replace(/"/g, '').trim());

      // Parse data rows
      const data = [];
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(val => val.replace(/"/g, '').trim());

        if (values.length === header.length) {
          const row = {};
          header.forEach((col, index) => {
            row[col] = values[index];
          });
          data.push(row);
        }
      }

      return data;
    } catch (error) {
      console.error(`Error parsing CSV file ${filePath}:`, error);
      return [];
    }
  }

  /**
   * Find CSV files in directory
   * @param {string} dirPath Directory path
   * @returns {Promise<Array>} List of CSV files
   */
  async findCSVFiles(dirPath) {
    try {
      const files = await FileSystem.readDirectoryAsync(dirPath);
      return files.filter(file => file.toLowerCase().endsWith('.csv'));
    } catch (error) {
      console.error('Error finding CSV files:', error);
      return [];
    }
  }

  /**
   * Import polyphenol data from Phenol-Explorer dataset
   * @param {string} dataPath Path to the data file or directory
   * @returns {Promise<boolean>} Success status
   */
  async importPolyphenols(dataPath) {
    try {
      let csvFiles = [];

      // Check if it's a directory or file
      const pathInfo = await FileSystem.getInfoAsync(dataPath);
      if (pathInfo.isDirectory) {
        csvFiles = await this.findCSVFiles(dataPath);
        csvFiles = csvFiles.map(file => dataPath + file);
      } else {
        csvFiles = [dataPath];
      }

      if (csvFiles.length === 0) {
        throw new Error('No CSV files found in Phenol-Explorer dataset');
      }

      // Process each CSV file
      for (const csvFile of csvFiles) {
        await this.processPolyphenolFile(csvFile);
      }

      return true;
    } catch (error) {
      console.error('Error importing Phenol-Explorer data:', error);
      return false;
    }
  }

  /**
   * Process a single polyphenol CSV file
   * @param {string} filePath Path to CSV file
   * @returns {Promise<void>}
   */
  async processPolyphenolFile(filePath) {
    try {
      const data = await this.parseCSV(filePath);

      if (data.length === 0) {
        console.warn(`No data found in file: ${filePath}`);
        return;
      }

      // Group data by food
      const foodData = {};

      for (const row of data) {
        try {
          // Extract food information (column names may vary)
          const foodName = row['Food name'] || row['Food'] || row['food_name'] || '';
          const polyphenolName = row['Polyphenol'] || row['Compound'] || row['polyphenol_name'] || '';
          const content = parseFloat(row['Content'] || row['Amount'] || row['content'] || '0');
          const unit = row['Unit'] || row['unit'] || 'mg';

          if (!foodName || !polyphenolName || isNaN(content)) {
            continue;
          }

          // Initialize food entry if not exists
          if (!foodData[foodName]) {
            foodData[foodName] = {
              name: foodName,
              polyphenols: []
            };
          }

          // Add polyphenol data
          foodData[foodName].polyphenols.push({
            name: polyphenolName,
            content: content,
            unit: unit
          });

        } catch (error) {
          console.error('Error processing row:', error);
          this.importStats.errors++;
        }
      }

      // Save foods and their polyphenol content
      for (const [foodName, food] of Object.entries(foodData)) {
        await this.saveFoodWithPolyphenols(food);
      }

    } catch (error) {
      console.error(`Error processing polyphenol file ${filePath}:`, error);
    }
  }

  /**
   * Save food with polyphenol content to database
   * @param {Object} food Food data with polyphenols
   * @returns {Promise<void>}
   */
  async saveFoodWithPolyphenols(food) {
    try {
      // Create food object
      const foodData = {
        name: food.name,
        description: `${food.name} (Polyphenol data from Phenol-Explorer)`,
        brand_name: '',
        serving_size: 100, // Phenol-Explorer data is typically per 100g
        serving_unit: 'g',
        barcode: '',
        is_custom: false,
        source: 'Phenol-Explorer',
        source_id: food.name.toLowerCase().replace(/\s+/g, '_'),
        category: 'Polyphenol-rich foods',
      };

      // Save food to database
      const savedFood = await saveFood(foodData);

      // Calculate total polyphenol content
      let totalPolyphenols = 0;
      const polyphenolsByClass = {};

      // Process each polyphenol
      for (const polyphenol of food.polyphenols) {
        const mapping = POLYPHENOL_MAPPING[polyphenol.name];

        if (mapping) {
          // Add to class totals
          const className = mapping.class;
          if (!polyphenolsByClass[className]) {
            polyphenolsByClass[className] = 0;
          }
          polyphenolsByClass[className] += polyphenol.content;
          totalPolyphenols += polyphenol.content;

          // Save individual polyphenol as nutrient
          const nutrientData = {
            food_id: savedFood.id,
            name: polyphenol.name,
            amount: polyphenol.content,
            unit: polyphenol.unit,
            type: 'polyphenol',
            source: 'Phenol-Explorer',
            category: mapping.class,
            subclass: mapping.subclass,
            benefits: mapping.benefits || '',
          };

          await saveNutrient(nutrientData);
          this.importStats.compounds++;
        }
      }

      // Save total polyphenol content
      if (totalPolyphenols > 0) {
        const totalNutrientData = {
          food_id: savedFood.id,
          name: 'Total Polyphenols',
          amount: totalPolyphenols,
          unit: 'mg',
          type: 'polyphenol',
          source: 'Phenol-Explorer',
          category: 'Total',
        };

        await saveNutrient(totalNutrientData);
        this.importStats.polyphenols++;
      }

      // Save polyphenol class totals
      for (const [className, total] of Object.entries(polyphenolsByClass)) {
        const classNutrientData = {
          food_id: savedFood.id,
          name: `Total ${className}`,
          amount: total,
          unit: 'mg',
          type: 'polyphenol',
          source: 'Phenol-Explorer',
          category: className,
        };

        await saveNutrient(classNutrientData);
      }

      this.importStats.foods++;

    } catch (error) {
      console.error(`Error saving food with polyphenols: ${food.name}`, error);
      this.importStats.errors++;
    }
  }

  /**
   * Start the import process
   * @returns {Promise<Object>} Import statistics
   */
  async startImport() {
    try {
      // Reset import stats
      this.importStats = {
        foods: 0,
        polyphenols: 0,
        compounds: 0,
        errors: 0,
      };

      // Initialize
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize Phenol-Explorer importer');
      }

      // Select dataset
      const dataPath = await this.selectDataset();
      if (!dataPath) {
        throw new Error('No Phenol-Explorer dataset selected');
      }

      // Extract dataset if needed
      const extractedPath = await this.extractDataset(dataPath);
      if (!extractedPath) {
        throw new Error('Failed to extract Phenol-Explorer dataset');
      }

      // Import polyphenol data
      const success = await this.importPolyphenols(extractedPath);

      // Clean up
      await this.cleanupTempFiles();

      return {
        success,
        datasetType: 'Phenol-Explorer',
        stats: this.importStats,
      };
    } catch (error) {
      console.error('Error during Phenol-Explorer import:', error);
      await this.cleanupTempFiles();
      return {
        success: false,
        error: error.message,
        stats: this.importStats,
      };
    }
  }
}

export default PhenolExplorerImporter;
