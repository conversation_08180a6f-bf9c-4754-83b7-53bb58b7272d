/**
 * Canadian Nutrient File (CNF) Importer
 * 
 * This utility imports food data from the Canadian Nutrient File database
 * https://www.canada.ca/en/health-canada/services/food-nutrition/healthy-eating/nutrient-data/canadian-nutrient-file-2015-download-files.html
 * 
 * The importer supports the CNF 2015 dataset format with CSV files
 */

import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import { unzip } from 'react-native-zip-archive';
import { saveFood, saveNutrient, saveIngredient, saveAllergen } from '../../services/databaseService';

// Mapping of CNF nutrient IDs to our internal nutrient types
const CNF_NUTRIENT_MAPPING = {
  // Macronutrients
  203: { name: 'Protein', unit: 'g', type: 'macro' },
  204: { name: 'Total Fat', unit: 'g', type: 'macro' },
  205: { name: 'Carbohydrate', unit: 'g', type: 'macro' },
  208: { name: 'Energy', unit: 'kcal', type: 'macro' },
  269: { name: 'Total Sugars', unit: 'g', type: 'macro' },
  291: { name: 'Dietary Fiber', unit: 'g', type: 'macro' },

  // Vitamins
  401: { name: 'Vitamin C', unit: 'mg', type: 'vitamin' },
  404: { name: 'Thiamin', unit: 'mg', type: 'vitamin' },
  405: { name: 'Riboflavin', unit: 'mg', type: 'vitamin' },
  406: { name: 'Niacin', unit: 'mg', type: 'vitamin' },
  410: { name: 'Pantothenic Acid', unit: 'mg', type: 'vitamin' },
  415: { name: 'Vitamin B6', unit: 'mg', type: 'vitamin' },
  417: { name: 'Folate', unit: 'mcg', type: 'vitamin' },
  418: { name: 'Vitamin B12', unit: 'mcg', type: 'vitamin' },
  320: { name: 'Vitamin A', unit: 'mcg', type: 'vitamin' },
  323: { name: 'Vitamin E', unit: 'mg', type: 'vitamin' },
  430: { name: 'Vitamin K', unit: 'mcg', type: 'vitamin' },
  324: { name: 'Vitamin D', unit: 'mcg', type: 'vitamin' },

  // Minerals
  301: { name: 'Calcium', unit: 'mg', type: 'mineral' },
  303: { name: 'Iron', unit: 'mg', type: 'mineral' },
  304: { name: 'Magnesium', unit: 'mg', type: 'mineral' },
  305: { name: 'Phosphorus', unit: 'mg', type: 'mineral' },
  306: { name: 'Potassium', unit: 'mg', type: 'mineral' },
  307: { name: 'Sodium', unit: 'mg', type: 'mineral' },
  309: { name: 'Zinc', unit: 'mg', type: 'mineral' },
  312: { name: 'Copper', unit: 'mg', type: 'mineral' },
  315: { name: 'Manganese', unit: 'mg', type: 'mineral' },
  317: { name: 'Selenium', unit: 'mcg', type: 'mineral' },

  // Fatty Acids
  606: { name: 'Saturated Fat', unit: 'g', type: 'other' },
  645: { name: 'Monounsaturated Fat', unit: 'g', type: 'other' },
  646: { name: 'Polyunsaturated Fat', unit: 'g', type: 'other' },
  601: { name: 'Cholesterol', unit: 'mg', type: 'other' },
};

// Common allergens to look for in food names and descriptions
const COMMON_ALLERGENS = [
  { name: 'Milk', keywords: ['milk', 'dairy', 'lactose', 'whey', 'casein', 'butter', 'cream', 'cheese', 'yogurt'] },
  { name: 'Eggs', keywords: ['egg', 'albumin', 'lysozyme', 'globulin', 'ovomucin', 'ovalbumin', 'ovotransferrin'] },
  { name: 'Fish', keywords: ['fish', 'cod', 'salmon', 'tuna', 'tilapia', 'halibut', 'anchovy', 'bass'] },
  { name: 'Shellfish', keywords: ['shellfish', 'crab', 'lobster', 'shrimp', 'prawn', 'crayfish', 'clam', 'mussel', 'oyster', 'scallop'] },
  { name: 'Tree Nuts', keywords: ['almond', 'brazil nut', 'cashew', 'chestnut', 'hazelnut', 'macadamia', 'pecan', 'pine nut', 'pistachio', 'walnut'] },
  { name: 'Peanuts', keywords: ['peanut', 'groundnut', 'arachis'] },
  { name: 'Wheat', keywords: ['wheat', 'flour', 'gluten', 'bulgur', 'durum', 'semolina', 'spelt', 'triticale'] },
  { name: 'Soybeans', keywords: ['soy', 'soybean', 'tofu', 'tempeh', 'miso', 'edamame'] },
  { name: 'Sesame', keywords: ['sesame', 'tahini', 'sesamum'] },
  { name: 'Mustard', keywords: ['mustard', 'brassica'] },
  { name: 'Sulphites', keywords: ['sulphite', 'sulfite', 'sodium bisulfite', 'potassium bisulfite'] },
];

/**
 * Canadian Nutrient File Data Importer class
 */
class CNFImporter {
  constructor() {
    this.tempDir = FileSystem.cacheDirectory + 'cnf_import/';
    this.importStats = {
      foods: 0,
      nutrients: 0,
      ingredients: 0,
      allergens: 0,
      errors: 0,
    };
  }

  /**
   * Initialize the importer
   */
  async initialize() {
    try {
      // Create temp directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(this.tempDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.tempDir, { intermediates: true });
      }

      // Clear any existing files
      await this.cleanupTempFiles();

      return true;
    } catch (error) {
      console.error('Error initializing CNF importer:', error);
      return false;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles() {
    try {
      const files = await FileSystem.readDirectoryAsync(this.tempDir);
      for (const file of files) {
        await FileSystem.deleteAsync(this.tempDir + file, { idempotent: true });
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }

  /**
   * Select CNF dataset file
   * @returns {Promise<string|null>} Path to selected file
   */
  async selectDataset() {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/zip', 'application/x-zip-compressed'],
        copyToCacheDirectory: true,
      });

      if (result.type === 'success') {
        return result.uri;
      }

      return null;
    } catch (error) {
      console.error('Error selecting CNF dataset:', error);
      return null;
    }
  }

  /**
   * Extract CNF dataset
   * @param {string} zipPath Path to the ZIP file
   * @returns {Promise<string|null>} Path to extracted directory
   */
  async extractDataset(zipPath) {
    try {
      const extractPath = this.tempDir + 'extracted/';

      // Create extraction directory
      await FileSystem.makeDirectoryAsync(extractPath, { intermediates: true });

      // Extract ZIP file
      await unzip(zipPath, extractPath);

      return extractPath;
    } catch (error) {
      console.error('Error extracting CNF dataset:', error);
      return null;
    }
  }

  /**
   * Validate CNF dataset structure
   * @param {string} extractPath Path to extracted directory
   * @returns {Promise<boolean>} Whether dataset is valid
   */
  async validateDataset(extractPath) {
    try {
      const requiredFiles = [
        'FOOD NAME.csv',
        'NUTRIENT AMOUNT.csv',
        'NUTRIENT NAME.csv',
        'FOOD GROUP.csv'
      ];

      for (const file of requiredFiles) {
        const filePath = extractPath + file;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        if (!fileInfo.exists) {
          console.error(`Required CNF file not found: ${file}`);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error validating CNF dataset:', error);
      return false;
    }
  }

  /**
   * Parse CSV file
   * @param {string} filePath Path to CSV file
   * @returns {Promise<Array>} Parsed CSV data
   */
  async parseCSV(filePath) {
    try {
      const content = await FileSystem.readAsStringAsync(filePath);
      const lines = content.split('\n').filter(line => line.trim().length > 0);

      if (lines.length === 0) {
        return [];
      }

      // Parse header
      const header = lines[0].split(',').map(col => col.replace(/"/g, '').trim());

      // Parse data rows
      const data = [];
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(val => val.replace(/"/g, '').trim());

        if (values.length === header.length) {
          const row = {};
          header.forEach((col, index) => {
            row[col] = values[index];
          });
          data.push(row);
        }
      }

      return data;
    } catch (error) {
      console.error(`Error parsing CSV file ${filePath}:`, error);
      return [];
    }
  }

  /**
   * Import foods from the CNF dataset
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<boolean>} Success status
   */
  async importFoods(extractPath) {
    try {
      // Parse CNF data files
      const foodNames = await this.parseCSV(extractPath + 'FOOD NAME.csv');
      const nutrientAmounts = await this.parseCSV(extractPath + 'NUTRIENT AMOUNT.csv');
      const nutrientNames = await this.parseCSV(extractPath + 'NUTRIENT NAME.csv');
      const foodGroups = await this.parseCSV(extractPath + 'FOOD GROUP.csv');

      if (!foodNames || !nutrientAmounts || !nutrientNames || !foodGroups) {
        throw new Error('Failed to parse required CNF files');
      }

      // Create lookup maps
      const nutrientLookup = {};
      nutrientNames.forEach(nutrient => {
        nutrientLookup[nutrient.NutrientID] = {
          id: nutrient.NutrientID,
          name: nutrient.NutrientName,
          unit: nutrient.NutrientUnit,
          symbol: nutrient.NutrientSymbol
        };
      });

      const foodGroupLookup = {};
      foodGroups.forEach(group => {
        foodGroupLookup[group.FoodGroupID] = {
          id: group.FoodGroupID,
          name: group.FoodGroupName
        };
      });

      // Group nutrient amounts by food ID
      const foodNutrients = {};
      nutrientAmounts.forEach(amount => {
        const foodId = amount.FoodID;
        if (!foodNutrients[foodId]) {
          foodNutrients[foodId] = [];
        }
        foodNutrients[foodId].push(amount);
      });

      // Process each food
      for (const food of foodNames) {
        try {
          // Skip foods without a name
          if (!food.FoodDescription) continue;

          // Get food group information
          const foodGroup = foodGroupLookup[food.FoodGroupID];

          // Create food object
          const foodData = {
            name: food.FoodDescription,
            description: food.FoodDescription,
            brand_name: '',
            serving_size: 100, // CNF data is per 100g
            serving_unit: 'g',
            barcode: '',
            is_custom: false,
            source: 'CNF',
            source_id: food.FoodID.toString(),
            category: foodGroup ? foodGroup.name : 'Unknown',
          };

          // Save food to database
          const savedFood = await saveFood(foodData);

          // Process nutrients for this food
          const nutrients = foodNutrients[food.FoodID] || [];
          for (const nutrientAmount of nutrients) {
            const nutrientInfo = nutrientLookup[nutrientAmount.NutrientID];
            const cnfMapping = CNF_NUTRIENT_MAPPING[nutrientAmount.NutrientID];

            if (nutrientInfo && cnfMapping && nutrientAmount.NutrientValue) {
              const nutrientData = {
                food_id: savedFood.id,
                name: cnfMapping.name,
                amount: parseFloat(nutrientAmount.NutrientValue),
                unit: cnfMapping.unit,
                type: cnfMapping.type,
                source: 'CNF',
              };

              // Save nutrient to database
              await saveNutrient(nutrientData);
              this.importStats.nutrients++;
            }
          }

          // Check for allergens in food name and description
          const searchText = (food.FoodDescription + ' ' + (foodGroup ? foodGroup.name : '')).toLowerCase();

          for (const allergen of COMMON_ALLERGENS) {
            const hasAllergen = allergen.keywords.some(keyword =>
              searchText.includes(keyword.toLowerCase())
            );

            if (hasAllergen) {
              const allergenData = {
                food_id: savedFood.id,
                name: allergen.name,
                source: 'CNF',
              };

              // Save allergen to database
              await saveAllergen(allergenData);
              this.importStats.allergens++;
              break; // Only add each allergen once per food
            }
          }

          this.importStats.foods++;
        } catch (error) {
          console.error(`Error importing CNF food ${food.FoodID}:`, error);
          this.importStats.errors++;
        }
      }

      return true;
    } catch (error) {
      console.error('Error importing CNF foods:', error);
      return false;
    }
  }

  /**
   * Start the import process
   * @returns {Promise<Object>} Import statistics
   */
  async startImport() {
    try {
      // Reset import stats
      this.importStats = {
        foods: 0,
        nutrients: 0,
        ingredients: 0,
        allergens: 0,
        errors: 0,
      };

      // Initialize
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize CNF importer');
      }

      // Select dataset
      const zipPath = await this.selectDataset();
      if (!zipPath) {
        throw new Error('No CNF dataset selected');
      }

      // Extract dataset
      const extractPath = await this.extractDataset(zipPath);
      if (!extractPath) {
        throw new Error('Failed to extract CNF dataset');
      }

      // Validate dataset
      const isValid = await this.validateDataset(extractPath);
      if (!isValid) {
        throw new Error('Invalid CNF dataset structure');
      }

      // Import foods
      const success = await this.importFoods(extractPath);

      // Clean up
      await this.cleanupTempFiles();

      return {
        success,
        datasetType: 'CNF',
        stats: this.importStats,
      };
    } catch (error) {
      console.error('Error during CNF import:', error);
      await this.cleanupTempFiles();
      return {
        success: false,
        error: error.message,
        stats: this.importStats,
      };
    }
  }
}

export default CNFImporter;
