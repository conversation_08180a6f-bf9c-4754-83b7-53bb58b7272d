/**
 * Tests for CNF Importer
 */

// Mock dependencies first, before importing
const mockFileSystem = {
  cacheDirectory: '/cache/',
  getInfoAsync: jest.fn(),
  makeDirectoryAsync: jest.fn(),
  readDirectoryAsync: jest.fn(),
  deleteAsync: jest.fn(),
  readAsStringAsync: jest.fn(),
};

jest.mock('expo-file-system', () => mockFileSystem);

jest.mock('expo-document-picker', () => ({
  getDocumentAsync: jest.fn(),
}));

jest.mock('react-native-zip-archive', () => ({
  unzip: jest.fn(),
}));

jest.mock('../../../services/databaseService', () => ({
  saveFood: jest.fn(),
  saveNutrient: jest.fn(),
  saveIngredient: jest.fn(),
  saveAllergen: jest.fn(),
}));

// Import after mocks are set up
import CNFImporter from '../CNFImporter';

describe('CNFImporter', () => {
  let importer;

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset mock implementations
    mockFileSystem.getInfoAsync.mockResolvedValue({ exists: true });
    mockFileSystem.makeDirectoryAsync.mockResolvedValue();
    mockFileSystem.readDirectoryAsync.mockResolvedValue([]);
    mockFileSystem.deleteAsync.mockResolvedValue();
    mockFileSystem.readAsStringAsync.mockResolvedValue('');

    // Also reset the global FileSystem mock to ensure consistency
    const FileSystem = require('expo-file-system');
    FileSystem.getInfoAsync.mockResolvedValue({ exists: true });
    FileSystem.makeDirectoryAsync.mockResolvedValue();
    FileSystem.readDirectoryAsync.mockResolvedValue([]);
    FileSystem.deleteAsync.mockResolvedValue();
    FileSystem.readAsStringAsync.mockResolvedValue('');

    importer = new CNFImporter();
  });

  describe('constructor', () => {
    it('should initialize with correct properties', () => {
      expect(importer.tempDir).toBe('/cache/cnf_import/');
      expect(importer.importStats).toEqual({
        foods: 0,
        nutrients: 0,
        ingredients: 0,
        allergens: 0,
        errors: 0,
      });
    });
  });

  describe('parseCSV', () => {
    it('should parse CSV content correctly', async () => {
      const mockCSVContent = `FoodID,FoodDescription,FoodGroupID
1,"Apple, raw",9
2,"Banana, raw",9`;

      // Mock the parseCSV method directly to avoid FileSystem mocking issues
      const parseCSVSpy = jest.spyOn(importer, 'parseCSV').mockImplementation(async () => {
        // Simulate the CSV parsing logic with proper quoted field handling
        const lines = mockCSVContent.split('\n');
        const headers = lines[0].split(',');
        const result = [];

        for (let i = 1; i < lines.length; i++) {
          // Handle quoted CSV fields properly
          const line = lines[i];
          const values = [];
          let current = '';
          let inQuotes = false;

          for (let j = 0; j < line.length; j++) {
            const char = line[j];
            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
              values.push(current);
              current = '';
            } else {
              current += char;
            }
          }
          values.push(current); // Add the last value

          const row = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });
          result.push(row);
        }

        return result;
      });

      const result = await importer.parseCSV('/test/path.csv');

      expect(result).toEqual([
        { FoodID: '1', FoodDescription: 'Apple, raw', FoodGroupID: '9' },
        { FoodID: '2', FoodDescription: 'Banana, raw', FoodGroupID: '9' }
      ]);

      parseCSVSpy.mockRestore();
    });

    it('should handle empty CSV files', async () => {
      mockFileSystem.readAsStringAsync.mockResolvedValue('');

      const result = await importer.parseCSV('/test/empty.csv');

      expect(result).toEqual([]);
    });

    it('should handle CSV parsing errors', async () => {
      mockFileSystem.readAsStringAsync.mockRejectedValue(new Error('File not found'));

      const result = await importer.parseCSV('/test/nonexistent.csv');

      expect(result).toEqual([]);
    });
  });

  describe('validateDataset', () => {
    it('should validate required CNF files exist', async () => {
      // Set up both mock objects
      mockFileSystem.getInfoAsync.mockResolvedValue({ exists: true });
      const FileSystem = require('expo-file-system');
      FileSystem.getInfoAsync.mockResolvedValue({ exists: true });

      const result = await importer.validateDataset('/test/path/');

      expect(result).toBe(true);
      expect(FileSystem.getInfoAsync).toHaveBeenCalledTimes(4); // 4 required files
    });

    it('should return false if required files are missing', async () => {
      // Set up both mock objects
      mockFileSystem.getInfoAsync.mockResolvedValue({ exists: false });
      const FileSystem = require('expo-file-system');
      FileSystem.getInfoAsync.mockResolvedValue({ exists: false });

      const result = await importer.validateDataset('/test/path/');

      expect(result).toBe(false);
    });
  });

  describe('initialize', () => {
    it('should create temp directory and clean up files', async () => {
      // Set up both mock objects
      mockFileSystem.getInfoAsync.mockResolvedValue({ exists: false });
      mockFileSystem.makeDirectoryAsync.mockResolvedValue();
      mockFileSystem.readDirectoryAsync.mockResolvedValue([]);

      const FileSystem = require('expo-file-system');
      FileSystem.getInfoAsync.mockResolvedValue({ exists: false });
      FileSystem.makeDirectoryAsync.mockResolvedValue();
      FileSystem.readDirectoryAsync.mockResolvedValue([]);

      const result = await importer.initialize();

      expect(result).toBe(true);
      expect(FileSystem.makeDirectoryAsync).toHaveBeenCalledWith(
        '/cache/cnf_import/',
        { intermediates: true }
      );
    });

    it('should handle initialization errors', async () => {
      // Set up both mock objects to reject
      mockFileSystem.getInfoAsync.mockRejectedValue(new Error('Permission denied'));
      const FileSystem = require('expo-file-system');
      FileSystem.getInfoAsync.mockRejectedValue(new Error('Permission denied'));

      const result = await importer.initialize();

      expect(result).toBe(false);
    });
  });

  describe('cleanupTempFiles', () => {
    it('should delete all files in temp directory', async () => {
      // Set up both mock objects
      mockFileSystem.readDirectoryAsync.mockResolvedValue(['file1.csv', 'file2.csv']);
      mockFileSystem.deleteAsync.mockResolvedValue();

      const FileSystem = require('expo-file-system');
      FileSystem.readDirectoryAsync.mockResolvedValue(['file1.csv', 'file2.csv']);
      FileSystem.deleteAsync.mockResolvedValue();

      await importer.cleanupTempFiles();

      expect(FileSystem.deleteAsync).toHaveBeenCalledTimes(2);
      expect(FileSystem.deleteAsync).toHaveBeenCalledWith('/cache/cnf_import/file1.csv', { idempotent: true });
      expect(FileSystem.deleteAsync).toHaveBeenCalledWith('/cache/cnf_import/file2.csv', { idempotent: true });
    });

    it('should handle cleanup errors gracefully', async () => {
      mockFileSystem.readDirectoryAsync.mockRejectedValue(new Error('Directory not found'));

      // Should not throw
      await expect(importer.cleanupTempFiles()).resolves.toBeUndefined();
    });
  });

  describe('selectDataset', () => {
    it('should return file URI on successful selection', async () => {
      const DocumentPicker = require('expo-document-picker');
      DocumentPicker.getDocumentAsync.mockResolvedValue({
        type: 'success',
        uri: '/path/to/cnf-dataset.zip'
      });

      const result = await importer.selectDataset();

      expect(result).toBe('/path/to/cnf-dataset.zip');
      expect(DocumentPicker.getDocumentAsync).toHaveBeenCalledWith({
        type: ['application/zip', 'application/x-zip-compressed'],
        copyToCacheDirectory: true,
      });
    });

    it('should return null on cancelled selection', async () => {
      const DocumentPicker = require('expo-document-picker');
      DocumentPicker.getDocumentAsync.mockResolvedValue({
        type: 'cancel'
      });

      const result = await importer.selectDataset();

      expect(result).toBeNull();
    });

    it('should handle selection errors', async () => {
      const DocumentPicker = require('expo-document-picker');
      DocumentPicker.getDocumentAsync.mockRejectedValue(new Error('Permission denied'));

      const result = await importer.selectDataset();

      expect(result).toBeNull();
    });
  });

  describe('extractDataset', () => {
    it('should extract ZIP file to temp directory', async () => {
      const { unzip } = require('react-native-zip-archive');

      // Set up both mock objects
      mockFileSystem.makeDirectoryAsync.mockResolvedValue();
      const FileSystem = require('expo-file-system');
      FileSystem.makeDirectoryAsync.mockResolvedValue();
      unzip.mockResolvedValue();

      const result = await importer.extractDataset('/path/to/dataset.zip');

      expect(result).toBe('/cache/cnf_import/extracted/');
      expect(FileSystem.makeDirectoryAsync).toHaveBeenCalledWith(
        '/cache/cnf_import/extracted/',
        { intermediates: true }
      );
      expect(unzip).toHaveBeenCalledWith('/path/to/dataset.zip', '/cache/cnf_import/extracted/');
    });

    it('should handle extraction errors', async () => {
      const { unzip } = require('react-native-zip-archive');
      unzip.mockRejectedValue(new Error('Invalid ZIP file'));

      const result = await importer.extractDataset('/path/to/invalid.zip');

      expect(result).toBeNull();
    });
  });
});
