/**
 * Recipe Management Service for ZnüniZähler
 * Comprehensive recipe management with AI-powered suggestions and nutrition analysis
 */

import { 
  saveRecipe, 
  getRecipes, 
  updateRecipe, 
  deleteRecipe,
  searchFoods,
  getFoodById 
} from './databaseService';
import analyticsService from './analyticsService';

class RecipeManagementService {
  constructor() {
    this.isInitialized = false;
    this.recipeCache = new Map();
    this.nutritionCache = new Map();
    this.ingredientDatabase = new Map();
    this.cookingTechniques = new Map();
    this.cuisineProfiles = new Map();
  }

  /**
   * Initialize the recipe management service
   */
  async initialize() {
    try {
      console.log('Initializing Recipe Management Service...');
      
      // Load existing recipes
      await this.loadRecipes();
      
      // Initialize cooking techniques and cuisine profiles
      this.initializeCookingKnowledge();
      
      // Load ingredient database
      await this.loadIngredientDatabase();
      
      this.isInitialized = true;
      console.log('Recipe Management Service initialized');
      return true;
    } catch (error) {
      console.error('Failed to initialize Recipe Management Service:', error);
      return false;
    }
  }

  /**
   * Create a new recipe with AI assistance
   * @param {Object} recipeData - Basic recipe information
   * @param {Object} options - Creation options
   * @returns {Promise<Object>} - Created recipe with enhancements
   */
  async createRecipe(recipeData, options = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const {
        enhanceWithAI = true,
        calculateNutrition = true,
        suggestImprovements = true,
        generateVariations = true
      } = options;

      // Validate recipe data
      const validation = this.validateRecipeData(recipeData);
      if (!validation.isValid) {
        throw new Error(`Invalid recipe data: ${validation.errors.join(', ')}`);
      }

      // Normalize and enhance recipe data
      let recipe = this.normalizeRecipeData(recipeData);
      
      // Calculate nutrition information
      if (calculateNutrition) {
        recipe.nutrition = await this.calculateRecipeNutrition(recipe.ingredients, recipe.servings);
      }

      // AI enhancements
      if (enhanceWithAI) {
        recipe = await this.enhanceRecipeWithAI(recipe);
      }

      // Generate cooking tips
      recipe.cookingTips = this.generateCookingTips(recipe);
      
      // Suggest recipe variations
      if (generateVariations) {
        recipe.variations = await this.generateRecipeVariations(recipe);
      }

      // Suggest improvements
      if (suggestImprovements) {
        recipe.improvements = await this.suggestRecipeImprovements(recipe);
      }

      // Add metadata
      recipe.id = this.generateRecipeId();
      recipe.createdAt = new Date().toISOString();
      recipe.updatedAt = recipe.createdAt;
      recipe.version = 1;
      recipe.rating = null;
      recipe.reviews = [];
      recipe.timesCooked = 0;
      recipe.lastCooked = null;

      // Save recipe
      await saveRecipe(recipe);
      this.recipeCache.set(recipe.id, recipe);

      // Track analytics
      analyticsService.trackUserAction('recipe_created', 'recipe_management', {
        cuisine: recipe.cuisine,
        difficulty: recipe.difficulty,
        cookingTime: recipe.cookingTime
      });

      return {
        success: true,
        recipe
      };
    } catch (error) {
      console.error('Error creating recipe:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Search and filter recipes
   * @param {Object} criteria - Search criteria
   * @returns {Promise<Array>} - Filtered and ranked recipes
   */
  async searchRecipes(criteria = {}) {
    try {
      const {
        query = '',
        cuisine = null,
        difficulty = null,
        maxCookingTime = null,
        dietaryRestrictions = [],
        ingredients = [],
        excludeIngredients = [],
        nutritionFilters = {},
        sortBy = 'relevance',
        limit = 20
      } = criteria;

      // Get all recipes
      let recipes = Array.from(this.recipeCache.values());

      // Apply text search
      if (query) {
        recipes = this.filterByTextQuery(recipes, query);
      }

      // Apply cuisine filter
      if (cuisine) {
        recipes = recipes.filter(recipe => recipe.cuisine === cuisine);
      }

      // Apply difficulty filter
      if (difficulty) {
        recipes = recipes.filter(recipe => recipe.difficulty === difficulty);
      }

      // Apply cooking time filter
      if (maxCookingTime) {
        recipes = recipes.filter(recipe => recipe.cookingTime <= maxCookingTime);
      }

      // Apply dietary restrictions
      if (dietaryRestrictions.length > 0) {
        recipes = this.filterByDietaryRestrictions(recipes, dietaryRestrictions);
      }

      // Apply ingredient filters
      if (ingredients.length > 0) {
        recipes = this.filterByIngredients(recipes, ingredients, true);
      }

      if (excludeIngredients.length > 0) {
        recipes = this.filterByIngredients(recipes, excludeIngredients, false);
      }

      // Apply nutrition filters
      if (Object.keys(nutritionFilters).length > 0) {
        recipes = this.filterByNutrition(recipes, nutritionFilters);
      }

      // Sort recipes
      recipes = this.sortRecipes(recipes, sortBy);

      // Limit results
      recipes = recipes.slice(0, limit);

      return {
        success: true,
        recipes,
        totalFound: recipes.length,
        criteria
      };
    } catch (error) {
      console.error('Error searching recipes:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Analyze recipe nutrition and suggest improvements
   * @param {Object} recipe - Recipe to analyze
   * @returns {Promise<Object>} - Nutrition analysis and suggestions
   */
  async analyzeRecipeNutrition(recipe) {
    try {
      // Calculate detailed nutrition
      const nutrition = await this.calculateDetailedNutrition(recipe);
      
      // Analyze nutritional balance
      const balance = this.analyzeNutritionalBalance(nutrition);
      
      // Identify nutritional strengths and weaknesses
      const strengths = this.identifyNutritionalStrengths(nutrition);
      const weaknesses = this.identifyNutritionalWeaknesses(nutrition);
      
      // Generate improvement suggestions
      const improvements = await this.generateNutritionalImprovements(recipe, nutrition);
      
      // Calculate health score
      const healthScore = this.calculateRecipeHealthScore(nutrition);
      
      // Generate nutrition insights
      const insights = this.generateNutritionInsights(nutrition, balance);

      return {
        success: true,
        nutrition,
        balance,
        strengths,
        weaknesses,
        improvements,
        healthScore,
        insights
      };
    } catch (error) {
      console.error('Error analyzing recipe nutrition:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate recipe variations
   * @param {Object} baseRecipe - Original recipe
   * @param {Object} options - Variation options
   * @returns {Promise<Array>} - Recipe variations
   */
  async generateRecipeVariations(baseRecipe, options = {}) {
    try {
      const {
        includeHealthier = true,
        includeDietaryAdaptations = true,
        includeCuisineVariations = true,
        includeSeasonalVariations = true,
        maxVariations = 5
      } = options;

      const variations = [];

      // Healthier variations
      if (includeHealthier) {
        const healthierVariation = await this.createHealthierVariation(baseRecipe);
        if (healthierVariation) {
          variations.push(healthierVariation);
        }
      }

      // Dietary adaptations
      if (includeDietaryAdaptations) {
        const dietaryVariations = await this.createDietaryVariations(baseRecipe);
        variations.push(...dietaryVariations);
      }

      // Cuisine variations
      if (includeCuisineVariations) {
        const cuisineVariations = await this.createCuisineVariations(baseRecipe);
        variations.push(...cuisineVariations);
      }

      // Seasonal variations
      if (includeSeasonalVariations) {
        const seasonalVariations = await this.createSeasonalVariations(baseRecipe);
        variations.push(...seasonalVariations);
      }

      // Limit and rank variations
      const rankedVariations = this.rankVariations(variations, baseRecipe);
      
      return rankedVariations.slice(0, maxVariations);
    } catch (error) {
      console.error('Error generating recipe variations:', error);
      return [];
    }
  }

  /**
   * Scale recipe for different serving sizes
   * @param {Object} recipe - Original recipe
   * @param {number} newServings - Target serving size
   * @returns {Object} - Scaled recipe
   */
  scaleRecipe(recipe, newServings) {
    try {
      const scaleFactor = newServings / recipe.servings;
      
      const scaledRecipe = {
        ...recipe,
        servings: newServings,
        ingredients: recipe.ingredients.map(ingredient => ({
          ...ingredient,
          amount: this.scaleIngredientAmount(ingredient.amount, scaleFactor),
          scaledAmount: ingredient.amount * scaleFactor
        })),
        nutrition: recipe.nutrition ? {
          ...recipe.nutrition,
          calories: Math.round(recipe.nutrition.calories * scaleFactor),
          protein: Math.round(recipe.nutrition.protein * scaleFactor * 10) / 10,
          carbs: Math.round(recipe.nutrition.carbs * scaleFactor * 10) / 10,
          fat: Math.round(recipe.nutrition.fat * scaleFactor * 10) / 10,
          fiber: Math.round(recipe.nutrition.fiber * scaleFactor * 10) / 10
        } : null
      };

      // Adjust cooking instructions if needed
      scaledRecipe.instructions = this.adjustInstructionsForScaling(
        recipe.instructions, 
        scaleFactor
      );

      // Adjust cooking time if significantly scaled
      if (scaleFactor > 2 || scaleFactor < 0.5) {
        scaledRecipe.cookingTime = this.adjustCookingTime(recipe.cookingTime, scaleFactor);
        scaledRecipe.scalingNotes = this.generateScalingNotes(scaleFactor);
      }

      return {
        success: true,
        scaledRecipe,
        scaleFactor,
        originalServings: recipe.servings
      };
    } catch (error) {
      console.error('Error scaling recipe:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate shopping list from recipes
   * @param {Array} recipes - List of recipes
   * @param {Object} options - Shopping list options
   * @returns {Object} - Organized shopping list
   */
  generateShoppingListFromRecipes(recipes, options = {}) {
    try {
      const {
        groupByCategory = true,
        consolidateQuantities = true,
        addPantryCheck = true
      } = options;

      // Extract all ingredients
      const allIngredients = [];
      recipes.forEach(recipe => {
        recipe.ingredients.forEach(ingredient => {
          allIngredients.push({
            ...ingredient,
            recipeId: recipe.id,
            recipeName: recipe.name
          });
        });
      });

      // Consolidate quantities
      const consolidatedIngredients = consolidateQuantities 
        ? this.consolidateIngredientQuantities(allIngredients)
        : allIngredients;

      // Categorize ingredients
      const categorizedList = groupByCategory 
        ? this.categorizeIngredients(consolidatedIngredients)
        : { 'All Items': consolidatedIngredients };

      // Add pantry check suggestions
      if (addPantryCheck) {
        this.addPantryCheckSuggestions(categorizedList);
      }

      return {
        success: true,
        shoppingList: categorizedList,
        totalItems: allIngredients.length,
        recipes: recipes.map(r => ({ id: r.id, name: r.name }))
      };
    } catch (error) {
      console.error('Error generating shopping list:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Private helper methods
   */

  async loadRecipes() {
    try {
      const recipes = await getRecipes();
      recipes.forEach(recipe => {
        this.recipeCache.set(recipe.id, recipe);
      });
      console.log(`Loaded ${recipes.length} recipes`);
    } catch (error) {
      console.error('Error loading recipes:', error);
    }
  }

  initializeCookingKnowledge() {
    // Initialize cooking techniques
    this.cookingTechniques.set('sautéing', {
      description: 'Quick cooking in a small amount of fat over high heat',
      tips: ['Use high heat', 'Keep ingredients moving', 'Don\'t overcrowd the pan'],
      bestFor: ['vegetables', 'thin cuts of meat', 'aromatics']
    });

    this.cookingTechniques.set('roasting', {
      description: 'Cooking with dry heat in an oven',
      tips: ['Preheat oven', 'Use appropriate temperature', 'Don\'t open door frequently'],
      bestFor: ['whole vegetables', 'large cuts of meat', 'root vegetables']
    });

    // Initialize cuisine profiles
    this.cuisineProfiles.set('Mediterranean', {
      commonIngredients: ['olive oil', 'tomatoes', 'garlic', 'herbs', 'lemon'],
      flavorProfile: 'fresh, herbaceous, bright',
      cookingMethods: ['grilling', 'roasting', 'sautéing'],
      healthBenefits: ['heart healthy', 'anti-inflammatory', 'rich in antioxidants']
    });

    this.cuisineProfiles.set('Asian', {
      commonIngredients: ['soy sauce', 'ginger', 'garlic', 'rice', 'vegetables'],
      flavorProfile: 'umami, balanced, aromatic',
      cookingMethods: ['stir-frying', 'steaming', 'braising'],
      healthBenefits: ['vegetable-rich', 'lean proteins', 'fermented foods']
    });
  }

  async loadIngredientDatabase() {
    try {
      // Load common ingredients with nutrition data
      const commonIngredients = await searchFoods('', { limit: 500 });
      commonIngredients.forEach(ingredient => {
        this.ingredientDatabase.set(ingredient.name.toLowerCase(), ingredient);
      });
    } catch (error) {
      console.error('Error loading ingredient database:', error);
    }
  }

  validateRecipeData(recipeData) {
    const errors = [];
    
    if (!recipeData.name || recipeData.name.trim().length === 0) {
      errors.push('Recipe name is required');
    }
    
    if (!recipeData.ingredients || recipeData.ingredients.length === 0) {
      errors.push('At least one ingredient is required');
    }
    
    if (!recipeData.instructions || recipeData.instructions.length === 0) {
      errors.push('Cooking instructions are required');
    }
    
    if (!recipeData.servings || recipeData.servings <= 0) {
      errors.push('Valid serving size is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  normalizeRecipeData(recipeData) {
    return {
      name: recipeData.name.trim(),
      description: recipeData.description?.trim() || '',
      ingredients: this.normalizeIngredients(recipeData.ingredients),
      instructions: this.normalizeInstructions(recipeData.instructions),
      servings: parseInt(recipeData.servings),
      cookingTime: parseInt(recipeData.cookingTime) || 30,
      prepTime: parseInt(recipeData.prepTime) || 15,
      difficulty: recipeData.difficulty || 'medium',
      cuisine: recipeData.cuisine || 'International',
      tags: recipeData.tags || [],
      image: recipeData.image || null
    };
  }

  normalizeIngredients(ingredients) {
    return ingredients.map(ingredient => ({
      name: ingredient.name.trim().toLowerCase(),
      amount: parseFloat(ingredient.amount) || 1,
      unit: ingredient.unit?.trim().toLowerCase() || 'piece',
      notes: ingredient.notes?.trim() || '',
      optional: ingredient.optional || false
    }));
  }

  normalizeInstructions(instructions) {
    return instructions.map((instruction, index) => ({
      step: index + 1,
      instruction: instruction.trim(),
      estimatedTime: this.estimateStepTime(instruction),
      technique: this.identifyTechnique(instruction)
    }));
  }

  async calculateRecipeNutrition(ingredients, servings) {
    let totalNutrition = {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      fiber: 0,
      sugar: 0,
      sodium: 0
    };

    for (const ingredient of ingredients) {
      const nutritionData = await this.getIngredientNutrition(ingredient);
      if (nutritionData) {
        const factor = this.calculateIngredientFactor(ingredient);
        totalNutrition.calories += (nutritionData.calories || 0) * factor;
        totalNutrition.protein += (nutritionData.protein || 0) * factor;
        totalNutrition.carbs += (nutritionData.carbs || 0) * factor;
        totalNutrition.fat += (nutritionData.fat || 0) * factor;
        totalNutrition.fiber += (nutritionData.fiber || 0) * factor;
        totalNutrition.sugar += (nutritionData.sugar || 0) * factor;
        totalNutrition.sodium += (nutritionData.sodium || 0) * factor;
      }
    }

    // Calculate per serving
    Object.keys(totalNutrition).forEach(key => {
      totalNutrition[key] = Math.round((totalNutrition[key] / servings) * 10) / 10;
    });

    return totalNutrition;
  }

  async getIngredientNutrition(ingredient) {
    const cacheKey = `${ingredient.name}_${ingredient.amount}_${ingredient.unit}`;
    
    if (this.nutritionCache.has(cacheKey)) {
      return this.nutritionCache.get(cacheKey);
    }

    try {
      const ingredientData = this.ingredientDatabase.get(ingredient.name);
      if (ingredientData) {
        const nutrition = {
          calories: ingredientData.calories || 0,
          protein: ingredientData.protein || 0,
          carbs: ingredientData.carbs || 0,
          fat: ingredientData.fat || 0,
          fiber: ingredientData.fiber || 0,
          sugar: ingredientData.sugar || 0,
          sodium: ingredientData.sodium || 0
        };
        
        this.nutritionCache.set(cacheKey, nutrition);
        return nutrition;
      }
    } catch (error) {
      console.error(`Error getting nutrition for ${ingredient.name}:`, error);
    }

    return null;
  }

  calculateIngredientFactor(ingredient) {
    // Convert ingredient amount to a standard factor
    // This is simplified - in production, you'd have proper unit conversions
    const unitFactors = {
      'cup': 1,
      'tbsp': 0.0625,
      'tsp': 0.0208,
      'oz': 0.125,
      'lb': 2,
      'g': 0.004,
      'kg': 4,
      'piece': 0.5,
      'medium': 0.5,
      'large': 0.75,
      'small': 0.25
    };

    const factor = unitFactors[ingredient.unit] || 1;
    return ingredient.amount * factor;
  }

  generateRecipeId() {
    return `recipe_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateCookingTips(recipe) {
    const tips = [];
    
    // Add technique-specific tips
    recipe.instructions.forEach(instruction => {
      const technique = this.identifyTechnique(instruction.instruction);
      if (technique && this.cookingTechniques.has(technique)) {
        const techniqueTips = this.cookingTechniques.get(technique).tips;
        tips.push(...techniqueTips);
      }
    });

    // Add cuisine-specific tips
    if (this.cuisineProfiles.has(recipe.cuisine)) {
      const cuisineProfile = this.cuisineProfiles.get(recipe.cuisine);
      tips.push(`This ${recipe.cuisine} dish benefits from ${cuisineProfile.flavorProfile} flavors`);
    }

    // Remove duplicates and return
    return [...new Set(tips)];
  }

  identifyTechnique(instruction) {
    const instructionLower = instruction.toLowerCase();
    
    if (instructionLower.includes('sauté') || instructionLower.includes('fry')) {
      return 'sautéing';
    }
    if (instructionLower.includes('roast') || instructionLower.includes('bake')) {
      return 'roasting';
    }
    if (instructionLower.includes('grill')) {
      return 'grilling';
    }
    if (instructionLower.includes('steam')) {
      return 'steaming';
    }
    
    return null;
  }

  estimateStepTime(instruction) {
    // Simple time estimation based on instruction content
    const instructionLower = instruction.toLowerCase();
    
    if (instructionLower.includes('chop') || instructionLower.includes('dice')) {
      return 5;
    }
    if (instructionLower.includes('cook') || instructionLower.includes('sauté')) {
      return 10;
    }
    if (instructionLower.includes('bake') || instructionLower.includes('roast')) {
      return 30;
    }
    
    return 5; // Default
  }
}

// Export singleton instance
export default new RecipeManagementService();
