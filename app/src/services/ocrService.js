/**
 * Enhanced OCR Service for ZnüniZähler
 * Provides advanced OCR capabilities for nutrition label reading
 */

import * as ImageManipulator from 'expo-image-manipulator';
import { Platform } from 'react-native';

// Configuration for different OCR providers
const OCR_CONFIG = {
  GOOGLE_VISION: {
    apiKey: process.env.GOOGLE_VISION_API_KEY,
    endpoint: 'https://vision.googleapis.com/v1/images:annotate',
  },
  // Fallback to local OCR processing
  LOCAL: {
    enabled: true,
  }
};

/**
 * Enhanced OCR service class
 */
class OCRService {
  constructor() {
    this.isInitialized = false;
    this.provider = 'LOCAL'; // Default to local processing
  }

  /**
   * Initialize the OCR service
   */
  async initialize() {
    try {
      // Check if Google Vision API is available
      if (OCR_CONFIG.GOOGLE_VISION.apiKey) {
        this.provider = 'GOOGLE_VISION';
        console.log('OCR Service initialized with Google Vision API');
      } else {
        console.log('OCR Service initialized with local processing');
      }
      
      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize OCR service:', error);
      return false;
    }
  }

  /**
   * Process image for OCR with preprocessing
   * @param {string} imageUri - URI of the image to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - OCR results
   */
  async processImage(imageUri, options = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Preprocess the image for better OCR accuracy
      const processedImage = await this.preprocessImage(imageUri, options);
      
      // Perform OCR based on available provider
      let ocrResult;
      if (this.provider === 'GOOGLE_VISION') {
        ocrResult = await this.processWithGoogleVision(processedImage.uri);
      } else {
        ocrResult = await this.processWithLocalOCR(processedImage.uri);
      }

      // Post-process the OCR results
      const processedResult = await this.postProcessOCR(ocrResult);

      return {
        success: true,
        data: processedResult,
        provider: this.provider,
        confidence: ocrResult.confidence || 0.8
      };
    } catch (error) {
      console.error('OCR processing failed:', error);
      return {
        success: false,
        error: error.message,
        provider: this.provider
      };
    }
  }

  /**
   * Preprocess image for better OCR accuracy
   * @param {string} imageUri - Original image URI
   * @param {Object} options - Preprocessing options
   * @returns {Promise<Object>} - Processed image
   */
  async preprocessImage(imageUri, options = {}) {
    const {
      resize = { width: 1024, height: 1024 },
      format = ImageManipulator.SaveFormat.JPEG,
      compress = 0.8,
      crop = null
    } = options;

    try {
      const actions = [];

      // Crop if specified
      if (crop) {
        actions.push({
          crop: crop
        });
      }

      // Resize for optimal OCR processing
      actions.push({
        resize: resize
      });

      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        actions,
        {
          compress,
          format,
          base64: this.provider === 'GOOGLE_VISION'
        }
      );

      return result;
    } catch (error) {
      console.error('Image preprocessing failed:', error);
      throw new Error('Failed to preprocess image for OCR');
    }
  }

  /**
   * Process image using Google Vision API
   * @param {string} imageUri - Image URI or base64
   * @returns {Promise<Object>} - OCR results
   */
  async processWithGoogleVision(imageUri) {
    try {
      const requestBody = {
        requests: [{
          image: {
            content: imageUri.includes('base64') ? 
              imageUri.split('base64,')[1] : 
              await this.imageToBase64(imageUri)
          },
          features: [
            { type: 'TEXT_DETECTION', maxResults: 1 },
            { type: 'DOCUMENT_TEXT_DETECTION', maxResults: 1 }
          ],
          imageContext: {
            languageHints: ['en', 'de', 'fr'] // Support multiple languages
          }
        }]
      };

      const response = await fetch(
        `${OCR_CONFIG.GOOGLE_VISION.endpoint}?key=${OCR_CONFIG.GOOGLE_VISION.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody)
        }
      );

      const result = await response.json();
      
      if (result.responses && result.responses[0]) {
        const textAnnotations = result.responses[0].textAnnotations;
        const fullText = result.responses[0].fullTextAnnotation;
        
        return {
          text: textAnnotations?.[0]?.description || '',
          fullText: fullText?.text || '',
          confidence: this.calculateConfidence(textAnnotations),
          blocks: this.extractTextBlocks(fullText),
          boundingBoxes: textAnnotations?.map(annotation => annotation.boundingPoly) || []
        };
      }

      throw new Error('No text detected in image');
    } catch (error) {
      console.error('Google Vision API error:', error);
      throw error;
    }
  }

  /**
   * Process image using local OCR (fallback)
   * @param {string} imageUri - Image URI
   * @returns {Promise<Object>} - OCR results
   */
  async processWithLocalOCR(imageUri) {
    // This is a simplified local OCR implementation
    // In a real app, you might use a library like react-native-tesseract-ocr
    console.log('Using local OCR processing for:', imageUri);
    
    return {
      text: 'Local OCR processing not fully implemented',
      fullText: 'This would contain the full extracted text',
      confidence: 0.6,
      blocks: [],
      boundingBoxes: []
    };
  }

  /**
   * Post-process OCR results to extract nutrition information
   * @param {Object} ocrResult - Raw OCR results
   * @returns {Promise<Object>} - Processed nutrition data
   */
  async postProcessOCR(ocrResult) {
    const { text, fullText } = ocrResult;
    const textToProcess = fullText || text;

    return {
      rawText: textToProcess,
      nutritionFacts: this.extractNutritionFacts(textToProcess),
      ingredients: this.extractIngredients(textToProcess),
      allergens: this.extractAllergens(textToProcess),
      additives: this.extractAdditives(textToProcess),
      servingInfo: this.extractServingInfo(textToProcess),
      confidence: ocrResult.confidence
    };
  }

  /**
   * Extract nutrition facts from OCR text
   * @param {string} text - OCR text
   * @returns {Object} - Nutrition facts
   */
  extractNutritionFacts(text) {
    const nutritionData = {};
    const lines = text.split('\n').map(line => line.trim());

    // Enhanced patterns for nutrition extraction
    const patterns = {
      calories: /(?:calories?|energy|kcal)[:\s]*(\d+(?:\.\d+)?)/i,
      protein: /protein[:\s]*(\d+(?:\.\d+)?)\s*g/i,
      carbs: /(?:carbohydrate|carbs?|total carbs)[:\s]*(\d+(?:\.\d+)?)\s*g/i,
      sugar: /(?:sugars?|total sugars)[:\s]*(\d+(?:\.\d+)?)\s*g/i,
      fat: /(?:total fat|fat)[:\s]*(\d+(?:\.\d+)?)\s*g/i,
      saturatedFat: /saturated fat[:\s]*(\d+(?:\.\d+)?)\s*g/i,
      fiber: /(?:fiber|fibre|dietary fiber)[:\s]*(\d+(?:\.\d+)?)\s*g/i,
      sodium: /sodium[:\s]*(\d+(?:\.\d+)?)\s*(?:mg|g)/i,
      cholesterol: /cholesterol[:\s]*(\d+(?:\.\d+)?)\s*mg/i
    };

    // Extract nutrition values
    for (const [nutrient, pattern] of Object.entries(patterns)) {
      const match = text.match(pattern);
      if (match) {
        nutritionData[nutrient] = parseFloat(match[1]);
      }
    }

    return nutritionData;
  }

  /**
   * Extract ingredients from OCR text
   * @param {string} text - OCR text
   * @returns {Array} - List of ingredients
   */
  extractIngredients(text) {
    const ingredientsPattern = /ingredients?[:\s]*(.*?)(?:\n\n|allergen|contains|may contain|$)/is;
    const match = text.match(ingredientsPattern);
    
    if (match) {
      const ingredientsText = match[1].trim();
      // Split by commas and clean up
      return ingredientsText
        .split(/[,;]/)
        .map(ingredient => ingredient.trim())
        .filter(ingredient => ingredient.length > 0);
    }
    
    return [];
  }

  /**
   * Extract allergen information
   * @param {string} text - OCR text
   * @returns {Array} - List of allergens
   */
  extractAllergens(text) {
    const allergenPattern = /(?:contains?|may contain)[:\s]*(.*?)(?:\n|$)/i;
    const match = text.match(allergenPattern);
    
    if (match) {
      const allergenText = match[1].toLowerCase();
      const commonAllergens = [
        'milk', 'eggs', 'fish', 'shellfish', 'tree nuts', 'peanuts', 
        'wheat', 'soybeans', 'sesame', 'gluten'
      ];
      
      return commonAllergens.filter(allergen => 
        allergenText.includes(allergen)
      );
    }
    
    return [];
  }

  /**
   * Extract food additives (E-numbers)
   * @param {string} text - OCR text
   * @returns {Array} - List of additives
   */
  extractAdditives(text) {
    const eNumberPattern = /E\d{3,4}/gi;
    const matches = text.match(eNumberPattern);
    return matches ? [...new Set(matches)] : [];
  }

  /**
   * Extract serving information
   * @param {string} text - OCR text
   * @returns {Object} - Serving information
   */
  extractServingInfo(text) {
    const servingPattern = /serving size[:\s]*(\d+(?:\.\d+)?)\s*(\w+)/i;
    const match = text.match(servingPattern);
    
    if (match) {
      return {
        size: parseFloat(match[1]),
        unit: match[2].toLowerCase()
      };
    }
    
    return { size: 100, unit: 'g' }; // Default serving size
  }

  /**
   * Helper methods
   */
  async imageToBase64(imageUri) {
    // Convert image URI to base64 for API calls
    // Implementation depends on platform
    return ''; // Placeholder
  }

  calculateConfidence(textAnnotations) {
    if (!textAnnotations || textAnnotations.length === 0) return 0;
    
    // Calculate average confidence from text annotations
    const confidences = textAnnotations
      .filter(annotation => annotation.confidence)
      .map(annotation => annotation.confidence);
    
    return confidences.length > 0 
      ? confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length
      : 0.8; // Default confidence
  }

  extractTextBlocks(fullTextAnnotation) {
    if (!fullTextAnnotation || !fullTextAnnotation.pages) return [];
    
    const blocks = [];
    fullTextAnnotation.pages.forEach(page => {
      page.blocks?.forEach(block => {
        blocks.push({
          text: block.paragraphs?.map(p => 
            p.words?.map(w => 
              w.symbols?.map(s => s.text).join('')
            ).join(' ')
          ).join('\n'),
          boundingBox: block.boundingBox
        });
      });
    });
    
    return blocks;
  }
}

// Export singleton instance
export default new OCRService();
