/**
 * Smart Meal Planning Service for ZnüniZähler
 * Advanced meal planning with AI-powered suggestions, recipe management, and shopping lists
 */

import { 
  getUserPreferences, 
  getNutrientGoals, 
  getConsumptionHistory,
  searchFoods,
  saveRecipe,
  getRecipes,
  saveMealPlan,
  getMealPlans
} from './databaseService';
import analyticsService from './analyticsService';

class SmartMealPlanningService {
  constructor() {
    this.isInitialized = false;
    this.userProfile = null;
    this.nutritionGoals = null;
    this.mealTemplates = new Map();
    this.recipeDatabase = new Map();
    this.seasonalIngredients = new Map();
    this.planningCache = new Map();
    this.shoppingLists = new Map();
  }

  /**
   * Initialize the meal planning service
   */
  async initialize() {
    try {
      console.log('Initializing Smart Meal Planning Service...');
      
      // Load user data
      const [preferences, goals, history] = await Promise.all([
        getUserPreferences(),
        getNutrientGoals(),
        getConsumptionHistory(30)
      ]);

      this.userProfile = preferences;
      this.nutritionGoals = goals;
      
      // Initialize meal templates and recipes
      await this.loadMealTemplates();
      await this.loadRecipeDatabase();
      await this.loadSeasonalIngredients();
      
      this.isInitialized = true;
      console.log('Smart Meal Planning Service initialized');
      return true;
    } catch (error) {
      console.error('Failed to initialize Smart Meal Planning Service:', error);
      return false;
    }
  }

  /**
   * Generate a comprehensive meal plan
   * @param {Object} options - Planning options
   * @returns {Promise<Object>} - Complete meal plan with recipes and shopping list
   */
  async generateMealPlan(options = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const {
        duration = 7,
        startDate = new Date(),
        budget = null,
        cookingTime = 'medium',
        skillLevel = 'intermediate',
        dietaryRestrictions = [],
        preferredCuisines = [],
        mealTypes = ['breakfast', 'lunch', 'dinner'],
        includeSnacks = true,
        varietyLevel = 'high',
        prepTime = 'balanced'
      } = options;

      // Build planning context
      const planningContext = await this.buildPlanningContext(options);
      
      // Generate daily meal plans
      const dailyPlans = [];
      for (let day = 0; day < duration; day++) {
        const dailyPlan = await this.generateDailyMealPlan(day, planningContext);
        dailyPlans.push(dailyPlan);
      }

      // Optimize meal plan for variety and nutrition
      const optimizedPlans = await this.optimizeMealPlan(dailyPlans, planningContext);
      
      // Generate shopping list
      const shoppingList = await this.generateShoppingList(optimizedPlans);
      
      // Calculate nutrition summary
      const nutritionSummary = this.calculatePlanNutrition(optimizedPlans);
      
      // Generate meal prep instructions
      const mealPrepGuide = this.generateMealPrepGuide(optimizedPlans);
      
      // Calculate cost estimate
      const costEstimate = this.calculateCostEstimate(optimizedPlans, shoppingList);

      const mealPlan = {
        id: this.generatePlanId(),
        duration,
        startDate: startDate.toISOString(),
        dailyPlans: optimizedPlans,
        shoppingList,
        nutritionSummary,
        mealPrepGuide,
        costEstimate,
        options,
        generatedAt: new Date().toISOString(),
        version: '1.0'
      };

      // Save meal plan
      await this.saveMealPlan(mealPlan);
      
      return {
        success: true,
        mealPlan
      };
    } catch (error) {
      console.error('Error generating meal plan:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate smart recipe suggestions
   * @param {Object} criteria - Recipe search criteria
   * @returns {Promise<Array>} - Ranked recipe suggestions
   */
  async generateRecipeSuggestions(criteria = {}) {
    try {
      const {
        mealType = 'dinner',
        cookingTime = 30,
        servings = 4,
        difficulty = 'medium',
        ingredients = [],
        excludeIngredients = [],
        nutritionTargets = {},
        cuisine = null,
        dietaryRestrictions = []
      } = criteria;

      // Get base recipe suggestions
      const baseRecipes = await this.getBaseRecipeSuggestions(criteria);
      
      // Apply AI filtering and ranking
      const rankedRecipes = await this.rankRecipesByPreferences(baseRecipes, criteria);
      
      // Add nutrition analysis
      const recipesWithNutrition = await this.addNutritionAnalysis(rankedRecipes);
      
      // Generate cooking tips and modifications
      const enhancedRecipes = await this.enhanceRecipesWithTips(recipesWithNutrition);

      return {
        success: true,
        recipes: enhancedRecipes.slice(0, 10),
        totalFound: baseRecipes.length,
        criteria
      };
    } catch (error) {
      console.error('Error generating recipe suggestions:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a custom recipe
   * @param {Object} recipeData - Recipe information
   * @returns {Promise<Object>} - Created recipe with nutrition analysis
   */
  async createCustomRecipe(recipeData) {
    try {
      const {
        name,
        description,
        ingredients,
        instructions,
        servings,
        cookingTime,
        prepTime,
        difficulty,
        cuisine,
        tags = [],
        image = null
      } = recipeData;

      // Validate recipe data
      const validation = this.validateRecipeData(recipeData);
      if (!validation.isValid) {
        throw new Error(`Invalid recipe data: ${validation.errors.join(', ')}`);
      }

      // Calculate nutrition information
      const nutrition = await this.calculateRecipeNutrition(ingredients, servings);
      
      // Generate cooking tips
      const cookingTips = this.generateCookingTips(recipeData);
      
      // Suggest recipe variations
      const variations = await this.suggestRecipeVariations(recipeData);
      
      // Create recipe object
      const recipe = {
        id: this.generateRecipeId(),
        name,
        description,
        ingredients: this.normalizeIngredients(ingredients),
        instructions: this.normalizeInstructions(instructions),
        servings,
        cookingTime,
        prepTime,
        totalTime: prepTime + cookingTime,
        difficulty,
        cuisine,
        tags,
        image,
        nutrition,
        cookingTips,
        variations,
        createdAt: new Date().toISOString(),
        createdBy: 'user',
        rating: null,
        reviews: []
      };

      // Save recipe to database
      await saveRecipe(recipe);
      
      // Add to local cache
      this.recipeDatabase.set(recipe.id, recipe);

      return {
        success: true,
        recipe
      };
    } catch (error) {
      console.error('Error creating custom recipe:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate intelligent shopping list
   * @param {Object} mealPlan - Meal plan or individual meals
   * @param {Object} options - Shopping list options
   * @returns {Promise<Object>} - Organized shopping list with optimizations
   */
  async generateShoppingList(mealPlan, options = {}) {
    try {
      const {
        groupByCategory = true,
        includePantryCheck = true,
        optimizeForStore = true,
        addQuantityBuffer = 0.1,
        includeAlternatives = true
      } = options;

      // Extract all ingredients from meal plan
      const allIngredients = this.extractIngredientsFromPlan(mealPlan);
      
      // Consolidate quantities
      const consolidatedIngredients = this.consolidateIngredients(allIngredients);
      
      // Apply quantity buffer
      const bufferedIngredients = this.applyQuantityBuffer(consolidatedIngredients, addQuantityBuffer);
      
      // Check against pantry items (if available)
      const filteredIngredients = includePantryCheck 
        ? await this.filterPantryItems(bufferedIngredients)
        : bufferedIngredients;
      
      // Categorize ingredients
      const categorizedList = groupByCategory 
        ? this.categorizeShoppingItems(filteredIngredients)
        : { 'All Items': filteredIngredients };
      
      // Add alternatives for key ingredients
      const listWithAlternatives = includeAlternatives 
        ? await this.addIngredientAlternatives(categorizedList)
        : categorizedList;
      
      // Optimize shopping order
      const optimizedList = optimizeForStore 
        ? this.optimizeShoppingOrder(listWithAlternatives)
        : listWithAlternatives;
      
      // Calculate cost estimates
      const costEstimate = this.calculateShoppingCost(optimizedList);
      
      // Generate shopping tips
      const shoppingTips = this.generateShoppingTips(optimizedList, mealPlan);

      const shoppingList = {
        id: this.generateShoppingListId(),
        items: optimizedList,
        totalItems: this.countTotalItems(optimizedList),
        estimatedCost: costEstimate,
        shoppingTips,
        mealPlanId: mealPlan.id || null,
        generatedAt: new Date().toISOString(),
        options
      };

      return {
        success: true,
        shoppingList
      };
    } catch (error) {
      console.error('Error generating shopping list:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Suggest meal modifications based on preferences
   * @param {Object} meal - Original meal
   * @param {Object} modifications - Requested modifications
   * @returns {Promise<Object>} - Modified meal with alternatives
   */
  async suggestMealModifications(meal, modifications = {}) {
    try {
      const {
        reduceSodium = false,
        increaseProtein = false,
        makeVegetarian = false,
        reduceCalories = false,
        addVegetables = false,
        changeSpiceLevel = null,
        substituteDairy = false,
        makeGlutenFree = false
      } = modifications;

      let modifiedMeal = { ...meal };
      const appliedModifications = [];

      // Apply sodium reduction
      if (reduceSodium) {
        modifiedMeal = await this.reduceMealSodium(modifiedMeal);
        appliedModifications.push('Reduced sodium content');
      }

      // Increase protein
      if (increaseProtein) {
        modifiedMeal = await this.increaseMealProtein(modifiedMeal);
        appliedModifications.push('Increased protein content');
      }

      // Make vegetarian
      if (makeVegetarian) {
        modifiedMeal = await this.makeVegetarian(modifiedMeal);
        appliedModifications.push('Made vegetarian-friendly');
      }

      // Reduce calories
      if (reduceCalories) {
        modifiedMeal = await this.reduceMealCalories(modifiedMeal);
        appliedModifications.push('Reduced calorie content');
      }

      // Add vegetables
      if (addVegetables) {
        modifiedMeal = await this.addVegetablesToMeal(modifiedMeal);
        appliedModifications.push('Added more vegetables');
      }

      // Substitute dairy
      if (substituteDairy) {
        modifiedMeal = await this.substituteDairy(modifiedMeal);
        appliedModifications.push('Replaced dairy ingredients');
      }

      // Make gluten-free
      if (makeGlutenFree) {
        modifiedMeal = await this.makeGlutenFree(modifiedMeal);
        appliedModifications.push('Made gluten-free');
      }

      // Recalculate nutrition
      const updatedNutrition = await this.calculateMealNutrition(modifiedMeal);
      modifiedMeal.nutrition = updatedNutrition;

      // Generate explanation of changes
      const changeExplanation = this.explainMealChanges(meal, modifiedMeal, appliedModifications);

      return {
        success: true,
        originalMeal: meal,
        modifiedMeal,
        appliedModifications,
        changeExplanation,
        nutritionComparison: this.compareNutrition(meal.nutrition, updatedNutrition)
      };
    } catch (error) {
      console.error('Error suggesting meal modifications:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate meal prep instructions
   * @param {Array} dailyPlans - Daily meal plans
   * @returns {Object} - Comprehensive meal prep guide
   */
  generateMealPrepGuide(dailyPlans) {
    const prepGuide = {
      overview: this.generatePrepOverview(dailyPlans),
      timeline: this.generatePrepTimeline(dailyPlans),
      batchCooking: this.identifyBatchCookingOpportunities(dailyPlans),
      storage: this.generateStorageInstructions(dailyPlans),
      reheating: this.generateReheatingInstructions(dailyPlans),
      tips: this.generateMealPrepTips(dailyPlans)
    };

    return prepGuide;
  }

  /**
   * Private helper methods
   */

  async loadMealTemplates() {
    // Load predefined meal templates
    this.mealTemplates.set('breakfast', [
      {
        name: 'Protein Power Bowl',
        structure: ['protein', 'complex_carb', 'healthy_fat', 'fruit'],
        ratios: { protein: 0.3, carbs: 0.4, fat: 0.3 },
        prepTime: 10,
        difficulty: 'easy'
      },
      {
        name: 'Smoothie Bowl',
        structure: ['fruit', 'protein_powder', 'liquid', 'toppings'],
        ratios: { protein: 0.25, carbs: 0.5, fat: 0.25 },
        prepTime: 5,
        difficulty: 'easy'
      },
      {
        name: 'Traditional Breakfast',
        structure: ['protein', 'grain', 'vegetable', 'fruit'],
        ratios: { protein: 0.35, carbs: 0.45, fat: 0.2 },
        prepTime: 15,
        difficulty: 'medium'
      }
    ]);

    this.mealTemplates.set('lunch', [
      {
        name: 'Power Salad',
        structure: ['leafy_greens', 'protein', 'complex_carb', 'healthy_fat', 'vegetables'],
        ratios: { protein: 0.3, carbs: 0.35, fat: 0.35 },
        prepTime: 15,
        difficulty: 'easy'
      },
      {
        name: 'Balanced Bowl',
        structure: ['grain', 'protein', 'vegetables', 'sauce'],
        ratios: { protein: 0.3, carbs: 0.4, fat: 0.3 },
        prepTime: 20,
        difficulty: 'medium'
      },
      {
        name: 'Soup & Sandwich',
        structure: ['soup_base', 'protein', 'bread', 'vegetables'],
        ratios: { protein: 0.25, carbs: 0.45, fat: 0.3 },
        prepTime: 25,
        difficulty: 'medium'
      }
    ]);

    this.mealTemplates.set('dinner', [
      {
        name: 'Classic Plate',
        structure: ['protein', 'starch', 'vegetables', 'sauce'],
        ratios: { protein: 0.35, carbs: 0.35, fat: 0.3 },
        prepTime: 30,
        difficulty: 'medium'
      },
      {
        name: 'One-Pot Meal',
        structure: ['protein', 'vegetables', 'grain', 'broth'],
        ratios: { protein: 0.3, carbs: 0.4, fat: 0.3 },
        prepTime: 45,
        difficulty: 'easy'
      },
      {
        name: 'Stir-Fry',
        structure: ['protein', 'vegetables', 'grain', 'sauce'],
        ratios: { protein: 0.3, carbs: 0.4, fat: 0.3 },
        prepTime: 20,
        difficulty: 'medium'
      }
    ]);
  }

  async loadRecipeDatabase() {
    try {
      // Load saved recipes from database
      const savedRecipes = await getRecipes();
      savedRecipes.forEach(recipe => {
        this.recipeDatabase.set(recipe.id, recipe);
      });

      // Load default recipes if none exist
      if (savedRecipes.length === 0) {
        await this.loadDefaultRecipes();
      }

      console.log(`Loaded ${this.recipeDatabase.size} recipes`);
    } catch (error) {
      console.error('Error loading recipe database:', error);
      await this.loadDefaultRecipes();
    }
  }

  async loadDefaultRecipes() {
    const defaultRecipes = [
      {
        id: 'recipe_001',
        name: 'Mediterranean Quinoa Bowl',
        description: 'A nutritious bowl with quinoa, vegetables, and feta cheese',
        ingredients: [
          { name: 'quinoa', amount: 1, unit: 'cup' },
          { name: 'cucumber', amount: 1, unit: 'medium' },
          { name: 'cherry tomatoes', amount: 1, unit: 'cup' },
          { name: 'feta cheese', amount: 0.5, unit: 'cup' },
          { name: 'olive oil', amount: 2, unit: 'tbsp' },
          { name: 'lemon juice', amount: 1, unit: 'tbsp' }
        ],
        instructions: [
          'Cook quinoa according to package directions',
          'Dice cucumber and halve cherry tomatoes',
          'Combine quinoa, vegetables, and feta in a bowl',
          'Drizzle with olive oil and lemon juice',
          'Season with salt and pepper to taste'
        ],
        servings: 2,
        cookingTime: 20,
        prepTime: 10,
        difficulty: 'easy',
        cuisine: 'Mediterranean',
        tags: ['healthy', 'vegetarian', 'quick'],
        nutrition: {
          calories: 380,
          protein: 15,
          carbs: 45,
          fat: 18,
          fiber: 6
        }
      }
      // Add more default recipes...
    ];

    defaultRecipes.forEach(recipe => {
      this.recipeDatabase.set(recipe.id, recipe);
    });
  }

  async loadSeasonalIngredients() {
    const currentMonth = new Date().getMonth();
    const seasons = {
      spring: [2, 3, 4], // Mar, Apr, May
      summer: [5, 6, 7], // Jun, Jul, Aug
      fall: [8, 9, 10],  // Sep, Oct, Nov
      winter: [11, 0, 1] // Dec, Jan, Feb
    };

    let currentSeason = 'spring';
    for (const [season, months] of Object.entries(seasons)) {
      if (months.includes(currentMonth)) {
        currentSeason = season;
        break;
      }
    }

    const seasonalLists = {
      spring: {
        vegetables: ['asparagus', 'peas', 'spinach', 'artichokes', 'radishes'],
        fruits: ['strawberries', 'apricots', 'rhubarb'],
        herbs: ['mint', 'chives', 'parsley']
      },
      summer: {
        vegetables: ['tomatoes', 'zucchini', 'corn', 'bell peppers', 'eggplant'],
        fruits: ['berries', 'peaches', 'watermelon', 'cherries'],
        herbs: ['basil', 'oregano', 'thyme']
      },
      fall: {
        vegetables: ['pumpkin', 'squash', 'brussels sprouts', 'sweet potatoes', 'cauliflower'],
        fruits: ['apples', 'pears', 'cranberries', 'pomegranate'],
        herbs: ['sage', 'rosemary', 'bay leaves']
      },
      winter: {
        vegetables: ['root vegetables', 'cabbage', 'kale', 'leeks', 'turnips'],
        fruits: ['citrus', 'persimmons', 'winter squash'],
        herbs: ['rosemary', 'thyme', 'sage']
      }
    };

    this.seasonalIngredients.set(currentSeason, seasonalLists[currentSeason]);
  }

  async buildPlanningContext(options) {
    return {
      userProfile: this.userProfile,
      nutritionGoals: this.nutritionGoals,
      seasonalIngredients: this.getCurrentSeasonalIngredients(),
      mealTemplates: this.mealTemplates,
      recipeDatabase: this.recipeDatabase,
      options,
      currentDate: new Date()
    };
  }

  async generateDailyMealPlan(dayIndex, context) {
    const date = new Date(context.options.startDate || new Date());
    date.setDate(date.getDate() + dayIndex);
    
    const dailyTargets = this.calculateDailyTargets(context.nutritionGoals);
    const mealTypes = context.options.mealTypes || ['breakfast', 'lunch', 'dinner'];
    
    const meals = {};
    
    for (const mealType of mealTypes) {
      meals[mealType] = await this.generateMealForSlot(mealType, dailyTargets, context);
    }
    
    if (context.options.includeSnacks) {
      meals.snacks = await this.generateSnacks(dailyTargets, context);
    }

    return {
      date: date.toISOString().split('T')[0],
      dayOfWeek: date.toLocaleDateString('en-US', { weekday: 'long' }),
      meals,
      dailyNutrition: this.calculateDailyNutrition(Object.values(meals).flat()),
      targetAchievement: this.calculateTargetAchievement(dailyTargets, Object.values(meals).flat())
    };
  }

  generatePlanId() {
    return `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateRecipeId() {
    return `recipe_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateShoppingListId() {
    return `shopping_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getCurrentSeasonalIngredients() {
    const currentMonth = new Date().getMonth();
    const seasons = ['spring', 'summer', 'fall', 'winter'];
    const seasonIndex = Math.floor(currentMonth / 3);
    const currentSeason = seasons[seasonIndex];
    
    return this.seasonalIngredients.get(currentSeason) || {};
  }

  async saveMealPlan(mealPlan) {
    try {
      await saveMealPlan(mealPlan);
      this.planningCache.set(mealPlan.id, mealPlan);
    } catch (error) {
      console.error('Error saving meal plan:', error);
    }
  }
}

// Export singleton instance
export default new SmartMealPlanningService();
