/**
 * Analytics Service for ZnüniZähler
 * Processes nutrition data and generates insights
 */

import { 
  getConsumptionByDateRange, 
  getFoodNutrients, 
  getUserGoals 
} from './databaseService';

class AnalyticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get comprehensive consumption statistics
   * @param {string} timeRange - 'week', 'month', 'year'
   * @returns {Promise<Object>} - Statistics data
   */
  async getConsumptionStats(timeRange) {
    const cacheKey = `stats_${timeRange}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const dateRange = this.getDateRange(timeRange);
      const consumptions = await getConsumptionByDateRange(dateRange.start, dateRange.end);
      
      const stats = {
        totalCalories: 0,
        totalProtein: 0,
        totalCarbs: 0,
        totalFat: 0,
        totalFiber: 0,
        totalSodium: 0,
        daysTracked: new Set(),
        mealCounts: {
          breakfast: 0,
          lunch: 0,
          dinner: 0,
          snack: 0
        },
        macroDistribution: [],
        avgCaloriesPerDay: 0,
        streak: 0
      };

      // Process consumption data
      for (const consumption of consumptions) {
        const date = consumption.consumption_date;
        stats.daysTracked.add(date);
        
        // Count meals
        if (stats.mealCounts[consumption.meal_type]) {
          stats.mealCounts[consumption.meal_type]++;
        }

        // Sum nutrients
        for (const item of consumption.items || []) {
          const nutrients = await getFoodNutrients(item.food_id);
          const multiplier = item.quantity / 100; // Assuming nutrients are per 100g
          
          stats.totalCalories += (nutrients.calories || 0) * multiplier;
          stats.totalProtein += (nutrients.protein || 0) * multiplier;
          stats.totalCarbs += (nutrients.carbs || 0) * multiplier;
          stats.totalFat += (nutrients.fat || 0) * multiplier;
          stats.totalFiber += (nutrients.fiber || 0) * multiplier;
          stats.totalSodium += (nutrients.sodium || 0) * multiplier;
        }
      }

      // Calculate averages
      const daysCount = stats.daysTracked.size || 1;
      stats.avgCaloriesPerDay = Math.round(stats.totalCalories / daysCount);

      // Calculate macro distribution
      const totalMacros = stats.totalProtein + stats.totalCarbs + stats.totalFat;
      if (totalMacros > 0) {
        stats.macroDistribution = [
          {
            name: 'Protein',
            amount: Math.round(stats.totalProtein),
            percentage: Math.round((stats.totalProtein / totalMacros) * 100),
            color: '#FF6B6B'
          },
          {
            name: 'Carbs',
            amount: Math.round(stats.totalCarbs),
            percentage: Math.round((stats.totalCarbs / totalMacros) * 100),
            color: '#4ECDC4'
          },
          {
            name: 'Fat',
            amount: Math.round(stats.totalFat),
            percentage: Math.round((stats.totalFat / totalMacros) * 100),
            color: '#45B7D1'
          }
        ];
      }

      // Calculate streak
      stats.streak = await this.calculateStreak();

      this.setCache(cacheKey, stats);
      return stats;
    } catch (error) {
      console.error('Error calculating consumption stats:', error);
      return null;
    }
  }

  /**
   * Get nutrient trends over time
   * @param {string} timeRange - Time range for trends
   * @returns {Promise<Object>} - Chart data for trends
   */
  async getNutrientTrends(timeRange) {
    const cacheKey = `trends_${timeRange}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const dateRange = this.getDateRange(timeRange);
      const consumptions = await getConsumptionByDateRange(dateRange.start, dateRange.end);
      
      // Group by date
      const dailyData = {};
      
      for (const consumption of consumptions) {
        const date = consumption.consumption_date;
        if (!dailyData[date]) {
          dailyData[date] = { calories: 0, protein: 0, carbs: 0, fat: 0 };
        }

        for (const item of consumption.items || []) {
          const nutrients = await getFoodNutrients(item.food_id);
          const multiplier = item.quantity / 100;
          
          dailyData[date].calories += (nutrients.calories || 0) * multiplier;
          dailyData[date].protein += (nutrients.protein || 0) * multiplier;
          dailyData[date].carbs += (nutrients.carbs || 0) * multiplier;
          dailyData[date].fat += (nutrients.fat || 0) * multiplier;
        }
      }

      // Convert to chart format
      const dates = Object.keys(dailyData).sort();
      const chartData = {
        labels: dates.map(date => this.formatDateLabel(date, timeRange)),
        datasets: [
          {
            data: dates.map(date => Math.round(dailyData[date].calories)),
            color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
            strokeWidth: 2
          }
        ]
      };

      this.setCache(cacheKey, chartData);
      return chartData;
    } catch (error) {
      console.error('Error calculating nutrient trends:', error);
      return null;
    }
  }

  /**
   * Get user's nutrient goals and current progress
   * @returns {Promise<Object>} - Goals data
   */
  async getNutrientGoals() {
    const cacheKey = 'nutrient_goals';
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const goals = await getUserGoals();
      const today = new Date().toISOString().split('T')[0];
      const todayConsumptions = await getConsumptionByDateRange(today, today);
      
      // Calculate current intake for today
      const currentIntake = {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sodium: 0
      };

      for (const consumption of todayConsumptions) {
        for (const item of consumption.items || []) {
          const nutrients = await getFoodNutrients(item.food_id);
          const multiplier = item.quantity / 100;
          
          Object.keys(currentIntake).forEach(nutrient => {
            currentIntake[nutrient] += (nutrients[nutrient] || 0) * multiplier;
          });
        }
      }

      // Format goals data
      const goalsData = {};
      Object.keys(currentIntake).forEach(nutrient => {
        const target = goals[nutrient] || this.getDefaultGoal(nutrient);
        goalsData[nutrient] = {
          name: this.getNutrientDisplayName(nutrient),
          current: Math.round(currentIntake[nutrient]),
          target: target,
          unit: this.getNutrientUnit(nutrient),
          percentage: Math.round((currentIntake[nutrient] / target) * 100)
        };
      });

      this.setCache(cacheKey, goalsData);
      return goalsData;
    } catch (error) {
      console.error('Error getting nutrient goals:', error);
      return {};
    }
  }

  /**
   * Get weekly activity progress for heatmap
   * @returns {Promise<Array>} - Activity data
   */
  async getWeeklyProgress() {
    const cacheKey = 'weekly_progress';
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 105); // 15 weeks

      const consumptions = await getConsumptionByDateRange(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );

      // Group by date and count entries
      const dailyActivity = {};
      for (const consumption of consumptions) {
        const date = consumption.consumption_date;
        dailyActivity[date] = (dailyActivity[date] || 0) + 1;
      }

      // Convert to heatmap format
      const heatmapData = [];
      for (let i = 0; i < 105; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        const dateStr = date.toISOString().split('T')[0];
        
        heatmapData.push({
          date: dateStr,
          count: dailyActivity[dateStr] || 0
        });
      }

      this.setCache(cacheKey, heatmapData);
      return heatmapData;
    } catch (error) {
      console.error('Error getting weekly progress:', error);
      return [];
    }
  }

  /**
   * Calculate current tracking streak
   * @returns {Promise<number>} - Streak in days
   */
  async calculateStreak() {
    try {
      const today = new Date();
      let streak = 0;
      
      for (let i = 0; i < 365; i++) { // Check up to a year
        const checkDate = new Date(today);
        checkDate.setDate(today.getDate() - i);
        const dateStr = checkDate.toISOString().split('T')[0];
        
        const dayConsumptions = await getConsumptionByDateRange(dateStr, dateStr);
        
        if (dayConsumptions.length > 0) {
          streak++;
        } else {
          break;
        }
      }
      
      return streak;
    } catch (error) {
      console.error('Error calculating streak:', error);
      return 0;
    }
  }

  /**
   * Helper methods
   */
  getDateRange(timeRange) {
    const end = new Date();
    const start = new Date();
    
    switch (timeRange) {
      case 'week':
        start.setDate(end.getDate() - 7);
        break;
      case 'month':
        start.setMonth(end.getMonth() - 1);
        break;
      case 'year':
        start.setFullYear(end.getFullYear() - 1);
        break;
      default:
        start.setDate(end.getDate() - 7);
    }
    
    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    };
  }

  formatDateLabel(date, timeRange) {
    const d = new Date(date);
    switch (timeRange) {
      case 'week':
        return d.toLocaleDateString('en-US', { weekday: 'short' });
      case 'month':
        return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case 'year':
        return d.toLocaleDateString('en-US', { month: 'short' });
      default:
        return d.toLocaleDateString();
    }
  }

  getDefaultGoal(nutrient) {
    const defaults = {
      calories: 2000,
      protein: 150,
      carbs: 250,
      fat: 65,
      fiber: 25,
      sodium: 2300
    };
    return defaults[nutrient] || 0;
  }

  getNutrientDisplayName(nutrient) {
    const names = {
      calories: 'Calories',
      protein: 'Protein',
      carbs: 'Carbohydrates',
      fat: 'Total Fat',
      fiber: 'Fiber',
      sodium: 'Sodium'
    };
    return names[nutrient] || nutrient;
  }

  getNutrientUnit(nutrient) {
    const units = {
      calories: 'kcal',
      protein: 'g',
      carbs: 'g',
      fat: 'g',
      fiber: 'g',
      sodium: 'mg'
    };
    return units[nutrient] || 'g';
  }

  /**
   * Cache management
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearCache() {
    this.cache.clear();
  }
}

// Export singleton instance
export default new AnalyticsService();
