/**
 * AI Recommendation Service for ZnüniZähler
 * Provides intelligent food suggestions and nutritional insights
 */

import analyticsService from './analyticsService';
import { 
  getUserPreferences, 
  getFoodsByNutrientProfile, 
  getConsumptionHistory,
  searchFoods 
} from './databaseService';

class AIRecommendationService {
  constructor() {
    this.userProfile = null;
    this.preferences = null;
    this.initialized = false;
  }

  /**
   * Initialize the AI service with user data
   */
  async initialize() {
    try {
      this.preferences = await getUserPreferences();
      this.userProfile = await this.buildUserProfile();
      this.initialized = true;
      console.log('AI Recommendation Service initialized');
    } catch (error) {
      console.error('Failed to initialize AI Recommendation Service:', error);
    }
  }

  /**
   * Get personalized food recommendations
   * @param {string} mealType - breakfast, lunch, dinner, snack
   * @param {Object} context - Additional context (time, location, etc.)
   * @returns {Promise<Array>} - Recommended foods
   */
  async getFoodRecommendations(mealType, context = {}) {
    if (!this.initialized) await this.initialize();

    try {
      const currentNutrition = await this.getCurrentDayNutrition();
      const nutritionalNeeds = await this.calculateNutritionalNeeds(currentNutrition, mealType);
      const userHistory = await this.getUserFoodHistory(mealType);
      
      // Generate recommendations based on multiple factors
      const recommendations = await this.generateRecommendations({
        nutritionalNeeds,
        userHistory,
        mealType,
        context,
        preferences: this.preferences
      });

      return this.rankRecommendations(recommendations);
    } catch (error) {
      console.error('Error generating food recommendations:', error);
      return [];
    }
  }

  /**
   * Get nutritional insights and suggestions
   * @returns {Promise<Array>} - Array of insights
   */
  async getNutritionalInsights() {
    if (!this.initialized) await this.initialize();

    try {
      const insights = [];
      const stats = await analyticsService.getConsumptionStats('week');
      const goals = await analyticsService.getNutrientGoals();

      // Analyze nutrient deficiencies
      const deficiencies = this.analyzeNutrientDeficiencies(goals);
      if (deficiencies.length > 0) {
        insights.push({
          type: 'deficiency',
          priority: 'high',
          title: 'Nutrient Gaps Detected',
          message: `You're low on ${deficiencies.join(', ')}. Consider adding foods rich in these nutrients.`,
          suggestions: await this.getFoodsForNutrients(deficiencies),
          icon: 'alert-circle'
        });
      }

      // Analyze macro balance
      const macroInsight = this.analyzeMacroBalance(stats);
      if (macroInsight) {
        insights.push(macroInsight);
      }

      // Analyze eating patterns
      const patternInsight = await this.analyzeEatingPatterns();
      if (patternInsight) {
        insights.push(patternInsight);
      }

      // Suggest variety improvements
      const varietyInsight = await this.analyzeVariety();
      if (varietyInsight) {
        insights.push(varietyInsight);
      }

      return insights.sort((a, b) => this.getPriorityScore(b.priority) - this.getPriorityScore(a.priority));
    } catch (error) {
      console.error('Error generating nutritional insights:', error);
      return [];
    }
  }

  /**
   * Get meal completion suggestions
   * @param {Array} currentMealItems - Items already in the meal
   * @returns {Promise<Array>} - Suggested additions
   */
  async getMealCompletionSuggestions(currentMealItems) {
    try {
      const currentNutrition = this.calculateMealNutrition(currentMealItems);
      const idealMealNutrition = this.getIdealMealNutrition();
      
      const gaps = this.identifyNutritionalGaps(currentNutrition, idealMealNutrition);
      const suggestions = await this.findFoodsToFillGaps(gaps);

      return suggestions.map(suggestion => ({
        ...suggestion,
        reason: this.generateSuggestionReason(suggestion, gaps),
        confidence: this.calculateConfidence(suggestion, gaps)
      }));
    } catch (error) {
      console.error('Error generating meal completion suggestions:', error);
      return [];
    }
  }

  /**
   * Get smart substitution suggestions
   * @param {Object} food - Food to substitute
   * @param {string} reason - Reason for substitution (health, preference, etc.)
   * @returns {Promise<Array>} - Alternative foods
   */
  async getSubstitutionSuggestions(food, reason = 'health') {
    try {
      const criteria = this.getSubstitutionCriteria(food, reason);
      const alternatives = await this.findSimilarFoods(food, criteria);
      
      return alternatives.map(alt => ({
        ...alt,
        similarity: this.calculateSimilarity(food, alt),
        improvement: this.calculateImprovement(food, alt, reason),
        reason: this.generateSubstitutionReason(food, alt, reason)
      }));
    } catch (error) {
      console.error('Error generating substitution suggestions:', error);
      return [];
    }
  }

  /**
   * Private helper methods
   */

  async buildUserProfile() {
    try {
      const history = await getConsumptionHistory(30); // Last 30 days
      const stats = await analyticsService.getConsumptionStats('month');
      
      return {
        avgCaloriesPerDay: stats?.avgCaloriesPerDay || 2000,
        macroPreferences: stats?.macroDistribution || [],
        favoritefoods: this.extractFavoriteFoods(history),
        eatingPatterns: this.analyzeEatingTimes(history),
        dietaryRestrictions: this.preferences?.dietaryRestrictions || [],
        healthGoals: this.preferences?.healthGoals || []
      };
    } catch (error) {
      console.error('Error building user profile:', error);
      return {};
    }
  }

  async getCurrentDayNutrition() {
    const today = new Date().toISOString().split('T')[0];
    const goals = await analyticsService.getNutrientGoals();
    
    return Object.keys(goals).reduce((acc, nutrient) => {
      acc[nutrient] = goals[nutrient]?.current || 0;
      return acc;
    }, {});
  }

  calculateNutritionalNeeds(currentNutrition, mealType) {
    const dailyGoals = {
      calories: this.userProfile?.avgCaloriesPerDay || 2000,
      protein: 150,
      carbs: 250,
      fat: 65,
      fiber: 25
    };

    const mealRatios = {
      breakfast: { calories: 0.25, protein: 0.25, carbs: 0.3, fat: 0.2, fiber: 0.25 },
      lunch: { calories: 0.35, protein: 0.35, carbs: 0.35, fat: 0.35, fiber: 0.35 },
      dinner: { calories: 0.3, protein: 0.3, carbs: 0.25, fat: 0.35, fiber: 0.3 },
      snack: { calories: 0.1, protein: 0.1, carbs: 0.1, fat: 0.1, fiber: 0.1 }
    };

    const mealGoals = {};
    const ratios = mealRatios[mealType] || mealRatios.snack;

    Object.keys(dailyGoals).forEach(nutrient => {
      const dailyGoal = dailyGoals[nutrient];
      const mealGoal = dailyGoal * ratios[nutrient];
      const remaining = mealGoal - (currentNutrition[nutrient] || 0);
      mealGoals[nutrient] = Math.max(0, remaining);
    });

    return mealGoals;
  }

  async generateRecommendations({ nutritionalNeeds, userHistory, mealType, context, preferences }) {
    const recommendations = [];

    // Get foods that match nutritional needs
    const nutritionMatches = await getFoodsByNutrientProfile(nutritionalNeeds);
    recommendations.push(...nutritionMatches.map(food => ({
      ...food,
      score: this.calculateNutritionScore(food, nutritionalNeeds),
      reason: 'nutritional_match'
    })));

    // Get foods based on user history
    const historyMatches = await this.getHistoryBasedRecommendations(userHistory, mealType);
    recommendations.push(...historyMatches.map(food => ({
      ...food,
      score: this.calculateHistoryScore(food, userHistory),
      reason: 'user_preference'
    })));

    // Get seasonal/contextual recommendations
    const contextualMatches = await this.getContextualRecommendations(context, mealType);
    recommendations.push(...contextualMatches.map(food => ({
      ...food,
      score: this.calculateContextScore(food, context),
      reason: 'contextual'
    })));

    return this.deduplicateRecommendations(recommendations);
  }

  rankRecommendations(recommendations) {
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 10) // Top 10 recommendations
      .map((rec, index) => ({
        ...rec,
        rank: index + 1,
        confidence: Math.min(rec.score / 100, 1)
      }));
  }

  analyzeNutrientDeficiencies(goals) {
    const deficiencies = [];
    
    Object.entries(goals).forEach(([nutrient, data]) => {
      if (data.percentage < 70) { // Less than 70% of goal
        deficiencies.push(nutrient);
      }
    });

    return deficiencies;
  }

  analyzeMacroBalance(stats) {
    if (!stats?.macroDistribution) return null;

    const idealRatios = { protein: 25, carbs: 45, fat: 30 };
    const current = stats.macroDistribution.reduce((acc, macro) => {
      acc[macro.name.toLowerCase()] = macro.percentage;
      return acc;
    }, {});

    const imbalances = [];
    Object.entries(idealRatios).forEach(([macro, ideal]) => {
      const actual = current[macro] || 0;
      const diff = Math.abs(actual - ideal);
      if (diff > 10) {
        imbalances.push({
          macro,
          actual,
          ideal,
          direction: actual > ideal ? 'reduce' : 'increase'
        });
      }
    });

    if (imbalances.length === 0) return null;

    return {
      type: 'macro_balance',
      priority: 'medium',
      title: 'Macro Balance Adjustment',
      message: `Consider ${imbalances.map(i => `${i.direction}ing ${i.macro}`).join(', ')} for better balance.`,
      details: imbalances,
      icon: 'scale-balance'
    };
  }

  calculateNutritionScore(food, needs) {
    let score = 0;
    const nutrients = food.nutrients || {};
    
    Object.entries(needs).forEach(([nutrient, needed]) => {
      const provided = nutrients[nutrient] || 0;
      const ratio = Math.min(provided / needed, 1);
      score += ratio * 20; // Max 20 points per nutrient
    });

    return score;
  }

  calculateHistoryScore(food, history) {
    const frequency = history.filter(h => h.food_id === food.id).length;
    const recency = this.getRecencyScore(food, history);
    return (frequency * 10) + recency;
  }

  calculateContextScore(food, context) {
    let score = 50; // Base score
    
    // Time-based scoring
    if (context.timeOfDay) {
      const timePreferences = this.getTimeBasedPreferences(context.timeOfDay);
      if (timePreferences.includes(food.category)) {
        score += 20;
      }
    }

    // Weather-based scoring
    if (context.weather) {
      const weatherPreferences = this.getWeatherBasedPreferences(context.weather);
      if (weatherPreferences.includes(food.category)) {
        score += 15;
      }
    }

    return score;
  }

  getPriorityScore(priority) {
    const scores = { high: 3, medium: 2, low: 1 };
    return scores[priority] || 0;
  }

  deduplicateRecommendations(recommendations) {
    const seen = new Set();
    return recommendations.filter(rec => {
      if (seen.has(rec.id)) return false;
      seen.add(rec.id);
      return true;
    });
  }

  // Additional helper methods would be implemented here...
  extractFavoriteFoods(history) { return []; }
  analyzeEatingTimes(history) { return {}; }
  getHistoryBasedRecommendations(history, mealType) { return []; }
  getContextualRecommendations(context, mealType) { return []; }
  getRecencyScore(food, history) { return 0; }
  getTimeBasedPreferences(timeOfDay) { return []; }
  getWeatherBasedPreferences(weather) { return []; }
}

// Export singleton instance
export default new AIRecommendationService();
