/**
 * Error Tracking Service for ZnüniZähler
 * Handles crash reporting and error monitoring
 */

import Constants from 'expo-constants';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

class ErrorTrackingService {
  constructor() {
    this.isInitialized = false;
    this.userId = null;
    this.sessionId = null;
    this.errorQueue = [];
    this.maxQueueSize = 50;
  }

  /**
   * Initialize error tracking
   */
  async initialize(userId = null) {
    try {
      this.userId = userId;
      this.sessionId = this.generateSessionId();

      // Set up global error handlers
      this.setupGlobalErrorHandlers();

      // Initialize Sentry or other crash reporting service
      await this.initializeCrashReporting();

      this.isInitialized = true;
      console.log('Error Tracking Service initialized');
    } catch (error) {
      console.error('Failed to initialize Error Tracking Service:', error);
    }
  }

  /**
   * Track application errors
   */
  trackError(error, context = {}, severity = 'error') {
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString(),
        userId: this.userId,
        sessionId: this.sessionId,
        context,
        severity,
        platform: Platform.OS,
        appVersion: Constants.expoConfig?.version,
        deviceInfo: this.getDeviceInfo()
      };

      // Add to queue
      this.addToQueue(errorData);

      // Send immediately for high severity errors
      if (severity === 'fatal' || severity === 'error') {
        this.sendErrorReport(errorData);
      }

      // Log in development
      if (__DEV__) {
        console.error('🚨 Error Tracked:', errorData);
      }
    } catch (e) {
      console.error('Error tracking failed:', e);
    }
  }

  /**
   * Track OCR-specific errors
   */
  trackOCRError(error, imageUri, ocrProvider) {
    this.trackError(error, {
      feature: 'ocr',
      imageUri: imageUri ? 'provided' : 'missing',
      ocrProvider,
      errorType: 'ocr_processing_failed'
    }, 'error');
  }

  /**
   * Track AI recommendation errors
   */
  trackAIError(error, context = {}) {
    this.trackError(error, {
      feature: 'ai_recommendations',
      ...context,
      errorType: 'ai_processing_failed'
    }, 'error');
  }

  /**
   * Track database errors
   */
  trackDatabaseError(error, operation, table = null) {
    this.trackError(error, {
      feature: 'database',
      operation,
      table,
      errorType: 'database_operation_failed'
    }, 'error');
  }

  /**
   * Track network errors
   */
  trackNetworkError(error, endpoint, method = 'GET') {
    this.trackError(error, {
      feature: 'network',
      endpoint,
      method,
      errorType: 'network_request_failed'
    }, 'warning');
  }

  /**
   * Track performance issues
   */
  trackPerformanceIssue(operation, duration, threshold) {
    if (duration > threshold) {
      this.trackError(new Error(`Performance issue: ${operation} took ${duration}ms`), {
        feature: 'performance',
        operation,
        duration,
        threshold,
        errorType: 'performance_slow'
      }, 'warning');
    }
  }

  /**
   * Track user-reported issues
   */
  trackUserReportedIssue(description, category, steps = []) {
    this.trackError(new Error(`User reported: ${description}`), {
      feature: 'user_feedback',
      category,
      steps,
      errorType: 'user_reported_issue'
    }, 'info');
  }

  /**
   * Set user context
   */
  setUserContext(userId, userProperties = {}) {
    this.userId = userId;
    
    // Update crash reporting service with user info
    if (this.crashReporting) {
      this.crashReporting.setUser({
        id: userId,
        ...userProperties
      });
    }
  }

  /**
   * Add breadcrumb for debugging
   */
  addBreadcrumb(message, category = 'navigation', level = 'info', data = {}) {
    try {
      const breadcrumb = {
        message,
        category,
        level,
        data,
        timestamp: new Date().toISOString()
      };

      if (this.crashReporting) {
        this.crashReporting.addBreadcrumb(breadcrumb);
      }

      if (__DEV__) {
        console.log('🍞 Breadcrumb:', breadcrumb);
      }
    } catch (error) {
      console.error('Failed to add breadcrumb:', error);
    }
  }

  /**
   * Private helper methods
   */

  setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    if (typeof global !== 'undefined' && global.HermesInternal) {
      // React Native with Hermes
      global.HermesInternal.setUnhandledPromiseRejectionHandler((error) => {
        this.trackError(error, { errorType: 'unhandled_promise_rejection' }, 'fatal');
      });
    }

    // Handle JavaScript errors
    const originalConsoleError = console.error;
    console.error = (...args) => {
      // Check if this looks like an error
      if (args[0] instanceof Error) {
        this.trackError(args[0], { errorType: 'console_error' }, 'error');
      } else if (typeof args[0] === 'string' && args[0].toLowerCase().includes('error')) {
        this.trackError(new Error(args.join(' ')), { errorType: 'console_error' }, 'warning');
      }
      
      originalConsoleError.apply(console, args);
    };
  }

  async initializeCrashReporting() {
    try {
      // In a real app, initialize Sentry or similar service
      // import * as Sentry from 'sentry-expo';
      
      // Sentry.init({
      //   dsn: Constants.expoConfig?.extra?.sentryDsn,
      //   enableInExpoDevelopment: false,
      //   debug: __DEV__
      // });

      console.log('Crash reporting initialized (placeholder)');
    } catch (error) {
      console.error('Crash reporting initialization failed:', error);
    }
  }

  addToQueue(errorData) {
    this.errorQueue.push(errorData);
    
    // Limit queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift(); // Remove oldest error
    }
  }

  async sendErrorReport(errorData) {
    try {
      // In a real app, send to your error reporting service
      if (__DEV__) {
        console.log('📤 Sending error report:', errorData);
      }
      
      // Example: Send to your backend
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorData)
      // });
    } catch (error) {
      console.error('Failed to send error report:', error);
    }
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getDeviceInfo() {
    return {
      platform: Platform.OS,
      version: Platform.Version,
      isDevice: Device.isDevice,
      brand: Device.brand,
      modelName: Device.modelName,
      osName: Device.osName,
      osVersion: Device.osVersion
    };
  }

  /**
   * Flush error queue (useful before app closes)
   */
  async flush() {
    try {
      const errors = [...this.errorQueue];
      this.errorQueue = [];
      
      for (const error of errors) {
        await this.sendErrorReport(error);
      }
    } catch (error) {
      console.error('Error flushing error queue:', error);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    return {
      queueSize: this.errorQueue.length,
      sessionId: this.sessionId,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Clear error queue
   */
  clearQueue() {
    this.errorQueue = [];
  }
}

// Export singleton instance
export default new ErrorTrackingService();

// Export convenience functions
export const trackError = (error, context, severity) => 
  ErrorTrackingService.trackError(error, context, severity);

export const trackOCRError = (error, imageUri, ocrProvider) => 
  ErrorTrackingService.trackOCRError(error, imageUri, ocrProvider);

export const trackAIError = (error, context) => 
  ErrorTrackingService.trackAIError(error, context);

export const trackDatabaseError = (error, operation, table) => 
  ErrorTrackingService.trackDatabaseError(error, operation, table);

export const trackNetworkError = (error, endpoint, method) => 
  ErrorTrackingService.trackNetworkError(error, endpoint, method);

export const addBreadcrumb = (message, category, level, data) => 
  ErrorTrackingService.addBreadcrumb(message, category, level, data);
