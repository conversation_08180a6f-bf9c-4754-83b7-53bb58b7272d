/**
 * Znü<PERSON>Zähler Main App
 * Entry point for the application
 */

import React, { useState, useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeProvider } from './theme/ThemeContext';
import MainApp from './screens/MainApp';
import './locales/i18n'; // Initialize i18n

// Main App component
export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize app data
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Here you would load any initial data from AsyncStorage
        // For example, user preferences, cached data, etc.

        // Simulate loading time
        await new Promise(resolve => setTimeout(resolve, 1000));

        setIsInitialized(true);
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing app:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  // Show loading screen while initializing
  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#121212' }}>
        <ActivityIndicator size="large" color="#4CAF50" />
      </View>
    );
  }

  // Render the app with ThemeProvider
  return (
    <ThemeProvider>
      <MainApp isInitialized={isInitialized} />
    </ThemeProvider>
  );
}
