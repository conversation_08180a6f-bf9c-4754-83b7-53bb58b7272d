/**
 * Swiss German Demo Component
 * Showcases Swiss German specific features and expressions
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert
} from 'react-native';
import { Card, Button, Chip, Divider } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from '../hooks/useTranslation';

const SwissGermanDemo = () => {
  const { 
    t, 
    isSwissGerman, 
    getSwissGermanGreeting, 
    getMealTimeSwissGerman,
    currentLanguage 
  } = useTranslation();
  
  const [currentTime, setCurrentTime] = useState(new Date());
  const [greeting, setGreeting] = useState('');
  const [mealTime, setMealTime] = useState('');

  // Update time and context every minute
  useEffect(() => {
    const updateTimeContext = () => {
      const now = new Date();
      setCurrentTime(now);
      
      if (isSwissGerman) {
        setGreeting(getSwissGermanGreeting());
        setMealTime(getMealTimeSwissGerman());
      }
    };

    updateTimeContext();
    const interval = setInterval(updateTimeContext, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [isSwissGerman, getSwissGermanGreeting, getMealTimeSwissGerman]);

  // Swiss German expressions
  const swissExpressions = [
    {
      key: 'grueziMitenand',
      context: 'Formal greeting',
      usage: 'Used when addressing a group'
    },
    {
      key: 'hoi',
      context: 'Casual greeting',
      usage: 'Informal hello'
    },
    {
      key: 'salue',
      context: 'Evening greeting',
      usage: 'Used in the evening'
    },
    {
      key: 'luegMol',
      context: 'Look here',
      usage: 'Getting attention'
    },
    {
      key: 'gellMerci',
      context: 'Thank you',
      usage: 'Polite thanks'
    }
  ];

  // Swiss meal times
  const swissMealTimes = [
    {
      key: 'morgenessen',
      time: '6:00-9:00',
      description: 'Breakfast'
    },
    {
      key: 'znueni',
      time: '9:00-11:00',
      description: 'Morning snack'
    },
    {
      key: 'zmittag',
      time: '11:00-14:00',
      description: 'Lunch'
    },
    {
      key: 'zvieri',
      time: '14:00-17:00',
      description: 'Afternoon snack'
    },
    {
      key: 'znacht',
      time: '17:00+',
      description: 'Dinner'
    }
  ];

  const showExpressionInfo = (expression) => {
    Alert.alert(
      t(`swissSpecific.expressions.${expression.key}`),
      `${expression.context}\n\n${expression.usage}`,
      [{ text: t('common.ok') }]
    );
  };

  if (!isSwissGerman) {
    return (
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.notSwissContainer}>
            <MaterialIcons name="info" size={48} color="#666" />
            <Text style={styles.notSwissTitle}>
              Swiss German Features
            </Text>
            <Text style={styles.notSwissText}>
              Switch to Swiss German (Schwiizerdütsch) in the language settings to see special Swiss expressions and meal times.
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Title 
          title="🇨🇭 Schwiizerdütsch Features" 
          subtitle="Regionali Bsunderheite"
        />
        <Card.Content>
          <View style={styles.currentContext}>
            <Text style={styles.contextTitle}>Aktuelli Ziit:</Text>
            <Text style={styles.contextValue}>
              {currentTime.toLocaleTimeString('de-CH')}
            </Text>
            
            <Text style={styles.contextTitle}>Grüessig:</Text>
            <Text style={styles.contextValue}>{greeting}</Text>
            
            <Text style={styles.contextTitle}>Mahlziit:</Text>
            <Text style={styles.contextValue}>{mealTime}</Text>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Title title="Schwiizer Usdrück" subtitle="Swiss German Expressions" />
        <Card.Content>
          <View style={styles.expressionsGrid}>
            {swissExpressions.map((expression, index) => (
              <TouchableOpacity
                key={index}
                style={styles.expressionCard}
                onPress={() => showExpressionInfo(expression)}
              >
                <Text style={styles.expressionText}>
                  {t(`swissSpecific.expressions.${expression.key}`)}
                </Text>
                <Text style={styles.expressionContext}>
                  {expression.context}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Title title="Schwiizer Mahlziite" subtitle="Swiss Meal Times" />
        <Card.Content>
          <View style={styles.mealTimesContainer}>
            {swissMealTimes.map((meal, index) => (
              <View key={index} style={styles.mealTimeRow}>
                <View style={styles.mealTimeInfo}>
                  <Text style={styles.mealTimeName}>
                    {t(`swissSpecific.${meal.key}`)}
                  </Text>
                  <Text style={styles.mealTimeDescription}>
                    {meal.description}
                  </Text>
                </View>
                <Chip 
                  mode="outlined" 
                  style={styles.timeChip}
                  textStyle={styles.timeChipText}
                >
                  {meal.time}
                </Chip>
              </View>
            ))}
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Title title="Sprach-Iistellige" subtitle="Language Settings" />
        <Card.Content>
          <View style={styles.languageInfo}>
            <Text style={styles.infoText}>
              Aktuelli Sprach: <Text style={styles.boldText}>{currentLanguage}</Text>
            </Text>
            <Text style={styles.infoText}>
              Schwiizerdütsch aktiv: <Text style={styles.boldText}>Ja ✓</Text>
            </Text>
            
            <Divider style={styles.divider} />
            
            <Text style={styles.featureTitle}>Aktivierti Features:</Text>
            <View style={styles.featuresList}>
              <Text style={styles.featureItem}>• Regionali Grüessige</Text>
              <Text style={styles.featureItem}>• Schwiizer Mahlziite-Name</Text>
              <Text style={styles.featureItem}>• Lokali Usdrück</Text>
              <Text style={styles.featureItem}>• Schwiizer Formatierig</Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    margin: 16,
    marginBottom: 8,
    elevation: 2,
  },
  notSwissContainer: {
    alignItems: 'center',
    padding: 20,
  },
  notSwissTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  notSwissText: {
    textAlign: 'center',
    color: '#666',
    lineHeight: 20,
  },
  currentContext: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
  },
  contextTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginTop: 8,
  },
  contextValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  expressionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  expressionCard: {
    width: '48%',
    backgroundColor: '#e8f5e8',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  expressionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2e7d32',
    marginBottom: 4,
  },
  expressionContext: {
    fontSize: 12,
    color: '#666',
  },
  mealTimesContainer: {
    marginTop: 8,
  },
  mealTimeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  mealTimeInfo: {
    flex: 1,
  },
  mealTimeName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  mealTimeDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  timeChip: {
    backgroundColor: '#fff3e0',
  },
  timeChipText: {
    color: '#f57c00',
    fontSize: 12,
  },
  languageInfo: {
    marginTop: 8,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  boldText: {
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  divider: {
    marginVertical: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  featuresList: {
    marginLeft: 8,
  },
  featureItem: {
    fontSize: 14,
    marginBottom: 4,
    color: '#666',
  },
});

export default SwissGermanDemo;
