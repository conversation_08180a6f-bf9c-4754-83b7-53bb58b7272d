/**
 * Food Item Component for ZnüniZähler
 * Displays a food item with its nutritional information
 */

import React, { useState } from 'react';
import { StyleSheet, View, Image } from 'react-native';
import { Card, Text, IconButton, Chip, TouchableRipple, Button } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import { saveFood } from '../services/databaseService';

/**
 * Food Item Component
 * @param {Object} props - Component props
 * @param {Object} props.food - Food data
 * @param {Function} props.onSelect - Callback function when food is selected
 * @param {boolean} props.showDetails - Whether to show detailed information
 * @param {Function} props.onEdit - Callback function when food is edited
 * @returns {JSX.Element} - Food item component
 */
const FoodItem = ({ food, onSelect, showDetails = false, onEdit }) => {
  const { theme } = useTheme();
  const [expanded, setExpanded] = useState(showDetails);
  const [isFavorite, setIsFavorite] = useState(food.is_favorite === 1);

  // Get macronutrients
  const calories = food.nutrients?.find(n => n.name === 'Calories')?.amount || 0;
  const protein = food.nutrients?.find(n => n.name === 'Protein')?.amount || 0;
  const carbs = food.nutrients?.find(n => n.name === 'Carbohydrates')?.amount || 0;
  const fat = food.nutrients?.find(n => n.name === 'Fat')?.amount || 0;

  // Toggle favorite
  const toggleFavorite = async () => {
    try {
      const newFavoriteStatus = !isFavorite;
      setIsFavorite(newFavoriteStatus);

      // Update food in database
      await saveFood({
        ...food,
        is_favorite: newFavoriteStatus ? 1 : 0
      });
    } catch (error) {
      console.error('Error toggling favorite:', error);
      // Revert UI state if error
      setIsFavorite(!isFavorite);
    }
  };

  return (
    <Card style={styles.card} mode="outlined">
      <TouchableRipple onPress={() => setExpanded(!expanded)}>
        <Card.Title
          title={food.name}
          subtitle={
            <View style={styles.subtitleContainer}>
              <Text>{food.brand_name || 'No brand'}</Text>
              {food.source && (
                <Chip
                  style={styles.sourceChip}
                  textStyle={styles.sourceChipText}
                  icon="database"
                >
                  {food.source}
                </Chip>
              )}
            </View>
          }
          left={(props) => (
            food.image_url ? (
              <Image source={{ uri: food.image_url }} style={styles.image} />
            ) : (
              <IconButton {...props} icon="food" />
            )
          )}
          right={(props) => (
            <IconButton
              {...props}
              icon={isFavorite ? 'star' : 'star-outline'}
              iconColor={isFavorite ? theme.colors.primary : theme.colors.onSurface}
              onPress={toggleFavorite}
            />
          )}
        />
      </TouchableRipple>

      <Card.Content>
        <View style={styles.macrosContainer}>
          <Chip icon="fire" style={styles.macroChip}>
            {calories} kcal
          </Chip>
          <Chip icon="protein" style={styles.macroChip}>
            {protein}g
          </Chip>
          <Chip icon="grain" style={styles.macroChip}>
            {carbs}g
          </Chip>
          <Chip icon="oil" style={styles.macroChip}>
            {fat}g
          </Chip>
        </View>

        {expanded && (
          <View style={styles.detailsContainer}>
            {food.description && (
              <Text style={styles.description}>{food.description}</Text>
            )}

            {food.barcode && (
              <Text style={styles.barcode}>Barcode: {food.barcode}</Text>
            )}

            <Text style={styles.servingSize}>
              Serving size: {food.serving_size} {food.serving_unit}
            </Text>

            {food.nutrients && food.nutrients.length > 0 && (
              <View style={styles.nutrientsContainer}>
                <Text style={styles.sectionTitle}>Nutrition Facts</Text>
                {food.nutrients.map((nutrient) => (
                  <View key={nutrient.nutrient_id} style={styles.nutrientRow}>
                    <Text>{nutrient.name}</Text>
                    <Text>
                      {nutrient.amount} {nutrient.unit}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            {food.ingredients && food.ingredients.length > 0 && (
              <View style={styles.ingredientsContainer}>
                <Text style={styles.sectionTitle}>Ingredients</Text>
                <View style={styles.ingredientChips}>
                  {food.ingredients.map((ingredient) => (
                    <Chip
                      key={ingredient.ingredient_id}
                      style={[
                        styles.ingredientChip,
                        ingredient.is_allergen === 1 && styles.allergenChip
                      ]}
                      textStyle={ingredient.is_allergen === 1 ? styles.allergenText : null}
                    >
                      {ingredient.name}
                    </Chip>
                  ))}
                </View>
              </View>
            )}
          </View>
        )}
      </Card.Content>

      <Card.Actions>
        {onSelect && (
          <Button onPress={() => onSelect(food)}>Select</Button>
        )}
        {onEdit && (
          <Button onPress={() => onEdit(food)}>Edit</Button>
        )}
        <Button onPress={() => setExpanded(!expanded)}>
          {expanded ? 'Hide Details' : 'Show Details'}
        </Button>
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 8,
    marginHorizontal: 16,
    elevation: 2,
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  sourceChip: {
    height: 24,
    marginLeft: 8,
    backgroundColor: '#e0e0e0',
  },
  sourceChipText: {
    fontSize: 10,
  },
  macrosContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  macroChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  detailsContainer: {
    marginTop: 16,
  },
  description: {
    marginBottom: 8,
  },
  barcode: {
    marginBottom: 8,
    fontStyle: 'italic',
  },
  servingSize: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
    marginTop: 16,
  },
  nutrientsContainer: {
    marginBottom: 16,
  },
  nutrientRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#ccc',
  },
  ingredientsContainer: {
    marginBottom: 16,
  },
  ingredientChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  ingredientChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  allergenChip: {
    backgroundColor: '#ffcdd2',
  },
  allergenText: {
    color: '#b71c1c',
  },
});

export default FoodItem;
