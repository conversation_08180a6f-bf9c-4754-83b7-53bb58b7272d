/**
 * Language Selector Component
 * Allows users to switch between English, German, and Swiss German
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  Alert
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from '../hooks/useTranslation';

const LanguageSelector = ({ style, showLabel = true }) => {
  const { 
    t, 
    currentLanguage, 
    supportedLanguages, 
    changeLanguage,
    isSwissGerman 
  } = useTranslation();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  // Get current language display info
  const getCurrentLanguageInfo = () => {
    return supportedLanguages.find(lang => lang.code === currentLanguage) || 
           supportedLanguages[0];
  };

  // Handle language change
  const handleLanguageChange = async (languageCode) => {
    if (languageCode === currentLanguage) {
      setModalVisible(false);
      return;
    }

    setIsChanging(true);
    
    try {
      const success = await changeLanguage(languageCode);
      
      if (success) {
        setModalVisible(false);
        
        // Show success message in the new language
        setTimeout(() => {
          const newLangInfo = supportedLanguages.find(lang => lang.code === languageCode);
          Alert.alert(
            t('common.success'),
            t('profile.languageChanged', { language: newLangInfo.nativeName }),
            [{ text: t('common.ok') }]
          );
        }, 100);
      } else {
        Alert.alert(
          t('common.error'),
          t('errors.languageChangeFailed'),
          [{ text: t('common.ok') }]
        );
      }
    } catch (error) {
      console.error('Error changing language:', error);
      Alert.alert(
        t('common.error'),
        t('errors.unknown'),
        [{ text: t('common.ok') }]
      );
    } finally {
      setIsChanging(false);
    }
  };

  // Render language option
  const renderLanguageOption = ({ item }) => {
    const isSelected = item.code === currentLanguage;
    
    return (
      <TouchableOpacity
        style={[
          styles.languageOption,
          isSelected && styles.selectedLanguageOption
        ]}
        onPress={() => handleLanguageChange(item.code)}
        disabled={isChanging}
      >
        <View style={styles.languageInfo}>
          <Text style={[
            styles.languageName,
            isSelected && styles.selectedLanguageName
          ]}>
            {item.nativeName}
          </Text>
          <Text style={[
            styles.languageCode,
            isSelected && styles.selectedLanguageCode
          ]}>
            {item.name}
          </Text>
        </View>
        
        {isSelected && (
          <MaterialIcons 
            name="check" 
            size={24} 
            color="#4CAF50" 
          />
        )}
        
        {/* Special indicator for Swiss German */}
        {item.code === 'de-CH' && (
          <View style={styles.swissFlag}>
            <Text style={styles.swissFlagText}>🇨🇭</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const currentLangInfo = getCurrentLanguageInfo();

  return (
    <View style={[styles.container, style]}>
      {showLabel && (
        <Text style={styles.label}>{t('profile.language')}</Text>
      )}
      
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setModalVisible(true)}
        disabled={isChanging}
      >
        <View style={styles.currentLanguage}>
          <Text style={styles.currentLanguageName}>
            {currentLangInfo.nativeName}
          </Text>
          {isSwissGerman && (
            <Text style={styles.swissIndicator}>🇨🇭</Text>
          )}
        </View>
        
        <MaterialIcons 
          name="expand-more" 
          size={24} 
          color="#666" 
        />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {t('profile.selectLanguage')}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <MaterialIcons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <FlatList
              data={supportedLanguages}
              keyExtractor={(item) => item.code}
              renderItem={renderLanguageOption}
              style={styles.languageList}
              showsVerticalScrollIndicator={false}
            />

            {isSwissGerman && (
              <View style={styles.swissNote}>
                <MaterialIcons name="info" size={16} color="#666" />
                <Text style={styles.swissNoteText}>
                  {t('profile.swissGermanNote')}
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  currentLanguage: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentLanguageName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  swissIndicator: {
    marginLeft: 8,
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  languageList: {
    maxHeight: 300,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
  },
  selectedLanguageOption: {
    backgroundColor: '#e8f5e8',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  selectedLanguageName: {
    color: '#2e7d32',
  },
  languageCode: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  selectedLanguageCode: {
    color: '#4CAF50',
  },
  swissFlag: {
    marginLeft: 8,
  },
  swissFlagText: {
    fontSize: 20,
  },
  swissNote: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
  },
  swissNoteText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
});

export default LanguageSelector;
