/**
 * Food List Component for Znü<PERSON>Zähler
 * Displays a list of food items with search and filtering
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, FlatList, ActivityIndicator } from 'react-native';
import { Searchbar, Chip, Text, Button, FAB, Badge } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import FoodItem from './FoodItem';
import BarcodeScanner from './BarcodeScanner';
import { searchFoods, getFavorites, getCustomFoods } from '../services/databaseService';

/**
 * Food List Component
 * @param {Object} props - Component props
 * @param {Function} props.onSelectFood - Callback function when a food is selected
 * @param {Function} props.onAddFood - Callback function to add a new food
 * @param {Function} props.onEditFood - Callback function to edit a food
 * @returns {JSX.Element} - Food list component
 */
const FoodList = ({ onSelectFood, onAddFood, onEditFood }) => {
  const { theme } = useTheme();
  const [foods, setFoods] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'favorites', 'custom', 'usda', 'foodb'
  const [showScanner, setShowScanner] = useState(false);
  const [sourceStats, setSourceStats] = useState({ usda: 0, foodb: 0 });

  // Load foods based on filter
  useEffect(() => {
    const loadFoods = async () => {
      setIsLoading(true);
      try {
        let results = [];
        let source = null;

        if (filter === 'usda') {
          source = 'USDA';
        } else if (filter === 'foodb') {
          source = 'FoodB';
        }

        if (searchQuery.trim()) {
          // Search foods by name, optionally filtered by source
          results = await searchFoods(searchQuery, 50, source);
        } else if (filter === 'favorites') {
          // Get favorite foods
          results = await getFavorites();
        } else if (filter === 'custom') {
          // Get custom foods
          results = await getCustomFoods();
        } else if (filter === 'usda') {
          // Get USDA foods
          results = await searchFoods('', 50, 'USDA');
        } else if (filter === 'foodb') {
          // Get FoodB foods
          results = await searchFoods('', 50, 'FoodB');
        } else {
          // Get all foods (limited to recent ones)
          results = await searchFoods('', 50);
        }

        setFoods(results);

        // Count foods by source
        if (filter === 'all' || searchQuery.trim()) {
          const usdaCount = results.filter(food => food.source && food.source.startsWith('USDA')).length;
          const foodbCount = results.filter(food => food.source && food.source.startsWith('FoodB')).length;
          setSourceStats({ usda: usdaCount, foodb: foodbCount });
        }
      } catch (error) {
        console.error('Error loading foods:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadFoods();
  }, [searchQuery, filter]);

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  // Handle filter change
  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
  };

  // Handle barcode scan
  const handleBarcodeScan = (food) => {
    setShowScanner(false);
    if (onSelectFood) {
      onSelectFood(food);
    }
  };

  // Render food item
  const renderFoodItem = ({ item }) => (
    <FoodItem
      food={item}
      onSelect={onSelectFood}
      onEdit={onEditFood}
    />
  );

  // Render empty list
  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>No foods found</Text>
      <Button
        mode="contained"
        onPress={() => onAddFood ? onAddFood() : null}
        style={styles.addButton}
      >
        Add New Food
      </Button>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Searchbar
        placeholder="Search foods..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchBar}
      />

      <View style={styles.filtersContainer}>
        <Chip
          selected={filter === 'all'}
          onPress={() => handleFilterChange('all')}
          style={styles.filterChip}
        >
          All
        </Chip>
        <Chip
          selected={filter === 'usda'}
          onPress={() => handleFilterChange('usda')}
          style={styles.filterChip}
          icon="database"
        >
          USDA {filter === 'all' && sourceStats.usda > 0 && (
            <Badge size={16} style={styles.badge}>{sourceStats.usda}</Badge>
          )}
        </Chip>
        <Chip
          selected={filter === 'foodb'}
          onPress={() => handleFilterChange('foodb')}
          style={styles.filterChip}
          icon="database"
        >
          FoodB {filter === 'all' && sourceStats.foodb > 0 && (
            <Badge size={16} style={styles.badge}>{sourceStats.foodb}</Badge>
          )}
        </Chip>
        <Chip
          selected={filter === 'favorites'}
          onPress={() => handleFilterChange('favorites')}
          style={styles.filterChip}
          icon="star"
        >
          Favorites
        </Chip>
        <Chip
          selected={filter === 'custom'}
          onPress={() => handleFilterChange('custom')}
          style={styles.filterChip}
          icon="pencil"
        >
          Custom
        </Chip>
      </View>

      {isLoading ? (
        <ActivityIndicator size="large" color={theme.colors.primary} style={styles.loader} />
      ) : (
        <FlatList
          data={foods}
          renderItem={renderFoodItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
        />
      )}

      <FAB
        icon="barcode-scan"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => setShowScanner(true)}
      />

      {showScanner && (
        <BarcodeScanner
          onScan={handleBarcodeScan}
          onClose={() => setShowScanner(false)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchBar: {
    margin: 16,
    elevation: 2,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  badge: {
    marginLeft: 4,
    backgroundColor: '#666',
  },
  listContent: {
    paddingBottom: 80, // Space for FAB
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 20,
  },
  addButton: {
    marginTop: 10,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default FoodList;
