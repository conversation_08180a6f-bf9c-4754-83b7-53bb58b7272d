/**
 * Logging Dashboard Component
 * 
 * Provides a comprehensive view of all logging data including
 * compile-time errors, runtime monitoring, and system logs.
 */

import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Alert,
  Share,
  RefreshControl 
} from 'react-native';
import { Card, Button, List, Chip, ProgressBar, Divider } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { 
  getLogs, 
  clearLogs, 
  exportLogs, 
  LOG_LEVELS, 
  LOG_CATEGORIES 
} from '../services/logService';
import { 
  getCompileIssues, 
  clearCompileIssues, 
  getCompileIssueStats,
  exportCompileIssues 
} from '../utils/compileTimeLogger';
import { 
  getRuntimeStats, 
  exportRuntimeData 
} from '../utils/runtimeMonitor';

const LoggingDashboard = () => {
  const [logs, setLogs] = useState([]);
  const [compileIssues, setCompileIssues] = useState([]);
  const [runtimeStats, setRuntimeStats] = useState({});
  const [compileStats, setCompileStats] = useState({});
  const [selectedCategory, setSelectedCategory] = useState('ALL');
  const [selectedLevel, setSelectedLevel] = useState('ALL');
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Load all logging data
  const loadData = async () => {
    try {
      setIsLoading(true);
      
      const [allLogs, issues, runtime, compile] = await Promise.all([
        getLogs(),
        getCompileIssues(),
        getRuntimeStats(),
        getCompileIssueStats(),
      ]);
      
      setLogs(allLogs);
      setCompileIssues(issues);
      setRuntimeStats(runtime);
      setCompileStats(compile);
    } catch (error) {
      console.error('Error loading logging data:', error);
      Alert.alert('Error', 'Failed to load logging data');
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Filter logs based on selected category and level
  const getFilteredLogs = () => {
    let filtered = logs;
    
    if (selectedCategory !== 'ALL') {
      filtered = filtered.filter(log => log.category === selectedCategory);
    }
    
    if (selectedLevel !== 'ALL') {
      filtered = filtered.filter(log => log.level === selectedLevel);
    }
    
    return filtered.slice(0, 50); // Limit to 50 most recent
  };

  // Get log level color
  const getLogLevelColor = (level) => {
    switch (level) {
      case LOG_LEVELS.ERROR:
      case LOG_LEVELS.FATAL:
        return '#f44336';
      case LOG_LEVELS.WARN:
        return '#ff9800';
      case LOG_LEVELS.INFO:
        return '#2196f3';
      case LOG_LEVELS.DEBUG:
        return '#9e9e9e';
      case LOG_LEVELS.PERFORMANCE:
        return '#9c27b0';
      case LOG_LEVELS.SECURITY:
        return '#e91e63';
      default:
        return '#757575';
    }
  };

  // Export all logging data
  const handleExportAll = async () => {
    try {
      const [allLogs, compileData, runtimeData] = await Promise.all([
        exportLogs(),
        exportCompileIssues(),
        exportRuntimeData(),
      ]);
      
      const combinedReport = {
        timestamp: new Date().toISOString(),
        systemLogs: JSON.parse(allLogs),
        compileIssues: JSON.parse(compileData),
        runtimeMonitoring: JSON.parse(runtimeData),
        summary: {
          totalLogs: logs.length,
          errorCount: logs.filter(log => log.level === LOG_LEVELS.ERROR).length,
          warningCount: logs.filter(log => log.level === LOG_LEVELS.WARN).length,
          compileIssueCount: compileIssues.length,
          runtimeStats,
        },
      };
      
      await Share.share({
        message: 'Complete Logging Report for ZnüniZähler',
        title: 'Logging Report',
        url: `data:application/json;base64,${btoa(JSON.stringify(combinedReport, null, 2))}`,
      });
    } catch (error) {
      console.error('Error exporting logs:', error);
      Alert.alert('Error', 'Failed to export logging data');
    }
  };

  // Clear all logs
  const handleClearAll = () => {
    Alert.alert(
      'Clear All Logs',
      'Are you sure you want to clear all logging data? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearLogs();
              clearCompileIssues();
              await loadData();
              Alert.alert('Success', 'All logs cleared');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear logs');
            }
          },
        },
      ]
    );
  };

  // Render log item
  const renderLogItem = (log, index) => (
    <Card key={log.id || index} style={styles.logCard}>
      <Card.Content>
        <View style={styles.logHeader}>
          <Chip 
            mode="outlined" 
            style={[styles.levelChip, { borderColor: getLogLevelColor(log.level) }]}
            textStyle={{ color: getLogLevelColor(log.level) }}
          >
            {log.level}
          </Chip>
          <Text style={styles.timestamp}>
            {new Date(log.timestamp).toLocaleTimeString()}
          </Text>
        </View>
        
        <Text style={styles.logMessage}>{log.message}</Text>
        
        {log.category && (
          <Chip mode="outlined" style={styles.categoryChip}>
            {log.category}
          </Chip>
        )}
        
        {log.metadata && Object.keys(log.metadata).length > 0 && (
          <TouchableOpacity 
            style={styles.metadataButton}
            onPress={() => Alert.alert('Metadata', JSON.stringify(log.metadata, null, 2))}
          >
            <Text style={styles.metadataText}>View Metadata</Text>
          </TouchableOpacity>
        )}
      </Card.Content>
    </Card>
  );

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <Card style={styles.statCard}>
          <Card.Content>
            <Text style={styles.statTitle}>System Logs</Text>
            <Text style={styles.statValue}>{logs.length}</Text>
            <Text style={styles.statSubtitle}>
              {logs.filter(log => log.level === LOG_LEVELS.ERROR).length} errors
            </Text>
          </Card.Content>
        </Card>
        
        <Card style={styles.statCard}>
          <Card.Content>
            <Text style={styles.statTitle}>Compile Issues</Text>
            <Text style={styles.statValue}>{compileStats.total || 0}</Text>
            <Text style={styles.statSubtitle}>
              {compileStats.errors || 0} errors, {compileStats.warnings || 0} warnings
            </Text>
          </Card.Content>
        </Card>
        
        <Card style={styles.statCard}>
          <Card.Content>
            <Text style={styles.statTitle}>Runtime Errors</Text>
            <Text style={styles.statValue}>{runtimeStats.errorCount || 0}</Text>
            <Text style={styles.statSubtitle}>
              {runtimeStats.isMonitoring ? 'Monitoring' : 'Not monitoring'}
            </Text>
          </Card.Content>
        </Card>
      </View>

      {/* Memory Usage */}
      {runtimeStats.memoryUsage && (
        <Card style={styles.memoryCard}>
          <Card.Content>
            <Text style={styles.cardTitle}>Memory Usage</Text>
            <ProgressBar 
              progress={runtimeStats.memoryUsage.usagePercent / 100} 
              style={styles.progressBar}
              color={runtimeStats.memoryUsage.usagePercent > 80 ? '#f44336' : '#4caf50'}
            />
            <Text style={styles.memoryText}>
              {Math.round(runtimeStats.memoryUsage.usagePercent)}% 
              ({Math.round(runtimeStats.memoryUsage.used / 1024 / 1024)}MB / 
              {Math.round(runtimeStats.memoryUsage.limit / 1024 / 1024)}MB)
            </Text>
          </Card.Content>
        </Card>
      )}

      {/* Filter Controls */}
      <Card style={styles.filterCard}>
        <Card.Content>
          <Text style={styles.cardTitle}>Filters</Text>
          
          <Text style={styles.filterLabel}>Category:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterRow}>
              {['ALL', ...Object.values(LOG_CATEGORIES)].map(category => (
                <Chip
                  key={category}
                  mode={selectedCategory === category ? 'flat' : 'outlined'}
                  onPress={() => setSelectedCategory(category)}
                  style={styles.filterChip}
                >
                  {category}
                </Chip>
              ))}
            </View>
          </ScrollView>
          
          <Text style={styles.filterLabel}>Level:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterRow}>
              {['ALL', ...Object.values(LOG_LEVELS)].map(level => (
                <Chip
                  key={level}
                  mode={selectedLevel === level ? 'flat' : 'outlined'}
                  onPress={() => setSelectedLevel(level)}
                  style={styles.filterChip}
                >
                  {level}
                </Chip>
              ))}
            </View>
          </ScrollView>
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <Button 
          mode="contained" 
          onPress={handleExportAll}
          icon="download"
          style={styles.actionButton}
        >
          Export All
        </Button>
        <Button 
          mode="outlined" 
          onPress={handleClearAll}
          icon="delete"
          style={styles.actionButton}
        >
          Clear All
        </Button>
      </View>

      {/* Logs List */}
      <Card style={styles.logsCard}>
        <Card.Content>
          <Text style={styles.cardTitle}>
            Recent Logs ({getFilteredLogs().length})
          </Text>
          <Divider style={styles.divider} />
          
          {getFilteredLogs().map((log, index) => renderLogItem(log, index))}
          
          {getFilteredLogs().length === 0 && (
            <Text style={styles.noLogsText}>No logs match the current filters</Text>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
  },
  statTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statSubtitle: {
    fontSize: 10,
    color: '#999',
  },
  memoryCard: {
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    marginBottom: 8,
  },
  memoryText: {
    fontSize: 12,
    color: '#666',
  },
  filterCard: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  filterRow: {
    flexDirection: 'row',
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  logsCard: {
    marginBottom: 16,
  },
  divider: {
    marginBottom: 16,
  },
  logCard: {
    marginBottom: 8,
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  levelChip: {
    height: 24,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  logMessage: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  categoryChip: {
    alignSelf: 'flex-start',
    height: 20,
    marginBottom: 4,
  },
  metadataButton: {
    alignSelf: 'flex-start',
  },
  metadataText: {
    fontSize: 12,
    color: '#2196f3',
    textDecorationLine: 'underline',
  },
  noLogsText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
  },
});

export default LoggingDashboard;
