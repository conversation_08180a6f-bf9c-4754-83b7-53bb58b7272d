/**
 * Canadian Nutrient File (CNF) API Integration
 * 
 * This module provides API access to the Canadian Nutrient File database
 * through the Government of Canada's Open Data API
 */

import axios from 'axios';

// CNF API base URL (Government of Canada Open Data)
const CNF_API_BASE_URL = 'https://open.canada.ca/data/api/3/action';

/**
 * Search for foods in the CNF database
 * @param {string} query - Search query
 * @param {number} limit - Maximum number of results (default: 10)
 * @returns {Promise} - Promise with search results
 */
export const searchCNFFoods = async (query, limit = 10) => {
  try {
    // Use the datastore_search API endpoint
    const response = await axios.get(`${CNF_API_BASE_URL}/datastore_search`, {
      params: {
        resource_id: '90a31d6a-9131-4f31-a156-cd1f3b2717fe', // CNF resource ID
        q: query,
        limit: limit
      }
    });
    
    if (response.data.success && response.data.result.records) {
      return {
        success: true,
        data: response.data.result.records,
        total: response.data.result.total
      };
    } else {
      return {
        success: false,
        error: 'No foods found'
      };
    }
  } catch (error) {
    console.error('Error searching CNF foods:', error);
    return {
      success: false,
      error: error.message || 'Failed to search CNF database'
    };
  }
};

/**
 * Get food details by CNF food ID
 * @param {string} foodId - CNF food ID
 * @returns {Promise} - Promise with food details
 */
export const getCNFFoodById = async (foodId) => {
  try {
    const response = await axios.get(`${CNF_API_BASE_URL}/datastore_search`, {
      params: {
        resource_id: '90a31d6a-9131-4f31-a156-cd1f3b2717fe',
        filters: JSON.stringify({ 'FoodID': foodId })
      }
    });
    
    if (response.data.success && response.data.result.records.length > 0) {
      return {
        success: true,
        data: response.data.result.records[0]
      };
    } else {
      return {
        success: false,
        error: 'Food not found'
      };
    }
  } catch (error) {
    console.error('Error fetching CNF food details:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch food details'
    };
  }
};

/**
 * Get nutrient data for a specific food
 * @param {string} foodId - CNF food ID
 * @returns {Promise} - Promise with nutrient data
 */
export const getCNFNutrients = async (foodId) => {
  try {
    // This would require access to the nutrient amount dataset
    // For now, we'll return a placeholder response
    return {
      success: true,
      data: [],
      message: 'Nutrient data requires local database import'
    };
  } catch (error) {
    console.error('Error fetching CNF nutrients:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch nutrient data'
    };
  }
};

/**
 * Get food groups from CNF
 * @returns {Promise} - Promise with food groups
 */
export const getCNFFoodGroups = async () => {
  try {
    // This would require access to the food group dataset
    const foodGroups = [
      { id: 1, name: 'Dairy products and substitutes' },
      { id: 2, name: 'Fats and oils' },
      { id: 3, name: 'Marine and fresh water animals' },
      { id: 4, name: 'Eggs' },
      { id: 5, name: 'Game meats' },
      { id: 6, name: 'Poultry' },
      { id: 7, name: 'Red meats' },
      { id: 8, name: 'Vegetables' },
      { id: 9, name: 'Fruits' },
      { id: 10, name: 'Grain products' },
      { id: 11, name: 'Nuts and seeds' },
      { id: 12, name: 'Legumes' },
      { id: 13, name: 'Beverages' },
      { id: 14, name: 'Miscellaneous' },
      { id: 15, name: 'Baked products' },
      { id: 16, name: 'Sweets' },
      { id: 17, name: 'Soups' },
      { id: 18, name: 'Combination dishes' },
      { id: 19, name: 'Snack foods' }
    ];
    
    return {
      success: true,
      data: foodGroups
    };
  } catch (error) {
    console.error('Error fetching CNF food groups:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch food groups'
    };
  }
};

/**
 * Convert CNF food data to our internal format
 * @param {Object} cnfFood - CNF food data
 * @returns {Object} - Formatted food data
 */
export const formatCNFFood = (cnfFood) => {
  return {
    name: cnfFood.FoodDescription || cnfFood.food_description || '',
    description: cnfFood.FoodDescription || cnfFood.food_description || '',
    brand_name: '',
    serving_size: 100, // CNF data is per 100g
    serving_unit: 'g',
    barcode: '',
    is_custom: false,
    source: 'CNF',
    source_id: cnfFood.FoodID || cnfFood.food_id || '',
    category: cnfFood.FoodGroupName || cnfFood.food_group_name || 'Unknown',
    food_group_id: cnfFood.FoodGroupID || cnfFood.food_group_id || null,
  };
};

/**
 * Search foods by food group
 * @param {number} foodGroupId - Food group ID
 * @param {number} limit - Maximum number of results
 * @returns {Promise} - Promise with foods in the group
 */
export const searchCNFFoodsByGroup = async (foodGroupId, limit = 20) => {
  try {
    const response = await axios.get(`${CNF_API_BASE_URL}/datastore_search`, {
      params: {
        resource_id: '90a31d6a-9131-4f31-a156-cd1f3b2717fe',
        filters: JSON.stringify({ 'FoodGroupID': foodGroupId }),
        limit: limit
      }
    });
    
    if (response.data.success && response.data.result.records) {
      return {
        success: true,
        data: response.data.result.records.map(formatCNFFood),
        total: response.data.result.total
      };
    } else {
      return {
        success: false,
        error: 'No foods found in this group'
      };
    }
  } catch (error) {
    console.error('Error searching CNF foods by group:', error);
    return {
      success: false,
      error: error.message || 'Failed to search foods by group'
    };
  }
};

/**
 * Get popular Canadian foods
 * @param {number} limit - Maximum number of results
 * @returns {Promise} - Promise with popular foods
 */
export const getPopularCNFFoods = async (limit = 20) => {
  try {
    // Get a selection of common Canadian foods
    const popularFoodIds = [
      '142', // Milk, 2% M.F.
      '2331', // Bread, white
      '2267', // Rice, white, long grain
      '1057', // Chicken, broiler, breast
      '1129', // Beef, ground, lean
      '2205', // Potato, flesh and skin
      '2206', // Carrot, raw
      '2207', // Broccoli, raw
      '2208', // Apple, raw, with skin
      '2209', // Banana, raw
      '2210', // Orange, raw
      '2211', // Tomato, red, ripe, raw
      '2212', // Onion, raw
      '2213', // Lettuce, iceberg, raw
      '2214', // Spinach, raw
    ];
    
    const foods = [];
    for (const foodId of popularFoodIds.slice(0, limit)) {
      const result = await getCNFFoodById(foodId);
      if (result.success) {
        foods.push(formatCNFFood(result.data));
      }
    }
    
    return {
      success: true,
      data: foods
    };
  } catch (error) {
    console.error('Error fetching popular CNF foods:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch popular foods'
    };
  }
};

export default {
  searchCNFFoods,
  getCNFFoodById,
  getCNFNutrients,
  getCNFFoodGroups,
  formatCNFFood,
  searchCNFFoodsByGroup,
  getPopularCNFFoods
};
