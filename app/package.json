{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "expo": {"entryPoint": "./test.js"}, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/ngrok": "^4.1.3", "@expo/webpack-config": "^19.0.1", "@google-cloud/vision": "^4.0.0", "@react-native-async-storage/async-storage": "~1.21.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "axios": "^1.6.2", "expo": "~50.0.21", "expo-barcode-scanner": "~12.6.0", "expo-camera": "~13.4.4", "expo-constants": "~15.4.5", "expo-device": "~5.9.3", "expo-image-manipulator": "~11.8.0", "expo-sqlite": "^14.0.6", "expo-status-bar": "~2.2.3", "expo-updates": "^0.24.13", "react": "18.3.1", "react-dom": "^18.3.1", "react-native": "0.74.5", "react-native-chart-kit": "^6.12.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.14.0", "react-native-image-crop-picker": "^0.40.3", "react-native-paper": "^5.11.4", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "13.9.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}