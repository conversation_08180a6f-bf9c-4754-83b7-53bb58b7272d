const createExpoWebpackConfigAsync = require('@expo/webpack-config');

module.exports = async function(env, argv) {
  const config = await createExpoWebpackConfigAsync(
    {
      ...env,
      babel: {
        dangerouslyAddModulePathsToTranspile: ['@expo/vector-icons']
      }
    },
    argv
  );
  
  // Customize the config before returning it
  if (config.entry) {
    // Ensure entry is an array
    if (!Array.isArray(config.entry)) {
      config.entry = [config.entry];
    }
  } else {
    // Set a default entry if none exists
    config.entry = ['./index.js'];
  }
  
  return config;
};
