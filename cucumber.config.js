/**
 * Cucumber Configuration for BDD Tests
 * Integrates with <PERSON><PERSON> for E2E testing
 */

const common = {
  requireModule: ['ts-node/register'],
  require: [
    'tests/e2e/step_definitions/**/*.js',
    'tests/e2e/support/**/*.js'
  ],
  format: [
    'progress-bar',
    'json:tests/e2e/reports/cucumber_report.json',
    'html:tests/e2e/reports/cucumber_report.html',
    'rerun:tests/e2e/reports/rerun.txt'
  ],
  formatOptions: {
    snippetInterface: 'async-await'
  },
  publishQuiet: true,
  dryRun: false,
  failFast: false,
  strict: true,
  worldParameters: {
    baseUrl: 'http://localhost:19006',
    timeout: 30000,
    headless: process.env.HEADLESS !== 'false',
    slowMo: process.env.SLOW_MO ? parseInt(process.env.SLOW_MO) : 0,
    browser: process.env.BROWSER || 'chromium'
  }
};

module.exports = {
  default: {
    ...common,
    paths: ['tests/e2e/features/**/*.feature'],
    tags: 'not @skip and not @wip'
  },
  
  // Profile for running only smoke tests
  smoke: {
    ...common,
    paths: ['tests/e2e/features/**/*.feature'],
    tags: '@smoke'
  },
  
  // Profile for running regression tests
  regression: {
    ...common,
    paths: ['tests/e2e/features/**/*.feature'],
    tags: '@regression'
  },
  
  // Profile for running mobile-specific tests
  mobile: {
    ...common,
    paths: ['tests/e2e/features/**/*.feature'],
    tags: '@mobile'
  },
  
  // Profile for running web-specific tests
  web: {
    ...common,
    paths: ['tests/e2e/features/**/*.feature'],
    tags: '@web'
  }
};
