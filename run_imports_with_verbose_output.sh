#!/bin/bash
# Run imports with verbose output
# This script runs the Python scripts to import USDA and FoodB data with verbose output

# Ensure we're in the database directory
cd "$(dirname "$0")"

# Make sure the Python scripts are executable
chmod +x import_full_usda_data.py
chmod +x import_full_foodb_data.py

# Create the output directory
mkdir -p ../app/src/assets/databases

echo "Starting USDA data import..."
python3 -u import_full_usda_data.py

echo "Starting FoodB data import..."
python3 -u import_full_foodb_data.py

echo "All imports completed!"
echo "Database files are located in ../app/src/assets/databases/"
echo "- USDA database: ../app/src/assets/databases/usda_foods.sqlite"
echo "- FoodB database: ../app/src/assets/databases/foodb_foods.sqlite"

# Check the sizes of the database files
echo "Database file sizes:"
ls -lh ../app/src/assets/databases/

# Count the number of foods and nutrients in each database
echo "Counting records in USDA database..."
sqlite3 ../app/src/assets/databases/usda_foods.sqlite "SELECT COUNT(*) AS 'Number of Foods' FROM foods;"
sqlite3 ../app/src/assets/databases/usda_foods.sqlite "SELECT COUNT(*) AS 'Number of Nutrients' FROM nutrients;"

echo "Counting records in FoodB database..."
sqlite3 ../app/src/assets/databases/foodb_foods.sqlite "SELECT COUNT(*) AS 'Number of Foods' FROM foods;"
sqlite3 ../app/src/assets/databases/foodb_foods.sqlite "SELECT COUNT(*) AS 'Number of Nutrients' FROM nutrients;"
sqlite3 ../app/src/assets/databases/foodb_foods.sqlite "SELECT COUNT(*) AS 'Number of Compounds' FROM compounds;"
