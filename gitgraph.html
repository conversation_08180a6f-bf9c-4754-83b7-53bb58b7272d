<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git Graph Visualization - <PERSON><PERSON><PERSON>-<PERSON>hler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .gitgraph-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        canvas {
            min-width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Git Graph Visualization - Z<PERSON>eni-Z<PERSON>hler</h1>
        <div class="gitgraph-container">
            <canvas id="gitgraph"></canvas>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@gitgraph/js"></script>
    <script>
        // Get the canvas element
        const graphContainer = document.getElementById("gitgraph");

        // Create a GitGraph instance with custom template
        const gitgraph = GitgraphJS.createGitgraph(graphContainer, {
            template: {
                colors: ["#F44D27", "#4584B6", "#2B9348", "#7B3FE4", "#FFA500"],
                branch: {
                    lineWidth: 4,
                    spacing: 50,
                    label: {
                        font: "normal 12pt Arial",
                        display: true
                    },
                },
                commit: {
                    spacing: 60,
                    dot: {
                        size: 10,
                    },
                    message: {
                        display: true,
                        font: "normal 12pt Arial",
                    },
                },
            },
            orientation: "vertical",
            mode: "compact"
        });

        // Create branches based on actual commit history
        const main = gitgraph.branch("main");

        // Initial commits
        main.commit({
            subject: "Initial commit",
            hash: "7fd63ea",
            author: "forkrul",
            date: "5 days ago"
        });

        main.commit({
            subject: "Pending changes exported from your codespace",
            hash: "aebf513",
            author: "forkrul",
            date: "5 days ago"
        });

        main.commit({
            subject: "Initial commit: Project setup with React Native and Expo",
            hash: "11cf43d",
            author: "forkrul",
            date: "5 days ago"
        });

        main.commit({
            subject: "Initial commit: Project setup with React Native and Expo",
            hash: "b6feca3",
            author: "forkrul",
            date: "5 days ago"
        });

        main.commit({
            subject: "Implement dark mode theme management system with centralized theme provider",
            hash: "e9d30ca",
            author: "forkrul",
            date: "4 days ago"
        });

        main.commit({
            subject: "Add ERD diagrams and database schema for nutrition tracking with dark mode support",
            hash: "ccdc0d3",
            author: "forkrul",
            date: "4 days ago"
        });

        // Create feature branch
        const featureUSDA = gitgraph.branch("feature/USDA-food-import");

        // This is where we are now - working on the feature branch
        featureUSDA.commit({
            subject: "Day end",
            hash: "fc20445",
            author: "forkrul",
            date: "3 days ago",
            style: {
                dot: {
                    color: "red",
                    size: 12,
                    strokeWidth: 3,
                    stroke: "black"
                }
            }
        });

        // Future merge (not yet done)
        /*
        main.merge({
            branch: featureUSDA,
            subject: "Merge USDA Food Import Feature",
            commitOptions: {
                style: {
                    message: {
                        color: "blue",
                        font: "bold 12pt Arial"
                    }
                }
            }
        });
        */
    </script>
</body>
</html>
