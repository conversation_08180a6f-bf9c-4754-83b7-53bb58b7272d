#!/usr/bin/env python3
"""
Generate Sample Data for USDA and FoodB

This script generates sample CSV and JSON files for the USDA and FoodB datasets
to be used for testing the import scripts.
"""

import os
import json
import csv
from pathlib import Path

# Configuration
USDA_DIR = Path("./usda_sr_legacy_csv")
FOODB_DIR = Path("./foodb_json")

# Ensure directories exist
os.makedirs(USDA_DIR, exist_ok=True)
os.makedirs(FOODB_DIR, exist_ok=True)

# Generate USDA sample data
def generate_usda_sample_data():
    print("Generating USDA sample data...")
    
    # Generate food_group.csv
    with open(USDA_DIR / "food_group.csv", 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['id', 'code', 'description'])
        writer.writerow(['1', 'FG1', 'Fruits'])
        writer.writerow(['2', 'FG2', 'Vegetables'])
        writer.writerow(['3', 'FG3', 'Grains'])
        writer.writerow(['4', 'FG4', 'Protein Foods'])
        writer.writerow(['5', 'FG5', 'Dairy'])
    
    # Generate food.csv
    with open(USDA_DIR / "food.csv", 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['fdc_id', 'food_group_id', 'description', 'publication_date'])
        writer.writerow(['1', '1', 'Apple', '2018-04-01'])
        writer.writerow(['2', '1', 'Banana', '2018-04-01'])
        writer.writerow(['3', '1', 'Orange', '2018-04-01'])
        writer.writerow(['4', '2', 'Broccoli', '2018-04-01'])
        writer.writerow(['5', '2', 'Spinach', '2018-04-01'])
        writer.writerow(['6', '3', 'Brown Rice', '2018-04-01'])
        writer.writerow(['7', '3', 'Whole Wheat Bread', '2018-04-01'])
        writer.writerow(['8', '4', 'Chicken Breast', '2018-04-01'])
        writer.writerow(['9', '4', 'Salmon', '2018-04-01'])
        writer.writerow(['10', '5', 'Milk', '2018-04-01'])
    
    # Generate nutrient.csv
    with open(USDA_DIR / "nutrient.csv", 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['id', 'name', 'unit_name'])
        writer.writerow(['203', 'Protein', 'g'])
        writer.writerow(['204', 'Total Fat', 'g'])
        writer.writerow(['205', 'Carbohydrates', 'g'])
        writer.writerow(['208', 'Energy', 'kcal'])
        writer.writerow(['269', 'Sugars', 'g'])
        writer.writerow(['291', 'Fiber', 'g'])
        writer.writerow(['301', 'Calcium', 'mg'])
        writer.writerow(['303', 'Iron', 'mg'])
        writer.writerow(['401', 'Vitamin C', 'mg'])
    
    # Generate food_nutrient.csv
    with open(USDA_DIR / "food_nutrient.csv", 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['id', 'fdc_id', 'nutrient_id', 'amount'])
        
        # Apple nutrients
        writer.writerow(['1', '1', '203', '0.3'])  # Protein
        writer.writerow(['2', '1', '204', '0.2'])  # Fat
        writer.writerow(['3', '1', '205', '14'])   # Carbs
        writer.writerow(['4', '1', '208', '52'])   # Energy
        writer.writerow(['5', '1', '269', '10.3']) # Sugars
        writer.writerow(['6', '1', '291', '2.4'])  # Fiber
        
        # Banana nutrients
        writer.writerow(['7', '2', '203', '1.1'])  # Protein
        writer.writerow(['8', '2', '204', '0.3'])  # Fat
        writer.writerow(['9', '2', '205', '23'])   # Carbs
        writer.writerow(['10', '2', '208', '89'])  # Energy
        writer.writerow(['11', '2', '269', '12.2']) # Sugars
        writer.writerow(['12', '2', '291', '2.6']) # Fiber
        
        # Add nutrients for other foods...
        for food_id in range(3, 11):
            for nutrient_id in ['203', '204', '205', '208']:
                writer.writerow([str(12 + (food_id-3)*4 + int(nutrient_id) % 4), str(food_id), nutrient_id, str(float(food_id) * 2)])
    
    print("USDA sample data generated in", USDA_DIR)

# Generate FoodB sample data
def generate_foodb_sample_data():
    print("Generating FoodB sample data...")
    
    # Generate Food.json
    foods = []
    for i in range(1, 11):
        food = {
            "id": i,
            "name": f"FoodB Food {i}",
            "description": f"Description for FoodB Food {i}",
            "scientific_name": f"Scientific Name {i}",
            "nutrients": [
                {"nutrient_id": 1, "amount": i * 2},
                {"nutrient_id": 2, "amount": i * 1.5},
                {"nutrient_id": 3, "amount": i * 3}
            ],
            "compounds": [
                {"compound_id": 1, "amount": i * 0.1, "unit": "mg"},
                {"compound_id": 2, "amount": i * 0.2, "unit": "mg"}
            ]
        }
        foods.append(food)
    
    with open(FOODB_DIR / "Food.json", 'w') as f:
        json.dump(foods, f, indent=2)
    
    # Generate Nutrient.json
    nutrients = [
        {"id": 1, "name": "Protein", "unit": "g"},
        {"id": 2, "name": "Total lipid (fat)", "unit": "g"},
        {"id": 3, "name": "Carbohydrate, by difference", "unit": "g"},
        {"id": 4, "name": "Energy", "unit": "kcal"},
        {"id": 5, "name": "Sugars, total", "unit": "g"},
        {"id": 6, "name": "Fiber, total dietary", "unit": "g"},
        {"id": 7, "name": "Calcium, Ca", "unit": "mg"},
        {"id": 8, "name": "Iron, Fe", "unit": "mg"},
        {"id": 9, "name": "Vitamin C, total ascorbic acid", "unit": "mg"}
    ]
    
    with open(FOODB_DIR / "Nutrient.json", 'w') as f:
        json.dump(nutrients, f, indent=2)
    
    # Generate Compound.json
    compounds = [
        {"id": 1, "name": "Caffeine", "description": "A stimulant compound"},
        {"id": 2, "name": "Milk protein", "description": "A protein found in milk"},
        {"id": 3, "name": "Gluten", "description": "A protein found in wheat"},
        {"id": 4, "name": "Vitamin E", "description": "An antioxidant vitamin"},
        {"id": 5, "name": "Omega-3 fatty acid", "description": "A healthy fat"}
    ]
    
    with open(FOODB_DIR / "Compound.json", 'w') as f:
        json.dump(compounds, f, indent=2)
    
    print("FoodB sample data generated in", FOODB_DIR)

# Main function
def main():
    generate_usda_sample_data()
    generate_foodb_sample_data()
    print("Sample data generation complete!")

if __name__ == "__main__":
    main()
