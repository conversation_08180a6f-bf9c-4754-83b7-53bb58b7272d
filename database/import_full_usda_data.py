#!/usr/bin/env python3
"""
USDA Full Data Import Script

This script downloads the complete USDA SR Legacy dataset,
processes it, and imports it into our improved SQLite database schema.
"""

import os
import sys
import csv
import json
import sqlite3
import urllib.request
import zipfile
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import time

# Configuration
OUTPUT_DIR = Path("../app/src/assets/databases")
USDA_DB_PATH = OUTPUT_DIR / "usda_foods.sqlite"
TEMP_DIR = Path("./temp_usda")
SCHEMA_PATH = Path("improved_food_database_schema.sql")

# USDA dataset URL - SR Legacy dataset from April 2018
USDA_DATASET_URL = "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_sr_legacy_food_csv_2018-04.zip"

# Nutrient mapping - maps USDA nutrient IDs to our internal types
NUTRIENT_TYPE_MAPPING = {
    # Macronutrients
    "203": 1,  # Protein
    "204": 1,  # Total Fat
    "205": 1,  # Carbohydrates
    "208": 1,  # Energy
    "269": 1,  # Sugars
    "291": 1,  # Fiber

    # Vitamins
    "301": 2,  # Calcium
    "401": 2,  # Vitamin C
    "404": 2,  # Thiamin
    "405": 2,  # Riboflavin
    "406": 2,  # Niacin
    "415": 2,  # Vitamin B-6
    "418": 2,  # Vitamin B-12
    "320": 2,  # Vitamin A
    "323": 2,  # Vitamin E
    "328": 2,  # Vitamin D
    "417": 2,  # Folate
    "430": 2,  # Vitamin K

    # Minerals
    "303": 3,  # Iron
    "304": 3,  # Magnesium
    "305": 3,  # Phosphorus
    "306": 3,  # Potassium
    "307": 3,  # Sodium
    "309": 3,  # Zinc
    "312": 3,  # Copper
    "315": 3,  # Manganese
    "317": 3,  # Selenium

    # Other
    "601": 4,  # Cholesterol
    "605": 4,  # Trans Fat
    "606": 4,  # Saturated Fat
    "645": 4,  # Monounsaturated Fat
    "646": 4,  # Polyunsaturated Fat
}

# Common allergens to look for in ingredients
COMMON_ALLERGENS = [
    {"name": "Milk", "keywords": ["milk", "dairy", "lactose", "whey", "casein", "butter", "cream", "cheese", "yogurt"]},
    {"name": "Eggs", "keywords": ["egg", "albumin", "lysozyme", "globulin", "ovomucin", "ovalbumin", "ovotransferrin"]},
    {"name": "Fish", "keywords": ["fish", "cod", "salmon", "tuna", "tilapia", "halibut", "anchovy", "bass"]},
    {"name": "Shellfish", "keywords": ["shellfish", "crab", "lobster", "shrimp", "prawn", "crayfish", "clam", "mussel", "oyster", "scallop"]},
    {"name": "Tree Nuts", "keywords": ["almond", "hazelnut", "walnut", "cashew", "pecan", "brazil nut", "pistachio", "macadamia"]},
    {"name": "Peanuts", "keywords": ["peanut", "arachis", "groundnut"]},
    {"name": "Wheat", "keywords": ["wheat", "flour", "bread", "bran", "bulgur", "durum", "gluten", "semolina", "spelt"]},
    {"name": "Soybeans", "keywords": ["soy", "soya", "tofu", "edamame", "miso", "tempeh", "textured vegetable protein", "tvp"]},
    {"name": "Sesame", "keywords": ["sesame", "tahini", "sesamol", "gingelly"]},
    {"name": "Sulfites", "keywords": ["sulfite", "sulphite", "sulfur dioxide", "so2", "e220", "e221", "e222", "e223", "e224", "e225", "e226", "e227", "e228"]},
]

def setup_database():
    """Set up the database with our improved schema."""
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Delete existing database file if it exists
    if os.path.exists(USDA_DB_PATH):
        os.remove(USDA_DB_PATH)

    # Create new database with our schema
    conn = sqlite3.connect(USDA_DB_PATH)

    with open(SCHEMA_PATH, 'r') as f:
        schema_sql = f.read()
        conn.executescript(schema_sql)

    # Insert nutrient types if not already in schema
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM nutrient_types")
    if cursor.fetchone()[0] == 0:
        cursor.executescript("""
            INSERT INTO nutrient_types (id, name, description) VALUES
            (1, 'macro', 'Macronutrients like protein, fat, carbohydrates'),
            (2, 'vitamin', 'Vitamins like A, C, D, etc.'),
            (3, 'mineral', 'Minerals like calcium, iron, etc.'),
            (4, 'other', 'Other nutritional components');
        """)

    conn.commit()
    return conn

def download_dataset(url, output_dir):
    """Download the USDA dataset with progress reporting."""
    os.makedirs(output_dir, exist_ok=True)
    zip_path = os.path.join(output_dir, "usda_dataset.zip")

    print(f"Downloading USDA dataset from {url}...")

    # Define a progress callback
    def report_progress(block_num, block_size, total_size):
        downloaded = block_num * block_size
        percent = min(100, downloaded * 100 / total_size)
        if block_num % 100 == 0:  # Update every 100 blocks to avoid too much output
            print(f"Download progress: {percent:.1f}% ({downloaded / (1024*1024):.1f} MB / {total_size / (1024*1024):.1f} MB)", end='\r')

    # Download with progress reporting
    urllib.request.urlretrieve(url, zip_path, reporthook=report_progress)
    print(f"\nDataset downloaded to {zip_path}")

    return zip_path

def extract_dataset(zip_path, output_dir):
    """Extract the USDA dataset with progress reporting."""
    os.makedirs(output_dir, exist_ok=True)

    print(f"Extracting dataset to {output_dir}...")
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        # Get total number of files
        total_files = len(zip_ref.namelist())
        print(f"Total files to extract: {total_files}")

        # Extract with progress reporting
        for i, file in enumerate(zip_ref.namelist()):
            if i % 10 == 0 or i == total_files - 1:  # Update every 10 files or on the last file
                percent = (i + 1) * 100 / total_files
                print(f"Extraction progress: {percent:.1f}% ({i+1}/{total_files})", end='\r')
            zip_ref.extract(file, output_dir)

    print("\nDataset extracted")

    # Count files by type
    csv_count = 0
    json_count = 0
    other_count = 0

    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.endswith('.csv'):
                csv_count += 1
            elif file.endswith('.json'):
                json_count += 1
            else:
                other_count += 1

    print(f"Extracted file summary: {csv_count} CSV files, {json_count} JSON files, {other_count} other files")

def find_csv_files(directory):
    """Find all CSV files in the directory structure."""
    csv_files = {}
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.csv'):
                base_name = os.path.splitext(file)[0].lower()
                csv_files[base_name] = os.path.join(root, file)
    return csv_files

def import_food_categories(conn, csv_files):
    """Import food categories from the USDA dataset."""
    cursor = conn.cursor()

    # Check if food_group.csv exists
    if 'food_group' not in csv_files:
        print("Warning: food_group.csv not found, skipping category import")
        return

    print("Importing food categories...")

    # Create a top-level category for USDA
    cursor.execute("""
        INSERT INTO food_categories (name, description, parent_id, source, source_id)
        VALUES (?, ?, NULL, ?, ?)
    """, ("USDA Foods", "Foods from USDA SR Legacy database", "USDA", "usda_main"))

    usda_parent_id = cursor.lastrowid

    # Read and import food groups as categories
    with open(csv_files['food_group'], 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                cursor.execute("""
                    INSERT INTO food_categories
                    (name, description, parent_id, source, source_id)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    row.get('description', ''),
                    row.get('description', ''),
                    usda_parent_id,
                    'USDA',
                    f"usda_fg_{row.get('id', '')}"
                ))
            except Exception as e:
                print(f"Error importing food category {row.get('id', '')}: {e}")

    conn.commit()
    print(f"Imported {cursor.rowcount} food categories")

def import_foods(conn, csv_files):
    """Import foods from the USDA dataset with detailed progress reporting."""
    cursor = conn.cursor()

    # Check if food.csv exists
    if 'food' not in csv_files:
        print("Warning: food.csv not found, skipping food import")
        return

    print("Importing foods...")

    # Create a mapping of food group IDs to category IDs
    food_group_map = {}
    cursor.execute("SELECT source_id, id FROM food_categories WHERE source = 'USDA'")
    for row in cursor.fetchall():
        source_id = row[0]
        if source_id.startswith('usda_fg_'):
            food_group_id = source_id[8:]  # Remove 'usda_fg_' prefix
            food_group_map[food_group_id] = row[1]

    # First count total rows to calculate progress
    total_foods = 0
    with open(csv_files['food'], 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for _ in reader:
            total_foods += 1

    print(f"Found {total_foods} foods to import")

    # Read and import foods
    food_count = 0
    start_time = time.time()
    last_update_time = start_time

    with open(csv_files['food'], 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                # Get the category ID from the food group ID
                category_id = None
                if 'food_group_id' in row and row['food_group_id'] in food_group_map:
                    category_id = food_group_map[row['food_group_id']]

                cursor.execute("""
                    INSERT INTO foods
                    (name, description, scientific_name, serving_size, serving_unit,
                     category_id, data_type, publication_date, source, source_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row.get('description', ''),
                    row.get('description', ''),
                    None,  # scientific_name
                    100,  # serving_size (default to 100g)
                    'g',  # serving_unit
                    category_id,
                    'SR Legacy',
                    row.get('publication_date', None),
                    'USDA',
                    f"usda_{row.get('id', '')}"
                ))

                food_count += 1

                # Update progress every 100 foods or every 5 seconds
                current_time = time.time()
                if food_count % 100 == 0 or current_time - last_update_time >= 5:
                    conn.commit()
                    percent = food_count * 100 / total_foods
                    elapsed = current_time - start_time
                    foods_per_sec = food_count / elapsed if elapsed > 0 else 0
                    eta = (total_foods - food_count) / foods_per_sec if foods_per_sec > 0 else 0

                    print(f"Importing foods: {percent:.1f}% ({food_count}/{total_foods}) - {foods_per_sec:.1f} foods/sec - ETA: {eta:.0f} sec", end='\r')
                    last_update_time = current_time

            except Exception as e:
                print(f"\nError importing food {row.get('id', '')}: {e}")

    conn.commit()
    elapsed = time.time() - start_time
    print(f"\nImported {food_count} foods in {elapsed:.1f} seconds ({food_count/elapsed:.1f} foods/sec)")

def import_nutrients(conn, csv_files):
    """Import nutrients from the USDA dataset with detailed progress reporting."""
    cursor = conn.cursor()

    # Check if required files exist
    required_files = ['food_nutrient', 'nutrient']
    for file in required_files:
        if file not in csv_files:
            print(f"Warning: {file}.csv not found, skipping nutrient import")
            return

    print("Importing nutrients...")

    # Create a mapping of nutrient IDs to names and units
    print("Building nutrient mapping...")
    nutrient_map = {}
    with open(csv_files['nutrient'], 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            nutrient_map[row.get('id', '')] = {
                'name': row.get('name', ''),
                'unit': row.get('unit_name', '')
            }

    print(f"Found {len(nutrient_map)} unique nutrients")

    # Create a mapping of USDA food IDs to our food IDs
    print("Building food ID mapping...")
    food_id_map = {}
    cursor.execute("SELECT source_id, id FROM foods WHERE source = 'USDA'")
    for row in cursor.fetchall():
        source_id = row[0]
        if source_id.startswith('usda_'):
            usda_id = source_id[5:]  # Remove 'usda_' prefix
            food_id_map[usda_id] = row[1]

    print(f"Found {len(food_id_map)} foods in database")

    # First count total rows to calculate progress
    total_nutrients = 0
    with open(csv_files['food_nutrient'], 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for _ in reader:
            total_nutrients += 1

    print(f"Found {total_nutrients} nutrient values to import")

    # Read and import food nutrients
    nutrient_count = 0
    skipped_count = 0
    start_time = time.time()
    last_update_time = start_time

    with open(csv_files['food_nutrient'], 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                # Get the food ID from our mapping
                food_id = None
                if 'food_id' in row and row['food_id'] in food_id_map:
                    food_id = food_id_map[row['food_id']]
                else:
                    skipped_count += 1
                    continue  # Skip if food not found

                # Get the nutrient info from our mapping
                nutrient_id = row.get('nutrient_id', '')
                if nutrient_id not in nutrient_map:
                    skipped_count += 1
                    continue  # Skip if nutrient not found

                nutrient_info = nutrient_map[nutrient_id]

                # Get the nutrient type ID
                type_id = 4  # Default to 'other'
                if nutrient_id in NUTRIENT_TYPE_MAPPING:
                    type_id = NUTRIENT_TYPE_MAPPING[nutrient_id]

                cursor.execute("""
                    INSERT INTO nutrients
                    (food_id, name, amount, unit, type_id, source_id, derivation_code)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    food_id,
                    nutrient_info['name'],
                    float(row.get('amount', 0)) if row.get('amount') else 0,
                    nutrient_info['unit'],
                    type_id,
                    f"usda_nutr_{nutrient_id}",
                    row.get('derivation_id', None)
                ))

                nutrient_count += 1

                # Update progress every 1000 nutrients or every 5 seconds
                current_time = time.time()
                if nutrient_count % 1000 == 0 or current_time - last_update_time >= 5:
                    conn.commit()
                    percent = (nutrient_count + skipped_count) * 100 / total_nutrients
                    elapsed = current_time - start_time
                    nutrients_per_sec = nutrient_count / elapsed if elapsed > 0 else 0
                    eta = (total_nutrients - (nutrient_count + skipped_count)) / nutrients_per_sec if nutrients_per_sec > 0 else 0

                    print(f"Importing nutrients: {percent:.1f}% ({nutrient_count + skipped_count}/{total_nutrients}) - {nutrients_per_sec:.1f} nutrients/sec - ETA: {eta:.0f} sec - Skipped: {skipped_count}", end='\r')
                    last_update_time = current_time

            except Exception as e:
                print(f"\nError importing nutrient {row.get('id', '')}: {e}")

    conn.commit()
    elapsed = time.time() - start_time
    print(f"\nImported {nutrient_count} nutrients in {elapsed:.1f} seconds ({nutrient_count/elapsed:.1f} nutrients/sec)")
    print(f"Skipped {skipped_count} nutrients (food not found or nutrient not mapped)")

def import_metadata(conn, version, record_count):
    """Import database metadata."""
    cursor = conn.cursor()

    cursor.execute("""
        INSERT INTO database_metadata
        (source, data_type, version, import_date, record_count, notes)
        VALUES (?, ?, ?, ?, ?, ?)
    """, (
        'USDA',
        'SR Legacy',
        version,
        datetime.now().isoformat(),
        record_count,
        'Full import of USDA SR Legacy dataset'
    ))

    conn.commit()
    print("Imported database metadata")

def main():
    """Main function."""
    start_time = time.time()

    try:
        # Set up the database
        conn = setup_database()

        # Create temp directory
        os.makedirs(TEMP_DIR, exist_ok=True)

        # Download dataset
        zip_path = download_dataset(USDA_DATASET_URL, TEMP_DIR)

        # Extract dataset
        extract_dir = os.path.join(TEMP_DIR, "extracted")
        extract_dataset(zip_path, extract_dir)

        # Find CSV files
        csv_files = find_csv_files(extract_dir)
        print(f"Found CSV files: {list(csv_files.keys())}")

        # Import data
        import_food_categories(conn, csv_files)
        import_foods(conn, csv_files)
        import_nutrients(conn, csv_files)

        # Get record count
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM foods")
        food_count = cursor.fetchone()[0]

        # Import metadata
        import_metadata(conn, "April 2018", food_count)

        # Close database
        conn.close()

        # Clean up
        shutil.rmtree(TEMP_DIR)

        elapsed_time = time.time() - start_time
        print(f"USDA database import completed in {elapsed_time:.2f} seconds")
        print(f"USDA database generated at {USDA_DB_PATH}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
