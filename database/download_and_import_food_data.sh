#!/bin/bash
# Download and import food data
# This script downloads the USDA and FoodB datasets and imports them into SQLite databases

# Set paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
USDA_DIR="$SCRIPT_DIR/usda_sr_legacy_csv"
FOODB_DIR="$SCRIPT_DIR/foodb_json"
OUTPUT_DIR="$SCRIPT_DIR/../app/src/assets/databases"

# Create directories
mkdir -p "$USDA_DIR"
mkdir -p "$FOODB_DIR"
mkdir -p "$OUTPUT_DIR"

# Function to download and extract USDA data
download_usda_data() {
    echo "Downloading USDA SR Legacy dataset..."
    
    # USDA dataset URL
    USDA_URL="https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_sr_legacy_food_csv_2018-04.zip"
    USDA_ZIP="$SCRIPT_DIR/usda_dataset.zip"
    
    # Download the dataset
    wget -O "$USDA_ZIP" "$USDA_URL" || curl -o "$USDA_ZIP" "$USDA_URL"
    
    if [ ! -f "$USDA_ZIP" ]; then
        echo "Failed to download USDA dataset"
        return 1
    fi
    
    echo "Extracting USDA dataset..."
    unzip -o "$USDA_ZIP" -d "$USDA_DIR"
    
    # Move CSV files to the root of the USDA directory
    find "$USDA_DIR" -name "*.csv" -exec mv {} "$USDA_DIR" \;
    
    # Clean up
    rm "$USDA_ZIP"
    
    echo "USDA dataset downloaded and extracted"
    return 0
}

# Function to download and extract FoodB data
download_foodb_data() {
    echo "Downloading FoodB dataset..."
    
    # FoodB dataset URL
    FOODB_URL="https://foodb.ca/public/system/downloads/foodb_2020_04_07_json.zip"
    FOODB_ZIP="$SCRIPT_DIR/foodb_dataset.zip"
    
    # Download the dataset
    wget -O "$FOODB_ZIP" "$FOODB_URL" || curl -o "$FOODB_ZIP" "$FOODB_URL"
    
    if [ ! -f "$FOODB_ZIP" ]; then
        echo "Failed to download FoodB dataset"
        return 1
    fi
    
    echo "Extracting FoodB dataset..."
    unzip -o "$FOODB_ZIP" -d "$FOODB_DIR"
    
    # Move JSON files to the root of the FoodB directory
    find "$FOODB_DIR" -name "*.json" -exec mv {} "$FOODB_DIR" \;
    
    # Clean up
    rm "$FOODB_ZIP"
    
    echo "FoodB dataset downloaded and extracted"
    return 0
}

# Function to import USDA data
import_usda_data() {
    echo "Importing USDA data..."
    python3 "$SCRIPT_DIR/import_usda_sr_legacy.py"
    return $?
}

# Function to import FoodB data
import_foodb_data() {
    echo "Importing FoodB data..."
    python3 "$SCRIPT_DIR/import_foodb.py"
    return $?
}

# Main function
main() {
    echo "Starting food data download and import process..."
    
    # Download datasets
    download_usda_data
    USDA_DOWNLOAD_STATUS=$?
    
    download_foodb_data
    FOODB_DOWNLOAD_STATUS=$?
    
    # Import datasets
    if [ $USDA_DOWNLOAD_STATUS -eq 0 ]; then
        import_usda_data
        USDA_IMPORT_STATUS=$?
    else
        echo "Skipping USDA import due to download failure"
        USDA_IMPORT_STATUS=1
    fi
    
    if [ $FOODB_DOWNLOAD_STATUS -eq 0 ]; then
        import_foodb_data
        FOODB_IMPORT_STATUS=$?
    else
        echo "Skipping FoodB import due to download failure"
        FOODB_IMPORT_STATUS=1
    fi
    
    # Check results
    if [ $USDA_IMPORT_STATUS -eq 0 ] && [ $FOODB_IMPORT_STATUS -eq 0 ]; then
        echo "All imports completed successfully!"
    else
        echo "Some imports failed. Check the logs for details."
    fi
    
    # Show database files
    echo "Database files:"
    ls -lh "$OUTPUT_DIR"
}

# Run the main function
main
