#!/usr/bin/env python3
"""
FoodB Import Script

This script imports the FoodB dataset directly into our improved SQLite database schema.
It handles the specific structure of the FoodB dataset and ensures proper ID mapping.
"""

import os
import sys
import json
import sqlite3
import time
from pathlib import Path
from datetime import datetime

# Configuration
OUTPUT_DIR = Path("../app/src/assets/databases")
FOODB_DB_PATH = OUTPUT_DIR / "foodb_foods.sqlite"
SCHEMA_PATH = Path("improved_food_database_schema.sql")
JSON_DIR = Path("./foodb_json")

# Nutrient mapping - maps FoodB nutrient names to our internal types
NUTRIENT_TYPE_MAPPING = {
    # Macronutrients
    "Protein": 1,
    "Total lipid (fat)": 1,
    "Carbohydrate, by difference": 1,
    "Energy": 1,
    "Fiber, total dietary": 1,
    "Sugars, total": 1,
    
    # Vitamins
    "Vitamin A, RAE": 2,
    "Vitamin C, total ascorbic acid": 2,
    "Vitamin B-6": 2,
    "Vitamin B-12": 2,
    "Vitamin D (D2 + D3)": 2,
    "Vitamin E (alpha-tocopherol)": 2,
    "Folate, total": 2,
    "Thiamin": 2,
    "Riboflavin": 2,
    "Niacin": 2,
    "Pantothenic acid": 2,
    "Vitamin K (phylloquinone)": 2,
    
    # Minerals
    "Calcium, Ca": 3,
    "Iron, Fe": 3,
    "Magnesium, Mg": 3,
    "Phosphorus, P": 3,
    "Potassium, K": 3,
    "Sodium, Na": 3,
    "Zinc, Zn": 3,
    "Copper, Cu": 3,
    "Manganese, Mn": 3,
    "Selenium, Se": 3,
    
    # Other
    "Cholesterol": 4,
    "Fatty acids, total trans": 4,
    "Fatty acids, total saturated": 4,
    "Fatty acids, total monounsaturated": 4,
    "Fatty acids, total polyunsaturated": 4,
}

# Common allergens to look for in ingredients
COMMON_ALLERGENS = [
    {"name": "Milk", "keywords": ["milk", "dairy", "lactose", "whey", "casein", "butter", "cream", "cheese", "yogurt"]},
    {"name": "Eggs", "keywords": ["egg", "albumin", "lysozyme", "globulin", "ovomucin", "ovalbumin", "ovotransferrin"]},
    {"name": "Fish", "keywords": ["fish", "cod", "salmon", "tuna", "tilapia", "halibut", "anchovy", "bass"]},
    {"name": "Shellfish", "keywords": ["shellfish", "crab", "lobster", "shrimp", "prawn", "crayfish", "clam", "mussel", "oyster", "scallop"]},
    {"name": "Tree Nuts", "keywords": ["almond", "hazelnut", "walnut", "cashew", "pecan", "brazil nut", "pistachio", "macadamia"]},
    {"name": "Peanuts", "keywords": ["peanut", "arachis", "groundnut"]},
    {"name": "Wheat", "keywords": ["wheat", "flour", "bread", "bran", "bulgur", "durum", "gluten", "semolina", "spelt"]},
    {"name": "Soybeans", "keywords": ["soy", "soya", "tofu", "edamame", "miso", "tempeh", "textured vegetable protein", "tvp"]},
    {"name": "Sesame", "keywords": ["sesame", "tahini", "sesamol", "gingelly"]},
    {"name": "Sulfites", "keywords": ["sulfite", "sulphite", "sulfur dioxide", "so2", "e220", "e221", "e222", "e223", "e224", "e225", "e226", "e227", "e228"]},
]

def setup_database():
    """Set up the database with our improved schema."""
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Delete existing database file if it exists
    if os.path.exists(FOODB_DB_PATH):
        os.remove(FOODB_DB_PATH)
    
    # Create new database with our schema
    conn = sqlite3.connect(FOODB_DB_PATH)
    
    with open(SCHEMA_PATH, 'r') as f:
        schema_sql = f.read()
        conn.executescript(schema_sql)
    
    # Insert nutrient types if not already in schema
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM nutrient_types")
    if cursor.fetchone()[0] == 0:
        cursor.executescript("""
            INSERT INTO nutrient_types (id, name, description) VALUES
            (1, 'macro', 'Macronutrients like protein, fat, carbohydrates'),
            (2, 'vitamin', 'Vitamins like A, C, D, etc.'),
            (3, 'mineral', 'Minerals like calcium, iron, etc.'),
            (4, 'other', 'Other nutritional components');
        """)
    
    conn.commit()
    return conn

def load_json_file(file_path):
    """Load a JSON file, handling both array and line-by-line formats."""
    data = []
    
    if not file_path.exists():
        print(f"Warning: {file_path} not found")
        return data
    
    print(f"Loading {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        try:
            # First try to load as a single JSON array
            data = json.load(f)
            print(f"Loaded {len(data)} items as JSON array")
        except json.JSONDecodeError:
            # If that fails, try to load each line as a separate JSON object
            print("JSON array loading failed, trying line-by-line parsing...")
            f.seek(0)
            line_count = 0
            for line in f:
                line = line.strip()
                if line:
                    try:
                        item = json.loads(line)
                        data.append(item)
                        line_count += 1
                        if line_count % 1000 == 0:
                            print(f"Parsed {line_count} lines...", end='\r')
                    except json.JSONDecodeError:
                        continue
            print(f"\nLoaded {len(data)} items from line-by-line parsing")
    
    return data

def import_food_categories(conn):
    """Import food categories for FoodB."""
    cursor = conn.cursor()
    
    print("Creating FoodB food categories...")
    
    # Create a top-level category for FoodB
    cursor.execute("""
        INSERT INTO food_categories (name, description, parent_id, source, source_id)
        VALUES (?, ?, NULL, ?, ?)
    """, ("FoodB Foods", "Foods from FoodB database", "FoodB", "foodb_main"))
    
    foodb_parent_id = cursor.lastrowid
    
    # Create some basic categories for FoodB
    categories = [
        ("Prepared Foods", "Ready-to-eat prepared foods"),
        ("Beverages", "Drinks and liquid refreshments"),
        ("Baked Goods", "Breads, pastries, and other baked items"),
        ("Dairy Products", "Milk-based foods and alternatives"),
        ("Desserts", "Sweet foods typically served after meals"),
        ("Snacks", "Foods eaten between meals"),
        ("Fruits", "Fresh and processed fruits"),
        ("Vegetables", "Fresh and processed vegetables"),
        ("Grains", "Grains, flours, and grain products"),
        ("Protein Foods", "Meat, poultry, fish, eggs, nuts, and seeds"),
    ]
    
    for i, (name, description) in enumerate(categories):
        cursor.execute("""
            INSERT INTO food_categories 
            (name, description, parent_id, source, source_id)
            VALUES (?, ?, ?, ?, ?)
        """, (
            name,
            description,
            foodb_parent_id,
            'FoodB',
            f"foodb_cat_{i+1}"
        ))
    
    conn.commit()
    print(f"Created {len(categories) + 1} food categories for FoodB")

def import_foods(conn):
    """Import foods from the FoodB dataset."""
    cursor = conn.cursor()
    
    # Path to the food file
    food_file = JSON_DIR / "Food.json"
    
    # Get category IDs
    print("Building category mapping...")
    category_map = {}
    cursor.execute("SELECT id, name FROM food_categories WHERE source = 'FoodB' AND parent_id IS NOT NULL")
    for row in cursor.fetchall():
        category_map[row[1].lower()] = row[0]
    
    print(f"Found {len(category_map)} categories")
    
    # Load food data
    foods_data = load_json_file(food_file)
    
    if not foods_data:
        print("No food data found, skipping food import")
        return 0
    
    total_foods = len(foods_data)
    print(f"Found {total_foods} foods to import")
    
    # Import foods with progress tracking
    food_count = 0
    skipped_count = 0
    start_time = time.time()
    last_update_time = start_time
    
    for i, food in enumerate(foods_data):
        try:
            # Skip foods without a name
            if not food.get('name'):
                skipped_count += 1
                continue
            
            # Assign a category based on name keywords
            category_id = None
            food_name = food.get('name', '').lower()
            food_description = food.get('description', '').lower() if food.get('description') else ''
            
            # Simple keyword matching for categories
            if any(word in food_name for word in ['juice', 'water', 'tea', 'coffee', 'drink', 'beverage']):
                category_id = category_map.get('beverages')
            elif any(word in food_name for word in ['bread', 'cake', 'pastry', 'cookie', 'muffin']):
                category_id = category_map.get('baked goods')
            elif any(word in food_name for word in ['milk', 'cheese', 'yogurt', 'cream']):
                category_id = category_map.get('dairy products')
            elif any(word in food_name for word in ['apple', 'banana', 'orange', 'fruit']):
                category_id = category_map.get('fruits')
            elif any(word in food_name for word in ['carrot', 'broccoli', 'spinach', 'vegetable']):
                category_id = category_map.get('vegetables')
            elif any(word in food_name for word in ['rice', 'wheat', 'oat', 'grain', 'cereal']):
                category_id = category_map.get('grains')
            elif any(word in food_name for word in ['beef', 'chicken', 'fish', 'meat', 'egg', 'nut']):
                category_id = category_map.get('protein foods')
            elif any(word in food_name for word in ['ice cream', 'pudding', 'dessert']):
                category_id = category_map.get('desserts')
            elif any(word in food_name for word in ['chip', 'cracker', 'snack']):
                category_id = category_map.get('snacks')
            else:
                category_id = category_map.get('prepared foods')  # Default category
            
            cursor.execute("""
                INSERT INTO foods 
                (name, description, scientific_name, serving_size, serving_unit, 
                 category_id, data_type, source, source_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                food.get('name', ''),
                food.get('description', '') or food.get('name', ''),
                food.get('scientific_name'),
                100,  # serving_size (default to 100g)
                'g',  # serving_unit
                category_id,
                'FoodB',
                'FoodB',
                f"foodb_{food.get('id', '')}"
            ))
            
            food_count += 1
            
            # Update progress every 100 foods or every 5 seconds
            current_time = time.time()
            if food_count % 100 == 0 or current_time - last_update_time >= 5:
                conn.commit()
                percent = (i + 1) * 100 / total_foods
                elapsed = current_time - start_time
                foods_per_sec = food_count / elapsed if elapsed > 0 else 0
                eta = (total_foods - (i + 1)) / foods_per_sec if foods_per_sec > 0 else 0
                
                print(f"Importing foods: {percent:.1f}% ({i+1}/{total_foods}) - {foods_per_sec:.1f} foods/sec - ETA: {eta:.0f} sec - Imported: {food_count} - Skipped: {skipped_count}", end='\r')
                last_update_time = current_time
        
        except Exception as e:
            print(f"\nError importing food {food.get('id', '')}: {e}")
    
    conn.commit()
    elapsed = time.time() - start_time
    print(f"\nImported {food_count} foods in {elapsed:.1f} seconds ({food_count/elapsed:.1f} foods/sec)")
    print(f"Skipped {skipped_count} foods (no name or other issues)")
    
    return food_count

def import_nutrients_and_compounds(conn):
    """Import nutrients and compounds from the FoodB dataset."""
    cursor = conn.cursor()
    
    # Paths to required files
    food_file = JSON_DIR / "Food.json"
    nutrient_file = JSON_DIR / "Nutrient.json"
    compound_file = JSON_DIR / "Compound.json"
    
    # Load data
    print("Loading nutrient data...")
    nutrients_data = load_json_file(nutrient_file)
    
    print("Loading compound data...")
    compounds_data = load_json_file(compound_file)
    
    if not nutrients_data or not compounds_data:
        print("Missing nutrient or compound data, skipping import")
        return 0, 0
    
    # Create mappings
    print("Building nutrient and compound mappings...")
    nutrient_map = {}
    for nutrient in nutrients_data:
        nutrient_map[nutrient['id']] = nutrient
    
    compound_map = {}
    for compound in compounds_data:
        compound_map[compound['id']] = compound
    
    print(f"Created mappings for {len(nutrient_map)} nutrients and {len(compound_map)} compounds")
    
    # Get food IDs
    print("Building food ID mapping...")
    food_id_map = {}
    cursor.execute("SELECT source_id, id FROM foods WHERE source = 'FoodB'")
    for row in cursor.fetchall():
        source_id = row[0]
        if source_id.startswith('foodb_'):
            foodb_id = source_id[6:]  # Remove 'foodb_' prefix
            try:
                food_id_map[int(foodb_id)] = row[1]
            except ValueError:
                # Skip if foodb_id is not a valid integer
                continue
    
    print(f"Found {len(food_id_map)} foods in database")
    
    # Load foods with nutrients and compounds
    foods_data = load_json_file(food_file)
    
    if not foods_data:
        print("No food data found, skipping nutrient and compound import")
        return 0, 0
    
    # Process foods with progress tracking
    total_foods = len(foods_data)
    print(f"Processing nutrients and compounds for {total_foods} foods")
    
    nutrient_count = 0
    compound_count = 0
    allergen_count = 0
    skipped_food_count = 0
    
    start_time = time.time()
    last_update_time = start_time
    
    for i, food in enumerate(foods_data):
        try:
            food_id = food.get('id')
            if food_id not in food_id_map:
                skipped_food_count += 1
                continue
            
            our_food_id = food_id_map[food_id]
            
            # Process nutrients
            if 'nutrients' in food and isinstance(food['nutrients'], list):
                for food_nutrient in food['nutrients']:
                    try:
                        nutrient_id = food_nutrient.get('nutrient_id')
                        if nutrient_id not in nutrient_map:
                            continue
                        
                        nutrient = nutrient_map[nutrient_id]
                        nutrient_name = nutrient.get('name', '')
                        
                        # Get the nutrient type ID
                        type_id = 4  # Default to 'other'
                        if nutrient_name in NUTRIENT_TYPE_MAPPING:
                            type_id = NUTRIENT_TYPE_MAPPING[nutrient_name]
                        
                        cursor.execute("""
                            INSERT INTO nutrients 
                            (food_id, name, amount, unit, type_id, source_id)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (
                            our_food_id,
                            nutrient_name,
                            float(food_nutrient.get('amount', 0)) if food_nutrient.get('amount') else 0,
                            nutrient.get('unit', 'g'),
                            type_id,
                            f"foodb_nutr_{nutrient_id}"
                        ))
                        
                        nutrient_count += 1
                    except Exception as e:
                        print(f"\nError importing nutrient {nutrient_id} for food {food_id}: {e}")
            
            # Process compounds
            if 'compounds' in food and isinstance(food['compounds'], list):
                for food_compound in food['compounds']:
                    try:
                        compound_id = food_compound.get('compound_id')
                        if compound_id not in compound_map:
                            continue
                        
                        compound = compound_map[compound_id]
                        
                        cursor.execute("""
                            INSERT INTO compounds 
                            (food_id, name, amount, unit, source_id)
                            VALUES (?, ?, ?, ?, ?)
                        """, (
                            our_food_id,
                            compound.get('name', ''),
                            float(food_compound.get('amount', 0)) if food_compound.get('amount') else 0,
                            food_compound.get('unit', 'mg'),
                            f"foodb_cmpd_{compound_id}"
                        ))
                        
                        compound_count += 1
                        
                        # Check for allergens in compound names
                        compound_name = compound.get('name', '').lower()
                        for allergen in COMMON_ALLERGENS:
                            if any(keyword.lower() in compound_name for keyword in allergen['keywords']):
                                # Check if this allergen is already added for this food
                                cursor.execute(
                                    "SELECT COUNT(*) FROM allergens WHERE food_id = ? AND name = ?",
                                    (our_food_id, allergen['name'])
                                )
                                if cursor.fetchone()[0] == 0:
                                    cursor.execute(
                                        "INSERT INTO allergens (food_id, name, certainty, source_id) VALUES (?, ?, ?, ?)",
                                        (our_food_id, allergen['name'], 'contains', f"foodb_alrg_{compound_id}")
                                    )
                                    allergen_count += 1
                    except Exception as e:
                        print(f"\nError importing compound {compound_id} for food {food_id}: {e}")
            
            # Update progress every 100 foods or every 5 seconds
            current_time = time.time()
            if (i + 1) % 100 == 0 or current_time - last_update_time >= 5:
                conn.commit()
                percent = (i + 1) * 100 / total_foods
                elapsed = current_time - start_time
                foods_per_sec = (i + 1) / elapsed if elapsed > 0 else 0
                eta = (total_foods - (i + 1)) / foods_per_sec if foods_per_sec > 0 else 0
                
                print(f"Processing: {percent:.1f}% ({i+1}/{total_foods}) - {foods_per_sec:.1f} foods/sec - ETA: {eta:.0f} sec - Nutrients: {nutrient_count} - Compounds: {compound_count} - Allergens: {allergen_count}", end='\r')
                last_update_time = current_time
        
        except Exception as e:
            print(f"\nError processing food {food.get('id', '')}: {e}")
    
    conn.commit()
    elapsed = time.time() - start_time
    print(f"\nProcessed {total_foods} foods in {elapsed:.1f} seconds ({total_foods/elapsed:.1f} foods/sec)")
    print(f"Imported {nutrient_count} nutrients, {compound_count} compounds, and {allergen_count} allergens")
    print(f"Skipped {skipped_food_count} foods (not found in database)")
    
    return nutrient_count, compound_count

def import_metadata(conn, food_count, nutrient_count, compound_count):
    """Import database metadata."""
    cursor = conn.cursor()
    
    cursor.execute("""
        INSERT INTO database_metadata 
        (source, data_type, version, import_date, record_count, notes)
        VALUES (?, ?, ?, ?, ?, ?)
    """, (
        'FoodB',
        'FoodB',
        'April 2020',
        datetime.now().isoformat(),
        food_count,
        f'Full import of FoodB dataset with {nutrient_count} nutrients and {compound_count} compounds'
    ))
    
    conn.commit()
    print("Imported database metadata")

def main():
    """Main function."""
    start_time = time.time()
    
    try:
        # Check if JSON directory exists
        if not JSON_DIR.exists():
            print(f"Error: JSON directory {JSON_DIR} not found")
            print("Please create this directory and place the FoodB JSON files in it")
            sys.exit(1)
        
        # Set up the database
        conn = setup_database()
        
        # Import data
        import_food_categories(conn)
        food_count = import_foods(conn)
        nutrient_count, compound_count = import_nutrients_and_compounds(conn)
        
        # Import metadata
        import_metadata(conn, food_count, nutrient_count, compound_count)
        
        # Close database
        conn.close()
        
        elapsed_time = time.time() - start_time
        print(f"FoodB database import completed in {elapsed_time:.2f} seconds")
        print(f"FoodB database generated at {FOODB_DB_PATH}")
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
