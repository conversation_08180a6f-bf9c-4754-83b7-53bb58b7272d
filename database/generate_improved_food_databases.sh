#!/bin/bash
# Generate improved SQLite database files for USDA and FoodB datasets
# This script uses the improved schema with soft delete functionality

# Set paths
OUTPUT_DIR="../app/src/assets/databases"
USDA_DB_PATH="$OUTPUT_DIR/usda_foods.sqlite"
FOODB_DB_PATH="$OUTPUT_DIR/foodb_foods.sqlite"
SCHEMA_PATH="improved_food_database_schema.sql"

# Ensure output directory exists
mkdir -p "$OUTPUT_DIR"

# Remove existing database files
rm -f "$USDA_DB_PATH" "$FOODB_DB_PATH"

echo "Generating USDA database with improved schema..."

# Create USDA database using the improved schema
sqlite3 "$USDA_DB_PATH" < "$SCHEMA_PATH"

# Insert sample USDA food categories
sqlite3 "$USDA_DB_PATH" <<EOF
-- Insert USDA food categories
INSERT INTO food_categories (name, description, parent_id, source, source_id) VALUES
('Fruits', 'Fresh and processed fruits', NULL, 'USDA', 'usda_cat_1'),
('Vegetables', 'Fresh and processed vegetables', NULL, 'USDA', 'usda_cat_2'),
('Grains', 'Grains, flours, and grain products', NULL, 'USDA', 'usda_cat_3'),
('Protein Foods', 'Meat, poultry, fish, eggs, nuts, and seeds', NULL, 'USDA', 'usda_cat_4'),
('Dairy', 'Milk and dairy products', NULL, 'USDA', 'usda_cat_5'),
('Fats and Oils', 'Fats, oils, and dressings', NULL, 'USDA', 'usda_cat_6'),
('Beverages', 'Alcoholic and non-alcoholic beverages', NULL, 'USDA', 'usda_cat_7'),
('Spices and Herbs', 'Spices, herbs, and flavorings', NULL, 'USDA', 'usda_cat_8');

-- Insert subcategories
INSERT INTO food_categories (name, description, parent_id, source, source_id) VALUES
('Citrus Fruits', 'Oranges, lemons, limes, etc.', 1, 'USDA', 'usda_cat_1_1'),
('Berries', 'Strawberries, blueberries, etc.', 1, 'USDA', 'usda_cat_1_2'),
('Tropical Fruits', 'Bananas, mangoes, etc.', 1, 'USDA', 'usda_cat_1_3'),
('Leafy Vegetables', 'Spinach, lettuce, kale, etc.', 2, 'USDA', 'usda_cat_2_1'),
('Root Vegetables', 'Carrots, potatoes, etc.', 2, 'USDA', 'usda_cat_2_2'),
('Cruciferous Vegetables', 'Broccoli, cauliflower, etc.', 2, 'USDA', 'usda_cat_2_3'),
('Bread and Baked Goods', 'Bread, rolls, etc.', 3, 'USDA', 'usda_cat_3_1'),
('Pasta and Noodles', 'Pasta, noodles, etc.', 3, 'USDA', 'usda_cat_3_2'),
('Breakfast Cereals', 'Ready-to-eat and cooked cereals', 3, 'USDA', 'usda_cat_3_3'),
('Beef', 'Beef products', 4, 'USDA', 'usda_cat_4_1'),
('Poultry', 'Chicken, turkey, etc.', 4, 'USDA', 'usda_cat_4_2'),
('Fish and Seafood', 'Fish, shellfish, etc.', 4, 'USDA', 'usda_cat_4_3'),
('Nuts and Seeds', 'Almonds, walnuts, etc.', 4, 'USDA', 'usda_cat_4_4'),
('Milk', 'Cow, goat, and plant-based milks', 5, 'USDA', 'usda_cat_5_1'),
('Cheese', 'Hard and soft cheeses', 5, 'USDA', 'usda_cat_5_2'),
('Yogurt', 'Yogurt and fermented milk products', 5, 'USDA', 'usda_cat_5_3');

-- Insert sample USDA foods with improved schema
INSERT INTO foods (name, description, scientific_name, serving_size, serving_unit, category_id, data_type, source, source_id) VALUES
('Apple', 'Fresh apple', 'Malus domestica', 100, 'g', 1, 'SR Legacy', 'USDA', 'usda_09003'),
('Banana', 'Fresh banana', 'Musa acuminata', 100, 'g', 3, 'SR Legacy', 'USDA', 'usda_09040'),
('Orange', 'Fresh orange', 'Citrus sinensis', 100, 'g', 9, 'SR Legacy', 'USDA', 'usda_09200'),
('Chicken Breast', 'Chicken breast, boneless, skinless', NULL, 100, 'g', 12, 'SR Legacy', 'USDA', 'usda_05062'),
('Salmon', 'Atlantic salmon, fresh', 'Salmo salar', 100, 'g', 13, 'SR Legacy', 'USDA', 'usda_15076'),
('Broccoli', 'Fresh broccoli', 'Brassica oleracea var. italica', 100, 'g', 14, 'SR Legacy', 'USDA', 'usda_11090'),
('Spinach', 'Fresh spinach', 'Spinacia oleracea', 100, 'g', 10, 'SR Legacy', 'USDA', 'usda_11457'),
('Brown Rice', 'Brown rice, cooked', 'Oryza sativa', 100, 'g', 3, 'SR Legacy', 'USDA', 'usda_20037'),
('Egg', 'Whole egg, raw', NULL, 50, 'g', 4, 'SR Legacy', 'USDA', 'usda_01123'),
('Milk', 'Whole milk', NULL, 240, 'ml', 17, 'SR Legacy', 'USDA', 'usda_01077');

-- Insert nutrients for Apple with improved schema
INSERT INTO nutrients (food_id, name, amount, unit, type_id, source_id) VALUES
(1, 'Energy', 52, 'kcal', 1, 'usda_nutr_208'),
(1, 'Protein', 0.3, 'g', 1, 'usda_nutr_203'),
(1, 'Carbohydrates', 14, 'g', 1, 'usda_nutr_205'),
(1, 'Total Fat', 0.2, 'g', 1, 'usda_nutr_204'),
(1, 'Fiber', 2.4, 'g', 1, 'usda_nutr_291'),
(1, 'Sugar', 10.3, 'g', 1, 'usda_nutr_269'),
(1, 'Calcium', 6, 'mg', 3, 'usda_nutr_301'),
(1, 'Iron', 0.12, 'mg', 3, 'usda_nutr_303'),
(1, 'Vitamin C', 4.6, 'mg', 2, 'usda_nutr_401');

-- Insert database metadata
INSERT INTO database_metadata (source, data_type, version, record_count, notes) VALUES
('USDA', 'SR Legacy', 'April 2018', 10, 'Sample data for development');
EOF

echo "USDA database generated with improved schema."

echo "Generating FoodB database with improved schema..."

# Create FoodB database using the improved schema
sqlite3 "$FOODB_DB_PATH" < "$SCHEMA_PATH"

# Insert sample FoodB food categories
sqlite3 "$FOODB_DB_PATH" <<EOF
-- Insert FoodB food categories
INSERT INTO food_categories (name, description, parent_id, source, source_id) VALUES
('Prepared Foods', 'Ready-to-eat prepared foods', NULL, 'FoodB', 'foodb_cat_1'),
('Beverages', 'Drinks and liquid refreshments', NULL, 'FoodB', 'foodb_cat_2'),
('Baked Goods', 'Breads, pastries, and other baked items', NULL, 'FoodB', 'foodb_cat_3'),
('Dairy Products', 'Milk-based foods and alternatives', NULL, 'FoodB', 'foodb_cat_4'),
('Desserts', 'Sweet foods typically served after meals', NULL, 'FoodB', 'foodb_cat_5'),
('Snacks', 'Foods eaten between meals', NULL, 'FoodB', 'foodb_cat_6');

-- Insert subcategories
INSERT INTO food_categories (name, description, parent_id, source, source_id) VALUES
('Soups', 'Liquid food served warm', 1, 'FoodB', 'foodb_cat_1_1'),
('Sandwiches', 'Food between bread slices', 1, 'FoodB', 'foodb_cat_1_2'),
('Salads', 'Mixed vegetable dishes', 1, 'FoodB', 'foodb_cat_1_3'),
('Fruit Juices', 'Juices from fruits', 2, 'FoodB', 'foodb_cat_2_1'),
('Smoothies', 'Blended fruit drinks', 2, 'FoodB', 'foodb_cat_2_2'),
('Coffee Drinks', 'Coffee-based beverages', 2, 'FoodB', 'foodb_cat_2_3'),
('Tea Drinks', 'Tea-based beverages', 2, 'FoodB', 'foodb_cat_2_4');

-- Insert sample FoodB foods with improved schema
INSERT INTO foods (name, description, brand_name, serving_size, serving_unit, category_id, data_type, source, source_id) VALUES
('Apple Juice', 'Apple juice', 'Generic', 240, 'ml', 10, 'Processed', 'FoodB', 'foodb_1001'),
('Banana Smoothie', 'Banana smoothie', 'Generic', 240, 'ml', 11, 'Processed', 'FoodB', 'foodb_1002'),
('Orange Juice', 'Orange juice', 'Generic', 240, 'ml', 10, 'Processed', 'FoodB', 'foodb_1003'),
('Chicken Soup', 'Chicken soup', 'Generic', 240, 'ml', 7, 'Processed', 'FoodB', 'foodb_1004'),
('Salmon Sushi', 'Salmon sushi roll', 'Generic', 100, 'g', 1, 'Processed', 'FoodB', 'foodb_1005'),
('Broccoli Soup', 'Broccoli cream soup', 'Generic', 240, 'ml', 7, 'Processed', 'FoodB', 'foodb_1006'),
('Spinach Salad', 'Spinach salad with dressing', 'Generic', 100, 'g', 9, 'Processed', 'FoodB', 'foodb_1007'),
('Brown Rice Bowl', 'Brown rice bowl with vegetables', 'Generic', 300, 'g', 1, 'Processed', 'FoodB', 'foodb_1008'),
('Egg Sandwich', 'Egg sandwich', 'Generic', 150, 'g', 8, 'Processed', 'FoodB', 'foodb_1009'),
('Milk Shake', 'Vanilla milk shake', 'Generic', 300, 'ml', 11, 'Processed', 'FoodB', 'foodb_1010');

-- Insert nutrients for Apple Juice with improved schema
INSERT INTO nutrients (food_id, name, amount, unit, type_id, source_id) VALUES
(1, 'Energy', 46, 'kcal', 1, 'foodb_nutr_1001'),
(1, 'Protein', 0.1, 'g', 1, 'foodb_nutr_1002'),
(1, 'Carbohydrates', 11.5, 'g', 1, 'foodb_nutr_1003'),
(1, 'Total Fat', 0.1, 'g', 1, 'foodb_nutr_1004'),
(1, 'Sugar', 9.6, 'g', 1, 'foodb_nutr_1005'),
(1, 'Vitamin C', 2.2, 'mg', 2, 'foodb_nutr_1006');

-- Insert ingredients for Apple Juice
INSERT INTO ingredients (food_id, name, amount, unit, order_number, percent) VALUES
(1, 'Apple', 100, '%', 1, 100);

-- Insert compounds for Apple Juice (FoodB specific)
INSERT INTO compounds (food_id, name, amount, unit, source_id) VALUES
(1, 'Malic acid', 0.3, 'g', 'foodb_cmpd_1001'),
(1, 'Quercetin', 0.05, 'mg', 'foodb_cmpd_1002'),
(1, 'Epicatechin', 0.03, 'mg', 'foodb_cmpd_1003');

-- Insert database metadata
INSERT INTO database_metadata (source, data_type, version, record_count, notes) VALUES
('FoodB', 'Processed', 'April 2020', 10, 'Sample data for development');
EOF

echo "FoodB database generated with improved schema."

echo "Database generation complete!"
echo "USDA database: $USDA_DB_PATH"
echo "FoodB database: $FOODB_DB_PATH"
