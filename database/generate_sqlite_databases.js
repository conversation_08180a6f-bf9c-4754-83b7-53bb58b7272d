/**
 * Generate SQLite Database Files
 *
 * This script generates SQLite database files for the USDA and FoodB datasets
 * to be used in the ZnüniZähler app.
 *
 * This script uses better-sqlite3 which is easier to install and use.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const OUTPUT_DIR = path.join(__dirname, '../app/src/assets/databases');
const USDA_DB_PATH = path.join(OUTPUT_DIR, 'usda_foods.sqlite');
const FOODB_DB_PATH = path.join(OUTPUT_DIR, 'foodb_foods.sqlite');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Create a new SQLite database file
 * @param {string} dbPath - Path to the database file
 * @returns {Promise<sqlite3.Database>} - Database connection
 */
function createDatabase(dbPath) {
  return new Promise((resolve, reject) => {
    // Delete existing database file if it exists
    if (fs.existsSync(dbPath)) {
      fs.unlinkSync(dbPath);
    }

    // Create new database
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve(db);
      }
    });
  });
}

/**
 * Run a SQL query
 * @param {sqlite3.Database} db - Database connection
 * @param {string} sql - SQL query
 * @param {Array} params - Query parameters
 * @returns {Promise<any>} - Query result
 */
function runQuery(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve(this);
      }
    });
  });
}

/**
 * Create database schema
 * @param {sqlite3.Database} db - Database connection
 * @returns {Promise<void>}
 */
async function createSchema(db) {
  // Create foods table
  await runQuery(db, `
    CREATE TABLE foods (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      brand_name TEXT,
      serving_size REAL DEFAULT 100,
      serving_unit TEXT DEFAULT 'g',
      barcode TEXT,
      source TEXT,
      source_id TEXT
    )
  `);

  // Create nutrients table
  await runQuery(db, `
    CREATE TABLE nutrients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      amount REAL NOT NULL,
      unit TEXT NOT NULL,
      type TEXT NOT NULL,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
  `);

  // Create ingredients table
  await runQuery(db, `
    CREATE TABLE ingredients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      amount REAL,
      unit TEXT,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
  `);

  // Create allergens table
  await runQuery(db, `
    CREATE TABLE allergens (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      food_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      FOREIGN KEY (food_id) REFERENCES foods (id)
    )
  `);

  // Create indexes
  await runQuery(db, `CREATE INDEX idx_foods_name ON foods (name)`);
  await runQuery(db, `CREATE INDEX idx_foods_source ON foods (source)`);
  await runQuery(db, `CREATE INDEX idx_nutrients_food_id ON nutrients (food_id)`);
  await runQuery(db, `CREATE INDEX idx_ingredients_food_id ON ingredients (food_id)`);
  await runQuery(db, `CREATE INDEX idx_allergens_food_id ON allergens (food_id)`);
}

/**
 * Generate USDA database with sample data
 */
async function generateUSDADatabase() {
  console.log('Generating USDA database...');

  const db = await createDatabase(USDA_DB_PATH);
  await createSchema(db);

  // Sample USDA foods
  const foods = [
    { name: 'Apple', description: 'Fresh apple', source: 'USDA', source_id: 'usda_09003' },
    { name: 'Banana', description: 'Fresh banana', source: 'USDA', source_id: 'usda_09040' },
    { name: 'Orange', description: 'Fresh orange', source: 'USDA', source_id: 'usda_09200' },
    { name: 'Chicken Breast', description: 'Chicken breast, boneless, skinless', source: 'USDA', source_id: 'usda_05062' },
    { name: 'Salmon', description: 'Atlantic salmon, fresh', source: 'USDA', source_id: 'usda_15076' },
    // Add more foods here...
  ];

  // Insert foods
  for (const food of foods) {
    const result = await runQuery(
      db,
      `INSERT INTO foods (name, description, source, source_id) VALUES (?, ?, ?, ?)`,
      [food.name, food.description, food.source, food.source_id]
    );

    const foodId = result.lastID;

    // Add nutrients
    if (food.name === 'Apple') {
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Energy', 52, 'kcal', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Protein', 0.3, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Carbohydrates', 14, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Total Fat', 0.2, 'g', 'macro']
      );
    } else if (food.name === 'Banana') {
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Energy', 89, 'kcal', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Protein', 1.1, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Carbohydrates', 23, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Total Fat', 0.3, 'g', 'macro']
      );
    }
    // Add nutrients for other foods...
  }

  // Close database
  db.close();

  console.log(`USDA database generated at ${USDA_DB_PATH}`);
}

/**
 * Generate FoodB database with sample data
 */
async function generateFoodBDatabase() {
  console.log('Generating FoodB database...');

  const db = await createDatabase(FOODB_DB_PATH);
  await createSchema(db);

  // Sample FoodB foods
  const foods = [
    { name: 'Apple Juice', description: 'Apple juice', brand_name: 'Generic', source: 'FoodB', source_id: 'foodb_1001' },
    { name: 'Banana Smoothie', description: 'Banana smoothie', brand_name: 'Generic', source: 'FoodB', source_id: 'foodb_1002' },
    { name: 'Orange Juice', description: 'Orange juice', brand_name: 'Generic', source: 'FoodB', source_id: 'foodb_1003' },
    // Add more foods here...
  ];

  // Insert foods
  for (const food of foods) {
    const result = await runQuery(
      db,
      `INSERT INTO foods (name, description, brand_name, source, source_id) VALUES (?, ?, ?, ?, ?)`,
      [food.name, food.description, food.brand_name, food.source, food.source_id]
    );

    const foodId = result.lastID;

    // Add nutrients
    if (food.name === 'Apple Juice') {
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Energy', 46, 'kcal', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Protein', 0.1, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Carbohydrates', 11.5, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Total Fat', 0.1, 'g', 'macro']
      );

      // Add ingredients
      await runQuery(
        db,
        `INSERT INTO ingredients (food_id, name, amount, unit) VALUES (?, ?, ?, ?)`,
        [foodId, 'Apple', 100, '%']
      );
    } else if (food.name === 'Banana Smoothie') {
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Energy', 95, 'kcal', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Protein', 1.5, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Carbohydrates', 22, 'g', 'macro']
      );
      await runQuery(
        db,
        `INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES (?, ?, ?, ?, ?)`,
        [foodId, 'Total Fat', 0.5, 'g', 'macro']
      );

      // Add ingredients
      await runQuery(
        db,
        `INSERT INTO ingredients (food_id, name, amount, unit) VALUES (?, ?, ?, ?)`,
        [foodId, 'Banana', 80, '%']
      );
      await runQuery(
        db,
        `INSERT INTO ingredients (food_id, name, amount, unit) VALUES (?, ?, ?, ?)`,
        [foodId, 'Milk', 20, '%']
      );

      // Add allergens
      await runQuery(
        db,
        `INSERT INTO allergens (food_id, name) VALUES (?, ?)`,
        [foodId, 'Milk']
      );
    }
    // Add nutrients for other foods...
  }

  // Close database
  db.close();

  console.log(`FoodB database generated at ${FOODB_DB_PATH}`);
}

/**
 * Main function
 */
async function main() {
  try {
    await generateUSDADatabase();
    await generateFoodBDatabase();
    console.log('Database generation complete!');
  } catch (error) {
    console.error('Error generating databases:', error);
  }
}

// Run the script
main();
