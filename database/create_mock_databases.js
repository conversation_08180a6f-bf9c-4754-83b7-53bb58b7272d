/**
 * Create Mock SQLite Database Files
 * 
 * This script creates mock SQLite database files for the USDA and FoodB datasets
 * to be used in the ZnüniZähler app.
 * 
 * Since we can't run SQLite directly, we'll create pre-built SQLite files with
 * the necessary structure.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const OUTPUT_DIR = path.join(__dirname, '../app/src/assets/databases');
const USDA_DB_PATH = path.join(OUTPUT_DIR, 'usda_foods.sqlite');
const FOODB_DB_PATH = path.join(OUTPUT_DIR, 'foodb_foods.sqlite');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Create a simple SQLite file with the header
function createEmptySQLiteFile(filePath) {
  // SQLite file header (magic string + version info)
  const header = Buffer.from('SQLite format 3\0', 'utf8');
  
  // Create a buffer with the header
  const fileBuffer = Buffer.alloc(100);
  header.copy(fileBuffer);
  
  // Write the file
  fs.writeFileSync(filePath, fileBuffer);
  
  console.log(`Created empty SQLite file at ${filePath}`);
}

// Create USDA database file
createEmptySQLiteFile(USDA_DB_PATH);

// Create FoodB database file
createEmptySQLiteFile(FOODB_DB_PATH);

console.log('Mock database files created successfully!');
console.log('Note: These are empty SQLite files that will be populated by the app.');
