#!/usr/bin/env python3
"""
USDA SR Legacy Import Script

This script imports the USDA SR Legacy dataset directly into our improved SQLite database schema.
It handles the specific structure of the SR Legacy dataset and ensures proper ID mapping.
"""

import os
import sys
import csv
import sqlite3
import time
from pathlib import Path
from datetime import datetime

# Configuration
OUTPUT_DIR = Path("../app/src/assets/databases")
USDA_DB_PATH = OUTPUT_DIR / "usda_foods.sqlite"
SCHEMA_PATH = Path("improved_food_database_schema.sql")
CSV_DIR = Path("./usda_sr_legacy_csv")

# Nutrient mapping - maps USDA nutrient IDs to our internal types
NUTRIENT_TYPE_MAPPING = {
    # Macronutrients
    "203": 1,  # Protein
    "204": 1,  # Total Fat
    "205": 1,  # Carbohydrates
    "208": 1,  # Energy
    "269": 1,  # Sugars
    "291": 1,  # Fiber
    
    # Vitamins
    "301": 2,  # Cal<PERSON>um
    "401": 2,  # Vitamin C
    "404": 2,  # Thiamin
    "405": 2,  # Riboflavin
    "406": 2,  # <PERSON><PERSON><PERSON>
    "415": 2,  # Vitamin B-6
    "418": 2,  # Vitamin B-12
    "320": 2,  # Vitamin A
    "323": 2,  # Vitamin E
    "328": 2,  # Vitamin D
    "417": 2,  # Folate
    "430": 2,  # Vitamin K
    
    # Minerals
    "303": 3,  # Iron
    "304": 3,  # Magnesium
    "305": 3,  # Phosphorus
    "306": 3,  # Potassium
    "307": 3,  # Sodium
    "309": 3,  # Zinc
    "312": 3,  # Copper
    "315": 3,  # Manganese
    "317": 3,  # Selenium
    
    # Other
    "601": 4,  # Cholesterol
    "605": 4,  # Trans Fat
    "606": 4,  # Saturated Fat
    "645": 4,  # Monounsaturated Fat
    "646": 4,  # Polyunsaturated Fat
}

def setup_database():
    """Set up the database with our improved schema."""
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Delete existing database file if it exists
    if os.path.exists(USDA_DB_PATH):
        os.remove(USDA_DB_PATH)
    
    # Create new database with our schema
    conn = sqlite3.connect(USDA_DB_PATH)
    
    with open(SCHEMA_PATH, 'r') as f:
        schema_sql = f.read()
        conn.executescript(schema_sql)
    
    # Insert nutrient types if not already in schema
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM nutrient_types")
    if cursor.fetchone()[0] == 0:
        cursor.executescript("""
            INSERT INTO nutrient_types (id, name, description) VALUES
            (1, 'macro', 'Macronutrients like protein, fat, carbohydrates'),
            (2, 'vitamin', 'Vitamins like A, C, D, etc.'),
            (3, 'mineral', 'Minerals like calcium, iron, etc.'),
            (4, 'other', 'Other nutritional components');
        """)
    
    conn.commit()
    return conn

def import_food_groups(conn):
    """Import food groups as categories."""
    cursor = conn.cursor()
    
    # Create a top-level category for USDA
    cursor.execute("""
        INSERT INTO food_categories (name, description, parent_id, source, source_id)
        VALUES (?, ?, NULL, ?, ?)
    """, ("USDA Foods", "Foods from USDA SR Legacy database", "USDA", "usda_main"))
    
    usda_parent_id = cursor.lastrowid
    
    # Path to the food groups file
    food_group_file = CSV_DIR / "food_group.csv"
    
    if not food_group_file.exists():
        print(f"Warning: {food_group_file} not found, skipping food group import")
        return
    
    print("Importing food groups as categories...")
    
    # Read and import food groups
    with open(food_group_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                cursor.execute("""
                    INSERT INTO food_categories 
                    (name, description, parent_id, source, source_id)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    row.get('description', ''),
                    row.get('description', ''),
                    usda_parent_id,
                    'USDA',
                    f"usda_fg_{row.get('id', '')}"
                ))
            except Exception as e:
                print(f"Error importing food category {row.get('id', '')}: {e}")
    
    conn.commit()
    print(f"Imported food categories")

def import_foods(conn):
    """Import foods from the USDA SR Legacy dataset."""
    cursor = conn.cursor()
    
    # Path to the food file
    food_file = CSV_DIR / "food.csv"
    
    if not food_file.exists():
        print(f"Warning: {food_file} not found, skipping food import")
        return
    
    print("Importing foods...")
    
    # Create a mapping of food group IDs to category IDs
    food_group_map = {}
    cursor.execute("SELECT source_id, id FROM food_categories WHERE source = 'USDA'")
    for row in cursor.fetchall():
        source_id = row[0]
        if source_id.startswith('usda_fg_'):
            food_group_id = source_id[8:]  # Remove 'usda_fg_' prefix
            food_group_map[food_group_id] = row[1]
    
    # First count total rows to calculate progress
    total_foods = 0
    with open(food_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for _ in reader:
            total_foods += 1
    
    print(f"Found {total_foods} foods to import")
    
    # Read and import foods
    food_count = 0
    start_time = time.time()
    last_update_time = start_time
    
    with open(food_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                # Get the category ID from the food group ID
                category_id = None
                if 'food_group_id' in row and row['food_group_id'] in food_group_map:
                    category_id = food_group_map[row['food_group_id']]
                
                cursor.execute("""
                    INSERT INTO foods 
                    (name, description, scientific_name, serving_size, serving_unit, 
                     category_id, data_type, publication_date, source, source_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row.get('description', ''),
                    row.get('description', ''),
                    None,  # scientific_name
                    100,  # serving_size (default to 100g)
                    'g',  # serving_unit
                    category_id,
                    'SR Legacy',
                    row.get('publication_date', None),
                    'USDA',
                    f"usda_{row.get('fdc_id', '')}"
                ))
                
                food_count += 1
                
                # Update progress every 100 foods or every 5 seconds
                current_time = time.time()
                if food_count % 100 == 0 or current_time - last_update_time >= 5:
                    conn.commit()
                    percent = food_count * 100 / total_foods
                    elapsed = current_time - start_time
                    foods_per_sec = food_count / elapsed if elapsed > 0 else 0
                    eta = (total_foods - food_count) / foods_per_sec if foods_per_sec > 0 else 0
                    
                    print(f"Importing foods: {percent:.1f}% ({food_count}/{total_foods}) - {foods_per_sec:.1f} foods/sec - ETA: {eta:.0f} sec", end='\r')
                    last_update_time = current_time
            
            except Exception as e:
                print(f"\nError importing food {row.get('fdc_id', '')}: {e}")
    
    conn.commit()
    elapsed = time.time() - start_time
    print(f"\nImported {food_count} foods in {elapsed:.1f} seconds ({food_count/elapsed:.1f} foods/sec)")

def import_nutrients(conn):
    """Import nutrients from the USDA SR Legacy dataset."""
    cursor = conn.cursor()
    
    # Paths to required files
    food_nutrient_file = CSV_DIR / "food_nutrient.csv"
    nutrient_file = CSV_DIR / "nutrient.csv"
    
    if not food_nutrient_file.exists() or not nutrient_file.exists():
        print(f"Warning: Required nutrient files not found, skipping nutrient import")
        return
    
    print("Importing nutrients...")
    
    # Create a mapping of nutrient IDs to names and units
    print("Building nutrient mapping...")
    nutrient_map = {}
    with open(nutrient_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            nutrient_map[row.get('id', '')] = {
                'name': row.get('name', ''),
                'unit': row.get('unit_name', '')
            }
    
    print(f"Found {len(nutrient_map)} unique nutrients")
    
    # Create a mapping of USDA food IDs to our food IDs
    print("Building food ID mapping...")
    food_id_map = {}
    cursor.execute("SELECT source_id, id FROM foods WHERE source = 'USDA'")
    for row in cursor.fetchall():
        source_id = row[0]
        if source_id.startswith('usda_'):
            usda_id = source_id[5:]  # Remove 'usda_' prefix
            food_id_map[usda_id] = row[1]
    
    print(f"Found {len(food_id_map)} foods in database")
    
    # First count total rows to calculate progress
    total_nutrients = 0
    with open(food_nutrient_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for _ in reader:
            total_nutrients += 1
    
    print(f"Found {total_nutrients} nutrient values to import")
    
    # Read and import food nutrients
    nutrient_count = 0
    skipped_count = 0
    start_time = time.time()
    last_update_time = start_time
    
    with open(food_nutrient_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                # Get the food ID from our mapping
                food_id = None
                if 'fdc_id' in row and row['fdc_id'] in food_id_map:
                    food_id = food_id_map[row['fdc_id']]
                else:
                    skipped_count += 1
                    continue  # Skip if food not found
                
                # Get the nutrient info from our mapping
                nutrient_id = row.get('nutrient_id', '')
                if nutrient_id not in nutrient_map:
                    skipped_count += 1
                    continue  # Skip if nutrient not found
                
                nutrient_info = nutrient_map[nutrient_id]
                
                # Get the nutrient type ID
                type_id = 4  # Default to 'other'
                if nutrient_id in NUTRIENT_TYPE_MAPPING:
                    type_id = NUTRIENT_TYPE_MAPPING[nutrient_id]
                
                cursor.execute("""
                    INSERT INTO nutrients 
                    (food_id, name, amount, unit, type_id, source_id, derivation_code)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    food_id,
                    nutrient_info['name'],
                    float(row.get('amount', 0)) if row.get('amount') else 0,
                    nutrient_info['unit'],
                    type_id,
                    f"usda_nutr_{nutrient_id}",
                    row.get('derivation_id', None)
                ))
                
                nutrient_count += 1
                
                # Update progress every 1000 nutrients or every 5 seconds
                current_time = time.time()
                if nutrient_count % 1000 == 0 or current_time - last_update_time >= 5:
                    conn.commit()
                    percent = (nutrient_count + skipped_count) * 100 / total_nutrients
                    elapsed = current_time - start_time
                    nutrients_per_sec = nutrient_count / elapsed if elapsed > 0 else 0
                    eta = (total_nutrients - (nutrient_count + skipped_count)) / nutrients_per_sec if nutrients_per_sec > 0 else 0
                    
                    print(f"Importing nutrients: {percent:.1f}% ({nutrient_count + skipped_count}/{total_nutrients}) - {nutrients_per_sec:.1f} nutrients/sec - ETA: {eta:.0f} sec - Imported: {nutrient_count} - Skipped: {skipped_count}", end='\r')
                    last_update_time = current_time
            
            except Exception as e:
                print(f"\nError importing nutrient {row.get('id', '')}: {e}")
    
    conn.commit()
    elapsed = time.time() - start_time
    print(f"\nImported {nutrient_count} nutrients in {elapsed:.1f} seconds ({nutrient_count/elapsed:.1f} nutrients/sec)")
    print(f"Skipped {skipped_count} nutrients (food not found or nutrient not mapped)")

def import_metadata(conn, food_count):
    """Import database metadata."""
    cursor = conn.cursor()
    
    cursor.execute("""
        INSERT INTO database_metadata 
        (source, data_type, version, import_date, record_count, notes)
        VALUES (?, ?, ?, ?, ?, ?)
    """, (
        'USDA',
        'SR Legacy',
        'April 2018',
        datetime.now().isoformat(),
        food_count,
        'Full import of USDA SR Legacy dataset'
    ))
    
    conn.commit()
    print("Imported database metadata")

def main():
    """Main function."""
    start_time = time.time()
    
    try:
        # Check if CSV directory exists
        if not CSV_DIR.exists():
            print(f"Error: CSV directory {CSV_DIR} not found")
            print("Please create this directory and place the USDA SR Legacy CSV files in it")
            sys.exit(1)
        
        # Set up the database
        conn = setup_database()
        
        # Import data
        import_food_groups(conn)
        import_foods(conn)
        
        # Get food count
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM foods")
        food_count = cursor.fetchone()[0]
        
        import_nutrients(conn)
        
        # Import metadata
        import_metadata(conn, food_count)
        
        # Close database
        conn.close()
        
        elapsed_time = time.time() - start_time
        print(f"USDA database import completed in {elapsed_time:.2f} seconds")
        print(f"USDA database generated at {USDA_DB_PATH}")
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
