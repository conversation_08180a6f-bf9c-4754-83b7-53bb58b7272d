-- Improved Food Database Schema for USDA and FoodB Data
-- This schema is designed to capture all relevant data from both sources
-- with proper soft delete functionality

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Food Categories table with self-referential relationship
CREATE TABLE food_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    parent_id INTEGER,
    source TEXT, -- 'USDA' or 'FoodB'
    source_id TEXT, -- Original ID in the source database
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT,
    FOREIGN KEY (parent_id) REFERENCES food_categories(id)
);

-- Create index on category name
CREATE INDEX idx_food_categories_name ON food_categories (name);

-- Foods table with enhanced fields
CREATE TABLE foods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    scientific_name TEXT, -- For foundation foods
    brand_name TEXT,
    manufacturer TEXT, -- For branded foods
    serving_size REAL DEFAULT 100,
    serving_unit TEXT DEFAULT 'g',
    household_serving_size REAL, -- Common household serving size
    household_serving_unit TEXT, -- e.g., "cup", "tablespoon"
    barcode TEXT,
    category_id INTEGER,
    data_type TEXT, -- 'Foundation', 'SR Legacy', 'Survey', 'Branded', 'FoodB'
    publication_date TEXT, -- When this food was published in the source
    source TEXT NOT NULL, -- 'USDA' or 'FoodB'
    source_id TEXT NOT NULL, -- Original ID in the source database
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT,
    FOREIGN KEY (category_id) REFERENCES food_categories(id)
);

-- Create indexes for efficient querying
CREATE INDEX idx_foods_name ON foods (name);
CREATE INDEX idx_foods_source ON foods (source);
CREATE INDEX idx_foods_source_id ON foods (source_id);
CREATE INDEX idx_foods_barcode ON foods (barcode);
CREATE INDEX idx_foods_category_id ON foods (category_id);
CREATE INDEX idx_foods_data_type ON foods (data_type);

-- Nutrient Types table
CREATE TABLE nutrient_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL, -- 'macronutrient', 'vitamin', 'mineral', etc.
    description TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT
);

-- Insert standard nutrient types
INSERT INTO nutrient_types (name, description) VALUES
('macro', 'Macronutrients like protein, fat, carbohydrates'),
('vitamin', 'Vitamins like A, C, D, etc.'),
('mineral', 'Minerals like calcium, iron, etc.'),
('other', 'Other nutritional components');

-- Nutrients table with enhanced fields
CREATE TABLE nutrients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    food_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    amount REAL NOT NULL,
    unit TEXT NOT NULL,
    type_id INTEGER,
    source_id TEXT, -- Original nutrient ID in the source database
    confidence REAL, -- Data quality indicator (0-1)
    min_value REAL, -- Minimum value if range is provided
    max_value REAL, -- Maximum value if range is provided
    median_value REAL, -- Median value if available
    derivation_code TEXT, -- How the value was derived (analytical, calculated, etc.)
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT,
    FOREIGN KEY (food_id) REFERENCES foods(id),
    FOREIGN KEY (type_id) REFERENCES nutrient_types(id)
);

-- Create indexes for nutrients
CREATE INDEX idx_nutrients_food_id ON nutrients (food_id);
CREATE INDEX idx_nutrients_name ON nutrients (name);
CREATE INDEX idx_nutrients_type_id ON nutrients (type_id);

-- Ingredients table with enhanced fields
CREATE TABLE ingredients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    food_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    amount REAL,
    unit TEXT,
    order_number INTEGER, -- Order in ingredient list
    percent REAL, -- Percentage in the food if available
    source_id TEXT, -- Original ID in the source database
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT,
    FOREIGN KEY (food_id) REFERENCES foods(id)
);

-- Create index for ingredients
CREATE INDEX idx_ingredients_food_id ON ingredients (food_id);
CREATE INDEX idx_ingredients_name ON ingredients (name);

-- Allergens table with enhanced fields
CREATE TABLE allergens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    food_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    certainty TEXT, -- 'contains', 'may contain', 'derived from'
    source_id TEXT, -- Original ID in the source database
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT,
    FOREIGN KEY (food_id) REFERENCES foods(id)
);

-- Create index for allergens
CREATE INDEX idx_allergens_food_id ON allergens (food_id);
CREATE INDEX idx_allergens_name ON allergens (name);

-- Compounds table (primarily for FoodB data)
CREATE TABLE compounds (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    food_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    amount REAL,
    unit TEXT,
    source_id TEXT, -- Original compound ID in the source database
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT,
    FOREIGN KEY (food_id) REFERENCES foods(id)
);

-- Create index for compounds
CREATE INDEX idx_compounds_food_id ON compounds (food_id);
CREATE INDEX idx_compounds_name ON compounds (name);

-- Food References table (for scientific references)
CREATE TABLE food_references (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    food_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    authors TEXT,
    journal TEXT,
    volume TEXT,
    issue TEXT,
    year TEXT,
    pages TEXT,
    doi TEXT,
    url TEXT,
    source_id TEXT, -- Original reference ID in the source database
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT,
    FOREIGN KEY (food_id) REFERENCES foods(id)
);

-- Create index for references
CREATE INDEX idx_food_references_food_id ON food_references (food_id);

-- Database Metadata table (for tracking imports and versions)
CREATE TABLE database_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source TEXT NOT NULL, -- 'USDA' or 'FoodB'
    data_type TEXT, -- 'Foundation', 'SR Legacy', etc.
    version TEXT NOT NULL, -- Version of the source data
    import_date TEXT DEFAULT (datetime('now')),
    record_count INTEGER, -- Number of records imported
    notes TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    deleted_at TEXT
);

-- Create triggers to update the updated_at timestamp
CREATE TRIGGER update_foods_timestamp 
AFTER UPDATE ON foods
BEGIN
    UPDATE foods SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_nutrients_timestamp 
AFTER UPDATE ON nutrients
BEGIN
    UPDATE nutrients SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_ingredients_timestamp 
AFTER UPDATE ON ingredients
BEGIN
    UPDATE ingredients SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_allergens_timestamp 
AFTER UPDATE ON allergens
BEGIN
    UPDATE allergens SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_compounds_timestamp 
AFTER UPDATE ON compounds
BEGIN
    UPDATE compounds SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_food_references_timestamp 
AFTER UPDATE ON food_references
BEGIN
    UPDATE food_references SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_food_categories_timestamp 
AFTER UPDATE ON food_categories
BEGIN
    UPDATE food_categories SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_nutrient_types_timestamp 
AFTER UPDATE ON nutrient_types
BEGIN
    UPDATE nutrient_types SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_database_metadata_timestamp 
AFTER UPDATE ON database_metadata
BEGIN
    UPDATE database_metadata SET updated_at = datetime('now') WHERE id = NEW.id;
END;
