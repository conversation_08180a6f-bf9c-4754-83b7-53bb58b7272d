#!/bin/bash
# Generate SQLite database files for USDA and FoodB datasets

# Set paths
OUTPUT_DIR="../app/src/assets/databases"
USDA_DB_PATH="$OUTPUT_DIR/usda_foods.sqlite"
FOODB_DB_PATH="$OUTPUT_DIR/foodb_foods.sqlite"

# Ensure output directory exists
mkdir -p "$OUTPUT_DIR"

# Remove existing database files
rm -f "$USDA_DB_PATH" "$FOODB_DB_PATH"

echo "Generating USDA database..."

# Create USDA database
sqlite3 "$USDA_DB_PATH" <<EOF
-- Create tables
CREATE TABLE foods (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  brand_name TEXT,
  serving_size REAL DEFAULT 100,
  serving_unit TEXT DEFAULT 'g',
  barcode TEXT,
  source TEXT,
  source_id TEXT
);

CREATE TABLE nutrients (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  food_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  amount REAL NOT NULL,
  unit TEXT NOT NULL,
  type TEXT NOT NULL,
  FOREIGN KEY (food_id) REFERENCES foods (id)
);

CREATE TABLE ingredients (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  food_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  amount REAL,
  unit TEXT,
  FOREIGN KEY (food_id) REFERENCES foods (id)
);

CREATE TABLE allergens (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  food_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  FOREIGN KEY (food_id) REFERENCES foods (id)
);

-- Create indexes
CREATE INDEX idx_foods_name ON foods (name);
CREATE INDEX idx_foods_source ON foods (source);
CREATE INDEX idx_nutrients_food_id ON nutrients (food_id);
CREATE INDEX idx_ingredients_food_id ON ingredients (food_id);
CREATE INDEX idx_allergens_food_id ON allergens (food_id);

-- Insert sample USDA foods (expanded list)
INSERT INTO foods (name, description, source, source_id) VALUES
('Apple', 'Fresh apple', 'USDA', 'usda_09003'),
('Banana', 'Fresh banana', 'USDA', 'usda_09040'),
('Orange', 'Fresh orange', 'USDA', 'usda_09200'),
('Chicken Breast', 'Chicken breast, boneless, skinless', 'USDA', 'usda_05062'),
('Salmon', 'Atlantic salmon, fresh', 'USDA', 'usda_15076'),
('Broccoli', 'Fresh broccoli', 'USDA', 'usda_11090'),
('Spinach', 'Fresh spinach', 'USDA', 'usda_11457'),
('Brown Rice', 'Brown rice, cooked', 'USDA', 'usda_20037'),
('Egg', 'Whole egg, raw', 'USDA', 'usda_01123'),
('Milk', 'Whole milk', 'USDA', 'usda_01077'),
('Beef Steak', 'Beef steak, lean', 'USDA', 'usda_13364'),
('Pork Chop', 'Pork chop, lean', 'USDA', 'usda_10062'),
('Lamb', 'Lamb, lean', 'USDA', 'usda_17002'),
('Turkey', 'Turkey, breast meat', 'USDA', 'usda_05200'),
('Tuna', 'Tuna, canned in water', 'USDA', 'usda_15121'),
('Cod', 'Cod, fresh', 'USDA', 'usda_15015'),
('Shrimp', 'Shrimp, cooked', 'USDA', 'usda_15151'),
('Potato', 'Potato, baked', 'USDA', 'usda_11674'),
('Sweet Potato', 'Sweet potato, baked', 'USDA', 'usda_11508'),
('Carrot', 'Carrot, raw', 'USDA', 'usda_11124'),
('Tomato', 'Tomato, raw', 'USDA', 'usda_11529'),
('Cucumber', 'Cucumber, with peel', 'USDA', 'usda_11205'),
('Bell Pepper', 'Bell pepper, raw', 'USDA', 'usda_11333'),
('Onion', 'Onion, raw', 'USDA', 'usda_11282'),
('Garlic', 'Garlic, raw', 'USDA', 'usda_11215'),
('Lettuce', 'Lettuce, iceberg', 'USDA', 'usda_11252'),
('Kale', 'Kale, raw', 'USDA', 'usda_11233'),
('Avocado', 'Avocado, raw', 'USDA', 'usda_09037'),
('Strawberry', 'Strawberry, raw', 'USDA', 'usda_09316'),
('Blueberry', 'Blueberry, raw', 'USDA', 'usda_09050'),
('Grape', 'Grape, raw', 'USDA', 'usda_09132'),
('Watermelon', 'Watermelon, raw', 'USDA', 'usda_09326'),
('Pineapple', 'Pineapple, raw', 'USDA', 'usda_09266'),
('Mango', 'Mango, raw', 'USDA', 'usda_09176'),
('Peach', 'Peach, raw', 'USDA', 'usda_09236'),
('Pear', 'Pear, raw', 'USDA', 'usda_09252'),
('Cherry', 'Cherry, raw', 'USDA', 'usda_09070'),
('Kiwi', 'Kiwi, raw', 'USDA', 'usda_09148'),
('Lemon', 'Lemon, raw', 'USDA', 'usda_09150'),
('Lime', 'Lime, raw', 'USDA', 'usda_09159'),
('Grapefruit', 'Grapefruit, raw', 'USDA', 'usda_09112'),
('Bread', 'Bread, white', 'USDA', 'usda_18069'),
('Bread', 'Bread, whole wheat', 'USDA', 'usda_18075'),
('Pasta', 'Pasta, cooked', 'USDA', 'usda_20121'),
('Oatmeal', 'Oatmeal, cooked', 'USDA', 'usda_08121'),
('Quinoa', 'Quinoa, cooked', 'USDA', 'usda_20137'),
('Corn', 'Corn, sweet, yellow, cooked', 'USDA', 'usda_11168'),
('Peas', 'Peas, green, cooked', 'USDA', 'usda_11305'),
('Beans', 'Beans, black, cooked', 'USDA', 'usda_16015'),
('Lentils', 'Lentils, cooked', 'USDA', 'usda_16070'),
('Chickpeas', 'Chickpeas, cooked', 'USDA', 'usda_16057'),
('Tofu', 'Tofu, firm', 'USDA', 'usda_16126'),
('Almonds', 'Almonds, raw', 'USDA', 'usda_12061'),
('Walnuts', 'Walnuts, raw', 'USDA', 'usda_12155'),
('Peanuts', 'Peanuts, raw', 'USDA', 'usda_16087'),
('Cashews', 'Cashews, raw', 'USDA', 'usda_12087'),
('Chia Seeds', 'Chia seeds', 'USDA', 'usda_12006'),
('Flax Seeds', 'Flax seeds', 'USDA', 'usda_12220'),
('Sunflower Seeds', 'Sunflower seeds', 'USDA', 'usda_12036'),
('Olive Oil', 'Olive oil', 'USDA', 'usda_04053'),
('Coconut Oil', 'Coconut oil', 'USDA', 'usda_04047'),
('Butter', 'Butter, salted', 'USDA', 'usda_01001'),
('Yogurt', 'Yogurt, plain', 'USDA', 'usda_01116'),
('Cheese', 'Cheese, cheddar', 'USDA', 'usda_01009'),
('Cottage Cheese', 'Cottage cheese', 'USDA', 'usda_01012'),
('Cream Cheese', 'Cream cheese', 'USDA', 'usda_01017'),
('Ice Cream', 'Ice cream, vanilla', 'USDA', 'usda_19095'),
('Honey', 'Honey', 'USDA', 'usda_19296'),
('Maple Syrup', 'Maple syrup', 'USDA', 'usda_19353'),
('Sugar', 'Sugar, white', 'USDA', 'usda_19335'),
('Salt', 'Salt, table', 'USDA', 'usda_02047'),
('Pepper', 'Pepper, black', 'USDA', 'usda_02030'),
('Cinnamon', 'Cinnamon, ground', 'USDA', 'usda_02010'),
('Basil', 'Basil, fresh', 'USDA', 'usda_02044'),
('Oregano', 'Oregano, dried', 'USDA', 'usda_02027'),
('Thyme', 'Thyme, fresh', 'USDA', 'usda_02049'),
('Rosemary', 'Rosemary, fresh', 'USDA', 'usda_02036'),
('Parsley', 'Parsley, fresh', 'USDA', 'usda_02029'),
('Cilantro', 'Cilantro, fresh', 'USDA', 'usda_11165'),
('Mint', 'Mint, fresh', 'USDA', 'usda_02064'),
('Chocolate', 'Chocolate, dark', 'USDA', 'usda_19081'),
('Coffee', 'Coffee, brewed', 'USDA', 'usda_14209'),
('Tea', 'Tea, brewed', 'USDA', 'usda_14355'),
('Wine', 'Wine, red', 'USDA', 'usda_14096'),
('Beer', 'Beer, regular', 'USDA', 'usda_14003'),
('Water', 'Water, bottled', 'USDA', 'usda_14555');

-- Insert standard nutrients for all USDA foods
-- This will add the same set of nutrients to all foods with different values
-- In a real database, these would be populated with accurate values for each food

-- Function to generate random nutrient values within realistic ranges
-- We'll use a deterministic approach based on food_id to ensure consistency
BEGIN TRANSACTION;

-- Insert Energy (kcal) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Energy',
       CASE
           WHEN id % 10 = 0 THEN 50 + (id % 400) -- Vegetables and fruits (lower calories)
           WHEN id % 10 = 1 THEN 200 + (id % 300) -- Meats and proteins (higher calories)
           WHEN id % 10 = 2 THEN 350 + (id % 250) -- Grains and starches (higher calories)
           WHEN id % 10 = 3 THEN 100 + (id % 200) -- Dairy (medium calories)
           WHEN id % 10 = 4 THEN 500 + (id % 100) -- Oils and fats (highest calories)
           ELSE 150 + (id % 250) -- Other foods (medium calories)
       END,
       'kcal', 'macro'
FROM foods WHERE source = 'USDA';

-- Insert Protein (g) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Protein',
       CASE
           WHEN id % 10 = 0 THEN 0.5 + (id % 5) * 0.3 -- Vegetables and fruits (lower protein)
           WHEN id % 10 = 1 THEN 15 + (id % 25) * 0.8 -- Meats and proteins (higher protein)
           WHEN id % 10 = 2 THEN 3 + (id % 10) * 0.5 -- Grains and starches (medium protein)
           WHEN id % 10 = 3 THEN 5 + (id % 8) * 0.7 -- Dairy (medium protein)
           WHEN id % 10 = 4 THEN 0 + (id % 2) * 0.1 -- Oils and fats (lowest protein)
           ELSE 2 + (id % 15) * 0.4 -- Other foods (varied protein)
       END,
       'g', 'macro'
FROM foods WHERE source = 'USDA';

-- Insert Carbohydrates (g) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Carbohydrates',
       CASE
           WHEN id % 10 = 0 THEN 5 + (id % 15) * 0.8 -- Vegetables and fruits (medium carbs)
           WHEN id % 10 = 1 THEN 0 + (id % 5) * 0.2 -- Meats and proteins (lowest carbs)
           WHEN id % 10 = 2 THEN 20 + (id % 60) * 0.5 -- Grains and starches (highest carbs)
           WHEN id % 10 = 3 THEN 5 + (id % 10) * 0.5 -- Dairy (medium-low carbs)
           WHEN id % 10 = 4 THEN 0 + (id % 1) * 0.1 -- Oils and fats (lowest carbs)
           ELSE 10 + (id % 20) * 0.6 -- Other foods (varied carbs)
       END,
       'g', 'macro'
FROM foods WHERE source = 'USDA';

-- Insert Total Fat (g) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Total Fat',
       CASE
           WHEN id % 10 = 0 THEN 0.1 + (id % 3) * 0.3 -- Vegetables and fruits (lowest fat)
           WHEN id % 10 = 1 THEN 5 + (id % 20) * 0.8 -- Meats and proteins (medium-high fat)
           WHEN id % 10 = 2 THEN 1 + (id % 5) * 0.5 -- Grains and starches (low fat)
           WHEN id % 10 = 3 THEN 3 + (id % 30) * 0.3 -- Dairy (varied fat)
           WHEN id % 10 = 4 THEN 10 + (id % 90) * 0.9 -- Oils and fats (highest fat)
           ELSE 2 + (id % 15) * 0.6 -- Other foods (varied fat)
       END,
       'g', 'macro'
FROM foods WHERE source = 'USDA';

-- Insert Fiber (g) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Fiber',
       CASE
           WHEN id % 10 = 0 THEN 1 + (id % 8) * 0.5 -- Vegetables and fruits (higher fiber)
           WHEN id % 10 = 1 THEN 0 + (id % 1) * 0.1 -- Meats and proteins (lowest fiber)
           WHEN id % 10 = 2 THEN 2 + (id % 10) * 0.4 -- Grains and starches (medium-high fiber)
           WHEN id % 10 = 3 THEN 0 + (id % 1) * 0.1 -- Dairy (lowest fiber)
           WHEN id % 10 = 4 THEN 0 + (id % 1) * 0.1 -- Oils and fats (lowest fiber)
           ELSE 0.5 + (id % 5) * 0.3 -- Other foods (low-medium fiber)
       END,
       'g', 'macro'
FROM foods WHERE source = 'USDA';

-- Insert Sugar (g) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Sugar',
       CASE
           WHEN id % 10 = 0 THEN 2 + (id % 15) * 0.8 -- Vegetables and fruits (medium-high sugar)
           WHEN id % 10 = 1 THEN 0 + (id % 1) * 0.1 -- Meats and proteins (lowest sugar)
           WHEN id % 10 = 2 THEN 0.5 + (id % 5) * 0.3 -- Grains and starches (low sugar)
           WHEN id % 10 = 3 THEN 5 + (id % 10) * 0.5 -- Dairy (medium sugar)
           WHEN id % 10 = 4 THEN 0 + (id % 1) * 0.1 -- Oils and fats (lowest sugar)
           ELSE 10 + (id % 30) * 0.9 -- Other foods (varied sugar)
       END,
       'g', 'macro'
FROM foods WHERE source = 'USDA';

-- Insert Sodium (mg) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Sodium',
       CASE
           WHEN id % 10 = 0 THEN 5 + (id % 30) * 2 -- Vegetables and fruits (low sodium)
           WHEN id % 10 = 1 THEN 50 + (id % 100) * 5 -- Meats and proteins (medium sodium)
           WHEN id % 10 = 2 THEN 100 + (id % 400) * 2 -- Grains and starches (medium-high sodium)
           WHEN id % 10 = 3 THEN 40 + (id % 150) * 3 -- Dairy (medium sodium)
           WHEN id % 10 = 4 THEN 0 + (id % 10) * 2 -- Oils and fats (lowest sodium)
           ELSE 200 + (id % 800) * 1 -- Other foods (varied sodium)
       END,
       'mg', 'mineral'
FROM foods WHERE source = 'USDA';

-- Insert Calcium (mg) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Calcium',
       CASE
           WHEN id % 10 = 0 THEN 10 + (id % 100) * 1 -- Vegetables and fruits (low-medium calcium)
           WHEN id % 10 = 1 THEN 5 + (id % 20) * 1 -- Meats and proteins (low calcium)
           WHEN id % 10 = 2 THEN 20 + (id % 50) * 1 -- Grains and starches (low-medium calcium)
           WHEN id % 10 = 3 THEN 100 + (id % 300) * 3 -- Dairy (highest calcium)
           WHEN id % 10 = 4 THEN 0 + (id % 5) * 1 -- Oils and fats (lowest calcium)
           ELSE 20 + (id % 100) * 1 -- Other foods (varied calcium)
       END,
       'mg', 'mineral'
FROM foods WHERE source = 'USDA';

-- Insert Iron (mg) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Iron',
       CASE
           WHEN id % 10 = 0 THEN 0.5 + (id % 3) * 0.3 -- Vegetables and fruits (medium iron)
           WHEN id % 10 = 1 THEN 1 + (id % 5) * 0.5 -- Meats and proteins (medium-high iron)
           WHEN id % 10 = 2 THEN 1 + (id % 4) * 0.4 -- Grains and starches (medium iron)
           WHEN id % 10 = 3 THEN 0.1 + (id % 1) * 0.1 -- Dairy (low iron)
           WHEN id % 10 = 4 THEN 0 + (id % 0.5) * 0.1 -- Oils and fats (lowest iron)
           ELSE 0.5 + (id % 3) * 0.3 -- Other foods (varied iron)
       END,
       'mg', 'mineral'
FROM foods WHERE source = 'USDA';

-- Insert Vitamin C (mg) for all foods
INSERT INTO nutrients (food_id, name, amount, unit, type)
SELECT id, 'Vitamin C',
       CASE
           WHEN id % 10 = 0 THEN 5 + (id % 80) * 1 -- Vegetables and fruits (highest vitamin C)
           WHEN id % 10 = 1 THEN 0 + (id % 2) * 0.1 -- Meats and proteins (lowest vitamin C)
           WHEN id % 10 = 2 THEN 0 + (id % 1) * 0.1 -- Grains and starches (lowest vitamin C)
           WHEN id % 10 = 3 THEN 0 + (id % 2) * 0.1 -- Dairy (lowest vitamin C)
           WHEN id % 10 = 4 THEN 0 + (id % 0.5) * 0.1 -- Oils and fats (lowest vitamin C)
           ELSE 1 + (id % 10) * 0.5 -- Other foods (low vitamin C)
       END,
       'mg', 'vitamin'
FROM foods WHERE source = 'USDA';

COMMIT;
EOF

echo "USDA database generated with 10 foods and nutrients."

echo "Generating FoodB database..."

# Create FoodB database
sqlite3 "$FOODB_DB_PATH" <<EOF
-- Create tables
CREATE TABLE foods (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  brand_name TEXT,
  serving_size REAL DEFAULT 100,
  serving_unit TEXT DEFAULT 'g',
  barcode TEXT,
  source TEXT,
  source_id TEXT
);

CREATE TABLE nutrients (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  food_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  amount REAL NOT NULL,
  unit TEXT NOT NULL,
  type TEXT NOT NULL,
  FOREIGN KEY (food_id) REFERENCES foods (id)
);

CREATE TABLE ingredients (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  food_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  amount REAL,
  unit TEXT,
  FOREIGN KEY (food_id) REFERENCES foods (id)
);

CREATE TABLE allergens (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  food_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  FOREIGN KEY (food_id) REFERENCES foods (id)
);

-- Create indexes
CREATE INDEX idx_foods_name ON foods (name);
CREATE INDEX idx_foods_source ON foods (source);
CREATE INDEX idx_nutrients_food_id ON nutrients (food_id);
CREATE INDEX idx_ingredients_food_id ON ingredients (food_id);
CREATE INDEX idx_allergens_food_id ON allergens (food_id);

-- Insert sample FoodB foods (expanded list)
INSERT INTO foods (name, description, brand_name, source, source_id) VALUES
('Apple Juice', 'Apple juice', 'Generic', 'FoodB', 'foodb_1001'),
('Banana Smoothie', 'Banana smoothie', 'Generic', 'FoodB', 'foodb_1002'),
('Orange Juice', 'Orange juice', 'Generic', 'FoodB', 'foodb_1003'),
('Chicken Soup', 'Chicken soup', 'Generic', 'FoodB', 'foodb_1004'),
('Salmon Sushi', 'Salmon sushi roll', 'Generic', 'FoodB', 'foodb_1005'),
('Broccoli Soup', 'Broccoli cream soup', 'Generic', 'FoodB', 'foodb_1006'),
('Spinach Salad', 'Spinach salad with dressing', 'Generic', 'FoodB', 'foodb_1007'),
('Brown Rice Bowl', 'Brown rice bowl with vegetables', 'Generic', 'FoodB', 'foodb_1008'),
('Egg Sandwich', 'Egg sandwich', 'Generic', 'FoodB', 'foodb_1009'),
('Milk Shake', 'Vanilla milk shake', 'Generic', 'FoodB', 'foodb_1010'),
('Beef Burger', 'Beef burger with cheese', 'Generic', 'FoodB', 'foodb_1011'),
('Chicken Wrap', 'Chicken wrap with vegetables', 'Generic', 'FoodB', 'foodb_1012'),
('Tuna Sandwich', 'Tuna sandwich', 'Generic', 'FoodB', 'foodb_1013'),
('Caesar Salad', 'Caesar salad with chicken', 'Generic', 'FoodB', 'foodb_1014'),
('Greek Salad', 'Greek salad with feta cheese', 'Generic', 'FoodB', 'foodb_1015'),
('Vegetable Soup', 'Vegetable soup', 'Generic', 'FoodB', 'foodb_1016'),
('Tomato Soup', 'Tomato soup', 'Generic', 'FoodB', 'foodb_1017'),
('Mushroom Soup', 'Mushroom soup', 'Generic', 'FoodB', 'foodb_1018'),
('Lentil Soup', 'Lentil soup', 'Generic', 'FoodB', 'foodb_1019'),
('Chicken Curry', 'Chicken curry with rice', 'Generic', 'FoodB', 'foodb_1020'),
('Beef Stew', 'Beef stew with vegetables', 'Generic', 'FoodB', 'foodb_1021'),
('Lasagna', 'Beef lasagna', 'Generic', 'FoodB', 'foodb_1022'),
('Spaghetti Bolognese', 'Spaghetti with beef sauce', 'Generic', 'FoodB', 'foodb_1023'),
('Mac and Cheese', 'Macaroni and cheese', 'Generic', 'FoodB', 'foodb_1024'),
('Pizza', 'Cheese pizza', 'Generic', 'FoodB', 'foodb_1025'),
('Pepperoni Pizza', 'Pepperoni pizza', 'Generic', 'FoodB', 'foodb_1026'),
('Vegetable Pizza', 'Vegetable pizza', 'Generic', 'FoodB', 'foodb_1027'),
('Chicken Pizza', 'Chicken pizza', 'Generic', 'FoodB', 'foodb_1028'),
('Pancakes', 'Pancakes with maple syrup', 'Generic', 'FoodB', 'foodb_1029'),
('Waffles', 'Waffles with maple syrup', 'Generic', 'FoodB', 'foodb_1030'),
('French Toast', 'French toast with maple syrup', 'Generic', 'FoodB', 'foodb_1031'),
('Omelette', 'Cheese omelette', 'Generic', 'FoodB', 'foodb_1032'),
('Scrambled Eggs', 'Scrambled eggs', 'Generic', 'FoodB', 'foodb_1033'),
('Fried Eggs', 'Fried eggs', 'Generic', 'FoodB', 'foodb_1034'),
('Boiled Eggs', 'Boiled eggs', 'Generic', 'FoodB', 'foodb_1035'),
('Bacon', 'Bacon, cooked', 'Generic', 'FoodB', 'foodb_1036'),
('Sausage', 'Pork sausage, cooked', 'Generic', 'FoodB', 'foodb_1037'),
('Hash Browns', 'Hash browns', 'Generic', 'FoodB', 'foodb_1038'),
('Bagel', 'Plain bagel', 'Generic', 'FoodB', 'foodb_1039'),
('Croissant', 'Butter croissant', 'Generic', 'FoodB', 'foodb_1040'),
('Muffin', 'Blueberry muffin', 'Generic', 'FoodB', 'foodb_1041'),
('Donut', 'Glazed donut', 'Generic', 'FoodB', 'foodb_1042'),
('Cinnamon Roll', 'Cinnamon roll with icing', 'Generic', 'FoodB', 'foodb_1043'),
('Chocolate Chip Cookie', 'Chocolate chip cookie', 'Generic', 'FoodB', 'foodb_1044'),
('Brownie', 'Chocolate brownie', 'Generic', 'FoodB', 'foodb_1045'),
('Cheesecake', 'New York cheesecake', 'Generic', 'FoodB', 'foodb_1046'),
('Apple Pie', 'Apple pie', 'Generic', 'FoodB', 'foodb_1047'),
('Chocolate Cake', 'Chocolate cake', 'Generic', 'FoodB', 'foodb_1048'),
('Carrot Cake', 'Carrot cake', 'Generic', 'FoodB', 'foodb_1049'),
('Ice Cream Sundae', 'Ice cream sundae with chocolate sauce', 'Generic', 'FoodB', 'foodb_1050'),
('Milkshake', 'Chocolate milkshake', 'Generic', 'FoodB', 'foodb_1051'),
('Smoothie', 'Berry smoothie', 'Generic', 'FoodB', 'foodb_1052'),
('Lemonade', 'Lemonade', 'Generic', 'FoodB', 'foodb_1053'),
('Iced Tea', 'Iced tea', 'Generic', 'FoodB', 'foodb_1054'),
('Hot Chocolate', 'Hot chocolate', 'Generic', 'FoodB', 'foodb_1055'),
('Cappuccino', 'Cappuccino', 'Generic', 'FoodB', 'foodb_1056'),
('Latte', 'Latte', 'Generic', 'FoodB', 'foodb_1057'),
('Espresso', 'Espresso', 'Generic', 'FoodB', 'foodb_1058'),
('Mocha', 'Mocha', 'Generic', 'FoodB', 'foodb_1059'),
('Americano', 'Americano', 'Generic', 'FoodB', 'foodb_1060'),
('Green Tea', 'Green tea', 'Generic', 'FoodB', 'foodb_1061'),
('Black Tea', 'Black tea', 'Generic', 'FoodB', 'foodb_1062'),
('Herbal Tea', 'Herbal tea', 'Generic', 'FoodB', 'foodb_1063'),
('Chai Tea', 'Chai tea', 'Generic', 'FoodB', 'foodb_1064'),
('Fruit Juice', 'Mixed fruit juice', 'Generic', 'FoodB', 'foodb_1065'),
('Vegetable Juice', 'Mixed vegetable juice', 'Generic', 'FoodB', 'foodb_1066'),
('Coconut Water', 'Coconut water', 'Generic', 'FoodB', 'foodb_1067'),
('Soda', 'Cola soda', 'Generic', 'FoodB', 'foodb_1068'),
('Diet Soda', 'Diet cola soda', 'Generic', 'FoodB', 'foodb_1069'),
('Energy Drink', 'Energy drink', 'Generic', 'FoodB', 'foodb_1070'),
('Sports Drink', 'Sports drink', 'Generic', 'FoodB', 'foodb_1071'),
('Protein Shake', 'Protein shake', 'Generic', 'FoodB', 'foodb_1072'),
('Almond Milk', 'Almond milk', 'Generic', 'FoodB', 'foodb_1073'),
('Soy Milk', 'Soy milk', 'Generic', 'FoodB', 'foodb_1074'),
('Oat Milk', 'Oat milk', 'Generic', 'FoodB', 'foodb_1075'),
('Coconut Milk', 'Coconut milk', 'Generic', 'FoodB', 'foodb_1076'),
('Rice Milk', 'Rice milk', 'Generic', 'FoodB', 'foodb_1077'),
('Cashew Milk', 'Cashew milk', 'Generic', 'FoodB', 'foodb_1078'),
('Hemp Milk', 'Hemp milk', 'Generic', 'FoodB', 'foodb_1079'),
('Chocolate Milk', 'Chocolate milk', 'Generic', 'FoodB', 'foodb_1080');

-- Insert nutrients for Apple Juice
INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES
(1, 'Energy', 46, 'kcal', 'macro'),
(1, 'Protein', 0.1, 'g', 'macro'),
(1, 'Carbohydrates', 11.5, 'g', 'macro'),
(1, 'Total Fat', 0.1, 'g', 'macro'),
(1, 'Sugar', 9.6, 'g', 'macro');

-- Insert ingredients for Apple Juice
INSERT INTO ingredients (food_id, name, amount, unit) VALUES
(1, 'Apple', 100, '%');

-- Insert nutrients for Banana Smoothie
INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES
(2, 'Energy', 95, 'kcal', 'macro'),
(2, 'Protein', 1.5, 'g', 'macro'),
(2, 'Carbohydrates', 22, 'g', 'macro'),
(2, 'Total Fat', 0.5, 'g', 'macro'),
(2, 'Sugar', 14, 'g', 'macro');

-- Insert ingredients for Banana Smoothie
INSERT INTO ingredients (food_id, name, amount, unit) VALUES
(2, 'Banana', 80, '%'),
(2, 'Milk', 20, '%');

-- Insert allergens for Banana Smoothie
INSERT INTO allergens (food_id, name) VALUES
(2, 'Milk');

-- Insert nutrients for other foods
-- Orange Juice
INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES
(3, 'Energy', 45, 'kcal', 'macro'),
(3, 'Protein', 0.7, 'g', 'macro'),
(3, 'Carbohydrates', 10.4, 'g', 'macro'),
(3, 'Total Fat', 0.2, 'g', 'macro');

-- Chicken Soup
INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES
(4, 'Energy', 75, 'kcal', 'macro'),
(4, 'Protein', 6, 'g', 'macro'),
(4, 'Carbohydrates', 8, 'g', 'macro'),
(4, 'Total Fat', 2.5, 'g', 'macro');

-- Salmon Sushi
INSERT INTO nutrients (food_id, name, amount, unit, type) VALUES
(5, 'Energy', 150, 'kcal', 'macro'),
(5, 'Protein', 5.8, 'g', 'macro'),
(5, 'Carbohydrates', 30, 'g', 'macro'),
(5, 'Total Fat', 0.7, 'g', 'macro');
EOF

echo "FoodB database generated with 10 foods, nutrients, ingredients, and allergens."

echo "Database generation complete!"
echo "USDA database: $USDA_DB_PATH"
echo "FoodB database: $FOODB_DB_PATH"
