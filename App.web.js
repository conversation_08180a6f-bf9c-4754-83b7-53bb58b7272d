import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialIcons } from '@expo/vector-icons';
import { Provider as PaperProvider } from 'react-native-paper';

// Import theme
import { DEFAULT_THEME } from './app/src/theme/theme';

const Tab = createBottomTabNavigator();

// Web-specific notice component
const WebNotice = () => (
  <View style={styles.webNotice}>
    <MaterialIcons name="info" size={20} color="#2196F3" />
    <Text style={styles.webNoticeText}>
      Web version has limited functionality. For full features, use the mobile app.
    </Text>
  </View>
);

// Web-compatible Home Screen
const WebHomeScreen = ({ navigation }) => (
  <View style={styles.container}>
    <WebNotice />
    <View style={styles.content}>
      <Text style={styles.title}>ZnüniZähler</Text>
      <Text style={styles.subtitle}>Smart Nutrition Tracking</Text>

      <View style={styles.featureList}>
        <View style={styles.feature}>
          <MaterialIcons name="document-scanner" size={24} color="#4CAF50" />
          <Text style={styles.featureText}>OCR Label Scanning (Mobile Only)</Text>
        </View>

        <View style={styles.feature}>
          <MaterialIcons name="qr-code-scanner" size={24} color="#2196F3" />
          <Text style={styles.featureText}>Barcode Scanning (Mobile Only)</Text>
        </View>

        <View style={styles.feature}>
          <MaterialIcons name="edit" size={24} color="#FF9800" />
          <Text style={styles.featureText}>Manual Food Entry</Text>
        </View>

        <View style={styles.feature}>
          <MaterialIcons name="analytics" size={24} color="#9C27B0" />
          <Text style={styles.featureText}>Nutrition Analytics</Text>
        </View>
      </View>

      <View style={styles.downloadSection}>
        <Text style={styles.downloadTitle}>Get the Full Experience</Text>
        <Text style={styles.downloadText}>
          Download the mobile app for OCR scanning, barcode lookup, and offline functionality.
        </Text>

        <View style={styles.downloadButtons}>
          <View style={styles.downloadButton}>
            <MaterialIcons name="android" size={24} color="#3DDC84" />
            <Text style={styles.downloadButtonText}>Android (Coming Soon)</Text>
          </View>

          <View style={styles.downloadButton}>
            <MaterialIcons name="apple" size={24} color="#000" />
            <Text style={styles.downloadButtonText}>iOS (Coming Soon)</Text>
          </View>
        </View>
      </View>
    </View>
  </View>
);

// Web-compatible Food Screen
const WebFoodScreen = () => (
  <View style={styles.container}>
    <WebNotice />
    <View style={styles.content}>
      <Text style={styles.title}>Food Database</Text>
      <Text style={styles.subtitle}>
        Food database features are optimized for mobile devices with local storage.
      </Text>

      <View style={styles.comingSoon}>
        <MaterialIcons name="restaurant" size={48} color="#ccc" />
        <Text style={styles.comingSoonText}>Web food database coming soon!</Text>
      </View>
    </View>
  </View>
);

// Web-compatible Profile Screen
const WebProfileScreen = () => (
  <View style={styles.container}>
    <WebNotice />
    <View style={styles.content}>
      <Text style={styles.title}>Profile & Settings</Text>
      <Text style={styles.subtitle}>
        User profiles and settings are stored locally on mobile devices.
      </Text>

      <View style={styles.comingSoon}>
        <MaterialIcons name="person" size={48} color="#ccc" />
        <Text style={styles.comingSoonText}>Web profiles coming soon!</Text>
      </View>
    </View>
  </View>
);

export default function App() {
  return (
    <PaperProvider theme={DEFAULT_THEME}>
      <NavigationContainer>
        <StatusBar style="auto" />
        <Tab.Navigator
          screenOptions={({ route }) => ({
            tabBarIcon: ({ focused, color, size }) => {
              let iconName;

              if (route.name === 'Home') {
                iconName = 'home';
              } else if (route.name === 'Food') {
                iconName = 'restaurant';
              } else if (route.name === 'Profile') {
                iconName = 'person';
              }

              return <MaterialIcons name={iconName} size={size} color={color} />;
            },
            tabBarActiveTintColor: '#4CAF50',
            tabBarInactiveTintColor: 'gray',
            headerStyle: {
              backgroundColor: '#4CAF50',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          })}
        >
          <Tab.Screen
            name="Home"
            component={WebHomeScreen}
            options={{ title: 'ZnüniZähler' }}
          />
          <Tab.Screen
            name="Food"
            component={WebFoodScreen}
            options={{ title: 'Food Database' }}
          />
          <Tab.Screen
            name="Profile"
            component={WebProfileScreen}
            options={{ title: 'Profile' }}
          />
        </Tab.Navigator>
      </NavigationContainer>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  webNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#bbdefb',
  },
  webNoticeText: {
    marginLeft: 8,
    color: '#1976d2',
    fontSize: 14,
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
    maxWidth: 400,
  },
  featureList: {
    width: '100%',
    maxWidth: 400,
    marginBottom: 40,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  featureText: {
    marginLeft: 15,
    fontSize: 16,
    color: '#333',
  },
  downloadSection: {
    alignItems: 'center',
    maxWidth: 400,
  },
  downloadTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  downloadText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  downloadButtons: {
    flexDirection: 'row',
    gap: 15,
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  downloadButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  comingSoon: {
    alignItems: 'center',
    marginTop: 50,
  },
  comingSoonText: {
    fontSize: 18,
    color: '#999',
    marginTop: 15,
  },
});
