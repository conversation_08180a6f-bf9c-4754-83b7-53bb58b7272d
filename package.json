{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cucumber-js --config cucumber.config.js", "test:e2e:smoke": "cucumber-js --config cucumber.config.js --profile smoke", "test:e2e:regression": "cucumber-js --config cucumber.config.js --profile regression", "test:e2e:mobile": "cucumber-js --config cucumber.config.js --profile mobile", "test:e2e:web": "cucumber-js --config cucumber.config.js --profile web", "test:playwright": "playwright test", "test:playwright:headed": "playwright test --headed", "test:playwright:debug": "playwright test --debug", "test:all": "npm run test && npm run test:e2e"}, "dependencies": {"@expo/webpack-config": "^19.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/datetimepicker": "7.2.0", "@react-native-vector-icons/material-design-icons": "^12.0.0", "@react-navigation/bottom-tabs": "^6.5.9", "@react-navigation/native": "^6.1.8", "@react-navigation/native-stack": "^6.9.14", "axios": "^1.6.0", "expo": "~49.0.13", "expo-barcode-scanner": "~12.5.3", "expo-document-picker": "~11.5.4", "expo-file-system": "~15.4.4", "expo-sharing": "~11.5.0", "expo-sqlite": "~11.3.3", "expo-status-bar": "~1.6.0", "i18next": "^21.10.0", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^11.18.6", "react-native": "0.72.10", "react-native-chart-kit": "^6.12.0", "react-native-paper": "^5.10.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.6", "react-native-zip-archive": "^6.0.12", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-react": "^7.27.1", "@cucumber/cucumber": "^11.3.0", "@playwright/test": "^1.52.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.3.0", "csv-parse": "^5.6.0", "cucumber": "^6.0.7", "cucumber-html-reporter": "^7.2.0", "expo-document-picker": "^13.1.5", "jest": "^29.7.0", "jest-expo": "^49.0.0", "react-native-zip-archive": "^7.0.1", "react-test-renderer": "18.2.0"}, "private": true}